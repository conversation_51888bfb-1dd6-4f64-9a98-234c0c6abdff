<template>
  <div class="sketch_content">
    <el-form :model="formData" class="resumeTable" :rules="rules" ref="form">
      <el-row>
        <el-col :span="11">
          <el-form-item label="发布人" label-width="90px" label-position="left" prop="createUserName">
            <el-input v-model="formData.createUserName" :disabled="true"></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="11">
          <el-form-item label="发布时间" label-width="90px" label-position="left" prop="createTime">
            <el-input v-model="formData.createTime" :disabled="true"></el-input>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row>
        <el-col :span="11">
          <el-form-item label="价格(元)" label-width="90px" label-position="left" prop="price">
            <el-input v-model="formData.price" :disabled="infoDisabled" clearable></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="11">
          <el-form-item label="发货方式" label-width="90px" label-position="left" prop="delivery">
            <el-select v-model="formData.delivery" :disabled="infoDisabled" clearable>
              <el-option-group>
                <el-option :value="0" label="线下交易"></el-option>
              </el-option-group>
            </el-select>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row>
        <el-col :span="11">
          <el-form-item label="封面" label-width="90px" label-position="left">
            <es-upload v-bind="coverAttrs" v-model="formData.coverUrl" :disabled="infoDisabled"
                       select-type="icon-plus" list-type="picture"></es-upload>
          </el-form-item>
        </el-col>
        <el-col :span="11">
          <el-form-item label="详情图" label-width="90px" label-position="left">
            <es-upload v-bind="detailAttrs" :disabled="infoDisabled"
                       select-type="icon-plus" list-type="picture-card" ref="detailUpload">
            </es-upload>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row>
        <el-col :span="22">
          <el-form-item label="标题" label-width="90px" label-position="left">
            <el-input type="textarea" v-model="formData.title" :disabled="infoDisabled" :rows="4"></el-input>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row>
        <el-col :span="22">
          <el-form-item label="内容" label-width="90px" label-position="left">
            <wangeditor v-model="formData.introduction" :read-only="infoDisabled"></wangeditor>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row>
        <el-col :span="3" style="float:right">
          <el-button type="reset" @click="infoPageClose(false)">取消</el-button>
        </el-col>
        <el-col :span="3" style="float:right">
          <el-button type="primary" @click="handleFormSubmit" v-show="!infoDisabled">保存</el-button>
        </el-col>
      </el-row>
    </el-form>
  </div>
</template>
<script>

import api from "@/http/platform/idleTrade";

const priceCheck = (row,value,callback)=>{
  const numReg = /^[0-9.]{0,}$/;
  const intReg = /^([0-9]{0,6}($|\.[\S]{0,}))$/;
  const floReg = /^([\S]{0,}\.[0-9]{1,2}|[0-9]{0,6})$/;
  if(numReg.test(value)){
    if(intReg.test(value)){
      if(floReg.test(value)){
        callback();
      }else callback(new Error("小数应为1~2位"));
    }else callback(new Error("整数应为1~6位"));
  }else  callback(new Error("只能输入数字"));
};
export default {
  name: 'infoPage',
  props: {
    baseData: {
      type: Object
    },
    infoPageMode: {
      type: String
    }
  },
  data() {
    return {
      infoDisabled: true,
      formData: {},
      pageMode: 'allOn',
      rules: {
        price:[
            {required:true, message: '请输入价格', trigger: 'blur'},
            {validator:priceCheck, trigger: ['blur','change']}
        ],
        delivery:{required:true, message: '请选择发货方式', trigger: 'blur'},
      }
    };
  },
  computed: {
    coverAttrs(){
      return {
        code: 'plat_idle_trade_cover',
        ownId: this.formData.id,
        portrait: true,
        preview: true,
        download: true,
        operate: true,
      }
    },
    detailAttrs() {
      return {
        code: 'plat_idle_trade_detail',
        ownId: this.formData.id,
        preview: true,
        download: true,
        operate: true,
        "auto-upload": false,
      }
    },
  },
  created() {
    this.formData = { ...this.baseData };
    this.pageMode = this.infoPageMode;
    switch (this.pageMode){
      case'新增':
        this.infoDisabled = false;break;
      case'查看':
        this.infoDisabled = true;break;
      case'编辑':
        this.infoDisabled = false;break;
    }
  },
  methods: {
    handleFormSubmit() {
      //校验
      this.$refs.form.validate((valid) => {
        if(valid){
          let apiUrl = this.pageMode === '编辑'? api.update:api.save;
          let saveData = {...this.formData};
          //处理数据
          if(typeof saveData.typeId == 'object')
            saveData.typeId = saveData.typeId.value;
          if(typeof saveData.teacherId == 'object')
            saveData.teacherId = saveData.teacherId.value;
          if(typeof saveData.principal == 'object')
            saveData.principal = saveData.principal.value;
          else saveData.principalId = saveData.principal;
          if(saveData.coverUrl != null){
            if(typeof saveData.coverUrl == 'object'){
              saveData.cover = saveData.coverUrl.response.adjunctId;
              this.$delete(saveData,'coverUrl');
            } else if(!saveData.coverUrl.startsWith('http')){
              saveData.cover = saveData.coverUrl;
              this.$delete(saveData,'coverUrl');
            }else {this.$delete(saveData,'coverUrl');}
          }

          this.$delete(saveData,'typeName');
          this.$delete(saveData,'teacherName');
          this.$delete(saveData,'college');

          this.$request({
            url: apiUrl,
            data: saveData,
            method: 'POST'
          }).then(res => {
            if (res.rCode === 0) {
              let id = res.results;
              if(id != null && id !== '') {
                this.formData.id = id;
                this.$set(this.$refs.detailUpload.datas,'ownId',id);
              }

              this.$refs.detailUpload.handleUpload();
              this.$message.success(res.msg);
              this.infoPageClose(true);
            } else {
              this.$message.error(res.msg);
            }
          });
        }
      })
    },
    infoPageClose(reload){
      this.pageMode = 'allOn';
      this.infoDisabled = false;
      this.$emit('activelyClose',reload);
    },
  },
  watch: {
  }
};
</script>
<style scoped>
  .sketch_content {
    overflow: auto;
    height: 100%;
    border-top: 1px solid #eff1f4;
    border-bottom: 1px solid #eff1f4;
    padding: 0 30px 11px 27px;
  }
  .resumeTable td {
    height: 50px;
    font-weight: bold;
  }

  ::v-deep .el-collapse-item__header.is-active {
    border-bottom: 1px solid #ebeef5;
    font-size: 18px;
    font-weight: bold;
  }
  ::v-deep .el-collapse-item__header::before {
    content: "";
    width: 4px;
    height: 18px;
    background-color: #0076E9;
    margin-right: 2px;
  }
</style>