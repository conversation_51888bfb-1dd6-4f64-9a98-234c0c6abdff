export const mixinList = {
	props: {
		// 路由props参数，用于动态化参数
		contentsKey: {
			type: String,
			required: true
		}
	},
	computed: {
		// 动态列表配置、接口、数据
		wd() {
			let obj = {
				basics: {}, // 基本配置
				toolbar: [], // 筛选配置
				thead: [] // 列表配置
			};
			switch (this.contentsKey) {
				// 学术论文
				case 'academicPaper':
					obj = {
						// 基本配置
						basics: {
							dataTableUrl: '/ybzy/paAcademicPaper/listJson', // 列表接口
							delete: '/ybzy/paAcademicPaper/removeById',
							info: '/ybzy/paAcademicPaper/info',
							totalStatus: '/ybzy/paAcademicPaper/total-status'
						},
						// 筛选配置
						toolbar: [
							{
								type: 'button',
								contents: []
							},
							{
								type: 'search',
								reset: true,
								contents: [
									{
										type: 'text',
										label: '论文名称',
										name: 'name',
										placeholder: '请输入论文名称',
										col: 3
									},
									{
										type: 'select',
										label: '类型',
										name: 'academicPaperType',
										placeholder: '请输入类型',
										clearable: true,
										data: this.codesObj.academic_type_code,
										'value-key': 'cciValue',
										'label-key': 'shortName',
										col: 3
									}
								]
							}
						],
						// 列表配置
						thead: [
							{
								title: '论文名称',
								align: 'center',
								field: 'name',
								showOverflowTooltip: true,
								minWidth: '130px'
							},
							{
								title: '创建人姓名',
								align: 'center',
								field: 'createUserName',
								showOverflowTooltip: true
							},
							{
								title: '创建时间',
								align: 'center',
								field: 'createTime',
								width: '160px',
								showOverflowTooltip: true
							},
							{
								title: '更新时间',
								align: 'center',
								field: 'updateTime',
								width: '160px',
								showOverflowTooltip: true
							},
							{
								title: '操作',
								type: 'handle',
								width: 180,
								fixed: 'right',
								template: '',
								events: [
									// 0-草稿  1-审核中  2-审核通过  9-驳回
									{
										code: 'look',
										icon: 'es-icon-chakan',
										text: '查看'
									},
									{
										code: 'edit',
										text: '编辑',
										icon: 'es-icon-bianji',
										rules: rows => rows.status === '0'
									},
									{
										code: 'delect',
										text: '删除',
										icon: 'es-icon-shanchu',
										rules: rows => rows.status === '0'
									}
								]
							}
						]
					};
					break;
				// 学术专利
				case 'academicPatent':
					obj = {
						// 基本配置
						basics: {
							dataTableUrl: '/ybzy/paAcademicPatent/listJson', // 列表接口
							delete: '/ybzy/paAcademicPatent/removeById',
							info: '/ybzy/paAcademicPatent/info',
							totalStatus: '/ybzy/paAcademicPatent/total-status'
						},
						// 筛选配置
						toolbar: [
							{
								type: 'button',
								contents: []
							},
							{
								type: 'search',
								reset: true,
								contents: [
									{
										type: 'text',
										label: '专利名称',
										name: 'name',
										placeholder: '请输入专利名称',
										col: 3
									}
								]
							}
						],
						// 列表配置
						thead: [
							{
								title: '专利名称',
								align: 'center',
								field: 'name',
								showOverflowTooltip: true,
								minWidth: '130px'
							},
							{
								title: '创建人姓名',
								align: 'center',
								field: 'createUserName',
								showOverflowTooltip: true
							},
							{
								title: '创建时间',
								align: 'center',
								field: 'createTime',
								width: '160px',
								showOverflowTooltip: true
							},
							{
								title: '更新时间',
								align: 'center',
								field: 'updateTime',
								width: '160px',
								showOverflowTooltip: true
							},
							{
								title: '操作',
								type: 'handle',
								width: 180,
								fixed: 'right',
								template: '',
								events: [
									// 0-草稿  1-审核中  2-审核通过  9-驳回
									{
										code: 'look',
										icon: 'es-icon-chakan',
										text: '查看'
									},
									{
										code: 'edit',
										text: '编辑',
										icon: 'es-icon-bianji',
										rules: rows => rows.status === '0'
									},
									{
										code: 'delect',
										text: '删除',
										icon: 'es-icon-shanchu',
										rules: rows => rows.status === '0'
									}
								]
							}
						]
					};
					break;
				// 学术著作
				case 'academicBook':
					obj = {
						// 基本配置
						basics: {
							dataTableUrl: '/ybzy/paAcademicWriting/listJson', // 列表接口
							delete: '/ybzy/paAcademicWriting/removeById',
							info: '/ybzy/paAcademicWriting/info',
							totalStatus: '/ybzy/paAcademicWriting/total-status'
						},
						// 筛选配置
						toolbar: [
							{
								type: 'button',
								contents: []
							},
							{
								type: 'search',
								reset: true,
								contents: [
									{
										type: 'text',
										label: '著作名称',
										name: 'name',
										placeholder: '请输入著作名称',
										col: 3
									}
								]
							}
						],
						// 列表配置
						thead: [
							{
								title: '著作名称',
								align: 'center',
								field: 'name',
								showOverflowTooltip: true,
								minWidth: '130px'
							},
							{
								title: '创建人姓名',
								align: 'center',
								field: 'createUserName',
								showOverflowTooltip: true
							},
							{
								title: '创建时间',
								align: 'center',
								field: 'createTime',
								width: '160px',
								showOverflowTooltip: true
							},
							{
								title: '更新时间',
								align: 'center',
								field: 'updateTime',
								width: '160px',
								showOverflowTooltip: true
							},
							{
								title: '操作',
								type: 'handle',
								width: 180,
								fixed: 'right',
								template: '',
								events: [
									// 0-草稿  1-审核中  2-审核通过  9-驳回
									{
										code: 'look',
										icon: 'es-icon-chakan',
										text: '查看'
									},
									{
										code: 'edit',
										text: '编辑',
										icon: 'es-icon-bianji',
										rules: rows => rows.status === '0'
									},
									{
										code: 'delect',
										text: '删除',
										icon: 'es-icon-shanchu',
										rules: rows => rows.status === '0'
									}
								]
							}
						]
					};
					break;
				// 平台团队
				case 'platformTeam':
					obj = {
						// 基本配置
						basics: {
							dataTableUrl: '/ybzy/paPlatformTeam/listJson', // 列表接口
							delete: '/ybzy/paPlatformTeam/removeById',
							info: '/ybzy/paPlatformTeam/info',
							totalStatus: '/ybzy/paPlatformTeam/total-status'
						},
						// 筛选配置
						toolbar: [
							{
								type: 'button',
								contents: []
							},
							{
								type: 'search',
								reset: true,
								contents: [
									{
										type: 'text',
										label: '平台团队名称',
										name: 'name',
										placeholder: '请输入平台团队名称',
										col: 3
									}
								]
							}
						],
						// 列表配置
						thead: [
							{
								title: '平台团队名称',
								align: 'center',
								field: 'name',
								showOverflowTooltip: true,
								minWidth: '130px'
							},
							{
								title: '创建人姓名',
								align: 'center',
								field: 'createUserName',
								showOverflowTooltip: true
							},
							{
								title: '创建时间',
								align: 'center',
								field: 'createTime',
								width: '160px',
								showOverflowTooltip: true
							},
							{
								title: '更新时间',
								align: 'center',
								field: 'updateTime',
								width: '160px',
								showOverflowTooltip: true
							},
							{
								title: '操作',
								type: 'handle',
								width: 180,
								fixed: 'right',
								template: '',
								events: [
									// 0-草稿  1-审核中  2-审核通过  9-驳回
									{
										code: 'look',
										icon: 'es-icon-chakan',
										text: '查看'
									},
									{
										code: 'edit',
										text: '编辑',
										icon: 'es-icon-bianji',
										rules: rows => rows.status === '0'
									},
									{
										code: 'delect',
										text: '删除',
										icon: 'es-icon-shanchu',
										rules: rows => rows.status === '0'
									}
								]
							}
						]
					};
					break;
				// 技术标准
				case 'technicalStandard':
					obj = {
						// 基本配置
						basics: {
							dataTableUrl: '/ybzy/paTechnicalStandard/listJson', // 列表接口
							delete: '/ybzy/paTechnicalStandard/removeById',
							info: '/ybzy/paTechnicalStandard/info',
							totalStatus: '/ybzy/paTechnicalStandard/total-status'
						},
						// 筛选配置
						toolbar: [
							{
								type: 'button',
								contents: []
							},
							{
								type: 'search',
								reset: true,
								contents: [
									{
										type: 'text',
										label: '标准名称',
										name: 'name',
										placeholder: '请输入标准名称',
										col: 3
									}
								]
							}
						],
						// 列表配置
						thead: [
							{
								title: '标准名称',
								align: 'center',
								field: 'name',
								showOverflowTooltip: true,
								minWidth: '130px'
							},
							{
								title: '创建人姓名',
								align: 'center',
								field: 'createUserName',
								showOverflowTooltip: true
							},
							{
								title: '创建时间',
								align: 'center',
								field: 'createTime',
								width: '160px',
								showOverflowTooltip: true
							},
							{
								title: '更新时间',
								align: 'center',
								field: 'updateTime',
								width: '160px',
								showOverflowTooltip: true
							},
							{
								title: '操作',
								type: 'handle',
								width: 180,
								fixed: 'right',
								template: '',
								events: [
									// 0-草稿  1-审核中  2-审核通过  9-驳回
									{
										code: 'look',
										icon: 'es-icon-chakan',
										text: '查看'
									},
									{
										code: 'edit',
										text: '编辑',
										icon: 'es-icon-bianji',
										rules: rows => rows.status === '0'
									},
									{
										code: 'delect',
										text: '删除',
										icon: 'es-icon-shanchu',
										rules: rows => rows.status === '0'
									}
								]
							}
						]
					};
					break;
				// 技术产品
				case 'technicalProduct':
					obj = {
						// 基本配置
						basics: {
							dataTableUrl: '/ybzy/paTechnicalProducts/listJson', // 列表接口
							delete: '/ybzy/paTechnicalProducts/removeById',
							info: '/ybzy/paTechnicalProducts/info',
							totalStatus: '/ybzy/paTechnicalProducts/total-status'
						},
						// 筛选配置
						toolbar: [
							{
								type: 'button',
								contents: []
							},
							{
								type: 'search',
								reset: true,
								contents: [
									{
										type: 'text',
										label: '成果名称',
										name: 'name',
										placeholder: '请输入成果名称',
										col: 3
									}
								]
							}
						],
						// 列表配置
						thead: [
							{
								title: '成果名称',
								align: 'center',
								field: 'name',
								showOverflowTooltip: true,
								minWidth: '130px'
							},
							{
								title: '创建人姓名',
								align: 'center',
								field: 'createUserName',
								showOverflowTooltip: true
							},
							{
								title: '创建时间',
								align: 'center',
								field: 'createTime',
								width: '160px',
								showOverflowTooltip: true
							},
							{
								title: '更新时间',
								align: 'center',
								field: 'updateTime',
								width: '160px',
								showOverflowTooltip: true
							},
							{
								title: '操作',
								type: 'handle',
								width: 180,
								fixed: 'right',
								template: '',
								events: [
									// 0-草稿  1-审核中  2-审核通过  9-驳回
									{
										code: 'look',
										icon: 'es-icon-chakan',
										text: '查看'
									},
									{
										code: 'edit',
										text: '编辑',
										icon: 'es-icon-bianji',
										rules: rows => rows.status === '0'
									},
									{
										code: 'delect',
										text: '删除',
										icon: 'es-icon-shanchu',
										rules: rows => rows.status === '0'
									}
								]
							}
						]
					};
					break;
				// 获奖成果 分社科、自然两种类型
				case 'awardAchievement':
					obj = {
						// 基本配置
						basics: {
							dataTableUrl: '/ybzy/paAwards/listJson', // 列表接口
							delete: '/ybzy/paAwards/removeById',
							info: '/ybzy/paAwards/info',
							totalStatus: '/ybzy/paAwards/total-status'
						},
						// 筛选配置
						toolbar: [
							{
								type: 'button',
								contents: []
							},
							{
								type: 'search',
								reset: true,
								contents: [
									{
										type: 'text',
										label: '成果名称',
										name: 'name',
										placeholder: '请输入成果名称',
										col: 3
									}
								]
							}
						],
						// 列表配置
						thead: [
							{
								title: '成果名称',
								align: 'center',
								field: 'name',
								showOverflowTooltip: true,
								minWidth: '130px'
							},
							{
								title: '创建人姓名',
								align: 'center',
								field: 'createUserName',
								showOverflowTooltip: true
							},
							{
								title: '创建时间',
								align: 'center',
								field: 'createTime',
								width: '160px',
								showOverflowTooltip: true
							},
							{
								title: '更新时间',
								align: 'center',
								field: 'updateTime',
								width: '160px',
								showOverflowTooltip: true
							},
							{
								title: '操作',
								type: 'handle',
								width: 180,
								fixed: 'right',
								template: '',
								events: [
									// 0-草稿  1-审核中  2-审核通过  9-驳回
									{
										code: 'look',
										icon: 'es-icon-chakan',
										text: '查看'
									},
									{
										code: 'edit',
										text: '编辑',
										icon: 'es-icon-bianji',
										rules: rows => rows.status === '0'
									},
									{
										code: 'delect',
										text: '删除',
										icon: 'es-icon-shanchu',
										rules: rows => rows.status === '0'
									}
								]
							}
						]
					};
					break;
				// 软著
				case 'softwareAchievement':
					obj = {
						// 基本配置
						basics: {
							dataTableUrl: '/ybzy/paSoftwareCopyright/listJson', // 列表接口
							delete: '/ybzy/paSoftwareCopyright/removeById',
							info: '/ybzy/paSoftwareCopyright/info',
							totalStatus: '/ybzy/paSoftwareCopyright/total-status'
						},
						// 筛选配置
						toolbar: [
							{
								type: 'button',
								contents: []
							},
							{
								type: 'search',
								reset: true,
								contents: [
									{
										type: 'text',
										label: '软件名称',
										name: 'name',
										placeholder: '请输入软件名称',
										col: 3
									}
								]
							}
						],
						// 列表配置
						thead: [
							{
								title: '软件名称',
								align: 'center',
								field: 'name',
								showOverflowTooltip: true,
								minWidth: '130px'
							},
							{
								title: '创建人姓名',
								align: 'center',
								field: 'createUserName',
								showOverflowTooltip: true
							},
							{
								title: '创建时间',
								align: 'center',
								field: 'createTime',
								width: '160px',
								showOverflowTooltip: true
							},
							{
								title: '更新时间',
								align: 'center',
								field: 'updateTime',
								width: '160px',
								showOverflowTooltip: true
							},
							{
								title: '操作',
								type: 'handle',
								width: 180,
								fixed: 'right',
								template: '',
								events: [
									// 0-草稿  1-审核中  2-审核通过  9-驳回
									{
										code: 'look',
										icon: 'es-icon-chakan',
										text: '查看'
									},
									{
										code: 'edit',
										text: '编辑',
										icon: 'es-icon-bianji',
										rules: rows => rows.status === '0'
									},
									{
										code: 'delect',
										text: '删除',
										icon: 'es-icon-shanchu',
										rules: rows => rows.status === '0'
									}
								]
							}
						]
					};
					break;
				// 科技转化成果
				case 'scientificConversionAchievement':
					obj = {
						// 基本配置
						basics: {
							dataTableUrl: '/ybzy/paScienceAchievement/listJson', // 列表接口
							delete: '/ybzy/paScienceAchievement/removeById',
							info: '/ybzy/paScienceAchievement/info',
							totalStatus: '/ybzy/paScienceAchievement/total-status'
						},
						// 筛选配置
						toolbar: [
							{
								type: 'button',
								contents: []
							},
							{
								type: 'search',
								reset: true,
								contents: [
									{
										type: 'text',
										label: '成果名称',
										name: 'name',
										placeholder: '请输入成果名称',
										col: 3
									}
								]
							}
						],
						// 列表配置
						thead: [
							{
								title: '成果名称',
								align: 'center',
								field: 'name',
								showOverflowTooltip: true,
								minWidth: '130px'
							},
							{
								title: '创建人姓名',
								align: 'center',
								field: 'createUserName',
								showOverflowTooltip: true
							},
							{
								title: '创建时间',
								align: 'center',
								field: 'createTime',
								width: '160px',
								showOverflowTooltip: true
							},
							{
								title: '更新时间',
								align: 'center',
								field: 'updateTime',
								width: '160px',
								showOverflowTooltip: true
							},
							{
								title: '操作',
								type: 'handle',
								width: 180,
								fixed: 'right',
								template: '',
								events: [
									// 0-草稿  1-审核中  2-审核通过  9-驳回
									{
										code: 'look',
										icon: 'es-icon-chakan',
										text: '查看'
									},
									{
										code: 'edit',
										text: '编辑',
										icon: 'es-icon-bianji',
										rules: rows => rows.status === '0'
									},
									{
										code: 'delect',
										text: '删除',
										icon: 'es-icon-shanchu',
										rules: rows => rows.status === '0'
									}
								]
							}
						]
					};
					break;
				// 竞赛获奖
				case 'pa_competition':
					obj = {
						// 基本配置
						basics: {
							dataTableUrl: '/ybzy/paCompetition/listJson', // 列表接口
							delete: '/ybzy/paCompetition/deleteById',
							info: '/ybzy/paCompetition/info',
							totalStatus: '/ybzy/paCompetition/total-status' //页签
						},
						// 筛选配置
						toolbar: [
							{
								type: 'button',
								contents: []
							},
							{
								type: 'search',
								reset: true,
								contents: [
									{
										type: 'text',
										label: '获奖名称',
										name: 'name',
										placeholder: '请输入获奖名称',
										col: 3
									}
								]
							}
						],
						thead: [
							{
								title: '获奖名称',
								align: 'center',
								field: 'name',
								showOverflowTooltip: true,
								minWidth: '130px'
							},
							{
								title: '创建人姓名',
								align: 'center',
								field: 'createUserName',
								showOverflowTooltip: true
							},
							{
								title: '创建时间',
								align: 'center',
								field: 'createTime',
								width: '160px',
								showOverflowTooltip: true
							},
							{
								title: '更新时间',
								align: 'center',
								field: 'updateTime',
								width: '160px',
								showOverflowTooltip: true
							},
							{
								title: '操作',
								type: 'handle',
								width: 180,
								fixed: 'right',
								template: '',
								events: [
									// 0-草稿  1-审核中  2-审核通过  9-驳回
									{
										code: 'look',
										icon: 'es-icon-chakan',
										text: '查看'
									},
									{
										code: 'edit',
										text: '编辑',
										icon: 'es-icon-bianji',
										rules: rows => rows.status === '0'
									},
									{
										code: 'delect',
										text: '删除',
										icon: 'es-icon-shanchu',
										rules: rows => rows.status === '0'
									}
								]
							}
						]
					};
					break;
				default:
					break;
			}
			return obj;
		}
	}
};
