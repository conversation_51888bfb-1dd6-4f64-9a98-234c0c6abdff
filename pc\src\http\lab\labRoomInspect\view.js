export default {
	computed: {
		viewItemList() {
			return [
				{
					name: 'inspectDate',
					label: '日期',
					value: '',
					col: 12
				},
				{
					name: 'roomName',
					label: '实验室名称',
					value: '',
					col: 12
				},
				{
					name: 'roomPersonLiable',
					label: '实验室负责人（实验员）',
					value: '',
					col: 12
				},
				{
					name: 'normal',
					label: '是否正常',
					value: '',
					col: 12
				},
				{
					name: 'existProblem',
					label: '存在具体问题',
					value: '',
					col: 12
				},
				{
					label: '附件',
					type: 'attachment',
					code: 'lab_room_inspect_file',
					ownId: this.viewData.id, // 业务id
					dragSort: true,
					limit: 8,
					col: 12
				},
				{
					name: 'inspectPerson',
					label: '检查人员',
					value: '',
					hide: this.viewData.checkStatus !== '1',
					col: 12
				},
				{
					name: 'checkDate',
					label: '检查日期',
					value: '',
					hide: this.viewData.checkStatus !== '1',
					col: 12
				},
				{
					name: 'checkDescribe',
					label: '检查描述',
					value: '',
					hide: this.viewData.checkStatus !== '1',
					col: 12
				}
			];
		}
	}
};
