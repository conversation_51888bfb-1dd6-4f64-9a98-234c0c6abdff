<!--
 @desc: 科研管理系统 立项项目 项目清单 
 @author: WH
 @date: 2023/11/13
 -->
<template>
	<main>
		<es-data-table
			ref="table"
			style="width: 100%"
			:row-style="tableRowClassName"
			:border="true"
			:checkbox="checkbox"
			:thead="thead"
			:page="true"
			stripe
			:url="basics.dataTableUrl"
			:param="param"
			:toolbar="toolbar"
			:response="func"
			method="get"
			close
			@submit="submit"
			@btnClick="btnClick"
		></es-data-table>

		<!-- height="dialogHeight" 不能被识别到具体高度 会根据内容自动撑开 -->
		<!-- size="full"  -->
		<es-dialog :title="title" :visible.sync="visible" :show-scale="false" size="full" height="100%">
			<Detail
				v-if="visible"
				:id="rowId"
				:key="visible"
				:open-type="openType"
				:title="title"
				:is-flow-pattern="false"
				:project-classify="projectClassify"
				:init-active-name="initActiveName"
				@reload="reload"
			/>
		</es-dialog>
		<!-- 
			parmas:
				approvalId: 项目id String
				showApprovalForm: 弹出框是否展示 false true
			methods:
				changeShow: 改变showAchievementForm
		-->
		<ApprovalForm
			v-if="showApprovalForm"
			:approval-id="approvalId"
			:project-approval-form="projectApprovalForm"
			:project-classify="projectClassify"
			:show-approval-form="showApprovalForm"
			@changeShow="changeShow"
		></ApprovalForm>
	</main>
</template>

<script>
// import { delPorjectByid, getPorjectList } from '@/api/scientific-sys.js';
import ApprovalForm from '@/components/form/approval-form.vue';

import Detail from './detail.vue';
import { openWindowHref } from '@/utils/index.js';
import { createNamespacedHelpers } from 'vuex';
const { mapState } = createNamespacedHelpers('getRoleMap');

export default {
	name: 'ApprovalList',
	components: { Detail, ApprovalForm },
	data() {
		return {
			basics: {
				dataTableUrl: '/ybzy/projectBaseInfo/listJson' // 列表接口
			},
			// tableUrl: getPorjectList,
			param: {
				type: 0
			},
			submitParams: {},
			showApprovalForm: false,
			//立项数据
			approvalId: '',
			projectClassify: '', //0 为纵向项目
			projectApprovalForm: {
				studyStartDate: null,
				studyEndDate: null
			},
			rowId: '',
			initActiveName: 'BasicInfo',
			toolbar: [
				{
					type: 'button',
					contents: [
						{
							text: '导出',
							code: 'importFile',
							type: 'primary'
						}
					]
				},
				{
					type: 'search',
					reset: true,
					contents: [
						{
							type: 'text',
							label: '关键字查询',
							name: 'keyword',
							placeholder: '请输入关键字',
							col: 3
						}
					]
				},
				{
					type: 'filter',
					contents: [
						{
							type: 'monthrange',
							label: '年度',
							name: 'declareTimeStartAndEnd',
							default: true,
							placeholder: '选择年度日期',
							clearable: true,
							col: 3
						},
						{
							type: 'text',
							label: '部门/二级学院',
							name: 'declareOrgName',
							placeholder: '输入部门/二级学院查询',
							col: 3
						},
						{
							type: 'text',
							label: '申报人 ',
							name: 'createUserName',
							placeholder: '输入申报人查询',
							col: 3
						},
						{
							type: 'text',
							label: '参与人 ',
							name: 'memberName',
							placeholder: '输入参与人查询',
							col: 3
						},
						{
							type: 'select',
							label: '项目类型',
							placeholder: '选择查询',
							name: 'projectType',
							sysCode: 'project_type',
							col: 3
						},
						{
							type: 'text',
							label: '项目名称',
							name: 'projectName',
							placeholder: '输入项目名称查询',
							col: 3
						},
						{
							type: 'select',
							label: '项目分类',
							placeholder: '选择查询',
							name: 'projectClassify',
							sysCode: 'project_classify',
							col: 3
						},
						{
							type: 'select',
							label: '项目状态',
							placeholder: '选择查询',
							name: 'state',
							sysCode: 'project_status',
							col: 3
						}
					]
				}
			],
			checkbox: false, //未上报开启选项框 提供多线批量催报功能
			selectList: [], //批量催报选项
			lookUrgeData: {}, //查看催报表单入参
			dialogHeight: '',
			// 弹窗
			visible: false,
			openType: 'look',
			showType: 'submitForm', //修改或者是(申请修改、审核)
			// 当前id
			formId: '',
			title: '查看',
			thead: [
				{
					title: '项目编号',
					field: 'projectNumber',
					align: 'center',
					minWidth: 180,
					showOverflowTooltip: true
				},
				{
					title: '项目名称',
					field: 'projectName',
					align: 'center',
					minWidth: 220,
					showOverflowTooltip: true
				},
				{
					title: '项目分类',
					field: 'projectClassifyTxt',
					align: 'center',
					minWidth: 90
				},
				{
					title: '申报人',
					field: 'createUserName',
					align: 'center',
					minWidth: 90
				},
				{
					title: '申报人电话',
					field: 'declarePhone',
					align: 'center',
					minWidth: 150
				},
				{
					title: '参与人',
					field: 'allMemberNames',
					showOverflowTooltip: true,
					minWidth: 120,
					align: 'center'
				},
				{
					title: '所在部门/学院',
					field: 'declareOrgName',
					align: 'center',
					minWidth: 180
				},
				// {
				// 	title: '已使用经费（元）',
				// 	field: 'expenditureAllUse',
				// 	align: 'center',
				// 	minWidth: 180
				// },
				{
					title: '项目状态',
					align: 'center',
					field: 'stateTxt',
					width: '90px',
					fixed: 'right',
					render: (h, params) => {
						let stateTxt = params.row.stateTxt;
						let state = params.row.state;
						return h(
							'el-tag',
							{
								props: {
									size: 'small',
									type: this.projecstate(state)
								}
							},
							stateTxt
						);
					}
				},
				{
					title: '操作',
					type: 'handle',
					width: '140',
					template: '',
					fixed: 'right',
					align: 'center',
					events: [
						{
							text: '查看项目',
							btnType: 'look'
						},
						// {
						// 	text: '编辑',
						// 	btnType: 'edit'
						// 	// rules: rows => [0, 1, 4, 6, 8].includes(rows.projectStatus)
						// },
						// {
						// 	text: '编辑',
						// 	btnType: 'edit'
						// 	rules: rows => [0, 1, 4, 6, 8].includes(rows.projectStatus)
						// },
						//已立项和待结项状态可以进行资金管理
						// {
						// 	text: '资金管理',
						// 	btnType: 'editCapital',
						// 	rules: rows => [1, 3].includes(rows.projectStatus)
						// }
						{
							text: '立项',
							btnType: 'approval',
							rules: rows => rows.state == 0 && rows.isScienceManager == 1
						},
						{
							text: '立项信息',
							btnType: 'approvalInfo',
							rules: rows => rows.state != 0
						}
						// {
						// 	text: '删除',
						// 	btnType: 'del'
						// }
					]
				}
			]
		};
	},
	computed: {
		...mapState(['projectScienceManager'])
	},
	watch: {
		projectScienceManager: {
			immediate: true,
			handler(newVal) {
				// projectScienceManager：父级传递的身份标识 如果为科技处老师值为true 可进行修改删除操作，反之不可
				if (typeof newVal == 'boolean' && newVal === true) {
					this.thead[this.thead.length - 1].events.push({
						text: '立项',
						btnType: 'approval',
						rules: rows => [0].includes(rows.projectStatus)
					});
				}
			}
		}
	},
	methods: {
		/**
		 * 表格行高
		 */
		tableRowClassName({ row, rowIndex }) {
			let styleRes = {
				height: '54px !important'
			};
			return styleRes;
		},
		// 获取项目审核状态
		projecstate(state) {
			let stateCurrent = '';
			// 项目状态（字典编码：project_status）0：待立项； 1：已立项； 2：立项不通过； 3：变更审核中； 4：结题审核中； 5：已结题； 6：已归档； 7：终止；
			switch (state) {
				case '0':
					stateCurrent = 'warning';
					break;
				case '3':
				case '4':
					stateCurrent = 'primary';
					break;
				case '1':
				case '5':
				case '6':
					stateCurrent = 'success';
					break;
				// case '2':
				// 	stateCurrent = 'info';
				// 	break;
				case '2':
				case '7':
					stateCurrent = 'danger';
					break;
				default:
					break;
			}
			return stateCurrent;
		},
		// 高级搜索确认
		submit(e) {
			this.submitParams = e.data;
		},
		reload(reload) {
			reload && this.$refs.table.reload();
		},
		// 改变子组件弹窗状态
		changeShow(addFinish) {
			if (addFinish) {
				this.$refs.table.reload();
			}
			this.showApprovalForm = this.showApprovalForm == false ? true : false;
		},
		//row=null  考虑到toolbar按钮不存在row
		btnClick(val) {
			const btnType = val.handle.btnType || val.handle.code;
			const row = val.row;

			this.approvalId = row?.id;
			this.projectClassify = row?.projectClassify;
			row && (this.rowId = row.id);
			let btnTask = {
				look: () => {
					this.initActiveName = 'BasicInfo';
					this.openType = 'look';
					this.title = '查看';
					this.visible = true;
				},
				approval: () => {
					this.approvalProject(row, '立项');
				},
				approvalInfo: () => {
					this.approvalProject(row, '立项信息');
				},
				edit: () => {
					this.initActiveName = 'BasicInfo';
					this.openType = 'edit';
					this.title = '编辑';
					this.visible = true;
				},
				editCapital: () => {
					this.openType = 'edit';
					this.initActiveName = 'CapitalInfo';
					this.title = '编辑';
					this.visible = true;
				},
				del: () => {
					// let _this = this;
					this.$confirm('是否删除?', '提示', {
						confirmButtonText: '确定',
						cancelButtonText: '取消',
						type: 'warning'
					})
						.then(() => {
							this.delPorjectByid(row.id);
						})
						.catch(() => {});
				},
				importFile: () => {
					let isDev = process.env.NODE_ENV === 'development' ? '/api' : '';
					const params = { ...this.param, ...this.submitParams };
					let url = `${isDev}/ybzy/projectBaseInfo/export`;
					let urlParams = this.objToUrlParams(params);
					if (urlParams) {
						url += '?' + urlParams;
					}
					window.open(url, '_self');
				}
			};
			btnTask[btnType]();
		},
		// 立项弹窗
		approvalProject(row, title) {
			this.initActiveName = 'BasicInfo';
			this.openType = 'approval';
			this.title = title;
			const projectApprovalForm = {
				id: row.id,
				state: row.state,
				studyStartDate: row.studyStartDate,
				studyEndDate: row.studyEndDate,
				approvalTime: row.approvalTime,
				isApproval: row.isApproval,
				projectName: row.projectName
			};

			this.projectApprovalForm = projectApprovalForm;
			this.showApprovalForm = true;
		},
		objToUrlParams(obj) {
			return Object.keys(obj)
				.map(key => encodeURIComponent(key) + '=' + encodeURIComponent(obj[key]))
				.join('&');
		},
		// 页面加载
		load(text) {
			return this.$.loading(this.$loading, text);
		},
		func(res) {
			let { data } = res;
			// console.log('>>>res', res);
			return data;
		},

		//需要配置路由名称
		openWindow() {
			window.open(
				openWindowHref() +
					this.$router.resolve({
						name: 'scientificDeail',
						query: {
							id: this.rowId,
							openType: this.openType
						}
					}).href
			);
		},

		// 删除
		async delPorjectByid(id) {
			const loading = this.load('提交中...');
			try {
				let { rCode, msg } = await this.$.ajax({
					url: this.basics.delete,
					method: 'post',
					// format: false,
					data: { id }
				});
				if (rCode == 0) {
					this.$message({
						type: 'success',
						message: msg
					});
					this.$refs.table.reload();
				} else {
					this.$message.error(msg);
				}
			} finally {
				loading.close();
			}
		}
	}
};
</script>
<style lang="scss" scoped>
main {
	width: 100%;
	height: 100%;
}
::v-deep {
	.el-dialog {
		height: 100%;
	}
}
</style>
