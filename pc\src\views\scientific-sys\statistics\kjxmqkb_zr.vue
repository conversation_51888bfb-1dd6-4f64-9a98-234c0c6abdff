<template>
	<div v-loading="loading" class="content">
		<es-data-table
			ref="table"
			:row-style="tableRowClassName"
			:thead="thead"
			:toolbar="toolbar"
			:border="true"
			filter
			:page="pageOption"
			:url="basics.dataTableUrl"
			:numbers="true"
			:param="params"
			show-label
			close
			@search="search"
			@reset="search({})"
			@btnClick="btnClick"
		></es-data-table>
	</div>
</template>

<script>
import { host } from '@/../config/config';
import qs from 'qs';
export default {
	data() {
		return {
			basics: {
				dataTableUrl: '/ybzy/projectBaseInfo/queryPageMap', // 列表接口
				download: '/ybzy/projectBaseInfo/exportStatistics' // 导出
			},
			loading: false,
			params: {
				projectType: 'nature',
				orderBy: 'createTime',
				asc: 'false' // true 升序，false 降序
			},
			selectParams: {},
			pageOption: {
				layout: 'total, prev, pager, next, jumper',
				pageSize: 20,
				// // hideOnSinglePage: true,
				position: 'center',
				current: 1,
				pageNum: 1
			},
			codesObj: {} //批量数据字典
		};
	},
	computed: {
		thead() {
			return [
				{
					title: '项目名称',
					align: 'center',
					showOverflowTooltip: true,
					width: 180,
					fixed: 'left',
					field: 'projectName'
				},
				{
					title: '项目批准（合同签定）时间',
					align: 'center',
					showOverflowTooltip: true,
					width: 110,
					field: 'approveDate'
				},
				{
					title: '当年拨入经费（千元）',
					align: 'center',
					showOverflowTooltip: true,
					width: 110,
					field: 'appropriatedGovernmental'
				},
				{
					title: '当年内部支出经费（千元）',
					align: 'center',
					field: 'expenditureFunds',
					showOverflowTooltip: true,
					width: 110
				},
				{
					title: '当年投入人员（人年）',
					align: 'center',
					field: 'p2',
					children: [
						{
							label: '合计',
							field: 'inputTotal',
							type: 'text',
							width: 80
						},
						{
							label: '其中：女',
							field: 'inputFemaleNum',
							type: 'text',
							width: 80
						},
						{
							label: '高级职称',
							field: 'inputHighNum',
							type: 'text',
							width: 80
						},
						{
							label: '中级职称',
							field: 'inputMiddleNum',
							type: 'text',
							width: 80
						},
						{
							label: '初级职称',
							field: 'inputElementaryNum',
							type: 'text',
							width: 80
						},
						{
							label: '其他',
							field: 'inputElseNum',
							type: 'text',
							width: 80
						}
					]
				},
				{
					title: '博士生折合人时（人年）',
					align: 'center',
					showOverflowTooltip: true,
					width: 110,
					field: 'doctorPerYear'
				},
				{
					align: 'center',
					title: '参与项目的研究生数（人）',
					field: 'p1',
					children: [
						{
							label: '合计',
							field: 'joinDoctorMasterSum',
							type: 'text',
							width: 80
						},
						{
							label: '其中:博士生',
							field: 'joinDoctorNum',
							type: 'text',
							width: 80
						},
						{
							label: '硕士生',
							field: 'joinMasterNum',
							type: 'text',
							width: 80
						}
					]
				},
				{
					title: '学科分类',
					align: 'center',
					showOverflowTooltip: true,
					width: 110,
					field: 'subjectType'
				},
				{
					title: '活动类型',
					align: 'center',
					showOverflowTooltip: true,
					width: 110,
					field: 'activityType'
				},
				{
					title: '项目来源',
					align: 'center',
					width: 160,
					showOverflowTooltip: true,
					field: 'projectSource'
				},
				{
					title: '组织形式',
					align: 'center',
					width: 90,
					showOverflowTooltip: true,
					field: 'orgForm'
				},
				{
					title: '合作形式',
					align: 'center',
					width: 110,
					showOverflowTooltip: true,
					field: 'cooForm'
				},
				{
					title: '服务的国民经济行业',
					align: 'center',
					width: 110,
					showOverflowTooltip: true,
					field: 'ecoIndustryType'
				},
				{
					title: '项目的社会经济目标',
					align: 'center',
					width: 110,
					showOverflowTooltip: true,
					field: 'ecoGoalType'
				},
				{
					title: '国际合作项目标识和企事业单位委托省内外标识',
					align: 'center',
					width: 110,
					showOverflowTooltip: true,
					field: 'isSign'
				}
			];
		},
		toolbar() {
			return [
				{
					type: 'button',
					contents: [
						{
							text: '导出',
							exportXls: true,
							icon: 'es-icon-daochu',
							type: 'primary',
							event: () => {
								const params = { ...this.params, ...this.selectParams };
								const url = host + this.basics.download + '?' + qs.stringify(params);
								window.open(url, '_self');
							}
						}
					]
				},
				{
					type: 'search',
					reset: true,
					showLabel: true,
					contents: [
						{
							type: 'daterange',
							name: 'dates',
							label: '时间范围',
							placeholder: '请选择时间范围'
						},
						{ type: 'text', label: '关键字', name: 'keyword', placeholder: '请输入关键字查询' }
					]
				}
			];
		}
	},
	created() {
		const codes = 'member_nation,hq_person_education,hq_person_state';
		this.$utils.findSysCodeList(codes).then(res => {
			this.codesObj = res;
		});
	},
	methods: {
		//表格行高
		tableRowClassName({ row, rowIndex }) {
			let styleRes = { height: '54px !important' };
			return styleRes;
		},
		//操作按钮点击事件
		btnClick(res) {},
		search(res) {
			const paramsN = { ...res };
			if (paramsN.dates) {
				paramsN.startDate = paramsN.dates[0] || '';
				paramsN.endDate = paramsN.dates[1] || '';
				delete paramsN.dates;
			}
			this.params = {
				...{
					projectType: this.params.projectType,
					orderBy: this.params.orderBy,
					asc: this.params.asc // true 升序，false 降序
				},
				...paramsN
			};
			this.selectParams = paramsN;
		},
		refreshData() {
			this.$refs.table.reload();
		}
	}
};
</script>

<style scoped lang="scss">
.content {
	width: 100%;
	height: 100%;
}
</style>
