<template>
	<div class="serve-hall">
		<!-- 顶部导航栏 -->
		<div class="hall-top">
			<VocationalHeader class="box-center"></VocationalHeader>
		</div>
		<!-- 顶部展示图片 -->
		<img src="@/assets/images/serveHall/top-bg.png" class="navbar-top" />

		<!-- 通知公告 -->
		<div class="notice">
			<div class="title-box">
				<span class="title-info">{{ noticeContent.title }}</span>
				<span class="title-child">
					{{ noticeContent.createTime }} 来源：{{ noticeContent.source }}
				</span>
			</div>
			<div class="notice-btn">
				<BackButtom />
			</div>
			<div class="notice-content" v-html="noticeContent.detail"></div>
		</div>
	</div>
</template>

<script>
import VocationalHeader from '../header/VocationalHeader.vue';
import BackButtom from '@/components/backButtom.vue';
import { tenantId } from '@/config';
export default {
	name: 'ServeHall',
	components: {
		VocationalHeader,
		BackButtom
	},
	data() {
		return {
			noticeContent: {}
		};
	},
	created() {
		this.getNotice();
		this.read();
	},
	methods: {
		//获取通知详情
		// this.$route.query.id || '';
		getNotice() {
			this.$.ajax({
				url: '/ybzy/cmsinfo/front/detail',
				params: {
					code: 'educationServiceNotice',
					id: this.$route.query.id || '',
					tenantId: tenantId
				}
			}).then(res => {
				this.noticeContent = res?.results || {};
			});
		},
		// 公告已读
		read() {
			this.$.ajax({
				url: '/ybzy/specificHomepageShortcut/save',
				method: 'POST',
				data: {
					sourceId: this.$route.query.id || '',
					type: 2, // 0, "收藏"  1, "最近访问" 2, "公告已读"
					sourceType: 0 //目标类型（0：系统，1：菜单）
				}
			});
		}
	}
};
</script>

<style lang="scss" scoped>
$maxWidth: 1200px;
.box-center {
	width: $maxWidth;
	margin: 0 auto;
}
.serve-hall {
	font-family: MicrosoftYaHei;
	width: 100%;
	height: 100%;
	background: #ecf0f4;
	min-width: 1550px;
	position: relative;
	overflow: auto;
	.hall-top {
		width: 100%;
		background: #0175e8;
	}
	.navbar-top {
		width: 100%;
		height: 200px;
		object-fit: cover;
	}
	.notice {
		width: 1200px;
		background: #ffffff;
		border-radius: 8px;
		margin: 20px auto;
		padding: 20px 20px 30px 20px;
		position: relative;
		.title-box {
			cursor: default;
			display: flex;
			flex-direction: column;
			justify-content: center;
			align-items: center;
			margin-bottom: 20px;
			margin-top: 58px;
			.title-info {
				height: 26px;
				font-size: 22px;
				color: #000000;
				line-height: 26px;
				margin-bottom: 10px;
			}

			.title-child {
				// width: 190px;
				height: 22px;
				font-size: 14px;
				font-family: ArialMT;
				color: #a1a5af;
				line-height: 22px;
			}
		}
		.notice-btn {
			position: absolute;
			top: 20px;
			left: 20px;
		}
		.notice-content {
		}
	}
}
</style>
