<template>
	<es-form
		ref="form"
		:model="formData1"
		:contents="formList1"
		:submit="
			type === 'audit' ||
            type === 'view'
		"
		:readonly="isReadOnly"
		v-bind="$attrs"
		@submit="handleSubmit"
		@reset="handleCancel"
        height="100%" :genre="2" collapse
	></es-form>
</template>
<script>

import interfaceUrl from '@/http/platform/person.js';
import $ from 'eoss-ui/lib/utils/util';
import request from 'eoss-ui/lib/utils/http';
import SnowflakeId from "snowflake-id";

export default {
	name: 'audit',
	props: {
		// 上级编码ID
		cciPid: {
			type: String
		},
		selectInfo: {
			type: String
		},
		type: {
			type: String
		},
		isReadOnly: {
			type: Boolean,
			default: false
		},
		formInfo: {
			type: Object,
			default: () => {}
		},
        regionData:{
            type: Array,
			default: () => []
        }
	},
	data() {
		return {
            //regionData:[],
            typeDicData:[],
			formData1: {
				...this.formInfo
			},
            auditBtn: {
                type: 'submit',
                skin: 'lay-form-btns',
                contents: [
                    {
                        type: 'primary',
                        text: '审核通过',
                        event: 'confirm',
                    },
                    {
                        type: 'primary',
                        text: '审核不通过',
                        event: 'confirm',
                    },
                ]
            },
            viewBtn:{
                type: 'submit',
                skin: 'lay-form-btns',
                contents: [
                    {
                        type: 'reset',
                        text: '关闭',
                        event: 'cancel',
                    },
                ]
            }
            
		};
	},
	computed: {
        tearcherFormItemList(){
            return  {
                title: '教职工信息',
                contents: [
                    {
                        label: '职工号',
                        name: 'platPersonTeacher_zgh',
                        type: 'input',
                        placeholder: '请选择职工号',
                        readonly: true,
                        rules: {
                            required: false,
                            message: '请选择职工号',
                            trigger: 'blur'
                        },
                        verify: 'required',
                        col: 5,
                    },
                    {
                        label: '所在单位',
                        name: 'platPersonTeacher_szdw',
                        placeholder: '请输入所在单位',
                        readonly: true,
                        rules: {
                            required: false,
                            message: '请输入所在单位',
                            trigger: 'blur'
                        },
                        verify: 'required',
                        col: 5
                    },
                    {
                        label: '当前状态',
                        name: 'platPersonTeacher_dqzt',
                        type: 'select',
                        placeholder: '请输入当前状态',
                        readonly: true,
                        rules: {
                            required: false,
                            message: '请输入当前状态',
                            trigger: 'blur'
                        },
                        col: 5
                    },
                    {
                        label: '籍贯代码',
                        readonly: true,
                        name: 'platPersonTeacher_jgdm',
                        placeholder: '请输入籍贯代码',
                        col: 5
                    },
                    {
                        label: '学历代码',
                        name: 'platPersonTeacher_xwdm',
                        readonly: true,
                        type: 'select',
                        placeholder: '请输入学历代码',
                        col: 5,
                        rules: {
                            required: false,
                            message: '请选择最高学位',
                            trigger: 'blur'
                        },
                        sysCode: 'plat_person_xldm',
                        'label-key': 'shortName',
                        'value-key': 'cciValue',
                    },
                    {
                        label: '最高学位',
                        name: 'platPersonTeacher_zgxw',
                        readonly: true,
                        type: 'select',
                        placeholder: '请选择最高学位',
                        rules: {
                            required: false,
                            message: '请选择最高学位',
                            trigger: 'blur'
                        },
                        sysCode: 'plat_person_xwdm',
                        'label-key': 'shortName',
                        'value-key': 'cciValue',
                        verify: 'required',
                        col: 5,
                    },
                    {
                        label: '来校日期',
                        name: 'platPersonTeacher_lxrq',
                        readonly: true,
                        type: 'date',
                        placeholder: '请选择来校日期',
                        rules: {
                            required: false,
                            message: '请选择最高学位',
                            trigger: 'blur'
                        },
                        verify: 'required',
                        col: 5,
                        
                    },
                    {
                        label: '教师类别',
                        name: 'platPersonTeacher_jslb',
                        readonly: true,
                        type: 'select',
                        placeholder: '请选择教师类别',
                        rules: {
                            required: false,
                            message: '请选择教师类别',
                            trigger: 'blur'
                        },
                        verify: 'required',
                        col: 5,
                        sysCode: 'plat_person_teacher_JSLBDM',
                        'label-key': 'shortName',
                        'value-key': 'cciValue',
                    },
                    {
                        label: '最高学历',
                        name: 'platPersonTeacher_zgxl',
                        readonly: true,
                        placeholder: '请输入最高学历',
                        rules: {
                            required: false,
                            message: '请选择最高学历',
                            trigger: 'blur'
                        },
                        verify: 'required',
                        col: 5
                    },
                    {
                        label: '参加工作日期',
                        name: 'platPersonTeacher_cjgzrq',
                        type: 'date',
                        readonly: true,
                        placeholder: '请选择参加工作日期',
                        rules: {
                            required: false,
                            message: '请选择参加工作日期',
                            trigger: 'blur'
                        },
                        verify: 'required',
                        col: 5,
                    },
                    {
                        label: '专业技术职务',
                        name: 'platPersonTeacher_zyjszw',
                        readonly: true,
                        type: 'input',
                        placeholder: '请输入专业技术职务',
                        col: 5,
                    },
                ]
            };
        },
        studentFormItemList(){
            return  {
                title: '学生信息',
                contents: [
                    {
                        label: '学号',
                        name: 'platPersonStudent_xh',
                        type: 'input',
                        placeholder: '请选择学号',
                        readonly: true,
                        rules: {
                            required: false,
                            message: '请选择学号',
                            trigger: 'blur'
                        },
                        verify: 'required',
                        col: 5,
             
                    },
                    {
                        label: '血型代码',
                        name: 'platPersonStudent_xxdm',
                        placeholder: '请输入血型代码',
                        readonly: true,
                        rules: {
                            required: false,
                            message: '请输入血型代码',
                            trigger: 'blur'
                        },
                        verify: 'required',
                        col: 5
                    },
                    {
                        label: '主页地址',
                        name: 'platPersonStudent_zydz',
                        placeholder: '请输入主页地址',
                        readonly: true,
                        rules: {
                            required: false,
                            message: '请输入主页地址',
                            trigger: 'blur'
                        },
                        verify: 'required',
                        col: 5
                    },
                    {
                        label: '特长',
                        name: 'platPersonStudent_tc',
                        readonly: true,
                        placeholder: '请输入特长',
                        col: 5
                    },
                    {
                        label: '是否在校',
                        name: 'platPersonStudent_sfzx',
                        readonly: true,
                        placeholder: '请输入是否在校',
                        col: 5
                    },
                    {
                        label: '专业代码',
                        name: 'platPersonStudent_zydm',
                        readonly: true,
                        type: 'text',
                        placeholder: '请选择专业代码',
                        rules: {
                            required: false,
                            message: '请选择专业代码',
                            trigger: 'blur'
                        },
                        verify: 'required',
                        col: 5
                    },
                    {
                        label: '学制',
                        name: 'platPersonStudent_xz',
                        readonly: true,
                        type: 'select',
                        placeholder: '请输入学制',
                        col: 5,
                        sysCode: 'plat_person_xzdm',
                        'label-key': 'shortName',
                        'value-key': 'cciValue',
                    },
                    {
                        label: '学籍状态代码',
                        name: 'platPersonStudent_xjztdm',
                        readonly: true,
                        type: 'select',
                        placeholder: '请选择学籍状态代码',
                        rules: {
                            required: false,
                            message: '请选择学籍状态代码',
                            trigger: 'blur'
                        },
                        verify: 'required',
                        col: 5,
                        sysCode: 'plat_person_student_XJZTDM',
                        'label-key': 'shortName',
                        'value-key': 'cciValue',
                    },
                    {
                        label: '院系代码',
                        name: 'platPersonStudent_yxdm',
                        readonly: true,
                        placeholder: '请输入院系代码',
                        rules: {
                            required: false,
                            message: '请选择院系代码',
                            trigger: 'blur'
                        },
                        verify: 'required',
                        col: 5
                    },
                    {
                        label: '入学日期',
                        name: 'platPersonStudent_rxrq',
                        type: 'date',
                        placeholder: '请选择入学日期',
                        readonly: true,
                        rules: {
                            required: false,
                            message: '请选择入学日期',
                            trigger: 'blur'
                        },
                        verify: 'required',
                        col: 5,
                    },
                    {
                        label: 'qq号码',
                        name: 'platPersonStudent_qqhm',
                        readonly: true,
                        type: 'input',
                        placeholder: '请输入qq号码',
                        col: 5,
                    },
                    {
                        label: '家庭电话',
                        name: 'platPersonStudent_jtdh',
                        readonly: true,
                        type: 'input',
                        placeholder: '请输入家庭电话',
                        rules: {
                            required: false,
                            message: '请输入家庭电话',
                            trigger: 'blur'
                        },
                        verify: 'required',
                        col: 5,
                    },
                    {
                        label: '年级代码',
                        name: 'platPersonStudent_njdm',
                        readonly: true,
                        type: 'input',
                        placeholder: '请选择年级代码',
                        rules: {
                            required: false,
                            message: '请选择年级代码',
                            trigger: 'blur'
                        },
                        verify: 'required',
                        col: 5,
                    },
                    {
                        label: '班号',
                        name: 'platPersonStudent_bh',
                        readonly: true,
                        type: 'input',
                        placeholder: '请选择班号',
                        rules: {
                            required: false,
                            message: '请选择班号',
                            trigger: 'blur'
                        },
                        verify: 'required',
                        col: 5,
                    },
                    {
                        label: '学历层次代码',
                        name: 'platPersonStudent_xlccdm',
                        type: 'select',
                        placeholder: '请选择学历层次代码',
                        readonly: true,
                        rules: {
                            required: false,
                            message: '请选择学历层次代码',
                            trigger: 'blur'
                        },
                        verify: 'required',
                        col: 5,
                        sysCode: 'plat_person_xldm',
                        'label-key': 'shortName',
                        'value-key': 'cciValue',
                    },
                    {
                        label: '培养层次代码',
                        name: 'platPersonStudent_pycdm',
                        type: 'select',
                        placeholder: '请选择培养层次代码',
                        readonly: true,
                        rules: {
                            required: false,
                            message: '请选择培养层次代码',
                            trigger: 'blur'
                        },
                        verify: 'required',
                        col: 5,
                        sysCode: 'plat_person_pyccdm',
                        'label-key': 'shortName',
                        'value-key': 'cciValue',
                    },
                    {
                        label: '报道注册标记',
                        name: 'platPersonStudent_bdzcbj',
                        type: 'select',
                        readonly: true,
                        placeholder: '请选择报道注册标记',
                        rules: {
                            required: false,
                            message: '请选择报道注册标记',
                            trigger: 'blur'
                        },
                        verify: 'required',
                        col: 5,
                        sysCode: 'yes_or_no',
                        'label-key': 'shortName',
                        'value-key': 'cciValue',
                    },
                ]
            };
        },
        formBaseItemList() {
            return [
                {
                    title: '人员信息',
                    contents: [
                        {
                            label: '人员类型',
                            name: 'zglxText',
                            type: 'input',
                            readonly: true,
                            placeholder: '请选择人员类型',
                            rules: {
                                required: false,
                                message: '请选择人员类型',
                                trigger: 'blur'
                            },
                            verify: 'required',
                            col: 5,
                        },
                        {
                            label: '姓名',
                            name: 'xm',
                            placeholder: '请输入姓名',
                            readonly: true,
                            rules: {
                                required: false,
                                message: '请输入姓名',
                                trigger: 'blur'
                            },
                            verify: 'required',
                            col: 5
                        },
                        {
                            label: '姓名拼音',
                            name: 'xmpy',
                            placeholder: '请输入姓名拼音',
                            readonly: true,
                            rules: {
                                required: false,
                                message: '请输入姓名拼音',
                                trigger: 'blur'
                            },
                            verify: 'required',
                            col: 5
                        },
                        {
                            label: '曾用名',
                            name: 'cym',
                            placeholder: '请输入曾用名',
                            readonly: true,
                            col: 5
                        },
                        {
                            label: '英文名',
                            name: 'ywm',
                            placeholder: '请输入英文名',
                            readonly: true,
                            col: 5
                        },
                        {
                            label: '性别',
                            name: 'xbdm',
                            type: 'select',
                            readonly: true,
                            placeholder: '请选择性别',
                            rules: {
                                required: false,
                                message: '请选择性别',
                                trigger: 'blur'
                            },
                            verify: 'required',
                            col: 5,
                            sysCode: 'plat_person_xb',
                            'label-key': 'shortName',
                            'value-key': 'cciValue',
                        },
                        {
                            label: '出生日期',
                            name: 'csrq',
                            type: 'date',
                            readonly: true,
                            placeholder: '请选择出生日期',
                            col: 5,
                            
                        },
                        {
                            label: '证件类型码',
                            name: 'zjlxm',
                            type: 'select',
                            placeholder: '请选择证件类型码',
                            readonly: true,
                            rules: {
                                required: false,
                                message: '请选择证件类型码',
                                trigger: 'blur'
                            },
                            verify: 'required',
                            col: 5,
                            sysCode: 'plat_person_sfzlx',
                            'label-key': 'shortName',
                            'value-key': 'cciValue',
                        },
                        {
                            label: '证件号',
                            name: 'zjh',
                            placeholder: '请输入证件号',
                            readonly: true,
                            rules: {
                                required: false,
                                message: '请选择证件类型码',
                                trigger: 'blur'
                            },
                            verify: 'required',
                            col: 5
                        },
                        {
                            label: '民族',
                            name: 'mzdm',
                            type: 'select',
                            placeholder: '请选择民族',
                            readonly: true,
                            rules: {
                                required: false,
                                message: '请选择民族',
                                trigger: 'blur'
                            },
                            verify: 'required',
                            col: 5,
                            sysCode: '	plat_person_mz',
                            'label-key': 'shortName',
                            'value-key': 'cciValue',
                        },
                        {
                            label: '国家(地区)代码',
                            name: 'gjdqdm',
                            type: 'select',
                            placeholder: '请选择国家(地区)代码',
                            readonly: true,
                            rules: {
                                required: false,
                                message: '请选择国家(地区)代码',
                                trigger: 'blur'
                            },
                            verify: 'required',
                            col: 5,
                            sysCode: '	plat_person_gjdq',
                            'label-key': 'shortName',
                            'value-key': 'cciValue',
                        },
                        {
                            label: '港澳台侨代码',
                            name: 'gatqdm',
                            type: 'select',
                            placeholder: '请选择港澳台侨代码',
                            readonly: true,
                            col: 5,
                            sysCode: 'plat_person_qqlb',
                            'label-key': 'shortName',
                            'value-key': 'cciValue',
                        },
                        {
                            label: '政治面貌代码',
                            name: 'zzmmdm',
                            type: 'select',
                            placeholder: '请选择政治面貌代码',
                            readonly: true,
                            rules: {
                                required: false,
                                message: '请选择政治面貌代码',
                                trigger: 'blur'
                            },
                            verify: 'required',
                            col: 5,
                            sysCode: 'plat_person_zzmm',
                            'label-key': 'shortName',
                            'value-key': 'cciValue',
                        },
                        {
                            label: '婚姻状况代码',
                            name: 'hyzkdm',
                            type: 'select',
                            placeholder: '请选择婚姻状况代码',
                            readonly: true,
                            rules: {
                                required: false,
                                message: '请选择婚姻状况代码',
                                trigger: 'blur'
                            },
                            verify: 'required',
                            col: 5,
                            sysCode: 'plat_person_hyzk',
                            'label-key': 'shortName',
                            'value-key': 'cciValue',
                        },
                        {
                            label: '健康状况代码',
                            name: 'jkzkdm',
                            type: 'select',
                            readonly: true,
                            placeholder: '请选择健康状况代码',
                            rules: {
                                required: false,
                                message: '请选择健康状况代码',
                                trigger: 'blur'
                            },
                            verify: 'required',
                            col: 5,
                            sysCode: 'plat_person_jkzk',
                            'label-key': 'shortName',
                            'value-key': 'cciValue',
                        },
                        
                    ]
                },
                {
                    title: '联系信息',
                    contents: [
                        {
                            label: '手机号',
                            name: 'sjh',
                            placeholder: '请输入手机号',
                            readonly: true,
                            rules: {
                                required: false,
                                message: '请输入手机号',
                                trigger: 'blur'
                            },
                            verify: 'required',
                            col: 5
                        },
                        {
                            label: '电子邮箱',
                            name: 'dzyx',
                            placeholder: '请输入电子邮箱',
                            readonly: true,
                            rules: {
                                required: false,
                                message: '请输入电子邮箱',
                                trigger: 'blur'
                            },
                            verify: 'email',
                            col: 5
                        },
                        {
                            label: '家庭地址',
                            name: 'jtdz',
                            placeholder: '请输入家庭地址',
                            readonly: true,
                            rules: {
                                required: false,
                                message: '请输入家庭地址',
                                trigger: 'blur'
                            },
                            verify: 'required',
                            col: 5
                        },
                    ]
                },
                
            ];
        },
		formList1(zglx) {
            //debugger;
            let fixFormList= this.formBaseItemList;
            fixFormList.splice(2,0,this.studentFormItemList);
            fixFormList.splice(3,0,this.tearcherFormItemList);
            

            let auditRequired = this.type==='audit';
            let audit = {
                title: '审核意见',
                contents: [
                    {
                        label: '审核意见',
                        name: 'auditComment',
                        placeholder: '请输入审核意见',
                        type: 'textarea',
                        rules: {
                            required: auditRequired,
                            message: '请输入审核意见',
                            trigger: 'blur'
                        },
                        verify: 'required',
                        rows: 5,
                        col: 10
                    }
                ]
            }
            
            
            if(this.type==="audit"){
                fixFormList.push(audit); 
            }else{
                for(var i in audit.contents){
                    audit.contents[i].readonly=true;
                    audit.contents[i].required=false;
                }
                fixFormList.push(audit); 
            }

            if(this.type==="audit"){
                fixFormList.push(this.auditBtn);
            }else{
                fixFormList.push(this.viewBtn);
            }

            return fixFormList;
					
            
        }	
		
	},
    created() {
        
        let id = this.selectInfo;
        this.handleFormData(this.formInfo);
	},
	methods: {
		
		handleSubmit(data,obj) {
            const loading = $.loading(this.$loading, '提交中');
            let auditComment = data.auditComment;
            let status;
            if(obj.text==="审核通过"){
                status = 1;
            }else{
                status = 2;
            }
            this.$request({
				url: interfaceUrl.audit,
				params: {"id":this.selectInfo,"status":status,"auditComment":auditComment},
				method: 'POST'
			}).then(res => {
                loading.close();
				this.$emit('cancel', false);
				if (res.rCode == 0) {
					this.$emit('refresh'); // 刷新数据
					this.$message.success(res.msg);
                    this.formData1 = {};
				} else {
					this.$message.error(res.msg);
				}
			});
            
		},
        /**
         * 处理formData数据为可展示数据
         */
         handleFormData(res){
            let personType = this.formData1.zglx;
            if(personType.indexOf("student")<0){
                delete this.formList1[2];
            }
            if(personType.indexOf("teacher")<0){
                delete this.formList1[3];
            }

            this.buildSubIndexName("platPersonStudent");
            this.buildSubIndexName("platPersonTeacher");

            
        },
		// 隐藏对话框
		handleCancel(event) {
			if (event.type === 'reset') {
				this.$emit('refresh'); // 刷新数据
			}
		},
        removeAttestationType(attestainType){
            for(var i in this.formList1){
                if(this.formList1[i].title!==attestainType){
                    this.formList1[i].hide = false;
                }
            }
            
        },
        /**
         * 获取下拉字典
         */
         getDictionary(){
             
             //行政区划
             this.$request({
                 url: interfaceUrl.region,
                 method: 'get'
             }).then(res => {
                 if (res.rCode == 0) {
                     this.regionData = res.results
                 }
             });
 
         },
         buildSubIndexName(subVOname){
            let vo = this.formData1[subVOname];
            if(vo!=undefined&&vo!=null){
                for (let key in vo) {
                    this.$set(this.formData1,subVOname+"_"+key,vo[key]||"");
                };
            }
            delete this.formData1[subVOname];
        }

	}
};
</script>
</script>
<style  lang="scss">
@import '../../../assets/style/style.scss';


.el-dialog__body {
    overflow: auto !important;
    .es-form .es-collapse{
        height: 100%;
    }
    .btn-box {
        margin-top: 20px;
        display: flex;
        justify-content: flex-end;

        .btn {
            padding: 5px 10px;
            color: #666;
            border: 1px solid #eee;
            cursor: pointer;
            margin-right: 5px;
            border-radius: 4px;
            // &.theme {
            // 	background: $--color-primary;
            // 	color: #fff;
            // 	border-color: $--color-primary;
            // }
        }
    }


}
</style>