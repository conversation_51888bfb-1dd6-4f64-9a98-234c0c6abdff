<!--
 @desc:项目管理
 @author: WH
 @date: 2023/11/14
 -->
<template>
	<component
		:is="isFlowPattern ? 'ProcesPage' : 'div'"
		:key="formData.changeId ? formData.changeId : id"
		:flow-data-props="{
			appId: formData.appId, // 流程图id
			businessId: formData.changeId ? formData.changeId : id, // 有 businessId-业务id 则为发起节点
			flowTypeCode: basics.flowTypeCode || wd.basics.flowTypeCode,
			defaultProcessKey: basics.defaultProcessKey || wd.basics.defaultProcessKey,
			isEdit: !formReadonly, // 是否编辑
			isFlow: !formReadonly // 是否显示右边流程功能
		}"
		:btn-list="btnList"
		@subFun="save"
		@handleSuccess="handleSuccess"
	>
		<div class="main">
			<div class="basic-info">
				<el-collapse v-model="activeNames">
					<el-collapse-item name="1">
						<template slot="title">
							<title-card title="基础信息"></title-card>
						</template>
						<es-form
							:key="formReadonly + '1' + formData.projectType"
							ref="formRef"
							height="100%"
							label-width="190px"
							:model="formData"
							:contents="wd.contents"
							table
							:submit="false"
							:readonly="formReadonly"
						></es-form>
					</el-collapse-item>
					<el-collapse-item name="2">
						<template slot="title">
							<title-card title="成员信息"></title-card>
						</template>
						<div :key="formReadonly + '2'" class="is-table">
							<div class="form-title">主要负责人信息</div>
							<es-data-table
								form
								:option-data="optionData"
								:readonly="formReadonly"
								:border="true"
								style="width: 100%"
								:thead="theadTeacherMy"
								:data="formData.teachersMy"
								@btnClick="btnClick"
							></es-data-table>
							<div class="form-title">其他成员信息</div>
							<es-data-table
								ref="formRef1"
								:height="formReadonly ? 'auto' : teacherHeight"
								:readonly="formReadonly"
								:border="true"
								style="width: 100%"
								:thead="theadTeacher"
								:data="formData.teachers"
								@btnClick="btnClick"
								@change="changeTeacher"
							></es-data-table>
						</div>
						<div :key="formReadonly + '3'" class="is-table">
							<div class="form-title">学生信息</div>
							<es-data-table
								ref="formRef2"
								:height="formReadonly ? 'auto' : studentHeight"
								form
								:readonly="formReadonly"
								:border="true"
								style="width: 100%"
								:thead="theadStudent"
								:data="formData.students"
								@btnClick="btnClick"
							></es-data-table>
							<es-data-table
								v-if="formData.projectType === '1'"
								ref="formRef3"
								:height="136"
								form
								:readonly="formReadonly"
								:border="true"
								:thead="theadStatistics"
								:data="formData.statistics"
								@btnClick="btnClick"
								@change="changeStatistics"
							></es-data-table>
						</div>
					</el-collapse-item>
				</el-collapse>
			</div>
			<!-- 变更事由 -->
			<div v-if="title.includes('变更') && title !== '查看变更详情'" class="card-change">
				<title-card title="变更信息"></title-card>
				<!-- <el-button
					v-if="formReadonly"
					class="btn-look"
					type="text"
					icon="el-icon-s-promotion"
					@click="visibleBasicInfo = true"
				>
					查看变更前详情
				</el-button> -->
				<es-form
					:key="formReadonly + '4'"
					ref="formRef4"
					:readonly="formReadonly"
					height="100%"
					label-width="190px"
					:model="formDataN.changeReason ? formDataN : formData"
					:contents="contentsChange"
					:submit="false"
					@submit="save"
					@reset="handleSuccess"
				></es-form>
			</div>
		</div>
		<!-- 选择主要负责人 -->
		<es-dialog
			title="选择主要负责人"
			:visible.sync="showForm"
			:drag="false"
			width="1200px"
			height="80%"
			:close-on-click-modal="false"
			:destroy-on-close="true"
		>
			<es-data-table
				v-if="showForm"
				:thead="theadInfo"
				:toolbar="toolbarInfo"
				:border="true"
				:page="true"
				url="/ybzy/projectMemberInfoTem/listJson"
				:numbers="true"
				:immediate="true"
				close
				form
				@btnClick="
					e => {
						e.row.assumeType = '0';
						formData.teachersMy = [e.row];
						getDiscipline(e.row.firstLevelDiscipline, '2');
						getDiscipline(e.row.subDiscipline, '3');

						showForm = false;
					}
				"
			></es-data-table>
		</es-dialog>
		<!-- 历史变更详情 -->
		<es-dialog
			title="查看变更前详情"
			:visible.sync="visibleBasicInfo"
			:show-scale="true"
			:drag="false"
			size="lg"
			width="90%"
			height="90%"
		>
			<ChangePop v-if="visibleBasicInfo" :id="formData.id" :contents-key="contentsKey" />
		</es-dialog>
		<es-dialog :title="newTitle" :visible.sync="teacherDialog" width="40%" height="60%">
			<es-form
				:model="teacherForm"
				:contents="newTitle == '新增教师' || newTitle == '编辑教师' ? teacherConfig : studentConfig"
				@submit="teacherSubmit"
			></es-form>
		</es-dialog>
	</component>
</template>

<script>
// import ProcesPage from '@/components/process-page.vue';
import { v4 as uuidv4 } from 'uuid';
import titleCard from '@cpt/scientific-sys/title-card.vue';
import ChangePop from '@/views/scientific-sys/components/project-info/change-pop.vue';
import { mixinInfo } from './mixinInfo';
export default {
	components: {
		// ProcesPage,
		ProcesPage: () => import('@/components/process-page.vue'),
		ChangePop,
		titleCard
	},
	mixins: [mixinInfo],
	props: {
		id: {
			type: String,
			default: ''
		},
		// 查看 编辑 新增
		title: {
			type: String,
			default: '查看变更'
		},
		basics: {
			type: Object,
			default: () => {
				return {};
			}
		},
		btnList: {
			// 按钮
			type: Array,
			default: () => [
				{ text: '提交', event: 'sub', type: 'primary' },
				{ text: '暂存', event: 'save', disabled: true }
			]
		},
		// 是否流程模式
		isFlowPattern: {
			type: Boolean,
			default: true
		},
		// 外部传入formData就不请求详情了
		formDataN: {
			type: Object,
			default: () => {
				return {};
			}
		}
	},
	data() {
		return {
			visibleChange: false,
			visibleBasicInfo: false,
			activeNames: ['1', '2', '3'],
			formReadonly: true,
			basicInfo: {},
			showForm: false,
			toolbarInfo: [
				{
					type: 'button',
					contents: []
				},
				{
					type: 'search',
					contents: [{ type: 'text', name: 'keyword', placeholder: '请输入关键字查询' }]
				}
			],

			formData: { students: [], teachers: [] },
			row: {},
			codesObj: {
				selectList1: []
			},
			optionData: {
				firstLevelDiscipline: [],
				subDiscipline: [],
				tertiaryDiscipline: []
			},
			teacherForm: {},
			teacherDialog: false,
			manualInput: false, //是否手动输入
			newTitle: '新增教师',
			editIndex: '',
			oldTeacherMy: [],
			oldTeachers: [],
			oldStudents: [],
			teacherMyCount: 0,
			teacherCount: 0,
			studentCount: 0
		};
	},
	computed: {
		teacherHeight() {
			let heightList = this.formData?.teachers?.length || 0;
			return heightList * 54 + 53;
		},
		studentHeight() {
			let heightList = this.formData?.students?.length || 0;
			return heightList * 119 + 53;
		},

		theadTeacherMy() {
			const projectType = this.formData.projectType || ''; // 1-自然 2-社科
			return projectType === '1'
				? [
						{
							align: 'center',
							label: '人员类型',
							field: 'type',
							type: 'select',
							data: this.codesObj.project_member_type,
							'value-key': 'cciValue',
							'label-key': 'shortName',
							width: 150,
							rules: {
								required: !this.formReadonly,
								message: '请选择',
								trigger: 'blur'
							},
							filterable: true
						},
						{
							align: 'center',
							label: '姓名',
							field: 'name',
							// field: this.formReadonly ? 'name' : 'member',
							// type: this.formReadonly ? 'text' : 'selector',
							// types: ['employee', 'otheremployee'],
							placeholder: '请选择',
							width: 180,
							multiple: false,
							filterable: true,
							rules: {
								required: !this.formReadonly,
								message: '请选择',
								trigger: 'blur'
							},
							events: {
								change: ({ data, item, name, value }) => {
									const obj = {
										...value[0],
										...JSON.parse(value[0]?.attr || '{}')
									};
									this.$set(data, 'number', obj.outcode);
									this.$set(data, 'name', obj.username);
									this.$set(data, 'orgName', obj.orgName);
									this.$set(data, 'deptId', obj.orgId);
									this.$set(data, 'professional', obj.postName);
									this.$set(data, 'phone', obj.phone);
									console.log({ data, obj }, '选中人时');
								},
								confirm: e => {}
							}
						},
						{
							align: 'center',
							label: '教工号',
							field: 'number',
							width:200,
							type: 'text',
							rules: {
								required: !this.formReadonly,
								message: '请选择',
								trigger: 'blur'
							},
							filterable: true
						},
						{
							align: 'center',
							label: '性别',
							field: 'sex',
							width: 100,
							type: 'select',
							'value-key': 'value',
							'label-key': 'label',
							rules: {
								required: !this.formReadonly,
								message: '请选择',
								trigger: 'blur'
							},
							data: [
								{
									align: 'center',
									label: '男',
									value: '0'
								},
								{
									align: 'center',
									label: '女',
									value: '1'
								}
							]
						},
						{
							align: 'center',
							label: '出生年月',
							field: 'birthdate',
							width: 150,
							type: 'text',
							rules: {
								required: !this.formReadonly,
								message: '请输入',
								trigger: 'blur'
							}
						},
						{
							align: 'center',
							label: '最后学历',
							field: 'highestEducation',
							width: 150,
							type: 'select',
							data: this.codesObj.member_highest_education,
							'value-key': 'cciValue',
							'label-key': 'shortName',
							filterable: true,
							rules: {
								required: !this.formReadonly,
								message: '请输入',
								trigger: 'blur'
							}
						},
						{
							align: 'center',
							label: '技术职务',
							field: 'technicalPosition',
							width: 150,
							type: 'select',
							data: this.codesObj.project_technical_position,
							'value-key': 'cciValue',
							'label-key': 'shortName',
							rules: {
								required: !this.formReadonly,
								message: '请输入',
								trigger: 'blur'
							}
						},
						{
							align: 'center',
							label: '职务类别',
							field: 'positionType',
							width: 170,
							data: this.codesObj.member_position_type,
							'value-key': 'cciValue',
							'label-key': 'shortName',
							rules: {
								required: !this.formReadonly,
								message: '请输入',
								trigger: 'blur'
							}
						},
						{
							align: 'center',
							label: '所属学科',
							field: 'relatedDiscipline',
							width: 150,
							type: 'select',
							data: this.codesObj.selectList1,
							'value-key': 'value',
							'label-key': 'label',
							rules: {
								required: !this.formReadonly,
								message: '请输入',
								trigger: 'blur'
							}
						},
						{
							align: 'center',
							label: '证件类型',
							field: 'idcardType',
							width: 150,
							type: 'select',
							data: this.codesObj.member_idcard_type,
							'value-key': 'cciValue',
							'label-key': 'shortName',
							rules: {
								required: !this.formReadonly,
								message: '请输入',
								trigger: 'blur'
							},
							filterable: true
						},
						{
							align: 'center',
							label: '证件号码',
							field: 'idcard',
							width: 180,
							type: 'text',
							rules: {
								required: !this.formReadonly,
								message: '请输入',
								trigger: 'blur'
							}
						},
						{
							align: 'center',
							label: '是否在编',
							field: 'isPermanentStaff',
							width: 100,
							type: 'select',
							'value-key': 'value',
							'label-key': 'label',
							rules: {
								required: !this.formReadonly,
								message: '请选择',
								trigger: 'blur'
							},
							data: [
								{
									align: 'center',
									label: '否',
									value: '0'
								},
								{
									align: 'center',
									label: '是',
									value: '1'
								}
							],
							filterable: true
						},
						{
							align: 'center',
							label: '岗位类型',
							field: 'postType',
							width: 150,
							type: 'select',
							data: this.codesObj.member_post_type,
							'value-key': 'cciValue',
							'label-key': 'shortName',
							rules: {
								required: !this.formReadonly,
								message: '请输入',
								trigger: 'blur'
							},
							filterable: true
						},
						{
							align: 'center',
							label: '部门/学院',
							field: 'orgName',
							width: 300,
							type: 'text',
							rules: {
								required: !this.formReadonly,
								message: '请选择',
								trigger: 'blur'
							}
						},
						{
							align: 'center',
							label: '职称',
							field: 'rank',
							width: 150,
							type: 'select',
							data: this.codesObj.member_rank,
							'value-key': 'cciValue',
							'label-key': 'shortName',
							filterable: true,
							rules: {
								required: !this.formReadonly,
								message: '请选择',
								trigger: 'blur'
							}
						},
						{
							align: 'center',
							label: '科技活动人员类型',
							field: 'activityMemberType',
							width: 150,
							type: 'select',
							data: this.codesObj.project_st_activity_member_type,
							'value-key': 'cciValue',
							'label-key': 'shortName',
							rules: {
								required: !this.formReadonly,
								message: '请选择',
								trigger: 'blur'
							}
						},
						{
							align: 'center',
							label: '入职年份',
							field: 'entryDate',
							width: 180,
							type: 'text',
							rules: {
								required: !this.formReadonly,
								message: '请选择',
								trigger: 'blur'
							}
						},
						{
							align: 'center',
							label: '联系电话',
							field: 'phone',
							width: 180,
							type: 'text',
							rules: {
								required: !this.formReadonly,
								message: '请选择',
								trigger: 'blur'
							}
						},
						{
							align: 'center',
							label: '承担类型',
							field: 'assumeType',
							width: 150,
							type: 'select',
							readonly: true,
							// align: 'center',
							rules: {
								required: !this.formReadonly,
								message: '请选择',
								trigger: 'blur'
							},
							fixed: 'right',
							data: this.codesObj.member_assume_type,
							'label-key': 'shortName',
							'value-key': 'cciValue',
							filterable: true
						},
						{
							title: '操作',
							type: 'handle',
							align: 'center',
							label: '操作',
							width: 100,
							hide: this.formReadonly,
							template: '',
							fixed: 'right',
							renderHeader: (h, params) => {
								return h('div', [
									h('span', {}, '选择模板'),
									h('el-button', {
										props: {
											type: 'text',
											icon: 'el-icon-circle-plus-outline'
										},
										on: {
											click: () => {
												this.showForm = true;
											}
										}
									})
								]);
							},
							events: [
								{
									text: '重选',
									size: 'mini',
									type: 'text',
									icon: 'el-icon-document',
									event: ({ $index, row }) => {
										this.showForm = true;
									}
								}
							]
						}
					]
				: [
						{
							align: 'center',
							label: '人员类型',
							field: 'type',
							type: 'select',
							data: this.codesObj.project_member_type,
							'value-key': 'cciValue',
							'label-key': 'shortName',
							width: 150,
							rules: {
								required: !this.formReadonly,
								message: '请选择',
								trigger: 'blur'
							},
							filterable: true
						},
						{
							align: 'center',
							label: '姓名',
							field: 'name',
							// field: this.formReadonly ? 'name' : 'member',
							// type: this.formReadonly ? 'text' : 'selector',
							// types: ['employee', 'otheremployee'],
							placeholder: '请选择',
							width: 180,
							multiple: false,
							filterable: true,
							rules: {
								required: !this.formReadonly,
								message: '请选择',
								trigger: 'blur'
							},
							events: {
								change: ({ data, item, name, value }) => {
									const obj = {
										...value[0],
										...JSON.parse(value[0]?.attr || '{}')
									};
									this.$set(data, 'number', obj.outcode);
									this.$set(data, 'name', obj.username);
									this.$set(data, 'orgName', obj.orgName);
									this.$set(data, 'deptId', obj.orgId);
									this.$set(data, 'professional', obj.postName);
									this.$set(data, 'phone', obj.phone);
									console.log({ data, obj }, '选中人时');
								},
								confirm: e => {}
							}
						},
						{
							align: 'center',
							label: '英文名字',
							field: 'englishName',
							width: 110,
							type: 'text',

							filterable: true
						},
						{
							align: 'center',
							label: '曾用名',
							field: 'formerName',
							width: 110,
							type: 'text',
							filterable: true
						},
						{
							align: 'center',
							label: '教工号',
							field: 'number',
							width:200,
							type: 'text',
							rules: {
								required: !this.formReadonly,
								message: '请选择',
								trigger: 'blur'
							},
							filterable: true
						},
						{
							align: 'center',
							label: '性别',
							field: 'sex',
							width: 100,
							type: 'select',
							'value-key': 'value',
							'label-key': 'label',
							rules: {
								required: !this.formReadonly,
								message: '请选择',
								trigger: 'blur'
							},
							data: [
								{
									align: 'center',
									label: '男',
									value: '0'
								},
								{
									align: 'center',
									label: '女',
									value: '1'
								}
							]
						},
						{
							align: 'center',
							label: '出生年月',
							field: 'birthdate',
							width: 150,
							type: 'date',
							rules: {
								required: !this.formReadonly,
								message: '请输入',
								trigger: 'blur'
							}
						},
						{
							align: 'center',
							label: '部门/学院',
							field: 'orgName',
							width: 300,
							type: 'text',
							rules: {
								required: !this.formReadonly,
								message: '请选择',
								trigger: 'blur'
							}
						},
						{
							align: 'center',
							label: '职称',
							field: 'rank',
							width: 150,
							type: 'select',
							data: this.codesObj.member_rank,
							'value-key': 'cciValue',
							'label-key': 'shortName',
							filterable: true,
							rules: {
								required: !this.formReadonly,
								message: '请选择',
								trigger: 'blur'
							}
						},
						{
							align: 'center',
							label: '最后学历',
							field: 'highestEducation',
							width: 150,
							type: 'select',
							data: this.codesObj.member_highest_education,
							'value-key': 'cciValue',
							'label-key': 'shortName',
							filterable: true,
							rules: {
								required: !this.formReadonly,
								message: '请输入',
								trigger: 'blur'
							}
						},
						{
							align: 'center',
							label: '最后学位',
							field: 'highestDegree',
							width: 150,
							type: 'select',
							data: this.codesObj.member_highest_degree,
							'value-key': 'cciValue',
							'label-key': 'shortName',
							rules: {
								required: !this.formReadonly,
								message: '请输入',
								trigger: 'blur'
							},
							filterable: true
						},
						{
							align: 'center',
							label: '证件类型',
							field: 'idcardType',
							width: 150,
							type: 'select',
							data: this.codesObj.member_idcard_type,
							'value-key': 'cciValue',
							'label-key': 'shortName',
							rules: {
								required: !this.formReadonly,
								message: '请输入',
								trigger: 'blur'
							},
							filterable: true
						},
						{
							align: 'center',
							label: '证件号码',
							field: 'idcard',
							width: 180,
							type: 'text',
							rules: {
								required: !this.formReadonly,
								message: '请输入',
								trigger: 'blur'
							}
						},
						{
							align: 'center',
							label: '一级学科',
							field: 'firstLevelDiscipline',
							width: 100,
							type: 'select',
							// data: this.codesObj.selectList1,
							'value-key': 'value',
							'label-key': 'label',
							rules: {
								required: !this.formReadonly,
								message: '请输入',
								trigger: 'blur'
							},
							filterable: true
						},
						{
							align: 'center',
							label: '二级学科',
							field: 'subDiscipline',
							width: 100,
							type: 'select',
							// data: this.codesObj.selectList2,
							'value-key': 'value',
							'label-key': 'label',
							rules: {
								required: !this.formReadonly,
								message: '请输入',
								trigger: 'blur'
							},
							filterable: true
						},
						{
							align: 'center',
							label: '三级学科',
							field: 'tertiaryDiscipline',
							width: 100,
							type: 'select',
							// data: this.codesObj.selectList3,
							'value-key': 'value',
							'label-key': 'label',
							rules: {
								required: !this.formReadonly,
								message: '请输入',
								trigger: 'blur'
							},
							filterable: true
						},
						// {
						// 	align: 'center',
						// 	label: '相关学科',
						// 	field: 'subject',
						// 	width: 100,
						// 	type: 'text',
						// 	rules: {
						// 		required: !this.formReadonly,
						// 		message: '请输入',
						// 		trigger: 'blur'
						// 	}
						// },
						{
							align: 'center',
							label: '政治面貌',
							field: 'politicalStatus',
							width: 100,
							type: 'select',
							data: this.codesObj.member_political_status,
							'value-key': 'cciValue',
							'label-key': 'shortName',
							rules: {
								required: !this.formReadonly,
								message: '请输入',
								trigger: 'blur'
							},
							filterable: true
						},
						{
							align: 'center',
							label: '研究方向',
							field: 'researchDirection',
							width: 180,
							type: 'text',
							rules: {
								required: !this.formReadonly,
								message: '请输入',
								trigger: 'blur'
							}
						},
						{
							align: 'center',
							label: 'E-mail',
							field: 'email',
							width: 180,
							type: 'text'
						},
						{
							align: 'center',
							label: '行政职务',
							field: 'administrativePost',
							width: 180,
							type: 'text',
							filterable: true
						},
						{
							align: 'center',
							label: '定职时间',
							field: 'confirmPostDate',
							width: 180,
							type: 'date'
						},
						{
							align: 'center',
							label: '国籍',
							field: 'nationality',
							width: 80,
							type: 'select',
							data: this.codesObj.member_nationality,
							'value-key': 'cciValue',
							'label-key': 'shortName',
							rules: {
								required: !this.formReadonly,
								message: '请输入',
								trigger: 'blur'
							},
							filterable: true
						},
						{
							align: 'center',
							label: '民族',
							field: 'nation',
							width: 80,
							type: 'select',
							data: this.codesObj.member_nation,
							'value-key': 'cciValue',
							'label-key': 'shortName',
							rules: {
								required: !this.formReadonly,
								message: '请输入',
								trigger: 'blur'
							},
							filterable: true
						},

						{
							align: 'center',
							label: '联系电话',
							field: 'phone',
							width: 180,
							type: 'text',
							rules: {
								required: !this.formReadonly,
								message: '请选择',
								trigger: 'blur'
							},
							filterable: true
						},
						{
							align: 'center',
							label: '办公电话',
							field: 'officePhone',
							width: 180,
							type: 'text'
						},
						{
							align: 'center',
							label: '个人网址',
							field: 'personalWebsite',
							width: 180,
							type: 'text'
						},
						{
							align: 'center',
							label: '承担类型',
							field: 'assumeType',
							width: 150,
							type: 'select',
							readonly: true,
							// align: 'center',
							rules: {
								required: !this.formReadonly,
								message: '请选择',
								trigger: 'blur'
							},
							fixed: 'right',
							data: this.codesObj.member_assume_type,
							'label-key': 'shortName',
							'value-key': 'cciValue',
							filterable: true
						},
						{
							title: '操作',
							type: 'handle',
							align: 'center',
							label: '操作',
							width: 100,
							hide: this.formReadonly,
							template: '',
							fixed: 'right',
							renderHeader: (h, params) => {
								return h('div', [
									h('span', {}, '选择模板'),
									h('el-button', {
										props: {
											type: 'text',
											icon: 'el-icon-circle-plus-outline'
										},
										on: {
											click: () => {
												this.showForm = true;
											}
										}
									})
								]);
							},
							events: [
								{
									text: '重选',
									size: 'mini',
									type: 'text',
									icon: 'el-icon-document',
									event: ({ $index, row }) => {
										this.showForm = true;
									}
								}
							]
						}
					];
		},
		teacherConfig() {
			return [
				{
					align: 'center',
					label: '人员类型',
					name: 'type',
					type: 'select',
					data: this.codesObj.project_member_type,
					'value-key': 'cciValue',
					'label-key': 'shortName',
					width: '150',
					rules: {
						required: !this.formReadonly,
						message: '请选择',
						trigger: 'blur'
					},
					events: {
						change: (res, val, index) => {
							console.log(res, val, index);
							this.manualInput = val == 1 ? true : false;
							// Object.keys(this.teacherForm).forEach(key => {
							// 	if (key !== 'type') this.teacherForm[key] = null;
							// });
						}
					},
					filterable: true
				},
				{
					align: 'center',
					label: '姓名',
					// name: this.formReadonly ? 'name' : 'member',
					name: 'name',
					type: 'text',
					width: '180',
					placeholder: '请选择',
					multiple: false,
					hide: !(this.teacherForm.type == 1),
					filterable: true,
					rules: {
						required: !this.formReadonly,
						message: '请选择',
						trigger: 'blur'
					}
				},
				{
					align: 'center',
					label: '姓名',
					width: '180',
					// name: this.formReadonly ? 'name' : 'member',
					name: 'name',
					type: 'selector',
					types: ['employee', 'otheremployee'],
					placeholder: '请选择',
					hide: this.teacherForm.type == 1,
					multiple: false,
					filterable: true,
					rules: {
						required: !this.formReadonly,
						message: '请选择',
						trigger: 'blur'
					},
					events: {
						confirm: val => {
							const obj = JSON.parse(val[0].attr);
							this.$set(this.teacherForm, 'number', obj.outcode);
							this.$set(this.teacherForm, 'name', obj.username);
							this.$set(this.teacherForm, 'member', obj.username);
							this.$set(this.teacherForm, 'orgName', obj.orgName);
							this.$set(this.teacherForm, 'deptId', obj.orgId);
							this.$set(this.teacherForm, 'professional', obj.postName);
							this.$set(this.teacherForm, 'phone', obj.phone);
						}
					}
				},
				{
					label: '性别',
					name: 'sex',
					width: '100',
					type: 'select',
					'value-key': 'value',
					'label-key': 'label',
					rules: {
						required: !this.formReadonly,
						message: '请选择',
						trigger: 'blur'
					},
					data: [
						{
							align: 'center',
							label: '男',
							value: '0'
						},
						{
							align: 'center',
							label: '女',
							value: '1'
						}
					]
				},
				{
					align: 'center',
					label: '教工号',
					width:'200',
					name: 'number',
					type: 'text',
					rules: {
						required: !this.manualInput,
						message: '请选择',
						trigger: 'blur'
					},
					filterable: true
				},
				{
					align: 'center',
					width:'400',
					label: '部门/学院/单位',
					name: 'orgName',
					type: 'text',
					rules: {
						required: !this.formReadonly,
						message: '请选择',
						trigger: 'blur'
					}
				},
				{
					align: 'center',
					label: '职称',
					width: '150',
					name: 'rank',
					type: 'select',
					data: this.codesObj.member_rank,
					'value-key': 'cciValue',
					'label-key': 'shortName',
					rules: {
						required: !this.formReadonly,
						message: '请选择',
						trigger: 'blur'
					}
				},
				{
					align: 'center',
					label: '最后学历',
					name: 'highestEducation',
					width: '200',
					type: 'select',
					data: this.codesObj.member_highest_education,
					'value-key': 'cciValue',
					'label-key': 'shortName',
					filterable: true,
					rules: {
						required: !this.formReadonly,
						message: '请输入',
						trigger: 'blur'
					}
				},
				{
					align: 'center',
					label: '联系电话',
					width: '230',
					name: 'phone',
					type: 'text',
					rules: {
						required: !this.formReadonly,
						message: '请选择',
						trigger: 'blur'
					}
				},
				{
					align: 'center',
					label: '承担类型',
					name: 'assumeType',
					type: 'select',
					rules: {
						required: !this.formReadonly,
						message: '请选择',
						trigger: 'blur'
					},
					'label-key': 'shortName',
					'value-key': 'cciValue',
					// data: this.codesObj.member_assume_type?.map(item => {
					// 	if (item.shortName === '学生') {
					// 		item.disabled = true;
					// 	}
					// 	return item;
					// }),
					data: [
						{ cciValue: '1', shortName: '主研' },
						{ cciValue: '3', shortName: '参研' }
					],
					filterable: true,
					events: {
						change: (res, val, index) => {
							console.log(res, val, index);
						}
					}
				}
			];
		},
		theadTeacher() {
			return [
				{
					align: 'center',
					label: '人员类型',
					width: 150,
					field: 'type',
					type: 'select',
					data: this.codesObj.project_member_type,
					'value-key': 'cciValue',
					'label-key': 'shortName',
					rules: {
						required: !this.formReadonly,
						message: '请选择',
						trigger: 'blur'
					},
					filterable: true
				},
				{
					align: 'center',
					label: '姓名',
					width: 180,
					// field: this.formReadonly ? 'name' : 'member',
					field: 'name',
					// type: 'selector',
					// types: ['employee', 'otheremployee'],
					placeholder: '请选择',
					multiple: false,
					filterable: true,
					rules: {
						required: !this.formReadonly,
						message: '请选择',
						trigger: 'blur'
					},
					events: {
						change: ({ data, item, name, value }) => {
							const obj = {
								...value[0],
								...JSON.parse(value[0]?.attr || '{}')
							};
							this.$set(data, 'number', obj.outcode);
							this.$set(data, 'name', obj.username);
							this.$set(data, 'orgName', obj.orgName);
							this.$set(data, 'deptId', obj.orgId);
							this.$set(data, 'professional', obj.postName);
							this.$set(data, 'phone', obj.phone);
							console.log({ data, obj }, '选中人时');
						},
						confirm: e => {}
					}
				},
				{
					align: 'center',
					label: '性别',
					width: 100,
					field: 'sex',
					type: 'select',
					'value-key': 'value',
					'label-key': 'label',
					rules: {
						required: !this.formReadonly,
						message: '请选择',
						trigger: 'blur'
					},
					data: [
						{
							align: 'center',
							label: '男',
							value: '0'
						},
						{
							align: 'center',
							label: '女',
							value: '1'
						}
					]
				},
				{
					align: 'center',
					label: '教工号',
					width:200,
					field: 'number',
					type: 'text',
					rules: {
								required: !this.formReadonly,
								message: '请选择',
								trigger: 'blur'
							},
					filterable: true
				},
				{
					align: 'center',
					label: '部门/学院/单位',
					width:400,
					field: 'orgName',
					type: 'text',
					rules: {
						required: !this.formReadonly,
						message: '请选择',
						trigger: 'blur'
					}
				},
				{
					align: 'center',
					label: '职称',
					width: 150,
					field: 'rank',
					type: 'select',
					data: this.codesObj.member_rank,
					'value-key': 'cciValue',
					'label-key': 'shortName',
					rules: {
						required: !this.formReadonly,
						message: '请选择',
						trigger: 'blur'
					}
				},
				{
					align: 'center',
					label: '最后学历',
					width: 200,
					field: 'highestEducation',
					type: 'select',
					data: this.codesObj.member_highest_education,
					'value-key': 'cciValue',
					'label-key': 'shortName',
					filterable: true,
					rules: {
						required: !this.formReadonly,
						message: '请输入',
						trigger: 'blur'
					}
				},
				{
					align: 'center',
					label: '联系电话',
					width: 230,
					field: 'phone',
					type: 'text',
					rules: {
						required: !this.formReadonly,
						message: '请选择',
						trigger: 'blur'
					}
				},
				{
					align: 'center',
					label: '承担类型',
					field: 'assumeType',
					type: 'select',
					rules: {
						required: !this.formReadonly,
						message: '请选择',
						trigger: 'blur'
					},
					'label-key': 'shortName',
					'value-key': 'cciValue',
					// data: this.codesObj.member_assume_type?.map(item => {
					// 	if (item.shortName === '学生') {
					// 		item.disabled = true;
					// 	}
					// 	return item;
					// }),
					data: [
						{ cciValue: '1', shortName: '主研' },
						{ cciValue: '3', shortName: '参研' }
					],
					filterable: true
				},

				{
					title: '操作',
					type: 'handle',
					align: 'center',
					fixed: 'right',
					label: '操作',
					width: 100,
					hide: this.formReadonly,
					template: '',
					renderHeader: (h, params) => {
						return h('div', [
							h('span', {}, '操作'),
							h('el-button', {
								props: {
									type: 'text',
									icon: 'el-icon-circle-plus-outline'
								},
								on: {
									click: () => {
										this.teacherForm = {};
										this.newTitle = '新增教师';
										this.teacherDialog = true;
										// this.$set(this.formData.teachers, this.formData.teachers.length, {
										// 	id: uuidv4(),
										// 	type: '',
										// 	member: [],
										// 	number: '',
										// 	orgName: '',
										// 	deptId: '',
										// 	phone: ''
										// });
									}
								}
							})
						]);
					},
					events: [
						{
							text: '删除',
							size: 'mini',
							type: 'text',
							icon: 'es-icon-shanchu',
							event: ({ $index, row }) => {
								const teachers = this.formData?.teachers?.filter(item => item.id !== row.id);
								this.$set(this.formData, 'teachers', teachers);
							}
						},
						{
							text: '编辑',
							size: 'mini',
							type: 'text',
							icon: 'es-icon-bianji',
							event: ({ $index, row }) => {
								this.newTitle = '编辑教师';
								this.editIndex = $index;
								this.teacherForm = JSON.parse(JSON.stringify(row));
								this.teacherDialog = true;
							}
						}
					]
				}
			];
		},
		studentConfig() {
			return [
				{
					label: '人员类型',
					name: 'type',
					width: '150',
					type: 'select',
					rules: {
						required: !this.formReadonly,
						message: '请选择',
						trigger: 'blur'
					},
					data: this.codesObj.project_member_type,
					'value-key': 'cciValue',
					'label-key': 'shortName',
					events: {
						change: (res, val, index) => {
							console.log(res, val, index);
							this.manualInput = val == 1 ? true : false;
							// Object.keys(this.teacherForm).forEach(key => {
							// 	if (key !== 'type' && key !== 'assumeType') this.teacherForm[key] = null;
							// });
						}
					},
					filterable: true
				},
				{
					align: 'center',
					label: '姓名',
					width: '180',
					// name: this.formReadonly ? 'name' : 'member',
					name: 'name',
					type: 'text',
					placeholder: '请选择',
					multiple: false,
					hide: !(this.teacherForm.type == 1),
					filterable: true,
					rules: {
						required: !this.formReadonly,
						message: '请选择',
						trigger: 'blur'
					}
				},
				{
					align: 'center',
					label: '姓名',
					width: '180',
					// name: this.formReadonly ? 'name' : 'member',
					name: 'name',
					type: 'selector',
					types: ['employee', 'otheremployee'],
					placeholder: '请选择',
					hide: this.teacherForm.type == 1,
					multiple: false,
					filterable: true,
					rules: {
						required: !this.formReadonly,
						message: '请选择',
						trigger: 'blur'
					},
					events: {
						confirm: val => {
							const obj = JSON.parse(val[0].attr);
							this.$set(this.teacherForm, 'number', obj.outcode);
							this.$set(this.teacherForm, 'name', obj.username);
							this.$set(this.teacherForm, 'member', obj.username);
							this.$set(this.teacherForm, 'orgName', obj.orgName);
							this.$set(this.teacherForm, 'deptId', obj.orgId);
							this.$set(this.teacherForm, 'professional', obj.postName);
							this.$set(this.teacherForm, 'phone', obj.phone);
						}
					}
				},
				{
					label: '性别',
					name: 'sex',
					width: '100',
					type: 'select',
					'value-key': 'value',
					'label-key': 'label',
					rules: {
						required: !this.formReadonly,
						message: '请选择',
						trigger: 'blur'
					},
					data: [
						{
							align: 'center',
							label: '男',
							value: '0'
						},
						{
							align: 'center',
							label: '女',
							value: '1'
						}
					]
				},
				{
					align: 'center',
					label: '学号',
					name: 'number',
					width:'200',
					type: 'text',
					rules: {
						required: !this.manualInput && !this.formReadonly,
						message: '请选择',
						trigger: 'blur'
					}
				},
				{
					align: 'center',
					label: '部门/学院',
					width: '300',
					name: 'orgName',
					type: 'text',
					rules: {
						required: !this.formReadonly,
						message: '请选择',
						trigger: 'blur'
					}
				},
				{
					align: 'center',
					label: '联系电话',
					width: '180',
					name: 'phone',
					type: 'text',
					rules: {
						required: !this.formReadonly,
						message: '请选择',
						trigger: 'blur'
					},
					filterable: true
				},
				{
					label: '承担类',
					name: 'assumeType',
					type: 'select',
					'label-key': 'shortName',
					'value-key': 'cciValue',
					// data: this.codesObj.member_assume_type?.map(item => {
					// 	if (item.shortName === '学生') {
					// 		item.disabled = true;
					// 	}
					// 	return item;
					// }),
					data:
						this.contentsKey == 'instituteProject'
							? [
									{ cciValue: '0', shortName: '项目负责人' },
									{ cciValue: '3', shortName: '参研' }
								]
							: [
									// { cciValue: '1', shortName: '主研' },
									{ cciValue: '3', shortName: '参研' }
								],
					filterable: true,
					events: {
						change: (res, val, index) => {
							console.log(res, val, index);
						}
					}
				}
			];
		},
		theadStudent() {
			return [
				{
					align: 'center',
					label: '人员类型',
					width: 150,
					field: 'type',
					type: 'select',
					readonly: true,
					rules: {
						required: !this.formReadonly,
						message: '请选择',
						trigger: 'blur'
					},
					data: this.codesObj.project_member_type,
					'value-key': 'cciValue',
					'label-key': 'shortName',
					filterable: true
				},
				{
					align: 'center',
					label: '姓名',
					// field: this.formReadonly ? 'name' : 'member',
					field: 'name',
					// type: 'selector',
					// types: ['employee', 'otheremployee'],
					placeholder: '请选择',
					width: 180,
					multiple: false,
					filterable: true,
					readonly: true,
					rules: {
						required: !this.formReadonly,
						message: '请选择',
						trigger: 'blur'
					},
					events: {
						change: ({ data, item, name, value }) => {
							const obj = {
								...value[0],
								...JSON.parse(value[0]?.attr || '{}')
							};
							this.$set(data, 'number', obj.outcode);
							this.$set(data, 'name', obj.username);
							this.$set(data, 'orgName', obj.orgName);
							this.$set(data, 'deptId', obj.orgId);

							this.$set(data, 'phone', obj.phone);
							// this.$set(data, 'assumeType', '3');
							console.log({ data, obj }, '选中人时');
						},
						confirm: e => {}
					}
				},
				{
					align: 'center',
					label: '性别',
					field: 'sex',
					width: 100,
					type: 'select',
					'value-key': 'value',
					'label-key': 'label',
					rules: {
						required: !this.formReadonly,
						message: '请选择',
						trigger: 'blur'
					},
					data: [
						{
							align: 'center',
							label: '男',
							value: '0'
						},
						{
							align: 'center',
							label: '女',
							value: '1'
						}
					]
				},
				{
					align: 'center',
					label: '学号',
					field: 'number',
					width:200,
					type: 'text',
					readonly: true,
					rules: {
						required: !this.manualInput && !this.formReadonly,
						message: '请选择',
						trigger: 'blur'
					}
				},
				{
					align: 'center',
					label: '部门/学院',
					field: 'orgName',
					width: 400,
					type: 'text',
					readonly: true,
					rules: {
						required: !this.formReadonly,
						message: '请选择',
						trigger: 'blur'
					}
				},
				{
					align: 'center',
					label: '联系电话',
					width: 180,
					field: 'phone',
					type: 'text',
					readonly: true,
					rules: {
						required: !this.formReadonly,
						message: '请选择',
						trigger: 'blur'
					},
					filterable: true
				},
				{
					align: 'center',
					label: '承担类型',
					field: 'assumeType',
					width: 150,
					type: 'select',
					readonly: true,
					'label-key': 'shortName',
					'value-key': 'cciValue',
					// data: this.codesObj.member_assume_type?.map(item => {
					// 	if (item.shortName === '学生') {
					// 		item.disabled = true;
					// 	}
					// 	return item;
					// }),
					data:
						this.contentsKey == 'instituteProject'
							? [
									{ cciValue: '0', shortName: '项目负责人' },
									{ cciValue: '3', shortName: '参研' }
								]
							: [
									// { cciValue: '1', shortName: '主研' },
									{ cciValue: '3', shortName: '参研' }
								],
					filterable: true
				},
				{
					align: 'right',
					label: '证明材料（学生证）',
					width:470,
					field: 'studentFile',
					rules: {
						required: !this.formReadonly,
						message: '请选择',
						trigger: 'blur'
					},
					filterable: true,
					render: (h, params) => {
						const fn = h('es-upload', {
							class: 'upload-box',
							attrs: {
								preview: true,
								showInfo: [],
								code: 'pro_member_student_att',
								ownId: params.row.id, // 业务id
								readonly: this.formReadonly,
								'list-height': this.formReadonly ? '' : '70px',
								fileList: params.row.studentFile,
								onChange: (res, file) => {
									params.row.studentFile = file;
									console.log(this.formData.students, 'this.formData.students');
								}
							}
						});
						return fn;
					}
				},
				{
					title: '操作',
					type: 'handle',
					align: 'center',
					fixed: 'right',
					label: '操作',
					hide: this.formReadonly,
					width: 100,
					renderHeader: (h, params) => {
						return h('div', [
							h('span', {}, '操作'),
							h('el-button', {
								props: {
									type: 'text',
									icon: 'el-icon-circle-plus-outline'
								},
								on: {
									click: () => {
										this.newTitle = '新增学生';
										this.teacherDialog = true;
										// this.teacherForm.assumeType = '3';
										// this.$set(this.formData.students, this.formData.students.length, {
										// 	id: uuidv4(),
										// 	type: '',
										// 	member: [],
										// 	number: '',
										// 	orgName: '',
										// 	deptId: '',
										// 	phone: '',
										// 	assumeType: '3',
										// 	studentFile: []
										// });
									}
								}
							})
						]);
					},
					events: [
						{
							text: '删除',
							size: 'mini',
							type: 'text',
							icon: 'es-icon-shanchu',
							event: ({ $index, row }) => {
								const students = this.formData?.students?.filter(item => item.id !== row.id);
								this.$set(this.formData, 'students', students);
							}
						},
						{
							text: '编辑',
							size: 'mini',
							type: 'text',
							icon: 'es-icon-bianji',
							event: ({ $index, row }) => {
								this.newTitle = '编辑学生';
								this.editIndex = $index;
								this.teacherForm = JSON.parse(JSON.stringify(row));
								this.teacherDialog = true;
							}
						}
					]
				}
			];
		},
		theadStatistics() {
			return [
				{
					label: '当年投入人员（人年）',
					childHead: [
						{
							prop: 'inputTotal',
							align: 'center',
							minWidth: 30,
							controls: false,
							label: '合计'
						},
						{
							label: '女',
							type: 'number',
							align: 'center',
							minWidth: 60,
							controls: false,
							field: 'inputFemaleNum'
						},
						{
							label: '高级职称',
							align: 'center',
							type: 'number',
							minWidth: 60,
							controls: false,
							field: 'inputHighNum'
						},
						{
							label: '中级职称',
							align: 'center',
							type: 'number',
							minWidth: 60,
							controls: false,
							field: 'inputMiddleNum'
						},
						{
							label: '初级职称',
							align: 'center',
							type: 'number',
							minWidth: 60,
							controls: false,
							field: 'inputElementaryNum'
						},
						{
							align: 'center',
							label: '其他',
							type: 'number',
							minWidth: 60,
							controls: false,
							field: 'inputElseNum'
						}
					]
				},
				{
					label: '参与项目的研究生数（人）',
					childHead: [
						{
							label: '博士生',
							align: 'center',
							type: 'number',
							minWidth: 60,
							controls: false,
							field: 'joinDoctorNum'
						},
						{
							label: '硕士生',
							align: 'center',
							type: 'number',
							minWidth: 60,
							controls: false,
							field: 'joinMasterNum'
						}
					]
				}
			];
		},
		theadInfo() {
			return [
				{
					title: '人员类型',
					align: 'center',
					showOverflowTooltip: true,
					width: 150,
					field: 'typeTxt'
				},
				{
					title: '姓名',
					width: 180,
					align: 'center',
					showOverflowTooltip: true,
					field: 'name'
				},
				{
					title: '教工号',
					align: 'center',
					width:200,
					showOverflowTooltip: true,
					field: 'number'
				},
				{
					title: '部门/学院',
					align: 'center',
					showOverflowTooltip: true,
					field: 'orgName'
				},
				{
					title: '研究方向',
					align: 'center',
					field: 'researchDirection'
				},
				{
					title: '岗位类型',
					width: 170,
					align: 'center',
					showOverflowTooltip: true,
					field: 'postTypeTxt'
				},
				{
					title: '操作',
					type: 'handle',
					template: '',
					width: 100,
					align: 'center',
					events: [{ code: 'view', text: '选择', icon: 'es-icon-gou' }]
				}
			];
		},
		contentsChange() {
			const changeId = this.formData?.changeId; // 项目附件id
			return [
				{
					name: 'terminateState',
					label: '是否终止',
					type: 'radio',
					'value-key': 'cciValue',
					'label-key': 'shortName',
					col: 12,
					rules: {
						required: true,
						message: '请选择',
						trigger: 'change'
					},
					sysCode: 'terminate_state',
					events: {
						//选择了是 (0：否   1：是) 就要提示 是否确定终止项目
						change: (key, value) => {
							if (key == 'terminateState' && value == 1) {
								this.$confirm('是否确定终止项目?', '提示', {
									confirmButtonText: '确定',
									cancelButtonText: '取消',
									type: 'warning'
								})
									.then(() => {})
									.catch(() => {
										this.formData.terminateState = '0';
									});
							}
						}
					}
					// data: [
					// 	{ name: '是', value: '1' },
					// 	{ name: '否', value: '0' }
					// ]
				},

				{
					type: 'text',
					label: '变更事由',
					name: 'changeReason',
					placeholder: '请输入',
					col: 12,
					rules: {
						required: true,
						message: '请输入',
						trigger: 'change'
					}
				},

				{
					name: 'changeContent',
					label: '变更内容',
					type: 'textarea',
					value: '',
					rows: 3
				},
				{
					name: 'fj3',
					label: '附件',
					type: 'attachment',
					value: '',
					col: 12,
					preview: true,
					code: 'changefile',
					ownId: changeId // 业务id
				}
			];
		}
	},
	watch: {
		id: {
			handler(newVal, oldVal) {
				if (newVal) {
					this.activeNames = ['1', '2', '3'];
					this.getPorjectInfo();
				}
			},
			immediate: true
		},
		'formData.teachersMy': {
			handler(newVal, oldVala) {
				const oldVal = this.oldTeacherMy;

				if (this.title.includes('编辑') && this.teacherMyCount <= 1) {
					this.teacherMyCount++;
					this.oldTeacherMy = JSON.parse(JSON.stringify(newVal));
					return;
				}
				if (this.formReadonly) return;

				// 定义职称等级对应的统计字段映射
				const rankStatMap = {
					1: 'inputHighNum',
					2: 'inputHighNum',
					3: 'inputMiddleNum',
					4: 'inputElementaryNum',
					5: 'inputElseNum'
				};
				// 定义最后学历对应的统计字段映射
				const educationStatMap = {
					1: 'joinDoctorNum', // 博士
					2: 'joinMasterNum' // 硕士
				};

				// 更新统计数据的通用方法
				const updateStatistics = (items, isAdd = true) => {
					items?.forEach(item => {
						// 更新性别统计
						if (item.sex === '1') {
							const femaleNum =
								Number(this.formData.statistics[0].inputFemaleNum) + (isAdd ? 1 : -1);
							this.$set(this.formData.statistics[0], 'inputFemaleNum', femaleNum);
						}
						// 更新学历统计
						const educationField = educationStatMap[item.highestEducation];
						if (educationField) {
							const educationNum =
								Number(this.formData.statistics[0][educationField]) + (isAdd ? 1 : -1);
							this.$set(this.formData.statistics[0], educationField, educationNum);
						}
						// 更新职称统计
						const statField = rankStatMap[item.rank];
						if (statField) {
							const rankNum = Number(this.formData.statistics[0][statField]) + (isAdd ? 1 : -1);
							this.$set(this.formData.statistics[0], statField, rankNum);
						}
					});
				};

				// 处理旧值和新值
				updateStatistics(oldVal, false);
				updateStatistics(newVal, true);

				// 保存当前值为旧值
				this.oldTeacherMy = JSON.parse(JSON.stringify(newVal));

				// 计算总数
				const inputTotal = [
					'inputHighNum',
					'inputMiddleNum',
					'inputElementaryNum',
					'inputElseNum'
				].reduce((sum, key) => sum + Number(this.formData.statistics[0][key]), 0);
				this.$set(this.formData.statistics[0], 'inputTotal', inputTotal);
			},
			deep: true
		},
		'formData.teachers': {
			handler(newVal, oldVal1) {
				const oldVal = this.oldTeachers;
				if (this.title.includes('编辑') && this.teacherCount <= 1) {
					this.oldTeachers = JSON.parse(JSON.stringify(newVal));
					this.teacherCount++;
					return;
				}
				if (this.formReadonly) return;

				// 定义映射关系
				const rankStatMap = {
					1: 'inputHighNum',
					2: 'inputHighNum',
					3: 'inputMiddleNum',
					4: 'inputElementaryNum',
					5: 'inputElseNum'
				};
				const educationStatMap = {
					1: 'joinDoctorNum',
					2: 'joinMasterNum'
				};

				// 更新统计数据的通用方法
				const updateStatistics = (items, isAdd = true) => {
					items?.forEach(item => {
						// 更新性别统计
						if (item.sex === '1') {
							const femaleNum =
								Number(this.formData.statistics[0].inputFemaleNum) + (isAdd ? 1 : -1);
							this.$set(this.formData.statistics[0], 'inputFemaleNum', femaleNum);
						}

						// 更新学历统计
						const eduField = educationStatMap[item.highestEducation];
						if (eduField) {
							const eduNum = Number(this.formData.statistics[0][eduField]) + (isAdd ? 1 : -1);
							this.$set(this.formData.statistics[0], eduField, eduNum);
						}

						// 更新职称统计
						const rankField = rankStatMap[item.rank];
						if (rankField) {
							const rankNum = Number(this.formData.statistics[0][rankField]) + (isAdd ? 1 : -1);
							this.$set(this.formData.statistics[0], rankField, rankNum);
						}
					});
				};

				// 处理旧值和新值
				updateStatistics(oldVal, false);
				updateStatistics(newVal, true);

				// 保存当前值为旧值
				this.oldTeachers = JSON.parse(JSON.stringify(newVal));

				// 计算总数
				const inputTotal = [
					'inputHighNum',
					'inputMiddleNum',
					'inputElementaryNum',
					'inputElseNum'
				].reduce((sum, key) => sum + Number(this.formData.statistics[0][key]), 0);
				this.$set(this.formData.statistics[0], 'inputTotal', inputTotal);
				this.editCount++;
			},
			deep: true
		},
		'formData.students': {
			handler(newVal, oldVal1) {
				const oldVal = this.oldStudents;
				if (this.title.includes('编辑') && this.studentCount <= 1) {
					this.studentCount++;
					this.oldStudents = JSON.parse(JSON.stringify(newVal));
					return;
				}
				if (this.formReadonly) return;

				// 统计女性学生信息
				const updateFemaleStudentStats = (items, isAdd = true) => {
					items?.forEach(item => {
						if (item.sex == '1') {
							// 女性
							const femaleNum =
								Number(this.formData.statistics[0].inputFemaleNum) + (isAdd ? 1 : -1);
							this.$set(this.formData.statistics[0], 'inputFemaleNum', femaleNum);
						}
					});
				};

				// 处理旧值和新值
				updateFemaleStudentStats(oldVal, false); // 减去旧值
				updateFemaleStudentStats(newVal, true); // 加上新值
				this.oldStudents = JSON.parse(JSON.stringify(newVal));
				this.editCount++;
			},
			deep: true
		}
	},
	created() {
		this.getSysCode();
		this.getDiscipline('', '1'); // 获取一级学科
		this.selectDictByType('ecoGoalType');
		this.selectDictByType('ecoIndustryType');
	},
	methods: {
		changeTeacher() {},
		teacherSubmit() {
			this.teacherForm.id = uuidv4();
			const isEditing = this.newTitle.includes('编辑');
			const isTeacher = this.newTitle.includes('教师');


			let {phone} = this.teacherForm
			   const regex = /^1[3-9]\d{9}$/;
			   let PhoneVerify = regex.test(phone);
			   if (!PhoneVerify) {
				this.$message.error('请输入正确的联系电话');
				 return
			   }

			// 处理编辑情况
			if (isEditing) {
				const targetList = isTeacher ? this.formData.teachers : this.formData.students;
				this.$set(targetList, this.editIndex, this.teacherForm);
			} else {
				this.teacherForm.deptId = this.teacherForm.deptId ? this.teacherForm.deptId : '';
				this.teacherForm.memberName = this.teacherForm.name;

				this.formData.teachers = this.formData.teachers || [];
				this.formData.students = this.formData.students || [];
				// 处理新增情况
				const targetList = isTeacher ? this.formData.teachers : this.formData.students;
				targetList.push(this.teacherForm);
			}

			// 重置表单和对话框状态
			this.teacherForm = {};
			this.teacherDialog = false;
		},

		// 批量请求数据字典
		getSysCode() {
			// 工作量 project_workload
			// 研究类别 project_study_type
			// 活动类型 project_activity_type
			// 项目来源 project_source
			// 组织形式 project_organization_form
			// 合作形式 project_cooperation_form
			// 横向项目-合作类型 project_cooperation_type
			// 纵向项目-合同类型 project_contract_type
			// 项目类型 project_type
			// 申报说明 project_declaration_instructions
			// 项目-预期成果形势|最终成果形势 project_results_form
			// 项目级别 纵向-project_level_lengthways、院级-project_level_college

			//人员类型 project_member_type
			//证件类型 member_idcard_type
			//承担类型 member_assume_type
			//职称 member_rank
			//国籍 member_nationality
			//民族 member_nation
			//政治面貌 member_political_status
			//职务类别 member_position_type
			//岗位类型 member_post_type
			//最后学历 member_highest_education
			//最后学位 member_highest_degree
			// const codes = 'project_type,project_cooperation_type';
			const codes =
				'project_results_form,project_level_lengthways,project_level_college,project_workload,project_study_type,project_activity_type,project_source,project_organization_form,project_cooperation_form,project_cooperation_type,project_contract_type,project_type,project_declaration_instructions,member_assume_type,member_rank,project_member_type,member_idcard_type,member_nationality,member_nation,member_political_status,member_position_type,member_post_type,member_highest_education,member_highest_degree,project_technical_position,selectList1,project_st_activity_member_type,project_source_college';
			this.$utils.findSysCodeList(codes).then(res => {
				this.codesObj = { ...this.codesObj, ...res };
			});
		},
		// 请求学科
		getDiscipline(parent = '', level = '') {
			//type 类型  parent 父级编码  level 层级
			this.$request({
				url: '/ybzy/projectMultilevelDict/selectList',
				params: { type: 'discipline', parent, level }
			}).then(res => {
				if (res.rCode == 0) {
					const arr = res.results;
					switch (level) {
						case '1':
							this.optionData.firstLevelDiscipline = arr;
							this.codesObj.selectList1 = arr;
							break;
						case '2':
							this.optionData.subDiscipline = arr;
							break;
						case '3':
							this.optionData.tertiaryDiscipline = arr;
							break;
						default:
							break;
					}
				} else {
					this.$message.error(res.msg);
				}
			});
		},
		// 请求经济目标 经济行业
		selectDictByType(type) {
			// 项目的社会经济目标 type=ecoGoalType
			// 服务的国民经济行业 type=ecoIndustryType
			this.$request({
				url: '/ybzy/projectMultilevelDict/selectDictByType',
				params: { type }
			}).then(res => {
				if (res.rCode == 0) {
					const arr = res.results;
					if (type) {
						this.$set(this.codesObj, type, arr);
					}
				} else {
					this.$message.error(res.msg);
				}
			});
		},
		// 是否只读和编辑
		toolFormReadonly() {
			this.$nextTick(() => {
				const isEdit = window.location.href.includes('isEdit'); // 判断是否流程页面的编辑
				this.formReadonly = this.title.includes('查看') && !isEdit ? true : false;
			});
		},
		//表格修改弹窗操作
		btnClick({ handle, row }) {
			// console.log(">>>", this.$router.resolve({ name: "allotSubmitList" }));
			// let { text, btnType } = handle;
			// this.row = { ...row, type: String(row.type), assumeType: String(row.assumeType) };
		},

		changeStatistics({ data, name, item }) {
			if (name !== 'inputTotal') {
				const inputTotal =
					// Number(data.inputFemaleNum || 0) +
					Number(data.inputHighNum || 0) +
					Number(data.inputMiddleNum || 0) +
					Number(data.inputElementaryNum || 0) +
					Number(data.inputElseNum || 0) +
					// Number(data.joinDoctorNum || 0) +
					// Number(data.joinMasterNum || 0);
					this.$set(this.formData.statistics[0], 'inputTotal', inputTotal);
			}
		},
		// 表格行高
		tableRowClassName({ row, rowIndex }) {
			let styleRes = {
				height: '54px !important'
			};
			return styleRes;
		},
		//修改保存
		async save(callBank) {
			if (!callBank) {
				const loading = this.load('提交中...');
				this.formData.expectedResultsForm = this.formData?.expectedResultsForm?.join?.(',');

				let formData = {
					...this.formData,
					ecoIndustryType: this.formData?.ecoIndustryType?.join?.(','),
					ecoGoalType: this.formData?.ecoGoalType?.join?.(','),
					students: this.formData.students?.map(e => {
						delete e.studentFile;
						delete e.member;
						return e;
					}),
					teachers: [
						...this.formData.teachersMy?.map(e => {
							delete e.member;
							return e;
						}),
						...this.formData.teachers.map(e => {
							delete e.member;
							return e;
						})
					],
					...(this.formData?.statistics?.[0] || {})
				};
				delete formData.teachersMy;
				delete formData.statistics;
				formData.fj1 && delete formData.fj1;
				formData.fj2 && delete formData.fj2;
				formData.fj3 && delete formData.fj3;
				// projectClassify->项目分类（字典编码：  project_classify->0：纵向；1：横向；2：院级）
				let url = this.title.includes('新增') ? this.wd.basics.save : this.wd.basics.edit;
				this.$.ajax({
					url: url,
					method: 'post',
					loading,
					data: {
						...formData,
						projectClassify:
							this.contentsKey === 'crosswiseProject'
								? '1'
								: this.contentsKey === 'verticalProject'
									? '0'
									: '2'
					},
					format: false
				}).then(res => {
					if (res.rCode == 0) {
						if (callBank) {
							callBank();
						} else {
							this.$message.success('暂存成功');
							// this.handleSuccess();
							this.$emit('visible', false);
							this.$emit('update:visible', false);
						}
					} else {
						this.$message.warning(res.msg);
					}
				});
				return;
			}

			const valid = await this.asyncValidate('formRef');
			// const valid1 = await this.asyncValidate('formRef1');
			// const valid2 = await this.asyncValidate('formRef2');
			const valid3 = await this.asyncValidate('formRef3');
			const valid4 = await this.asyncValidate('formRef4');
			// && valid1 && valid2
			if (valid && valid3 && valid4) {
				if (this.formData.teachersMy.length < 1) {
					this.$message.warning('教师成员信息请为第一条数据添加主持（负责人）');
					return;
				}

				console.log('>>>>', this.formData);
				const loading = this.load('提交中...');
				this.formData.expectedResultsForm = this.formData?.expectedResultsForm?.join?.(',');

				let formData = {
					...this.formData,
					ecoIndustryType: this.formData?.ecoIndustryType?.join?.(','),
					ecoGoalType: this.formData?.ecoGoalType?.join?.(','),
					students: this.formData.students?.map(e => {
						delete e.studentFile;
						delete e.member;
						return e;
					}),
					teachers: [
						...this.formData.teachersMy?.map(e => {
							delete e.member;
							return e;
						}),
						...this.formData.teachers.map(e => {
							delete e.member;
							return e;
						})
					],
					...(this.formData?.statistics?.[0] || {})
				};
				delete formData.teachersMy;
				delete formData.statistics;
				formData.fj1 && delete formData.fj1;
				formData.fj2 && delete formData.fj2;
				formData.fj3 && delete formData.fj3;
				// projectClassify->项目分类（字典编码：  project_classify->0：纵向；1：横向；2：院级）
				let url = this.title.includes('新增') ? this.wd.basics.save : this.wd.basics.edit;
				this.$.ajax({
					url: url,
					method: 'post',
					loading,
					data: {
						...formData,
						projectClassify:
							this.contentsKey === 'crosswiseProject'
								? '1'
								: this.contentsKey === 'verticalProject'
									? '0'
									: '2'
					},
					format: false
				}).then(res => {
					if (res.rCode == 0) {
						if (callBank) {
							callBank();
						} else {
							this.$message.success(res.msg);
							this.handleSuccess();
						}
					} else {
						this.$message.warning(res.msg);
					}
				});
			} else {
				this.$message.warning('请先完成必填项！');
				return false;
			}
		},
		asyncValidate(ref) {
			return new Promise((resolve, reject) => {
				if (this.$refs[ref]) {
					this.$refs[ref].validate(valid => {
						resolve(valid);
					});
				} else {
					resolve(true);
				}
			});
		},
		// 提交成功
		handleSuccess(e) {
			//pendingId则为流程的审核页面，否则是弹窗的流程
			const isFlow = window.location.href.includes('pendingId');
			if (isFlow) {
				window.close();
			} else {
				this.$emit('visible', false);
				this.$emit('update:visible', false);
			}
		},

		// 页面加载
		load(text) {
			return this.$.loading(this.$loading, text);
		},
		async getPorjectInfo() {
			// 横向假数据
			const pro1 = {
				students: [],
				teachers: [],
				statistics: [
					{
						inputFemaleNum: 0,
						inputHighNum: 0,
						inputMiddleNum: 0,
						inputElementaryNum: 0,
						inputElseNum: 0,
						joinDoctorNum: 0,
						joinMasterNum: 0
					}
				],
				teachersMy: [
					{
						id: '13d45d50-0b61-4f49-a458-096ff4969414',
						type: '0',
						typeTxt: '校内人员',
						name: '张灵',
						englishName: 'zhangning',
						formerName: '无',
						number: '*********',
						sex: '1',
						sexTxt: '女',
						birthdate: '2024-08-22',
						idcardType: '1',
						idcardTypeTxt: '身份证件',
						idcard: '512501197511061962',
						nationality: '1',
						nationalityTxt: '中国',
						nation: '1',
						nationTxt: '汉族',
						politicalStatus: '1',
						politicalStatusTxt: '中共党员',
						orgCode: '',
						orgName: '教务处',
						entryDate: '2024-08-30',
						isPermanentStaff: '1',
						isPermanentStaffTxt: '在编',
						rank: '1',
						rankTxt: '教授',
						positionType: '1',
						positionTypeTxt: '教师技术职务系列人员',
						technicalPosition: '无',
						postType: '1',
						postTypeTxt: '教学科研（教学型）',
						administrativePost: '无',
						confirmPostDate: '2024-08-30',
						highestEducation: '1',
						highestEducationTxt: '研究生',
						highestDegree: '1',
						highestDegreeTxt: '博士',
						firstLevelDiscipline: '210',
						firstLevelDisciplineTxt: '农学',
						subDiscipline: '21020',
						subDisciplineTxt: '农业基础学科',
						tertiaryDiscipline: '2102010',
						tertiaryDisciplineTxt: '农业数学',
						subject: '210',
						subjectTxt: '农学',
						relatedDiscipline: '农业',
						researchDirection: '无',
						isDoctoralAdvisor: '',
						isDoctoralAdvisorTxt: null,
						firstLanguage: '',
						firstLanguageTxt: null,
						firstLanguageLevel: '',
						firstLanguageLevelTxt: null,
						secondLanguage: '',
						secondLanguageTxt: null,
						secondLanguageLevel: '',
						secondLanguageLevelTxt: null,
						furtherStudy: '',
						socialWork: '',
						socialSpeciality: '',
						phone: '15881385434',
						officePhone: '无',
						email: '无',
						personalWebsite: '无',
						activityMemberType: '无',
						remark: '',
						assumeType: '0'
					}
				],
				id: '36695a90-1345-4299-a9fa-95751acbd878',
				declarationAdjunctId: 'cf9fe207-b88f-4b71-ac31-00edc61a322a',
				materialsAdjunctId: 'd68a6798-799c-4c1e-b1ba-c137aee6fc59',
				contractListAdjunctId: 'a2ba7e5e-c3e8-420b-a857-566810d86f29',
				contractAdjunctId: 'aff98eac-77d5-4734-85f4-64abb7ceeaa1',
				projectName: '量子力学课题研究2',
				projectType: '1',
				doctorPerYear: 4,
				signContractDate: '2024-09-03',
				studyStartDate: '2024-09-04',
				studyEndDate: '2024-09-12',
				approveDate: '2024-09-13',
				subjectType: '130',
				activityType: '2',
				projectSource: '1',
				ecoIndustryType: ['C', '16', '169'],
				test: '1',
				cooForm: '1',
				responsibleContent: '无',
				ecoGoalType: ['011', '01104'],
				cooperationType: '2',
				contractType: '1',
				projectAdjunctId: '6840e7dd-e48f-4ad7-a712-3f450207a21a',
				orgForm: '1',
				isSign: '1',
				// fj1: [
				// 	{
				// 		status: 'success',
				// 		name: '339563-106.jpg',
				// 		size: 11576,
				// 		percentage: 100,
				// 		uid: 1725700503967,
				// 		raw: '[object File]',
				// 		suffix: 'jpg',
				// 		uploadTime: '2024-09-07 17:15:05',
				// 		fileSize: '11.3KB',
				// 		userName: '管理员',
				// 		response: {
				// 			adjunctId: 'eb98f697-11c9-40ee-99e9-1a377a5ba4c1',
				// 			folder: 'pro_crosswise_audit_att',
				// 			originalName: '339563-106.jpg',
				// 			newName: 'ca0ade68-79e0-42af-b98d-67c0ebfc359f.jpg',
				// 			userName: '管理员',
				// 			url: 'a934ec8a-f047-41ed-98b5-bfd6209b891e',
				// 			absolutePath: 'procrosswiseauditatt',
				// 			suffix: 'jpg',
				// 			size: 11576,
				// 			status: 0,
				// 			uploadTime: '2024-09-07 17:15:05',
				// 			orderIndex: 0,
				// 			extend: null,
				// 			fileSize: '11.3KB',
				// 			filenamecolour: null,
				// 			createorgid: null,
				// 			createdeptid: null,
				// 			createorgName: null,
				// 			createdeptName: null,
				// 			ownereorgid: null,
				// 			ownerdeptid: null,
				// 			ownerorgName: null,
				// 			ownerdeptName: null,
				// 			classid: null,
				// 			className: null,
				// 			ownId: '6840e7dd-e48f-4ad7-a712-3f450207a21a',
				// 			code: 'pro_crosswise_audit_att',
				// 			titleWithoutExt: '339563-106',
				// 			fileOppositePath: null
				// 		}
				// 	}
				// ],
				// fj2: [
				// 	{
				// 		status: 'success',
				// 		name: '342174-106.jpg',
				// 		size: 23027,
				// 		percentage: 100,
				// 		uid: 1725700508468,
				// 		raw: '[object File]',
				// 		suffix: 'jpg',
				// 		uploadTime: '2024-09-07 17:15:10',
				// 		fileSize: '22.5KB',
				// 		userName: '管理员',
				// 		response: {
				// 			adjunctId: '2e7f0f69-7be2-404d-bd5b-12d994f089ec',
				// 			folder: 'pro_crosswise_contract_att',
				// 			originalName: '342174-106.jpg',
				// 			newName: '937c4722-e57f-46c1-9a71-bffbc507ea13.jpg',
				// 			userName: '管理员',
				// 			url: '84c3407c-c462-4770-b07d-17c331c9e25c',
				// 			absolutePath: 'procrosswisecontractatt',
				// 			suffix: 'jpg',
				// 			size: 23027,
				// 			status: 0,
				// 			uploadTime: '2024-09-07 17:15:10',
				// 			orderIndex: 0,
				// 			extend: null,
				// 			fileSize: '22.5KB',
				// 			filenamecolour: null,
				// 			createorgid: null,
				// 			createdeptid: null,
				// 			createorgName: null,
				// 			createdeptName: null,
				// 			ownereorgid: null,
				// 			ownerdeptid: null,
				// 			ownerorgName: null,
				// 			ownerdeptName: null,
				// 			classid: null,
				// 			className: null,
				// 			ownId: '6840e7dd-e48f-4ad7-a712-3f450207a21a',
				// 			code: 'pro_crosswise_contract_att',
				// 			titleWithoutExt: '342174-106',
				// 			fileOppositePath: null
				// 		}
				// 	}
				// ],
				declarationInstructions: '0',
				declareTime: '2024-09-12'
			};
			// 纵向假数据
			const pro2 = {
				students: [],
				teachers: [
					{
						id: 'd6dc2809-b734-444c-875d-61f4898c3355',
						type: '0',
						member: [
							{
								showid: 'u636bea576c474755a654f662dcfdbf25',
								showname: '管理员(管理员)',
								showshortname: '',
								stype: 'employee',
								stypename: null,
								pathfilid: 'en7a2d5188629441cd9d4469c89dcd182d',
								pathname: '宜宾职业技术学院-管理员',
								attr: '{"userId":"u636bea576c474755a654f662dcfdbf25","oldUserId":null,"outcode":"ybzyadmin","username":"管理员","orgId":"en7a2d5188629441cd9d4469c89dcd182d","orgpId":null,"orgCode":null,"orgName":"宜宾职业技术学院","creditCode":null,"orgShortName":"管理员","hideUnits":0,"orgBusinessName":null,"depId":"dep9201c51a86b346ca92352d3549553ef8","olddepId":null,"depCode":null,"depName":"管理员","depShortName":"管理员","loginName":"ybzyadmin","phone":"***********","email":"","idcard":"511522190001011219","officetel":"","cakey":"","postId":"596bd9bdf69e46eb97b1f9ca8608e4c7","postName":"管理员","subcenterId":"","subcenterName":null,"position":"","loginid":"41aba02d01b449c3b56723503da3d9db","personid":"em90d577f65ba9421888bfc189a83221c0","userpersonid":null,"sex":"0","pwd":null,"oldpwd":null,"sort":0,"plsort":0,"entrustUserId":null,"isaddress":0,"isofficetel":0,"ischooser":0,"state":1,"officeId":null,"officeCode":null,"officeName":null,"officefullName":null,"addn":null,"adobjecid":null,"telshort":null,"type":"0","roleRange":null,"userRolelist":null,"usrResource":null,"birthface":null,"workingyears":null,"orgStructureType":null,"subsystemids":null,"orgpath":null,"tenantid":null,"tenantfullName":null,"tenantshortName":null,"txtbornDate":null,"historystate":0,"otherPostLevel":0,"updateorgRootPath":null,"extendList":{}}',
								strdisabled: 'false'
							}
						],
						number: 'ybzyadmin',
						orgName: '宜宾职业技术学院',
						deptId: 'en7a2d5188629441cd9d4469c89dcd182d',
						phone: '***********',
						name: '管理员',
						professional: '管理员',
						rank: '1',
						assumeType: '2'
					}
				],
				statistics: [
					{
						inputFemaleNum: 6,
						inputHighNum: 6,
						inputMiddleNum: 6,
						inputElementaryNum: 2,
						inputElseNum: 0,
						joinDoctorNum: 0,
						joinMasterNum: 0,
						inputTotal: 20
					}
				],
				teachersMy: [
					{
						id: '13d45d50-0b61-4f49-a458-096ff4969414',
						type: '0',
						typeTxt: '校内人员',
						name: '张灵',
						englishName: 'zhangning',
						formerName: '无',
						number: '*********',
						sex: '1',
						sexTxt: '女',
						birthdate: '2024-08-22',
						idcardType: '1',
						idcardTypeTxt: '身份证件',
						idcard: '512501197511061962',
						nationality: '1',
						nationalityTxt: '中国',
						nation: '1',
						nationTxt: '汉族',
						politicalStatus: '1',
						politicalStatusTxt: '中共党员',
						orgCode: '',
						orgName: '教务处',
						entryDate: '2024-08-30',
						isPermanentStaff: '1',
						isPermanentStaffTxt: '在编',
						rank: '1',
						rankTxt: '教授',
						positionType: '1',
						positionTypeTxt: '教师技术职务系列人员',
						technicalPosition: '无',
						postType: '1',
						postTypeTxt: '教学科研（教学型）',
						administrativePost: '无',
						confirmPostDate: '2024-08-30',
						highestEducation: '1',
						highestEducationTxt: '研究生',
						highestDegree: '1',
						highestDegreeTxt: '博士',
						firstLevelDiscipline: '210',
						firstLevelDisciplineTxt: '农学',
						subDiscipline: '21020',
						subDisciplineTxt: '农业基础学科',
						tertiaryDiscipline: '2102010',
						tertiaryDisciplineTxt: '农业数学',
						subject: '210',
						subjectTxt: '农学',
						relatedDiscipline: '农业',
						researchDirection: '无',
						isDoctoralAdvisor: '',
						isDoctoralAdvisorTxt: null,
						firstLanguage: '',
						firstLanguageTxt: null,
						firstLanguageLevel: '',
						firstLanguageLevelTxt: null,
						secondLanguage: '',
						secondLanguageTxt: null,
						secondLanguageLevel: '',
						secondLanguageLevelTxt: null,
						furtherStudy: '',
						socialWork: '',
						socialSpeciality: '',
						phone: '15881385434',
						officePhone: '无',
						email: '无',
						personalWebsite: '无',
						activityMemberType: '无',
						remark: '',
						assumeType: '0'
					}
				],
				id: 'b90fec45-040e-4484-bf55-1a4cc5969cb1',
				projectType: '1',
				doctorPerYear: 2,
				projectName: '关于社会主义中国化研究',
				projectLevel: '0',
				belowOrgName: '无',
				studyEndDate: '2024-09-06',
				projectSource: '1',
				studyStartDate: '2024-09-06',
				approveDate: '2024-09-13',
				subjectType: '120',
				activityType: '1',
				orgForm: '2',
				cooForm: '2',
				isSign: '1',
				declareTaskSource: 'www.63.com',
				researchContents: '无',
				remark: '无',
				responsibleContent: '无',
				declarationInstructions: '0',
				declareTime: '2024-09-12',
				inputFemaleNum: 0,
				inputHighNum: 0,
				inputMiddleNum: 0,
				inputElementaryNum: 0,
				inputElseNum: 0,
				joinDoctorNum: 0,
				joinMasterNum: 0,
				projectClassify: '0',
				fj1: [
					{
						adjunctId: '63522a24-54b7-43a3-a684-7c74f3e9ab0c',
						folder: 'pro_declaration_att',
						originalName: '342174-106.jpg',
						newName: 'fb94c912-0d07-4530-8588-deda16dff157.jpg',
						userName: '管理员',
						url: 'bc208402-8c95-4eba-9908-7d4dbd5900fb',
						absolutePath: 'prodeclarationatt',
						suffix: 'jpg',
						size: 23027,
						status: 'success',
						uploadTime: '2024-09-07 15:11:18',
						orderIndex: 0,
						extend: { outsideid: null, outSideOwnerId: null },
						fileSize: '22.5KB',
						filenamecolour: null,
						createorgid: null,
						createdeptid: null,
						createorgName: null,
						createdeptName: null,
						ownereorgid: null,
						ownerdeptid: null,
						ownerorgName: null,
						ownerdeptName: null,
						classid: null,
						className: null,
						ownId: 'f805ca04-aadf-491d-9b7f-46f392668786',
						code: 'pro_declaration_att',
						titleWithoutExt: '342174-106',
						fileOppositePath: null,
						uid: 1725694073922
					}
				],
				fj2: [
					{
						adjunctId: 'e8b4b123-0c95-4054-ba4b-addaec7615b5',
						folder: 'pro_lengthways_audit_att',
						originalName: '334907-106.jpg',
						newName: 'a4f7df1e-6c6f-4399-884a-0ac3d5171862.jpg',
						userName: '管理员',
						url: 'd30c6ad5-c4a8-4039-897c-9857936a6802',
						absolutePath: 'prolengthwaysauditatt',
						suffix: 'jpg',
						size: 29607,
						status: 'success',
						uploadTime: '2024-09-07 15:11:22',
						orderIndex: 0,
						extend: { outsideid: null, outSideOwnerId: null },
						fileSize: '28.9KB',
						filenamecolour: null,
						createorgid: null,
						createdeptid: null,
						createorgName: null,
						createdeptName: null,
						ownereorgid: null,
						ownerdeptid: null,
						ownerorgName: null,
						ownerdeptName: null,
						classid: null,
						className: null,
						ownId: 'f805ca04-aadf-491d-9b7f-46f392668786',
						code: 'pro_lengthways_audit_att',
						titleWithoutExt: '334907-106',
						fileOppositePath: null,
						uid: 1725694074214
					}
				],
				ecoIndustryType: ['B', '07', '071'],
				ecoGoalType: ['011', '01103']
			};
			// 院级假数据
			const pro3 = {
				students: [],
				teachers: [
					{
						id: 'd6dc2809-b734-444c-875d-61f4898c3355',
						type: '0',
						member: [
							{
								showid: 'u636bea576c474755a654f662dcfdbf25',
								showname: '管理员(管理员)',
								showshortname: '',
								stype: 'employee',
								stypename: null,
								pathfilid: 'en7a2d5188629441cd9d4469c89dcd182d',
								pathname: '宜宾职业技术学院-管理员',
								attr: '{"userId":"u636bea576c474755a654f662dcfdbf25","oldUserId":null,"outcode":"ybzyadmin","username":"管理员","orgId":"en7a2d5188629441cd9d4469c89dcd182d","orgpId":null,"orgCode":null,"orgName":"宜宾职业技术学院","creditCode":null,"orgShortName":"管理员","hideUnits":0,"orgBusinessName":null,"depId":"dep9201c51a86b346ca92352d3549553ef8","olddepId":null,"depCode":null,"depName":"管理员","depShortName":"管理员","loginName":"ybzyadmin","phone":"***********","email":"","idcard":"511522190001011219","officetel":"","cakey":"","postId":"596bd9bdf69e46eb97b1f9ca8608e4c7","postName":"管理员","subcenterId":"","subcenterName":null,"position":"","loginid":"41aba02d01b449c3b56723503da3d9db","personid":"em90d577f65ba9421888bfc189a83221c0","userpersonid":null,"sex":"0","pwd":null,"oldpwd":null,"sort":0,"plsort":0,"entrustUserId":null,"isaddress":0,"isofficetel":0,"ischooser":0,"state":1,"officeId":null,"officeCode":null,"officeName":null,"officefullName":null,"addn":null,"adobjecid":null,"telshort":null,"type":"0","roleRange":null,"userRolelist":null,"usrResource":null,"birthface":null,"workingyears":null,"orgStructureType":null,"subsystemids":null,"orgpath":null,"tenantid":null,"tenantfullName":null,"tenantshortName":null,"txtbornDate":null,"historystate":0,"otherPostLevel":0,"updateorgRootPath":null,"extendList":{}}',
								strdisabled: 'false'
							}
						],
						number: 'ybzyadmin',
						orgName: '宜宾职业技术学院',
						deptId: 'en7a2d5188629441cd9d4469c89dcd182d',
						phone: '***********',
						name: '管理员',
						professional: '管理员',
						rank: '1',
						assumeType: '2'
					}
				],
				statistics: [
					{
						inputFemaleNum: 6,
						inputHighNum: 6,
						inputMiddleNum: 6,
						inputElementaryNum: 2,
						inputElseNum: 0,
						joinDoctorNum: 0,
						joinMasterNum: 0,
						inputTotal: 20
					}
				],
				teachersMy: [
					{
						id: '13d45d50-0b61-4f49-a458-096ff4969414',
						type: '0',
						typeTxt: '校内人员',
						name: '张灵',
						englishName: 'zhangning',
						formerName: '无',
						number: '*********',
						sex: '1',
						sexTxt: '女',
						birthdate: '2024-08-22',
						idcardType: '1',
						idcardTypeTxt: '身份证件',
						idcard: '512501197511061962',
						nationality: '1',
						nationalityTxt: '中国',
						nation: '1',
						nationTxt: '汉族',
						politicalStatus: '1',
						politicalStatusTxt: '中共党员',
						orgCode: '',
						orgName: '教务处',
						entryDate: '2024-08-30',
						isPermanentStaff: '1',
						isPermanentStaffTxt: '在编',
						rank: '1',
						rankTxt: '教授',
						positionType: '1',
						positionTypeTxt: '教师技术职务系列人员',
						technicalPosition: '无',
						postType: '1',
						postTypeTxt: '教学科研（教学型）',
						administrativePost: '无',
						confirmPostDate: '2024-08-30',
						highestEducation: '1',
						highestEducationTxt: '研究生',
						highestDegree: '1',
						highestDegreeTxt: '博士',
						firstLevelDiscipline: '210',
						firstLevelDisciplineTxt: '农学',
						subDiscipline: '21020',
						subDisciplineTxt: '农业基础学科',
						tertiaryDiscipline: '2102010',
						tertiaryDisciplineTxt: '农业数学',
						subject: '210',
						subjectTxt: '农学',
						relatedDiscipline: '农业',
						researchDirection: '无',
						isDoctoralAdvisor: '',
						isDoctoralAdvisorTxt: null,
						firstLanguage: '',
						firstLanguageTxt: null,
						firstLanguageLevel: '',
						firstLanguageLevelTxt: null,
						secondLanguage: '',
						secondLanguageTxt: null,
						secondLanguageLevel: '',
						secondLanguageLevelTxt: null,
						furtherStudy: '',
						socialWork: '',
						socialSpeciality: '',
						phone: '15881385434',
						officePhone: '无',
						email: '无',
						personalWebsite: '无',
						activityMemberType: '无',
						remark: '',
						assumeType: '0'
					}
				],
				projectAdjunctId: 'ea177669-c3af-4054-8a9a-c7f83e737b7b',
				id: 'eb532509-04ab-4f46-bb39-4d60b9227035',
				projectType: '1',
				doctorPerYear: 2,
				projectName: '关于社会主义中国化研究',
				projectLevel: '0',
				belowOrgName: '无',
				studyEndDate: '2024-09-06',
				projectSource: '1',
				studyStartDate: '2024-09-06',
				approveDate: '2024-09-13',
				subjectType: '120',
				activityType: '1',
				orgForm: '2',
				cooForm: '2',
				isSign: '1',
				declareTaskSource: 'www.63.com',
				researchContents: '无',
				remark: '无',
				responsibleContent: '无',
				declarationInstructions: '0',
				declareTime: '2024-09-12',
				inputFemaleNum: 0,
				inputHighNum: 0,
				inputMiddleNum: 0,
				inputElementaryNum: 0,
				inputElseNum: 0,
				joinDoctorNum: 0,
				joinMasterNum: 0,
				projectClassify: '0',
				fj1: [
					{
						adjunctId: '63522a24-54b7-43a3-a684-7c74f3e9ab0c',
						folder: 'pro_declaration_att',
						originalName: '342174-106.jpg',
						newName: 'fb94c912-0d07-4530-8588-deda16dff157.jpg',
						userName: '管理员',
						url: 'bc208402-8c95-4eba-9908-7d4dbd5900fb',
						absolutePath: 'prodeclarationatt',
						suffix: 'jpg',
						size: 23027,
						status: 'success',
						uploadTime: '2024-09-07 15:11:18',
						orderIndex: 0,
						extend: { outsideid: null, outSideOwnerId: null },
						fileSize: '22.5KB',
						filenamecolour: null,
						createorgid: null,
						createdeptid: null,
						createorgName: null,
						createdeptName: null,
						ownereorgid: null,
						ownerdeptid: null,
						ownerorgName: null,
						ownerdeptName: null,
						classid: null,
						className: null,
						ownId: 'f805ca04-aadf-491d-9b7f-46f392668786',
						code: 'pro_declaration_att',
						titleWithoutExt: '342174-106',
						fileOppositePath: null,
						uid: 1725694073922
					}
				],
				fj2: [
					{
						adjunctId: 'e8b4b123-0c95-4054-ba4b-addaec7615b5',
						folder: 'pro_lengthways_audit_att',
						originalName: '334907-106.jpg',
						newName: 'a4f7df1e-6c6f-4399-884a-0ac3d5171862.jpg',
						userName: '管理员',
						url: 'd30c6ad5-c4a8-4039-897c-9857936a6802',
						absolutePath: 'prolengthwaysauditatt',
						suffix: 'jpg',
						size: 29607,
						status: 'success',
						uploadTime: '2024-09-07 15:11:22',
						orderIndex: 0,
						extend: { outsideid: null, outSideOwnerId: null },
						fileSize: '28.9KB',
						filenamecolour: null,
						createorgid: null,
						createdeptid: null,
						createorgName: null,
						createdeptName: null,
						ownereorgid: null,
						ownerdeptid: null,
						ownerorgName: null,
						ownerdeptName: null,
						classid: null,
						className: null,
						ownId: 'f805ca04-aadf-491d-9b7f-46f392668786',
						code: 'pro_lengthways_audit_att',
						titleWithoutExt: '334907-106',
						fileOppositePath: null,
						uid: 1725694074214
					}
				],
				ecoIndustryType: ['B', '07', '071'],
				ecoGoalType: ['011', '01103'],
				contractAdjunctId: '9b071028-3818-4ef5-a20a-ff124ba2050f',
				targetTask: '无'
			};
			if (this.title.includes('新增')) {
				this.formData = {
					students: [],
					teachers: [],
					statistics: [{}],
					teachersMy: [],
					// ...pro2,
					projectAdjunctId: this.$uuidv4(),
					id: this.id,
					projectType: '1'
				};
				this.toolFormReadonly();
				return;
			}

			//如果有传入数据就直接用，没有就请求数据
			if (JSON.stringify(this.formDataN) !== '{}') {
				this.setTableData(this.formDataN);
				this.toolFormReadonly();
				return;
			}

			let loading = this.load('加载中...');
			try {
				let { rCode, msg, results } = await this.$.ajax({
					url: this.wd.basics.info,
					method: 'get',
					loading,
					params: {
						id: this.id
						// businessCode: 'projectApply',
						// projectClassify:
						// 	this.contentsKey === 'crosswiseProject'
						// 		? '1'
						// 		: this.contentsKey === 'verticalProject'
						// 		? '0'
						// 		: '2'
					}
				});

				if (rCode == 0) {
					let obj = results.changeProjectInfo ? JSON.parse(results.changeProjectInfo) : results;
					const changeProjectInfo = results;
					this.setTableData(obj || {}, changeProjectInfo || {});
					this.toolFormReadonly();
				} else {
					this.$message.error(msg);
				}
			} catch (error) {
				console.error('>>>error', error);
			}
		},
		setTableData(obj, changeProjectInfo) {
			const teacherCopy = JSON.parse(JSON.stringify(obj?.teachers || []));
			obj.expectedResultsForm = obj?.expectedResultsForm?.split?.(',') || [];
			let formData = {
				...obj,
				ecoIndustryType: obj?.ecoIndustryType?.split?.(',') || [],
				ecoGoalType: obj?.ecoGoalType?.split?.(',') || [],
				teachersMy: [],
				teachers: [],
				statistics: [
					{
						inputTotal: obj?.inputTotal,
						inputFemaleNum: obj?.inputFemaleNum,
						inputHighNum: obj?.inputHighNum,
						inputMiddleNum: obj?.inputMiddleNum,
						inputElementaryNum: obj?.inputElementaryNum,
						inputElseNum: obj?.inputElseNum,
						joinDoctorNum: obj?.joinDoctorNum,
						joinMasterNum: obj?.joinMasterNum
					}
				],
				students:
					obj?.students?.map?.(e => {
						e.member = [
							{
								outcode: e.number,
								username: e.name,
								showname: e.name,
								orgName: e.orgName,
								orgId: e.deptId,

								phone: e.phone
							}
						];
						return e;
					}) || [],
				// 变更数据
				terminateState: changeProjectInfo?.terminateState || '',
				changeReason: changeProjectInfo?.changeReason || '',
				changeContent: changeProjectInfo?.changeContent || '',
				appId: changeProjectInfo?.appId || obj?.appId,
				changeId: changeProjectInfo?.id || this.$uuidv4()
			};

			// if (this.title.includes('变更')) {
			// 	formData.changeId = this.$uuidv4();
			// }
			teacherCopy?.forEach?.((e, i) => {
				e.member = [
					{
						outcode: e.number,
						username: e.name,
						showname: e.name,
						orgName: e.orgName,
						orgId: e.deptId,
						postName: e.professional,
						phone: e.phone
					}
				];
				if (e.assumeType === '0') {
					formData.teachersMy.push(e);
				} else {
					formData.teachers.push(e);
				}
					// formData.teachers.push(e);

			});
			this.formData = formData;
			console.log(formData, 'formData');
			this.$emit('formData', formData);
		}
	}
};
</script>

<style lang="scss" scoped>
@import '@ast/style/public.scss';
.main {
	// @include flexBox();
	// flex-direction: column;
	// width: 100%;
	height: 100%;
	overflow: auto;
	padding: 0 8px 60px;
	.basic-info {
		// width: 100%;
		.form-title {
			background-color: #f8f8f8;
			// border-color: #e1e1e1;
			border: 1px solid #e1e1e1;
			border-bottom: 0;
			font-size: 14px;
			font-weight: bold;
			color: #747474;
			display: flex;
			align-items: center;
			justify-content: center;
			height: 40px;
			width: 100%;
		}
		.is-table {
			padding: 12px 12px 24px;
		}
	}
	.card-change {
		position: relative;
		.btn-look {
			position: absolute;
			right: 0;
			top: 0;
		}
	}
}
::v-deep .upload-box {
	width: 100%;
	display: flex;
   align-items: center;
   justify-content: center;
	.el-button--medium {
		padding: 2px 6px;
		font-size: 12px;
	}
	.el-upload-list {
		// margin-top: -15px !important;
		.el-upload-list__item-name {
			width: auto;
			top: 0px;
			text-align: center;
			height: 37.7px;
			line-height: 39.7px;
		}
	}
	table {
		border-collapse: collapse; /* 合并单元格边框，避免双线 */
		border-spacing: 0; /* 当 border-collapse 不起作用时，可以尝试这个 */
	}

	table,
	th,
	td {
		border: none; /* 移除表格、表头和单元格的边框 */
	}
}
::v-deep .el-dialog__header {
	height: 44px;
}
::v-deep .es-form-content {
	padding: 0 !important;
}
::v-deep .es-table-form {
	width: 100%;
	.es-table-form-label {
		text-align: right;
		color: #747474;
		font-weight: 550;
	}
}
::v-deep .el-collapse-item__content {
	padding-bottom: 0px;
}
::v-deep .cell {
	color: #747474;
}
</style>
