<template>
	<div class="bodyBox">
		<div v-show="showECharts" style="width: 100%; height: 36%">
			<div class="echartsTitle">
				<h3>社团成员统计</h3>
			</div>
			<div id="societyStatsECharts" ></div>
		</div>
		<div class="content-box" style="width: 100%;">
			<es-data-table
				ref="table"
				:key="tableCount"
				:row-style="tableRowClassName"
				:full="true"
				:fit="true"
				:thead="thead"
				:toolbar="toolbar"
				:page="page"
				:url="dataTableUrl"
				:border="true"
				:numbers="true"
				form
			></es-data-table>
		</div>
	</div>
</template>

<script>
import interfaceUrl from '@/http/society/societyHome/stats.js';
import * as echarts from 'echarts';

const societyStatsOptions = {
	title: {
		// text: '社团成员统计'
	},
	tooltip: {
		trigger: 'axis'
	},
	legend: {
		data: [], //各数据组名称
		show: true,
		left: '85%'
	},
	grid: {
		left: '3%',
		right: '3%',
		top:'10%',
		bottom: '6%',
		containLabel: true
	},

	xAxis: {
		type: 'category',
		axisTick: {
			alignWithLabel: true
		},
		data: []
	},
	yAxis: {
		type: 'value'
	},
	series: {
		name: '社团成员数',
		type: 'bar',
		barWidth: '40%',
		data: [],
		itemStyle: {
			color: '#41a0f9',
			borderColor: '#ddeef6'
		}
	}
};

export default {
	data() {
		return {
			societyCharts: null,
			societyChartsData: null,
			showECharts: true, // 是否展示顶部图表
			//表格数据
			page: {
				pageSize: 10,
				totalCount: 0
			},
			tableCount: 1,
			dataTableUrl: interfaceUrl.societyList,
			thead: [
				{
					title: '社团名称',
					align: 'center',
					field: 'name',
					showOverflowTooltip: true
				},
				{
					title: '所属学院',
					align: 'center',
					field: 'collegeName',
					showOverflowTooltip: true
				},
				{
					title: '指导教师',
					width: '180px',
					align: 'center',
					field: 'teacherName',
					showOverflowTooltip: true
				},
				{
					title: '社长',
					width: '180px',
					align: 'center',
					field: 'principalName',
					showOverflowTooltip: true
				},
				{
					title: '社团简介',
					align: 'center',
					field: 'introduction',
					showOverflowTooltip: true
				},
				{
					title: '成员人数（人）',
					width: '140px',
					align: 'center',
					field: 'memberNum',
					showOverflowTooltip: true
				},
				{
					title: '创建时间',
					width: '200px',
					align: 'center',
					field: 'createTime',
					showOverflowTooltip: true
				}
			],
			toolbar: []
		};
	},
	created() {
		this.$nextTick(() => {
			this.init();
		});
	},
	mounted() {},
	methods: {
		/**
		 * 表格行高
		 */
		tableRowClassName() {
			return {
				height: '54px !important'
			};
		},
		init() {
			this.loadSocietyStatsECharts();
		},
		createSocietyStatsECharts(x, y) {
			if (this.societyCharts) {
				this.societyCharts.dispose();
			}
			let chartDom = document.getElementById('societyStatsECharts');
			if (!chartDom) {
				return;
			}
			this.societyCharts = echarts.init(chartDom);
			let options = societyStatsOptions;
			options.xAxis.data = x;
			options.series.data = y;
			this.societyCharts.setOption(options);
			window.addEventListener('resize', () => {
				this.societyCharts && this.societyCharts.resize();
			});
		},
		loadSocietyStatsECharts() {
			this.$request({ url: interfaceUrl.ecDataStats, method: 'GET' }).then(res => {
				if (res.rCode !== 0) {
					return;
				}
				let societyName = res.results.xAxis || [];
				let memberNum = res.results.memberNums || [];
				let chartDom = document.getElementsByClassName('content-box')[0];
				if (societyName.length > 0 || memberNum.length > 0) {
					this.showECharts = true;
					chartDom.style = "height: 64%";
				}else{
					this.showECharts = false;
					chartDom.style = "height: 100%";
					return 
				}
				this.createSocietyStatsECharts(societyName, memberNum);
				this.societyChartsData = {
					societyName: societyName,
					memberNum: memberNum
				};
			});
		}
	}
};
</script>

<style scoped lang="scss">
.bodyBox {
	width: 100%;
	height: 100%;
	padding: 10px;
	overflow: auto;
}
.bodyBox > div {
	padding-top: 10px;
	padding-bottom: 10px;
	// border-bottom: 1px solid grey;
}

.bodyBox > div:nth-child(3) {
	border-bottom: none;
}

#societyStatsECharts {
	width: 100%;
	height:calc(100% - 20%);
}

.echartsTitle {
	display: flex;
	flex-direction: row;
	align-items: center;
}
.echartsTitle > h3 {
	margin-right: 10px;
}
</style>
