<template>
	<div class="content" style="height: 100%">
		<div style="width: 100%; height: 100%">
			<es-data-table
				v-if="true"
				ref="table"
				:key="tableCount"
				:row-style="tableRowClassName"
				:full="true"
				:fit="true"
				:thead="thead"
				:toolbar="toolbar"
				:page="page"
				:url="dataTableUrl"
				:param="dataTableParam"
				:border="true"
				:numbers="true"
				:option-data="optionData"
				checkbox
				form
				@btnClick="btnClick"
				@selection-change="handleSelectionChange"
				@edit="changeTable"
			></es-data-table>
			<el-dialog
				v-if="showInfoPage"
				:title="formTitle"
				:visible.sync="showInfoPage"
				append-to-body
				height="auto"
				width="70%"
			>
				<div style="height: 80vh">
					<infoPage
						ref="infoPage"
						:base-data="formData"
						:info-page-mode="infoPageMode"
						@activelyClose="closeInfoPage"
					></infoPage>
				</div>
			</el-dialog>
		</div>
	</div>
</template>

<script>
import api from '@/http/job/jobServiceType/api';
import InfoPage from '@/views/job/jobServiceType/infoPage.vue';
import { fileAccess } from '../../../../config/config';
import SnowflakeId from 'snowflake-id';

export default {
	components: { InfoPage },
	data() {
		return {
			formData: {},
			page: {
				pageSize: 20,
				totalCount: 0
			},
			infoPageMode: 'allOn',
			selectRowData: [],
			selectRowIds: [],
			showInfoPage: false,
			tableCount: 1,
			dialogType: '',
			dataTableUrl: api.jobServiceTypeListJson,
			dataTableParam: {},
			formTitle: '',
			validityOfDateDisable: false,
			thead: [],
			toolbar: [],
			editToolbar: [
				{
					type: 'button',
					contents: [
						{
							text: '查看',
							code: 'toolbar',
							type: 'primary'
						},
						{
							text: '删除',
							code: 'toolbar',
							type: 'danger'
						},
						{
							text: '新增',
							code: 'toolbar'
						}
					]
				},
				{
					type: 'search',
					reset: true,
					contents: [
						{
							type: 'text',
							name: 'keyword',
							placeholder: '关键字查询',
							clearable: true
						},
						{
							name: 'type',
							type: 'select',
							label: '就业或创业',
							placeholder: '就业或创业',
							col: 3,
							clearable: true,
							data: [
								{ pid: '0', value: '0', label: '创业' },
								{ pid: '1', value: '1', label: '就业' }
							]
						}
						// {
						//   type: 'date',
						//   col: 6,
						//   name: 'startDate',
						//   label: '申请时间(开始)',
						//   placeholder: '申请时间(开始)'
						// },
						// {
						//   type: 'date',
						//   col: 6,
						//   name: 'endDate',
						//   label: '申请时间(结束)',
						//   placeholder: '申请时间(结束)'
						// }
					]
				}
			],
			listThead: [
				{
					title: '分类名称',
					width: '130px',
					align: 'center',
					field: 'name',
					sortable: 'custom',
					showOverflowTooltip: true
				},
				{
					title: '创业/就业',
					width: '130px',
					align: 'center',
					field: 'typeName',
					sortable: 'custom',
					showOverflowTooltip: true
				},
				{
					title: '简介',
					align: 'left',
					field: 'introduction',
					sortable: 'custom',
					showOverflowTooltip: true
				},
				{
					title: '创建时间',
					width: '150px',
					align: 'center',
					field: 'createTime',
					sortable: 'custom',
					showOverflowTooltip: true
				},
				{
					title: '状态',
					width: '100px',
					field: 'status',
					align: 'center',
					type: 'switch'
				}
			],
			btnJson: {
				title: '操作',
				type: 'handle',
				width: 180,
				template: '',
				events: [
					{
						code: 'row',
						text: '查看'
					},
					// {
					//   code: 'row',
					//   text: '审核',
					//   rules: rows => {
					//     return rows.auditStatus === 0;
					//   }
					// },
					{
						code: 'row',
						text: '编辑'
					},
					{
						code: 'row',
						text: '删除'
					}
				]
			},
			optionData: {
				status: [
					{
						value: 1,
						name: '启用'
					},
					{
						value: 0,
						name: '禁用'
					}
				]
			}
		};
	},
	created() {
		//初始化查询待审核列表
		this.thead = this.getListThead(this.btnJson);
		this.toolbar = this.editToolbar;
	},
	methods: {
		/**
		 * 表格行高
		 */
		tableRowClassName() {
			return {
				height: '54px !important'
			};
		},
		btnClick(res) {
			let text = res.handle.text;
			let code = res.handle.code;

			if (code === 'row') {
				switch (text) {
					case '查看':
					case '审核':
					case '编辑':
						this.openInfoPage(res.row.id, text);
						break;
					case '删除':
						this.deleteRows([res.row.id]);
						break;
				}
			} else {
				switch (text) {
					case '新增':
						this.openInfoPage(null, text);
						break;
					case '查看':
						if (this.selectRowIds.length > 1) {
							this.$message.warning('只能选择一个查看');
						} else if (this.selectRowIds.length < 1) {
							this.$message.warning('请选择一个进行查看');
						} else {
							this.openInfoPage(this.selectRowIds[0], text);
						}
						break;
					case '删除':
						if (this.selectRowIds.length < 1) {
							this.$message.warning('请选择一个删除');
						} else {
							this.deleteRows(this.selectRowIds);
						}
						break;
				}
			}
		},
		//打开infoPage
		openInfoPage(id, pageMode) {
			if (pageMode !== '新增') {
				this.$request({
					url: api.jobServiceTypeInfo,
					params: { id: id },
					method: 'GET'
				}).then(res => {
					//处理返回数据
					this.formData = { ...res.results };
					this.formData.status = this.formData.status === 1;
					if (this.formData.typeId != null && this.formData.typeId !== '')
						this.formData.typeId = { value: this.formData.typeId, label: this.formData.typeName };
					// 生成随机数避免相同路径图片不重新渲染
					const v = Math.random().toString(36).substring(2);
					this.formData.iconUrl = `${fileAccess + this.formData.icon}&v=${v}`;
					this.formData.coverUrl = `${fileAccess + this.formData.cover}&v=${v}`;
					this.formTitle = pageMode;
					this.infoPageMode = pageMode;
					this.showInfoPage = true;
				});
			} else {
				//初始化表单数据
				const snowflake = new SnowflakeId();
				this.formData = { id: snowflake.generate(), status: false, auditStatus: 0 };

				this.formTitle = pageMode;
				this.infoPageMode = pageMode;
				this.showInfoPage = true;
			}
		},
		//关闭infoPage
		closeInfoPage(reload) {
			this.formData = {};
			this.showInfoPage = false;
			if (reload) this.$refs.table.reload();
		},
		// 数据列表多选回调
		handleSelectionChange(data) {
			let ids = [];
			data.forEach(row => {
				ids.push(row.id);
			});
			this.selectRowData = data;
			this.selectRowIds = ids;
		},
		deleteRows(ids) {
			this.$confirm('确定要删除选中的数据吗？', '删除', {
				confirmButtonText: '确定',
				cancelButtonText: '取消',
				type: 'warning'
			})
				.then(() => {
					this.$request({
						url: api.jobServiceTypeDeleteByIds,
						data: { ids: ids.join(',') },
						method: 'POST'
					}).then(res => {
						if (res.rCode === 0) {
							this.$message.success('操作完成');
							this.$refs.table.reload();
						} else {
							this.$message.error(res.msg);
						}
					});
				})
				.catch(() => {});
		},
		getListThead(btnJson) {
			let tempThead = [...this.listThead];
			tempThead.push(btnJson);
			return tempThead;
		},
		changeTable(val) {
			switch (val.name) {
				case 'status':
					this.changeStatus(val.data.id);
					break;
			}
		},
		//改变数据启用状态
		changeStatus(id) {
			this.$request({
				url: api.jobServiceTypeChangeStatus,
				data: { id: id },
				method: 'POST'
			}).then(res => {
				if (res.success) {
					this.$message.success('修改成功');
				} else {
					this.$message.error(res.msg);
				}
			});
		}
	}
};
</script>
