import httpApi from './api.js';
export default {
	computed: {
		component() {
			return {
				// 纯列表结构
				name: 'es-data-table'
			};
		},
		treeParam() {
			return {};
		},
		table() {
			return {
				url: httpApi.labReformNotifyList,
				param: { handlerType: '' },
				full: true,
				fit: true,
				page: true,
				stripe: true,
				numbers: true,
				'row-key': 'id',
				toolbar: [
					{
						type: 'button',
						contents: [
							{
								text: '新增',
								type: 'primary'
							}
						]
					},
					{
						type: 'search',
						reset: true,
						contents: [
							{
								type: 'select',
								col: 6,
								label: '二级学院',
								placeholder: '二级学院',
								name: 'college',
								filterable: true,
								'value-key': 'value',
								'label-key': 'label',
								clearable: true,
								data: this.collegeList
							},
							{
								name: 'title',
								placeholder: '标题',
								label: '标题',
								col: 6
							},
							{
								type: 'select',
								col: 6,
								label: '整改状态',
								placeholder: '整改状态',
								name: 'reformStatus',
								sysCode: 'lab_reform_status',
								clearable: true
							}
						]
					}
				],
				thead: [
					{
						title: '二级学院',
						align: 'left',
						field: 'collegeName',
						width: 240
					},
					{
						title: '标题',
						align: 'left',
						field: 'title'
					},
					{
						title: '整改状态',
						align: 'left',
						field: 'reformStatus',
						sysCode: 'lab_reform_status',
						width: 100
					},
					{
						title: '操作',
						type: 'handle',
						width: 180,
						events: [
							{
								text: '查看'
							},
							{
								text: '编辑',
								rules: rows => {
									return rows.reformStatus === '0';
								}
							},
							{
								text: '删除',
								rules: rows => {
									return rows.reformStatus === '0';
								}
							}
						]
					}
				]
			};
		}
	}
};
