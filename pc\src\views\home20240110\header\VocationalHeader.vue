<template>
	<div class="navbar">
		<img src="@/assets/images/serveHall/vocational-logo.png" class="navbar-left" @click="goHome" />

		<div class="navbar-right">
			<headAvator
				:own-id="_userinfo.id"
				:user-id="_userinfo.id"
				class="navbar-right-avator"
				@click="handleButton('draw')"
			/>
			<div class="navbar-right-name" @click="handleButton('draw')">
				<div>{{ _userinfo.username }}</div>
				<div>{{ _userinfo.phone }}</div>
			</div>
			<!-- <HeaderDraw v-model="showMore" offset-top="60px" @jumpLink="toRoute"></HeaderDraw> -->
			<div v-for="(item, index) of list" :key="index" class="navbar-right-list">
				<!-- <img
					class="img"
					:style="index === 0 ? 'width:12px;height: 12px;' : ''"
					:src="item.img"
					alt=""
					@click="handleButton(item.type)"
				/> -->
				<div class="line"></div>
				<img class="img" :src="item.img" alt="" @click="handleButton(item.type)" />
				<!-- <div v-if="index !== list.length - 1" class="line"></div> -->
				<!-- <div class="line"></div> -->
			</div>
		</div>
	</div>
</template>

<script>
import { mapMutations } from 'vuex';
// import HeaderDraw from './HeaderDraw.vue';
import headAvator from './headAvator.vue';
import { alumniUrl } from '@/config';
// import headAvatorVue from './headAvator.vue';
import { logout } from '@/utils/index.js';

export default {
	components: {
		// HeaderDraw,
		headAvator
	},
	data() {
		return {
			_userinfo: {},
			list: [
				// { img: require('@/assets/images/home/<USER>') },
				{ img: require('@/assets/images/home/<USER>'), type: 'home' },
				{ img: require('@/assets/images/home/<USER>') },
				{ img: require('@/assets/images/home/<USER>') },
				{ img: require('@/assets/images/home/<USER>') },
				{ img: require('@/assets/images/home/<USER>') },
				{ img: require('@/assets/images/home/<USER>'), type: 'open' }
			],
			showMore: false
		};
	},
	created() {
		let value = localStorage.getItem('userInfo');
		let parsedValue = JSON.parse(value);
		this._userinfo = parsedValue;
	},
	methods: {
		...mapMutations('user', ['SET_IDENTITY', 'SET_ROLES', 'SET_USER_INFO']),
		/**返回首页*/
		goHome() {
			// this.$router.push('/home');
			// window.open(`${alumniUrl}/project-ybzy/ybzy_zhxy/index.html#/home`, '_parent');
		},
		/**点击更多里面的内容*/
		toRoute(tab, i) {
			// 如果更新地址，就往路由历史加一个对象
			if (tab.path) {
				this.$router.push(tab.path);
				//this.addRouteHistory(tab); // 往路由历史添加信息
			} else {
				this.$message.warning('开发中，敬请期待...');
			}
		},

		/**处理导航栏按钮点击*/
		handleButton(type) {
			if (type === 'open') {
				this.$confirm('确认退出系统吗？', '提示', {
					confirmButtonText: '确定',
					cancelButtonText: '取消',
					type: 'warning'
				})
					.then(() => {
						logout();
					})
					.catch(() => {});
			} else if (type === 'home') {
				// this.$router.push('/home');
				window.open(`${alumniUrl}/project-ybzy/ybzy_zhxy/index.html#/home`, '_parent');
			} else if (type === 'draw') {
				this.showMore = !this.showMore;
			}
		}
	}
};
</script>

<style lang="scss" scoped>
.navbar {
	height: 60px;
	background: var(--brand-6, #0076e8);
	padding: 0 20px;
	display: flex;
	align-items: center;
	justify-content: space-between;
	&-left {
		height: 37px;
		display: flex;
		align-items: center;
		cursor: pointer;
		&-icon {
			width: 93px;
			height: 34px;
			margin-right: 58px;
		}
		&-tabs {
			display: flex;
			align-items: center;
			font-size: 16px;
			font-family: Microsoft YaHei;
			font-weight: 400;
			color: #ffffff;
			.tab {
				margin-right: 58px;
				position: relative;
				cursor: pointer;
				&-line {
					width: 20px;
					height: 3px;
					background: #eaff00;
					border-radius: 2px;
					position: absolute;
					bottom: -8px;
					left: calc(50% - 10px);
				}
			}
			.select {
				color: #eaff00;
			}
		}
	}
	&-right {
		display: flex;
		align-items: center;
		&-avator {
			width: 30px;
			height: 30px;
			border-radius: 50%;
			margin-right: 9px;
			background: #ffffff;
			cursor: pointer;
			.img {
				width: 100%;
				height: 100%;
			}
		}
		&-name {
			margin-right: 12px;
			font-size: 12px;
			font-family: Microsoft YaHei;
			font-weight: 400;
			color: #ffffff;
			line-height: 20px;
			cursor: pointer;
		}
		&-list {
			display: flex;
			align-items: center;
			.line {
				width: 1px;
				height: 16px;
				background: #000000;
				opacity: 0.2;
				margin: 0 22px;
			}
			.img {
				width: 14px;
				height: 14px;
				width: 21px;
				height: 21px;
				cursor: pointer;
			}
		}
	}
}
.dialog-footer {
	display: flex;
	text-align: right;
	width: 100%;
	height: 100px;
	display: block;
	padding: 50px 20px 0 0;
}
</style>
