<template>
	<div class="time-select-more">
		<el-input class="input-box" readonly :placeholder="tags.length > 0 ? '' : '请添加时间'">
			<el-button
				v-if="!readonly"
				slot="append"
				icon="el-icon-circle-plus-outline"
				@click="showDelete = true"
			></el-button>
		</el-input>
		<div class="time-list">
			<el-tag
				v-for="(tag, i) in tags"
				:key="tag"
				:closable="!readonly"
				size="small"
				type="info"
				@close="closeIcon(tag, i)"
			>
				{{ tag }}
			</el-tag>
		</div>
		<es-dialog
			title="添加时间"
			:visible.sync="showDelete"
			width="500px"
			:close-on-click-modal="false"
			:middle="true"
			height="120px"
		>
			<div><TimeSelect :default-time="defaultTime" @changeStr="changeStr" /></div>
			<!-- <el-button type="primary" class="btn-box" size="small">确认</el-button> -->
		</es-dialog>
	</div>
</template>

<script>
import TimeSelect from '@/components/time-select.vue';

export default {
	components: {
		TimeSelect
	},
	props: {
		readonly: {
			type: Boolean,
			default: false
		},
		defaultTime: {
			// 初始化值
			type: Object,
			default: () => {
				return {
					start: '08:40',
					end: '21:00'
				};
			}
		},
		initData: {
			type: Array,
			default: () => {
				return [];
			}
		}
	},
	data() {
		return {
			showDelete: false,
			tags: []
		};
	},
	watch: {
		initData: {
			handler(val) {
				this.tags = val;
			},
			immediate: true,
			deep: true
		}
	},
	methods: {
		closeIcon(tag, i) {
			this.tags.splice(i, 1);
			this.$emit('change', this.tags);
		},
		changeStr(val) {
			this.tags.push(val);
			this.$emit('change', this.tags);
			this.showDelete = false;
		}
	}
};
</script>
<style lang="scss" scoped>
.time-select-more {
	width: 100%;
	.input-box {
		width: 100%;
	}
	position: relative;
	.time-list {
		position: absolute;
		top: 0;
		left: 0;
		bottom: 0;
		right: 58px;
		overflow: auto;
		margin: 0 2px;
	}
}
.btn-box {
	margin-top: 50px;
	margin-left: 91%;
}
</style>
