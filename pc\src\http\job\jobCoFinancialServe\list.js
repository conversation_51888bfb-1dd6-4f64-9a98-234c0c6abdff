import httpApi from '@/http/job/jobCoFinancialServe/api.js';
export default {
	computed: {
		component() {
			return {
				// 纯列表结构
				name: 'es-data-table'
			};
		},
		treeParam() {
			return {
				
			};
		},
		table() {
			return {
				url: httpApi.jobCoFinancialServeList,
				param: { handlerType: '' },
				full: true,
				fit: true,
				page: true,
				stripe: true,
				numbers: true,
				checkbox: true,
				'row-key': 'id',
				toolbar: [
					{
						type: 'button',
						contents: [
							{
								text: '新增',
								type: '',
								code: 'toolbar',
							},
							{
								text: '删除',
								type: 'danger',
								code: 'toolbar',
							},
							{
								text: '查看',
								type: 'primary',
								code: 'toolbar',
							},
						]
					},
					{
						type: 'search',
						reset: true,
						contents: [
							//{
							//	name: 'accountName_like',
							//	placeholder: '配置名称',
							//	label: '配置名称',
							//	col: 6
							//},
							{
								col: 6,
								name: 'name',
								label: '产品名称',
								placeholder: '产品名称',
							},
							{
								type: 'select',
								col: 6,
								name: 'auditStatus',
								label: '产品状态',
								placeholder: '产品状态',
								data: [
									{ value: 0, label: '待审核'},
									{ value: 1, label: '已通过'},
									{ value: 2, label: '已驳回'},
								]
							},
							{
								col: 6,
								name: 'startDate',
								label: '创建时间(开始)',
								placeholder: '创建时间(开始)'
							},
							{
								type: 'date',
								col: 6,
								name: 'endDate',
								label: '创建时间(结束)',
								placeholder: '创建时间(结束)'
							}
						]
					}
				],
				thead: [
					{
						title: '金融机构',
						align: 'left',
						field: 'orgNameVO',
					},
					{
						title: '产品名称',
						align: 'left',
						field: 'productName'
					},
					{
						title: '适用主体',
						align: 'left',
						field: 'applicableEntityVO'
					},
					{
						title: '产品分类',
						align: 'center',
						field: 'productType'
					},
					{
						title: '融资额度(万)',
						align: 'center',
						field: 'financingAmountVO'
					},
					{
						title: '融资期限(月)',
						align: 'center',
						field: 'financingPeriodVO'
					},
					{
						title: '参考年利率(%)',
						align: 'center',
						field: 'annualRateVO'
					},
					{
						title: '状态',
						align: 'center',
						field: 'auditStatusVO',
						render: (h, param) => {
							return h(
								'el-tag',
								{props:{type: param.row.auditStatusVO === '已驳回'? 'danger':''}},
								param.row.auditStatusVO
							)
						}
					},
					{
						title: '启用状态',
						align: 'center',
						field: 'status',
						type: 'switch',
					},
					{
						title: '操作',
						type: 'handle',
						width: 180,
						events: [
							{
								text: '查看',
								code: 'row',
							},
							{
								text: '审核',
								code: 'row',
								rules: rows => {
									return rows.auditStatusVO === '待审核';
								}
							},
							{
								text: '编辑',
								code: 'row',
							},
							{
								text: '删除',
								code: 'row',
							}
						]
					}
				],
				optionData: {
					status: [
						{
							value: 1,
							name: '启用'
						},
						{
							value: 0,
							name: '禁用'
						}
					]
				},
			};
		}
	},
	methods: {
		changeTable(res){
			switch (res.name){
				case 'status': this.changeStatus(res.data.id); break;
			}
		},
		//改变数据启用状态
		changeStatus(id) {
			this.$request({
				url: httpApi.jobCoFinancialServeChangeStatus,
				data: {id: id},
				method: 'POST'
			}).then(res=>{
				if(res.success){
					this.$message.success('修改成功');
				}else{
					this.$message.error(res.msg);
				}
			});
		}
	}
};
