const api = {
	categoryList: '/ybzy/hqbasematerialcategory/listJson', // 分类列表获取
	categoryDel: '/ybzy/hqbasematerialcategory/deleteById', // 分类删除
	categoryUpdateStatus: '/ybzy/hqbasematerialcategory/updateStatus', // 分类状态更新
	info: '/ybzy/hqbasematerialcategory', //分类
	save: '/ybzy/hqbasematerialcategory/save', //分类新增
	update: '/ybzy/hqbasematerialcategory/update', //分类编辑
	categoryListSel: '/ybzy/hqbasematerialcategory/list', // 分类下拉列表
	firstCategoryList: '/ybzy/hqbasematerialcategory/selectFirstCategory', // 分类下拉列表
	categoryTree: '/ybzy/hqbasematerialcategory/getTreeListOne', // 原料分类树
	getListAllKeyValue: '/ybzy/hqbasematerialcategory/getListAllKeyValue', // 工会列表树
};
export default api;