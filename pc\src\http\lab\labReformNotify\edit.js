export default {
	computed: {
		formItemList() {
			return [
				{
					type: 'select',
					name: 'college',
					label: '二级学院',
					filterable: true,
					'value-key': 'value',
					'label-key': 'label',
					clearable: true,
					col: 12,
					rules: {
						required: true,
						message: '请选择二级学院'
					},
					data: this.collegeList
				},
				{
					name: 'title',
					label: '标题',
					value: '',
					col: 12,
					rules: {
						required: true,
						message: '请输入标题'
					}
				},
				{
					name: 'depict',
					label: '描述',
					value: '',
					col: 12,
					rules: {
						required: true,
						message: '请输入描述'
					}
				},
				{
					label: '整改报告文件',
					type: 'attachment',
					code: 'lab_room_reform_file',
					ownId: this.formData.id, // 业务id
					dragSort: true,
					limit: 8,
					col: 12
				}
			];
		}
	}
};
