<template>
	<div class="app-container">
		<el-form
			v-show="showSearch"
			ref="queryForm"
			:model="queryParams"
			size="small"
			:inline="true"
			label-width="68px"
		>
			<el-form-item label="姓名" prop="name">
				<el-input
					v-model="queryParams.name"
					placeholder="请输入姓名"
					clearable
					@keyup.enter.native="handleQuery"
				/>
			</el-form-item>
			<el-form-item label="职称:医生、护士" prop="zhicheng">
				<el-input
					v-model="queryParams.zhicheng"
					placeholder="请输入职称:医生、护士"
					clearable
					@keyup.enter.native="handleQuery"
				/>
			</el-form-item>
			<el-form-item label="工龄" prop="lengthService">
				<el-input
					v-model="queryParams.lengthService"
					placeholder="请输入工龄"
					clearable
					@keyup.enter.native="handleQuery"
				/>
			</el-form-item>
			<el-form-item label="所属单位" prop="affiliatedUint">
				<el-input
					v-model="queryParams.affiliatedUint"
					placeholder="请输入所属单位"
					clearable
					@keyup.enter.native="handleQuery"
				/>
			</el-form-item>
			<el-form-item>
				<el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">
					搜索
				</el-button>
				<el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
			</el-form-item>
		</el-form>

		<el-row :gutter="10" class="mb8">
			<el-col :span="1.5">
				<el-button
					v-hasPermi="['big:yjMedicalForce:add']"
					type="primary"
					plain
					icon="el-icon-plus"
					size="mini"
					@click="handleAdd"
				>
					新增
				</el-button>
			</el-col>
			<el-col :span="1.5">
				<el-button
					v-hasPermi="['big:yjMedicalForce:edit']"
					type="success"
					plain
					icon="el-icon-edit"
					size="mini"
					:disabled="single"
					@click="handleUpdate"
				>
					修改
				</el-button>
			</el-col>
			<el-col :span="1.5">
				<el-button
					v-hasPermi="['big:yjMedicalForce:remove']"
					type="danger"
					plain
					icon="el-icon-delete"
					size="mini"
					:disabled="multiple"
					@click="handleDelete"
				>
					删除
				</el-button>
			</el-col>
			<el-col :span="1.5">
				<el-button
					v-hasPermi="['big:yjMedicalForce:export']"
					type="warning"
					plain
					icon="el-icon-download"
					size="mini"
					@click="handleExport"
				>
					导出
				</el-button>
			</el-col>
			<right-toolbar :show-search.sync="showSearch" @queryTable="getList"></right-toolbar>
		</el-row>

		<el-table
			v-loading="loading"
			:data="yjMedicalForceList"
			@selection-change="handleSelectionChange"
		>
			<el-table-column type="selection" width="55" align="center" />
			<el-table-column label="主键" align="center" prop="id" />
			<el-table-column label="姓名" align="center" prop="name" />
			<el-table-column label="职称:医生、护士" align="center" prop="zhicheng" />
			<el-table-column label="工龄" align="center" prop="lengthService" />
			<el-table-column label="所属单位" align="center" prop="affiliatedUint" />
			<el-table-column label="备注" align="center" prop="remark" />
			<el-table-column label="操作" align="center" class-name="small-padding fixed-width">
				<template slot-scope="scope">
					<el-button
						v-hasPermi="['big:yjMedicalForce:edit']"
						size="mini"
						type="text"
						icon="el-icon-edit"
						@click="handleUpdate(scope.row)"
					>
						修改
					</el-button>
					<el-button
						v-hasPermi="['big:yjMedicalForce:remove']"
						size="mini"
						type="text"
						icon="el-icon-delete"
						@click="handleDelete(scope.row)"
					>
						删除
					</el-button>
				</template>
			</el-table-column>
		</el-table>

		<pagination
			v-show="total > 0"
			:total="total"
			:page.sync="queryParams.pageNum"
			:limit.sync="queryParams.pageSize"
			@pagination="getList"
		/>

		<!-- 添加或修改医疗力量对话框 -->
		<el-dialog :title="title" :visible.sync="open" width="500px" append-to-body>
			<el-form ref="form" :model="form" :rules="rules" label-width="80px">
				<el-form-item label="姓名" prop="name">
					<el-input v-model="form.name" placeholder="请输入姓名" />
				</el-form-item>
				<el-form-item label="职称:医生、护士" prop="zhicheng">
					<el-input v-model="form.zhicheng" placeholder="请输入职称:医生、护士" />
				</el-form-item>
				<el-form-item label="工龄" prop="lengthService">
					<el-input v-model="form.lengthService" placeholder="请输入工龄" />
				</el-form-item>
				<el-form-item label="所属单位" prop="affiliatedUint">
					<el-input v-model="form.affiliatedUint" placeholder="请输入所属单位" />
				</el-form-item>
				<el-form-item label="备注" prop="remark">
					<el-input v-model="form.remark" type="textarea" placeholder="请输入内容" />
				</el-form-item>
			</el-form>
			<div slot="footer" class="dialog-footer">
				<el-button type="primary" @click="submitForm">确 定</el-button>
				<el-button @click="cancel">取 消</el-button>
			</div>
		</el-dialog>
	</div>
</template>

<script>
import {
	listYjMedicalForce,
	getYjMedicalForce,
	delYjMedicalForce,
	addYjMedicalForce,
	updateYjMedicalForce
} from '@/http/screen/yjMedicalForce';

export default {
	name: 'YjMedicalForce',
	data() {
		return {
			// 遮罩层
			loading: true,
			// 选中数组
			ids: [],
			// 非单个禁用
			single: true,
			// 非多个禁用
			multiple: true,
			// 显示搜索条件
			showSearch: true,
			// 总条数
			total: 0,
			// 医疗力量表格数据
			yjMedicalForceList: [],
			// 弹出层标题
			title: '',
			// 是否显示弹出层
			open: false,
			// 查询参数
			queryParams: {
				pageNum: 1,
				pageSize: 10,
				name: null,
				zhicheng: null,
				lengthService: null,
				affiliatedUint: null
			},
			// 表单参数
			form: {},
			// 表单校验
			rules: {}
		};
	},
	created() {
		this.getList();
	},
	methods: {
		/** 查询医疗力量列表 */
		getList() {
			this.loading = true;
			listYjMedicalForce(this.queryParams).then(response => {
				this.yjMedicalForceList = response.rows;
				this.total = response.total;
				this.loading = false;
			});
		},
		// 取消按钮
		cancel() {
			this.open = false;
			this.reset();
		},
		// 表单重置
		reset() {
			this.form = {
				id: null,
				name: null,
				zhicheng: null,
				lengthService: null,
				affiliatedUint: null,
				remark: null
			};
			this.resetForm('form');
		},
		/** 搜索按钮操作 */
		handleQuery() {
			this.queryParams.pageNum = 1;
			this.getList();
		},
		/** 重置按钮操作 */
		resetQuery() {
			this.resetForm('queryForm');
			this.handleQuery();
		},
		// 多选框选中数据
		handleSelectionChange(selection) {
			this.ids = selection.map(item => item.id);
			this.single = selection.length !== 1;
			this.multiple = !selection.length;
		},
		/** 新增按钮操作 */
		handleAdd() {
			this.reset();
			this.open = true;
			this.title = '添加医疗力量';
		},
		/** 修改按钮操作 */
		handleUpdate(row) {
			this.reset();
			const id = row.id || this.ids;
			getYjMedicalForce(id).then(response => {
				this.form = response.data;
				this.open = true;
				this.title = '修改医疗力量';
			});
		},
		/** 提交按钮 */
		submitForm() {
			this.$refs['form'].validate(valid => {
				if (valid) {
					if (this.form.id != null) {
						updateYjMedicalForce(this.form).then(response => {
							this.$modal.msgSuccess('修改成功');
							this.open = false;
							this.getList();
						});
					} else {
						addYjMedicalForce(this.form).then(response => {
							this.$modal.msgSuccess('新增成功');
							this.open = false;
							this.getList();
						});
					}
				}
			});
		},
		/** 删除按钮操作 */
		handleDelete(row) {
			const ids = row.id || this.ids;
			this.$modal
				.confirm('是否确认删除医疗力量编号为"' + ids + '"的数据项？')
				.then(function () {
					return delYjMedicalForce(ids);
				})
				.then(() => {
					this.getList();
					this.$modal.msgSuccess('删除成功');
				})
				.catch(() => {});
		},
		/** 导出按钮操作 */
		handleExport() {
			this.download(
				'big/yjMedicalForce/export',
				{
					...this.queryParams
				},
				`yjMedicalForce_${new Date().getTime()}.xlsx`
			);
		}
	}
};
</script>
