<template>
	<es-form
		ref="form"
		:model="formData1"
		:contents="formList1"
		:submit="type === 'audit' || type === 'view'"
		:readonly="isReadOnly"
		v-bind="$attrs"
		height="100%"
		:genre="2"
		collapse
		@submit="handleSubmit"
		@reset="handleCancel"
	></es-form>
</template>
<script>
import interfaceUrl from '@/http/platform/enterprise.js';
import $ from 'eoss-ui/lib/utils/util';
import request from 'eoss-ui/lib/utils/http';
import SnowflakeId from 'snowflake-id';

export default {
	name: 'Audit',
	props: {
    title:{
      type: String
    },

		// 上级编码ID
		cciPid: {
			type: String
		},
		selectInfo: {
			type: String
		},
		type: {
			type: String
		},
		isSystemReo: {
			//true招就处，false二级学院
			type: Boolean,
			default: false
		},
		isReadOnly: {
			type: Boolean,
			default: false
		},
		formInfo: {
			type: Object,
			default: () => {}
		},
		regionData: {
			type: Array,
			default: () => []
		}
	},
	data() {
		return {
			//regionData:[],
			typeDicData: [],
			formData1: {
				...this.formInfo
			},
			viewBtn: {
				type: 'submit',
				skin: 'lay-form-btns',
				contents: [
					{
						type: 'reset',
						text: '关闭',
						event: 'cancel'
					}
				]
			}
		};
	},
	computed: {
		auditBtn() {
			return {
				type: 'submit',
				skin: 'lay-form-btns',
				contents: [
					{
						type: 'primary',
						text: this.isSystemReo ? '招就处审核通过' : '学院审核通过',
						code: this.isSystemReo ? 'zjc' : 'xy',
						event: 'confirm'
					},
					{
						type: 'primary',
						text: '审核不通过',
						code: 'pass',
						event: 'confirm'
					},
					{
						type: 'reset',
						text: '取消',
						event: 'cancel'
					}
				]
			};
		},
		formList1() {
			//debugger;
			let fixFormList = [
				{
					title: '企业工商信息',
					contents: [
						{
							label: '企业性质',
							name: 'corpType',
							type: 'select',
							placeholder: '请选择企业性质',
							readonly: true,
							rules: {
								required: false,
								message: '请选择企业性质',
								trigger: 'blur'
							},
							verify: 'required',
							col: 5,
							sysCode: 'plat_enp_quality',
							'label-key': 'shortName',
							'value-key': 'cciValue'
						},
						{
							label: '企业规模',
							name: 'corpScale',
							placeholder: '请选择企业规模',
							readonly: true,
							type: 'select',
							controls: false,
							rules: {
								required: false,
								message: '请选择企业规模',
								trigger: 'blur'
							},
							verify: 'required',
							col: 5,
							sysCode: 'plat_enp_scale',
							'label-key': 'shortName',
							'value-key': 'cciValue'
						},
						{
							label: '所属行业',
							name: 'corpIndustry',
							placeholder: '请选择所属行业',
							type: 'select',
							readonly: true,
							rules: {
								required: false,
								message: '请选择所属行业',
								trigger: 'blur'
							},
							verify: 'required',
							col: 10,
							sysCode: 'plat_enp_industry',
							'label-key': 'shortName',
							'value-key': 'cciValue'
						},
						{
							name: 'fj',
							label: '营业执照',
							type: 'attachment',
							value: '',
							readonly: true,
							code: 'plat_enterprise_validity',
							preview: true,
							listType: 'picture-card',
							// onPreview: res => {
							// console.log(res);
							// },
							ownId: this.formData1.id // 业务id
							//rules: {
							//required: true,
							//    message: '请上传营业执照',
							//    trigger: 'blur'
							//}
						},
						{
							label: '企业名称',
							name: 'corpName',
							placeholder: '请输入企业名称',
							readonly: true,
							rules: {
								required: false,
								message: '请输入企业名称',
								trigger: 'blur'
							},
							verify: 'required',
							col: 5
						},
						{
							label: '统一社会信用代码',
							name: 'socialCode',
							placeholder: '请输入统一社会信用代码',
							readonly: true,
							rules: {
								required: false,
								message: '请输入统一社会信用代码',
								trigger: 'blur'
							},
							verify: 'required',
							col: 5
						},
						{
							label: '企业成立日期',
							name: 'establishDate',
							placeholder: '请输入企业成立日期',
							readonly: true,
							type: 'date',
							rules: {
								required: false,
								message: '请输入企业成立日期',
								trigger: 'blur'
							},
							verify: 'required',
							col: 5
						},
						{
							label: '注册资本(万元)',
							name: 'registeredCapital',
							placeholder: '请输入注册资本',
							readonly: true,
							rules: {
								required: false,
								message: '请输入注册资本',
								trigger: 'blur'
							},
							verify: 'required',
							col: 5
						},
						{
							label: '法定代表人',
							name: 'lawPerson',
							placeholder: '请输入法定代表人',
							readonly: true,
							rules: {
								required: false,
								message: '请输入法定代表人',
								trigger: 'blur'
							},
							verify: 'required',
							col: 5
						},
						{
							label: '企业注册地址',
							name: 'registeredAddress',
							placeholder: '请输入企业注册地址',
							readonly: true,
							rules: {
								required: false,
								message: '请输入企业注册地址',
								trigger: 'blur'
							},
							verify: 'required',
							col: 5
						},
						{
							label: '营业执照有效期',
							name: 'validityOfDate',
							placeholder: '请输入营业执照有效期',
							readonly: true,
							type: 'text',
							unlinkPanels: true,
							rules: {
								required: false,
								message: '请输入营业执照有效期',
								trigger: 'change'
							},
							col: 5
						},
						{
							name: 'businessScope',
							label: '经营范围',
							placeholder: '请输入经营范围',
							readonly: true,
							type: 'textarea',
							rows: 5,
							rules: {
								required: false,
								message: '请输入经营范围',
								trigger: 'blur'
							},
							verify: 'required',
							col: 10
						}
					]
				},
				{
					title: '企业联系信息',
					contents: [
						{
							type: 'text',
							label: '企业所属地区',
							name: 'regionId',
							col: 10,
							verify: 'required',
							readonly: true
						},
						{
							label: '通讯地址',
							name: 'contactAddress',
							placeholder: '请输入通讯地址',
							readonly: true,
							rules: {
								required: false,
								message: '请输入通讯地址',
								trigger: 'blur'
							},
							verify: 'required',
							col: 5
						},
						{
							label: '企业联系人',
							name: 'linkMan',
							placeholder: '请输入企业联系人',
							readonly: true,
							rules: {
								required: false,
								message: '请输入企业联系人',
								trigger: 'blur'
							},
							verify: 'required',
							col: 5
						},
						{
							label: '联系电话',
							name: 'linkPhone',
							readonly: true,
							placeholder: '请输入联系电话',
							rules: {
								required: false,
								message: '请输入联系电话',
								trigger: 'blur'
							},
							verify: 'required',
							col: 5
						},
						{
							label: '联系人电子邮箱',
							name: 'linkEmail',
							readonly: true,
							placeholder: '请输入联系人电子邮箱',
							rules: {
								required: false,
								message: '请输入联系人电子邮箱',
								trigger: 'blur'
							},
							verify: 'email',
							col: 5
						}
					]
				}
			];
			let entrustFormList = {
				title: '法定代表人授权认证',
				contents: [
					{
						label: '使用该认证方式',
						readonly: true,
						name: 'platEnterpriseEntrustDTO_isUse',
						placeholder: '请选择是否使用该认证方式',
						type: 'radio',
						rules: {
							required: false,
							message: '请选择是否使用该认证方式',
							trigger: 'blur'
						},
						col: 5,
						sysCode: 'yn_tag',
						'label-key': 'shortName',
						'value-key': 'cciValue'
					},
					{
						label: '审核状态',
						readonly: true,
						name: 'platEnterpriseEntrustDTO_status',
						type: 'select',
						rules: {
							required: false,
							message: '请选择审核状态',
							trigger: 'blur'
						},
						col: 5,
						sysCode: 'plat_enp_attestation_audit',
						'label-key': 'shortName',
						'value-key': 'cciValue'
					},
					{
						label: '管理员姓名',
						name: 'platEnterpriseEntrustDTO_adminName',
						placeholder: '请输入管理员姓名',
						readonly: true,
						rules: {
							required: false,
							message: '请输入管理员姓名',
							trigger: 'blur'
						},
						col: 5
					},
					{
						name: 'fj_lawPersonEmpower',
						label: '企业授权委托书',
						type: 'attachment',
						readonly: true,
						value: '',
						code: 'plat_enterprise_lawPersonEmpower',
						//preview:true,
						onPreview: res => {
							console.log(res);
						},
						append: {
							label: '下载模板',
							event: res => {
								console.log(res);
							}
						},
						ownId: this.formData1.id // 业务id
						//rules: {
						//required: true,
						//    message: '请上传营业执照',
						//    trigger: 'blur'
						//}
					}
				]
			};
			let bankFormList = {
				title: '企业对公打款认证',
				contents: [
					{
						label: '使用该认证方式',
						name: 'platEnterpriseBankDTO_isUse',
						placeholder: '请选择是否使用该认证方式',
						type: 'radio',
						readonly: true,
						rules: {
							required: false,
							message: '请选择是否使用该认证方式',
							trigger: 'blur'
						},
						col: 10,
						sysCode: 'yn_tag',
						'label-key': 'shortName',
						'value-key': 'cciValue'
					},
					{
						label: '开户银行',
						name: 'platEnterpriseBankDTO_bankName',
						readonly: true,
						placeholder: '请输入开户银行',
						rules: {
							required: false,
							message: '请输入开户银行',
							trigger: 'blur'
						},
						col: 5
					},
					{
						label: '银行所在地',
						name: 'platEnterpriseBankDTO_bankAddress',
						readonly: true,
						placeholder: '请输入银行所在地',
						rules: {
							required: false,
							message: '请输入银行所在地',
							trigger: 'blur'
						},
						col: 5
					},
					{
						label: '开户支行名',
						name: 'platEnterpriseBankDTO_branchName',
						readonly: true,
						placeholder: '请输入开户支行名',
						rules: {
							required: false,
							message: '请输入开户支行名',
							trigger: 'blur'
						},
						col: 5
					},
					{
						label: '企业银行卡账号',
						name: 'platEnterpriseBankDTO_cardNo',
						readonly: true,
						placeholder: '请输入企业银行卡账号',
						rules: {
							required: false,
							message: '请输入企业银行卡账号',
							trigger: 'blur'
						},
						col: 5
					}
				]
			};
			let lawperson = {
				title: '法定代表人身份认证',
				contents: [
					{
						label: '使用该认证方式',
						name: 'platEnterpriseLawpersonDTO_isUse',
						readonly: true,
						placeholder: '请选择是否使用该认证方式',
						type: 'radio',
						rules: {
							required: false,
							message: '请选择是否使用该认证方式',
							trigger: 'blur'
						},
						col: 10,
						sysCode: 'yn_tag',
						'label-key': 'shortName',
						'value-key': 'cciValue'
					},
					{
						label: '法定代表人',
						name: 'platEnterpriseLawpersonDTO_legalName',
						readonly: true,
						placeholder: '请输入法定代表人',
						rules: {
							required: false,
							message: '请输入法定代表人',
							trigger: 'blur'
						},
						col: 5
					},
					{
						label: '法人手机号',
						name: 'platEnterpriseLawpersonDTO_legalTel',
						readonly: true,
						placeholder: '请输入法人手机号',
						rules: {
							required: false,
							message: '请输入法人手机号',
							trigger: 'blur'
						},
						col: 5
					},
					{
						label: '证件有效日期',
						name: 'platEnterpriseLawpersonDTO_certDate',
						readonly: true,
						placeholder: '请输入证件有效日期',
						type: 'daterange',
						rules: {
							required: false,
							message: '请输入证件有效日期',
							trigger: 'blur'
						},
						col: 5
					},
					{
						label: '法人身份证号',
						name: 'platEnterpriseLawpersonDTO_idCard',
						readonly: true,
						placeholder: '请输入法人身份证号',
						rules: {
							required: false,
							message: '请输入法人身份证号',
							trigger: 'blur'
						},
						col: 5
					},
					{
						name: 'fj_lawPerson',
						label: '法人身份证',
						type: 'attachment',
						readonly: true,
						value: '',
						code: 'plat_enterprise_lawPerson',
						//preview:true,
						onPreview: res => {
							console.log(res);
						},
						append: {
							label: '下载模板',
							event: res => {
								console.log(res);
							}
						},
						ownId: this.formData1.id // 业务id
					}
				]
			};

			let platEnterpriseEntrustVO = this.formInfo.platEnterpriseEntrustVO;
			if (platEnterpriseEntrustVO != undefined) {
				if (platEnterpriseEntrustVO['isUse'] == 1) {
					fixFormList.push(entrustFormList);
				}
			}

			let platEnterpriseBankVO = this.formInfo.platEnterpriseBankVO;
			if (platEnterpriseBankVO != undefined) {
				if (platEnterpriseEntrustVO['isUse'] == 1) {
					fixFormList.push(bankFormList);
				}
			}

			let platEnterpriseLawpersonVO = this.formInfo.platEnterpriseLawpersonVO;
			if (platEnterpriseLawpersonVO != undefined) {
				if (platEnterpriseLawpersonVO['isUse'] == 1) {
					fixFormList.push(lawperson);
				}
			}
			//status--0:待审核（二级学院审核）,11:待招就处审核（二级学院审核通过）,1:已审核（招就处审核通过）, 2:审核未通过,9草稿
			let audit = {
				title: '审核意见',
				contents: [
					{
						label: '学院审核信息',
						name: 'collegeAuditCommentInfo',
						type: 'textarea',
						readonly: true,
						rows: 3,
						col: 10,
						hide: this.formData1.status === 0
					},
					{
						label: '招就处审核信息',
						name: 'auditCommentInfo',
						type: 'textarea',
						readonly: true,
						rows: 3,
						col: 10,
						hide: this.formData1.status === 0 || this.formData1.status === 11
					}
				]
			};

			if (this.type === 'audit') {
				fixFormList.push(audit);
			} else {
				for (var i in audit.contents) {
					audit.contents[i].readonly = true;
					audit.contents[i].required = false;
				}
				fixFormList.push(audit);
			}

			if (this.type === 'audit') {
				fixFormList.push(this.auditBtn);
			} else {
				fixFormList.push(this.viewBtn);
			}

			return fixFormList;
		}
	},
	created() {
		let id = this.selectInfo;
		this.handleFormData(this.formInfo);
	},
	methods: {
		handleSubmit(data, obj) {
			let params = { id: this.selectInfo };
			const code = obj.code;
			let status;
			this.$prompt(
				code === 'pass' ? '不通过理由：' : '审核意见：',
				this.isSystemReo ? '招就处审核' : '学院审核',
				{
					confirmButtonText: '确定',
					cancelButtonText: '取消',
					cancelPlaceholder: '请输入',
					// 校验不能为空
					inputValidator: val => {
						if (!val) {
							return code === 'pass' ? '请输入不通过理由' : '请输入审核意见';
						}
						return true;
					}
					//  type: code === 'pass'?'warning':'success'
				}
			)
				.then(({ value }) => {
					const loading = $.loading(this.$loading, '提交中');
					const statusTxt = code === 'pass' ? '不通过' : '通过';
					if (this.isSystemReo) {
						// 招就处审核
						if (code === 'pass') {
							status = 2; // 审核未通过
						} else {
							status = 1; // 招就处审核通过
						}
						params.status = status;
						params.auditComment = `${statusTxt}：${value}`;
					} else {
						// 二级学院审核
						if (code === 'pass') {
							status = 2; // 审核未通过
						} else {
							status = 11; // 待招就处审核（二级学院审核通过）
						}
						params.status = status;
						params.auditComment = `${statusTxt}：${value}`;
					}

					this.$request({
						url: interfaceUrl.audit,
						params: params,
						method: 'POST'
					}).then(res => {
						loading.close();
						this.$emit('cancel', false);
						if (res.rCode == 0) {
							this.$emit('refresh'); // 刷新数据
							this.$message.success(res.msg);
							this.formData1 = {};
						} else {
							this.$message.error(res.msg);
						}
					});
				})
				.catch(() => {});
		},
		/**
		 * 处理formData数据为可展示数据
		 */
		handleFormData(res) {
			res.registeredCapital = res.registeredCapital + '';
			this.formData1 = res;
			if (res.validityStartDate != undefined && res.validityEndDate != undefined) {
				let validityOfDate = [res.validityStartDate, res.validityEndDate];
				this.formData1.validityOfDate = res.validityStartDate + ' - ' + res.validityEndDate;
			}
			if (res.validityOfLicense != undefined && res.validityOfLicense == 'longperiod') {
				this.formData1.validityOfDate = '长期';
			}

			let selectRegionArray = [];
			if (res.regionId != undefined && res.regionId != '' && this.title==='编辑') {
				let regionIdArray = res.regionId.split(',');
				let regionNameArray = res.regionName.split('-');
				for (var i in regionIdArray) {
					var region = {};
					region.code = regionIdArray[i];
					region.name = regionNameArray[i];
					region.label = regionNameArray[i];
					selectRegionArray.push(region);
				}
				console.log(regionIdArray, 'view');
			}
			this.formData1.regionId = res.regionName;

			let platEnterpriseEntrustVO = this.formData1.platEnterpriseEntrustVO;
			if (platEnterpriseEntrustVO != undefined && platEnterpriseEntrustVO['isUse'] != undefined) {
				this.formData1['platEnterpriseEntrustDTO_isUse'] = platEnterpriseEntrustVO['isUse'] + '';
				this.formData1['platEnterpriseEntrustDTO_adminName'] =
					platEnterpriseEntrustVO['adminName'] + '';
				this.formData1['platEnterpriseEntrustDTO_status'] = platEnterpriseEntrustVO['status'] + '';
				if (this.formData1['platEnterpriseEntrustDTO_isUse'] == '0') {
					this.removeAttestationType('法定代表人授权认证');
				}
			} else {
				this.removeAttestationType('法定代表人授权认证');
			}
			this.formData1.platEnterpriseEntrustVO = '';

			let platEnterpriseBankVO = this.formData1.platEnterpriseBankVO;
			if (platEnterpriseBankVO != undefined && platEnterpriseEntrustVO['isUse'] != undefined) {
				this.formData1['platEnterpriseBankDTO_isUse'] = platEnterpriseEntrustVO['isUse'] + '';
				this.formData1['platEnterpriseBankDTO_bankName'] = platEnterpriseBankVO['bankName'] + '';
				this.formData1['platEnterpriseBankDTO_bankAddress'] =
					platEnterpriseBankVO['bankAddress'] + '';
				this.formData1['platEnterpriseBankDTO_branchName'] =
					platEnterpriseBankVO['branchName'] + '';
				this.formData1['platEnterpriseBankDTO_cardNo'] = platEnterpriseBankVO['cardNo'] + '';
				if (this.formData1['platEnterpriseBankDTO_isUse'] == '0') {
					this.removeAttestationType('企业对公打款认证');
				}
			} else {
				this.removeAttestationType('企业对公打款认证');
			}

			this.formData1.platEnterpriseBankVO = '';

			let platEnterpriseLawpersonVO = this.formData1.platEnterpriseLawpersonVO;
			if (
				platEnterpriseLawpersonVO != undefined &&
				platEnterpriseLawpersonVO['isUse'] != undefined
			) {
				this.formData1['platEnterpriseLawpersonDTO_isUse'] =
					platEnterpriseLawpersonVO['isUse'] + '';
				this.formData1['platEnterpriseLawpersonDTO_legalName'] =
					platEnterpriseLawpersonVO['legalName'] + '';
				this.formData1['platEnterpriseLawpersonDTO_legalTel'] =
					platEnterpriseLawpersonVO['legalTel'] + '';
				this.formData1['platEnterpriseLawpersonDTO_legalEmail'] =
					platEnterpriseLawpersonVO['legalEmail'] + '';
				this.formData1['platEnterpriseLawpersonDTO_idCard'] =
					platEnterpriseLawpersonVO['idCard'] + '';
				this.formData1['platEnterpriseLawpersonDTO_certStartDate'] =
					platEnterpriseLawpersonVO['certStartDate'] + '';
				this.formData1['platEnterpriseLawpersonDTO_certEndDate'] =
					platEnterpriseLawpersonVO['certEndDate'] + '';
				let certDate = [
					platEnterpriseLawpersonVO['certStartDate'],
					platEnterpriseLawpersonVO['certEndDate']
				];
				this.formData1['platEnterpriseLawpersonDTO_certDate'] = certDate;
			}
			this.formData1.platEnterpriseLawpersonVO = '';
			this.formData1.collegeAuditCommentInfo = `${this.formData1.collegeAuditTime} ${this.formData1.collegeAuditComment}`;
			this.formData1.auditCommentInfo = `${this.formData1.collegeAuditTime} ${this.formData1.auditComment}`;
		},
		// 隐藏对话框
		handleCancel(event) {
			if (event.type === 'reset') {
				this.$emit('refresh'); // 刷新数据
			}
		},
		removeAttestationType(attestainType) {
			for (var i in this.formList1) {
				if (this.formList1[i].title !== attestainType) {
					this.formList1[i].hide = false;
				}
			}
		},
		/**
		 * 获取下拉字典
		 */
		getDictionary() {
			//行政区划
			this.$request({
				url: interfaceUrl.region,
				method: 'get'
			}).then(res => {
				if (res.rCode == 0) {
					this.regionData = res.results;
				}
			});
		}
	}
};
</script>
<style lang="scss">
@import '../../../assets/style/style.scss';

.el-dialog__body {
	overflow: auto !important;

	.es-form .es-collapse {
		height: 100%;
	}

	.btn-box {
		margin-top: 20px;
		display: flex;
		justify-content: flex-end;

		.btn {
			padding: 5px 10px;
			color: #666;
			border: 1px solid #eee;
			cursor: pointer;
			margin-right: 5px;
			border-radius: 4px;
			// &.theme {
			// 	background: $--color-primary;
			// 	color: #fff;
			// 	border-color: $--color-primary;
			// }
		}
	}
}
</style>
