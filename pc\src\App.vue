<template>
	<div id="app">
		<router-view />
	</div>
</template>
<script>
import $ from 'eoss-ui/lib/utils/util';
import { mapState, mapActions } from 'vuex';

export default {
	computed: {
		...mapState('user', ['logoBj'])
	},
	created() {
		const color = localStorage.getItem('theme') || '#0076e9';
		$.updateTheme(color);
	},

	mounted() {
		if (!!window.ActiveXObject || 'ActiveXObject' in window) {
			window.addEventListener(
				'hashchange',
				() => {
					setTimeout(() => {
						let currentPath = window.location.hash.slice(1);
						if (this.$route.path !== currentPath) {
							this.$router.push(currentPath); // 主动更改路由界面
						}
					}, 500);
				},
				false
			);
		}
		this.getTenantId();
	},
	methods: {
		...mapActions('user', ['getTenantId']),
		onHome() {
			// 返回上一页
			this.$router.replace('/home');
		}
	}
};
</script>
<style lang="scss">
@import './assets/style/flexbox.scss';
::v-deep .es-notice-box {
	z-index: 99998;
}
::v-deep.el-cascader-panel {
	height: 300px !important;
}
.el-dialog__icon {
	color: #fff !important;
}

#app {
	height: 100%;
	overflow: hidden;
}

::-webkit-scrollbar {
	width: 5px;
	height: 5px;
	background-color: #f5f5f5;
}

/*定义滚动条轨道 内阴影+圆角*/
::-webkit-scrollbar-track {
	-webkit-box-shadow: inset 0 0 6px rgba(0, 0, 0, 0.3);
	border-radius: 10px;
	background-color: #f5f5f5;
}

/*定义滑块 内阴影+圆角*/
::-webkit-scrollbar-thumb {
	border-radius: 10px;
	-webkit-box-shadow: inset 0 0 6px rgba(0, 0, 0, 0.3);
	background-color: #555;
}
</style>
