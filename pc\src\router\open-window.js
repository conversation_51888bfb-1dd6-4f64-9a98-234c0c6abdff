//在新窗口打开
export default [
	{
		path: '/scientificDeail',
		title: '科研详情',
		name: 'scientificDeail',
		component: resolve =>
			require(['@/views/scientific-sys/project-management/inventory/detail.vue'], resolve)
	},
	{
		path: '/interimManageFlow',
		title: '中期检查流程办理',
		name: 'interimManageFlow',
		component: resolve =>
			require([
				'@/views/scientific-sys/project-management/inventory/interim-check/manageFlow.vue'
			], resolve)
	},
	//办理里嵌套的详情页
	{
		path: '/scientificDeailUrl',
		title: '项目管理详情',
		name: 'scientificDeailUrl',
		component: resolve =>
			require(['@/views/scientific-sys/project-management/inventory/detailUrl.vue'], resolve)
	},
	{
		path: '/leaveFlow',
		title: '考勤管理-请假-流程',
		name: 'leaveFlow',
		component: resolve => require(['@/views/sys/leave/flow-page'], resolve)
	},

	// @-算法管理------
	{
		path: '/arithmetic',
		title: '算法管理',
		name: 'arithmetict',
		component: resolve => require(['@/views/arithmetic/index.vue'], resolve)
	},
	{
		path: '/arithmeticClassify',
		title: '算法分类管理',
		name: 'arithmeticClassify',
		component: resolve => require(['@/views/arithmetic/classify.vue'], resolve)
	},
	// ----------

	{
		path: '/specificVisitorApptTeacher',
		title: '访客预约',
		name: 'specificVisitorApptTeacher',
		component: resolve => require(['@/views/specific/specificVisitorAppt/teacher.vue'], resolve)
	}
];
