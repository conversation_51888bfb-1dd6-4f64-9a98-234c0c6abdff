<template>
	<div class="guidance">
		<div
			:class="(index + 1) % 6 ? 'item ' : 'item noBorderRight'"
			v-for="(item, index) in myUsullyApp"
			:key="index"
			@click="toOpenWindow(item)"
		>
			<div class="icon">
				<div class="picBox">
					<img :src="handelPicUrl(item.icon)" alt="" height="100%" />
				</div>
			</div>
			<div class="name">{{ item.name }}</div>
		</div>
	</div>
</template>

<script>
import { picUrl } from '@/config/index';
export default {
	data() {
		return {
			myUsullyApp: [],
			loading: false
		};
	},
	methods: {
		toOpenWindow(item) {
			window.open(`${picUrl}${item.newDataUrl}${item.code}`);
		},
		//处理图片路径
		handelPicUrl(url) {
			return `${picUrl}${url}`;
		},
		//获取接口数据
		getListData() {
			this.loading = true;
			this.$request({
				url: '/cform/cformAffairDefinition/availableForms.dhtml',
				params: {},
				method: 'GET'
			})
				.then(res => {
					this.loading = false;
					let arr = res.data;
					this.myUsullyApp = arr;
					this.$nextTick(() => {
						let dom = document.getElementsByClassName('guidance');
						let height = dom[0].clientHeight;
						window.parent.setIframeHeight(height);
						let name = 'frameStart';
						window.parent.postMessage(name, '*');
					});
				})
				.catch(error => {
					this.loading = false;
				});
		}
	},
	mounted() {
		this.getListData();
	}
};
</script>

<style lang="scss" scoped>
* {
	margin: 0;
	padding: 0;
}
.guidance {
	width: 100%;
	display: flex;
	flex-wrap: wrap;
	background: #fff;
	.item {
		margin: 0 20px 20px 0;
		width: 183px;
		height: 128px;
		border: 1px solid #e8eaf0;
		border-radius: 10px;
		cursor: pointer;
		.icon {
			width: 100%;
			height: 80px;
			display: flex;
			justify-content: center;
			align-items: center;
			.picBox {
				border: 1px solid red;
				border-radius: 14px;
				width: 50px;
				height: 50px;
			}
		}
		.name {
			height: 48px;
			line-height: 28px;
			font-size: 16px;
			font-weight: bold;
			color: #333333;
			text-align: center;
			white-space: nowrap;
			overflow: hidden;
			text-overflow: ellipsis;
		}
	}
	.noBorderRight {
		margin-right: 0 !important;
	}
}
</style>
