import httpApi from './api.js';
export default {
	computed: {
		component() {
			return {
				// 纯列表结构
				name: 'es-data-table'
			};
		},
		treeParam() {
			return {};
		},
		table() {
			return {
				url: httpApi.labRoomInspectList,
				param: { handlerType: '' },
				full: true,
				fit: true,
				page: true,
				stripe: true,
				numbers: true,
				'row-key': 'id',
				toolbar: [
					{
						type: 'button',
						contents: [
							{
								text: '新增',
								type: 'primary'
							},
							{
								text: '导出',
								code: 'export',
								type: 'primary'
							}
						]
					},
					{
						type: 'search',
						reset: true,
						contents: [
							{
								type: 'date',
								col: 6,
								name: 'startDate',
								label: '日期开始时间',
								placeholder: '日期开始时间'
							},
							{
								type: 'date',
								col: 6,
								name: 'endDate',
								label: '日期结束时间',
								placeholder: '日期结束时间'
							},
							{
								name: 'roomName',
								placeholder: '实验室名称',
								label: '实验室名称',
								col: 6
							},
							{
								type: 'select',
								col: 6,
								label: '检查状态',
								placeholder: '检查状态',
								name: 'checkStatus',
								sysCode: 'lab_check_status',
								clearable: true
							}
						]
					}
				],
				thead: [
					{
						title: '日期',
						align: 'left',
						field: 'inspectDate',
						width: 160
					},
					{
						title: '实验室名称',
						align: 'left',
						field: 'roomName'
					},
					{
						title: '实验室负责人（实验员）',
						align: 'left',
						field: 'roomPersonLiable',
						width: 200
					},
					{
						title: '是否正常',
						align: 'left',
						field: 'normal',
						width: 100
					},
					{
						title: '存在具体问题',
						align: 'left',
						showOverflowTooltip: true,
						field: 'existProblem'
					},
					{
						title: '检查状态',
						align: 'left',
						field: 'checkStatus',
						sysCode: 'lab_check_status',
						width: 100
					},
					{
						title: '操作',
						type: 'handle',
						width: 180,
						events: [
							{
								text: '查看'
							},
							{
								text: '编辑',
								rules: rows => {
									return rows.checkStatus === '0';
								}
							},
							{
								text: '删除',
								rules: rows => {
									return rows.checkStatus === '0';
								}
							}
						]
					}
				]
			};
		}
	}
};
