<template>
	<div class="sketch_content">
		<el-collapse v-model="activeName" style="margin-bottom: 2em">
			<el-collapse-item title="访问申请" :name="1">
				<el-form ref="form" :model="formData" class="resumeTable" :rules="rules">
					<el-row>
						<el-col :span="8">
							<el-form-item label="访客姓名" label-width="120px" label-position="left" prop="name">
								<el-input v-model="formData.name" :disabled="infoDisabled"></el-input>
							</el-form-item>
						</el-col>
						<el-col :span="8">
							<el-form-item label="证件类型" label-width="120px" label-position="left" prop="zjlx">
								<el-input v-model="formData.zjlx" :disabled="infoDisabled"></el-input>
							</el-form-item>
						</el-col>
						<el-col :span="8">
							<el-form-item label="证件号" label-width="120px" label-position="left" prop="zjh">
								<el-input v-model="formData.zjh" :disabled="infoDisabled"></el-input>
							</el-form-item>
						</el-col>
					</el-row>
					<el-row>
						<el-col :span="8">
							<el-form-item
								label="手机号码"
								label-width="120px"
								label-position="left"
								prop="telephone"
							>
								<el-input v-model="formData.telephone" :disabled="infoDisabled"></el-input>
							</el-form-item>
						</el-col>
						<el-col :span="8">
							<el-form-item
								label="预计来访时间"
								label-width="120px"
								label-position="left"
								prop="visitStart"
							>
								<el-input v-model="formData.visitStart" :disabled="infoDisabled"></el-input>
							</el-form-item>
						</el-col>
						<el-col :span="8">
							<el-form-item
								label="预计结束时间"
								label-width="120px"
								label-position="left"
								prop="visitEnd"
							>
								<el-input v-model="formData.visitEnd" :disabled="infoDisabled"></el-input>
							</el-form-item>
						</el-col>
					</el-row>
					<el-row>
						<el-col :span="8">
							<el-form-item
								label="是否开车入园"
								label-width="120px"
								label-position="left"
								prop="drive"
							>
								<el-input v-model="formData.drive" :disabled="infoDisabled"></el-input>
							</el-form-item>
						</el-col>
						<el-col :span="8">
							<el-form-item label="车牌号" label-width="120px" label-position="left" prop="carNum">
								<el-input v-model="formData.carNum" :disabled="infoDisabled"></el-input>
							</el-form-item>
						</el-col>
						<el-col :span="8">
							<el-form-item
								label="同行人数"
								label-width="120px"
								label-position="left"
								prop="compNum"
							>
								<el-input v-model="formData.compNum" :disabled="infoDisabled"></el-input>
							</el-form-item>
						</el-col>
					</el-row>
					<el-row>
						<el-col :span="8">
							<el-form-item
								label="被访问人姓名"
								label-width="120px"
								label-position="left"
								prop="visitedName"
							>
								<el-input v-model="formData.visitedName" :disabled="infoDisabled"></el-input>
							</el-form-item>
						</el-col>
						<el-col :span="8">
							<el-form-item
								label="被访问人电话"
								label-width="120px"
								label-position="left"
								prop="visitedTelephone"
							>
								<el-input v-model="formData.visitedTelephone" :disabled="infoDisabled"></el-input>
							</el-form-item>
						</el-col>
						<el-col :span="8">
							<el-form-item
								label="被访所属学院"
								label-width="120px"
								label-position="left"
								prop="visitedCollege"
							>
								<el-input v-model="formData.visitedCollege" :disabled="infoDisabled"></el-input>
							</el-form-item>
						</el-col>
					</el-row>
					<el-row>
						<el-col :span="22">
							<el-form-item
								label="来访事由"
								label-width="120px"
								label-position="left"
								prop="purposes"
							>
								<el-input
									v-model="formData.purposes"
									type="textarea"
									:disabled="infoDisabled"
								></el-input>
							</el-form-item>
						</el-col>
					</el-row>
					<el-row>
						<el-col :span="22">
							<el-form-item
								label="附件图片"
								label-width="120px"
								label-position="left"
								prop="activityContent"
							>
								<es-upload
									v-bind="coverAttrs"
									ref="upload"
									:disabled="infoDisabled"
									select-type="icon-plus"
									list-type="picture-card"
								></es-upload>
							</el-form-item>
						</el-col>
					</el-row>
				</el-form>
			</el-collapse-item>
			<el-collapse-item title="同行人" :name="2">
				<es-data-table
					:url="tableUrl"
					:param="defaultParam"
					:thead="thead"
					:toolbar="toolbar"
					height="auto"
					@btnClick="btnClick"
				></es-data-table>
			</el-collapse-item>
		</el-collapse>
		<el-row v-if="!isTeacher">
			<el-form ref="auditForm" :model="formData" :rules="rules">
				<el-col :span="22">
					<el-form-item
						label="审核意见"
						label-width="120px"
						label-position="left"
						prop="auditOpinion"
					>
						<el-input
							v-model="formData.auditOpinion"
							type="textarea"
							:disabled="auditDisabled"
						></el-input>
					</el-form-item>
				</el-col>
			</el-form>
		</el-row>
		<el-row v-if="!isTeacher">
			<el-col :span="2" style="float: right">
				<el-button type="reset" @click="infoPageClose(false)">取消</el-button>
			</el-col>
			<el-col :span="3" style="float: right">
				<el-button v-show="auditBtnVisible" type="danger" @click="handleFormAudit('驳回')">
					驳回
				</el-button>
			</el-col>
			<el-col :span="3" style="float: right">
				<el-button v-show="auditBtnVisible" type="primary" @click="handleFormAudit('审核通过')">
					审核通过
				</el-button>
			</el-col>
		</el-row>
	</div>
</template>
<script>
import api from '@/http/specific/specificVisitorAppt/api';
import SnowflakeId from 'snowflake-id';

export default {
	name: 'InfoPage',
	props: {
		baseData: {
			type: Object
		},
		infoPageMode: {
			type: String
		}
	},
	data() {
		return {
			auditDisabled: false,
			auditBtnVisible: false,
			tableUrl: api.compListJson,
			infoDisabled: true,
			showMemberList: true,
			formData: {},
			pageMode: 'allOn',
			activeName: [1, 2],
			thead: [
				{
					title: '姓名',
					align: 'center',
					field: 'name',
					sortable: 'custom',
					showOverflowTooltip: true
				},
				{
					title: '电话',
					width: '220px',
					align: 'center',
					field: 'telephone',
					sortable: 'custom',
					showOverflowTooltip: true
				}
			],
			toolbar: [
				{
					type: 'search',
					reset: true,
					contents: [
						{
							type: 'text',
							name: 'keyword',
							placeholder: '姓名',
							clearable: true
						}
					]
				}
			]
		};
	},
	computed: {
		defaultParam() {
			return {
				apptId: this.formData.id
			};
		},
		isTeacher() {
			return window.location.href.includes('specificVisitorApptTeacher');
		},

		rules() {
			return {
				auditOpinion: [
					{ required: this.auditBtnVisible, message: '请输入审核意见', trigger: 'blur' }
				]
			};
		},

		coverAttrs() {
			return {
				code: 'specific_visitor_appt_adjunct',
				ownId: this.formData.id,
				preview: true,
				download: true,
				operate: true
			};
		}
	},
	watch: {},
	created() {
		this.formData = { ...this.baseData, drive: this.baseData.drive ? '是' : '否' };
		this.pageMode = this.infoPageMode;
		switch (this.pageMode) {
			case '新增':
				{
					const snowflake = new SnowflakeId();
					this.formData.id = snowflake.generate();
					this.showMemberList = false;
					this.infoDisabled = false;
					this.auditDisabled = true;
					this.auditBtnVisible = false;
				}
				break;
			case '查看':
				this.showMemberList = true;
				this.infoDisabled = true;
				this.auditDisabled = true;
				this.auditBtnVisible = false;
				break;
			case '审核':
				this.showMemberList = true;
				this.infoDisabled = true;
				this.auditDisabled = false;
				this.auditBtnVisible = true;
				break;
		}
	},
	methods: {
		btnClick(res) {},
		handleFormSubmit() {
			//校验
			this.$refs.form.validate(valid => {
				if (valid) {
					let apiUrl = this.pageMode === '编辑' ? api.update : api.save;
					let saveData = { ...this.formData };
					//处理数据

					this.$request({
						url: apiUrl,
						data: saveData,
						method: 'POST'
					}).then(res => {
						if (res.rCode === 0) {
							this.$message.success('操作成功');
							this.infoPageClose(true);
						} else {
							this.$message.error(res.msg);
						}
					});
				}
			});
		},
		handleFormAudit(res) {
			//校验
			this.$refs['auditForm'].validate(valid => {
				//开启校验
				if (valid) {
					// 如果校验通过，请求接口
					let btnType = res;
					// 处理请求数据
					let auditData = {};
					auditData.id = this.formData.id;
					auditData.auditOpinion = this.formData.auditOpinion;

					this.$confirm('是否确认' + btnType + '？', '审核', {
						confirmButtonText: '确定',
						cancelButtonText: '取消',
						type: 'warning'
					})
						.then(() => {
							// 处理请求数据
							switch (btnType) {
								case '审核通过':
									auditData.auditStatus = 1;
									break;
								case '驳回':
									auditData.auditStatus = 2;
									break;
							}

							if (auditData.auditStatus !== -1) {
								this.$request({
									url: api.audit,
									data: auditData,
									method: 'POST'
								}).then(response => {
									if (response.success) {
										this.$message.success('审核成功');
										this.infoPageClose(true);
									} else {
										this.$message.error(response.msg);
									}
								});
							}
						})
						.catch(() => {});
				} else {
					return false;
				} //校验不通过
			});
		},
		infoPageClose(reload) {
			this.pageMode = 'allOn';
			this.infoDisabled = false;
			this.$emit('activelyClose', reload);
		}
	}
};
</script>
<style scoped>
.sketch_content {
	overflow: auto;
	height: 100%;
	border-top: 1px solid #eff1f4;
	border-bottom: 1px solid #eff1f4;
	padding: 0 30px 11px 27px;
}
.resumeTable td {
	height: 50px;
	font-weight: bold;
}

::v-deep .el-collapse-item__header.is-active {
	border-bottom: 1px solid #ebeef5;
	font-size: 18px;
	font-weight: bold;
}
::v-deep .el-collapse-item__header::before {
	content: '';
	width: 4px;
	height: 18px;
	background-color: #0076e9;
	margin-right: 2px;
}
</style>
