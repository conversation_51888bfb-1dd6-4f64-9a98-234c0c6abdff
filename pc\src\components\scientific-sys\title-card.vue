<!--
 @desc:标题卡片组件
 @author: WH
 @date: 2023/11/13
 -->
<template>
	<div class="mini-title">
		<span>{{ title }}</span>
		<slot />
	</div>
</template>
<script>
export default {
	props: {
		title: {
			type: String,
			required: true
		}
	}
};
</script>

<style lang="scss" scoped>
@import '@ast/style/public.scss';
.mini-title {
	@include flexBox(space-between);
	margin: 10px 0;
	font-size: 14px;
	font-weight: bold;
	border-bottom: 1px solid #e8eaf0;
	color: #ffffff;
	overflow: hidden;
	width: 100%;
	height: 35px;
	span {
		position: relative;
		width: 130px;
		padding: 8px 0 8px 20px;
		display: block;
		background: #0076e8;
		&::before,
		&::after {
			position: absolute;
			right: 0px;
			top: 0;
			display: inline-block;
			content: '';
			width: 16px;
			height: 100%;
			background: #479cee;
			transform: skew(30deg, 0);
		}
		&::after {
			right: -16px;
			background: #b2d6f8;
		}
	}
	p {
		font-size: 14px;
		margin-right: 10px;
		color: #0076e8;
		cursor: pointer;
	}
}
</style>
