<template>
	<div style="width: 100%; height: 100%">
		<es-data-table
			ref="refTable"
			style="width: 100%"
			:row-style="tableRowClassName"
			:thead="thead"
			:page="true"
			:url="basic.tableUrl"
			:param="param"
			close
			stripe
			:border="true"
			:toolbar="toolbar"
			method="get"
			@btnClick="btnClick"
		></es-data-table>
		<es-dialog
			:title="title + '结题'"
			:visible.sync="visibleBasicInfo"
			size="lg"
			width="72%"
			height="700px"
		>
			<BasicInfoFinal
				v-if="visibleBasicInfo"
				:id="rowId"
				:project-id="projectId"
				:is-flow-pattern="true"
				:project-classify="projectClassify"
				:title="title"
				@visible="
					e => {
						visibleBasicInfo = e;
						$refs.refTable.reload();
					}
				"
			/>
		</es-dialog>
		<es-dialog title="查看" :visible.sync="visibleDetail" size="full" class="is-dialog">
			<Detail
				v-if="visibleDetail"
				:id="rowId"
				:project-classify="projectClassify"
				:is-flow-pattern="false"
				open-type="look"
			/>
		</es-dialog>
		<!-- 新增结题弹框 -->
		<es-dialog
			v-if="visibleChange"
			ref="visibleChange"
			class="dialog-box"
			title="选择项目"
			size="md"
			height="650px"
			:append-to-body="false"
			:modal-append-to-body="false"
			:visible.sync="visibleChange"
			@close="visibleChange = false"
		>
			<SelectData @cloeseChoose="cloeseChoose" />
		</es-dialog>
	</div>
</template>

<script>
import BasicInfoFinal from './components/basic-info-final.vue';
import Detail from '@/views/scientific-sys/project-management/inventory/detail.vue';
import SelectData from '@/views/scientific-sys/components/selectData.vue';

export default {
	components: {
		BasicInfoFinal,
		SelectData,
		Detail
	},
	data() {
		return {
			basic: {
				tableUrl: '/ybzy/projectBaseInfo/projectConclusionPage'
			},
			param: {
				asc: 'false',
				orderBy: 'createTime'
			},
			visibleChange: false,
			rowId: '',
			projectId: '',
			formData: {},
			visible: false,
			visibleDetail: false,
			tableData: [
				// {
				// 	bgzt: '待审核'
				// },
				// {
				// 	bgzt: '待变更'
				// },
				// {
				// 	bgzt: '审核中'
				// },
				// {
				// 	bgzt: '已变更'
				// },
				// {
				// 	bgzt: '未通过'
				// },
				// {
				// 	bgzt: '暂存'
				// }
			],
			toolbar: [
				{
					type: 'button',
					contents: [
						{
							text: '结题申请',
							type: 'primary'
						}
					]
				},
				{
					type: 'search',
					reset: true,
					contents: [
						{
							type: 'text',
							label: '关键字查询',
							name: 'keyword',
							placeholder: '请输入关键字',
							col: 3
						}
					]
				},
				{
					type: 'filter',
					contents: [
						{
							type: 'text',
							label: '项目编号',
							name: 'projectNumber',
							placeholder: '请输入立项编号',
							col: 3
						},
						{
							type: 'text',
							label: '项目名称',
							name: 'projectName',
							placeholder: '请输入项目名称',
							col: 3
						},
						{
							type: 'select',
							label: '项目分类',
							placeholder: '选择查询',
							name: 'projectClassify',
							sysCode: 'project_classify',
							col: 3
						},
						{
							type: 'select',
							label: '申请状态',
							placeholder: '请选择',
							name: 'auditState',
							col: 3,
							sysCode: 'project_flow_state'
						},
						// {
						// 	type: 'text',
						// 	label: '部门/二级学院',
						// 	name: 'declareOrgName',
						// 	placeholder: '输入部门/二级学院查询',
						// 	col: 3
						// },
						{
							type: 'text',
							label: '申请人 ',
							name: 'createUserName',
							placeholder: '输入申报人查询',
							col: 3
						},
						{
							type: 'monthrange',
							label: '申请时间',
							name: 'declareTimeStartAndEnd',
							default: true,
							placeholder: '选择日期',
							clearable: true,
							col: 3
						}
					]
				}
			],
			thead: [
				{
					title: '项目编号',
					field: 'projectNumber',
					align: 'center',
					showOverflowTooltip: true
				},
				{
					title: '项目名称',
					field: 'projectName',
					showOverflowTooltip: true,
					align: 'center',
				},
				{
					title: '项目分类',
					field: 'projectClassifyTxt',
					align: 'center'
				},
				{
					title: '申请时间',
					field: 'createTime',
					align: 'center'
					// render: (h, params) => {
					// 	let updataTimeStr = '';
					// 	if (params.row.updateTime) updataTimeStr = params.row.updateTime.substring(0, 10);
					// 	return h('p', {}, updataTimeStr);
					// }
				},
				{
					title: '申请人',
					field: 'createUserName',
					align: 'center'
				},
				// {
				// 	title: '申报人电话',
				// 	field: 'declarePhone',
				// 	align: 'center'
				// },
				// {
				// 	title: '工作单位',
				// 	field: 'declareOrgName',
				// 	align: 'center',
				// 	width: 180
				// },
				// {
				// 	title: '立项编号',
				// 	field: 'projectNumber',
				// 	align: 'center'
				// },
				{
					title: '结题状态',
					align: 'center',
					field: 'auditStateTxt',
					// width: '90px',
					// fixed: 'right',
					render: (h, params) => {
						let auditStateTxt = params.row.auditStateTxt;
						let auditState = params.row.auditState;
						return h(
							'el-tag',
							{
								props: {
									size: 'small',
									type: this.projecstate(auditState)
								}
							},
							auditStateTxt
						);
					}
				},
				{
					title: '操作',
					type: 'handle',
					// fixed: 'right',
					// width: '100',
					align: 'center',
					events: [
						{
							text: '查看'
						}
						// {
						// 	text: '结题'
						// 	//1已立项 3待结题（未变更中）
						// 	// rules: rows => [1, 3].includes(rows.projectStatus) && rows.projectChangeStatus == 0
						// }
					]
				}
			],
			title: '查看',
			visibleBasicInfo: false,
			projectClassify: ''
		};
	},
	computed: {
		contentsKey() {
			// LENTHWAYS("lengthWays", "纵向项目",0),
			// CROSSWISE("crosswise", "横向项目",1),
			// COLLEGE("college", "院级项目",2),
			const projectClassify = Number(this.projectClassify);
			let contentsKey = '';
			switch (projectClassify) {
				case 0:
					contentsKey = 'verticalProject'; // 纵向项目
					break;
				case 1:
					contentsKey = 'crosswiseProject'; // 横向项目
					break;
				case 2:
					contentsKey = 'instituteProject'; // 院级项目
					break;
			}
			return contentsKey;
		}
	},
	methods: {
		/**
		 * 表格行高
		 */
		tableRowClassName({ row, rowIndex }) {
			let styleRes = {
				height: '54px !important'
			};
			return styleRes;
		},
		// 获取项目审核状态
		projecstate(state) {
			let stateCurrent = '';
			// 项目状态（字典编码：project_status）0：草稿、1：待审核：2：审核中、3：审核通过、9：驳回
			switch (state) {
				case '0':
					stateCurrent = 'warning';
					break;
				case '2':
					stateCurrent = 'primary';
					break;
				case '3':
					stateCurrent = 'success';
					break;
				case '1':
					stateCurrent = 'info';
					break;
				case '9':
					stateCurrent = 'danger';
					break;
				default:
					break;
			}
			return stateCurrent;
		},
		load(text) {
			return this.$.loading(this.$loading, text);
		},
		//高级搜索
		submit({ data }) {
			console.log(data); //搜索的字段
			this.$.ajax({
				url: '/ybzy/projectBaseInfo/listJson',
				params: data
			})
				.then(res => {
					if (res.results == null) {
						this.tableData = [];
					} else {
						this.tableData = res.results.records;
					}
				})
				.catch(err => {});
		},
		btnClick({ handle, row }) {
			this.projectClassify = row?.projectClassify || '';
			switch (handle.text) {
				case '结题申请':
					this.visibleChange = true;
					break;
				case '查看':
					// this.visibleDetail = true;
					this.rowId = row.id;
					this.projectId = row.projectId;
					this.title = '查看';
					// this.visibleDetail = true;
					this.visibleBasicInfo = true;
					break;
				case '结题':
					this.rowId = row.id;
					this.projectId = row.projectId;
					this.title = '编辑';
					this.visibleBasicInfo = true;
					break;
				default:
					break;
			}
		},
		cloeseChoose(row) {
			this.projectClassify = row?.projectClassify || '';
			this.visibleBasicInfo = true;
			this.title = '新增';
			this.rowId = this.$uuidv4();
			this.projectId = row.id;
			this.isChange = true;
			this.visibleChange = false;
		},
		closeChange() {
			this.visibleChange = false;
		},
		reload() {
			this.$refs.table.reload();
		}
	}
	/* 	mounted() {
		this.$.ajax({
			url: '/ybzy/projectBaseInfo/listJson'
		})
			.then(res => {
				this.tableData = res.results.records;
			})
			.catch(err => {});
	} */
};
</script>
<style lang="scss" scoped>
.is-dialog > ::v-deep .el-dialog {
	height: 100%;
}
</style>
