<!--
 @desc:工会首页
 @author: WH
 @date: 2023/11/9
 -->
 <template>
	<div class="main">
		<div class="card-box">
			<mini-card
				v-for="(item, index) in card"
				:key="index"
				:card-data="item"
				body-style="font-size:12px;"
			/>
		</div>
		<titile-card title="工会管理" :img-url="require('@/assets/images/sys/ghgl.png')">
			<template #filtre>
				<div class="tabs">
					<p :class="{ 'is-active': tabActive == 0 }" @click="changeTab(0)">我的工会</p>
					<p :class="{ 'is-active': tabActive == 1 }" @click="changeTab(1)">工会活动</p>
				</div>
			</template>
			<template #content>
				<div class="table-box">
					<es-data-table
						ref="table"
						:thead="thead"
						:page="true"
            :url="tableUrl"
						:data="tableUrlData"
						:toolbar="toolbar"
						:response="func"
						style="width: 100%"
						height="400px"
						@change="pageSizeChange"
						@current="pageCurrentChange"
						@success="succ"
					></es-data-table>
				</div>
			</template>
		</titile-card>
		<div class="e-box">
			<titile-card title="活动统计" :img-url="require('@/assets/images/sys/hdtj.png')">
				<template #content>
					<div class="is-pie">
						<e-pie id="hdtj" :series-data="pieData" />
					</div>
				</template>
			</titile-card>
			<titile-card title="人数统计" :img-url="require('@/assets/images/sys/rstj.png')">
				<template #content>
					<div class="is-pie">
						<e-line id="rstj" :series-data="lineData" />
					</div>
				</template>
			</titile-card>
			<titile-card title="收支统计" :img-url="require('@/assets/images/sys/sztj.png')">
				<template #filtre>
					<el-date-picker v-model="szjlMonth" type="month" placeholder="选择月"></el-date-picker>
				</template>
				<template #content>
					<div class="is-pie">
						<e-bar id="szjl" :series-data="barData" />
					</div>
				</template>
			</titile-card>
		</div>
	</div>
</template>

<script>
import MiniCard from '@/components/show/mini-card.vue';
import TitileCard from '@/components/show/titile-card.vue';
import EPie from '@/components/echarts/pie.vue';
import ELine from '@/components/echarts/line.vue';
import EBar from '@/components/echarts/bar.vue';

const THEAD1 = [
	{
		title: '公会名称',
		field: 'unionName',
		align: 'center'

		// fixed: true
		// render: (h, params) => {
		//   return h('p', {}, `${params.row.reportYear}年${params.row.reportMonth}月`);
		// }
	},
	{
		title: '上级工会',
		field: 'parentName',
		fixed: false,
		align: 'center'
	},
	{
		title: '工会人数',
		field: 'memberSum',
		fixed: false,
		align: 'center'
	},
	{
		title: '工会主席',
		field: 'viceChairmanName',
		fixed: false,
		align: 'center'
	},
	{
		title: '主席联系方式',
		field: 'chairmanPhone',
		fixed: false,
		align: 'center'
	},
	{
		title: '工会副主席',
		field: 'viceChairmanName',
		fixed: false,
		align: 'center'
	},
	{
		title: '副主席联系方式',
		field: 'viceChairmanPhone',
		fixed: false,
		align: 'center'
	}
];
const THEAD2 = [
	{
		title: '活动名称',
		field: 'createTime',
		fixed: true
	},
	{
		title: '发布人',
		field: 'formName',
		fixed: false
	},
	{
		title: '活动时间',
		field: 'companyname',
		fixed: false
	},
	{
		title: '人数上限',
		field: 'fillUsername',
		fixed: false
	},
	{
		title: '联系电话',
		field: 'telephonenum',
		fixed: false
	},
	{
		title: '活动地址',
		field: 'startdate',
		width: '200px',
		fixed: false
	},
	{
		title: '活动状态',
		field: 'companyStatusText',
		fixed: false
	}
];
export default {
	components: { MiniCard, TitileCard, EPie, ELine, EBar },

	data() {
		return {
			szjlMonth: '',
			tabActive: 0,
			supportMethod: 'POST',
			card: [
				{
					img: require('@/assets/images/sys/gh_icon1.png'),
					title: '工会总数',
					unit: '个',
					num: '0'
				},
				{
					img: require('@/assets/images/sys/gh_icon2.png'),
					title: '工会总人数',
					unit: '人',
					num: '0'
				},
				{
					img: require('@/assets/images/sys/gh_icon3.png'),
					title: '工会总收入',
					unit: '元',
					num: '0'
				},
				{
					img: require('@/assets/images/sys/gh_icon4.png'),
					title: '工会总支出',
					unit: '元',
					num: '0'
				}
			],
			//tableUrl: '/ybzy/union/home/<USER>',
      tableUrl: '/ybzy/union/home/<USER>',
			tableUrlData: [],
			toolbar: [
				{
					type: 'search',
					reset: true,
					contents: [
						{
							type: 'text',
							label: '关键字',
							name: 'keyword',
							placeholder: '请输入关键字'
						}
					]
				}
			],
			thead: THEAD1,
			barData: [],
			lineData: [],
			pieData: []
		};
	},
	mounted() {
		//console.log(this.$refs.table, 333);
		this.getBarData();
		this.getLineData();
		this.getTotalData();
		this.getPieData();
	},
	methods: {
		//返回成功的数据
		succ(value) {
			console.info('请求成功返回的数据：', value);
		},
		//
		changeTab(index) {
			if (this.tabActive == index) return;
			this.tabActive = index;
			if (index == 0) {
				this.thead = THEAD1;
				this.changeTableUrl('/ybzy/union/home/<USER>', 'GET', []);
			} else if (index == 1) {
				this.thead = THEAD2;
				let params = {
					pageNumber: 1,
					pageSize: 3,
					status: 0,
					tenementId: '248063dd305a48b4a6d0c3759b1db327',
					plateCode: 'sthd'
				};
				this.changeTableUrl('/museumcloud/activity/activityItem/front/list', 'POST', params);
			}
			this.$refs.table.reload();
		},
		changeTableUrl(urls, methods, params) {
			this.$.ajax({
				method: methods,
				url: urls,
				data: params
			}).then(data => {
				methods == 'GET'
					? (this.tableUrlData = data.results.records)
					: (this.tableUrlData = data.data.list);
				console.info('data：', data);
			});
		},
		pageCurrentChange(current) {
			console.log('current', current);
		},
		pageSizeChange(size) {
			console.log('size', size);
		},
		func(res) {
			let { data } = res;
			let param = {};
			for (let i in data) {
				if (i == 'startdate') {
					param['start'] = data[i][0];
					param['end'] = data[i][1];
				} else {
					param[i] = data[i];
				}
			}
			console.log(data);
			return param;
		},
		getBarData() {
			this.$.ajax({
				method: 'GET',
				url: '/ybzy/union/home/<USER>'
			}).then(data => {
				let ArrList = [];
				ArrList.push(data.results.unionNameArr);
				ArrList.push(data.results.unionInArr);
				ArrList.push(data.results.unionOutArr);
				this.barData = ArrList;
				let unionInArr = ArrList[1].map(Number);
				let unionOutArr = ArrList[2].map(Number);
				let revenue = 0;
				let expenditures = 0;
				for (let i = 0; i < ArrList[1].length; i++) {
					revenue = unionInArr[i] + revenue;
					expenditures = unionOutArr[i] + expenditures;
				}
				this.card[2].num = String(revenue);
				this.card[3].num = String(expenditures);
				//console.info('打印原始数据:', data.results);
				//console.info('打印收入支出柱状图数据', ArrList);
			});
		},
		getTotalData() {
			this.$.ajax({
				method: 'GET',
				url: '/ybzy/union/home/<USER>'
			}).then(data => {
				this.card[0].num = data.results.total;
			});
		},
		getPieData() {
			this.$.ajax({
				method: 'GET',
				url: '/museumcloud/activity/activityItem/front/activityStatusCount'
			}).then(data => {
				//console.info('成功：', data.data.list);
				let activityList = [];
				data.data.list.forEach(el => {
					activityList.push({ name: el.name, value: 20 });
				});
				this.pieData = activityList;
				//console.info('成功：', this.pieData);
			});
		},
		getLineData() {
			this.$.ajax({
				method: 'GET',
				url: '/ybzy/union/home/<USER>'
			}).then(data => {
				//console.info('打印原始数据:', data.results);
				let ArrList = [];
				let numList = [];
				let sum = 0;
				ArrList.push(data.results.unionMemberNumHistogram.unionNames);
				ArrList.push(data.results.unionMemberNumHistogram.unionMemberNums);
				//console.info('打印工会人数占比柱状图数据', ArrList);
				let numCount = ArrList[1].map(Number);
				for (let i = 0; i < numCount.length; i++) {
					sum = numCount[i] + sum;
				}
				this.card[1].num = sum;
				for (let i = 0; i < numCount.length; i++) {
					numList.push(String((numCount[i] / sum).toFixed(4)));
				}
				ArrList.splice(1, 0, numList);
				this.lineData = ArrList;
				//console.info('打印工会人数占比柱状图数据', ArrList);
			});
		}
	}
};
</script>
<style lang="scss" scoped>
@import '@ast/style/public.scss';

.main {
	background: #f0f2f5;
	padding: 12px;
	overflow: auto;
	height: 100%;
	width: 100%;
	& > * {
		background: #fff;
	}
	header {
		@include flexBox(space-between);
		padding: 6px 20px;
		border-radius: 10px;
		font-size: 20px;
		font-weight: 550;
	}
	.card-box {
		@include flexBox(space-between);
		width: 100%;
		background: #f0f2f5;
		margin-bottom: 12px;

		.card {
			width: 24.5%;
		}
	}
	.tabs {
		// width: 300px;
		flex-shrink: 0;
		@include flexBox();
		p {
			margin-left: 10px;
			font-weight: 550;
			cursor: pointer;
		}
		.is-active {
			color: #0377e8;
		}
	}
	.e-box {
		@include flexBox(space-between);
		background: #f0f2f5;
		margin-top: 12px;
		width: 100%;
		& > * {
			background: #fff;
		}
		.title-card {
			width: 32.7%;
		}
	}
	.is-pie {
		width: 100%;
		height: 320px;
	}
}
</style>
