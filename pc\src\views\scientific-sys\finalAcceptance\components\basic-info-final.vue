<!--
 @desc:项目管理
 @author: WH
 @date: 2023/11/14
 -->
<template>
	<component
		:is="isFlowPattern ? 'ProcesPage' : 'div'"
		:flow-data-props="{
			appId: formData.appId, // 流程图id
			businessId: id, // 有 businessId-业务id 则为发起节点
			flowTypeCode: basics.flowTypeCode,
			defaultProcessKey: basics.defaultProcessKey,
			isEdit: !formReadonly, // 是否编辑
			isFlow: !formReadonly // 是否显示右边流程功能
		}"
		:btn-list="[{ text: '提交', event: 'sub', type: 'primary' }]"
		@subFun="save"
		@handleSuccess="handleSuccess"
	>
		<div class="main">
			<div class="btn-box">
				<el-button type="text" size="medium" icon="el-icon-s-promotion" @click="openDetail">
					查看项目详情
				</el-button>
			</div>
			<el-form
				ref="elform"
				:key="formReadonly"
				:model="formData"
				label-width="120px"
				class="is-form"
			>
				<el-form-item label="结题说明">
					<el-input
						v-model="formData.specification"
						:readonly="formReadonly"
						type="textarea"
					></el-input>
				</el-form-item>
				<el-form-item label="结题材料">
					<ul class="table-box">
						<li class="row is-head">
							<div>结题材料类型</div>
							<div>附件</div>
							<div class="btn">
								<es-button
									type="primary"
									size="mini"
									icon="es-icon-jiahao"
									:disabled="formReadonly"
									circle
									@click="addTableRow"
								/>
							</div>
						</li>
						<li v-for="(item, index) in tableData" :key="index" class="row is-body">
							<div>
								<es-select
									v-model="item.achievementType"
									placeholder="请选择"
									sys-code="project_close_achievement_type"
									:readonly="formReadonly"
									value-type="object"
								></es-select>
							</div>
							<div>
								<es-upload
									v-bind="{
										code: 'conclusionfile',
										ownId: item.id,
										// 业务id
										download: true,
										dragSort: true //是否允许附件列表进行拖拽排序
									}"
									:show-info="['uploadTime', 'fileSize']"
									:readonly="formReadonly"
									:multiple="false"
									:limit="1"
								></es-upload>
							</div>
							<div class="btn">
								<es-button
									type="danger"
									size="mini"
									icon="es-icon-jianhao"
									circle
									:disabled="formReadonly"
									@click="delTableRow(index)"
								/>
							</div>
						</li>
					</ul>
				</el-form-item>
				<el-form-item
					v-if="node === '3' || title.includes('查看')"
					label="结题验收表"
					prop="upload"
				>
					<div class="download" @click="download">
						<i class="el-icon-download"></i>
						下载结题验收表模板
					</div>
					<es-upload
						ref="refUpload"
						:readonly="node !== '3'"
						:show-info="['uploadTime', 'fileSize']"
						v-bind="{
							code: 'conclusionfile',
							ownId: formData.id,
							// 业务id
							// fileSize: '20023KB',
							download: true,
							dragSort: true //是否允许附件列表进行拖拽排序
						}"
					></es-upload>
				</el-form-item>
				<!-- <el-form-item
					v-if="node === '3' || title.includes('查看')"
					label="结题是否通过"
					prop="upload"
				>
					<el-radio-group v-model="formData.isPass" :disabled="formReadonly">
						<el-radio label="是"></el-radio>
						<el-radio label="否"></el-radio>
					</el-radio-group>
				</el-form-item> -->
			</el-form>
			<!-- <div class="btn-save">
				<el-button size="medium" type="primary" @click="save">保存</el-button>
				<el-button size="medium" @click="handleSuccess">取消</el-button>
			</div> -->
			<es-dialog
				:title="formTitle"
				:visible.sync="visibleBasicInfo"
				:show-scale="false"
				size="full"
				height="100%"
			>
				<BasicInfo
					v-if="visibleBasicInfo"
					:id="projectId"
					:contents-key="contentsKey"
					:title="formTitle"
					:is-flow-pattern="false"
					@visible="
						e => {
							visibleBasicInfo = e;
							$refs.refTable.reload();
						}
					"
				/>
			</es-dialog>
		</div>
	</component>
</template>

<script>
// import ProcesPage from '@/components/process-page.vue';

import titleCard from '@cpt/scientific-sys/title-card.vue';
import BasicInfo from '../../components/project-info/basic-info.vue';

// import { mixinInfo } from './mixinInfo';
export default {
	components: {
		// ProcesPage,
		ProcesPage: () => import('@/components/process-page.vue'),
		titleCard,
		BasicInfo
	},
	// mixins: [mixinInfo],
	props: {
		id: {
			type: String,
			default: ''
		},
		projectId: {
			type: String,
			default: ''
		},
		// 查看 编辑 新增
		title: {
			type: String,
			default: '查看'
		},
		projectClassify: {
			type: String,
			default: ''
		},
		// 是否流程模式
		isFlowPattern: {
			type: Boolean,
			default: true
		}
	},

	data() {
		return {
			basics: {
				info: '/ybzy/projectBaseInfo/getConclusionById', // 详情接口
				save: '/ybzy/projectBaseInfo/projectConclusion', // 新增
				// edit: '/ybzy/projectBaseInfo/update', // 修改
				// editProjectChange: '/ybzy/projectBaseInfo/editProjectChange', // 项目变更
				flowTypeCode: 'conclusion', // 流程code
				defaultProcessKey: 'conclusion' // 默认关联流程 key
			},
			formReadonly: true,
			formTitle: '查看',
			visibleBasicInfo: false,

			row: {},
			codesObj: {
				selectList1: []
			},
			dialog: false,
			activeName: 'info',
			formData: {
				specification: '' //结题说明
			},
			tableData: [],

			flowTypeCode: 'conclusion',
			btnList: [{ text: '提交', event: 'sub', type: 'primary' }]
		};
	},
	computed: {
		contentsKey() {
			// LENTHWAYS("lengthWays", "纵向项目",0),
			// CROSSWISE("crosswise", "横向项目",1),
			// COLLEGE("college", "院级项目",2),
			const projectClassify = Number(this.projectClassify);
			let contentsKey = '';
			switch (projectClassify) {
				case 0:
					contentsKey = 'verticalProject'; // 纵向项目
					break;
				case 1:
					contentsKey = 'crosswiseProject'; // 横向项目
					break;
				case 2:
					contentsKey = 'instituteProject'; // 院级项目
					break;
			}
			return contentsKey;
		},
		node() {
			const query = this.$route.query;
			return query.node ? query.node : '';
		}
	},
	watch: {
		id: {
			handler(newVal, oldVal) {
				if (newVal) {
					this.getPorjectInfo();
				}
			},
			immediate: true
		}
	},

	methods: {
		// 是否只读和编辑
		toolFormReadonly() {
			this.$nextTick(() => {
				const isEdit = window.location.href.includes('isEdit'); // 判断是否流程页面的编辑
				this.formReadonly = this.title.includes('查看') && !isEdit ? true : false;
			});
		},
		// 表格行高
		tableRowClassName({ row, rowIndex }) {
			let styleRes = {
				height: '54px !important'
			};
			return styleRes;
		},
		download() {
			window.open(
				this.$host +
					'/project-ybzy/picture/sys/%E7%A7%91%E6%8A%80%E4%B8%8E%E7%A4%BE%E4%BC%9A%E6%9C%8D%E5%8A%A1%E5%A4%84%E5%85%B3%E4%BA%8E%E5%86%8D%E6%AC%A1%E6%B8%85%E7%90%86%E9%99%A2%E7%BA%A7%E7%A7%91%E7%A0%94%E9%A1%B9%E7%9B%AE%E5%BA%94%E7%BB%93%E9%A2%98%E7%9A%84%E9%80%9A%E7%9F%A524.3.19.rar'
			);
		},
		openDetail() {
			this.visibleBasicInfo = true;
		},
		addTableRow() {
			this.tableData.push({
				id: this.$uuidv4()
			});
		},
		//修改保存
		async save(callBank) {
			const refUpload = this.$refs?.refUpload?.filesTotalSize;
			if (refUpload < 1 && this.node === '3') {
				this.$message.warning('请上传结题验收表');
				return;
			}
			if (!this.formData.isPass && this.node === '3') {
				this.$message.warning('请选择结题是否通过');
				return;
			}
			const loading = this.load('提交中...');
			let tableData = this.tableData.map(item => {
				return {
					name: item.achievementType.shortName, //成果类型中文
					type: item.achievementType.cciValue, //成果类型编码
					id: item.id //附件id
				};
			});
			let formData = {
				...this.formData,
				projectId: this.projectId,
				achievements: tableData //结题申请列表
			};
			// projectClassify->项目分类（字典编码：  project_classify->0：纵向；1：横向；2：院级）
			let url = this.basics.save;
			this.$.ajax({
				url: url,
				method: 'post',
				data: {
					...formData
				},
				loading,
				format: false
			}).then(res => {
				if (res.rCode == 0) {
					if (callBank) {
						callBank();
					} else {
						this.$message.success(res.msg);
						this.handleSuccess();
					}
				} else {
					this.$message.warning(res.msg);
				}
			});
		},
		// 提交成功
		handleSuccess(e) {
			//pendingId则为流程的审核页面，否则是弹窗的流程
			const isFlow = window.location.href.includes('pendingId');
			if (isFlow) {
				window.close();
			} else {
				this.$emit('visible', false);
				this.$emit('update:visible', false);
			}
		},
		delTableRow(index) {
			this.$delete(this.tableData, index);
		},

		// 页面加载
		load(text) {
			return this.$.loading(this.$loading, text);
		},
		async getPorjectInfo() {
			this.toolFormReadonly();
			if (this.title.includes('新增')) {
				this.formData = {
					id: this.id
				};
				return;
			}
			const loading = this.load('加载中...');
			try {
				let { rCode, msg, results } = await this.$.ajax({
					url: this.basics.info,
					method: 'get',
					params: {
						id: this.id
					}
				});
				if (rCode == 0) {
					let obj = results;
					this.setTableData(obj || {});
					this.toolFormReadonly();
				} else {
					this.$message.error(msg);
				}
			} finally {
				loading.close();
			}
		},
		setTableData(obj) {
			let formData = { ...obj };
			this.tableData = obj.achievements.map(item => {
				return {
					id: item.id,
					achievementType: {
						cciValue: item.type,
						shortName: item.name
					}
				};
			});
			this.formData = formData;
		}
	}
};
</script>

<style lang="scss" scoped>
@import '@ast/style/public.scss';
.main {
	$--border-base: 1px solid #d1d1d1;
	$--border-color: #d1d1d1;
	// @include flexBox();
	// flex-direction: column;
	// width: 100%;
	height: 620px;
	overflow: auto;
	padding: 0 8px 10px;
	position: relative;
	.btn-box {
		width: 100%;
		text-align: end;
		padding: 12px 6px;
	}
	.btn-save {
		position: absolute;
		bottom: 12px;
		right: 12px;
	}
	.download {
		cursor: pointer;
		color: #107fc9;
	}
	.basic-info {
		// width: 100%;
		.form-title {
			background-color: #f8f8f8;
			// border-color: #e1e1e1;
			border: 1px solid #e1e1e1;
			border-bottom: 0;
			font-size: 14px;
			font-weight: bold;
			color: #747474;
			display: flex;
			align-items: center;
			justify-content: center;
			height: 40px;
			width: 100%;
		}
		.is-table {
			padding: 12px 12px 24px;
		}
	}
	.table-box {
		max-height: 400px;
		overflow: auto;
		.row {
			@include flexBox();
			width: 100%;
			height: 80px;
			border: 1px solid $--border-color;
			& > div {
				height: 100%;
				// line-height: 50px;
				// text-align: center;
				padding: 0 10px;
				@include flexBox();

				&:nth-of-type(1) {
					border-right: 1px solid $--border-color;
					width: 180px;
					padding: 0 10px;
				}
				&:nth-of-type(2) {
					@include flexBox(flex-start);
					border-right: 1px solid $--border-color;
					flex: 1;
				}
				&:nth-of-type(3) {
					width: 100px;
				}
			}
		}

		.is-head {
			height: 50px;
			font-weight: 550;
		}
		.btn {
			.el-button {
				width: 26px;
			}
		}
	}
	.form-btn {
		display: flex;
		justify-content: flex-end;
		margin: 0 auto;
		padding: 5px 5px;
		//position: fixed;
		//right: 48.5%;
		//z-index: 10;
		//bottom: 10px;
	}
}
::v-deep .upload-box,
.es-upload {
	width: 100%;
	.el-button--medium {
		padding: 2px 6px;
		font-size: 12px;
	}
	.el-upload-list {
		// margin-top: -15px !important;
		.el-upload-list__item-name {
			width: auto;
			top: 0px;
			text-align: center;
			height: 37.7px;
			line-height: 39.7px;
		}
	}
	table {
		border-collapse: collapse; /* 合并单元格边框，避免双线 */
		border-spacing: 0; /* 当 border-collapse 不起作用时，可以尝试这个 */
	}

	table,
	th,
	td {
		border: none; /* 移除表格、表头和单元格的边框 */
	}
}
::v-deep .el-dialog__header {
	height: 44px;
}
::v-deep .es-form-content {
	padding: 0 !important;
}
::v-deep .es-table-form {
	width: 100%;
	.es-table-form-label {
		text-align: right;
		color: #747474;
		font-weight: 550;
	}
}
::v-deep .el-collapse-item__content {
	padding-bottom: 0px;
}
::v-deep .cell {
	color: #747474;
}
</style>
