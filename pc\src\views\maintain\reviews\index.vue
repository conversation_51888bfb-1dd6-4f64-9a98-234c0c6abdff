<template>
	<div class="content" style="height: 100%">
		<div style="width: 100%; height: 100%">
			<es-data-table
				v-if="true"
				ref="table"
				:key="tableCount"
				:row-style="tableRowClassName"
				:full="true"
				:fit="true"
				:thead="thead"
				:toolbar="toolbar"
				:page="page"
				:url="dataTableUrl"
				:param="dataTableParam"
				:border="true"
				:numbers="true"
				form
				@btnClick="btnClick"
			></es-data-table>
		</div>
	</div>
</template>

<script>
import api from '@/http/maintain/applicationApi';

export default {
	components: {},
	data() {
		return {
			page: {
				pageSize: 20,
				totalCount: 0
			},

			tableCount: 1,
			dataTableUrl: api.getlistOfReview,
			dataTableParam: { orderBy: 'create_time', asc: false },
			thead: [],
			toolbar: [],
			editToolbar: [
				{
					type: 'search',
					reset: true,
					contents: [
						{
							type: 'text',
							name: 'repairCode',
							placeholder: '编号',
							clearable: true
						},
						{
							type: 'text',
							name: 'createUserName',
							placeholder: '评价人',
							clearable: true
						},
						{
							type: 'date',
							name: 'reviewDate',
							placeholder: '评价时间',
							clearable: true
						}
					]
				}
			],
			listThead: [
				{
					title: '编号',
					align: 'center',
					width: '160px',
					field: 'repairCode',
					showOverflowTooltip: true
				},
				{
					title: '满意程度',
					align: 'center',
					width: '120px',
					field: 'attitudeName',
					showOverflowTooltip: true
				},
				{
					title: '维修服务打分',
					width: '240px',
					align: 'center',
					field: 'serviceScore',
					showOverflowTooltip: true,
					render: (h, params) => {
						return h(
							'el-rate',
							{ props: { value: Number(params.row.serviceScore), 'show-score': true, disabled: true } },
							params.row.serviceScore
						);
					}
				},
				{
					title: '评价内容',
					align: 'center',
					field: 'remark',
					showOverflowTooltip: true
				},
				{
					title: '评价人',
					align: 'center',
					width: '160px',
					field: 'createUserName',
					showOverflowTooltip: true
				},
				{
					title: '评价时间',
					align: 'center',
					width: '200px',
					field: 'createTime',
					showOverflowTooltip: true
				}
			],
			btnJson: {
				title: '操作',
				type: 'handle',
				width: 120,
				template: '',
				events: [
					{
						code: 'delete',
						text: '删除'
					}
				]
			}
		};
	},
	created() {
		//初始化查询列表
		this.thead = this.getListThead(this.btnJson);
		this.toolbar = this.editToolbar;
	},
	methods: {
		/**
		 * 表格行高
		 */
		tableRowClassName() {
			return {
				height: '54px !important'
			};
		},
		// 操作按钮
		btnClick(res) {
			let code = res.handle.code;
			switch (code) {
				case 'delete':
					this.handleDelete(res.row.id);
					break;
				default:
					break;
			}
		},
		handleDelete(id) {
			this.$confirm('确定要永久删除该数据吗？', '删除', {
				confirmButtonText: '确定',
				cancelButtonText: '取消',
				type: 'warning'
			})
				.then(() => {
					this.$request({
						url: api.delReviewById,
						data: { id: id },
						method: 'POST'
					}).then(res => {
						if (res.rCode === 0) {
							this.$message.success('操作成功！');
							this.$refs.table.reload();
						} else {
							this.$message.error(res.msg);
						}
					});
				})
				.catch(() => {});
		},
		getListThead(btnJson) {
			let tempThead = [...this.listThead];
			tempThead.push(btnJson);
			return tempThead;
		}
	}
};
</script>
