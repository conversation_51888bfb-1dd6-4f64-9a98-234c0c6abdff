<template>
	<div v-loading="loading">
		<es-form
			ref="form"
			:model="formData"
			:contents="formItemList"
			:genre="2"
			label-width="140px"
			height="620px"
			collapse
			@submit="handleFormSubmit"
			@reset="cancel"
		/>
	</div>
</template>
<script>
import api from '@/http/maintain/applicationApi';
import SnowflakeId from 'snowflake-id';

export default {
	props: {
		id: {
			type: String
		}
	},
	data() {
		return {
			loading: false,
			ownId: null,
			formData: {}
		};
	},
	computed: {
		formItemList() {
			return [
				{
					name: 'repairCode',
					label: '维修单编号',
					placeholder: '维修单编号',
					readonly: true,
					col: 6
				},
				{
					name: 'repairTypeName',
					label: '报修类别',
					readonly: true,
					col: 6
				},
				{
					label: '报修人',
					name: 'reportUser',
					readonly: true,
					col: 6
				},
				{
					label: '报修电话',
					name: 'repairPhone',
					readonly: true,
					col: 6
				},
				{
					name: 'createTime',
					label: '报修时间',
					readonly: true,
					col: 6
				},
				{
					type: 'datetime',
					name: 'expectDate',
					label: '期待维修时间',
					readonly: true,
					col: 6
				},
				{
					name: 'addressTypeName',
					label: '报修地点',
					readonly: true,
					col: 6
				},
				{
					label: '详细地址',
					name: 'address',
					readonly: true,
					col: 12
				},
				{
					label: '报修描述',
					type: 'textarea',
					name: 'reportContent',
					readonly: true,
					rows: 5
				},
				{
					label: '报修图片',
					name: 'reportImage',
					type: 'attachment',
					readonly: true,
					code: 'hq_report_image',
					'select-type': 'icon-plus',
					preview: true,
					listType: 'picture-card',
					ownId: this.id // 业务id
				},
				{
					type: 'datetime',
					name: 'esCompletionDate',
					label: '预计维修完成时间',
					placeholder: '预计维修完成时间',
					rules: {
						required: true,
						message: '请选择预计维修完成时间',
						trigger: 'blur'
					},
					col: 6
				},
				{
					label: '紧急程度',
					type: 'radio',
					name: 'urgency',
					sysCode: 'hq_urgency',
					'label-key': 'shortName',
					'value-key': 'cciValue',
					readonly: true,
					col: 6
				},
				{
					label: '紧急原因',
					name: 'urgencyReason',
					readonly: true,
					rows: 5
				},
				{
					label: '维修员类型',
					name: 'repairUserTypeName',
					readonly: true,
					col: 6
				},
				{
					label: '维修员',
					name: 'repairUserName',
					readonly: true,
					col: 6
				},
				{
					type: 'submit',
					skin: 'lay-form-btns',
					contents: [
						{ type: 'primary', text: '确定', event: this.handleFormSubmit },
						{ type: 'reset', text: '取消', event: this.cancel }
					]
				}
			];
		}
	},
	watch: {},
	created() {
		this.getInfo();
	},
	methods: {
		getInfo() {
			this.loading = true;
			this.$request({ url: api.getApplicationInfo + '/' + this.id, method: 'GET' })
				.then(res => {
					this.loading = false;
					if (res.rCode === 0) {
						this.formData = res.results;
						this.ownId = this.id;
					} else {
						this.$message.error(res.msg);
					}
				})
				.catch(e => {
					this.loading = false;
				});
		},
		handleFormSubmit() {
			//校验
			this.$refs.form.validate(valid => {
				if (valid) {
					let apiUrl = api.updateEsCompletionDate;
					const snowflake = new SnowflakeId();
					let updateData = {
						id: snowflake.generate(),
						reportId: this.id,
						esCompletionDate: this.formData.esCompletionDate
					};
					this.$request({
						url: apiUrl,
						data: updateData,
						method: 'POST'
					}).then(res => {
						if (res.rCode === 0) {
							this.$message.success('操作成功');
							this.$emit('closeEditSendOrderPage', 'success');
						} else {
							this.$message.error(res.msg);
						}
					});
				}
			});
		},
		cancel() {
			this.$emit('closeEditSendOrderPage', 'cancel');
		}
	}
};
</script>
