<template>
	<div class="content">
		<es-data-table
			ref="table"
			:row-style="tableRowClassName"
			:full="true"
			:fit="true"
			:thead="thead"
			:toolbar="toolbar"
			:border="true"
			:page="pageOption"
			:url="dataTableUrl"
			:option-data="optionData"
			:numbers="true"
			:param="params"
			close
			form
			@btnClick="btnClick"
			@sort-change="sortChange"
			@submit="hadeSubmit"
		></es-data-table>
		<es-dialog
			:title="formTitle"
			:visible.sync="showForm"
			width="1400px"
			height="740px"
			:drag="false"
			:close-on-click-modal="false"
			:destroy-on-close="true"
		>
			<div v-if="showForm" style="margin: 10px">
				<el-form
					ref="form"
					:inline="false"
					label-position="right"
					label-width="110px"
					:model="formData"
					:rules="formRules"
				>
					<el-row>
						<el-col :span="6">
							<el-form-item v-if="formData.type === '1'" label="采购单号" prop="purchaseCode">
								<el-select
									v-model="formData.purchaseCode"
									placeholder="请选择采购单号"
									filterable
									remote
									@change="changePurchase"
								>
									<el-option
										v-for="(item, index) in receiptOptions"
										:key="index"
										:label="item.code"
										:value="item.code"
									></el-option>
								</el-select>
							</el-form-item>
						</el-col>
						<el-col :span="6">
							<el-form-item label="库房" prop="storeroomId">
								<el-select
									v-model="formData.storeroomId"
									placeholder="请选择库房"
									@change="changeStoreroom"
									:disabled="formData.type === '1'"
								>
									<el-option
										v-for="item in storeroomOptions"
										:key="item.id"
										:label="item.name"
										:value="item.id"
									></el-option>
								</el-select>
							</el-form-item>
						</el-col>
						<el-col :span="6">
							<el-form-item label="入库时间" prop="receiptTime">
								<el-date-picker
									v-model="formData.receiptTime"
									type="datetime"
									value-format="yyyy-MM-dd HH:mm:ss"
									placeholder="选择入库时间"
								></el-date-picker>
							</el-form-item>
						</el-col>
						<el-col :span="6">
							<el-form-item label="入库人" prop="receiptPerson">
								<el-input
									v-model="formData.receiptPerson"
									placeholder="请输入入库人"
									maxlength="30"
								></el-input>
							</el-form-item>
						</el-col>
						<el-col :span="6">
							<el-form-item label="质检员" prop="administratorId">
								<el-select v-model="formData.administratorId" placeholder="请选择质检员">
									<el-option
										v-for="item in administratorList"
										:key="item.value"
										:label="item.label"
										:value="item.value"
									></el-option>
								</el-select>
							</el-form-item>
						</el-col>
						<el-col :span="6">
							<el-form-item label="库房管理员" prop="storeKeeperId">
								<el-select v-model="formData.storeKeeperId" placeholder="请选择库房管理员" disabled>
									<el-option
										v-for="item in storeKeeperList"
										:key="item.value"
										:label="item.label"
										:value="item.value"
									></el-option>
								</el-select>
							</el-form-item>
						</el-col>
					</el-row>
				</el-form>
				<el-button
					v-if="formData.type !== '1'"
					size="small"
					style="background-color: #0076e9; border-color: #0076e9; color: #ffffff"
					@click="showMaterial = true"
				>
					添加原料
				</el-button>
				<div style="display: flex; flex-direction: column; align-items: center;">
					<h3 style="margin-bottom: 10px">原料单</h3>
					<el-table
						:data="formData.details"
						border
						size="mini"
						style="width: 100%"
						max-height="260px"
						v-loading="loading"
					>
						<el-table-column label="序号" type="index"></el-table-column>
						<el-table-column
							prop="code"
							label="原料编号"
							min-width="80"
							:show-overflow-tooltip="true"
						></el-table-column>
						<el-table-column
							prop="name"
							label="原料名称"
							min-width="100"
							:show-overflow-tooltip="true"
						></el-table-column>
						<el-table-column prop="unit" label="单位" min-width="60"></el-table-column>
						<el-table-column prop="specification" label="规格" min-width="60"></el-table-column>
						<el-table-column prop="brand" label="供应商"></el-table-column>
						<el-table-column
							v-if="formData.type === '1'"
							prop="orderNum"
							label="预计入库量"
							min-width="60"
						></el-table-column>
						<el-table-column prop="num" label="入库量" width="130">
							<template slot-scope="scope">
								<el-input-number
									v-model="scope.row.num"
									:controls="false"
									:min="0"
									size="small"
									style="width: 100px"
								></el-input-number>
							</template>
						</el-table-column>
						<el-table-column prop="opt" label="操作" width="60px">
							<template slot-scope="scope">
								<i
									class="el-icon-remove"
									style="color: red; cursor: pointer"
									@click="removeDetail(scope.$index, scope.row)"
								></i>
							</template>
						</el-table-column>
					</el-table>
				</div>
				<h3 style="margin: 20px 0 10px;text-align: center;">检疫记录</h3>
				<el-form
					ref="form2"
					label-position="right"
					label-width="110px"
					:model="formData"
					style="margin-top: 20px;"
				>
					<el-row>
						<el-col :span="6">
							<el-form-item label="检疫人员" prop="quarantinePersonnel">
								<el-input
									v-model="formData.quarantinePersonnel"
									placeholder="请输入检疫人员"
									maxlength="25"
								></el-input>
							</el-form-item>
						</el-col>
						<el-col :span="6">
							<el-form-item label="检疫时间" prop="quarantineTime">
								<el-date-picker
									v-model="formData.quarantineTime"
									type="datetime"
									value-format="yyyy-MM-dd HH:mm:ss"
									placeholder="请输入检疫时间"
								></el-date-picker>
							</el-form-item>
						</el-col>
						<el-col :span="12">
							<el-form-item label="检疫结果" prop="quarantineResults">
								<el-input
									v-model="formData.quarantineResults"
									type="textarea"
									placeholder="请输入检疫结果"
									maxlength="500"
									style="width: 100%;"
								></el-input>
							</el-form-item>
						</el-col>
					</el-row>
					<el-row>
						<el-col :span="6">
							<el-form-item label="处理日期" prop="processingDate">
								<el-date-picker
									v-model="formData.processingDate"
									type="date"
									value-format="yyyy-MM-dd"
									placeholder="请输入处理日期"
								></el-date-picker>
							</el-form-item>
						</el-col>
						<el-col :span="6">
							<el-form-item label="处理措施" prop="handlingMeasures">
								<el-input
									v-model="formData.handlingMeasures"
									type="textarea"
									placeholder="请输入处理措施"
									maxlength="500"
								></el-input>
							</el-form-item>
						</el-col>
						<el-col :span="12">
							<el-form-item label="处理结果" prop="processingResults">
								<el-input
									v-model="formData.processingResults"
									type="textarea"
									placeholder="请输入处理结果"
									maxlength="500"
								></el-input>
							</el-form-item>
						</el-col>
					</el-row>
				</el-form>
				<div
					style="
						display: flex;
						flex-direction: row;
						justify-content: center;
						padding-top: 15px;
						padding-right: 20px;
					"
				>
					<el-button
						size="small"
						style="background-color: #0076e9; border-color: #0076e9; color: #ffffff"
						@click="save('0')"
					>
						暂存
					</el-button>
					<el-button
						size="small"
						style="background-color: #0076e9; border-color: #0076e9; color: #ffffff"
						@click="save('1')"
					>
						提交
					</el-button>
					<el-button
						size="small"
						style="background-color: #f5f5f5; border-color: #d9d9d9; color: #000000"
						@click="showForm = false"
					>
						取消
					</el-button>
				</div>
			</div>
		</es-dialog>

		<es-dialog
			title="原料列表"
			:visible.sync="showMaterial"
			width="60%"
			:drag="false"
			:close-on-click-modal="false"
			:middle="true"
		>
			<Material v-if="showMaterial" :visible.sync="showMaterial" @selected="selMaterial" />
		</es-dialog>
		<es-dialog
			title="查看"
			:visible.sync="showViewForm"
			width="1400px"
			height="740px"
			:drag="false"
			:close-on-click-modal="false"
			:destroy-on-close="true"
		>
			<div v-if="showViewForm" style="margin: 10px">
				<el-form :inline="true" label-position="right" label-width="120px" :model="formData">
					<el-row>
						<el-col :span="8">
							<el-form-item v-if="formData.type === '1'" label="采购单号：">
								<span>{{ formData.purchaseCode }}</span>
							</el-form-item>
						</el-col>
						<el-col :span="8">
							<el-form-item label="库房：">
								<span>{{ formData.storeroomId | storeroomFilter }}</span>
							</el-form-item>
						</el-col>
						<el-col :span="8">
							<el-form-item label="入库时间：">
								<span>{{ formData.receiptTime }}</span>
							</el-form-item>
						</el-col>
						<el-col :span="8">
							<el-form-item label="入库人：">
								<span>{{ formData.receiptPerson }}</span>
							</el-form-item>
						</el-col>
						<el-col :span="8">
							<el-form-item label="质检员：">
								<span>{{ formData.administratorId | administratorFilter }}</span>
							</el-form-item>
						</el-col>
						<el-col :span="8">
							<el-form-item label="库房管理员：">
								<span>{{ formData.storeKeeperId | storeKeeperFilter }}</span>
							</el-form-item>
						</el-col>
					</el-row>

				</el-form>
				<div style="display: flex; flex-direction: column; align-items: center;">
					<h3 style="margin-bottom: 10px">原料单</h3>
					<el-table :data="formData.details" border size="mini" style="width: 100%" height="350px">
						<el-table-column label="序号" type="index"></el-table-column>
						<el-table-column
							prop="code"
							label="原料编号"
							min-width="100"
							:show-overflow-tooltip="true"
						></el-table-column>
						<el-table-column
							prop="name"
							label="原料名称"
							min-width="100"
							:show-overflow-tooltip="true"
						></el-table-column>
						<el-table-column prop="unit" label="单位"></el-table-column>
						<el-table-column prop="specification" label="规格"></el-table-column>
						<el-table-column prop="brand" label="供应商" min-width="140"></el-table-column>
						<el-table-column prop="num" label="入库量" width="130"></el-table-column>
					</el-table>
				</div>
				<h3 style="margin: 30px 0 10px;text-align: center;">检疫记录</h3>
				<el-form :inline="true" label-position="right" label-width="120px" :model="formData">
					<el-row>
						<el-col :span="8">
							<el-form-item label="检疫人员：">
								<span>{{ formData.quarantinePersonnel }}</span>
							</el-form-item>
						</el-col>
						<el-col :span="8">
							<el-form-item label="检疫时间：">
								<span>{{ formData.quarantineTime }}</span>
							</el-form-item>
						</el-col>
						<el-col :span="8">
							<el-form-item label="检疫结果：">
								<span>{{ formData.quarantineResults }}</span>
							</el-form-item>
						</el-col>
						<el-col :span="8">
							<el-form-item label="处理日期：">
								<span>{{ formData.processingDate }}</span>
							</el-form-item>
						</el-col>
						<el-col :span="8">
							<el-form-item label="处理措施：">
								<span>{{ formData.handlingMeasures }}</span>
							</el-form-item>
						</el-col>
						<el-col :span="8">
							<el-form-item label="处理结果：">
								<span>{{ formData.processingResults }}</span>
							</el-form-item>
						</el-col>
					</el-row>
				</el-form>
				<div
					style="display: flex; flex-direction: row; justify-content: flex-end; padding-top: 15px"
				>
					<el-button
						size="small"
						style="background-color: #f5f5f5; border-color: #d9d9d9; color: #000000"
						@click="showViewForm = false"
					>
						取消
					</el-button>
				</div>
			</div>
		</es-dialog>
		<es-dialog
			title="提交入库"
			:visible.sync="showSubmit"
			width="20%"
			:close-on-click-modal="false"
			:middle="true"
			:drag="false"
			height="140px"
		>
			<div>确定要提交该条数据吗</div>
			<div class="btn-box">
				<div class="btn theme" @click="ensureSubmit">确定</div>
				<div class="btn" @click="showSubmit = false">取消</div>
			</div>
		</es-dialog>
		<es-dialog
			title="删除"
			:visible.sync="showDelete"
			width="20%"
			:close-on-click-modal="false"
			:middle="true"
			:drag="false"
			height="140px"
		>
			<div>确定要删除该条数据吗</div>
			<div class="btn-box">
				<div class="btn theme" @click="deleteRow">确定</div>
				A
				<div class="btn" @click="showDelete = false">取消</div>
			</div>
		</es-dialog>
	</div>
</template>

<script>
import interfaceUrl from '@/http/logistics/receiptOrder.js';
import storeroomApi from '@/http/logistics/hqStoeroom.js';
import operatePersonApi from '@/http/logistics/operatePerson.js';
import SnowflakeId from 'snowflake-id';
import Material from '../components/material.vue';
import { host } from '../../../../config/config';

let that;
export default {
	components: { Material },

	filters: {
		storeroomFilter(val) {
			for (let item of that.storeroomOptions) {
				if (item.id === val) {
					return item.name;
				}
			}
			return '';
		},
		administratorFilter(val) {
			for (let item of that.administratorList) {
				if (item.value === val) {
					return item.label;
				}
			}
			return '';
		},
		storeKeeperFilter(val) {
			for (let item of that.storeKeeperList) {
				if (item.value === val) {
					return item.label;
				}
			}
			return '';
		}
	},
	data() {
		return {
			ownId: '',
			dataTableUrl: interfaceUrl.listJson,
			showForm: false,
			showViewForm: false,
			showDelete: false,
			formTitle: '编辑',

			loading: false,

			storeroomOptions: [], //库房选择列表
			receiptOptions: [], //采购入库单列表(用于采购单号选择)

			//质检员、库房管理员
			administratorList: [],
			storeKeeperList: [],

			editBtn: {
				type: 'submit',
				skin: 'lay-form-btns',
				contents: [
					{
						type: 'primary',
						text: '确定',
						event: 'confirm'
					},
					{
						type: 'reset',
						text: '取消',
						event: 'cancel'
					}
				]
			},
			cancelBtn: {
				type: 'submit',
				skin: 'lay-form-btns',
				contents: [
					{
						type: 'reset',
						text: '取消',
						event: 'cancel'
					}
				]
			},
			thead: [
				{
					title: '库房名称',
					align: 'left',
					field: 'storeroomName',
					sortable: 'custom',
					showOverflowTooltip: true
				},
				{
					title: '入库单号',
					align: 'left',
					field: 'receiptCode',
					sortable: 'custom',
					showOverflowTooltip: true
				},
				{
					title: '采购单号',
					align: 'left',
					field: 'purchaseCode',
					sortable: 'custom',
					showOverflowTooltip: true
				},
				{
					title: '入库类型',
					align: 'left',
					field: 'type',
					sortable: 'custom',
					showOverflowTooltip: true
				},
				{
					title: '入库日期',
					align: 'left',
					field: 'receiptTime',
					sortable: 'custom',
					showOverflowTooltip: true
				},

				{
					title: '入库状态',
					align: 'center',
					field: 'status',
					sortable: 'custom',
					type: 'switch',
					readonly: true
				},
				{
					title: '操作',
					type: 'handle',
					width: 180,
					template: '',
					events: [
						{
							code: 'view',
							text: '查看'
						},
						{
							code: 'edit',
							text: '编辑',
							rules: rows => {
								return rows.status === '0';
							}
						},
						{
							code: 'submit',
							text: '提交',
							rules: rows => {
								return rows.status === '0';
							}
						},
						{
							code: 'download',
							text: '导出'
						}
					]
				}
			],
			optionData: {
				type: [
					{ name: '采购入库', value: 1 },
					{ name: '临时入库', value: 2 }
				],
				status: [
					{ name: '待提交', value: 0 },
					{ name: '已入库', value: 1 }
				]
			},
			pageOption:true,
			params: {
				asc: false,
				orderBy: 'create_time'
			},
			formData: {
				type: null,
				purchaseCode: null,
				supplierId: null,
				storeroomId: null,
				receiptTime: null,
				receiptPerson: null,
				administratorId: null,
				storeKeeperId: null,
				details: []
			},
			formRules: {
				purchaseCode: [{ required: true, message: '请填写采购单号', trigger: 'blur' }],
				storeroomId: [{ required: true, message: '请选择库房', trigger: 'change' }],
				receiptTime: [{ required: true, message: '请选择入库时间', trigger: 'blur' }],
				receiptPerson: [{ required: true, message: '请填写入库人姓名', trigger: 'blur' }],
				administratorId: [{ required: true, message: '请选择质检员', trigger: 'change' }],
				storeKeeperId: [{ required: true, message: '请选择库房管理员', trigger: 'change' }]
			},

			curMaterialIds: [], //当前选择的原料(用于重新选择时去重)
			showSubmit: false,
			showMaterial: false,

			curRowType: '1'
		};
	},
	computed: {
		toolbar() {
			return [
				{
					type: 'button',
					contents: [
						{
							text: '采购入库',
							code: 'add',
							type: 'primary'
						},
						{
							text: '临时入库',
							code: 'addTemp',
							type: 'primary'
						}
					]
				},
				{
					type: 'filter',
					reset: true,
					contents: [
						{
							type: 'text',
							name: 'receiptCode',
							label: '入库单号',
							clearable: true,
							col: 4
						},
						// {
						// 	type: 'select',
						// 	name: 'supplierId',
						// 	label: '供应商',
						// 	data: this.supplierOptions,
						// 	'label-key': 'name',
						// 	'value-key': 'id',
						// 	clearable: true,
						// 	col: 4
						// },
						{
							type: 'select',
							name: 'storeroomId',
							label: '库房',
							data: this.storeroomOptions,
							'label-key': 'name',
							'value-key': 'id',
							clearable: true,
							col: 4
						},
						{
							type: 'date',
							label: '入库开始时间',
							name: 'start',
							clearable: true,
							col: 4,
							value: '',
							rules: {
								required: false,
								message: '请选择开始时间',
								trigger: 'change'
							}
						},
						{
							type: 'date',
							label: '入库结束时间',
							name: 'end',
							clearable: true,
							col: 4,
							value: '',
							rules: {
								required: false,
								message: '请选择开始结束时间',
								trigger: 'change'
							}
						},
						{
							type: 'select',
							name: 'type',
							label: '入库类型',
							data: [
								{ label: '采购入库', value: '1' },
								{ label: '临时入库', value: '2' }
							],
							clearable: true,
							col: 4
						}
					]
				}
			];
		}
	},
	created() {
		that = this;
		this.storeroomList();
		this.getSelectPersonList(1);
		this.getSelectPersonList(3);
	},
	mounted() {
		this.$request({
			url: '/ybzy/hqpurchaseorder/list',
			method: 'GET'
		}).then(res => {
			if (res.rCode !== 0) {
				this.$message.error(res.msg);
				return;
			}
			this.receiptOptions = res.results;
		});
	},
	methods: {
		storeroomList() {
			this.$request({
				url: storeroomApi.listAll,
				method: 'GET'
			}).then(res => {
				if (res.rCode === 0) {
					this.storeroomOptions = res.results;
				}
			});
		},
		/**
		 * 表格行高
		 */
		tableRowClassName({ row, rowIndex }) {
			let styleRes = {
				height: '54px !important'
			};
			return styleRes;
		},
		//获取相关人员下拉列表 类型：质检员：1 领料人：2 库房管理员：3
		getSelectPersonList(value) {
			this.$request({
				url: operatePersonApi.getSelectList,
				params: { type: value, status: 1 },
				method: 'GET'
			}).then(res => {
				if (res.rCode === 0) {
					if (value == 1) {
						this.administratorList = res.results;
					}
					if (value == 3) {
						this.storeKeeperList = res.results;
					}
				}
			});
		},
		inputChange(key, value) {},
		hadeSubmit(data) {},
		changeStoreroom(val) {
			if (val) {
				let storeroom = this.storeroomOptions.filter(v => v.id === this.formData.storeroomId);
				if (storeroom) {
					this.formData.storeKeeperId = storeroom[0].manager;
				}
			}
		},
		/**
		 * 操作按钮点击事件
		 * @param {*} res
		 */
		btnClick(res) {
			let code = res.handle.code;
			const snowflake = new SnowflakeId();
			switch (code) {
				case 'add':
					// 新增
					this.formTitle = '采购入库';
					this.ownId = snowflake.generate();
					this.curMaterialIds = [];
					this.formData = { id: this.ownId, type: '1', details: [] };
					this.showForm = true;
					break;
				case 'addTemp':
					// 新增
					this.formTitle = '临时入库';
					this.ownId = snowflake.generate();
					this.curMaterialIds = [];
					this.formData = { id: this.ownId, type: '2', details: [] };
					this.showForm = true;
					break;
				case 'edit':
					// 编辑
					this.formTitle = '编辑';
					this.ownId = res.row.id;
					this.$request({
						url: interfaceUrl.info + '/' + res.row.id,
						method: 'GET'
					}).then(res => {
						if (res.rCode === 0) {
							this.curMaterialIds = [];
							this.formData = res.results;
							let details = this.formData.details;
							for (let item of details) {
								this.curMaterialIds.push(item.materialId);
							}
							this.showForm = true;
						}
					});
					break;
				case 'view':
					this.formTitle = '查看';
					this.ownId = res.row.id;
					this.curRowType = res.row.type;
					this.$request({
						url: interfaceUrl.info + '/' + res.row.id,
						method: 'GET'
					}).then(res => {
						if (res.rCode === 0) {
							this.formData = res.results;
							this.showViewForm = true;
						}
					});
					break;
				case 'delete':
					this.deleteId = res.row.id;
					this.showDelete = true;
					break;
				case 'submit':
					this.ownId = res.row.id;
					this.showSubmit = true;
					break;
				case 'download':
					window.open(host + interfaceUrl.download + '?id=' + res.row.id, '_self');
					break;
				default:
					break;
			}
		},
		handleClose() {},
		handleFormItemChange() {},
		handleFormSubmit(data) {
			let formData = data;
			let url = '';
			if (this.formTitle === '采购入库' || this.formTitle === '临时入库') {
				url = interfaceUrl.save;
			} else {
				url = interfaceUrl.update;
			}
			this.$request({
				url: url,
				data: formData,
				method: 'POST',
				format: false
			}).then(res => {
				if (res.rCode === 0) {
					this.$message.success('操作成功');
					this.showForm = false;
					this.formData = {};
					this.$refs.table.reload();
				} else {
					this.$message.error(res.msg);
				}
			});
		},
		deleteRow() {
			this.$request({
				url: interfaceUrl.deleteBatchIds,
				data: { ids: this.deleteId },
				method: 'POST'
			}).then(res => {
				if (res.rCode === 0) {
					this.$refs.table.reload();
					this.$message.success('删除成功');
				} else {
					this.$message.error(res.msg);
				}
				this.showDelete = false;
			});
		},
		/**
		 * 排序变化事件
		 * @param {*} column
		 * @param {*} prop
		 * @param {*} order
		 */
		sortChange(column, prop, order) {
			if (column.order == 'ascending') {
				//升序
				this.params = {
					asc: 'true',
					orderBy: column.prop
				};
			} else if (column.order == 'descending') {
				//降序
				this.params = {
					asc: 'false',
					orderBy: column.prop
				};
			} else {
				//不排序
				this.params = {
					asc: 'false',
					orderBy: 'createTime'
				};
			}
			this.$refs.table.reload();
		},
		removeDetail(index, row) {
			this.formData.details.splice(index, 1);
			let i = this.curMaterialIds.indexOf(row.materialId);
			this.curMaterialIds.splice(i, 1);
		},

		storeroomList() {
			this.$request({
				url: storeroomApi.listAll,
				method: 'GET'
			}).then(res => {
				if (res.rCode === 0) {
					this.storeroomOptions = res.results;
				}
			});
		},
		// purchaseCodeQuery(keyword) {
		// 	if (keyword !== '') {
		// 		this.loading = true;
		// 		this.$request({
		// 			url: '/ybzy/hqpurchaseorder/list',
		// 			method: 'GET',
		// 			params: {
		// 				pageNum: 1,
		// 				pageSize: 10,
		// 				type: '1',
		// 				purchaseCode: keyword
		// 			}
		// 		}).then(res => {
		// 			this.loading = false;
		// 			if (res.rCode !== 0) {
		// 				this.$message.error(res.msg);
		// 				return;
		// 			}
		// 			this.receiptOptions = res.results.records;
		// 		});
		// 	} else {
		// 		this.receiptOptions = [];
		// 	}
		// },
		//原料选择
		selMaterial(rows) {
			const snowflake = new SnowflakeId();
			for (let row of rows) {
				let materialId = row.id;
				if (this.curMaterialIds.includes(materialId)) {
					continue;
				}
				this.curMaterialIds.push(materialId);
				let ownId = snowflake.generate();
				let createDetail = {
					id: ownId,
					code: row.code,
					name: row.name,
					unit: row.unit,
					num: 0,
					brand: row.brand,
					specification: row.specification,
					receiptOrderId: this.ownId,
					materialId: materialId
				};
				this.formData.details.push(createDetail);
			}
		},
		changePurchase(val) {
			if (val) {
				this.loading = true;
				let order = this.receiptOptions.filter(v => v.code === this.formData.purchaseCode);
				if (order) {
					this.formData.storeroomId = order[0].storeroom;
					//填充库房管理员
					let storeroom = this.storeroomOptions.filter(v => v.id === this.formData.storeroomId);
					if (storeroom) {
						this.formData.storeKeeperId = storeroom[0].manager;
					}
				}
				this.$request({
					url: '/ybzy/hqreceiptorder/purchase/' + val,
					method: 'GET'
				}).then(res => {
					this.loading = false;
					if (res.rCode !== 0) {
						this.$message.error(res.msg);
						return;
					}
					let details = res.results;
					let snowflake = new SnowflakeId();
					for (let item of details) {
						if (!item.id) {
							//还未保存过的退料查询记录,只有商品和订购信息
							item.id = snowflake.generate();
							item.receiptOrderId = this.ownId;
							// item.num = item.maxPurchaseNum;
							// item.max = item.maxPurchaseNum;
							item.orderNum = item.purchaseNum;
							item.num = item.purchaseNum;
						}
					}
					this.formData.details = details;
				});
			}
		},
		ensureSubmit() {
			this.$request({
				url: interfaceUrl.submit + '/' + this.ownId,
				method: 'GET'
			}).then(res => {
				if (res.rCode !== 0) {
					this.$message.error(res.msg);
					return;
				}
				this.$message.success('操作成功');
				this.$refs.table.reload();
				this.showSubmit = false;
			});
		},
		save(status) {
			this.$refs['form'].validate(valid => {
				if (!valid) {
					return;
				}
				let details = this.formData.details;
				if (details === null || details === undefined || details.length === 0) {
					this.$message.warning('请完善原料单');
					return;
				}
				for (let item of this.formData.details) {
					if (!item.num) {
						this.$message.warning('请填写入库量');
						return;
					}
				}
				this.formData.status = status;
				this.handleFormSubmit(this.formData);
			});
		}
	}
};
</script>
<style scoped lang="scss">
@import '../../../assets/style/style.scss';

.content {
	width: 100%;
	height: 100%;
}

.el-dialog__body {
	.btn-box {
		margin-top: 20px;
		display: flex;
		justify-content: flex-end;

		.btn {
			padding: 5px 10px;
			color: #666;
			border: 1px solid #eee;
			cursor: pointer;
			margin-right: 5px;
			border-radius: 4px;
		}
	}
}
</style>
