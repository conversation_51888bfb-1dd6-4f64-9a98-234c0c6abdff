<template>
	<BasicInfo
		:id="formId"
		:key="formId"
		:title="title"
		:basics="wd.basics"
		@visible="$emit('visible', false)"
	/>
</template>

<script>
import BasicInfo from './components/basic-info';
import { v4 as uuidv4 } from 'uuid';
import { mixinList } from './mixinList';
export default {
	components: {
		BasicInfo
	},
	mixins: [mixinList],
	props: {
		title: {
			type: String,
			default: '查看'
		}
	},
	computed: {
		formId() {
			const query = this.$route.query;
			return query?.recordId || query?.recordid || uuidv4();
		}
	}
};
</script>
