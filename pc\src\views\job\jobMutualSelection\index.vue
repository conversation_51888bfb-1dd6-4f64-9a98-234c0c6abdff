<template>
	<div v-loading="loading" class="content">
		<es-data-table
			ref="table"
			:key="tableCount"
			:row-style="tableRowClassName"
			:full="true"
			:fit="true"
			:thead="thead"
			:toolbar="toolbar"
			:border="true"
			:page="pageOption"
			:url="dataTableUrl"
			:numbers="true"
			:param="params"
			close
			form
			@btnClick="btnClick"
			@sort-change="sortChange"
		></es-data-table>
		<es-dialog
			v-if="showForm"
			:title="formTitle"
			:visible.sync="showForm"
			width="80%"
			height="92%"
			:close-on-click-modal="false"
			:destroy-on-close="true"
		>
			<resumeView
				:form-title="formTitle"
				:info="formData"
				@close="
					() => {
						$refs.table.reload();
						showForm = false;
					}
				"
			></resumeView>
		</es-dialog>
	</div>
</template>

<script>
import interfaceUrl from '@/http/job/jobDualSelect/api.js';
import resumeView from './components/view.vue';
import SnowflakeId from 'snowflake-id';
export default {
	components: { resumeView },
	data() {
		return {
			loading: false,
			dataTableUrl: interfaceUrl.listJsonSelection,
			tableCount: 1,
			showForm: false,
			params: {
				orderBy: 'createTime',
				asc: 'false'
			},
			toolbar: [
				{
					type: 'button',
					contents: [
						{
							text: '新增',
							code: 'add',
							icon: 'el-icon-circle-plus-outline',
							type: 'primary'
						}
					]
				},
				{
					type: 'search',
					contents: [{ type: 'text', name: 'keyword', placeholder: '标题关键字查询' }]
				}
			],
			thead: [
				{
					title: '标题',
					minWidth: 135,
					align: 'center',
					// showOverflowTooltip: true,
					field: 'title'
				},
				{
					title: '企业回复时间',
					align: 'center',
					field: 'replyStartEndDes'
				},
				{
					title: '双选会开展时间',
					align: 'center',
					field: 'meetingStartEndDes'
				},
				{
					title: '创建人',
					width: 90,
					align: 'center',
					field: 'createUserName'
				},
				{
					title: '创建时间',
					align: 'center',
					width: 170,
					field: 'createTime'
				},
				{
					title: '状态',
					align: 'center',
					width: 80,
					field: 'states',
					render: (h, data) => {
						return h(
							'el-tag',
							{
								props: { type: data.row.states === 0 ? 'warning' : 'success', size: 'small' }
							},
							data.row.states === 0 ? '草稿' : '已发布'
						);
					}
				},
				{
					title: '操作',
					type: 'handle',
					width: 210,
					template: '',
					align: 'center',
					events: [
						{ code: 'view', text: '查看', icon: 'el-icon-view' },
						{
							code: 'edit',
							text: '编辑',
							icon: 'el-icon-edit',
							rules: rows => {
								return true;
							}
						},
						{
							code: 'delete',
							text: '删除',
							icon: 'el-icon-delete',
							rules: rows => {
								return rows.states === 0;
							}
						},
						{
							code: 'back',
							text: '撤回',
							icon: 'el-icon-back',
							rules: rows => {
								return rows.isRecall === 'YES' && rows.states === 1;
							}
						}
					]
				}
			],
			pageOption: {
				layout: 'total, prev, pager, next, jumper',
				pageSize: 20,
				// hideOnSinglePage: true,
				position: 'right',
				current: 1,
				pageNum: 1
			},
			formData: {},
			formTitle: '查看'
		};
	},
	watch: {
		dataTableUrl() {
			this.tableCount++;
		}
	},
	methods: {
		//表格行高
		tableRowClassName({ row, rowIndex }) {
			let styleRes = { height: '54px !important' };
			return styleRes;
		},
		inputChange(key, value) {},
		//操作按钮点击事件
		btnClick(res) {
			let code = res.handle.code;
			switch (code) {
				case 'add':
					this.formTitle = '新增';
					this.formData = { id: new SnowflakeId().generate(), status: true };
					this.showForm = true;
					this.loading = false;
					break;
				case 'edit':
					this.formTitle = '编辑';
					this.loading = true;
					this.$request({ url: interfaceUrl.infoSelection + '/' + res.row.id, method: 'GET' }).then(
						res => {
							this.loading = false;

							if (res.rCode === 0) {
								this.formData = res.results;
								this.showForm = true;
							}
						}
					);
					break;
				case 'view':
					this.formTitle = '查看';
					this.loading = true;
					this.$request({ url: interfaceUrl.infoSelection + '/' + res.row.id, method: 'GET' }).then(
						res => {
							this.loading = false;

							if (res.rCode === 0) {
								this.formData = res.results;
								this.showForm = true;
							}
						}
					);
					break;
				case 'back':
					this.$confirm(`确定要撤回“${res.row.title}”吗？`, '提示', {
						confirmButtonText: '确定',
						cancelButtonText: '取消',
						type: 'warning'
					})
						.then(() => {
							this.loading = true;
							this.$request({
								url: interfaceUrl.recallSelection,
								data: { id: res.row.id },
								method: 'POST'
							}).then(res => {
								this.loading = false;
								if (res.rCode === 0) {
									this.$refs.table.reload();
									this.$message.success('撤回成功');
								} else {
									this.$message.error(res.msg);
								}
							});
						})
						.catch(() => {});

					break;
				case 'delete':
					// 确认提示
					this.$confirm(`确定要删除“${res.row.title}”吗？`, '提示', {
						confirmButtonText: '确定',
						cancelButtonText: '取消',
						type: 'warning'
					})
						.then(() => {
							this.loading = true;
							this.$request({
								url: interfaceUrl.deleteById,
								data: { id: res.row.id },
								method: 'POST'
							}).then(res => {
								this.loading = false;
								if (res.rCode === 0) {
									this.$refs.table.reload();
									this.$message.success('删除成功');
								} else {
									this.$message.error(res.msg);
								}
							});
						})
						.catch(() => {});
					break;
				default:
					break;
			}
		},
		//删除行
		deleteRow() {
			this.$request({
				url: interfaceUrl.delete,
				data: { id: this.deleteId },
				method: 'POST'
			}).then(res => {
				if (res.rCode === 0) {
					this.$refs.table.reload();
					this.$message.success('删除成功');
				} else {
					this.$message.error(res.msg);
				}
			});
		},
		//排序变化事件
		sortChange(column, prop, order) {
			let asc = this.params.asc;
			let orderBy = this.params.orderBy;
			if (column.order === 'ascending') {
				//升序
				asc = 'true';
				orderBy = column.prop;
			} else if (column.order === 'descending') {
				//降序
				asc = 'false';
				orderBy = column.prop;
			} else {
				//不排序
				asc = 'false';
				orderBy = column.prop;
			}
			this.params.asc = asc;
			this.params.orderBy = orderBy;
			this.$refs.table.reload();
		},
		refreshData() {
			this.$refs.table.reload();
		}
	}
};
</script>

<style scoped lang="scss">
@import '../../../assets/style/style.scss';
.content {
	width: 100%;
	height: 100%;

	//::v-deep .es-data-table {
	// flex: 1;
	// display: flex;
	// flex-direction: column;
	// height: calc(100% - 58px);

	// .es-data-table-content {
	// 	flex: 1;
	// 	height: 0;
	// 	display: flex;
	// 	flex-direction: column;

	//.el-table {
	// flex: 1;
	// height: 100% !important;
	//}

	// 	.es-thead-border {
	// 		.el-table__header {
	// 			th {
	// 				border-right: 1px solid #e1e1e1;
	// 			}
	// 		}
	// 	}
	// }
	//}

	// ::v-deep .el-form-item__label {
	// 	background: none;
	// 	border: 0px solid #c2c2c2;
	// }
}
</style>
