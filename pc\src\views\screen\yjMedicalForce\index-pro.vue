<template>
  <div class="content">
    <es-data-table ref="table" :row-style="tableRowClassName" :full="true" :fit="true" :thead="thead" :toolbar="toolbar"
                   :border="true" :page="pageOption" :url="dataTableUrl" :numbers="true" @btnClick="btnClick"
                   @sort-change="sortChange" :param="params" @submit="hadeSubmit"
                   close form></es-data-table>
    <es-dialog :title="formTitle" :visible.sync="showForm" width="60%" :close-on-click-modal="false"
               :destroy-on-close="true">
      <es-form ref="form" :model="formData" :contents="formItemList" height="500px" :genre="2" collapse
               @change="inputChange" @submit="handleFormSubmit" @reset="showForm = false" />
    </es-dialog>
    <es-dialog title="删除" :visible.sync="showDelete" width="20%" :close-on-click-modal="false" :middle="true"
               height="140px">
      <div>确定要删除该条数据吗</div>
      <div class="btn-box">
        <div class="btn theme" @click="deleteRow">确定</div>
        <div class="btn" @click="showDelete = false">取消</div>
      </div>
    </es-dialog>
  </div>
</template>

<script>
import interfaceUrl from '@/http/screen/yjMedicalForce';
import SnowflakeId from "snowflake-id";
export default {
  name: "yjMedicalForce",//医疗力量
  data() {
    return {
      ownId: '',
      dataTableUrl: interfaceUrl.listJson,
      showForm: false,
      showDelete: false,
      formData: {},
      formTitle: '编辑',
      editBtn: {
        type: 'submit',
        skin: 'lay-form-btns',
        contents: [
          {
            type: 'primary',
            text: '确定',
            event: 'confirm'
          },
          {
            type: 'reset',
            text: '取消',
            event: 'cancel'
          },
        ]
      },
      cancelBtn: {
        type: 'submit',
        skin: 'lay-form-btns',
        contents: [
          {
            type: 'reset',
            text: '取消',
            event: 'cancel'
          },
        ]
      },
      toolbar: [
        {
          type: 'button',
          contents: [
            {
              text: '新增',
              code: 'add',
              type: 'primary'
            }
          ]
        },
        {
          type: 'search',
          contents: [
            {
              type: 'text',
              name: 'keyword',
              placeholder: '关键字查询'
            },
          ]
        }
      ],
      thead: [
        {
          title: '姓名',
          align: 'left',
          field: 'name',
          sortable: 'custom',
          showOverflowTooltip: true,
        },
        {
          title: '职称',
          align: 'left',
          field: 'zhicheng',
          sortable: 'custom',
          showOverflowTooltip: true,
        },
        {
          title: '工龄',
          align: 'left',
          field: 'lengthService',
          sortable: 'custom',
          showOverflowTooltip: true,
        },
        {
          title: '所属单位',
          align: 'left',
          field: 'affiliatedUint',
          sortable: 'custom',
          showOverflowTooltip: true,
        },
        {
          title: '备注',
          align: 'left',
          field: 'remark',
          sortable: 'custom',
          showOverflowTooltip: true,
        },
        {
          title: '操作',
          type: 'handle',
          width: 180,
          template: '',
          events: [
            {
              code: 'edit',
              text: '编辑'
            },
            {
              code: 'view',
              text: '查看'
            },
            {
              code: 'delete',
              text: '删除'
            },
          ]
        }
      ],
      pageOption: {
        layout: 'total, prev, pager, next, jumper',
        pageSize: 10,
        // hideOnSinglePage: true,
        position: 'right',
        current: 1,
        pageNum: 1
      },
      params: {
        asc: "false",
        orderBy: "createTime"
      },
    };
  },
  computed: {
    formItemList() {
      return [
        {
          label: '姓名',
          name: 'name',
          placeholder: '请输入名称',
          event: 'multipled',
          rules: {
            required: true,
            message: '请输入名称',
            trigger: 'blur'
          },
          verify: 'required',
          col: 10
        },
        {
          label: '职称',
          name: 'zhicheng',
          placeholder: '请输入装备类型',
          event: 'multipled',
          rules: {
            required: true,
            message: '请输入装备类型',
            trigger: 'blur'
          },
          verify: 'required',
          col: 10
        },
        {
          label: '工龄',
          name: 'lengthService',
          placeholder: '请输入应用领域',
          event: 'multipled',
          rules: {
            required: true,
            message: '请输入应用领域',
            trigger: 'blur'
          },
          verify: 'required',
          col: 10
        },
        {
          label: '所属单位',
          name: 'affiliatedUint',
          placeholder: '请输入图片',
          event: 'multipled',
          rules: {
            required: true,
            message: '请输入图片',
            trigger: 'blur'
          },
          verify: 'required',
          col: 10
        },
        {
          name: 'remark',
          label: '备注',
          placeholder: '请输入备注',
          type: 'textarea',
          col: 10
        }
      ]
    },
  },
  watch: {

  },
  created() {

  },
  mounted() {

  },
  methods: {
    /**
     * 表格行高
     */
    tableRowClassName({ row, rowIndex }) {
      let styleRes = {
        "height": "54px !important"
      }
      return styleRes

    },
    inputChange(key, value) {

    },
    hadeSubmit(data) {

    },
    /**
     * 操作按钮点击事件
     * @param {*} res
     */
    btnClick(res) {
      let code = res.handle.code;
      switch (code) {
        case 'add':
          // 新增
          this.formTitle = '新增';
          this.editModule(this.formItemList);
          const snowflake = new SnowflakeId();
          let id = snowflake.generate();
          this.ownId = id;
          this.formData = { id: id, state: true, photoUrlTemp: null };
          this.showForm = true;
          break;
        case 'edit':
          // 编辑
          this.formTitle = '编辑';
          this.ownId = res.row.id;
          this.editModule(this.formItemList);
          this.$request({
            url: interfaceUrl.info + '/' + res.row.id,
            method: 'GET'
          }).then(res => {
            if (res.rCode == 0) {
              this.formData = res.results;
              if(undefined !== this.formData.photoUrl && null !== this.formData.photoUrl){
                this.formData.photoUrlTemp = '/main2/mecpfileManagement/previewAdjunct?adjunctId='+this.formData.photoUrl;
              }
              this.formData.state = 1 == this.formData.state ? true : false;
              this.formData.sex = this.formData.sex + '';
              this.showForm = true;
            }
          });
          break;
        case 'view':
          this.formTitle = '查看';
          this.ownId = res.row.id;
          this.readModule(this.formItemList);
          this.$request({
            url: interfaceUrl.info + '/' + res.row.id,
            method: 'GET'
          }).then(res => {
            if (res.rCode == 0) {
              this.formData = res.results;
              if(undefined !== this.formData.photoUrl && null !== this.formData.photoUrl){
                this.formData.photoUrlTemp = '/main2/mecpfileManagement/previewAdjunct?adjunctId='+this.formData.photoUrl;
              }
              this.formData.state = 1 == this.formData.state ? true : false;
              this.formData.sex = this.formData.sex + '';
              this.showForm = true;
            }
          });
          break;
        case 'delete':
          this.deleteId = res.row.id;
          this.showDelete = true;
          break;
        default:
          break;
      }
    },
    handleClose() { },
    handleFormItemChange() {

    },
    handleFormSubmit(data) {
      let formData = data;
      delete formData.extMap;
      let url = "";
      if (this.formTitle == '新增') {
        url = interfaceUrl.save;
        let Base64 = require('js-base64').Base64
        formData['password'] = Base64.encode(formData.pwd);
      } else {
        url = interfaceUrl.update;
      }
      formData.state = formData.state ? 1 : 0;
      if (undefined != formData.photoUrlTemp && undefined != formData.photoUrlTemp.response) {
        formData.photoUrl = formData.photoUrlTemp.response.adjunctId;
      }
      this.$request({
        url: url,
        data: formData,
        method: 'POST'
      }).then(res => {
        if (res.rCode == 0) {
          this.$message.success('操作成功');
          this.showForm = false;
          this.formData = {};
          this.$refs.table.reload();
        } else {
          this.formData.state = 1 == this.formData.state ? true : false;
          this.$message.error(res.msg);
        }
      });
    },
    deleteRow() {
      this.$request({
        url: interfaceUrl.deleteBatchIds,
        data: { ids: this.deleteId },
        method: 'POST'
      }).then(res => {
        if (res.rCode == 0) {
          this.$refs.table.reload();
          this.$message.success('删除成功');
        } else {
          this.$message.error(res.msg);
        }
        this.showDelete = false;
      });
    },
    /**
     * 排序变化事件
     * @param {*} column
     * @param {*} prop
     * @param {*} order
     */
    sortChange(column, prop, order) {
      if (column.order == 'ascending') {//升序
        this.params = {
          asc: "true",
          orderBy: column.prop
        }

      } else if (column.order == 'descending') {//降序
        this.params = {
          asc: "false",
          orderBy: column.prop
        }
      } else { //不排序
        this.params = {
          asc: "false",
          orderBy: "createTime"
        }
      }
      this.$refs.table.reload()

    },
    /**
     * 编辑模式
     */
    editModule(list) {
      for (var i in list) {
        var item = list[i];
        item.readonly = false;
      }
      list.push(this.editBtn);
    },
    /**
     * 只读模式
     */
    readModule(list, hideField) {
      for (var i in list) {
        var item = list[i];
        item.readonly = true;
      }
      list.push(this.cancelBtn);
    },
  }
};
</script>
<style scoped lang="scss">
@import '../../../assets/style/style.scss';

.content {
  display: flex;
  width: 100%;
  height: 100%;

  ::v-deep .es-data-table {
    flex: 1;
    display: flex;
    flex-direction: column;

    .es-data-table-content {
      flex: 1;
      height: 0;
      display: flex;
      flex-direction: column;

      .el-table {
        flex: 1;
        // height: 100% !important;
      }

      .es-thead-border {
        .el-table__header {
          th {
            border-right: 1px solid #E1E1E1;
          }
        }
      }
    }
  }

  ::v-deep .el-form-item__label {
    background: none;
    border: 0px solid #c2c2c2;
  }
}

.el-dialog__body {
  .btn-box {
    margin-top: 20px;
    display: flex;
    justify-content: flex-end;

    .btn {
      padding: 5px 10px;
      color: #666;
      border: 1px solid #eee;
      cursor: pointer;
      margin-right: 5px;
      border-radius: 4px;
      // &.theme {
      // 	background: $--color-primary;
      // 	color: #fff;
      // 	border-color: $--color-primary;
      // }
    }
  }


}
</style>
