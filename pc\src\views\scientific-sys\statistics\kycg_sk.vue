<template>
	<div v-loading="loading" class="content">
		<es-data-table
			ref="table"
			:row-style="tableRowClassName"
			:thead="thead"
			:toolbar="toolbar"
			:border="true"
			:page="pageOption"
			:url="basics.dataTableUrl"
			filter
			:numbers="true"
			show-label
			:param="params"
			close
			@search="search"
			@reset="search({})"
			@btnClick="btnClick"
		></es-data-table>
	</div>
</template>

<script>
import { host } from '@/../config/config';
import qs from 'qs';
export default {
	data() {
		return {
			basics: {
				dataTableUrl: '/ybzy/paBaseInfo/statisticsAcademicPaperSocial', // 列表接口on/deleteById'
				download: '/ybzy/paBaseInfo/exportPaperSocial' // 导出
			},
			loading: false,
			params: {},
			pageOption: {
				layout: 'total, prev, pager, next, jumper',
				pageSize: 20,
				// hideOnSinglePage: true,
				position: 'center',
				current: 1,
				pageNum: 1
			},
			codesObj: {} //批量数据字典
		};
	},
	computed: {
		thead() {
			return [
				{
					title: '成果名称',
					field: 'name',
					align: 'center',
					showOverflowTooltip: true,
					minWidth: 200
				},
				{
					title: '第一作者',
					field: 'firstAuthor',
					align: 'center',
					minWidth: 100
				},
				{
					title: '出版时间',
					field: 'periodicalPublishDate',
					align: 'center',
					minWidth: 100
				},
				{
					title: '出版、发行、使用单位',
					field: 'usingUnit',
					align: 'center',
					showOverflowTooltip: true,
					minWidth: 170
				},
				{
					title: '所属机构',
					field: 'orgName',
					align: 'center',
					minWidth: 100
				},
				{
					title: '一级学科',
					field: 'firstLevelDiscipline',
					align: 'center',
					minWidth: 100
				},
				{
					title: '成果形式',
					field: 'form',
					align: 'center',
					minWidth: 100
				},
				{
					title: '成果来源',
					field: 'source',
					align: 'center',
					showOverflowTooltip: true,
					minWidth: 120
				},
				{
					title: '发表范围',
					field: 'publishScope',
					align: 'center',
					showOverflowTooltip: true,
					minWidth: 120
				},
				{
					title: '研究类别',
					field: 'researchCategory',
					align: 'center',
					minWidth: 100
				},
				{
					title: '二级学科',
					field: 'subDiscipline',
					align: 'center',
					minWidth: 120
				},
				{
					title: '成果编号',
					field: 'achievementNum',
					align: 'center',
					minWidth: 120
				},
				{
					title: '成果英文名称',
					field: 'achievementEnglishName',
					align: 'center',
					showOverflowTooltip: true,
					minWidth: 120
				},
				{
					title: 'ISBN或ISSN号',
					field: 'serialNum',
					align: 'center',
					showOverflowTooltip: true,
					minWidth: 120
				},
				{
					title: '是否被使用单位采纳',
					field: 'isUnitAdopt',
					align: 'center',
					showOverflowTooltip: true,
					minWidth: 150
				},
				{
					title: '是否译成外文',
					field: 'isForeignLanguage',
					align: 'center',
					minWidth: 120
				},
				{
					title: '成果字数',
					field: 'wordCount',
					align: 'center',
					minWidth: 120
				},
				{
					title: '其他作者',
					field: 'otherAuthor',
					align: 'center',
					showOverflowTooltip: true,
					minWidth: 120
				},
				{
					title: '关键字',
					field: 'keyword',
					align: 'center',
					showOverflowTooltip: true,
					minWidth: 120
				},
				{
					title: '英文关键字',
					field: 'englishKeyword',
					align: 'center',
					showOverflowTooltip: true,
					minWidth: 120
				},
				{
					title: '成果摘要',
					field: 'textAbstract',
					align: 'center',
					showOverflowTooltip: true,
					minWidth: 120
				},
				{
					title: '成果引用采纳情况',
					field: 'adoptSituation',
					align: 'center',
					minWidth: 140
				}
			];
		},
		toolbar() {
			return [
				{
					type: 'button',
					contents: [
						{
							text: '导出',
							exportXls: true,
							icon: 'es-icon-daochu',
							type: 'primary',
							event: () => {
								const params = { ...this.params, ...this.selectParams };
								const url = host + this.basics.download + '?' + qs.stringify(params);
								window.open(url, '_self');
							}
						}
					]
				},
				{
					type: 'search',
					reset: true,
					contents: [
						{
							type: 'daterange',
							name: 'dates',
							label: '时间范围',
							placeholder: '请选择时间范围'
						},
						{ type: 'text', label: '关键字', name: 'keyword', placeholder: '请输入关键字查询' }
					]
				}
			];
		}
	},
	created() {
		const codes = 'member_nation,hq_person_education,hq_person_state';
		this.$utils.findSysCodeList(codes).then(res => {
			this.codesObj = res;
		});
	},
	methods: {
		//表格行高
		tableRowClassName({ row, rowIndex }) {
			let styleRes = { height: '54px !important' };
			return styleRes;
		},
		//操作按钮点击事件
		btnClick(res) {},
		search(res) {
			const paramsN = { ...res };
			if (paramsN.dates) {
				paramsN.startDate = paramsN.dates[0] || '';
				paramsN.endDate = paramsN.dates[1] || '';
				delete paramsN.dates;
			}
			this.params = {
				...{
					projectType: this.params.projectType,
					orderBy: this.params.orderBy,
					asc: this.params.asc // true 升序，false 降序
				},
				...paramsN
			};
			this.selectParams = paramsN;
		},
		refreshData() {
			this.$refs.table.reload();
		}
	}
};
</script>

<style scoped lang="scss">
.content {
	width: 100%;
	height: 100%;
}
</style>
