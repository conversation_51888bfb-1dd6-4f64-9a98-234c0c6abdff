import httpApi from './api.js';
export default {
	computed: {
		component() {
			return {
				// 树表结构
				//name: 'es-tree-group'
				// 纯列表结构
				name: 'es-data-table'
			};
		},
		treeParam() {
			return {
				// 不需要左边树 可以
				//url: httpApi.hrnPersonPostTree,
				//title: '分类树'
			};
		},
		table() {
			return {
				url: httpApi.hrnPersonPostList,
				param: { handlerType: '' },
				full: true,
				fit: true,
				page: true,
				stripe: true,
				numbers: true,
				'row-key': 'id',
				toolbar: [
					{
						type: 'button',
						contents: [
							{
								text: '新增',
								type: 'primary'
							}
						]
					},
					{
						type: 'search',
						reset: true,
						contents: [
							//{
							//	name: 'accountName_like',
							//	placeholder: '配置名称',
							//	label: '配置名称',
							//	col: 6
							//},
							{
								type: 'date',
								col: 6,
								name: 'updateTime_begin',
								label: '更新开始时间',
								placeholder: '更新开始时间'
							},
							{
								type: 'date',
								col: 6,
								name: 'updateTime_end',
								label: '更新结束时间',
								placeholder: '更新结束时间'
							}
						]
					}
				],
				thead: [
					{
						title: '基本信息ID',
						align: 'left',
						field: 'hrnPersonId'
					},
					{
						title: '部门/学院ID',
						align: 'left',
						field: 'orgId'
					},
					{
						title: '部门/学院',
						align: 'left',
						field: 'orgName'
					},
					{
						title: '岗位名称ID',
						align: 'left',
						field: 'postId'
					},
					{
						title: '岗位名称',
						align: 'left',
						field: 'postName'
					},
					{
						title: '岗位性质ID',
						align: 'left',
						field: 'postPropertyId'
					},
					{
						title: '岗位性质',
						align: 'left',
						field: 'postPropertyName'
					},
					{
						title: '岗位类别',
						align: 'left',
						field: 'postTypeName'
					},
					{
						title: '岗位类别ID',
						align: 'left',
						field: 'postTypeId'
					},
					{
						title: '岗位状态（已转岗，在任）',
						align: 'left',
						field: 'postStatus'
					},
					{
						title: '转岗ID',
						align: 'left',
						field: 'transferId'
					},
					{
						title: '状态',
						align: 'left',
						field: 'status'
					},
					{
						title: '操作',
						type: 'handle',
						width: 180,
						events: [
							{
								text: '查看'
							},
							{
								text: '编辑'
							},
							{
								text: '删除'
							}
						]
					}
				]
			};
		}
	}
};
