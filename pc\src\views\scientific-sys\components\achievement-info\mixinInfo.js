export const mixinInfo = {
	props: {
		// 动态化参数
		contentsKey: {
			type: String,
			// required: true
			default: ''
		}
	},
	computed: {
		wd() {
			const formReadonly = formReadonly;
			const formData = this.formData;
			const academicPaperType = formData.academicPaperType;
			const awardType = formData.awardType;

			if (typeof formData.thesisLevel === 'string') {
				formData.thesisLevel = formData.thesisLevel.split(',');
			} else if (!Array.isArray(formData.thesisLevel)) {
				formData.thesisLevel = [];
			}

			// formData.thesisLevel = formData.thesisLevel ? [formData.thesisLevel] : formData.thesisLevel;
			let obj = {
				basics: {}, // 基础配置
				contents: [] // 表单配置
			};
			switch (this.contentsKey) {
				// 学术论文 分社科、自然两种类型
				case 'academicPaper':
					obj = {
						// 基本配置
						basics: {
							info: '/ybzy/paAcademicPaper/info', // 详情接口
							save: '/ybzy/paAcademicPaper/save', // 新增
							edit: '/ybzy/paAcademicPaper/update', // 修改
							flowTypeCode: 'projectAchievement_etipnuuezknypsv', // 流程code
							defaultProcessKey: 'projectAchievement_etipnuuezknypsv' // 默认关联流程 key
						},
						// 列表配置
						contents: [
							{
								name: 'academicPaperType',
								label: '类型',
								type: 'select',
								data: this.codesObj.academic_type_code,
								'value-key': 'cciValue',
								'label-key': 'shortName',
								rules: {
									required: !formReadonly,
									message: '请选择',
									trigger: 'blur'
								},
								col: 6
							},
							{
								name: 'firstAuthor',
								placeholder: '请输入',
								label: '第一作者',
								rules: {
									required: !formReadonly,
									message: '请输入',
									trigger: 'blur'
								},
								col: 6
							},
							{
								name: 'name',
								placeholder: '请输入',
								label: '论文名称',
								rules: {
									required: !formReadonly,
									message: '请输入',
									trigger: 'blur'
								},
								col: 6
							},
							{
								name: 'subjectType',
								placeholder: '请选择',
								label: '学科门类',
								type: 'select',
								hide: academicPaperType !== '1',
								rules: {
									required: true,
									message: '请选择',
									trigger: 'blur'
								},
								data: this.codesObj.academicPaper.pa_subject_type,
								'value-key': 'cciValue',
								'label-key': 'shortName',
								clearable: true,
								col: 6
							},
							{
								name: 'isForeignPeriodical',
								placeholder: '请选择',
								label: '是否发表在国外学术刊物',
								type: 'select',
								hide: academicPaperType !== '1',
								rules: {
									required: false,
									message: '请选择',
									trigger: 'blur'
								},
								data: this.codesObj.pa_common_yes_no,
								'value-key': 'cciValue',
								'label-key': 'shortName',
								clearable: true,
								col: 6
							},
							{
								name: 'retrievalSystem',
								placeholder: '请选择',
								label: '论文检索系统',
								type: 'select',
								hide: academicPaperType !== '1',
								rules: {
									required: false,
									message: '请选择',
									trigger: 'blur'
								},
								data: this.codesObj.academicPaper.pa_retrieval_system,
								'value-key': 'cciValue',
								'label-key': 'shortName',
								clearable: true,
								col: 6
							},
							{
								name: 'thesisUrl',
								label: '论文查询网址',
								type: 'textarea',
								sysCode: 'project_type',
								rules: {
									required: !formReadonly,
									message: '请输入',
									trigger: 'blur'
								},
								col: 6
							},
							{
								name: 'projectNum',
								label: '论文依托的项目编号',
								type: 'text',
								placeholder: '若无依托项目名称/编号，则填无',
								rules: {
									required: !formReadonly,
									message: '请输入',
									trigger: 'blur'
								},
								col: 6
							},
							{
								name: 'projectName',
								label: '论文依托的项目名称',
								placeholder: '若无依托项目名称/编号，则填无',
								type: 'textarea',
								rules: {
									required: !formReadonly,
									message: '请输入',
									trigger: 'blur'
								},
								col: 6
							},
							{
								name: 'usingUnit',
								label: '出版、发行、使用单位',
								type: 'text',
								hide: academicPaperType !== '2',
								rules: {
									required: true,
									message: '请输入',
									trigger: 'blur'
								},
								col: 6
							},
							{
								name: 'form',
								placeholder: '请选择',
								label: '成果形式',
								type: 'select',
								hide: academicPaperType !== '2',
								rules: {
									required: true,
									message: '请选择',
									trigger: 'blur'
								},
								data: this.codesObj.academicPaper.achievement_form,
								'value-key': 'cciValue',
								'label-key': 'shortName',
								clearable: true,
								col: 6
							},
							{
								name: 'source',
								placeholder: '请选择',
								label: '成果来源',
								type: 'select',
								hide: academicPaperType !== '2',
								rules: {
									required: true,
									message: '请选择',
									trigger: 'blur'
								},
								data: this.codesObj.academicPaper.achievement_source,
								'value-key': 'cciValue',
								'label-key': 'shortName',
								clearable: true,
								col: 6
							},
							{
								name: 'publishScope',
								placeholder: '请选择',
								label: '发表范围',
								type: 'select',
								hide: academicPaperType !== '2',
								rules: {
									required: true,
									message: '请选择',
									trigger: 'blur'
								},
								data: this.codesObj.academicPaper.pa_publish_scope,
								'value-key': 'cciValue',
								'label-key': 'shortName',
								clearable: true,
								col: 6
							},
							{
								name: 'researchCategory',
								placeholder: '请选择',
								label: '研究类别',
								type: 'select',
								hide: academicPaperType !== '2',
								rules: {
									required: true,
									message: '请选择',
									trigger: 'blur'
								},
								data: this.codesObj.academicPaper.pa_research_category,
								'value-key': 'cciValue',
								'label-key': 'shortName',
								clearable: true,
								col: 6
							},
							{
								label: '一级学科',
								name: 'firstLevelDiscipline',
								col: 6,
								type: 'select',
								hide: academicPaperType !== '2',
								data: this.codesObj.selectList1,
								'value-key': 'value',
								'label-key': 'label',
								rules: {
									required: true,
									message: '请选择',
									trigger: 'blur'
								},
								events: {
									change: (e, v) => {
										this.$set(this.formData, 'subDiscipline', null);
										this.codesObj.selectList2 = [];
										this.getDiscipline(v, '2');
									}
								},
								filterable: true,
								clearable: true
							},
							{
								label: '二级学科',
								name: 'subDiscipline',
								col: 6,
								type: 'select',
								hide: academicPaperType !== '2',
								data: this.codesObj.selectList2,
								'value-key': 'value',
								'label-key': 'label',
								rules: {
									required: true,
									message: '请选择',
									trigger: 'blur'
								},
								filterable: true,
								clearable: true
							},
							{
								name: 'isUnitAdopt',
								placeholder: '请选择',
								label: '是否被使用单位采纳',
								type: 'select',
								hide: academicPaperType !== '2',
								rules: {
									required: true,
									message: '请选择',
									trigger: 'blur'
								},
								data: this.codesObj.pa_common_yes_no,
								'value-key': 'cciValue',
								'label-key': 'shortName',
								clearable: true,
								col: 6
							},
							{
								name: 'isForeignLanguage',
								placeholder: '请选择',
								label: '是否译成外文',
								type: 'select',
								hide: academicPaperType !== '2',
								rules: {
									required: false,
									message: '请选择',
									trigger: 'blur'
								},
								data: this.codesObj.pa_common_yes_no,
								'value-key': 'cciValue',
								'label-key': 'shortName',
								clearable: true,
								col: 6
							},
							{
								name: 'wordCount',
								label: '成果字数(千)',
								type: 'number',
								controls: false,
								default: false,
								min: 0,
								max: 999999.999,
								precision: 3,
								hide: academicPaperType !== '2',
								rules: {
									required: true,
									message: '请输入',
									trigger: 'blur'
								},
								col: 6
							},
							{
								name: 'adoptSituation',
								label: '成果引用采纳情况',
								type: 'text',
								hide: academicPaperType !== '2',
								rules: {
									required: false,
									message: '请输入',
									trigger: 'blur'
								},
								col: 6
							},
							{
								name: 'textAbstract',
								label: '成果摘要',
								type: 'textarea',
								hide: academicPaperType !== '2',
								rules: {
									required: false,
									message: '请输入',
									trigger: 'blur'
								},
								col: 6
							},
							{
								name: 'keyword',
								label: '关键字',
								type: 'text',
								hide: academicPaperType !== '2',
								rules: {
									required: false,
									message: '请输入',
									trigger: 'blur'
								},
								col: 6
							},
							{
								name: 'englishKeyword',
								label: '英文关键字',
								type: 'text',
								hide: academicPaperType !== '2',
								rules: {
									required: false,
									message: '请输入',
									trigger: 'blur'
								},
								col: 6
							},
							{
								name: 'periodicalName',
								label: '刊物名称',
								type: 'text',
								rules: {
									required: !formReadonly,
									message: '请输入',
									trigger: 'blur'
								},
								col: 6
							},
							{
								name: 'periodicalPublishDate',
								placeholder: '请选择',
								col: 6,
								label: '刊物出版时间',
								rules: {
									required: !formReadonly,
									message: '请选择',
									trigger: 'blur'
								},
								type: 'date'
							},
							{
								name: 'periodicalPageNum',
								placeholder: '请输入',
								col: 6,
								label: '论文所在刊物页码',
								rules: [
									{
										required: !formReadonly,
										message: '请输入',
										trigger: 'blur'
									},
									{
										max: 100,
										message: '长度应在1到10个字符之间',
										trigger: 'blur'
									}
								]
							},
							{
								name: 'thesisLevel',
								placeholder: '请选择',
								col: 6,
								label: '论文级别',
								// type: 'select',
								type: 'cascader',

								rules: {
									required: !formReadonly,
									message: '请选择',
									trigger: 'blur'
								},
								// data: this.codesObj.academicPaper.pa_thesis_level,
								data: this.levelList,
								'value-key': 'value',
								'label-key': 'label',
								filterable: true,
								clearable: true,
								props: { checkStrictly: true },
								events: {
									change: (e, v) => {
										if (v == '1A') {
											this.$message.info('《自然》(Nature)、《科学》(science)');
										} else if (v == '1B01' || v == '1B02' || v == '1B03' || v == '1B04') {
											this.$message.info(
												'发表在具有国际影响力的国内科技期刊、业界公认的国际顶级或重要科技期刊的论文，以及在国内外顶级学术会议上进行报告的论文(简称“三类高质量论文”)，以检索报告分区为准'
											);
										} else if (v == '2A') {
											this.$message.info('被《新华文摘》、《人大复印资料》全文转载 1500 字以上');
										} else if (v == '2B') {
											this.$message.info(
												'B1 在《人民日报》、《光明日报》《中国教育报》理论版发表文章或被转载学术文章 1500 字以上;B2《中国科学引文数据库》(CSCD)或《中国人文社会科学引文索引》(CSSCI南大核心期刊),且同时是北大图书馆《中文核心期刊要目总览》收录期刊;B4 北大图书馆《中文核心期刊要目总览》收录期刊、CSSCI扩展板收录期刊、国际学术会议论文集(有正式出版号)'
											);
										}
									}
								}
							},
							{
								name: 'correspondingAuthor',
								placeholder: '如无,则填写无',
								col: 6,
								label: '通讯作者姓名',
								rules: {
									required: !formReadonly,
									message: '请输入',
									trigger: 'blur'
								}
							},
							// {
							// 	name: 'studentIsFirst',
							// 	placeholder: '请选择',
							// 	label: '学生是否为第一作者',
							// 	type: 'select',
							// 	rules: {
							// 		required: false,
							// 		message: '请选择',
							// 		trigger: 'blur'
							// 	},
							// 	data: this.codesObj.pa_common_yes_no,
							// 	'value-key': 'cciValue',
							// 	'label-key': 'shortName',
							// 	clearable: true,
							// 	col: 6,
							// 	events: {
							// 		change: a => {
							// 			this.$set(this.formData, 'studentName', null);
							// 			this.$set(this.formData, 'studentNum', null);
							// 		}
							// 	}
							// },
							// {
							// 	name: 'studentName',
							// 	label: '学生姓名',
							// 	rules: {
							// 		required: false,
							// 		message: '请输入',
							// 		trigger: 'blur'
							// 	},
							// 	col: 6
							// },
							// {
							// 	name: 'studentNum',
							// 	label: '学生学号',
							// 	rules: {
							// 		required: false,
							// 		message: '请输入',
							// 		trigger: 'blur'
							// 	},
							// 	col: 6
							// },
							{
								name: 'serialNum',
								placeholder: '请填写',
								col: 12,
								label: '期刊号',
								rules: {
									required: !formReadonly,
									message: '请输入',
									trigger: 'blur'
								}
							},
							{
								name: 'fj1',
								label: '支撑材料（封面、目录、论文页、封底）',
								type: 'attachment',
								value: '',
								col: 12,
								preview: true,
								code: 'transationform_editfile',
								ownId: formData.id, // 业务id
								rules: {
									required: !formReadonly,
									message: '请上传',
									trigger: 'blur'
								}
							},
							{
								name: 'isBelongYbzy',
								label: '第一归属单位是否为宜宾职业技术学院',
								type: 'select',
								data: [
									{ cciValue: '0', shortName: '否' },
									{ cciValue: '1', shortName: '是' }
								],
								'value-key': 'cciValue',
								'label-key': 'shortName',
								rules: {
									required: !formReadonly,
									message: '请选择',
									trigger: 'blur'
								},
								col: 12
							},
							{
								name: 'cooperatingOrg',
								label: '合作单位',
								hide: this.formData.isBelongYbzy != '0',
								rules: {
									required: !formReadonly,
									message: '请选择',
									trigger: 'blur'
								},
								col: 12
							}
						]
					};
					break;
				// 学术专利
				case 'academicPatent':
					obj = {
						// 基本配置
						basics: {
							info: '/ybzy/paAcademicPatent/info', // 详情接口
							save: '/ybzy/paAcademicPatent/save', // 新增
							edit: '/ybzy/paAcademicPatent/update', // 修改
							flowTypeCode: 'projectAchievement_mmtradvuivsevsp', // 流程code
							defaultProcessKey: 'projectAchievement_wyenftrmyuzfeqh' // 默认关联流程 key
						},
						// 列表配置
						contents: [
							{
								name: 'declarant',
								placeholder: '请输入',
								label: '申报人',
								rules: {
									required: !formReadonly,
									message: '请输入',
									trigger: 'blur'
								},
								col: 6
							},
							{
								name: 'name',
								placeholder: '请输入',
								label: '专利名称',
								rules: {
									required: !formReadonly,
									message: '请输入',
									trigger: 'blur'
								},
								col: 6
							},
							{
								name: 'recordNum',
								placeholder: '请输入',
								label: '科技处备案号',
								rules: {
									required: !formReadonly,
									message: '请输入',
									trigger: 'blur'
								},
								col: 6
							},
							{
								name: 'patentType',
								placeholder: '请选择',
								col: 6,
								label: '专利类型',
								type: 'select',
								rules: {
									required: !formReadonly,
									message: '请选择',
									trigger: 'blur'
								},
								data: this.codesObj.academicPatent.patent_type,
								'value-key': 'cciValue',
								'label-key': 'shortName',
								clearable: true
							},
							{
								name: 'declarationDate',
								placeholder: '请选择',
								col: 6,
								label: '申报时间',
								rules: {
									required: !formReadonly,
									message: '请选择',
									trigger: 'blur'
								},
								type: 'date'
							},
							{
								name: 'apDate',
								placeholder: '请选择',
								col: 6,
								label: '授权公告日',
								rules: {
									required: !formReadonly,
									message: '请选择',
									trigger: 'blur'
								},
								type: 'date'
							},
							{
								name: 'apNum',
								placeholder: '请输入',
								label: '授权专利号',
								rules: {
									required: !formReadonly,
									message: '请输入',
									trigger: 'blur'
								},
								col: 6
							},
							{
								name: 'agency',
								placeholder: '请输入',
								label: '代理机构',
								rules: {
									required: !formReadonly,
									message: '请输入',
									trigger: 'blur'
								},
								col: 6
							},
							{
								name: 'projectName',
								placeholder: '若无依托项目名称/编号，则填无',
								label: '费用依托项目名称',
								rules: {
									required: false,
									message: '请输入',
									trigger: 'blur'
								},
								col: 6
							},
							{
								name: 'projectNum',
								placeholder: '若无依托项目名称/编号，则填无',
								label: '费用支出依托项目编号',
								rules: {
									required: !formReadonly,
									message: '请输入',
									trigger: 'blur'
								},
								col: 6
							},
							{
								name: 'caRank',
								placeholder: '请选择',
								col: 6,
								label: '合作申报的排名',
								type: 'select',
								rules: {
									required: !formReadonly,
									message: '请选择',
									trigger: 'blur'
								},
								data: this.codesObj.academicPatent.patent_ca_rank,
								'value-key': 'cciValue',
								'label-key': 'shortName',
								clearable: true
							},
							{
								name: 'collaborations',
								placeholder: '请输入',
								label: '合作单位',
								rules: {
									required: false,
									message: '请输入',
									trigger: 'blur'
								},
								col: 6
							},
							{
								name: 'iprType',
								placeholder: '请选择',
								col: 6,
								label: '知识产权类型',
								type: 'select',
								rules: {
									required: false,
									message: '请选择',
									trigger: 'blur'
								},
								data: this.codesObj.academicPatent.patent_ipr_type,
								'value-key': 'cciValue',
								'label-key': 'shortName',
								clearable: true
							},
							{
								name: 'isAuthorize',
								placeholder: '请选择',
								label: '是否授权',
								type: 'select',
								rules: {
									required: !formReadonly,
									message: '请选择',
									trigger: 'blur'
								},
								data: this.codesObj.pa_common_yes_no,
								'value-key': 'cciValue',
								'label-key': 'shortName',
								clearable: true,
								col: 6
							},
							{
								name: 'other',
								label: '其他',
								rules: {
									required: false,
									message: '请输入',
									trigger: 'blur'
								},
								col: 12
							},
							{
								name: 'fj1',
								label: '支撑材料（上传授权证书正反面）',
								type: 'attachment',
								value: '',
								col: 12,
								preview: true,
								code: 'transationform_editfile',
								ownId: formData.id, // 业务id
								rules: {
									required: !formReadonly,
									message: '请上传',
									trigger: 'blur'
								}
							},
							{
								name: 'remark',
								label: '备注',
								rules: {
									required: false,
									message: '请输入',
									trigger: 'blur'
								},
								col: 12
							},
							{
								name: 'isBelongYbzy',
								label: '第一归属单位是否为宜宾职业技术学院',
								type: 'select',
								data: [
									{ cciValue: '0', shortName: '否' },
									{ cciValue: '1', shortName: '是' }
								],
								'value-key': 'cciValue',
								'label-key': 'shortName',
								rules: {
									required: !formReadonly,
									message: '请选择',
									trigger: 'blur'
								},
								col: 12
							},
							{
								name: 'cooperatingOrg',
								label: '合作单位',
								hide: this.formData.isBelongYbzy != '0',
								rules: {
									required: !formReadonly,
									message: '请选择',
									trigger: 'blur'
								},
								col: 12
							}
						]
					};
					break;
				// 学术著作
				case 'academicBook':
					obj = {
						// 基本配置
						basics: {
							info: '/ybzy/paAcademicWriting/info', // 详情接口
							save: '/ybzy/paAcademicWriting/save', // 新增
							edit: '/ybzy/paAcademicWriting/update', // 修改
							flowTypeCode: 'projectAchievement_wyenftrmyuzfeqh', // 流程code
							defaultProcessKey: 'projectAchievement_wyenftrmyuzfeqh' // 默认关联流程 key
						},
						// 列表配置
						contents: [
							{
								name: 'name',
								placeholder: '请输入',
								label: '著作名称',
								rules: {
									required: !formReadonly,
									message: '请输入',
									trigger: 'blur'
								},
								col: 6
							},
							{
								name: 'writingIsbnNo',
								placeholder: '请输入',
								label: 'ISBN号',
								rules: {
									required: !formReadonly,
									message: '请输入',
									trigger: 'blur'
								},
								col: 6
							},
							{
								name: 'writingCategory',
								placeholder: '请选择',
								col: 6,
								label: '等级',
								type: 'select',
								rules: {
									required: !formReadonly,
									message: '请选择',
									trigger: 'blur'
								},
								data: this.codesObj.academicBook.writing_category,
								'value-key': 'cciValue',
								'label-key': 'shortName',
								clearable: true
							},
							{
								name: 'writingPublishingHouse',
								placeholder: '请输入',
								label: '出版社',
								rules: {
									required: !formReadonly,
									message: '请输入',
									trigger: 'blur'
								},
								col: 6
							},
							{
								name: 'writingPublishingDate',
								placeholder: '请选择',
								col: 6,
								label: '出版日期',
								rules: {
									required: !formReadonly,
									message: '请选择',
									trigger: 'blur'
								},
								type: 'date'
							},
							{
								name: 'writingOneEditor',
								placeholder: '请输入',
								label: '第一主编',
								rules: {
									required: !formReadonly,
									message: '请输入',
									trigger: 'blur'
								},
								col: 6
							},
							{
								name: 'writingAuthorSort',
								placeholder: '请选择',
								col: 6,
								label: '作者排序',
								type: 'select',
								rules: {
									required: false,
									message: '请选择',
									trigger: 'blur'
								},
								data: this.codesObj.academicBook.writing_author_sort,
								'value-key': 'cciValue',
								'label-key': 'shortName',
								clearable: true
							},
							{
								name: 'writingWordCount',
								label: '著作总字数(千)',
								type: 'number',
								controls: false,
								default: false,
								min: 0,
								max: 999999.999,
								precision: 3,
								rules: {
									required: false,
									message: '请输入',
									trigger: 'blur'
								},
								col: 6
							},
							{
								name: 'writingType',
								placeholder: '请选择',
								col: 6,
								label: '著作类别',
								type: 'select',
								rules: {
									required: false,
									message: '请选择',
									trigger: 'blur'
								},
								data: this.codesObj.academicBook.writing_type,
								'value-key': 'cciValue',
								'label-key': 'shortName',
								clearable: true
							},
							{
								name: 'writingPublishingPlace',
								placeholder: '请选择',
								col: 6,
								label: '出版地',
								type: 'select',
								rules: {
									required: false,
									message: '请选择',
									trigger: 'blur'
								},
								data: this.codesObj.academicBook.publishing_place,
								'value-key': 'cciValue',
								'label-key': 'shortName',
								clearable: true
							},
							{
								label: '学科分类',
								name: 'writingDiscipline',
								col: 6,
								type: 'select',
								data: this.codesObj.selectList1,
								'value-key': 'value',
								'label-key': 'label',
								rules: {
									required: false,
									message: '请选择',
									trigger: 'blur'
								},
								filterable: true,
								clearable: true
							},
							{
								name: 'wordCount',
								label: '撰写字数(千)',
								type: 'number',
								controls: false,
								default: false,
								min: 0,
								max: 999999.999,
								precision: 3,
								rules: {
									required: !formReadonly,
									message: '请输入',
									trigger: 'blur'
								},
								col: 6
							},
							{
								name: 'studentIsOne',
								placeholder: '请选择',
								label: '学生是否为第一作者',
								type: 'select',
								rules: {
									required: !formReadonly,
									message: '请选择',
									trigger: 'blur'
								},
								data: this.codesObj.pa_common_yes_no,
								'value-key': 'cciValue',
								'label-key': 'shortName',
								clearable: true,
								col: 6
							},
							// {
							// 	name: 'studentName',
							// 	placeholder: '请输入',
							// 	label: '学生姓名',
							// 	rules: {
							// 		required: false,
							// 		message: '请输入',
							// 		trigger: 'blur'
							// 	},
							// 	col: 6
							// },
							// {
							// 	name: 'studentNum',
							// 	placeholder: '请输入',
							// 	label: '学生学号',
							// 	rules: {
							// 		required: false,
							// 		message: '请输入',
							// 		trigger: 'blur'
							// 	},
							// 	col: 6
							// },
							{
								name: 'fj1',
								label: '支撑材料（封面、目录、第一作者所在页、封底）',
								type: 'attachment',
								value: '',
								col: 12,
								preview: true,
								code: 'transationform_editfile',
								ownId: formData.id, // 业务id
								rules: {
									required: !formReadonly,
									message: '请上传',
									trigger: 'blur'
								}
							},
							{
								name: 'remark',
								label: '备注',
								rules: {
									required: false,
									message: '请输入',
									trigger: 'blur'
								},
								col: 12
							},
							{
								name: 'isBelongYbzy',
								label: '第一归属单位是否为宜宾职业技术学院',
								type: 'select',
								data: [
									{ cciValue: '0', shortName: '否' },
									{ cciValue: '1', shortName: '是' }
								],
								'value-key': 'cciValue',
								'label-key': 'shortName',
								rules: {
									required: !formReadonly,
									message: '请选择',
									trigger: 'blur'
								},
								col: 12
							},
							{
								name: 'cooperatingOrg',
								label: '合作单位',
								hide: this.formData.isBelongYbzy != '0',
								rules: {
									required: !formReadonly,
									message: '请选择',
									trigger: 'blur'
								},
								col: 12
							}
						]
					};
					break;
				// 平台团队
				case 'platformTeam':
					obj = {
						// 基本配置
						basics: {
							info: '/ybzy/paPlatformTeam/info', // 详情接口
							save: '/ybzy/paPlatformTeam/save', // 新增
							edit: '/ybzy/paPlatformTeam/update', // 修改
							flowTypeCode: 'projectAchievement_fjtnxbcilitdrpb', // 流程code
							defaultProcessKey: 'projectAchievement_wyenftrmyuzfeqh' // 默认关联流程 key
						},
						// 列表配置
						contents: [
							{
								name: 'name',
								placeholder: '请输入',
								label: '平台团队名称',
								rules: {
									required: !formReadonly,
									message: '请输入',
									trigger: 'blur'
								},
								col: 6
							},
							{
								name: 'principalUserName',
								placeholder: '请输入',
								label: '负责人姓名',
								rules: {
									required: !formReadonly,
									message: '请输入',
									trigger: 'blur'
								},
								col: 6
							},
							{
								name: 'projectNo',
								placeholder: '请输入',
								label: '项目编号',
								rules: {
									required: !formReadonly,
									message: '请输入',
									trigger: 'blur'
								},
								col: 6
							},
							{
								name: 'teamLevel',
								placeholder: '请选择',
								col: 6,
								label: '级别',
								type: 'select',
								rules: {
									required: !formReadonly,
									message: '请选择',
									trigger: 'blur'
								},
								data: this.codesObj.platformTeam.team_level,
								'value-key': 'cciValue',
								'label-key': 'shortName',
								clearable: true
							},
							{
								name: 'projectApprovalDate',
								placeholder: '请选择',
								col: 6,
								label: '立项时间',
								rules: {
									required: !formReadonly,
									message: '请选择',
									trigger: 'blur'
								},
								type: 'date'
							},
							{
								name: 'passCheckDate',
								placeholder: '请选择',
								col: 6,
								label: '通过验收时间',
								rules: {
									required: !formReadonly,
									message: '请选择',
									trigger: 'blur'
								},
								type: 'date'
							},
							{
								name: 'fj1',
								label: '支撑材料',
								type: 'attachment',
								value: '',
								col: 12,
								preview: true,
								code: 'transationform_editfile',
								ownId: formData.id, // 业务id
								rules: {
									required: !formReadonly,
									message: '请上传',
									trigger: 'blur'
								}
							},
							{
								name: 'remark',
								label: '备注',
								rules: {
									required: false,
									message: '请输入',
									trigger: 'blur'
								},
								col: 12
							},
							{
								name: 'isBelongYbzy',
								label: '第一归属单位是否为宜宾职业技术学院',
								type: 'select',
								data: [
									{ cciValue: '0', shortName: '否' },
									{ cciValue: '1', shortName: '是' }
								],
								'value-key': 'cciValue',
								'label-key': 'shortName',
								rules: {
									required: !formReadonly,
									message: '请选择',
									trigger: 'blur'
								},
								col: 12
							},
							{
								name: 'cooperatingOrg',
								label: '合作单位',
								hide: this.formData.isBelongYbzy != '0',
								rules: {
									required: !formReadonly,
									message: '请选择',
									trigger: 'blur'
								},
								col: 12
							}
						]
					};
					break;
				// 技术标准
				case 'technicalStandard':
					obj = {
						// 基本配置
						basics: {
							info: '/ybzy/paTechnicalStandard/info', // 详情接口
							save: '/ybzy/paTechnicalStandard/save', // 新增
							edit: '/ybzy/paTechnicalStandard/update', // 修改
							flowTypeCode: 'projectAchievement_kzpovkrvycscvrg', // 流程code
							defaultProcessKey: 'projectAchievement_wyenftrmyuzfeqh' // 默认关联流程 key
						},
						// 列表配置
						contents: [
							{
								name: 'deptName',
								placeholder: '请输入',
								label: '部门/学院名称',
								rules: {
									required: !formReadonly,
									message: '请输入',
									trigger: 'blur'
								},
								type: 'select',
								filterable: true,
								data: this.deptList,
								'value-key': 'label',
								'label-key': 'label',
								col: 6
							},
							{
								name: 'principalUserName',
								placeholder: '请输入',
								label: '负责人姓名',
								rules: {
									required: !formReadonly,
									message: '请输入',
									trigger: 'blur'
								},
								col: 6
							},
							{
								name: 'name',
								placeholder: '请输入',
								label: '标准名称',
								rules: {
									required: !formReadonly,
									message: '请输入',
									trigger: 'blur'
								},
								col: 6
							},
							{
								name: 'standardNo',
								placeholder: '请输入',
								label: '成果编号',
								rules: {
									required: !formReadonly,
									message: '请输入',
									trigger: 'blur'
								},
								col: 6
							},
							{
								name: 'standardType',
								placeholder: '请选择',
								col: 6,
								label: '标准类别',
								type: 'select',
								rules: {
									required: !formReadonly,
									message: '请选择',
									trigger: 'blur'
								},
								data: this.codesObj.technicalStandard.standard_type,
								'value-key': 'cciValue',
								'label-key': 'shortName',
								clearable: true
							},
							{
								name: 'affirmUnit',
								placeholder: '请输入',
								label: '认定单位',
								rules: {
									required: !formReadonly,
									message: '请输入',
									trigger: 'blur'
								},
								col: 6
							},
							{
								name: 'enactDate',
								placeholder: '请选择',
								col: 6,
								label: '制定时间',
								rules: {
									required: !formReadonly,
									message: '请选择',
									trigger: 'blur'
								},
								type: 'date'
							},
							{
								name: 'affirmDate',
								placeholder: '请选择',
								col: 6,
								label: '认定时间',
								rules: {
									required: !formReadonly,
									message: '请选择',
									trigger: 'blur'
								},
								type: 'date'
							},
							{
								name: 'fj1',
								label: '支撑材料',
								type: 'attachment',
								value: '',
								col: 12,
								preview: true,
								code: 'transationform_editfile',
								ownId: formData.id, // 业务id
								rules: {
									required: !formReadonly,
									message: '请上传',
									trigger: 'blur'
								}
							},
							{
								name: 'remark',
								label: '备注',
								rules: {
									required: false,
									message: '请输入',
									trigger: 'blur'
								},
								col: 12
							},
							{
								name: 'isBelongYbzy',
								label: '第一归属单位是否为宜宾职业技术学院',
								type: 'select',
								data: [
									{ cciValue: '0', shortName: '否' },
									{ cciValue: '1', shortName: '是' }
								],
								'value-key': 'cciValue',
								'label-key': 'shortName',
								rules: {
									required: !formReadonly,
									message: '请选择',
									trigger: 'blur'
								},
								col: 12
							},
							{
								name: 'cooperatingOrg',
								label: '合作单位',
								hide: this.formData.isBelongYbzy != '0',
								rules: {
									required: !formReadonly,
									message: '请选择',
									trigger: 'blur'
								},
								col: 12
							}
						]
					};
					break;
				// 技术产品
				case 'technicalProduct':
					obj = {
						// 基本配置
						basics: {
							info: '/ybzy/paTechnicalProducts/info', // 详情接口
							save: '/ybzy/paTechnicalProducts/save', // 新增
							edit: '/ybzy/paTechnicalProducts/update', // 修改
							flowTypeCode: 'projectAchievement_nsukrhuoldxhuwf', // 流程code
							defaultProcessKey: 'projectAchievement_wyenftrmyuzfeqh' // 默认关联流程 key
						},
						// 列表配置
						contents: [
							{
								name: 'deptName',
								placeholder: '请输入',
								label: '部门/学院名称',
								rules: {
									required: !formReadonly,
									message: '请输入',
									trigger: 'blur'
								},
								type: 'select',
								filterable: true,
								data: this.deptList,
								'value-key': 'label',
								'label-key': 'label',
								col: 6
							},
							{
								name: 'applyUserName',
								placeholder: '请输入',
								label: '申报人',
								rules: {
									required: !formReadonly,
									message: '请输入',
									trigger: 'blur'
								},
								col: 6
							},
							{
								name: 'name',
								placeholder: '请输入',
								label: '成果名称',
								rules: {
									required: !formReadonly,
									message: '请输入',
									trigger: 'blur'
								},
								col: 6
							},
							{
								name: 'productsNo',
								placeholder: '请输入',
								label: '成果编号',
								rules: {
									required: !formReadonly,
									message: '请输入',
									trigger: 'blur'
								},
								col: 6
							},
							{
								name: 'productsCategory',
								placeholder: '请输入',
								label: '类别',
								rules: {
									required: !formReadonly,
									message: '请输入',
									trigger: 'blur'
								},
								col: 6
							},
							{
								name: 'productsGrade',
								placeholder: '请输入',
								label: '等级',
								rules: {
									required: !formReadonly,
									message: '请输入',
									trigger: 'blur'
								},
								col: 6
							},
							{
								name: 'affirmUnit',
								placeholder: '请输入',
								label: '认定单位',
								rules: {
									required: !formReadonly,
									message: '请输入',
									trigger: 'blur'
								},
								col: 6
							},
							{
								name: 'oneInventUser',
								placeholder: '请输入',
								label: '第一发明人',
								rules: {
									required: !formReadonly,
									message: '请输入',
									trigger: 'blur'
								},
								col: 6
							},
							{
								name: 'otherRemark',
								placeholder: '请输入',
								label: '其他',
								rules: {
									required: !formReadonly,
									message: '请输入',
									trigger: 'blur'
								},
								col: 12
							},
							{
								name: 'fj1',
								label: '支撑材料',
								type: 'attachment',
								value: '',
								col: 12,
								preview: true,
								code: 'transationform_editfile',
								ownId: formData.id, // 业务id
								rules: {
									required: !formReadonly,
									message: '请上传',
									trigger: 'blur'
								}
							},
							{
								name: 'remark',
								label: '备注',
								rules: {
									required: false,
									message: '请输入',
									trigger: 'blur'
								},
								col: 12
							},
							{
								name: 'isBelongYbzy',
								label: '第一归属单位是否为宜宾职业技术学院',
								type: 'select',
								data: [
									{ cciValue: '0', shortName: '否' },
									{ cciValue: '1', shortName: '是' }
								],
								'value-key': 'cciValue',
								'label-key': 'shortName',
								rules: {
									required: !formReadonly,
									message: '请选择',
									trigger: 'blur'
								},
								col: 12
							},
							{
								name: 'cooperatingOrg',
								label: '合作单位',
								hide: this.formData.isBelongYbzy != '0',
								rules: {
									required: !formReadonly,
									message: '请选择',
									trigger: 'blur'
								},
								col: 12
							}
						]
					};
					break;
				// 获奖成果 分社科、自然两种类型
				case 'awardAchievement':
					obj = {
						// 基本配置
						basics: {
							info: '/ybzy/paAwards/info', // 详情接口
							save: '/ybzy/paAwards/save', // 新增
							edit: '/ybzy/paAwards/update', // 修改
							flowTypeCode: 'projectAchievement_veqormhclslamcx', // 流程code
							defaultProcessKey: 'projectAchievement_wyenftrmyuzfeqh' // 默认关联流程 key
						},
						// 列表配置
						contents: [
							{
								name: 'awardType',
								label: '类型',
								type: 'select',
								data: this.codesObj.academic_type_code,
								'value-key': 'cciValue',
								'label-key': 'shortName',
								rules: {
									required: !formReadonly,
									message: '请选择',
									trigger: 'blur'
								},
								col: 6
							},
							{
								name: 'deptName',
								placeholder: '请输入',
								label: '部门/学院名称',
								rules: {
									required: !formReadonly,
									message: '请输入',
									trigger: 'blur'
								},
								type: 'select',
								filterable: true,
								data: this.deptList,
								'value-key': 'label',
								'label-key': 'label',
								col: 6
							},
							{
								name: 'prizewinnerName',
								placeholder: '请输入',
								label: '获奖人姓名',
								rules: {
									required: !formReadonly,
									message: '请输入',
									trigger: 'blur'
								},
								col: 6
							},
							{
								label: '一级学科',
								name: 'firstLevelDiscipline',
								hide: awardType !== '2',
								col: 6,
								type: 'select',
								data: this.codesObj.selectList1,
								'value-key': 'value',
								'label-key': 'label',
								rules: {
									required: true,
									message: '请选择',
									trigger: 'blur'
								},
								filterable: true,
								clearable: true
							},
							{
								name: 'prizewinnerRank',
								label: '获奖人排名',
								hide: awardType !== '2',
								type: 'select',
								data: this.codesObj.awardAchievement.award_prizewinner_rank,
								'value-key': 'cciValue',
								'label-key': 'shortName',
								rules: {
									required: !formReadonly,
									message: '请选择',
									trigger: 'blur'
								},
								col: 6
							},
							{
								name: 'name',
								placeholder: '请输入',
								label: '成果名称',
								rules: {
									required: !formReadonly,
									message: '请输入',
									trigger: 'blur'
								},
								col: 6
							},
							{
								name: 'achievementNum',
								placeholder: '请输入',
								label: '成果编号',
								rules: {
									required: !formReadonly,
									message: '请输入',
									trigger: 'blur'
								},
								col: 6
							},
							{
								name: 'rewardName',
								placeholder: '请输入',
								label: '奖励名称',
								hide: awardType !== '1',
								rules: {
									required: false,
									message: '请输入',
									trigger: 'blur'
								},
								col: 6
							},
							{
								name: 'awardTitle',
								placeholder: '请输入',
								label: '奖项名称',
								rules: {
									required: !formReadonly,
									message: '请输入',
									trigger: 'blur'
								},
								col: 6
							},
							{
								name: 'rewardRank',
								placeholder: '请输入',
								label: '奖励级别',
								hide: awardType !== '2',
								rules: {
									required: false,
									message: '请输入',
									trigger: 'blur'
								},
								col: 6
							},
							{
								name: 'rewardGrade',
								placeholder: '请输入',
								label: '奖励等级',
								hide: awardType !== '2',
								rules: {
									required: false,
									message: '请输入',
									trigger: 'blur'
								},
								col: 6
							},
							{
								name: 'awardUnit',
								placeholder: '请输入',
								label: '授予单位',
								hide: awardType !== '1',
								rules: {
									required: !formReadonly,
									message: '请输入',
									trigger: 'blur'
								},
								col: 6
							},
							{
								label: '学科分类',
								name: 'subjectType',
								hide: awardType !== '1',
								col: 6,
								type: 'select',
								data: this.codesObj.selectList1,
								'value-key': 'value',
								'label-key': 'label',
								rules: {
									required: false,
									message: '请选择',
									trigger: 'blur'
								},
								filterable: true,
								clearable: true
							},
							{
								name: 'rewardCategory',
								label: '奖励类别',
								hide: awardType !== '1',
								type: 'select',
								data: this.codesObj.awardAchievement.reward_category,
								'value-key': 'cciValue',
								'label-key': 'shortName',
								rules: {
									required: !formReadonly,
									message: '请选择',
									trigger: 'blur'
								},
								col: 6
							},
							{
								name: 'acquisitionTime',
								placeholder: '请选择',
								col: 6,
								label: awardType === '1' ? '获奖时间' : '奖励日期',
								rules: {
									required: !formReadonly,
									message: '请选择',
									trigger: 'blur'
								},
								type: 'date'
							},
							{
								name: 'rewardApprovalNum',
								placeholder: '请输入',
								label: '奖励批准号',
								hide: awardType !== '2',
								rules: {
									required: false,
									message: '请输入',
									trigger: 'blur'
								},
								col: 6
							},
							{
								name: 'rewardUnit',
								placeholder: '请输入',
								label: '奖励单位',
								hide: awardType !== '2',
								rules: {
									required: false,
									message: '请输入',
									trigger: 'blur'
								},
								col: 6
							},
							{
								name: 'form',
								placeholder: '请输入',
								label: '成果形式',
								hide: awardType !== '2',
								rules: {
									required: true,
									message: '请输入',
									trigger: 'blur'
								},
								col: 6
							},
							{
								name: 'source',
								placeholder: '请输入',
								label: '项目来源',
								hide: awardType !== '2',
								rules: {
									required: false,
									message: '请输入',
									trigger: 'blur'
								},
								col: 6
							},
							{
								name: 'awardGrade',
								placeholder: '请输入',
								label: '获奖等次',
								hide: awardType !== '2',
								rules: {
									required: !formReadonly,
									message: '请输入',
									trigger: 'blur'
								},
								col: 6
							},
							{
								name: 'awardUnitSort',
								label: '获奖单位排序',
								hide: awardType !== '1',
								type: 'select',
								data: this.codesObj.awardAchievement.award_unit_sort,
								'value-key': 'cciValue',
								'label-key': 'shortName',
								rules: {
									required: !formReadonly,
									message: '请选择',
									trigger: 'blur'
								},
								col: 6
							},
							{
								name: 'awardLevel',
								label: '获奖等级',
								hide: awardType !== '1',
								type: 'select',
								data: this.codesObj.awardAchievement.award_level,
								'value-key': 'cciValue',
								'label-key': 'shortName',
								rules: {
									required: !formReadonly,
									message: '请选择',
									trigger: 'blur'
								},
								col: 6
							},
							{
								name: 'schoolRank',
								label: '学校排位',
								type: 'select',
								data: this.codesObj.awardAchievement.school_rank,
								'value-key': 'cciValue',
								'label-key': 'shortName',
								rules: {
									required: !formReadonly,
									message: '请选择',
									trigger: 'blur'
								},
								col: 6
							},
							{
								name: 'personalRank',
								label: '个人排位',
								hide: awardType !== '2',
								type: 'select',
								data: this.codesObj.awardAchievement.award_personal_rank,
								'value-key': 'cciValue',
								'label-key': 'shortName',
								rules: {
									required: !formReadonly,
									message: '请选择',
									trigger: 'blur'
								},
								col: 6
							},
							{
								name: 'foundUnit',
								placeholder: '请输入',
								label: '设奖单位',
								rules: {
									required: !formReadonly,
									message: '请输入',
									trigger: 'blur'
								},
								col: 6
							},
							{
								name: 'fj1',
								label: '奖励证书附件',
								type: 'attachment',
								value: '',
								col: 12,
								preview: true,
								code: 'transationform_editfile',
								ownId: formData.id, // 业务id
								rules: {
									required: !formReadonly,
									message: '请上传',
									trigger: 'blur'
								}
							},
							{
								name: 'remark',
								label: '备注',
								rules: {
									required: false,
									message: '请输入',
									trigger: 'blur'
								},
								col: 12
							},
							{
								name: 'isBelongYbzy',
								label: '第一归属单位是否为宜宾职业技术学院',
								type: 'select',
								data: [
									{ cciValue: '0', shortName: '否' },
									{ cciValue: '1', shortName: '是' }
								],
								'value-key': 'cciValue',
								'label-key': 'shortName',
								rules: {
									required: !formReadonly,
									message: '请选择',
									trigger: 'blur'
								},
								col: 12
							},
							{
								name: 'cooperatingOrg',
								label: '合作单位',
								hide: this.formData.isBelongYbzy != '0',
								rules: {
									required: !formReadonly,
									message: '请选择',
									trigger: 'blur'
								},
								col: 12
							}
						]
					};
					break;
				// 软著
				case 'softwareAchievement':
					obj = {
						// 基本配置
						basics: {
							info: '/ybzy/paSoftwareCopyright/info', // 详情接口
							save: '/ybzy/paSoftwareCopyright/save', // 新增
							edit: '/ybzy/paSoftwareCopyright/update', // 修改
							flowTypeCode: 'ruanzhu', // 流程code
							defaultProcessKey: 'rzlc' // 默认关联流程 key
						},
						// 列表配置
						contents: [
							{
								name: 'name',
								placeholder: '请输入',
								label: '软件名称',
								rules: {
									required: !formReadonly,
									message: '请输入',
									trigger: 'blur'
								},
								col: 6
							},
							{
								name: 'authorityUser',
								placeholder: '请输入',
								label: '著作权人',
								rules: {
									required: !formReadonly,
									message: '请输入',
									trigger: 'blur'
								},
								col: 6
							},
							{
								name: 'accomplishDate',
								placeholder: '请选择',
								col: 6,
								label: '开发完成日期',
								rules: {
									required: !formReadonly,
									message: '请选择',
									trigger: 'blur'
								},
								type: 'date'
							},
							{
								name: 'oneDate',
								placeholder: '请选择',
								col: 6,
								label: '首次发表日期',
								rules: {
									required: false,
									message: '请选择',
									trigger: 'blur'
								},
								type: 'date'
							},
							{
								name: 'authorityType',
								placeholder: '请输入',
								label: '权利取得方式',
								rules: {
									required: !formReadonly,
									message: '请输入',
									trigger: 'blur'
								},
								col: 6
							},
							{
								name: 'authorityScope',
								placeholder: '请输入',
								label: '权利取得范围',
								rules: {
									required: !formReadonly,
									message: '请输入',
									trigger: 'blur'
								},
								col: 6
							},
							{
								name: 'registerNo',
								placeholder: '请输入',
								label: '登记号',
								rules: {
									required: !formReadonly,
									message: '请输入',
									trigger: 'blur'
								},
								col: 6
							},
							{
								name: 'registerDate',
								placeholder: '请选择',
								col: 6,
								label: '登记日期',
								rules: {
									required: !formReadonly,
									message: '请选择',
									trigger: 'blur'
								},
								type: 'date'
							},
							{
								name: 'authorityIsUnit',
								placeholder: '请选择',
								label: '著作权人是否有单位',
								type: 'select',
								rules: {
									required: !formReadonly,
									message: '请选择',
									trigger: 'blur'
								},
								data: this.codesObj.pa_common_yes_no,
								'value-key': 'cciValue',
								'label-key': 'shortName',
								clearable: true,
								col: 6
							},
							{
								name: 'authorityUnit',
								placeholder: '请输入',
								label: '著作权人单位',
								rules: {
									required: false,
									message: '请输入',
									trigger: 'blur'
								},
								col: 6
							},
							{
								name: 'fj1',
								label: '支撑材料',
								type: 'attachment',
								value: '',
								col: 12,
								preview: true,
								code: 'transationform_editfile',
								ownId: formData.id, // 业务id
								rules: {
									required: !formReadonly,
									message: '请上传',
									trigger: 'blur'
								}
							},
							{
								name: 'isBelongYbzy',
								label: '第一归属单位是否为宜宾职业技术学院',
								type: 'select',
								data: [
									{ cciValue: '0', shortName: '否' },
									{ cciValue: '1', shortName: '是' }
								],
								'value-key': 'cciValue',
								'label-key': 'shortName',
								rules: {
									required: !formReadonly,
									message: '请选择',
									trigger: 'blur'
								},
								col: 12
							},
							{
								name: 'cooperatingOrg',
								label: '合作单位',
								hide: this.formData.isBelongYbzy != '0',
								rules: {
									required: !formReadonly,
									message: '请选择',
									trigger: 'blur'
								},
								col: 12
							}
						]
					};
					break;
				// 科技转化成果
				case 'scientificConversionAchievement':
					obj = {
						// 基本配置
						basics: {
							info: '/ybzy/paScienceAchievement/info', // 详情接口
							save: '/ybzy/paScienceAchievement/save', // 新增
							edit: '/ybzy/paScienceAchievement/update', // 修改
							flowTypeCode: 'projectAchievement_xgivtjabknckzve', // 流程code
							defaultProcessKey: 'projectAchievement_wyenftrmyuzfeqh' // 默认关联流程 key
						},
						// 列表配置
						contents: [
							{
								name: 'deptName',
								placeholder: '请输入',
								label: '部门/学院名称',
								rules: {
									required: !formReadonly,
									message: '请输入',
									trigger: 'blur'
								},
								type: 'select',
								filterable: true,
								data: this.deptList,
								'value-key': 'label',
								'label-key': 'label',
								col: 6
							},
							{
								name: 'principal',
								placeholder: '请输入',
								label: '负责人（第一完成人）',
								rules: {
									required: !formReadonly,
									message: '请输入',
									trigger: 'blur'
								},
								col: 6
							},
							{
								name: 'name',
								placeholder: '请输入',
								label: '成果名称',
								rules: {
									required: !formReadonly,
									message: '请输入',
									trigger: 'blur'
								},
								col: 6
							},
							{
								name: 'achievementNum',
								placeholder: '请输入',
								label: '成果编号',
								rules: {
									required: !formReadonly,
									message: '请输入',
									trigger: 'blur'
								},
								col: 6
							},
							{
								name: 'scienceAchievementType',
								placeholder: '请选择',
								col: 6,
								label: '科技成果类别',
								type: 'select',
								rules: {
									required: !formReadonly,
									message: '请选择',
									trigger: 'blur'
								},
								data: this.codesObj.scientificConversionAchievement.science_achievement_type,
								'value-key': 'cciValue',
								'label-key': 'shortName',
								clearable: true
							},
							{
								name: 'changeWay',
								placeholder: '请选择',
								col: 6,
								label: '转化方式',
								type: 'select',
								rules: {
									required: !formReadonly,
									message: '请选择',
									trigger: 'blur'
								},
								data: this.codesObj.scientificConversionAchievement.pa_change_way,
								'value-key': 'cciValue',
								'label-key': 'shortName',
								clearable: true
							},
							{
								name: 'transfereeType',
								placeholder: '请选择',
								col: 6,
								label: '受让方类型',
								type: 'select',
								rules: {
									required: false,
									message: '请选择',
									trigger: 'blur'
								},
								data: this.codesObj.scientificConversionAchievement.pa_transferee_type,
								'value-key': 'cciValue',
								'label-key': 'shortName',
								clearable: true
							},
							{
								name: 'changeAmount',
								label: '转化金额(元)',
								type: 'number',
								controls: false,
								default: false,
								min: 0,
								max: 99999999.99,
								precision: 2,
								rules: {
									required: !formReadonly,
									message: '请输入',
									trigger: 'blur'
								},
								col: 6
							},
							{
								name: 'arrivalAmount',
								label: '到账金额(元)',
								type: 'number',
								controls: false,
								default: false,
								min: 0,
								max: 99999999.99,
								precision: 2,
								rules: {
									required: !formReadonly,
									message: '请输入',
									trigger: 'blur'
								},
								col: 6
							},
							{
								name: 'transferee',
								placeholder: '请输入',
								label: '成果受让方',
								rules: {
									required: !formReadonly,
									message: '请输入',
									trigger: 'blur'
								},
								col: 6
							},
							{
								name: 'transfereeUser',
								placeholder: '请输入',
								label: '成果受让方联系人',
								rules: {
									required: !formReadonly,
									message: '请输入',
									trigger: 'blur'
								},
								col: 6
							},
							{
								name: 'transfereePhone',
								placeholder: '请输入',
								label: '成果受让方联系方式',
								rules: {
									required: !formReadonly,
									message: '请输入',
									trigger: 'blur'
								},
								col: 6
							},
							{
								name: 'fj1',
								label: '支撑材料（转化合同、申请表、专利授权证书、到账通知单）',
								type: 'attachment',
								value: '',
								col: 12,
								preview: true,
								code: 'transationform_editfile',
								ownId: formData.id, // 业务id
								rules: {
									required: !formReadonly,
									message: '请上传',
									trigger: 'blur'
								}
							},
							{
								name: 'remark',
								label: '备注',
								rules: {
									required: false,
									message: '请输入',
									trigger: 'blur'
								},
								col: 12
							},
							{
								name: 'isBelongYbzy',
								label: '第一归属单位是否为宜宾职业技术学院',
								type: 'select',
								data: [
									{ cciValue: '0', shortName: '否' },
									{ cciValue: '1', shortName: '是' }
								],
								'value-key': 'cciValue',
								'label-key': 'shortName',
								rules: {
									required: !formReadonly,
									message: '请选择',
									trigger: 'blur'
								},
								col: 12
							},
							{
								name: 'cooperatingOrg',
								label: '合作单位',
								hide: this.formData.isBelongYbzy != '0',
								rules: {
									required: !formReadonly,
									message: '请选择',
									trigger: 'blur'
								},
								col: 12
							}
						]
					};
					break;
				// 竞赛获奖
				case 'pa_competition':
					obj = {
						// 基本配置
						basics: {
							info: '/ybzy/paCompetition/info', // 详情接口
							save: '/ybzy/paCompetition/save', // 新增
							edit: '/ybzy/paCompetition/update', // 修改
							flowTypeCode: 'projectAchievement_competition', // 流程code
							defaultProcessKey: 'pa_competition' // 默认关联流程 key
						},
						// 列表配置
						contents: [
							{
								name: 'name',
								placeholder: '请输入',
								type: 'textarea',
								label: '项目名称',
								rules: {
									required: !formReadonly,
									message: '请输入',
									trigger: 'blur'
								},
								col: 12
							},
							{
								name: 'type',
								placeholder: '请选择',
								label: '项目类别',
								type: 'select',
								rules: {
									required: !formReadonly,
									message: '请选择',
									trigger: 'blur'
								},
								data: this.codesObj.pa_competition.competition_type,
								'value-key': 'cciValue',
								'label-key': 'shortName',
								clearable: true,
								col: 6
							},
							{
								name: 'level',
								placeholder: '请选择',
								label: '级别',
								type: 'select',
								rules: {
									required: !formReadonly,
									message: '请选择',
									trigger: 'blur'
								},
								data: this.codesObj.pa_competition.competition_level,
								'value-key': 'cciValue',
								'label-key': 'shortName',
								clearable: true,
								col: 6
							},
							{
								name: 'grade',
								placeholder: '请选择',
								label: '获奖等级',
								type: 'select',
								rules: {
									required: !formReadonly,
									message: '请选择',
									trigger: 'blur'
								},
								data: this.codesObj.pa_competition.competition_grade,
								'value-key': 'cciValue',
								'label-key': 'shortName',
								clearable: true,
								col: 6
							},
							{
								name: 'sponsor',
								placeholder: '请输入',
								type: 'textarea',
								label: '奖项（赛项）主办方',
								rules: {
									required: !formReadonly,
									message: '请输入',
									trigger: 'blur'
								},
								col: 12
							},
							{
								name: 'awardDate',
								placeholder: '请选择',
								label: '获奖日期',
								type: 'date',
								rules: {
									required: !formReadonly,
									message: '请选择',
									trigger: 'blur'
								},
								col: 6
							},
							{
								name: 'fj1',
								label: '获奖通知文件、获奖证书',
								type: 'attachment',
								value: '',
								col: 12,
								preview: true,
								code: 'transationform_editfile',
								ownId: formData.id, // 业务id
								rules: {
									required: !formReadonly,
									message: '请上传',
									trigger: 'blur'
								}
							},
						]
					};
					break;
				default:
					break;
			}
			return obj;
		}
	}
};
