<template>
	<!-- 课表 -->
	<div
		class="experiment"
		:style="{
			transform: `scale(${scale}) translate3d(0, 0, 0)`
		}"
	>
		<div class="">
			<span class="experiment-title">课表</span>
		</div>
		<!-- 内容区域 -->
		<div class="experiment-content">
			<el-table :data="tableData" border style="width: 100%" :cell-class-name="cellClassName">
				<el-table-column prop="date" label="时间段" width="90">
					<template slot-scope="scope">
						<p>{{ scope.row.date }}</p>
						<p class="colse-cell" @click="colseCell(scope)">
							{{ scope.row.colseCell ? '展开' : '收起' }}
							<i :class="scope.row.colseCell ? 'el-icon-arrow-down' : 'el-icon-arrow-up'"></i>
						</p>
					</template>
				</el-table-column>
				<el-table-column prop="name" label="节次" width="90"></el-table-column>
				<el-table-column
					v-for="(weeksItem, weeksIndex) in weeks"
					:key="weeksIndex"
					prop="address"
					:label="weeksItem.day"
				>
					<template slot-scope="scope">
						<div v-for="(item, index) in scope.row[weeksItem.key]" :key="index" class="class-card">
							<p>
								<span v-for="(tagItem, tagsIndex) in item.tag" :key="tagsIndex" class="tag-item">
									{{ tagItem }}
								</span>
							</p>
							<p class="title">{{ item.title }}</p>
							<p>{{ item.position }}</p>
							<p>{{ item.subTitle }}</p>
							<p>{{ item.time }}</p>
							<p>{{ item.nums }}</p>
						</div>
					</template>
				</el-table-column>
			</el-table>
		</div>
	</div>
</template>

<script>
import { xodbApi2 } from '@/api/xodb';

export default {
	components: {},
	props: {},
	data() {
		return {
			scale: 1,
			weeks: [
				{
					day: '星期一',
					key: 'monClassList'
				},
				{
					day: '星期二',
					key: 'tuesClassList'
				},
				{
					day: '星期三',
					key: 'wedClassList'
				},
				{
					day: '星期四',
					key: 'thursClassList'
				},
				{
					day: '星期五',
					key: 'friClassList'
				},
				{
					day: '星期六',
					key: 'satClassList'
				},
				{
					day: '星期天',
					key: 'sunClassList'
				}
			],
			tableData: [
				{
					date: '上午',
					name: '1-4节',
					colseCell: false,
					monClassList: [],
					tuesClassList: [],
					wedClassList: [],
					thursClassList: [],
					friClassList: [],
					satClassList: [
						// {
						// 	title: 'Web漏洞扫描及渗透测试(6102121',
						// 	position: '电智学院在线教室2',
						// 	subTitle: 'web漏洞扫描及渗透测试(安全12201',
						// 	time: '课程总学时：72',
						// 	nums: '选课人数：36',
						// 	tag: ['1-4节', '5-7周']
						// },
						// {
						// 	title: 'Web漏洞扫描及渗透测试(6102121',
						// 	position: '电智学院在线教室2',
						// 	subTitle: 'web漏洞扫描及渗透测试(安全12201',
						// 	time: '课程总学时：72',
						// 	nums: '选课人数：43',
						// 	tag: ['5-8节', '1-2周、6-7周、9-14周']
						// }
					],
					sunClassList: [
						// {
						// 	title: 'Web漏洞扫描及渗透测试(6102121',
						// 	position: '电智学院在线教室2',
						// 	subTitle: 'web漏洞扫描及渗透测试(安全12201',
						// 	time: '课程总学时：72',
						// 	nums: '选课人数：36',
						// 	tag: ['1-4节', '4周']
						// }
					]
				},
				{
					date: '下午',
					name: '5-8节',
					colseCell: false,
					monClassList: [
						// {
						// 	title: 'Web漏洞扫描及渗透测试(6102121',
						// 	position: '电智学院在线教室2',
						// 	subTitle: 'web漏洞扫描及渗透测试(安全12201',
						// 	time: '课程总学时：72',
						// 	nums: '选课人数：36',
						// 	tag: ['1-4节', '5-7周']
						// },
						// {
						// 	title: 'Web漏洞扫描及渗透测试(6102121',
						// 	position: '电智学院在线教室2',
						// 	subTitle: 'web漏洞扫描及渗透测试(安全12201',
						// 	time: '课程总学时：72',
						// 	nums: '选课人数：43',
						// 	tag: ['5-8节', '1-2周、6-7周、9-14周']
						// }
					],
					tuesClassList: [
						// {
						// 	title: 'Web漏洞扫描及渗透测试(6102121',
						// 	position: '电智学院在线教室2',
						// 	subTitle: 'web漏洞扫描及渗透测试(安全12201',
						// 	time: '课程总学时：72',
						// 	nums: '选课人数：36',
						// 	tag: ['1-4节', '5-7周']
						// },
						// {
						// 	title: 'Web漏洞扫描及渗透测试(6102121',
						// 	position: '电智学院在线教室2',
						// 	subTitle: 'web漏洞扫描及渗透测试(安全12201',
						// 	time: '课程总学时：72',
						// 	nums: '选课人数：43',
						// 	tag: ['5-8节', '1-2周、6-7周、9-14周']
						// }
					],
					wedClassList: [],
					thursClassList: [
						// {
						// 	title: 'Web漏洞扫描及渗透测试(6102121',
						// 	position: '电智学院在线教室2',
						// 	subTitle: 'web漏洞扫描及渗透测试(安全12201',
						// 	time: '课程总学时：72',
						// 	nums: '选课人数：36',
						// 	tag: ['1-4节', '5-7周']
						// },
						// {
						// 	title: 'Web漏洞扫描及渗透测试(6102121',
						// 	position: '电智学院在线教室2',
						// 	subTitle: 'web漏洞扫描及渗透测试(安全12201',
						// 	time: '课程总学时：72',
						// 	nums: '选课人数：43',
						// 	tag: ['5-8节', '1-2周、6-7周、9-14周']
						// }
					],
					friClassList: [],
					satClassList: [
						// {
						// 	title: 'Web漏洞扫描及渗透测试(6102121',
						// 	position: '电智学院在线教室2',
						// 	subTitle: 'web漏洞扫描及渗透测试(安全12201',
						// 	time: '课程总学时：72',
						// 	nums: '选课人数：36',
						// 	tag: ['1-4节', '5-7周']
						// },
						// {
						// 	title: 'Web漏洞扫描及渗透测试(6102121',
						// 	position: '电智学院在线教室2',
						// 	subTitle: 'web漏洞扫描及渗透测试(安全12201',
						// 	time: '课程总学时：72',
						// 	nums: '选课人数：43',
						// 	tag: ['5-8节', '1-2周、6-7周、9-14周']
						// },
						// {
						// 	title: 'Web漏洞扫描及渗透测试(6102121',
						// 	position: '电智学院在线教室2',
						// 	subTitle: 'web漏洞扫描及渗透测试(安全12201',
						// 	time: '课程总学时：72',
						// 	nums: '选课人数：36',
						// 	tag: ['1-4节', '5-7周']
						// },
						// {
						// 	title: 'Web漏洞扫描及渗透测试(6102121',
						// 	position: '电智学院在线教室2',
						// 	subTitle: 'web漏洞扫描及渗透测试(安全12201',
						// 	time: '课程总学时：72',
						// 	nums: '选课人数：43',
						// 	tag: ['5-8节', '1-2周、6-7周、9-14周']
						// }
					],
					sunClassList: []
				},
				{
					date: '晚上',
					name: '9-12节',
					colseCell: false,
					monClassList: [
						// {
						// 	title: 'Web漏洞扫描及渗透测试(6102121',
						// 	position: '电智学院在线教室2',
						// 	subTitle: 'web漏洞扫描及渗透测试(安全12201',
						// 	time: '课程总学时：72',
						// 	nums: '选课人数：43',
						// 	tag: ['1-4节', '5-7周']
						// },
						// {
						// 	title: 'Web漏洞扫描及渗透测试(6102121',
						// 	position: '电智学院在线教室2',
						// 	subTitle: 'web漏洞扫描及渗透测试(安全12201',
						// 	time: '课程总学时：72',
						// 	nums: '选课人数：43',
						// 	tag: ['5-8节', '1-2周、6-7周、9-14周']
						// }
					],
					tuesClassList: [],
					wedClassList: [],
					thursClassList: [],
					friClassList: [
						// {
						// 	title: 'Web漏洞扫描及渗透测试(6102121',
						// 	position: '凤凰楼302计算机应用实训室2',
						// 	subTitle: 'web漏洞扫描及渗透测试(安全12201',
						// 	time: '课程总学时：72',
						// 	nums: '选课人数：43',
						// 	tag: ['9-10节', '15周']
						// },
						// {
						// 	title: 'Web漏洞扫描及渗透测试(6102121',
						// 	position: '凤凰楼302计算机应用实训室2',
						// 	subTitle: 'web漏洞扫描及渗透测试(安全12201',
						// 	time: '课程总学时：72',
						// 	nums: '选课人数：43',
						// 	tag: ['9-12节', '15周']
						// },
						// {
						// 	title: 'Web漏洞扫描及渗透测试(6102121',
						// 	position: '凤凰楼302计算机应用实训室2',
						// 	subTitle: 'web漏洞扫描及渗透测试(安全12201',
						// 	time: '课程总学时：72',
						// 	nums: '选课人数：43',
						// 	tag: ['11-12节', '15周']
						// }
					],
					satClassList: [],
					sunClassList: []
				}
			],
			userInfo: {}
		};
	},
	created() {
		this.userInfo = JSON.parse(localStorage.getItem('loginUserInfo')) || {};
		this.scale = this.$utils.toolViewRatio();
	},
	mounted() {
		this.getInfoN('ads_jx_jskbxx_query');
	},
	computed: {
		loginUserInfo() {
			return JSON.parse(localStorage.getItem('loginUserInfo')) || {};
		}
	},
	methods: {
		// 请求课表
		async getInfoN(url) {
			try {
				// const LeaderRole = localStorage.getItem('LeaderRole');
				const {
					data: { list }
				} = await xodbApi2.post({
					url: url,
					pageSize: 300,
					params: {
						// XH: '202315871',
						// xyjgbh: LeaderRole === 'xLeader' ? '1' : this.loginUserInfo.orgCode
						jgh: this.loginUserInfo.code
					}
				});
				if (list.length < 1) {
					return;
				}
				// 筛选出zc1:"16,17,18"  对应的当年周
				list.forEach(e => {
					// 判断上课时间段 上午是1,2,3,4,下午是5,6,7,8,晚上是9,10,11,12  e.skjc字段为"5-8"
					const skjc = e.skjc.split('-')[0];
					let tableDataI = null; // 0 上午 1 下午 2 晚上
					switch (skjc) {
						case '1':
						case '2':
						case '3':
						case '4':
							tableDataI = 0;
							break;
						case '5':
						case '6':
						case '7':
						case '8':
							tableDataI = 1;
							break;
						case '9':
						case '10':
						case '11':
						case '12':
							tableDataI = 2;
							break;
					}
					if (tableDataI === null) {
						console.log('课表：tableDataI获取失败', e);
						return;
					}

					const w = this.weeks[Number(e.xqj) - 1];
					this.tableData[tableDataI][w.key].push({
						title: e.kcmc,
						position: '班级：' + e.bjmc,
						subTitle: '地点：' + e.skdd,
						time: '课程总学时：' + e.kczxs,
						nums: '选课人数：' + e.xkrs,
						tag: [e.skjc + '节', e.zc]
					});
				});
			} catch (error) {
				console.error('课表：处理数据失败:', error);
			}
		},
		// 展开收起事件
		colseCell(row) {
			row.row.colseCell = !row.row.colseCell;
		},
		//自定义属性事件和展开收起配合的样式修改
		cellClassName({ row, column, rowIndex, columnIndex }) {
			if (row.colseCell) {
				return 'colseCellName';
			}
		}
	}
};
</script>

<style lang="scss" scoped>
.experiment {
	width: 1826px;
	// height: 934px;
	transform-origin: top left;
	background: #e7f6ff;
	padding: 29px 11px 13px 12px;
	border-radius: 5px;
	overflow: hidden;
	&-title {
		font-family: MicrosoftYaHei, MicrosoftYaHei;
		font-weight: bold;
		font-size: 24px;
		color: #454545;
		line-height: 31px;
		margin-left: 27px;
	}
	&-content {
		margin-top: 22px;
	}
	.class-card {
		background: rgba(255, 255, 255, 0.25);
		box-shadow: 0px 0px 6px 0px rgba(0, 0, 0, 0.09);
		border-radius: 3px;
		border: 1px solid #ffffff;
		padding: 12px 10px 6px;
		margin-bottom: 6px;
		font-size: 12px;
		color: #454545;
		line-height: 16px;
		font-style: normal;
		text-align: left;
		.title {
			font-weight: bold;
			color: #053b6d;
			font-style: normal;
		}
		.tag-item {
			display: inline-block;
			padding: 3px 13px;
			background: #e2fcff;
			border-radius: 2px;
			border: 1px solid #17d7ec;
			margin-right: 4px;
		}
		.tag-item + .tag-item {
			background: #f9e1f9;
			border-radius: 2px;
			border: 1px solid #ff81e4;
		}
		> p {
			margin-bottom: 6px;
		}
	}
	.colse-cell {
		font-size: 14px;
		color: #0275e8;
		line-height: 19px;
		font-style: normal;
		margin-top: 10px;
		cursor: pointer;
	}
}
::v-deep .el-table__row .colseCellName .cell {
	height: 60px;
	overflow: auto;
}
::v-deep .el-table .cell {
	padding: 0 !important;
}
::v-deep .el-table th,
::v-deep .el-table tr {
	background-color: #ecf6ff !important;
}
.el-table--border th,
.el-table--border td {
	border: 1px solid #d2dee2;
}
</style>
