<template>
  <div class="content" style="height: 100%">
    <div style="width:100%;height: 100%">
      <es-data-table :row-style="tableRowClassName" v-if="true" ref="table" :key="tableCount" :full="true" :fit="true" :thead="thead" :toolbar="toolbar"
                     @btnClick="btnClick" @selection-change="handleSelectionChange" @edit="changeTable"
                     :page="page" :url="dataTableUrl" :param="dataTableParam" :border="true" :numbers="true" checkbox form>
      </es-data-table>
      <es-dialog :drag="false" :title="formTitle" v-if="showInfoPage" :visible.sync="showInfoPage" width="70%" height="auto" append-to-body>
        <div style="height: 80vh">
          <infoPage ref="infoPage" v-on:activelyClose="closeInfoPage" :base-data="formData" :info-page-mode="this.infoPageMode"></infoPage>
        </div>
      </es-dialog>
    </div>
  </div>
</template>

<script>
import api from '@/http/society/societyBaseInfo/api';
import InfoPage from "@/views/society/societyBaseInfoAudit/infoPage.vue";

export default {
  components: {InfoPage},
  data() {
    return {
      formData: {},
      page: {
        pageSize: 20,
        totalCount: 0,
      },
      infoPageMode: 'allOn',
      selectRowData: [],
      selectRowIds: [],
      showInfoPage: false,
      tableCount: 1,
      dialogType: "",
      dataTableUrl: api.societyBaseInfoListJson,
      dataTableParam: { orderBy: 'create_time', asc: false },
      formTitle: '',
      validityOfDateDisable: false,
      thead: [],
      toolbar: [],
      editToolbar:[
        {
          type: 'button',
          contents: [
            {
              text: '查看',
              code: 'toolbar',
              type: 'primary'
            },
            {
              text: '删除',
              code: 'toolbar',
              type: 'danger'
            },
          ]
        },
        {
          type: 'search',
          reset: true,
          contents: [
            {
              type: 'text',
              name: 'keyword',
              placeholder: '关键字查询',
              clearable: true,
            },
            {
              type: 'select',
              name: 'auditStatus',
              placeholder: '审核状态',
              clearable: true,
              data: [
                {
                  value: 1,
                  label: '待审核'
                },
                {
                  value: 2,
                  label: '审核通过'
                },
                {
                  value: 3,
                  label: '驳回'
                }
              ]
            },
          ]
        },
      ],
      listThead: [
        {
          title: '社团名称',
          align: 'left',
          field: 'name',
          sortable: 'custom',
          showOverflowTooltip: true,
        },
        {
          title: '申请人',
          width: "150px",
          align: 'center',
          field: 'createUserName',
          sortable: 'custom',
          showOverflowTooltip: true,
        },
        {
          title: '申请时间',
          width: "150px",
          align: 'center',
          field: 'createTime',
          sortable: 'custom',
          showOverflowTooltip: true,
        },
        {
          title: '社团社长',
          width: "150px",
          align: 'center',
          field: 'principalName',
          sortable: 'custom',
          showOverflowTooltip: true,
        },
        {
          title: '审核状态',
          width: "150px",
          align: 'center',
          field: 'auditStatusVO',
          sortable: 'custom',
          showOverflowTooltip: true,
          render: (h, param)=>{
            return h (
                'el-tag',
                {props: {type: param.row.auditStatus === 3? 'danger':''}},
                param.row.auditStatusVO
            )
          }
        },
      ],
      btnJson: {
        title: '操作',
        type: 'handle',
        width: 180,
        template: '',
        events: [
          {
            code: 'row',
            text: '查看'
          },
          {
            code: 'row',
            text: '审核',
            rules: rows => {
              return rows.auditStatus === 1;
            }
          },
        ]
      },
    };
  },
  created() {
    //初始化查询待审核列表
    this.thead = this.getListThead(this.btnJson);
    this.toolbar = this.editToolbar;
  },
  methods: {
    /**
     * 表格行高
     */
    tableRowClassName() {
      return {
        "height": "54px !important"
      };
    },
    btnClick(res){
      let text = res.handle.text;
      let code = res.handle.code;

      if(code === 'row'){
        switch (text){
          case '查看':
          case '审核': this.openInfoPage(res.row.id, text); break;
          case '删除': this.deleteRows([res.row.id]); break;
        }
      }else {
        switch (text){
          case '查看':
            if(this.selectRowIds.length>1){
              this.$message.warning('只能选择一个查看');
            }else if(this.selectRowIds.length<1){
              this.$message.warning('请选择一个进行查看');
            }else {this.openInfoPage(this.selectRowIds[0], text);}
            break;
          case '删除':
            if(this.selectRowIds.length<1){
              this.$message.warning('请选择一个删除');
            }else {this.deleteRows(this.selectRowIds);}
            break;
        }
      }
    },
    //打开infoPage
    openInfoPage(id, pageMode){
      this.$request({
        url: api.societyBaseInfoInfo+'/'+id,
        method: 'GET'
      }).then(res => {
        //处理请求数据
        this.formData = {...res.results};
        this.formData.principal = { value:this.formData.principal, label:this.formData.principalName };
        this.formData.college = { value:this.formData.collegeId, label:this.formData.collegeName };
        this.formData.teacherId = this.formData.teacherId?.split(',');
        if(this.formData.typeId != null && this.formData.typeId !== '')
          this.formData.typeId = { value:this.formData.typeId, label:this.formData.typeName };

        this.formTitle = pageMode;
        this.infoPageMode = pageMode;
        this.showInfoPage = true;
      });
    },
    //关闭infoPage
    closeInfoPage(reload){
      this.formData = {};
      this.showInfoPage = false;
      if(reload)
        this.$refs.table.reload();
    },
    // 数据列表多选回调
    handleSelectionChange(data) {
      let ids = [];
      data.forEach(row => {
        ids.push(row.id);
      });
      this.selectRowData = data;
      this.selectRowIds = ids;
    },
    deleteRows(ids){
      this.$confirm('确定要删除选中的数据吗？', '删除', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      })
          .then(() => {
            this.$request({
              url: api.societyBaseInfoDeleteIds,
              data: { ids: ids.join(',') },
              method: 'POST'
            }).then(res => {
              if (res.rCode === 0) {
                this.$message.success('操作完成');
                this.$refs.table.reload();
              } else {
                this.$message.error(res.msg);
              }
            });
          })
          .catch(() => {});
    },
    getListThead(btnJson){
      let tempThead = [...this.listThead];
      tempThead.push(btnJson);
      return tempThead;
    },
    changeTable(val) {
      switch (val.name){
        case 'status': break;
      }
    },
  },
};
</script>