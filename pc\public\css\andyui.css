@import "./animate.css";
@import "./jquery.mCustomScrollbar.css";
html,
body {
    width: 100%;
    height: 100%;
    color: #333;
    background-color: #f8f8f8;
}

html,
body,
h1,
h2,
h3,
h4,
h5,
h6,
div,
dl,
dt,
dd,
ul,
ol,
li,
p,
blockquote,
pre,
hr,
figure,
table,
caption,
th,
td,
form,
fieldset,
legend,
input,
button,
textarea,
menu,
a,
label,
i,
span {
    margin: 0;
    padding: 0;
    font-family: 'Microsoft YaHei', \5fae\8f6f\96c5\9ed1, arial, \5b8b\4f53;
    -moz-box-sizing: border-box;
    -webkit-box-sizing: border-box;
    -o-box-sizing: border-box;
    -ms-box-sizing: border-box;
    box-sizing: border-box;
}

body {
    _background-image: url(about:blank);
    _background-attachment: fixed;
}

header,
footer,
section,
article,
aside,
nav,
hgroup,
address,
figure,
figcaption,
menu,
details {
    display: block;
}

table {
    border-collapse: collapse;
    border-spacing: 0;
}

caption,
th {
    font-weight: normal;
}

html,
body,
fieldset,
iframe,
abbr {
    border: 0;
    display: inherit;
}

img {
    border: 0;
    display: block;
    display: inline-block;
}

i,
cite,
em,
var,
address,
dfn {
    font-style: normal;
}

[hidefocus],
summary {
    outline: 0;
}

li {
    list-style: none;
    line-height: 22px;
}

sup,
sub {
    font-size: 83%;
}

pre,
code,
kbd,
samp {
    font-family: inherit;
}

code {
    padding: 2px 6px;
    color: #c7254e;
    background-color: #efefef;
    white-space: nowrap;
    border-radius: 3px;
    margin: 1px 3px;
}

q:before,
q:after {
    content: none;
}

textarea {
    overflow: auto;
    resize: none;
}

label,
summary {
    cursor: default;
}

a,
button {
    cursor: pointer;
    /* star: expression(this.onFocus=this.blur()); */
    color: var(--theme-primary);
}

a:hover,
button:hover {
    transition: all 0.3s;
    -moz-transition: all 0.3s;
    -webkit-transition: all 0.3s;
    -o-transition: all 0.3s;
    color: #6cc4ed;
}

del,
ins,
u,
s,
a,
a:hover {
    text-decoration: none;
}

hr {
    height: 1px;
    border: none;
    border-top-width: 1px;
    border-top-style: solid;
    border-color: #ddd;
    margin: 0;
    *margin: 0 0 -14px 0;
    float: none;
    *float: left;
    display: block;
}

p {
    padding-bottom: 15px;
    line-height: 22px;
}

p.text {
    text-indent: 2em;
}

input {
    *filter: chroma(color=#000000) !important;
}

button {
    *filter: chroma(color=#000000) !important;
    *overflow: visible;
}

h1,
.h1 {
    font-weight: 500;
    font-size: 36px;
    line-height: 61.2px;
    padding-top: 10.8px;
    margin-bottom: 18px;
}

h1.line,
.h1.line {
    border-bottom-style: solid;
    border-bottom-width: 1px;
    border-color: #ddd;
    padding-left: 10px;
    padding-right: 10px;
    padding-top: 18px;
    margin-bottom: 36px;
}

h1.center,
.h1.center {
    text-align: center;
}

h1.center div,
.h1.center div {
    margin-left: auto!important;
    margin-right: auto!important;
    text-align: center;
    display: table;
}

h1.subtitle,
.h1.subtitle {
    margin-bottom: 54px;
    line-height: 54px;
}

h1.subtitle div,
.h1.subtitle div {
    font-size: 36px;
    margin-bottom: -28.8px;
    margin-left: 10px;
    background-color: #fff;
    display: table;
    padding: 0 10.8px;
    border-radius: 5px;
}

h1.subtitle div span,
.h1.subtitle div span {
    text-transform: uppercase;
    font-size: 14.4px;
    display: block;
    line-height: 21.6px;
    margin-top: -3.6px;
    opacity: 0.5;
}

h1 small,
.h1 small {
    font-size: 18px;
}

h2,
.h2 {
    font-weight: 500;
    font-size: 30px;
    line-height: 51px;
    padding-top: 9px;
    margin-bottom: 15px;
}

h2.line,
.h2.line {
    border-bottom-style: solid;
    border-bottom-width: 1px;
    border-color: #ddd;
    padding-left: 10px;
    padding-right: 10px;
    padding-top: 15px;
    margin-bottom: 30px;
}

h2.center,
.h2.center {
    text-align: center;
}

h2.center div,
.h2.center div {
    margin-left: auto!important;
    margin-right: auto!important;
    text-align: center;
    display: table;
}

h2.subtitle,
.h2.subtitle {
    margin-bottom: 45px;
    line-height: 45px;
}

h2.subtitle div,
.h2.subtitle div {
    font-size: 30px;
    margin-bottom: -24px;
    margin-left: 10px;
    background-color: #fff;
    display: table;
    padding: 0 9px;
    border-radius: 5px;
}

h2.subtitle div span,
.h2.subtitle div span {
    text-transform: uppercase;
    font-size: 12px;
    display: block;
    line-height: 18px;
    margin-top: -3px;
    opacity: 0.5;
}

h2 small,
.h2 small {
    font-size: 15px;
}

h3,
.h3 {
    font-weight: 500;
    font-size: 24px;
    line-height: 40.8px;
    padding-top: 7.2px;
    margin-bottom: 12px;
}

h3.line,
.h3.line {
    border-bottom-style: solid;
    border-bottom-width: 1px;
    border-color: #ddd;
    padding-left: 10px;
    padding-right: 10px;
    padding-top: 12px;
    margin-bottom: 24px;
}

h3.center,
.h3.center {
    text-align: center;
}

h3.center div,
.h3.center div {
    margin-left: auto!important;
    margin-right: auto!important;
    text-align: center;
    display: table;
}

h3.subtitle,
.h3.subtitle {
    margin-bottom: 36px;
    line-height: 36px;
}

h3.subtitle div,
.h3.subtitle div {
    font-size: 24px;
    margin-bottom: -19.2px;
    margin-left: 10px;
    background-color: #fff;
    display: table;
    padding: 0 7.2px;
    border-radius: 5px;
}

h3.subtitle div span,
.h3.subtitle div span {
    text-transform: uppercase;
    font-size: 9.6px;
    display: block;
    line-height: 14.4px;
    margin-top: -2.4px;
    opacity: 0.5;
}

h3 small,
.h3 small {
    font-size: 12px;
}

h4,
.h4 {
    font-weight: 500;
    font-size: 18px;
    line-height: 30.6px;
    padding-top: 5.4px;
    margin-bottom: 9px;
}

h4.line,
.h4.line {
    border-bottom-style: solid;
    border-bottom-width: 1px;
    border-color: #ddd;
    padding-left: 10px;
    padding-right: 10px;
    padding-top: 9px;
    margin-bottom: 18px;
}

h4.center,
.h4.center {
    text-align: center;
}

h4.center div,
.h4.center div {
    margin-left: auto!important;
    margin-right: auto!important;
    text-align: center;
    display: table;
}

h4.subtitle,
.h4.subtitle {
    margin-bottom: 27px;
    line-height: 27px;
}

h4.subtitle div,
.h4.subtitle div {
    font-size: 18px;
    margin-bottom: -14.4px;
    margin-left: 10px;
    background-color: #fff;
    display: table;
    padding: 0 5.4px;
    border-radius: 5px;
}

h4.subtitle div span,
.h4.subtitle div span {
    text-transform: uppercase;
    font-size: 7.2px;
    display: block;
    line-height: 10.8px;
    margin-top: -1.8px;
    opacity: 0.5;
}

h4 small,
.h4 small {
    font-size: 9px;
}

h5,
.h5 {
    font-weight: 500;
    font-size: 14px;
    line-height: 23.8px;
    padding-top: 4.2px;
    margin-bottom: 7px;
}

h5.line,
.h5.line {
    border-bottom-style: solid;
    border-bottom-width: 1px;
    border-color: #ddd;
    padding-left: 10px;
    padding-right: 10px;
    padding-top: 7px;
    margin-bottom: 14px;
}

h5.center,
.h5.center {
    text-align: center;
}

h5.center div,
.h5.center div {
    margin-left: auto!important;
    margin-right: auto!important;
    text-align: center;
    display: table;
}

h5.subtitle,
.h5.subtitle {
    margin-bottom: 21px;
    line-height: 21px;
}

h5.subtitle div,
.h5.subtitle div {
    font-size: 14px;
    margin-bottom: -11.2px;
    margin-left: 10px;
    background-color: #fff;
    display: table;
    padding: 0 4.2px;
    border-radius: 5px;
}

h5.subtitle div span,
.h5.subtitle div span {
    text-transform: uppercase;
    font-size: 5.6px;
    display: block;
    line-height: 8.4px;
    margin-top: -1.4px;
    opacity: 0.5;
}

h5 small,
.h5 small {
    font-size: 7px;
}

h6,
.h6 {
    font-weight: 500;
    font-size: 12px;
    line-height: 20.4px;
    padding-top: 3.6px;
    margin-bottom: 6px;
}

h6.line,
.h6.line {
    border-bottom-style: solid;
    border-bottom-width: 1px;
    border-color: #ddd;
    padding-left: 10px;
    padding-right: 10px;
    padding-top: 6px;
    margin-bottom: 12px;
}

h6.center,
.h6.center {
    text-align: center;
}

h6.center div,
.h6.center div {
    margin-left: auto!important;
    margin-right: auto!important;
    text-align: center;
    display: table;
}

h6.subtitle,
.h6.subtitle {
    margin-bottom: 18px;
    line-height: 18px;
}

h6.subtitle div,
.h6.subtitle div {
    font-size: 12px;
    margin-bottom: -9.6px;
    margin-left: 10px;
    background-color: #fff;
    display: table;
    padding: 0 3.6px;
    border-radius: 5px;
}

h6.subtitle div span,
.h6.subtitle div span {
    text-transform: uppercase;
    font-size: 4.8px;
    display: block;
    line-height: 7.2px;
    margin-top: -1.2px;
    opacity: 0.5;
}

h6 small,
.h6 small {
    font-size: 6px;
}

.h1,
.h2,
.h3,
.h4,
.h5,
.h6 {
    padding: 0;
    margin: 0;
}

strong,
b,
.b {
    font-weight: bold;
}

label {
    position: relative;
}

label .file {
    position: absolute;
    top: 0;
    left: 0;
    opacity: 0!important;
    filter: alpha(opacity=0) !important;
    cursor: pointer;
    width: 1px;
    height: 1px;
    overflow: hidden;
    font-size: 0px;
    z-index: -1px;
}

.g-layout {
    position: relative;
    clear: both;
    display: block;
    overflow: hidden;
}

.g-layout .layout-head,
.g-layout .layout-left,
.g-layout .layout-right,
.g-layout .layout-center,
.g-layout .layout-foot {
    position: absolute;
    clear: both;
    display: block;
    z-index: 1;
    overflow-x: hidden;
    overflow-y: auto;
    box-sizing: border-box;
}

.g-layout .layout-head {
    top: 0;
    z-index: 4!important;
    overflow: visible;
}

.g-layout .layout-left {
    left: 0;
    z-index: 3!important;
    overflow: visible!important;
}

.g-layout .layout-right {
    right: 0;
    z-index: 1;
}

.g-layout .layout-foot {
    bottom: 0;
    z-index: 4!important;
    overflow: hidden;
}

.g-layout .layout-center {
    z-index: 2!important;
}

.g-layout .layout-center>.m-panel.fit.f-b {
    border-top: none;
}

.g-layout .layout-center>.m-panel.fit>.panel-head {
    background-color: #fff;
    color: #333;
    border-color: #d1d1d1;
}

.g-layout .layout-center>.m-panel.fit>.panel-head a {
    color: #808080;
}

.g-layout .layout-center>.m-panel.fit>.panel-head a:hover {
    color: #999999;
}

.g-layout .layout-center>.m-panel.fit>.panel-body {
    padding: 60px 15px 15px 15px;
    background-color: transparent;
}

.g-layout .layout-center>.m-tabs>.m-tabs-content>.item {
    border-width: 15px;
    border-style: solid;
    border-color: #f8f8f8;
}

.g-layout .layout-center>.m-tabs>.m-tabs-content>.item.active {
    border-color: #ffffff;
}

@media screen and (max-width: 1295px) {
    .g-layout .layout-center>.g-max.f-p-sm,
    .g-layout .layout-center>.g-max>.row>.f-p-sm {
        padding: 3px;
    }
}

.g-layout .layout-center.pageshadow>.m-panel.fit.f-b {
    border: none;
}

.g-box1000,
.container-1000 {
    width: 1000px;
    margin: 0 auto;
    display: block;
    position: relative;
}

.g-box1200,
.container-1200 {
    width: 1200px;
    margin: 0 auto;
    display: block;
    position: relative;
}

.col-1,
.col-2,
.col-3,
.col-4,
.col-5,
.col-6,
.col-7,
.col-8,
.col-9,
.col-10,
.col-11,
.col-12 {
    float: left;
    position: relative;
    display: block;
}

.col-1 {
    width: 8.33333333%;
}

.col-3 {
    width: 25%;
}

.col-2 {
    width: 16.66666667%;
}

.col-4 {
    width: 33.33333333%;
}

.col-5 {
    width: 41.66666667%;
}

.col-6 {
    width: 50%;
}

.col-7 {
    width: 58.33333333%;
}

.col-8 {
    width: 66.66666667%;
}

.col-9 {
    width: 75%;
}

.col-10 {
    width: 83.33333333%;
}

.col-11 {
    width: 91.66666667%;
}

.col-12 {
    width: 100%;
}

.g-h-max {
    position: relative;
    overflow-y: hidden;
}

.g-h-auto {
    overflow: auto;
}

.g-w-max {
    float: left;
}

.g-w-auto {
    float: left;
    overflow: auto;
}

@font-face {
    font-family: "iconfont";
    src: url('font/iconfont.eot?t=1492051565618');
    src: url('font/iconfont.eot?t=1492051565618#iefix') format('embedded-opentype'), url('font/iconfont.woff?t=1492051565618') format('woff'), url('font/iconfont.ttf?t=1492051565618') format('truetype'), url('font/iconfont.svg?t=1492051565618#andyicon') format('svg');
}

.iconfont {
    font-family: "iconfont" !important;
    font-style: normal;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
}

.u-btn {
    display: inline-block;
    moz-user-select: -moz-none;
    -moz-user-select: none;
    -o-user-select: none;
    -khtml-user-select: none;
    -webkit-user-select: none;
    -ms-user-select: none;
    user-select: none;
    border-radius: 3px;
    text-decoration: none;
    outline: none;
    position: relative;
    *position: static;
    border-style: solid;
    border-width: 1px;
    border-color: transparent;
    background-color: transparent;
    height: 30px;
    min-width: 30px;
    line-height: 28px;
    padding: 0 9px;
}

.u-btn.xl {
    height: 50px;
    line-height: 48px;
    padding: 0 15px;
    font-size: 20px;
}

.u-btn.lg {
    height: 45px;
    line-height: 43px;
    padding: 0 14px;
    font-size: 18px;
}

.u-btn.md {
    height: 35px;
    min-width: 35px;
    line-height: 33px;
    padding: 0 11px;
}

.u-btn.sm {
    height: 26px;
    min-width: 26px;
    line-height: 24px;
    padding: 0 8px;
}

.u-btn.xs {
    height: 20px;
    min-width: 20px;
    line-height: 18px;
    padding: 0 6px;
    font-size: 12px;
}

.u-btn:hover {
    cursor: pointer;
}

.u-btn:active,
.u-btn.active {
    box-shadow: 0 5px 5px rgba(0, 0, 0, 0.3) inset;
}

.u-btn.active {
    cursor: default;
}

.u-btn:disabled,
.u-btn.disabled {
    background-color: #cecece !important;
    color: #686868 !important;
    border-color: #b7b7b7 !important;
    background: #cecece !important;
    box-shadow: none!important;
}

.u-btn:disabled a,
.u-btn.disabled a {
    color: #4f4f4f !important;
}

.u-btn:disabled a:hover,
.u-btn.disabled a:hover {
    color: #686868 !important;
}

.u-btn>.u-point {
    position: absolute;
    top: 0;
    right: 0;
    border: none;
}

.u-btn.link {
    border: none;
    box-shadow: none;
}

.u-btn>.iconfont {
    margin: 0;
}

.u-btn.upload {
    width: 80px;
    height: 80px;
    border-style: dotted;
    border-width: 1px;
    line-height: 30px;
    text-align: center;
}

.u-btn.upload .iconfont {
    font-size: 50px;
    display: block;
    margin-top: 17px;
}

.u-uploadPreview {
    width: 80px;
    height: 80px;
    display: inline-block;
    margin: 0 5px 0 0;
    float: left;
}

.u-input,
.u-label,
.u-checkbox,
.u-select,
.u-textarea {
    height: 30px;
    line-height: 28px;
    padding: 0 9px;
    display: block;
    float: left;
    border-radius: 3px;
    border-style: solid;
    border-width: 1px;
    outline: none;
    position: relative;
    background-color: #fff;
    color: #333;
    border-color: #d1d1d1;
}

.u-input a,
.u-label a,
.u-checkbox a,
.u-select a,
.u-textarea a {
    color: #808080;
}

.u-input a:hover,
.u-label a:hover,
.u-checkbox a:hover,
.u-select a:hover,
.u-textarea a:hover {
    color: #999999;
}

.u-input::-webkit-input-placeholder,
.u-label::-webkit-input-placeholder,
.u-checkbox::-webkit-input-placeholder,
.u-select::-webkit-input-placeholder,
.u-textarea::-webkit-input-placeholder {
    color: #b3b3b3;
}

.u-input:-moz-placeholder,
.u-label:-moz-placeholder,
.u-checkbox:-moz-placeholder,
.u-select:-moz-placeholder,
.u-textarea:-moz-placeholder {
    color: #b3b3b3;
}

.u-input::-moz-placeholder,
.u-label::-moz-placeholder,
.u-checkbox::-moz-placeholder,
.u-select::-moz-placeholder,
.u-textarea::-moz-placeholder {
    color: #b3b3b3;
}

.u-input:-ms-input-placeholder,
.u-label:-ms-input-placeholder,
.u-checkbox:-ms-input-placeholder,
.u-select:-ms-input-placeholder,
.u-textarea:-ms-input-placeholder {
    color: #b3b3b3;
}

.u-input:hover,
.u-label:hover,
.u-checkbox:hover,
.u-select:hover,
.u-textarea:hover {
    background-position: right 50%;
    background-repeat: no-repeat;
    background-image: url("../img/edit.png");
}

.u-input.nohover:hover,
.u-label.nohover:hover,
.u-checkbox.nohover:hover,
.u-select.nohover:hover,
.u-textarea.nohover:hover {
    background-image: inherit;
}


/*输入框获取焦点背景色修改*/

.u-input:focus,
.u-label:focus,
.u-checkbox:focus,
.u-select:focus,
.u-textarea:focus {
    background-image: inherit;
    background-color: rgba(167, 122, 62, 0.1);
    color: #666;
    border-color: #999;
}

.u-input:focus a,
.u-label:focus a,
.u-checkbox:focus a,
.u-select:focus a,
.u-textarea:focus a {
    color: #666;
}

.u-input:focus a:hover,
.u-label:focus a:hover,
.u-checkbox:focus a:hover,
.u-select:focus a:hover,
.u-textarea:focus a:hover {
    color: #666;
}


/*输入框获取焦点后提示文字之颜色*/

.u-input:focus::-webkit-input-placeholder,
.u-label:focus::-webkit-input-placeholder,
.u-checkbox:focus::-webkit-input-placeholder,
.u-select:focus::-webkit-input-placeholder,
.u-textarea:focus::-webkit-input-placeholder {
    color: #666;
}

.u-input:focus:-moz-placeholder,
.u-label:focus:-moz-placeholder,
.u-checkbox:focus:-moz-placeholder,
.u-select:focus:-moz-placeholder,
.u-textarea:focus:-moz-placeholder {
    color: #666;
}

.u-input:focus::-moz-placeholder,
.u-label:focus::-moz-placeholder,
.u-checkbox:focus::-moz-placeholder,
.u-select:focus::-moz-placeholder,
.u-textarea:focus::-moz-placeholder {
    color: #666;
}

.u-input:focus:-ms-input-placeholder,
.u-label:focus:-ms-input-placeholder,
.u-checkbox:focus:-ms-input-placeholder,
.u-select:focus:-ms-input-placeholder,
.u-textarea:focus:-ms-input-placeholder {
    color: #666;
}

.u-input.nofocus:focus,
.u-label.nofocus:focus,
.u-checkbox.nofocus:focus,
.u-select.nofocus:focus,
.u-textarea.nofocus:focus {
    box-shadow: none;
    background-image: inherit;
}

.u-input.err,
.u-label.err,
.u-checkbox.err,
.u-select.err,
.u-textarea.err {
    background-image: inherit;
    background-color: #fef8f8;
    color: #82170f;
    border-color: #f5aba6;
}

.u-input.err a,
.u-label.err a,
.u-checkbox.err a,
.u-select.err a,
.u-textarea.err a {
    color: #dd261a;
}

.u-input.err a:hover,
.u-label.err a:hover,
.u-checkbox.err a:hover,
.u-select.err a:hover,
.u-textarea.err a:hover {
    color: var(--theme-primary);
}

.u-input.err::-webkit-input-placeholder,
.u-label.err::-webkit-input-placeholder,
.u-checkbox.err::-webkit-input-placeholder,
.u-select.err::-webkit-input-placeholder,
.u-textarea.err::-webkit-input-placeholder {
    color: #FF0000;
}

.u-input.err:-moz-placeholder,
.u-label.err:-moz-placeholder,
.u-checkbox.err:-moz-placeholder,
.u-select.err:-moz-placeholder,
.u-textarea.err:-moz-placeholder {
    color: #FF0000;
}

.u-input.err::-moz-placeholder,
.u-label.err::-moz-placeholder,
.u-checkbox.err::-moz-placeholder,
.u-select.err::-moz-placeholder,
.u-textarea.err::-moz-placeholder {
    color: #FF0000;
}

.u-input.err:-ms-input-placeholder,
.u-label.err:-ms-input-placeholder,
.u-checkbox.err:-ms-input-placeholder,
.u-select.err:-ms-input-placeholder,
.u-textarea.err:-ms-input-placeholder {
    color: #FF0000;
}

.u-input:disabled,
.u-label:disabled,
.u-checkbox:disabled,
.u-select:disabled,
.u-textarea:disabled {
    background-image: inherit;
    background: #e6e6e6;
}

.u-input.disabled,
.u-label.disabled,
.u-checkbox.disabled,
.u-select.disabled,
.u-textarea.disabled,
.u-input.u-diseditor,
.u-label.u-diseditor,
.u-checkbox.u-diseditor,
.u-select.u-diseditor,
.u-textarea.u-diseditor {
    background-image: inherit;
    cursor: pointer;
    background: #e6e6e6;
}

.u-select,
select.u-input {
    padding: 0 5px;
    padding-right: 0\0;
}

.u-select:hover,
select.u-input:hover {
    cursor: pointer;
    background-image: inherit;
}

.u-label,
.u-checkbox {
    border-color: transparent;
    display: inline-block;
    *display: block;
    *float: left;
    background-color: transparent;
    min-width: 30px;
}

.u-label input,
.u-checkbox input {
    position: relative;
    margin: 0 5px 0 5px;
    top: 2px;
    *top: -1px;
    *margin: 0 2px 0 2px;
}

.u-label:hover,
.u-checkbox:hover {
    background-image: none;
    cursor: pointer;
}

.u-label.text,
.u-checkbox.text {
    padding: 0 0 0 15px;
    font: 14px;
}

.u-label.text input,
.u-checkbox.text input {
    display: none;
}

.u-label.text.active,
.u-checkbox.text.active {
    color: var(--theme-primary);
    font-weight: bold;
}

.u-input-err {
    width: 20px;
    height: 20px;
    position: absolute;
    padding-right: 1px;
    right: 2px;
    top: 50%;
    background: url("../img/err.png") 50% center no-repeat;
    margin-top: -10px;
    z-index: 1;
}

.u-input-cor {
    width: 20px;
    height: 20px;
    position: absolute;
    padding-right: 1px;
    right: 2px;
    top: 50%;
    background: url("../img/correct.png") 50% center no-repeat;
    margin-top: -10px;
    z-index: 1;
}

textarea.u-input,
textarea.u-textarea {
    height: auto;
}

.u-btn-eject .u-btn-checkbox,
.u-inputitem .u-btn-checkbox,
.u-btn-eject .checkbox,
.u-inputitem .checkbox {
    height: 30px;
    line-height: 28px;
    padding: 0 9px;
    border-radius: 3px;
    border-style: solid;
    border-width: 1px;
    outline: none;
    position: relative;
    background-color: #fff;
    color: #333;
    border-color: #d1d1d1;
    width: 100%;
    overflow-y: auto;
    overflow-x: hidden;
    display: block;
    float: left;
    padding: 0;
    text-indent: 0;
}

.u-btn-eject .u-btn-checkbox a,
.u-inputitem .u-btn-checkbox a,
.u-btn-eject .checkbox a,
.u-inputitem .checkbox a {
    color: #808080;
}

.u-btn-eject .u-btn-checkbox a:hover,
.u-inputitem .u-btn-checkbox a:hover,
.u-btn-eject .checkbox a:hover,
.u-inputitem .checkbox a:hover {
    color: #999999;
}

.u-btn-eject .u-btn-checkbox::-webkit-input-placeholder,
.u-inputitem .u-btn-checkbox::-webkit-input-placeholder,
.u-btn-eject .checkbox::-webkit-input-placeholder,
.u-inputitem .checkbox::-webkit-input-placeholder {
    color: #b3b3b3;
}

.u-btn-eject .u-btn-checkbox:-moz-placeholder,
.u-inputitem .u-btn-checkbox:-moz-placeholder,
.u-btn-eject .checkbox:-moz-placeholder,
.u-inputitem .checkbox:-moz-placeholder {
    color: #b3b3b3;
}

.u-btn-eject .u-btn-checkbox::-moz-placeholder,
.u-inputitem .u-btn-checkbox::-moz-placeholder,
.u-btn-eject .checkbox::-moz-placeholder,
.u-inputitem .checkbox::-moz-placeholder {
    color: #b3b3b3;
}

.u-btn-eject .u-btn-checkbox:-ms-input-placeholder,
.u-inputitem .u-btn-checkbox:-ms-input-placeholder,
.u-btn-eject .checkbox:-ms-input-placeholder,
.u-inputitem .checkbox:-ms-input-placeholder {
    color: #b3b3b3;
}

.u-btn-eject .u-btn-checkbox:hover,
.u-inputitem .u-btn-checkbox:hover,
.u-btn-eject .checkbox:hover,
.u-inputitem .checkbox:hover {
    background-position: right 50%;
    background-repeat: no-repeat;
    background-image: url("../img/edit.png");
}

.u-btn-eject .u-btn-checkbox.nohover:hover,
.u-inputitem .u-btn-checkbox.nohover:hover,
.u-btn-eject .checkbox.nohover:hover,
.u-inputitem .checkbox.nohover:hover {
    background-image: inherit;
}

.u-btn-eject .u-btn-checkbox:focus,
.u-inputitem .u-btn-checkbox:focus,
.u-btn-eject .checkbox:focus,
.u-inputitem .checkbox:focus {
    background-image: inherit;
    background-color: #fce3e1;
    color: #666;
    border-color: #999;
}

.u-btn-eject .u-btn-checkbox:focus a,
.u-inputitem .u-btn-checkbox:focus a,
.u-btn-eject .checkbox:focus a,
.u-inputitem .checkbox:focus a {
    color: var(--theme-primary);
}

.u-btn-eject .u-btn-checkbox:focus a:hover,
.u-inputitem .u-btn-checkbox:focus a:hover,
.u-btn-eject .checkbox:focus a:hover,
.u-inputitem .checkbox:focus a:hover {
    color: var(--theme-primary);
}

.u-btn-eject .u-btn-checkbox:focus::-webkit-input-placeholder,
.u-inputitem .u-btn-checkbox:focus::-webkit-input-placeholder,
.u-btn-eject .checkbox:focus::-webkit-input-placeholder,
.u-inputitem .checkbox:focus::-webkit-input-placeholder {
    color: #666;
}

.u-btn-eject .u-btn-checkbox:focus:-moz-placeholder,
.u-inputitem .u-btn-checkbox:focus:-moz-placeholder,
.u-btn-eject .checkbox:focus:-moz-placeholder,
.u-inputitem .checkbox:focus:-moz-placeholder {
    color: #666;
}

.u-btn-eject .u-btn-checkbox:focus::-moz-placeholder,
.u-inputitem .u-btn-checkbox:focus::-moz-placeholder,
.u-btn-eject .checkbox:focus::-moz-placeholder,
.u-inputitem .checkbox:focus::-moz-placeholder {
    color: #666;
}

.u-btn-eject .u-btn-checkbox:focus:-ms-input-placeholder,
.u-inputitem .u-btn-checkbox:focus:-ms-input-placeholder,
.u-btn-eject .checkbox:focus:-ms-input-placeholder,
.u-inputitem .checkbox:focus:-ms-input-placeholder {
    color: #666;
}

.u-btn-eject .u-btn-checkbox.nofocus:focus,
.u-inputitem .u-btn-checkbox.nofocus:focus,
.u-btn-eject .checkbox.nofocus:focus,
.u-inputitem .checkbox.nofocus:focus {
    box-shadow: none;
    background-image: inherit;
}

.u-btn-eject .u-btn-checkbox.err,
.u-inputitem .u-btn-checkbox.err,
.u-btn-eject .checkbox.err,
.u-inputitem .checkbox.err {
    background-image: inherit;
    background-color: #fef8f8;
    color: #82170f;
    border-color: #f5aba6;
}

.u-btn-eject .u-btn-checkbox.err a,
.u-inputitem .u-btn-checkbox.err a,
.u-btn-eject .checkbox.err a,
.u-inputitem .checkbox.err a {
    color: #dd261a;
}

.u-btn-eject .u-btn-checkbox.err a:hover,
.u-inputitem .u-btn-checkbox.err a:hover,
.u-btn-eject .checkbox.err a:hover,
.u-inputitem .checkbox.err a:hover {
    color: var(--theme-primary);
}

.u-btn-eject .u-btn-checkbox.err::-webkit-input-placeholder,
.u-inputitem .u-btn-checkbox.err::-webkit-input-placeholder,
.u-btn-eject .checkbox.err::-webkit-input-placeholder,
.u-inputitem .checkbox.err::-webkit-input-placeholder {
    color: #FF0000;
}

.u-btn-eject .u-btn-checkbox.err:-moz-placeholder,
.u-inputitem .u-btn-checkbox.err:-moz-placeholder,
.u-btn-eject .checkbox.err:-moz-placeholder,
.u-inputitem .checkbox.err:-moz-placeholder {
    color: #FF0000;
}

.u-btn-eject .u-btn-checkbox.err::-moz-placeholder,
.u-inputitem .u-btn-checkbox.err::-moz-placeholder,
.u-btn-eject .checkbox.err::-moz-placeholder,
.u-inputitem .checkbox.err::-moz-placeholder {
    color: #FF0000;
}

.u-btn-eject .u-btn-checkbox.err:-ms-input-placeholder,
.u-inputitem .u-btn-checkbox.err:-ms-input-placeholder,
.u-btn-eject .checkbox.err:-ms-input-placeholder,
.u-inputitem .checkbox.err:-ms-input-placeholder {
    color: #FF0000;
}

.u-btn-eject .u-btn-checkbox:disabled,
.u-inputitem .u-btn-checkbox:disabled,
.u-btn-eject .checkbox:disabled,
.u-inputitem .checkbox:disabled {
    background-image: inherit;
    background: #e6e6e6;
}

.u-btn-eject .u-btn-checkbox.disabled,
.u-inputitem .u-btn-checkbox.disabled,
.u-btn-eject .checkbox.disabled,
.u-inputitem .checkbox.disabled,
.u-btn-eject .u-btn-checkbox.u-diseditor,
.u-inputitem .u-btn-checkbox.u-diseditor,
.u-btn-eject .checkbox.u-diseditor,
.u-inputitem .checkbox.u-diseditor {
    background-image: inherit;
    cursor: pointer;
    background: #e6e6e6;
}

.u-btn-eject .u-btn-checkbox:hover,
.u-inputitem .u-btn-checkbox:hover,
.u-btn-eject .checkbox:hover,
.u-inputitem .checkbox:hover {
    background-image: none !important;
}

.u-btn-eject .multi-select-box,
.u-inputitem .multi-select-box,
.u-btn-eject .multi-box,
.u-inputitem .multi-box {
    padding-left: 3px;
    padding-right: 3px;
    display: flex;
    *padding-bottom: 2px;
    *position: static!important;
}

.u-btn-eject .multi-select-box:hover,
.u-inputitem .multi-select-box:hover,
.u-btn-eject .multi-box:hover,
.u-inputitem .multi-box:hover {
    background-image: none !important;
}

.u-btn-eject .multi-select-box .option-block,
.u-inputitem .multi-select-box .option-block,
.u-btn-eject .multi-box .option-block,
.u-inputitem .multi-box .option-block {
    behavior: none!important;
    position: static;
    z-index: auto;
    margin: 2px 1px;
    margin: 0px 1px 1px 1px\9;
    height: 24px;
    line-height: 24px;
    padding: 0 3px 0 6px;
    *margin: 3px 1px;
    font-size: 12px;
    border-radius: 3px;
    display: inline-block;
    background-color: #e6e6e6;
    color: #808080;
    border-color: #bfbfbf;
    cursor: pointer;
    overflow-y: hidden;
}

.u-btn-eject .multi-select-box .option-block a,
.u-inputitem .multi-select-box .option-block a,
.u-btn-eject .multi-box .option-block a,
.u-inputitem .multi-box .option-block a {
    color: #666666;
}

.u-btn-eject .multi-select-box .option-block a:hover,
.u-inputitem .multi-select-box .option-block a:hover,
.u-btn-eject .multi-box .option-block a:hover,
.u-inputitem .multi-box .option-block a:hover {
    color: #808080;
}

.u-btn-eject .multi-select-box .option-block i.iconfont,
.u-inputitem .multi-select-box .option-block i.iconfont,
.u-btn-eject .multi-box .option-block i.iconfont,
.u-inputitem .multi-box .option-block i.iconfont {
    font-size: 12px;
    margin-left: 4px;
}

.u-btn-eject .multi-select-box .iconfont,
.u-inputitem .multi-select-box .iconfont,
.u-btn-eject .multi-box .iconfont,
.u-inputitem .multi-box .iconfont {
    padding: 0!important;
}

.u-btn-eject .multi-select-box .iconfont:hover,
.u-inputitem .multi-select-box .iconfont:hover,
.u-btn-eject .multi-box .iconfont:hover,
.u-inputitem .multi-box .iconfont:hover {
    color: #2A2A2A;
    cursor: pointer;
}

.u-btn-eject .multi-select-box .option-block,
.u-inputitem .multi-select-box .option-block,
.u-btn-eject .multi-box .option-block,
.u-inputitem .multi-box .option-block,
.u-btn-eject .multi-select-box .input,
.u-inputitem .multi-select-box .input,
.u-btn-eject .multi-box .input,
.u-inputitem .multi-box .input {
    display: inline-flex;
}

.u-btn-eject .multi-select-box .input,
.u-inputitem .multi-select-box .input,
.u-btn-eject .multi-box .input,
.u-inputitem .multi-box .input {
    width: 100%;
    border: none;
    outline: none;
    padding-left: 5px;
}

.m-upload {
    position: relative;
}

.m-upload .upload-box {
    position: absolute;
    background: #fff;
    display: block;
    padding: 10px;
    border: 1px solid #ccc;
    border-radius: 3px;
    box-shadow: 5px 5px 5px rgba(0, 0, 0, 0.1);
}

.m-upload .item-l {
    margin-left: -1px;
}

.u-upload {
    height: 26px;
    border: 1px solid #CCC;
    cursor: pointer;
    position: relative;
    padding: 0;
    border-radius: 3px;
    background-color: #fff;
    text-indent: 0px;
}

.u-upload .thumbnail {
    padding: 1px 0 0 2px;
}

.u-upload .thumbnail img {
    padding-right: 3px;
    width: 16px;
    height: 18px;
}

.u-upload .icon {
    position: absolute;
    right: 0px;
    top: 0px;
    display: inline-block;
    width: 26px;
    height: 26px;
    font-size: 21px;
    line-height: 28px;
    color: #AEBBBB;
}

.u-upload-load {
    display: block;
    float: left;
}

.u-upload-load li {
    display: block;
    float: left;
    width: 101px;
    height: 120px;
    margin: 0 5px;
    border: 1px solid #CCC;
    padding: 5px;
    margin-bottom: 10px;
    overflow: hidden;
    position: relative;
}

.u-upload-load li.last {
    padding: 0px;
    border: none;
    cursor: pointer;
    width: 113px;
    height: 129px;
}

.u-upload-load li p {
    width: 102px;
    height: 23px;
    line-height: 23px;
    border-bottom: 1px solid #66CCFF;
    overflow: hidden;
    margin-bottom: 0px;
}

.u-upload-load li .iconfont {
    float: right;
    color: #EC5944;
    cursor: pointer;
    line-height: 23px;
}

.u-upload-load li .loading {
    width: 102px;
    height: 4px;
    background: #CCCCCC;
    position: absolute;
    left: 5px;
    top: 116px;
    overflow: hidden;
}

.u-upload-load li .loading .speed {
    width: 102px;
    height: 4px;
    background: #66CC00;
    position: absolute;
    left: 0px;
    top: 0px;
    overflow: hidden;
}

.u-upload-down {
    position: absolute;
    left: -1px;
    top: 38px;
    background: #FFF;
}

.u-upload-down li {
    margin: 0;
    margin-right: 5px;
}

.u-upload-path {
    padding: 10px;
}

.u-upload-path span {
    color: #8C8C8C;
}

.u-upload-choice li {
    border: none !important;
    width: 68px;
    height: 105px;
}

.u-upload-choice li img {
    margin-bottom: 10px;
}

.u-upload-choice li p {
    border-bottom: none !important;
    width: 63px;
    text-align: center;
    color: #666;
    height: 18px;
    line-height: 18px;
}

.ts .m-tooltip {
    height: auto!important;
    width: 100%!important;
}

.ts .m-tooltip .u-input-cor {
    margin-top: 5px!important;
}

.ts input.editable-select {
    padding: 2px 8px 2px 10px!important;
}

.ts .editable-select,
.ts .infobox {
    width: 100%!important;
}

.ts input.editable-select {
    border-color: #e6e6e6 !important;
}

.editable-select-options {
    z-index: 9999!important;
    border-color: #e6e6e6 !important;
    border-top: none!important;
}

.editable-select-options ul li.selected {
    background-color: var(--theme-primary) !important;
}

.u-switch {
    height: 30px;
    line-height: 28px;
    padding: 0 5px;
    border-width: 1px;
    border-style: solid;
    border-color: transparent;
    display: inline-block;
    position: relative;
    min-width: 42px;
}

.u-switch .iconfont {
    font-size: 31px;
    cursor: pointer;
    position: relative;
    top: 2px;
    height: 28px;
    line-height: 28px;
    display: block;
}

.u-switch>input {
    display: none;
}

.u-switch.on .iconfont {
    color: #339933;
}

.u-switch.off .iconfont {
    color: #cecece;
}

.u-group {
    position: relative;
    display: block;
    min-width: 100px;
}

.u-group .mark {
    padding: 0 7px;
    border-style: solid;
    border-width: 1px;
    min-width: 35px!important;
}

.u-group>.item,
.u-group .m-tooltip {
    white-space: nowrap;
    border-radius: 0;
    min-width: auto;
    vertical-align: middle;
    float: left;
    *display: block;
    *position: static;
    display: inline-block;
}

.u-group>.item.iconfont,
.u-group .m-tooltip.iconfont {
    border-style: solid;
    border-width: 1px;
    border-color: transparent;
    min-width: 20px;
    text-align: center;
    padding: 0px;
}

.u-group>.item.u-btn,
.u-group .m-tooltip.u-btn,
.u-group>.item.u-input,
.u-group .m-tooltip.u-input,
.u-group>.item.u-checkbox,
.u-group .m-tooltip.u-checkbox,
.u-group>.item.mark,
.u-group .m-tooltip.mark,
.u-group>.item.u-select,
.u-group .m-tooltip.u-select,
.u-group>.item.u-textarea,
.u-group .m-tooltip.u-textarea,
.u-group>.item.u-switch,
.u-group .m-tooltip.u-switch,
.u-group>.item .u-btn,
.u-group .m-tooltip .u-btn,
.u-group>.item .u-input,
.u-group .m-tooltip .u-input,
.u-group>.item .u-checkbox,
.u-group .m-tooltip .u-checkbox,
.u-group>.item .mark,
.u-group .m-tooltip .mark,
.u-group>.item .u-select,
.u-group .m-tooltip .u-select,
.u-group>.item .u-textarea,
.u-group .m-tooltip .u-textarea,
.u-group>.item .u-switch,
.u-group .m-tooltip .u-switch,
.u-group>.item.iconfont,
.u-group .m-tooltip.iconfont {
    border-radius: 0;
    border-right-width: 0;
}

.u-group>.item:first-child,
.u-group .m-tooltip:first-child,
.u-group>.item.item-l,
.u-group .m-tooltip.item-l,
.u-group>.item.item-l .item-l,
.u-group .m-tooltip.item-l .item-l,
.u-group>.item .item-l,
.u-group .m-tooltip .item-l {
    border-top-left-radius: 3px;
    border-bottom-left-radius: 3px;
}

.u-group>.item:last-child,
.u-group .m-tooltip:last-child,
.u-group>.item.item-r,
.u-group .m-tooltip.item-r,
.u-group>.item.item-r .item-r,
.u-group .m-tooltip.item-r .item-r,
.u-group>.item .item-r,
.u-group .m-tooltip .item-r {
    border-top-right-radius: 3px;
    border-bottom-right-radius: 3px;
}

.u-group>.item:last-child.u-btn,
.u-group .m-tooltip:last-child.u-btn,
.u-group>.item.item-r.u-btn,
.u-group .m-tooltip.item-r.u-btn,
.u-group>.item.item-r .item-r.u-btn,
.u-group .m-tooltip.item-r .item-r.u-btn,
.u-group>.item .item-r.u-btn,
.u-group .m-tooltip .item-r.u-btn,
.u-group>.item:last-child.u-input,
.u-group .m-tooltip:last-child.u-input,
.u-group>.item.item-r.u-input,
.u-group .m-tooltip.item-r.u-input,
.u-group>.item.item-r .item-r.u-input,
.u-group .m-tooltip.item-r .item-r.u-input,
.u-group>.item .item-r.u-input,
.u-group .m-tooltip .item-r.u-input,
.u-group>.item:last-child.u-checkbox,
.u-group .m-tooltip:last-child.u-checkbox,
.u-group>.item.item-r.u-checkbox,
.u-group .m-tooltip.item-r.u-checkbox,
.u-group>.item.item-r .item-r.u-checkbox,
.u-group .m-tooltip.item-r .item-r.u-checkbox,
.u-group>.item .item-r.u-checkbox,
.u-group .m-tooltip .item-r.u-checkbox,
.u-group>.item:last-child.mark,
.u-group .m-tooltip:last-child.mark,
.u-group>.item.item-r.mark,
.u-group .m-tooltip.item-r.mark,
.u-group>.item.item-r .item-r.mark,
.u-group .m-tooltip.item-r .item-r.mark,
.u-group>.item .item-r.mark,
.u-group .m-tooltip .item-r.mark,
.u-group>.item:last-child.u-select,
.u-group .m-tooltip:last-child.u-select,
.u-group>.item.item-r.u-select,
.u-group .m-tooltip.item-r.u-select,
.u-group>.item.item-r .item-r.u-select,
.u-group .m-tooltip.item-r .item-r.u-select,
.u-group>.item .item-r.u-select,
.u-group .m-tooltip .item-r.u-select,
.u-group>.item:last-child.u-textarea,
.u-group .m-tooltip:last-child.u-textarea,
.u-group>.item.item-r.u-textarea,
.u-group .m-tooltip.item-r.u-textarea,
.u-group>.item.item-r .item-r.u-textarea,
.u-group .m-tooltip.item-r .item-r.u-textarea,
.u-group>.item .item-r.u-textarea,
.u-group .m-tooltip .item-r.u-textarea,
.u-group>.item:last-child.u-switch,
.u-group .m-tooltip:last-child.u-switch,
.u-group>.item.item-r.u-switch,
.u-group .m-tooltip.item-r.u-switch,
.u-group>.item.item-r .item-r.u-switch,
.u-group .m-tooltip.item-r .item-r.u-switch,
.u-group>.item .item-r.u-switch,
.u-group .m-tooltip .item-r.u-switch {
    border-right-width: 1px;
}

.u-group>.item:last-child.iconfont,
.u-group .m-tooltip:last-child.iconfont,
.u-group>.item.item-r.iconfont,
.u-group .m-tooltip.item-r.iconfont,
.u-group>.item.item-r .item-r.iconfont,
.u-group .m-tooltip.item-r .item-r.iconfont,
.u-group>.item .item-r.iconfont,
.u-group .m-tooltip .item-r.iconfont {
    border-left-width: 0;
    border-right-width: 1px;
}

.u-group>.item:last-child>.m-tooltip .u-input,
.u-group .m-tooltip:last-child>.m-tooltip .u-input,
.u-group>.item.item-r>.m-tooltip .u-input,
.u-group .m-tooltip.item-r>.m-tooltip .u-input,
.u-group>.item.item-r .item-r>.m-tooltip .u-input,
.u-group .m-tooltip.item-r .item-r>.m-tooltip .u-input,
.u-group>.item .item-r>.m-tooltip .u-input,
.u-group .m-tooltip .item-r>.m-tooltip .u-input {
    border-top-right-radius: 3px;
    border-bottom-right-radius: 3px;
    border-right-width: 1px;
}

.u-group>.item .item-l,
.u-group .m-tooltip .item-l {
    border-top-left-radius: 0;
    border-bottom-left-radius: 0;
}

.u-group>.item .item-r,
.u-group .m-tooltip .item-r {
    border-top-right-radius: 0;
    border-bottom-right-radius: 0;
}

.u-group>.item .item-r.u-btn,
.u-group .m-tooltip .item-r.u-btn,
.u-group>.item .item-r.u-input,
.u-group .m-tooltip .item-r.u-input,
.u-group>.item .item-r.u-checkbox,
.u-group .m-tooltip .item-r.u-checkbox,
.u-group>.item .item-r.mark,
.u-group .m-tooltip .item-r.mark,
.u-group>.item .item-r.u-select,
.u-group .m-tooltip .item-r.u-select,
.u-group>.item .item-r.u-textarea,
.u-group .m-tooltip .item-r.u-textarea,
.u-group>.item .item-r.u-switch,
.u-group .m-tooltip .item-r.u-switch {
    border-right-width: 0;
}

.u-group>.item .item-r.iconfont,
.u-group .m-tooltip .item-r.iconfont {
    border-right-width: 0;
}

.u-group div.u-input {
    padding: 0;
    border: none;
    background-image: inherit!important;
}

.u-group .u-input {
    width: 1px;
}

.u-group label.u-btn {
    min-width: 13px;
}

.u-group .m-tooltip {
    float: left;
}

.u-group .item,
.u-group .m-tooltip,
.u-group textarea.u-input,
.u-group textarea.u-textarea,
.u-group select.u-input {
    height: 30px;
    line-height: 28px;
}

.u-group .mark {
    background-color: #fefefe;
}

.u-group .u-btn {
    background-color: #fefefe;
    color: #4c4c4c;
    border-color: #d0d0d0;
    color: #727272 !important;
}

.u-group .u-btn a {
    color: #7f7f7f;
}

.u-group .u-btn a:hover {
    color: #989898;
}

.u-group .u-btn:hover {
    background-color: #ffffff;
    color: #333;
    border-color: #d1d1d1;
    box-shadow: 0 0 8px #fefefe;
}

.u-group .u-btn:hover a {
    color: #808080;
}

.u-group .u-btn:hover a:hover {
    color: #999999;
}

.u-group .u-btn:active,
.u-group .u-btn.active {
    box-shadow: 0 3px 3px #d8d8d8 inset;
}

.u-group .u-btn:hover {
    color: #585858;
}

.u-group .u-btn>.u-point {
    background-color: #fefefe;
    color: #fff;
}

.u-group .u-btn.active {
    background-color: var(--theme-primary);
    color: #fef8f8;
    border-color: #e52e21;
    border-color: var(--theme-primary) !important;
    color: #fff!important;
    box-shadow: none!important;
}

.u-group .u-btn.active a {
    color: #fef8f8;
}

.u-group .u-btn.active a:hover {
    color: #ffffff;
}

.u-group .u-btn.active:hover {
    background-color: #eb594f;
    color: #ffffff;
    border-color: #e73b2f;
    box-shadow: 0 0 8px var(--theme-primary);
}

.u-group .u-btn.active:hover a {
    color: #ffffff;
}

.u-group .u-btn.active:hover a:hover {
    color: #ffffff;
}

.u-group .u-btn.active:active,
.u-group .u-btn.active.active {
    box-shadow: 0 3px 3px #c72217 inset;
}

.u-group .u-btn.active:hover {
    color: #ffffff;
}

.u-group .u-btn.active>.u-point {
    background-color: #fff;
    color: var(--theme-primary);
}

.u-group .item.u-btn,
.u-group .m-tooltip.u-btn,
.u-group .item.u-input,
.u-group .m-tooltip.u-input,
.u-group .item.u-checkbox,
.u-group .m-tooltip.u-checkbox,
.u-group .item.mark,
.u-group .m-tooltip.mark,
.u-group .item.u-select,
.u-group .m-tooltip.u-select,
.u-group .item.u-textarea,
.u-group .m-tooltip.u-textarea,
.u-group .item.u-switch,
.u-group .m-tooltip.u-switch,
.u-group .item .u-btn,
.u-group .m-tooltip .u-btn,
.u-group .item .u-input,
.u-group .m-tooltip .u-input,
.u-group .item .u-checkbox,
.u-group .m-tooltip .u-checkbox,
.u-group .item .mark,
.u-group .m-tooltip .mark,
.u-group .item .u-select,
.u-group .m-tooltip .u-select,
.u-group .item .u-textarea,
.u-group .m-tooltip .u-textarea,
.u-group .item .u-switch,
.u-group .m-tooltip .u-switch,
.u-group .item.iconfont,
.u-group .m-tooltip.iconfont {
    border-color: #ddd;
}

.u-group .u-inputitem {
    padding-left: 0;
    padding-right: 0;
}


/* CSS Document */

.u-float {
    position: absolute;
    z-index: 1000;
    background-color: #FFFFFF;
    box-shadow: -2px 0 4px rgba(0, 0, 0, 0.3);
}

.u-float.hide {
    display: none;
}

.u-float.top {
    left: 0;
    top: 0;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
    width: 100%;
    min-height: 50px;
    border-bottom: #eee 1px solid \9;
}

.u-float.bottom {
    left: 0;
    bottom: 0;
    box-shadow: 0 -2px 4px rgba(0, 0, 0, 0.3);
    width: 100%;
    min-height: 50px;
    border-top: #eee 1px solid \9;
}

.u-float.left {
    left: 0;
    bottom: 0;
    box-shadow: 2px 0 4px rgba(0, 0, 0, 0.3);
    width: 200px;
    height: 100%;
    border-right: #eee 1px solid \9;
}

.u-float.right {
    right: 0;
    bottom: 0;
    box-shadow: -2px 0 4px rgba(0, 0, 0, 0.3);
    width: 200px;
    height: 100%;
    border-left: #eee 1px solid \9;
}

.u-point {
    height: 18px;
    line-height: 16px;
    font-size: 10px;
    text-indent: 0;
    min-width: 18px;
    width: auto;
    display: inline-block;
    border-radius: 50%;
    -moz-border-radius: 50%;
    -webkit-border-radius: 50%;
    text-align: center;
    font-weight: normal;
    border-style: solid;
    border-width: 1px;
    background-color: #cecece;
    color: #686868;
    border-color: #b7b7b7;
}

.u-point .iconfont {
    margin-left: 3px;
    margin-right: 3px;
}

.u-point.iconfont {
    font-size: 18px;
}

.u-point.text {
    border-radius: 3px 3px 3px 3px;
    padding: 0 4px;
    -moz-border-radius: 3px;
    -webkit-border-radius: 3px;
}

.u-point a {
    color: #4f4f4f;
}

.u-point a:hover {
    color: #686868;
}

.m-alertbox {
    width: 300px;
    position: fixed;
    z-index: 9999;
}

.m-alertbox.t-m {
    top: 10px;
    left: 50%;
    margin-left: -170px;
}

.m-alertbox.t-r {
    top: 10px;
    right: 10px;
}

.m-alertbox.b-r {
    bottom: 10px;
    right: 10px;
    border: none;
}

.m-alertbox.c {
    top: 50%;
    left: 50%;
    margin-left: -170px;
    margin-top: -60px;
}

.u-alert,
.m-alertbox .alert {
    cursor: pointer;
    padding: 10px 22px 10px 37px;
    border: 1px solid transparent;
    border-radius: 3px;
    position: relative;
    background-color: #FFFFFF;
    border-color: #eee;
}

.u-alert img,
.m-alertbox .alert img {
    width: 25px;
    height: 25px;
    position: absolute;
    top: 5px;
    left: 6px;
}

.u-alert i,
.m-alertbox .alert i {
    font-size: 16px;
    position: absolute;
    top: 12px;
    left: 8px;
}

.u-alert i.close,
.m-alertbox .alert i.close {
    left: auto;
    top: 6px;
    right: 4px;
    font-size: 12px;
    transition: top 0.2s, right 0.2s;
    -moz-transition: top 0.2s, right 0.2s;
    /* Firefox 4 */
    -webkit-transition: top 0.2s, right 0.2s;
    /* Safari 和 Chrome */
    -o-transition: top 0.2s, right 0.2s;
    /* Opera */
}

.u-alert i.close:hover,
.m-alertbox .alert i.close:hover {
    top: 4px;
    right: 2px;
}

.u-alert i.close:active,
.m-alertbox .alert i.close:active {
    top: 5px;
    right: 3px;
}

.u-alert.alert-default,
.m-alertbox .alert.alert-default,
.u-alert.default,
.m-alertbox .alert.default {
    background-color: #FFFFFF;
}

.u-alert.alert-black,
.m-alertbox .alert.alert-black,
.u-alert.black,
.m-alertbox .alert.black {
    background-color: rgba(0, 0, 0, 0.7);
    color: #fff;
    border-color: #000;
}

.u-alert.alert-success,
.m-alertbox .alert.alert-success,
.u-alert.success,
.m-alertbox .alert.success {
    background-color: #638C32;
    color: #fff;
    border-color: #638C32;
}

.u-alert.alert-info,
.m-alertbox .alert.alert-info,
.u-alert.info,
.m-alertbox .alert.info {
    background-color: #1A70B7;
    color: #fff;
    border-color: #1A70B7;
}

.u-alert.alert-warning,
.m-alertbox .alert.alert-warning,
.u-alert.warning,
.m-alertbox .alert.warning {
    background-color: #FB8808;
    color: #fff;
    border-color: #FB8808;
}

.u-alert.alert-danger,
.m-alertbox .alert.alert-danger,
.u-alert.danger,
.m-alertbox .alert.danger {
    background-color: #CC3B3D;
    color: #fff;
    border-color: #CC3B3D;
}

.u-alert.shadow,
.m-alertbox .alert.shadow,
.u-alert.alert-default,
.m-alertbox .alert.alert-default,
.u-alert.alert-black,
.m-alertbox .alert.alert-black,
.u-alert.alert-success,
.m-alertbox .alert.alert-success,
.u-alert.alert-info,
.m-alertbox .alert.alert-info,
.u-alert.alert-warning,
.m-alertbox .alert.alert-warning,
.u-alert.alert-danger,
.m-alertbox .alert.alert-danger {
    box-shadow: 1px 1px 3px rgba(0, 0, 0, 0.3);
}

.m-menu,
.u-down-menu {
    border-radius: 3px;
    width: 100px;
}

.m-menu>li,
.u-down-menu>li,
.m-menu ul>li,
.u-down-menu ul>li {
    position: relative;
    display: block;
    width: 100%;
    padding: 0;
    margin: 0;
}

.m-menu>li>a,
.u-down-menu>li>a,
.m-menu ul>li>a,
.u-down-menu ul>li>a {
    border-width: 1px;
    border-style: solid;
    width: 100%;
    display: block;
    padding: 0 10px;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    position: relative;
    border-bottom-width: 0;
}

.m-menu>li.first>a,
.u-down-menu>li.first>a,
.m-menu ul>li.first>a,
.u-down-menu ul>li.first>a,
.m-menu>li:first-child>a,
.u-down-menu>li:first-child>a,
.m-menu ul>li:first-child>a,
.u-down-menu ul>li:first-child>a {
    border-radius: 3px 3px 0 0;
}

.m-menu>li.last>a,
.u-down-menu>li.last>a,
.m-menu ul>li.last>a,
.u-down-menu ul>li.last>a,
.m-menu>li:last-child>a,
.u-down-menu>li:last-child>a,
.m-menu ul>li:last-child>a,
.u-down-menu ul>li:last-child>a {
    border-bottom-width: 1px;
    border-radius: 0 0 3px 3px;
}

.m-menu>li.line,
.u-down-menu>li.line,
.m-menu ul>li.line,
.u-down-menu ul>li.line {
    border-top-width: 1px;
    border-top-style: solid;
}

.m-menu>li ul,
.u-down-menu>li ul,
.m-menu ul>li ul,
.u-down-menu ul>li ul {
    position: absolute;
    left: 100%;
    top: 0;
    z-index: 200;
}

.m-menu>li .iconfont,
.u-down-menu>li .iconfont,
.m-menu ul>li .iconfont,
.u-down-menu ul>li .iconfont {
    margin-right: 3px;
}

.m-menu.items>li>a,
.u-down-menu.items>li>a,
.m-menu.items ul>li>a,
.u-down-menu.items ul>li>a {
    border-bottom-width: 1px;
    border-radius: 3px;
}

.m-menu.items>li.first>a,
.u-down-menu.items>li.first>a,
.m-menu.items ul>li.first>a,
.u-down-menu.items ul>li.first>a,
.m-menu.items>li:first-child>a,
.u-down-menu.items>li:first-child>a,
.m-menu.items ul>li:first-child>a,
.u-down-menu.items ul>li:first-child>a {
    border-radius: 3px;
}

.m-menu.items>li.last>a,
.u-down-menu.items>li.last>a,
.m-menu.items ul>li.last>a,
.u-down-menu.items ul>li.last>a,
.m-menu.items>li:last-child>a,
.u-down-menu.items>li:last-child>a,
.m-menu.items ul>li:last-child>a,
.u-down-menu.items ul>li:last-child>a {
    border-radius: 3px;
}

.m-menu.items>li.active>a,
.u-down-menu.items>li.active>a,
.m-menu.items ul>li.active>a,
.u-down-menu.items ul>li.active>a {
    border-left-width: 3px;
    font-size: 14px;
}

.m-menu>li>a,
.u-down-menu>li>a,
.m-menu ul>li>a,
.u-down-menu ul>li>a {
    height: 30px;
    line-height: 29px;
    background-color: #ffffff;
    color: #333;
    border-color: #d1d1d1;
}

.m-menu>li>a a,
.u-down-menu>li>a a,
.m-menu ul>li>a a,
.u-down-menu ul>li>a a {
    color: #808080;
}

.m-menu>li>a a:hover,
.u-down-menu>li>a a:hover,
.m-menu ul>li>a a:hover,
.u-down-menu ul>li>a a:hover {
    color: #999999;
}

.m-menu>li>a:hover,
.u-down-menu>li>a:hover,
.m-menu ul>li>a:hover,
.u-down-menu ul>li>a:hover {
    background-color: #f2f2f2;
    color: #404040;
    border-color: #c4c4c4;
}

.m-menu>li>a:hover a,
.u-down-menu>li>a:hover a,
.m-menu ul>li>a:hover a,
.u-down-menu ul>li>a:hover a {
    color: #737373;
}

.m-menu>li>a:hover a:hover,
.u-down-menu>li>a:hover a:hover,
.m-menu ul>li>a:hover a:hover,
.u-down-menu ul>li>a:hover a:hover {
    color: #8c8c8c;
}

.m-menu>li.active>a,
.u-down-menu>li.active>a,
.m-menu ul>li.active>a,
.u-down-menu ul>li.active>a {
    background-color: var(--theme-primary);
    color: #fef8f8;
    border-color: #e52e21;
    border-color: var(--theme-primary);
}

.m-menu>li.active>a a,
.u-down-menu>li.active>a a,
.m-menu ul>li.active>a a,
.u-down-menu ul>li.active>a a {
    color: #fef8f8;
}

.m-menu>li.active>a a:hover,
.u-down-menu>li.active>a a:hover,
.m-menu ul>li.active>a a:hover,
.u-down-menu ul>li.active>a a:hover {
    color: #ffffff;
}

.m-menu>li.line,
.u-down-menu>li.line,
.m-menu ul>li.line,
.u-down-menu ul>li.line {
    background-color: #ffffff;
    color: #333;
    border-color: #d1d1d1;
}

.m-menu>li.line a,
.u-down-menu>li.line a,
.m-menu ul>li.line a,
.u-down-menu ul>li.line a {
    color: #808080;
}

.m-menu>li.line a:hover,
.u-down-menu>li.line a:hover,
.m-menu ul>li.line a:hover,
.u-down-menu ul>li.line a:hover {
    color: #999999;
}

.m-menu.items>li,
.u-down-menu.items>li,
.m-menu.items ul>li,
.u-down-menu.items ul>li {
    margin-bottom: 6px;
}

.m-menu.items>li.active>a,
.u-down-menu.items>li.active>a,
.m-menu.items ul>li.active>a,
.u-down-menu.items ul>li.active>a {
    background-color: #ffffff;
    color: #333;
    border-color: #d1d1d1;
    border-left-color: var(--theme-primary);
    color: var(--theme-primary);
}

.m-menu.items>li.active>a a,
.u-down-menu.items>li.active>a a,
.m-menu.items ul>li.active>a a,
.u-down-menu.items ul>li.active>a a {
    color: #808080;
}

.m-menu.items>li.active>a a:hover,
.u-down-menu.items>li.active>a a:hover,
.m-menu.items ul>li.active>a a:hover,
.u-down-menu.items ul>li.active>a a:hover {
    color: #999999;
}

.g-combo,
.m-combo {
    position: relative;
    *position: static;
    display: inline-block;
    min-width: 140px;
    float: left;
}

.g-combo .u-group,
.m-combo .u-group {
    width: 100%;
}

.g-combo .u-group .iconfont,
.m-combo .u-group .iconfont {
    cursor: pointer;
}

.g-combo .u-group .item.iconfont,
.m-combo .u-group .item.iconfont {
    min-width: 20px;
    text-align: center;
    padding: 0px;
}

.g-combo .u-group .u-input,
.m-combo .u-group .u-input {
    cursor: pointer;
}

.g-combo .combo,
.m-combo .combo {
    display: none;
    float: left;
    position: absolute;
    left: 0;
    z-index: 9999;
    margin-top: -1px;
    overflow: auto;
    min-width: 100%;
    box-shadow: 2px 2px 2px rgba(0, 0, 0, 0.1);
    border-width: 0;
    border-style: solid;
    border-top-width: 0px;
}

.g-combo .combo.m-menu>li.first>a,
.m-combo .combo.m-menu>li.first>a,
.g-combo .combo.m-menu>li:first-child>a,
.m-combo .combo.m-menu>li:first-child>a {
    border-radius: 0;
}

.g-combo .u-group .mark,
.m-combo .u-group .mark {
    background-color: #fff;
}

.g-combo .u-group .u-btn,
.m-combo .u-group .u-btn {
    background-color: #fff;
    color: #333;
    border-color: #d1d1d1;
    color: #737373 !important;
}

.g-combo .u-group .u-btn a,
.m-combo .u-group .u-btn a {
    color: #808080;
}

.g-combo .u-group .u-btn a:hover,
.m-combo .u-group .u-btn a:hover {
    color: #999999;
}

.g-combo .u-group .u-btn:hover,
.m-combo .u-group .u-btn:hover {
    background-color: #ffffff;
    color: #333;
    border-color: #d1d1d1;
    box-shadow: 0 0 8px #fff;
}

.g-combo .u-group .u-btn:hover a,
.m-combo .u-group .u-btn:hover a {
    color: #808080;
}

.g-combo .u-group .u-btn:hover a:hover,
.m-combo .u-group .u-btn:hover a:hover {
    color: #999999;
}

.g-combo .u-group .u-btn:active,
.m-combo .u-group .u-btn:active,
.g-combo .u-group .u-btn.active,
.m-combo .u-group .u-btn.active {
    box-shadow: 0 3px 3px #d9d9d9 inset;
}

.g-combo .u-group .u-btn:hover,
.m-combo .u-group .u-btn:hover {
    color: #595959;
}

.g-combo .u-group .u-btn>.u-point,
.m-combo .u-group .u-btn>.u-point {
    background-color: #fff;
    color: #fff;
}

.g-combo .u-group .u-btn.active,
.m-combo .u-group .u-btn.active {
    background-color: var(--theme-primary);
    color: #fef8f8;
    border-color: #e52e21;
    border-color: var(--theme-primary) !important;
    color: #fff!important;
    box-shadow: none!important;
}

.g-combo .u-group .u-btn.active a,
.m-combo .u-group .u-btn.active a {
    color: #fef8f8;
}

.g-combo .u-group .u-btn.active a:hover,
.m-combo .u-group .u-btn.active a:hover {
    color: #ffffff;
}

.g-combo .u-group .u-btn.active:hover,
.m-combo .u-group .u-btn.active:hover {
    background-color: #eb594f;
    color: #ffffff;
    border-color: #e73b2f;
    box-shadow: 0 0 8px var(--theme-primary);
}

.g-combo .u-group .u-btn.active:hover a,
.m-combo .u-group .u-btn.active:hover a {
    color: #ffffff;
}

.g-combo .u-group .u-btn.active:hover a:hover,
.m-combo .u-group .u-btn.active:hover a:hover {
    color: #ffffff;
}

.g-combo .u-group .u-btn.active:active,
.m-combo .u-group .u-btn.active:active,
.g-combo .u-group .u-btn.active.active,
.m-combo .u-group .u-btn.active.active {
    box-shadow: 0 3px 3px #c72217 inset;
}

.g-combo .u-group .u-btn.active:hover,
.m-combo .u-group .u-btn.active:hover {
    color: #ffffff;
}

.g-combo .u-group .u-btn.active>.u-point,
.m-combo .u-group .u-btn.active>.u-point {
    background-color: #fff;
    color: var(--theme-primary);
}

.g-combo .u-group .item.u-btn,
.m-combo .u-group .item.u-btn,
.g-combo .u-group .m-tooltip.u-btn,
.m-combo .u-group .m-tooltip.u-btn,
.g-combo .u-group .item.u-input,
.m-combo .u-group .item.u-input,
.g-combo .u-group .m-tooltip.u-input,
.m-combo .u-group .m-tooltip.u-input,
.g-combo .u-group .item.u-checkbox,
.m-combo .u-group .item.u-checkbox,
.g-combo .u-group .m-tooltip.u-checkbox,
.m-combo .u-group .m-tooltip.u-checkbox,
.g-combo .u-group .item.mark,
.m-combo .u-group .item.mark,
.g-combo .u-group .m-tooltip.mark,
.m-combo .u-group .m-tooltip.mark,
.g-combo .u-group .item.u-select,
.m-combo .u-group .item.u-select,
.g-combo .u-group .m-tooltip.u-select,
.m-combo .u-group .m-tooltip.u-select,
.g-combo .u-group .item.u-textarea,
.m-combo .u-group .item.u-textarea,
.g-combo .u-group .m-tooltip.u-textarea,
.m-combo .u-group .m-tooltip.u-textarea,
.g-combo .u-group .item.u-switch,
.m-combo .u-group .item.u-switch,
.g-combo .u-group .m-tooltip.u-switch,
.m-combo .u-group .m-tooltip.u-switch,
.g-combo .u-group .item .u-btn,
.m-combo .u-group .item .u-btn,
.g-combo .u-group .m-tooltip .u-btn,
.m-combo .u-group .m-tooltip .u-btn,
.g-combo .u-group .item .u-input,
.m-combo .u-group .item .u-input,
.g-combo .u-group .m-tooltip .u-input,
.m-combo .u-group .m-tooltip .u-input,
.g-combo .u-group .item .u-checkbox,
.m-combo .u-group .item .u-checkbox,
.g-combo .u-group .m-tooltip .u-checkbox,
.m-combo .u-group .m-tooltip .u-checkbox,
.g-combo .u-group .item .mark,
.m-combo .u-group .item .mark,
.g-combo .u-group .m-tooltip .mark,
.m-combo .u-group .m-tooltip .mark,
.g-combo .u-group .item .u-select,
.m-combo .u-group .item .u-select,
.g-combo .u-group .m-tooltip .u-select,
.m-combo .u-group .m-tooltip .u-select,
.g-combo .u-group .item .u-textarea,
.m-combo .u-group .item .u-textarea,
.g-combo .u-group .m-tooltip .u-textarea,
.m-combo .u-group .m-tooltip .u-textarea,
.g-combo .u-group .item .u-switch,
.m-combo .u-group .item .u-switch,
.g-combo .u-group .m-tooltip .u-switch,
.m-combo .u-group .m-tooltip .u-switch,
.g-combo .u-group .item.iconfont,
.m-combo .u-group .item.iconfont,
.g-combo .u-group .m-tooltip.iconfont,
.m-combo .u-group .m-tooltip.iconfont {
    border-color: #ebebeb;
}

.g-combo .u-group .u-input:focus,
.m-combo .u-group .u-input:focus {
    background-color: #fff;
    color: #333;
    border-color: #d1d1d1;
}

.g-combo .u-group .u-input:focus a,
.m-combo .u-group .u-input:focus a {
    color: #808080;
}

.g-combo .u-group .u-input:focus a:hover,
.m-combo .u-group .u-input:focus a:hover {
    color: #999999;
}

.g-combo .combo,
.m-combo .combo {
    background-color: #fff;
    color: #333;
    border-color: #d1d1d1;
}

.g-combo .combo a,
.m-combo .combo a {
    color: #808080;
}

.g-combo .combo a:hover,
.m-combo .combo a:hover {
    color: #999999;
}

.g-combo .combo.m-menu,
.m-combo .combo.m-menu {
    width: 100px;
}

.g-combo .combo.m-menu>li>a,
.m-combo .combo.m-menu>li>a,
.g-combo .combo.m-menu ul>li>a,
.m-combo .combo.m-menu ul>li>a {
    height: 26px;
    line-height: 25px;
    background-color: #fff;
    color: #333;
    border-color: #d1d1d1;
}

.g-combo .combo.m-menu>li>a a,
.m-combo .combo.m-menu>li>a a,
.g-combo .combo.m-menu ul>li>a a,
.m-combo .combo.m-menu ul>li>a a {
    color: #808080;
}

.g-combo .combo.m-menu>li>a a:hover,
.m-combo .combo.m-menu>li>a a:hover,
.g-combo .combo.m-menu ul>li>a a:hover,
.m-combo .combo.m-menu ul>li>a a:hover {
    color: #999999;
}

.g-combo .combo.m-menu>li>a:hover,
.m-combo .combo.m-menu>li>a:hover,
.g-combo .combo.m-menu ul>li>a:hover,
.m-combo .combo.m-menu ul>li>a:hover {
    background-color: #f2f2f2;
    color: #404040;
    border-color: #c4c4c4;
}

.g-combo .combo.m-menu>li>a:hover a,
.m-combo .combo.m-menu>li>a:hover a,
.g-combo .combo.m-menu ul>li>a:hover a,
.m-combo .combo.m-menu ul>li>a:hover a {
    color: #737373;
}

.g-combo .combo.m-menu>li>a:hover a:hover,
.m-combo .combo.m-menu>li>a:hover a:hover,
.g-combo .combo.m-menu ul>li>a:hover a:hover,
.m-combo .combo.m-menu ul>li>a:hover a:hover {
    color: #8c8c8c;
}

.g-combo .combo.m-menu>li.active>a,
.m-combo .combo.m-menu>li.active>a,
.g-combo .combo.m-menu ul>li.active>a,
.m-combo .combo.m-menu ul>li.active>a {
    background-color: var(--theme-primary);
    color: #fef8f8;
    border-color: #e52e21;
    border-color: var(--theme-primary);
}

.g-combo .combo.m-menu>li.active>a a,
.m-combo .combo.m-menu>li.active>a a,
.g-combo .combo.m-menu ul>li.active>a a,
.m-combo .combo.m-menu ul>li.active>a a {
    color: #fef8f8;
}

.g-combo .combo.m-menu>li.active>a a:hover,
.m-combo .combo.m-menu>li.active>a a:hover,
.g-combo .combo.m-menu ul>li.active>a a:hover,
.m-combo .combo.m-menu ul>li.active>a a:hover {
    color: #ffffff;
}

.g-combo .combo.m-menu>li.line,
.m-combo .combo.m-menu>li.line,
.g-combo .combo.m-menu ul>li.line,
.m-combo .combo.m-menu ul>li.line {
    background-color: #fff;
    color: #333;
    border-color: #d1d1d1;
}

.g-combo .combo.m-menu>li.line a,
.m-combo .combo.m-menu>li.line a,
.g-combo .combo.m-menu ul>li.line a,
.m-combo .combo.m-menu ul>li.line a {
    color: #808080;
}

.g-combo .combo.m-menu>li.line a:hover,
.m-combo .combo.m-menu>li.line a:hover,
.g-combo .combo.m-menu ul>li.line a:hover,
.m-combo .combo.m-menu ul>li.line a:hover {
    color: #999999;
}

.g-combo .combo.m-menu.items>li,
.m-combo .combo.m-menu.items>li,
.g-combo .combo.m-menu.items ul>li,
.m-combo .combo.m-menu.items ul>li {
    margin-bottom: 6px;
}

.g-combo .combo.m-menu.items>li.active>a,
.m-combo .combo.m-menu.items>li.active>a,
.g-combo .combo.m-menu.items ul>li.active>a,
.m-combo .combo.m-menu.items ul>li.active>a {
    background-color: #fff;
    color: #333;
    border-color: #d1d1d1;
    border-left-color: var(--theme-primary);
    color: var(--theme-primary);
}

.g-combo .combo.m-menu.items>li.active>a a,
.m-combo .combo.m-menu.items>li.active>a a,
.g-combo .combo.m-menu.items ul>li.active>a a,
.m-combo .combo.m-menu.items ul>li.active>a a {
    color: #808080;
}

.g-combo .combo.m-menu.items>li.active>a a:hover,
.m-combo .combo.m-menu.items>li.active>a a:hover,
.g-combo .combo.m-menu.items ul>li.active>a a:hover,
.m-combo .combo.m-menu.items ul>li.active>a a:hover {
    color: #999999;
}

.m-tooltip {
    position: relative;
    height: 30px;
    z-index: 1000;
}

.m-tooltip:after {
    content: "\20";
    display: block;
    height: 0;
    clear: both;
    visibility: hidden;
}

.m-tooltip .infobox {
    position: absolute;
    left: 0;
    z-index: 9999;
}

.m-tooltip .infobox i {
    position: absolute;
    top: -12px;
    left: 5px;
    color: rgba(0, 0, 0, 0.8);
}

.m-tooltip .infobox i.l {
    top: 5px;
    left: auto;
    right: -12px !important;
    font-size: 18px;
}

.m-tooltip .infobox i.t {
    top: auto;
    bottom: -11px;
    left: 5px;
    font-size: 18px;
}

.m-tooltip .infobox i.r {
    top: auto;
    bottom: 5px;
    left: -12px;
    font-size: 18px;
}

.m-tooltip .infobox i.b {
    top: -10px;
    *top: -9px;
    bottom: auto;
    left: 5px;
    font-size: 18px;
}

.m-tooltip .infobox .content {
    color: #fff;
    background: url("../img/bg-bg.png");
    padding: 5px;
    border-radius: 3px;
    line-height: 18px !important;
    font-size: 12px;
}

.m-tooltip .infobox .iconfont {
    line-height: 18px !important;
}

.m-tooltip.terr .infobox .content {
    background: #FF0004;
}

.m-tooltip.terr .infobox i {
    color: #FF0004;
}

.m-tooltip.u-input {
    padding: 0px;
    border: none;
    height: auto;
    text-indent: 0px;
}

.m-tooltip .ie-placeholder {
    position: absolute;
    display: block;
    float: left;
    top: 1px;
    left: 8px;
    z-index: 3;
}

.m-toolbar>.item,
.m-toolbar>.title {
    float: left;
    display: inline-block;
}

.m-toolbar>.item {
    margin-left: 3px;
    margin-right: 3px;
}

.m-toolbar>.title {
    padding: 0 8px;
    font-weight: 500;
}

.m-pagebar {
    zoom: 1;
}

.m-pagebar:after {
    display: block;
    clear: both;
    visibility: hidden;
    overflow: hidden;
    content: "\20";
}

.m-pagebar .u-input {
    float: none !important;
    display: inline-block;
    text-align: center;
    text-indent: 0px;
}

.m-panel {
    border-style: solid;
    border-width: 0;
    border-color: transparent;
}

.m-panel .panel-head {
    position: relative;
    border-bottom-style: solid;
    border-bottom-width: 1px;
}

.m-panel .panel-head>.title,
.m-panel .panel-head>.item {
    display: inline-block;
    float: left;
}

.m-panel .panel-head>.title {
    font-weight: 400;
}

.m-panel .panel-head>.title .iconfont {
    margin-right: 2px;
    margin-top: -2px;
}

.m-panel .panel-head .more {
    display: inline-block;
    float: right;
    border-radius: 5px;
    height: 20px;
    line-height: 20px;
    font-size: 12px;
    padding: 0 8px;
    margin-right: 5px;
}

.m-panel .panel-head .m-toolbar {
    padding: 0 10px;
    background-color: transparent;
}

.m-panel .panel-head .m-toolbar>.title,
.m-panel .panel-head .m-toolbar>.item {
    margin: 6px 2px;
    padding: 0;
}

.m-panel .panel-head .m-toolbar>.title.u-btn,
.m-panel .panel-head .m-toolbar>.item.u-btn {
    padding: 0 8px;
}

.m-panel .panel-head .m-toolbar .u-group.f-right:first-child .mark,
.m-panel .panel-head .m-toolbar>.item.f-right:first-child .u-group .mark {
    background-color: var(--theme-primary);
}

.m-panel .panel-head .m-toolbar .u-group.f-right:first-child .u-btn,
.m-panel .panel-head .m-toolbar>.item.f-right:first-child .u-group .u-btn {
    background-color: var(--theme-primary);
    color: #fef8f8;
    border-color: #e52e21;
    color: #fff!important;
}

.m-panel .panel-head .m-toolbar .u-group.f-right:first-child .u-btn a,
.m-panel .panel-head .m-toolbar>.item.f-right:first-child .u-group .u-btn a {
    color: #fef8f8;
}

.m-panel .panel-head .m-toolbar .u-group.f-right:first-child .u-btn a:hover,
.m-panel .panel-head .m-toolbar>.item.f-right:first-child .u-group .u-btn a:hover {
    color: #ffffff;
}

.m-panel .panel-head .m-toolbar .u-group.f-right:first-child .u-btn:hover,
.m-panel .panel-head .m-toolbar>.item.f-right:first-child .u-group .u-btn:hover {
    background-color: #eb594f;
    color: #ffffff;
    border-color: #e73b2f;
    box-shadow: 0 0 8px var(--theme-primary);
}

.m-panel .panel-head .m-toolbar .u-group.f-right:first-child .u-btn:hover a,
.m-panel .panel-head .m-toolbar>.item.f-right:first-child .u-group .u-btn:hover a {
    color: #ffffff;
}

.m-panel .panel-head .m-toolbar .u-group.f-right:first-child .u-btn:hover a:hover,
.m-panel .panel-head .m-toolbar>.item.f-right:first-child .u-group .u-btn:hover a:hover {
    color: #ffffff;
}

.m-panel .panel-head .m-toolbar .u-group.f-right:first-child .u-btn:active,
.m-panel .panel-head .m-toolbar>.item.f-right:first-child .u-group .u-btn:active,
.m-panel .panel-head .m-toolbar .u-group.f-right:first-child .u-btn.active,
.m-panel .panel-head .m-toolbar>.item.f-right:first-child .u-group .u-btn.active {
    box-shadow: 0 3px 3px #c72217 inset;
}

.m-panel .panel-head .m-toolbar .u-group.f-right:first-child .u-btn:hover,
.m-panel .panel-head .m-toolbar>.item.f-right:first-child .u-group .u-btn:hover {
    color: #ffffff;
}

.m-panel .panel-head .m-toolbar .u-group.f-right:first-child .u-btn>.u-point,
.m-panel .panel-head .m-toolbar>.item.f-right:first-child .u-group .u-btn>.u-point {
    background-color: #fff;
    color: var(--theme-primary);
}

.m-panel .panel-head .m-toolbar .u-group.f-right:first-child .u-btn.active,
.m-panel .panel-head .m-toolbar>.item.f-right:first-child .u-group .u-btn.active {
    background-color: var(--theme-primary);
    color: #fef8f8;
    border-color: #e52e21;
    border-color: var(--theme-primary) !important;
    color: #fff!important;
    box-shadow: none!important;
}

.m-panel .panel-head .m-toolbar .u-group.f-right:first-child .u-btn.active a,
.m-panel .panel-head .m-toolbar>.item.f-right:first-child .u-group .u-btn.active a {
    color: #fef8f8;
}

.m-panel .panel-head .m-toolbar .u-group.f-right:first-child .u-btn.active a:hover,
.m-panel .panel-head .m-toolbar>.item.f-right:first-child .u-group .u-btn.active a:hover {
    color: #ffffff;
}

.m-panel .panel-head .m-toolbar .u-group.f-right:first-child .u-btn.active:hover,
.m-panel .panel-head .m-toolbar>.item.f-right:first-child .u-group .u-btn.active:hover {
    background-color: #eb594f;
    color: #ffffff;
    border-color: #e73b2f;
    box-shadow: 0 0 8px var(--theme-primary);
}

.m-panel .panel-head .m-toolbar .u-group.f-right:first-child .u-btn.active:hover a,
.m-panel .panel-head .m-toolbar>.item.f-right:first-child .u-group .u-btn.active:hover a {
    color: #ffffff;
}

.m-panel .panel-head .m-toolbar .u-group.f-right:first-child .u-btn.active:hover a:hover,
.m-panel .panel-head .m-toolbar>.item.f-right:first-child .u-group .u-btn.active:hover a:hover {
    color: #ffffff;
}

.m-panel .panel-head .m-toolbar .u-group.f-right:first-child .u-btn.active:active,
.m-panel .panel-head .m-toolbar>.item.f-right:first-child .u-group .u-btn.active:active,
.m-panel .panel-head .m-toolbar .u-group.f-right:first-child .u-btn.active.active,
.m-panel .panel-head .m-toolbar>.item.f-right:first-child .u-group .u-btn.active.active {
    box-shadow: 0 3px 3px #c72217 inset;
}

.m-panel .panel-head .m-toolbar .u-group.f-right:first-child .u-btn.active:hover,
.m-panel .panel-head .m-toolbar>.item.f-right:first-child .u-group .u-btn.active:hover {
    color: #ffffff;
}

.m-panel .panel-head .m-toolbar .u-group.f-right:first-child .u-btn.active>.u-point,
.m-panel .panel-head .m-toolbar>.item.f-right:first-child .u-group .u-btn.active>.u-point {
    background-color: #fff;
    color: var(--theme-primary);
}

.m-panel .panel-head .m-toolbar .u-group.f-right:first-child .item.u-btn,
.m-panel .panel-head .m-toolbar>.item.f-right:first-child .u-group .item.u-btn,
.m-panel .panel-head .m-toolbar .u-group.f-right:first-child .m-tooltip.u-btn,
.m-panel .panel-head .m-toolbar>.item.f-right:first-child .u-group .m-tooltip.u-btn,
.m-panel .panel-head .m-toolbar .u-group.f-right:first-child .item.u-input,
.m-panel .panel-head .m-toolbar>.item.f-right:first-child .u-group .item.u-input,
.m-panel .panel-head .m-toolbar .u-group.f-right:first-child .m-tooltip.u-input,
.m-panel .panel-head .m-toolbar>.item.f-right:first-child .u-group .m-tooltip.u-input,
.m-panel .panel-head .m-toolbar .u-group.f-right:first-child .item.u-checkbox,
.m-panel .panel-head .m-toolbar>.item.f-right:first-child .u-group .item.u-checkbox,
.m-panel .panel-head .m-toolbar .u-group.f-right:first-child .m-tooltip.u-checkbox,
.m-panel .panel-head .m-toolbar>.item.f-right:first-child .u-group .m-tooltip.u-checkbox,
.m-panel .panel-head .m-toolbar .u-group.f-right:first-child .item.mark,
.m-panel .panel-head .m-toolbar>.item.f-right:first-child .u-group .item.mark,
.m-panel .panel-head .m-toolbar .u-group.f-right:first-child .m-tooltip.mark,
.m-panel .panel-head .m-toolbar>.item.f-right:first-child .u-group .m-tooltip.mark,
.m-panel .panel-head .m-toolbar .u-group.f-right:first-child .item.u-select,
.m-panel .panel-head .m-toolbar>.item.f-right:first-child .u-group .item.u-select,
.m-panel .panel-head .m-toolbar .u-group.f-right:first-child .m-tooltip.u-select,
.m-panel .panel-head .m-toolbar>.item.f-right:first-child .u-group .m-tooltip.u-select,
.m-panel .panel-head .m-toolbar .u-group.f-right:first-child .item.u-textarea,
.m-panel .panel-head .m-toolbar>.item.f-right:first-child .u-group .item.u-textarea,
.m-panel .panel-head .m-toolbar .u-group.f-right:first-child .m-tooltip.u-textarea,
.m-panel .panel-head .m-toolbar>.item.f-right:first-child .u-group .m-tooltip.u-textarea,
.m-panel .panel-head .m-toolbar .u-group.f-right:first-child .item.u-switch,
.m-panel .panel-head .m-toolbar>.item.f-right:first-child .u-group .item.u-switch,
.m-panel .panel-head .m-toolbar .u-group.f-right:first-child .m-tooltip.u-switch,
.m-panel .panel-head .m-toolbar>.item.f-right:first-child .u-group .m-tooltip.u-switch,
.m-panel .panel-head .m-toolbar .u-group.f-right:first-child .item .u-btn,
.m-panel .panel-head .m-toolbar>.item.f-right:first-child .u-group .item .u-btn,
.m-panel .panel-head .m-toolbar .u-group.f-right:first-child .m-tooltip .u-btn,
.m-panel .panel-head .m-toolbar>.item.f-right:first-child .u-group .m-tooltip .u-btn,
.m-panel .panel-head .m-toolbar .u-group.f-right:first-child .item .u-input,
.m-panel .panel-head .m-toolbar>.item.f-right:first-child .u-group .item .u-input,
.m-panel .panel-head .m-toolbar .u-group.f-right:first-child .m-tooltip .u-input,
.m-panel .panel-head .m-toolbar>.item.f-right:first-child .u-group .m-tooltip .u-input,
.m-panel .panel-head .m-toolbar .u-group.f-right:first-child .item .u-checkbox,
.m-panel .panel-head .m-toolbar>.item.f-right:first-child .u-group .item .u-checkbox,
.m-panel .panel-head .m-toolbar .u-group.f-right:first-child .m-tooltip .u-checkbox,
.m-panel .panel-head .m-toolbar>.item.f-right:first-child .u-group .m-tooltip .u-checkbox,
.m-panel .panel-head .m-toolbar .u-group.f-right:first-child .item .mark,
.m-panel .panel-head .m-toolbar>.item.f-right:first-child .u-group .item .mark,
.m-panel .panel-head .m-toolbar .u-group.f-right:first-child .m-tooltip .mark,
.m-panel .panel-head .m-toolbar>.item.f-right:first-child .u-group .m-tooltip .mark,
.m-panel .panel-head .m-toolbar .u-group.f-right:first-child .item .u-select,
.m-panel .panel-head .m-toolbar>.item.f-right:first-child .u-group .item .u-select,
.m-panel .panel-head .m-toolbar .u-group.f-right:first-child .m-tooltip .u-select,
.m-panel .panel-head .m-toolbar>.item.f-right:first-child .u-group .m-tooltip .u-select,
.m-panel .panel-head .m-toolbar .u-group.f-right:first-child .item .u-textarea,
.m-panel .panel-head .m-toolbar>.item.f-right:first-child .u-group .item .u-textarea,
.m-panel .panel-head .m-toolbar .u-group.f-right:first-child .m-tooltip .u-textarea,
.m-panel .panel-head .m-toolbar>.item.f-right:first-child .u-group .m-tooltip .u-textarea,
.m-panel .panel-head .m-toolbar .u-group.f-right:first-child .item .u-switch,
.m-panel .panel-head .m-toolbar>.item.f-right:first-child .u-group .item .u-switch,
.m-panel .panel-head .m-toolbar .u-group.f-right:first-child .m-tooltip .u-switch,
.m-panel .panel-head .m-toolbar>.item.f-right:first-child .u-group .m-tooltip .u-switch,
.m-panel .panel-head .m-toolbar .u-group.f-right:first-child .item.iconfont,
.m-panel .panel-head .m-toolbar>.item.f-right:first-child .u-group .item.iconfont,
.m-panel .panel-head .m-toolbar .u-group.f-right:first-child .m-tooltip.iconfont,
.m-panel .panel-head .m-toolbar>.item.f-right:first-child .u-group .m-tooltip.iconfont {
    border-color: var(--theme-primary);
}

.m-panel .panel-foot .m-pagebar {
    padding: 5px;
}

.m-panel .panel-foot .m-pagebar .u-btn {
    margin-left: -3px;
}

.m-panel .panel-foot .m-pagebar .u-input {
    margin-bottom: -6px;
}

.m-panel.f-b {
    border-width: 1px;
}

.m-panel.f-r {
    border-radius: 5px;
}

.m-panel.f-r .panel-head {
    border-radius: 5px 5px 0 0;
}

.m-panel.f-r .panel-body {
    border-radius: 0 0 5px 5px;
}

.m-panel.window {
    box-shadow: 3px 3px 9px 3px rgba(0, 0, 0, 0.3);
    border-style: solid;
    border-width: 1px;
    border-radius: 3px;
    background-color: #fff;
}

.m-panel.window .panel-head {
    border-radius: 3px 3px 0 0;
}

.m-panel.window .panel-head .m-toolbar .item:first-child {
    padding-left: 10px;
}

.m-panel.window .panel-head .m-toolbar .u-group {
    min-width: 30px;
}

.m-panel.window .panel-head .m-toolbar .u-group .u-btn.sm {
    height: 33px;
    line-height: 31px;
}

.m-panel.window .panel-body {
    margin: 5px 5px 0 5px;
}

.m-panel.window .panel-foot {
    text-align: right;
    padding: 5px;
}

.m-panel.window .panel-foot .u-btn {
    padding-left: 8px;
    padding-right: 8px;
    margin: 0 1px;
}

.m-panel.window.an_d_massage {
    background: #fff !important;
}

.m-panel.window.an_d_massage .panel-head {
    border-bottom: none;
    background: #fff !important;
}

.m-panel.window.an_d_massage .panel-head .m-toolbar {
    padding-left: 0px;
    padding-right: 0px;
    background: #fff !important;
}

.m-panel.window.an_d_massage .panel-head .m-toolbar .u-group .u-btn,
.m-panel.window.an_d_massage .panel-head .m-toolbar .u-group .item .u-btn {
    border: none !important;
    background: none !important;
}

.m-panel.window.an_d_massage .panel-body .panel-massage-box .panel-massage-icon {
    font-size: 64px;
    width: 64px;
    height: 64px;
    float: left;
    margin: 0 10px;
}

.m-panel.window.an_d_massage .panel-body .panel-massage-box .panel-massage {
    padding: 0 10px 0 100px;
}

.m-panel.window.an_d_massage .panel-foot {
    background: #fff !important;
}

.m-panel.window.zoomIn {
    animation: zoomIn 0.3s;
    -moz-animation: zoomIn 0.3s;
    -webkit-animation: zoomIn 0.3s;
    -o-animation: zoomIn 0.3s;
}

.m-panel.window.zoomOut {
    animation: zoomOut 0.3s;
    -moz-animation: zoomOut 0.3s;
    -webkit-animation: zoomOut 0.3s;
    -o-animation: zoomOut 0.3s;
}

.m-panel.fit {
    position: relative;
    height: 100%;
    width: 100%;
}

.m-panel.fit .panel-head {
    position: absolute;
    left: 0;
    top: 0;
    width: 100%;
    z-index: 999;
}

.m-panel.fit.f-r .panel-body {
    border-radius: 5px;
}

.m-panel.fit .panel-body {
    padding: 45px 0 0 0;
    height: 100%;
    width: 100%;
    overflow: auto;
}

.m-panel.fit .panel-body.f-ng-p .m-list {
    padding-left: 0px;
    padding-right: 0px;
}

.m-panel.white {
    background-color: #fff;
    box-shadow: 0 1px 4px 0 rgba(0, 0, 0, 0);
    transition: all 0.3s;
    -moz-transition: all 0.3s;
    -webkit-transition: all 0.3s;
    -o-transition: all 0.3s;
}

.m-panel.white:hover {
    box-shadow: 0 7px 13px 0 rgba(0, 0, 0, 0.25);
}

.m-panel.white .panel-head,
.m-panel.white .panel-body {
    background-color: transparent;
    border: none;
}

.m-panel .u-btn {
    background-color: #ffffff;
    color: #333;
    border-color: #d1d1d1;
    color: #737373 !important;
}

.m-panel .u-btn a {
    color: #808080;
}

.m-panel .u-btn a:hover {
    color: #999999;
}

.m-panel .u-btn:hover {
    background-color: #ffffff;
    color: #333;
    border-color: #d1d1d1;
    box-shadow: 0 0 8px #ffffff;
}

.m-panel .u-btn:hover a {
    color: #808080;
}

.m-panel .u-btn:hover a:hover {
    color: #999999;
}

.m-panel .u-btn:active,
.m-panel .u-btn.active {
    box-shadow: 0 3px 3px #d9d9d9 inset;
}

.m-panel .u-btn:hover {
    color: #595959;
}

.m-panel .u-btn>.u-point {
    background-color: #ffffff;
    color: #fff;
}

.m-panel .panel-head .icon {
    background-color: var(--theme-primary);
}

.m-panel .panel-head {
    border-bottom-color: #ddd;
    background-color: #f3f3f3;
}

.m-panel .panel-head>.title {
    font-size: 16px;
}

.m-panel .panel-head .more {
    background-color: var(--theme-primary);
    color: #fef8f8;
    border-color: var(--theme-primary);
    color: #fff!important;
}

.m-panel .panel-head .more a {
    color: #fef8f8;
}

.m-panel .panel-head .more a:hover {
    color: #ffffff;
}

.m-panel .panel-head .more:hover {
    background-color: #eb594f;
    color: #ffffff;
    border-color: #e73b2f;
    box-shadow: 0 0 8px var(--theme-primary);
}

.m-panel .panel-head .more:hover a {
    color: #ffffff;
}

.m-panel .panel-head .more:hover a:hover {
    color: #ffffff;
}

.m-panel .panel-head .more:active,
.m-panel .panel-head .more.active {
    box-shadow: 0 3px 3px #c72217 inset;
}

.m-panel .panel-head .more:hover {
    color: #ffffff;
}

.m-panel .panel-head .more>.u-point {
    background-color: #fff;
    color: var(--theme-primary);
}

.m-panel .panel-body {
    background-color: #ffffff;
}

.m-panel.f-b {
    border-color: #ddd;
}

.m-panel.f-b .panel-head {
    border-bottom-color: #ddd;
}

.m-panel.f-b .panel-foot {
    border-bottom-color: #ddd;
}

.m-panel .panel-head {
    min-height: 45px;
}

.m-panel .panel-head>.title,
.m-panel .panel-head>.item {
    height: 44px;
    line-height: 44px;
    padding: 0 13.5px;
}

.m-panel .panel-head .icon {
    vertical-align: middle;
    border-radius: 50%;
    height: 33px;
    width: 33px;
    line-height: 33px;
    padding: 0;
    margin: 6px;
    overflow: hidden;
    text-align: center;
}

.m-panel .panel-head .icon img,
.m-panel .panel-head .icon i {
    font-size: 18px;
    color: #fff;
}

.m-panel .panel-head .more {
    margin-top: 12.5px;
}

.m-panel .panel-head .m-toolbar {
    height: 44px;
}

.m-panel .panel-head .m-toolbar>.title,
.m-panel .panel-head .m-toolbar>.item {
    height: 32px;
    line-height: 32px;
}

.m-panel .panel-head .m-toolbar>.title.u-btn,
.m-panel .panel-head .m-toolbar>.item.u-btn {
    line-height: 30px;
    min-width: 32px;
}

.m-panel .panel-head .m-toolbar>.title>.u-btn,
.m-panel .panel-head .m-toolbar>.item>.u-btn,
.m-panel .panel-head .m-toolbar>.title>.u-input,
.m-panel .panel-head .m-toolbar>.item>.u-input,
.m-panel .panel-head .m-toolbar>.title>.u-select,
.m-panel .panel-head .m-toolbar>.item>.u-select {
    line-height: 30px;
    height: 32px;
    min-width: 32px;
}

.m-panel .panel-head .m-toolbar>.title.u-group .item,
.m-panel .panel-head .m-toolbar>.item.u-group .item,
.m-panel .panel-head .m-toolbar>.title .u-group .item,
.m-panel .panel-head .m-toolbar>.item .u-group .item,
.m-panel .panel-head .m-toolbar>.title.u-group .m-tooltip,
.m-panel .panel-head .m-toolbar>.item.u-group .m-tooltip,
.m-panel .panel-head .m-toolbar>.title .u-group .m-tooltip,
.m-panel .panel-head .m-toolbar>.item .u-group .m-tooltip,
.m-panel .panel-head .m-toolbar>.title.u-group textarea.u-input,
.m-panel .panel-head .m-toolbar>.item.u-group textarea.u-input,
.m-panel .panel-head .m-toolbar>.title .u-group textarea.u-input,
.m-panel .panel-head .m-toolbar>.item .u-group textarea.u-input,
.m-panel .panel-head .m-toolbar>.title.u-group textarea.u-textarea,
.m-panel .panel-head .m-toolbar>.item.u-group textarea.u-textarea,
.m-panel .panel-head .m-toolbar>.title .u-group textarea.u-textarea,
.m-panel .panel-head .m-toolbar>.item .u-group textarea.u-textarea,
.m-panel .panel-head .m-toolbar>.title.u-group select.u-input,
.m-panel .panel-head .m-toolbar>.item.u-group select.u-input,
.m-panel .panel-head .m-toolbar>.title .u-group select.u-input,
.m-panel .panel-head .m-toolbar>.item .u-group select.u-input {
    height: 32px;
    line-height: 30px;
}

.m-panel.fit .panel-body {
    padding: 45px 0 0 0;
}

.m-panel-mask {
    display: none;
    position: fixed;
    overflow: hidden;
    left: 0px;
    top: 0px;
    *position: absolute;
    /* *top: expression(eval(document.documentElement.scrollTop)); */
}

.m-panel-maskshow {
    display: block;
    filter: alpha(opacity=40) !important;
    opacity: 0.6 !important;
    background: #000;
}

.m-tabs-header,
.tabs-head {
    width: 100%;
    border-bottom-style: solid;
    border-bottom-width: 1px;
    padding: 8px 8px 0 8px;
    border-bottom-color: #ddd;
    height: 45px;
}

.m-tabs-header .title,
.tabs-head .title {
    display: inline-block;
    float: left;
    padding: 0 20px 0 10px;
    font-size: 16px;
}

.m-tabs-header .m-tabs-nav,
.tabs-head .m-tabs-nav,
.m-tabs-header .tabs-nav,
.tabs-head .tabs-nav {
    width: 100%;
}

.m-tabs-header .m-tabs-nav li,
.tabs-head .m-tabs-nav li,
.m-tabs-header .tabs-nav li,
.tabs-head .tabs-nav li {
    display: block;
    position: relative;
    float: left;
    margin-right: 2px;
}

.m-tabs-header .m-tabs-nav li a,
.tabs-head .m-tabs-nav li a,
.m-tabs-header .tabs-nav li a,
.tabs-head .tabs-nav li a {
    display: block;
    float: left;
    padding: 0 17px;
    border-style: solid;
    border-width: 1px;
    border-color: transparent;
    margin-top: 1px;
    position: relative;
}

.m-tabs-header .m-tabs-nav li.activate a,
.tabs-head .m-tabs-nav li.activate a,
.m-tabs-header .tabs-nav li.activate a,
.tabs-head .tabs-nav li.activate a,
.m-tabs-header .m-tabs-nav li.active a,
.tabs-head .m-tabs-nav li.active a,
.m-tabs-header .tabs-nav li.active a,
.tabs-head .tabs-nav li.active a {
    font-weight: bold;
}

.m-tabs-header .m-tabs-nav li .close,
.tabs-head .m-tabs-nav li .close,
.m-tabs-header .tabs-nav li .close,
.tabs-head .tabs-nav li .close {
    position: absolute;
    right: 5px;
    top: 50%;
    z-index: 100;
    font-size: 10px!important;
    width: 10px;
    height: 10px;
    margin-top: -5px;
    line-height: 10px!important;
    display: none;
    cursor: pointer;
}

.m-tabs-header .m-tabs-nav li:hover .close,
.tabs-head .m-tabs-nav li:hover .close,
.m-tabs-header .tabs-nav li:hover .close,
.tabs-head .tabs-nav li:hover .close {
    display: block;
}

.m-tabs-header .tabs-bar,
.tabs-head .tabs-bar {
    height: 30px;
    position: absolute;
    padding: 0px;
    top: 6px;
    right: 6px;
    z-index: 800;
}

.m-tabs-header .tabs-bar i,
.tabs-head .tabs-bar i {
    font-size: 13px;
}

.m-tabs-header .tabs-bar .u-group>.item,
.tabs-head .tabs-bar .u-group>.item {
    border-color: var(--theme-primary);
}

.m-tabs-header .tabs-bar .u-group>.item.u-btn,
.tabs-head .tabs-bar .u-group>.item.u-btn {
    background-color: var(--theme-primary);
    color: #fef8f8;
    border-color: #e52e21;
    color: #fff!important;
}

.m-tabs-header .tabs-bar .u-group>.item.u-btn a,
.tabs-head .tabs-bar .u-group>.item.u-btn a {
    color: #fef8f8;
}

.m-tabs-header .tabs-bar .u-group>.item.u-btn a:hover,
.tabs-head .tabs-bar .u-group>.item.u-btn a:hover {
    color: #ffffff;
}

.m-tabs-header .tabs-bar .u-group>.item.u-btn:hover,
.tabs-head .tabs-bar .u-group>.item.u-btn:hover {
    background-color: #eb594f;
    color: #ffffff;
    border-color: #e73b2f;
    box-shadow: 0 0 8px var(--theme-primary);
}

.m-tabs-header .tabs-bar .u-group>.item.u-btn:hover a,
.tabs-head .tabs-bar .u-group>.item.u-btn:hover a {
    color: #ffffff;
}

.m-tabs-header .tabs-bar .u-group>.item.u-btn:hover a:hover,
.tabs-head .tabs-bar .u-group>.item.u-btn:hover a:hover {
    color: #ffffff;
}

.m-tabs-header .tabs-bar .u-group>.item.u-btn:active,
.tabs-head .tabs-bar .u-group>.item.u-btn:active,
.m-tabs-header .tabs-bar .u-group>.item.u-btn.active,
.tabs-head .tabs-bar .u-group>.item.u-btn.active {
    box-shadow: 0 3px 3px #c72217 inset;
}

.m-tabs-header .tabs-bar .u-group>.item.u-btn:hover,
.tabs-head .tabs-bar .u-group>.item.u-btn:hover {
    color: #ffffff;
}

.m-tabs-header .tabs-bar .u-group>.item.u-btn>.u-point,
.tabs-head .tabs-bar .u-group>.item.u-btn>.u-point {
    background-color: #fff;
    color: var(--theme-primary);
}

.m-tabs-header .m-tabs-nav li>a,
.tabs-head .m-tabs-nav li>a,
.m-tabs-header .tabs-nav li>a,
.tabs-head .tabs-nav li>a {
    color: #a6a6a6 !important;
}

.m-tabs-header .m-tabs-nav li>a:hover,
.tabs-head .m-tabs-nav li>a:hover,
.m-tabs-header .tabs-nav li>a:hover,
.tabs-head .tabs-nav li>a:hover {
    background-color: rgba(255, 255, 255, 0.5);
    color: var(--theme-primary) !important;
}

.m-tabs-header .m-tabs-nav li>a span,
.tabs-head .m-tabs-nav li>a span,
.m-tabs-header .tabs-nav li>a span,
.tabs-head .tabs-nav li>a span {
    background-color: var(--theme-primary);
    color: #fff;
}

.m-tabs-header .m-tabs-nav li.activate>a,
.tabs-head .m-tabs-nav li.activate>a,
.m-tabs-header .tabs-nav li.activate>a,
.tabs-head .tabs-nav li.activate>a,
.m-tabs-header .m-tabs-nav li.active>a,
.tabs-head .m-tabs-nav li.active>a,
.m-tabs-header .tabs-nav li.active>a,
.tabs-head .tabs-nav li.active>a {
    border-color: #ddd;
    background-color: #ffffff;
    border-bottom-color: #ffffff;
    color: var(--theme-primary) !important;
}

.m-tabs-header .title,
.tabs-head .title {
    height: 36px;
    line-height: 30px;
}

.m-tabs-header .m-tabs-nav,
.tabs-head .m-tabs-nav,
.m-tabs-header .tabs-nav,
.tabs-head .tabs-nav {
    height: 36px;
}

.m-tabs-header .m-tabs-nav li,
.tabs-head .m-tabs-nav li,
.m-tabs-header .tabs-nav li,
.tabs-head .tabs-nav li {
    height: 36px;
}

.m-tabs-header .m-tabs-nav li a,
.tabs-head .m-tabs-nav li a,
.m-tabs-header .tabs-nav li a,
.tabs-head .tabs-nav li a {
    height: 36px;
    line-height: 34px;
}

.m-tabs-header .m-tabs-nav li a span,
.tabs-head .m-tabs-nav li a span,
.m-tabs-header .tabs-nav li a span,
.tabs-head .tabs-nav li a span {
    width: 16px;
    height: 16px;
    line-height: 16px;
    font-size: 12px;
    position: absolute;
    text-align: center;
    font-weight: normal;
    top: 0;
    right: 0;
    border-radius: 50%;
}

.m-tabs-header.center .m-tabs-nav,
.tabs-head.center .m-tabs-nav,
.m-tabs-header.center .tabs-nav,
.tabs-head.center .tabs-nav {
    text-align: center;
}

.m-tabs-header.center .m-tabs-nav li,
.tabs-head.center .m-tabs-nav li,
.m-tabs-header.center .tabs-nav li,
.tabs-head.center .tabs-nav li {
    display: inline-block;
    float: none;
}

.m-tabs-header.btn,
.tabs-head.btn {
    padding: 5px;
}

.m-tabs-header.btn .m-tabs-nav,
.tabs-head.btn .m-tabs-nav,
.m-tabs-header.btn .tabs-nav,
.tabs-head.btn .tabs-nav {
    height: 34px;
}

.m-tabs-header.btn .m-tabs-nav li,
.tabs-head.btn .m-tabs-nav li,
.m-tabs-header.btn .tabs-nav li,
.tabs-head.btn .tabs-nav li {
    margin-right: 8px;
    height: 34px;
}

.m-tabs-header.btn .m-tabs-nav li>a,
.tabs-head.btn .m-tabs-nav li>a,
.m-tabs-header.btn .tabs-nav li>a,
.tabs-head.btn .tabs-nav li>a {
    margin-top: 0px;
    height: 34px;
    border-radius: 17px;
    line-height: 34px;
    border: none;
}


/*首页页签颜色*/

.m-tabs-header.btn .m-tabs-nav li>a span,
.tabs-head.btn .m-tabs-nav li>a span,
.m-tabs-header.btn .tabs-nav li>a span,
.tabs-head.btn .tabs-nav li>a span {
    width: 6px;
    height: 6px;
    line-height: 6px;
    font-size: 1px;
    position: absolute;
    top: 6px;
    right: 9px;
    border-radius: 50%;
    background-color: var(--theme-primary);
}

.m-tabs-header.btn .m-tabs-nav li>a:hover,
.tabs-head.btn .m-tabs-nav li>a:hover,
.m-tabs-header.btn .tabs-nav li>a:hover,
.tabs-head.btn .tabs-nav li>a:hover {
    color: var(--theme-primary) !important;
}

.m-tabs-header.btn .m-tabs-nav li.activate>a,
.tabs-head.btn .m-tabs-nav li.activate>a,
.m-tabs-header.btn .tabs-nav li.activate>a,
.tabs-head.btn .tabs-nav li.activate>a,
.m-tabs-header.btn .m-tabs-nav li.active>a,
.tabs-head.btn .m-tabs-nav li.active>a,
.m-tabs-header.btn .tabs-nav li.active>a,
.tabs-head.btn .tabs-nav li.active>a {
    background-color: var(--theme-primary);
    color: #fff!important;
    font-size: 16px;
}

.m-tabs-header.btn .m-tabs-nav li.activate>a span,
.tabs-head.btn .m-tabs-nav li.activate>a span,
.m-tabs-header.btn .tabs-nav li.activate>a span,
.tabs-head.btn .tabs-nav li.activate>a span,
.m-tabs-header.btn .m-tabs-nav li.active>a span,
.tabs-head.btn .m-tabs-nav li.active>a span,
.m-tabs-header.btn .tabs-nav li.active>a span,
.tabs-head.btn .tabs-nav li.active>a span {
    background-color: #fff;
}

.m-tabs-header.line,
.tabs-head.line {
    padding: 0 8px;
    border-bottom-color: transparent;
}

.m-tabs-header.line .m-tabs-nav,
.tabs-head.line .m-tabs-nav,
.m-tabs-header.line .tabs-nav,
.tabs-head.line .tabs-nav {
    height: 44px;
}

.m-tabs-header.line .m-tabs-nav li,
.tabs-head.line .m-tabs-nav li,
.m-tabs-header.line .tabs-nav li,
.tabs-head.line .tabs-nav li {
    height: 44px;
}

.m-tabs-header.line .m-tabs-nav li a,
.tabs-head.line .m-tabs-nav li a,
.m-tabs-header.line .tabs-nav li a,
.tabs-head.line .tabs-nav li a {
    margin-top: 0;
    height: 44px;
    line-height: 42px;
    border-bottom-style: solid;
    border-bottom-width: 3px;
}

.m-tabs-header.line .m-tabs-nav li a:hover,
.tabs-head.line .m-tabs-nav li a:hover,
.m-tabs-header.line .tabs-nav li a:hover,
.tabs-head.line .tabs-nav li a:hover {
    background-color: transparent;
    color: var(--theme-primary) !important;
}

.m-tabs-header.line .m-tabs-nav li.activate a,
.tabs-head.line .m-tabs-nav li.activate a,
.m-tabs-header.line .tabs-nav li.activate a,
.tabs-head.line .tabs-nav li.activate a,
.m-tabs-header.line .m-tabs-nav li.active a,
.tabs-head.line .m-tabs-nav li.active a,
.m-tabs-header.line .tabs-nav li.active a,
.tabs-head.line .tabs-nav li.active a {
    border-color: transparent;
    background-color: transparent;
    border-bottom-color: var(--theme-primary);
    color: var(--theme-primary) !important;
    font-size: 16px;
    font-weight: bold;
}

.m-tabs-header.overflow,
.tabs-head.overflow {
    position: relative;
}

.m-tabs-header.overflow .right-bar,
.tabs-head.overflow .right-bar,
.m-tabs-header.overflow .left-bar,
.tabs-head.overflow .left-bar {
    width: 16px;
    font-size: 12px;
    position: absolute;
    top: 0;
    text-align: center;
    border-radius: 3px;
    cursor: pointer;
    z-index: 500;
}

.m-tabs-header.overflow .right-bar,
.tabs-head.overflow .right-bar {
    right: 0;
}

.m-tabs-header.overflow .left-bar,
.tabs-head.overflow .left-bar {
    left: 0;
}

.m-tabs-header.overflow .m-tabs-nav,
.tabs-head.overflow .m-tabs-nav {
    width: auto;
    padding: 1px 19px 0 19px;
    position: absolute;
}

.m-tabs {
    position: relative;
    overflow: hidden;
    width: 100%;
    height: 100%;
    padding-top: 45px;
}

.m-tabs .m-tabs-header,
.m-tabs .tabs-head {
    position: absolute;
    z-index: 600;
    left: 0;
    top: 0;
}

.m-tabs .m-tabs-content,
.m-tabs .tabs-content {
    position: relative;
    left: 0px;
    bottom: 0px;
    border-width: 1px;
    border-style: solid;
    border-color: transparent;
    border-top: none;
    *position: relative;
    width: 100%;
    height: 100%;
}

.m-tabs .m-tabs-content>.item,
.m-tabs .tabs-content>.item {
    position: absolute;
    left: 0px;
    top: 0px;
    z-index: 200;
    overflow-y: auto;
    overflow-x: hidden;
    background-color: #ffffff;
    width: 100%;
    height: 100%;
}

.m-tabs .m-tabs-content>.item.activate,
.m-tabs .tabs-content>.item.activate,
.m-tabs .m-tabs-content>.item.active,
.m-tabs .tabs-content>.item.active {
    z-index: 400;
}

.m-tabs .m-tabs-content>.item .item,
.m-tabs .tabs-content>.item .item {
    position: inherit;
    overflow: inherit;
}

.m-tabs.f-b {
    border: #ddd 1px solid;
}

.m-tabs.f-b .m-tabs-header,
.m-tabs.f-b .tabs-header {
    border-top: 1px solid transparent;
    border-left: 1px solid transparent;
    border-right: 1px solid transparent;
    border-bottom-width: 1px;
}

.m-tabs.f-b .m-tabs-header.btn,
.m-tabs.f-b .tabs-header.btn,
.m-tabs.f-b .m-tabs-header.line,
.m-tabs.f-b .tabs-header.line {
    border-bottom-width: 1px;
}

.m-tabs.f-b .m-tabs-content,
.m-tabs.f-b .tabs-content {
    border: none;
}

.m-tabs.f-r {
    border-radius: 5px;
}

.m-tabs.f-r .m-tabs-header,
.m-tabs.f-r .m-tabs-nav {
    border-radius: 5px 5px 0 0;
}

.m-table {
    margin: 0;
    line-height: 18px;
    border-style: solid;
    border-width: 1px;
    border-spacing: 0px;
    border-collapse: collapse;
    table-layout: fixed;
    word-break: break-all;
    background-color: #fff;
    width: 100%;
}

.m-table table {
    width: 100%;
    table-layout: fixed;
}

.m-table th,
.m-table td {
    border-style: solid;
    border-width: 1px;
}

.m-table th p,
.m-table td p,
.m-table th .u-input-span,
.m-table td .u-input-span {
    padding: 0 8px 0px 8px;
}

.m-table th {
    font-weight: bold;
}

.m-table thead th:last-child {
    width: auto;
}

.m-table thead th .m-icon {
    display: inline;
}

.m-table tbody tr:hover td,
.m-table tbody tr.current:hover td,
.m-table tbody tr.current:hover .lockColumnBg td {
    cursor: pointer;
}

.m-table tbody tr.current,
.m-table tbody tr.current .lockColumnBg,
.m-table tbody tr.current:hover td {
    cursor: pointer;
}

.m-table tbody tr:hover {
    transition: all 0.4s, color 0.4s, all 0.4s;
    -moz-transition: all 0.4s, color 0.4s, all 0.4s;
    -webkit-transition: all 0.4s, color 0.4s, all 0.4s;
    -o-transition: all 0.4s, color 0.4s, all 0.4s;
}

.m-table tbody.complex {
    border-top-width: 13px;
    border-top-style: solid;
    border-bottom-width: 2px;
    border-bottom-style: solid;
}

.m-table .m-table-form td {
    border-style: solid;
    border-width: 1px;
    padding: 0px!important;
}

.m-table .m-table-form td .u-formitem {
    padding: 5px;
}

.m-table.bootstrap,
.m-table.style01 {
    border-top: none;
    border-left: none;
    border-right: none;
    border-bottom: none;
}

.m-table.bootstrap tbody td,
.m-table.style01 tbody td,
.m-table.bootstrap thead th,
.m-table.style01 thead th {
    border-left: none;
    border-right: none;
}

.m-table.bootstrap thead th,
.m-table.style01 thead th {
    border-top: none;
}

.m-table .table-text,
.m-table .text {
    display: block;
    float: left;
    width: 100%;
    margin: 0px;
    padding: 0px;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
}

.m-table.m-treegrid tr td.node {
    background-image: url("../img/node.png");
}

.m-table.m-treegrid tr td.group {
    background-image: url("../img/group.png");
}

.m-table.m-treegrid tr td.group.open {
    background-image: url("../img/group-open.png");
}

.m-table.m-treegrid tr td.node {
    background-repeat: no-repeat;
}

.m-table.m-treegrid tr td.group {
    background-repeat: no-repeat;
    cursor: pointer;
}

.m-table.m-treegrid tr td.group.open {
    background-repeat: no-repeat;
    cursor: pointer;
}

.m-table.m-treegrid tr td.n1 {
    padding-left: 44px;
    background-position: 8px 50%;
}

.m-table.m-treegrid tr td.n2 {
    padding-left: 61px;
    background-position: 25px 50%;
}

.m-table.m-treegrid tr td.n3 {
    padding-left: 81px;
    background-position: 45px 50%;
}

.m-table.m-treegrid tr td.n4 {
    padding-left: 103px;
    background-position: 66px 50%;
}

.m-table.m-treegrid tr td.n5 {
    padding-left: 124px;
    background-position: 87px 50%;
}

.m-table.m-treegrid tr td.n6 {
    padding-left: 146px;
    background-position: 109px 50%;
}

.m-table.m-treegrid tr td.n7 {
    padding-left: 164px;
    background-position: 128px 50%;
}

.m-table.m-treegrid tr td.n8 {
    padding-left: 184px;
    background-position: 147px 50%;
}

.m-table.m-treegrid tr td.n9 {
    padding-left: 206px;
    background-position: 168px 50%;
}

.m-table.m-treegrid tr td.n10 {
    padding-left: 229px;
    background-position: 190px 50%;
}

.m-table thead tr>th {
    height: 40px;
    padding: 5px 8px;
}

.m-table tbody tr>td {
    height: 40px;
    padding: 3px 8px;
}

.m-table tbody tr>td .u-btn {
    height: 34px;
    line-height: 32px;
}

.m-table.mini thead tr>th,
.m-table.min thead tr>th {
    height: 20px;
    padding: 2px 4px;
}

.m-table.mini tbody tr>td,
.m-table.min tbody tr>td {
    height: 20px;
    padding: 1px 4px;
}

.m-table.mini tbody tr>td .u-btn,
.m-table.min tbody tr>td .u-btn {
    height: 18px;
    line-height: 16px;
}

.m-table.sm thead tr>th {
    height: 28px;
    padding: 3px 6px;
}

.m-table.sm tbody tr>td {
    height: 28px;
    padding: 2px 5px;
}

.m-table.sm tbody tr>td .u-btn {
    height: 24px;
    line-height: 22px;
}

.m-table td,
.m-table th {
    border-color: #ddd;
}

.m-table thead tr th {
    background-color: #f3f3f3;
}

.m-table tbody tr .u-btn {
    background-color: #f5f5f5;
    background: -moz-linear-gradient(top, #ffffff 0%, #f5f5f5 100%);
    background: -webkit-linear-gradient(top, #ffffff 0%, #f5f5f5 100%);
    background: linear-gradient(to bottom, #ffffff 0%, #f5f5f5 100%);
    filter: progid: DXImageTransform.Microsoft.gradient(startColorstr=#ffffff, endColorstr=#f5f5f5, GradientType=0);
    color: #8f8f8f;
    border-color: #e9e9e9;
    background-repeat: no-repeat;
    color: #696969 !important;
}

.m-table tbody tr .u-btn a {
    color: #767676;
}

.m-table tbody tr .u-btn a:hover {
    color: #8f8f8f;
}

.m-table tbody tr .u-btn:hover {
    background-color: #fdfdfd;
    background: -moz-linear-gradient(top, #ffffff 0%, #fdfdfd 100%);
    background: -webkit-linear-gradient(top, #ffffff 0%, #fdfdfd 100%);
    background: linear-gradient(to bottom, #ffffff 0%, #fdfdfd 100%);
    filter: progid: DXImageTransform.Microsoft.gradient(startColorstr=#ffffff, endColorstr=#fdfdfd, GradientType=0);
    color: #979797;
    border-color: #f0f0f0;
    background-repeat: no-repeat;
    box-shadow: none;
}

.m-table tbody tr .u-btn:hover a {
    color: #7e7e7e;
}

.m-table tbody tr .u-btn:hover a:hover {
    color: #979797;
}

.m-table tbody tr .u-btn:active,
.m-table tbody tr .u-btn.active {
    box-shadow: 0 3px 3px #cfcfcf inset;
}

.m-table tbody tr .u-btn:hover {
    color: #505050;
}

.m-table tbody tr:nth-child(2n) {
    background-color: #f5f5f5;
}


/*表格一行hover背景及单元格内按钮*/

.m-table tbody tr:hover {
    background-color: rgba(167, 122, 62, 0.1);
    color: #333;
}

.m-table tbody tr:hover a {
    color: #666;
}

.m-table tbody tr:hover a:hover {
    color: #666;
}

.m-table tbody tr:hover .u-btn {
    background-color: #fdf0ef;
    background: -moz-linear-gradient(top, #ffffff 0%, #fdf0ef 100%);
    background: -webkit-linear-gradient(top, #ffffff 0%, #fdf0ef 100%);
    background: linear-gradient(to bottom, #ffffff 0%, #fdf0ef 100%);
    filter: progid: DXImageTransform.Microsoft.gradient(startColorstr=#ffffff, endColorstr=#fdf0ef, GradientType=0);
    color: #e84338;
    background-repeat: no-repeat;
    color: #666 !important;
}

.m-table tbody tr:hover .u-btn a {
    color: #333;
}

.m-table tbody tr:hover .u-btn a:hover {
    color: #333;
}

.m-table tbody tr:hover .u-btn:hover {
    background-color: #fffdfc;
    background: -moz-linear-gradient(top, #ffffff 0%, #fffdfc 100%);
    background: -webkit-linear-gradient(top, #ffffff 0%, #fffdfc 100%);
    background: linear-gradient(to bottom, #ffffff 0%, #fffdfc 100%);
    filter: progid: DXImageTransform.Microsoft.gradient(startColorstr=#ffffff, endColorstr=#fffdfc, GradientType=0);
    color: #ea5046;
    border-color: #fce7e6;
    background-repeat: no-repeat;
    box-shadow: none;
}

.m-table tbody tr:hover .u-btn:hover a {
    color: #e2271a;
}

.m-table tbody tr:hover .u-btn:hover a:hover {
    color: #ea5046;
}

.m-table tbody tr:hover .u-btn:active,
.m-table tbody tr:hover .u-btn.active {
    box-shadow: 0 3px 3px #f5afaa inset;
}

.m-table tbody tr:hover .u-btn:hover {
    color: #901911;
}

.m-table tbody tr:active {
    background-color: #fff1db;
    color: #ff9f0f;
    border-color: #ffd28f;
}

.m-table tbody tr:active a {
    color: #db8400;
}

.m-table tbody tr:active a:hover {
    color: #ff9f0f;
}

.m-table tbody tr:active .u-btn {
    background-color: #fff1db;
    background: -moz-linear-gradient(top, #ffffff 0%, #fff1db 100%);
    background: -webkit-linear-gradient(top, #ffffff 0%, #fff1db 100%);
    background: linear-gradient(to bottom, #ffffff 0%, #fff1db 100%);
    filter: progid: DXImageTransform.Microsoft.gradient(startColorstr=#ffffff, endColorstr=#fff1db, GradientType=0);
    color: #ff9f0f;
    border-color: #ffe7c2;
    background-repeat: no-repeat;
    color: #c27400 !important;
}

.m-table tbody tr:active .u-btn a {
    color: #db8400;
}

.m-table tbody tr:active .u-btn a:hover {
    color: #ff9f0f;
}

.m-table tbody tr:active .u-btn:hover {
    background-color: #fff7eb;
    background: -moz-linear-gradient(top, #ffffff 0%, #fff7eb 100%);
    background: -webkit-linear-gradient(top, #ffffff 0%, #fff7eb 100%);
    background: linear-gradient(to bottom, #ffffff 0%, #fff7eb 100%);
    filter: progid: DXImageTransform.Microsoft.gradient(startColorstr=#ffffff, endColorstr=#fff7eb, GradientType=0);
    color: #ffa51f;
    border-color: #ffedd1;
    background-repeat: no-repeat;
    box-shadow: none;
}

.m-table tbody tr:active .u-btn:hover a {
    color: #eb8d00;
}

.m-table tbody tr:active .u-btn:hover a:hover {
    color: #ffa51f;
}

.m-table tbody tr:active .u-btn:active,
.m-table tbody tr:active .u-btn.active {
    box-shadow: 0 3px 3px #ffd28f inset;
}

.m-table tbody tr:active .u-btn:hover {
    color: #8f5600;
}

.m-table td,
.m-table th {
    color: #333;
}

.Scrollbar {
    height: 100%!important;
}

.ui-jqgrid tr.jqgrow,
.ui-jqgrid tr.ui-row-ltr,
.ui-jqgrid tr.ui-row-rtl {
    color: #333;
}

.ui-jqgrid-bdiv {
    border-left-width: 1px;
    border-left-style: solid;
    border-right-width: 1px;
    border-right-style: solid;
    background-color: #ffffff;
}

.ui-jqgrid .ui-jqgrid-labels,
.ui-jqgrid .ui-jqgrid-hdiv,
.ui-jqgrid .ui-jqgrid-sortable,
.ui-jqgrid .ui-jqgrid-pager {
    background-color: #f3f3f3 !important;
    color: #404040 !important;
    border-color: #c5c5c5 !important;
    border-color: #ddd !important;
}

.ui-jqgrid .ui-jqgrid-labels a,
.ui-jqgrid .ui-jqgrid-hdiv a,
.ui-jqgrid .ui-jqgrid-sortable a,
.ui-jqgrid .ui-jqgrid-pager a {
    color: #737373 !important;
}

.ui-jqgrid .ui-jqgrid-labels a:hover,
.ui-jqgrid .ui-jqgrid-hdiv a:hover,
.ui-jqgrid .ui-jqgrid-sortable a:hover,
.ui-jqgrid .ui-jqgrid-pager a:hover {
    color: #8d8d8d !important;
}

.ui-jqgrid .ui-jqgrid-pager {
    border-left: 1px solid #e4e4e4 !important;
    border-right: 1px solid #e4e4e4 !important;
    border-bottom: 1px solid #e4e4e4 !important;
    height: 40px!important;
    box-sizing: border-box;
}

.ui-jqgrid .ui-jqgrid-hdiv {
    border-left: 1px solid #e4e4e4 !important;
    border-right: 1px solid #e4e4e4 !important;
    box-sizing: border-box;
}

.ui-jqgrid-btable .ui-widget-content.ui-priority-secondary {
    background-color: #f5f5f5 !important;
}

.ui-jqgrid-btable .ui-widget-content.ui-priority-secondary:hover {
    background-color: #fdf0ef !important;
}

.ui-jqgrid-btable .ui-widget-content.ui-priority-secondary:hover td {
    color: #eb594f !important;
}


/*首页表格高亮修改*/

.ui-widget-content.jqgrow.ui-row-ltr:hover {
    background-color: #F7F7F7 !important;
}

.ui-widget-content.jqgrow.ui-row-ltr:hover td {
    color: #666!important;
}

.ui-widget-content.jqgrow.ui-row-ltr.ui-state-highlight,
.ui-widget-content.jqgrow.ui-row-ltr.ui-priority-secondary.ui-state-highlight {
    background-color: #F7F7F7 !important;
}

.ui-widget-content.jqgrow.ui-row-ltr.ui-state-highlight td,
.ui-widget-content.jqgrow.ui-row-ltr.ui-priority-secondary.ui-state-highlight td {
    color: #666 !important;
}

.ui-widget-content.jqgrow.ui-row-ltr.ui-state-highlight:hover,
.ui-widget-content.jqgrow.ui-row-ltr.ui-priority-secondary.ui-state-highlight:hover {
    background-color: #F7F7F7 !important;
}


/*首页表格单元格边框*/

.ui-jqgrid .ui-jqgrid-labels th,
.ui-jqgrid tr.ui-row-ltr td,
.ui-jqgrid tr.ui-row-rtl td,
.ui-jqgrid .ui-jqgrid-bdiv {
    border-color: #eee !important;
    overflow-y: visible!important;
}

.ui-jqgrid tr.ui-row-ltr td:last-child {
    overflow: visible!important;
}

.ui-jqgrid .ui-jqgrid-htable th div {
    line-height: 16px!important;
}

.ui-jqgrid tr.ui-row-ltr td {
    border-right-width: 0px!important;
}

.ui-jqgrid .ui-jqgrid-sortable {
    font-weight: normal;
}

.f-visible {
    overflow: visible;
}

.f-visible-i {
    overflow: visible !important;
}

.f-y-visible {
    overflow-y: visible;
}

.f-y-visible-i {
    overflow: visible !important;
}

.ui-jqgrid tr.jqgrow td {
    height: 38px!important;
}

.f-visible-i td a,
.ui-jqgrid-btable td a {
    color: var(--theme-primary);
    text-decoration: underline;
}

.f-visible-i td a:hover,
.ui-jqgrid-btable td a:hover {
    color: #0054FF;
}

.f-visible-i .u-btn,
.ui-jqgrid-btable .u-btn,
.f-visible-i .u-btn.sm,
.ui-jqgrid-btable .u-btn.sm,
.f-visible-i .u-btn.xs,
.ui-jqgrid-btable .u-btn.xs {
    height: 22px;
    line-height: 20px;
    padding: 0 6px;
    margin-right: 3px;
    font-size: 12px!important;
    text-decoration: none;
}

.f-visible-i .u-btn .iconfont,
.ui-jqgrid-btable .u-btn .iconfont,
.f-visible-i .u-btn.sm .iconfont,
.ui-jqgrid-btable .u-btn.sm .iconfont,
.f-visible-i .u-btn.xs .iconfont,
.ui-jqgrid-btable .u-btn.xs .iconfont {
    font-size: 14px!important;
    top: 1px!important;
    margin-right: 3px;
}

.f-visible-i .u-btn:hover,
.ui-jqgrid-btable .u-btn:hover,
.f-visible-i .u-btn.sm:hover,
.ui-jqgrid-btable .u-btn.sm:hover,
.f-visible-i .u-btn.xs:hover,
.ui-jqgrid-btable .u-btn.xs:hover {
    box-shadow: 2px 2px 3px #e1e1e1;
    color: #6c6c6c;
}

.f-visible-i .u-btn:active,
.ui-jqgrid-btable .u-btn:active,
.f-visible-i .u-btn.sm:active,
.ui-jqgrid-btable .u-btn.sm:active,
.f-visible-i .u-btn.xs:active,
.ui-jqgrid-btable .u-btn.xs:active {
    box-shadow: 0 2px 3px #e1e1e1 inset;
}

.f-visible-i .u-group,
.ui-jqgrid-btable .u-group {
    float: none;
    display: inline-block;
    min-width: 22px;
}

.f-visible-i .u-group .item,
.ui-jqgrid-btable .u-group .item {
    margin-right: 0;
}

.f-visible-i .u-group .item.btn:hover,
.ui-jqgrid-btable .u-group .item.btn:hover {
    box-shadow: none;
}

.f-visible-i .m-combo,
.ui-jqgrid-btable .m-combo {
    min-width: 22px;
    float: none;
}

.f-visible-i .m-combo .u-btn,
.ui-jqgrid-btable .m-combo .u-btn {
    margin-right: 0;
}

.f-visible-i .m-combo a,
.ui-jqgrid-btable .m-combo a {
    text-decoration: none;
}

.f-visible-i .u-switch,
.ui-jqgrid-btable .u-switch {
    float: inherit !important;
}

.f-visible-i .u-float,
.ui-jqgrid-btable .u-float {
    box-shadow: -2px 2px 4px rgba(0, 0, 0, 0.05);
    top: 28px!important;
}

.m-table-form {
    width: 100%;
    border-spacing: 0px;
    table-layout: fixed;
    word-break: break-all;
    padding: 0px;
}

.m-table-form td {
    vertical-align: middle;
    z-index: 1;
    overflow: visible;
    height: 30px;
    padding-bottom: 5px;
}

.m-table-form td.table-head {
    text-align: right;
    padding: 7px 10px 10px 0;
    width: 100px;
    color: #333;
    vertical-align: top;
}

.m-table-form td .u-input,
.m-table-form td .m-tooltip,
.m-table-form td .u-select,
.m-table-form td .u-textarea,
.m-table-form td .u-group {
    width: 100%;
}

.m-table-form td .u-input {
    *width: 99%;
}

.m-table-form td>.u-input {
    box-sizing: border-box;
}

.m-table-form td .u-group {
    float: none;
}

.m-table-form td .u-group .item-r {
    margin-left: -1px \9;
}

.m-table-form td .u-group .u-input {
    width: 1px;
}

.m-table-form td p,
.m-table-form td .u-input-span {
    padding: 0 8px 0px 8px;
    line-height: 29px;
    display: block;
    clear: both;
}

.m-table-form td .m-combo {
    display: block;
    width: 100%;
    *position: static;
}

.m-table-form tr td:last-child {
    padding-right: 15px;
}

.m-table-form .text {
    padding-left: 10px;
    padding-right: 10px;
    line-height: 20px;
    margin-top: 5px;
    display: block;
}

.m-table-form .u-checkbox,
.m-table-form .u-label,
.m-table-form .u-switch {
    width: auto;
    margin: 0;
    margin-left: 0;
    z-index: 1;
    display: block;
    float: left;
    border-right: none;
    font-size: 12px;
    padding-right: 0;
    text-align: left;
    padding-top: 0;
}

.m-table-form .u-checkbox.text,
.m-table-form .u-label.text,
.m-table-form .u-switch.text {
    font-size: 14px;
}

.m-table-form .u-switch {
    margin-left: 10px;
}

.m-table-form .ui-airport-swicth {
    height: 22px;
    line-height: 22px;
    margin: 0;
    padding: 0;
}

.m-table-form.document {
    border-collapse: collapse;
    border-spacing: 0;
    border-left: 1px solid #E80C10;
}

.m-table-form.document td {
    border-style: solid;
    border-width: 1px;
    border-color: #E80C10;
    padding-left: 5px;
    padding-right: 5px;
    color: #E80C10!important;
    border-left-width: 0;
}

.m-table-form.document td.table-head {
    border-right-width: 0;
    color: #E80C10!important;
}

.m-table-form.inline,
.m-table-form.table {
    border-collapse: separate;
    *border-collapse: collapse;
    border-spacing: 1px;
    border-color: #ddd;
    background-color: #ddd;
}

.m-table-form.inline td,
.m-table-form.table td,
.m-table-form.inline th,
.m-table-form.table th {
    padding: 0;
}

.m-table-form.inline th,
.m-table-form.table th {
    text-align: right;
    padding-right: 5px;
}

.m-table-form.inline td,
.m-table-form.table td {
    background-color: #fff;
    vertical-align: top;
}

.m-table-form.inline td.table-head,
.m-table-form.table td.table-head {
    padding: 5px 10px 5px 0;
    background-color: #f3f3f3;
    vertical-align: middle;
}

.m-table-form.inline td .u-group button,
.m-table-form.table td .u-group button {
    height: 28px;
}

.m-table-form.inline td table,
.m-table-form.table td table {
    border-collapse: separate;
    *border-collapse: collapse;
    border-spacing: 1px;
}

.m-table-form.inline .u-input,
.m-table-form.table .u-input,
.m-table-form.inline .u-label,
.m-table-form.table .u-label,
.m-table-form.inline .u-checkbox,
.m-table-form.table .u-checkbox,
.m-table-form.inline .checkbox,
.m-table-form.table .checkbox,
.m-table-form.inline .u-select,
.m-table-form.table .u-select,
.m-table-form.inline .u-textarea,
.m-table-form.table .u-textarea,
.m-table-form.inline .u-btn,
.m-table-form.table .u-btn,
.m-table-form.inline .item,
.m-table-form.table .item,
.m-table-form.inline .u-btn-eject,
.m-table-form.table .u-btn-eject,
.m-table-form.inline .u-inputitem,
.m-table-form.table .u-inputitem,
.m-table-form.inline .m-combo,
.m-table-form.table .m-combo,
.m-table-form.inline .u-btn-checkbox,
.m-table-form.table .u-btn-checkbox {
    border-width: 0;
    border-radius: 0;
}

.m-table-form.inline .u-group .item-l,
.m-table-form.table .u-group .item-l {
    border-left-width: 0px!important;
}

.m-table-form.inline .u-group .u-group .u-input,
.m-table-form.table .u-group .u-group .u-input {
    border-right-width: 0px!important;
}

.m-table-form.inline .u-group .u-input,
.m-table-form.table .u-group .u-input,
.m-table-form.inline .u-group .mark,
.m-table-form.table .u-group .mark,
.m-table-form.inline .u-group .item,
.m-table-form.table .u-group .item {
    border-right-width: 1px;
}

.m-table-form.inline .u-group .u-input.item-r,
.m-table-form.table .u-group .u-input.item-r,
.m-table-form.inline .u-group .mark.item-r,
.m-table-form.table .u-group .mark.item-r,
.m-table-form.inline .u-group .item.item-r,
.m-table-form.table .u-group .item.item-r,
.m-table-form.inline .u-group .u-input:last-child,
.m-table-form.table .u-group .u-input:last-child,
.m-table-form.inline .u-group .mark:last-child,
.m-table-form.table .u-group .mark:last-child,
.m-table-form.inline .u-group .item:last-child,
.m-table-form.table .u-group .item:last-child {
    border-right-width: 0px!important;
}

.m-table-form.inline .u-group .u-input:first-child,
.m-table-form.table .u-group .u-input:first-child,
.m-table-form.inline .u-group .mark:first-child,
.m-table-form.table .u-group .mark:first-child,
.m-table-form.inline .u-group .item:first-child,
.m-table-form.table .u-group .item:first-child,
.m-table-form.inline .u-group .u-input.item-l,
.m-table-form.table .u-group .u-input.item-l,
.m-table-form.inline .u-group .mark.item-l,
.m-table-form.table .u-group .mark.item-l,
.m-table-form.inline .u-group .item.item-l,
.m-table-form.table .u-group .item.item-l {
    border-left-width: 0px!important;
}

.m-table-form.inline .u-group .item-r,
.m-table-form.table .u-group .item-r {
    border-right-width: 0px!important;
}

.m-table-form.inline .m-menu,
.m-table-form.table .m-menu,
.m-table-form.inline .u-down-menu,
.m-table-form.table .u-down-menu,
.m-table-form.inline .menu,
.m-table-form.table .menu {
    border-radius: 0!important;
}

.m-table-form.inline .m-menu a,
.m-table-form.table .m-menu a,
.m-table-form.inline .u-down-menu a,
.m-table-form.table .u-down-menu a,
.m-table-form.inline .menu a,
.m-table-form.table .menu a,
.m-table-form.inline .m-menu li,
.m-table-form.table .m-menu li,
.m-table-form.inline .u-down-menu li,
.m-table-form.table .u-down-menu li,
.m-table-form.inline .menu li,
.m-table-form.table .menu li {
    border-radius: 0!important;
}

.m-table-form.inline .u-l-select .u-input,
.m-table-form.table .u-l-select .u-input {
    border-radius: 0!important;
    border-left-width: 0!important;
    border-right-width: 1px!important;
}

.m-table-form.inline .u-l-select .u-input:last-child,
.m-table-form.table .u-l-select .u-input:last-child {
    border-right-width: 0!important;
}

.m-table-form.view .u-input:hover,
.m-table-form.view .u-label:hover,
.m-table-form.view .u-checkbox:hover,
.m-table-form.view .u-select:hover,
.m-table-form.view .u-textarea:hover {
    background-image: none;
}

.m-table-form.view .u-input:focus,
.m-table-form.view .u-label:focus,
.m-table-form.view .u-checkbox:focus,
.m-table-form.view .u-select:focus,
.m-table-form.view .u-textarea:focus {
    background-image: inherit;
    background-color: #fff;
    color: inherit;
    border-color: #e6e6e6;
}

.m-table-form.view .u-input:focus::-webkit-input-placeholder,
.m-table-form.view .u-label:focus::-webkit-input-placeholder,
.m-table-form.view .u-checkbox:focus::-webkit-input-placeholder,
.m-table-form.view .u-select:focus::-webkit-input-placeholder,
.m-table-form.view .u-textarea:focus::-webkit-input-placeholder {
    color: inherit;
}

.m-table-form.view .u-input:focus:-moz-placeholder,
.m-table-form.view .u-label:focus:-moz-placeholder,
.m-table-form.view .u-checkbox:focus:-moz-placeholder,
.m-table-form.view .u-select:focus:-moz-placeholder,
.m-table-form.view .u-textarea:focus:-moz-placeholder {
    color: inherit;
}

.m-table-form.view .u-input:focus::-moz-placeholder,
.m-table-form.view .u-label:focus::-moz-placeholder,
.m-table-form.view .u-checkbox:focus::-moz-placeholder,
.m-table-form.view .u-select:focus::-moz-placeholder,
.m-table-form.view .u-textarea:focus::-moz-placeholder {
    color: inherit;
}

.m-table-form.view .u-input:focus:-ms-input-placeholder,
.m-table-form.view .u-label:focus:-ms-input-placeholder,
.m-table-form.view .u-checkbox:focus:-ms-input-placeholder,
.m-table-form.view .u-select:focus:-ms-input-placeholder,
.m-table-form.view .u-textarea:focus:-ms-input-placeholder {
    color: inherit;
}

.m-table-form.view .u-input:disabled,
.m-table-form.view .u-label:disabled,
.m-table-form.view .u-checkbox:disabled,
.m-table-form.view .u-select:disabled,
.m-table-form.view .u-textarea:disabled {
    background-image: inherit;
    background: #fff;
}

.m-list {
    width: 100%;
    padding-left: 10px;
    padding-right: 10px;
}

.m-list li {
    width: 100%;
    border-bottom-style: double;
    border-bottom-color: transparent;
    border-bottom-width: 1px;
    height: 30px;
    line-height: 30px;
    position: relative;
}

.m-list li>* {
    display: block;
    height: 30px;
    line-height: 30px;
}

.m-list li a {
    text-decoration: none;
    line-height: 30px;
    color: #333;
    width: 100%;
}

.m-list li:hover {
    background-color: #fafafa;
}

.m-list li:hover a {
    color: var(--theme-primary);
}

.m-list.line li {
    border-bottom-color: #ddd;
}

.m-list.news li a {
    padding-left: 30px;
    padding-right: 130px;
}

.m-list.news li>.daytime,
.m-list.news li>.icon,
.m-list.news li>.time {
    position: absolute;
    top: 0;
}

.m-list.news li>.daytime,
.m-list.news li>.time {
    width: 130px;
    text-align: right;
    right: 0;
}

.m-list.news li>.icon {
    width: 30px;
    text-align: center;
    left: 0;
}

.m-list.newsinfo li {
    height: inherit;
    overflow: inherit;
    padding: 10px;
    zoom: 1;
}

.m-list.newsinfo li:after {
    content: "";
    display: block;
    height: 0;
    clear: both;
    visibility: hidden;
}

.m-list.newsinfo li a {
    height: inherit;
}

.m-list.newsinfo li .title {
    font-size: 18px;
    line-height: 24px;
    padding: 0 0 10px 0;
    font-weight: 500;
}

.m-list.newsinfo li .title .time {
    display: block;
    font-size: 12px;
    font-weight: normal;
}

.m-list.newsinfo li .info {
    line-height: 22px;
    font-size: 14px;
    height: inherit;
    opacity: 0.6;
}

.m-list.newsinfo li p.info {
    text-indent: 2em;
}

.m-list.newsinfo li.pic {
    padding-left: 170px;
    position: relative;
    min-height: 120px;
}

.m-list.newsinfo li.pic .img {
    position: absolute;
    width: 125px;
    height: 90px;
    top: 8px;
    left: 0;
    border: 5px #fff solid;
    box-shadow: 3px 3px 5px 0 rgba(0, 0, 0, 0.1);
}

.m-list.newsinfo li.bigtime {
    padding-left: 130px;
    position: relative;
    min-height: 110px;
}

.m-list.newsinfo li.bigtime .time {
    color: var(--theme-primary);
    position: absolute;
    width: 110px;
    height: 90px;
    top: 10px;
    left: 10px;
    border-right: 1px solid #ddd;
}

.m-list.newsinfo li.bigtime .time .datatime {
    font-size: 36px;
    display: block;
    line-height: 44px;
    padding-bottom: 5px;
}

.m-list.newsinfo li.bigtime .time .year {
    font-size: 22px;
    display: block;
    opacity: 0.5;
}

.m-list.message li {
    padding: 5px;
    margin: 5px 0;
    background-color: #f8f8f8;
    border-radius: 5px;
    line-height: 20px;
    height: auto;
}

.m-list.message li div {
    height: auto;
}

.m-list.message li p {
    line-height: 20px;
}

.m-list.icon li {
    width: 33.3%;
    float: left;
    height: inherit;
}

.m-list.icon li a {
    width: 100%;
    padding: 8px;
    text-align: center!important;
    height: inherit;
}

.m-list.icon li a>.icon {
    width: 66px;
    height: 66px;
    line-height: 66px;
    text-align: center;
    background-color: #e88f3c;
    border-radius: 50%;
    display: inline-block;
    position: relative;
}

.m-list.icon li a>.icon span {
    width: 16px;
    height: 16px;
    border-radius: 50%;
    position: absolute;
    top: 9px;
    right: 13px;
    background-color: #fff;
    color: var(--theme-primary);
    text-align: center;
    line-height: 16px;
    font-size: 10px;
}

.m-list.icon li a>.icon .iconfont {
    color: #fff;
    font-size: 42px;
}

.m-list.icon li a p,
.m-list.icon li a .title,
.m-list.icon li a .name {
    height: 30px;
    line-height: 30px;
    text-align: center;
    color: #333;
    padding: 0;
    width: 100%;
    display: block;
}


/*常用应用之五色循环*/

.m-list.icon li:nth-child(1n) a>.icon {
    background-color: #3E290D;
}

.m-list.icon li:nth-child(2n) a>.icon {
    background-color: #805419;
}

.m-list.icon li:nth-child(3n) a>.icon {
    background-color: #CB8931;
}

.m-list.icon li:nth-child(4n) a>.icon {
    background-color: #F4DDB3;
}

.m-list.icon li:nth-child(5n) a>.icon {
    background-color: #FCCF7F;
}

.m-list.icon.li2 li {
    width: 50%;
}

.m-list.icon.li4 li {
    width: 25%;
}

.m-list.icon.li5 li {
    width: 20%;
}

.m-list.img {
    padding-left: 0px;
    padding-right: 0px;
    margin-left: -4px;
    margin-right: -4px;
}

.m-list.img li {
    width: 16.67%;
    border: none;
    height: inherit;
    line-height: inherit;
    position: relative;
    z-index: 3;
    padding: 4px;
    display: inline-block;
    float: left;
}

.m-list.img li.col-1 {
    width: 8.33333333%;
}

.m-list.img li.col-2 {
    width: 16.66666667%;
}

.m-list.img li.col-3 {
    width: 25%;
}

.m-list.img li.col-4 {
    width: 33.33333333%;
}

.m-list.img li.col-6 {
    width: 50%;
}

.m-list.img li.col-12 {
    width: 100%;
}

.m-list.img li>* {
    display: block;
    height: min-content;
    line-height: inherit;
}

.m-list.img li a {
    text-decoration: none;
    line-height: inherit;
    color: transparent;
    width: 100%;
}

.m-list.img li img,
.m-list.img li a img {
    width: 100%;
    border: 4px solid #fff;
    box-shadow: 3px 3px 3px rgba(0, 0, 0, 0.07);
    box-sizing: border-box;
}

.m-list.img li:hover {
    background-color: transparent;
}

.m-list.img li:hover a {
    color: transparent;
}

.m-list.img li:hover img,
.m-list.img li:hover a img {
    box-shadow: 5px 5px 5px rgba(0, 0, 0, 0.15);
}

.m-list.img li .bar {
    position: absolute;
    bottom: 0;
    left: 0;
    width: 100%;
    display: flex;
    z-index: 3;
    padding: 8px;
}

.m-list.img li .bar .btn {
    line-height: 26px;
    text-align: center;
    background-color: rgba(0, 0, 0, 0.5);
    color: rgba(255, 255, 255, 0.8);
    flex-direction: column;
}

.m-list.img li .bar .btn:hover {
    background-color: #000000;
    color: #ffffff;
}

.m-list.img li .active {
    position: absolute;
    z-index: 4;
    width: 56px;
    height: 56px;
    line-height: 56px;
    text-align: center;
    top: 50%;
    left: 50%;
    margin: -28px 0 0 -28px;
}

.m-list.img li .active i {
    font-size: 33px;
    color: #bcf909;
}

.m-selectbox {
    width: 100%;
    height: 100%;
    display: table;
    clear: both;
    box-sizing: border-box;
    position: relative;
    border-collapse: collapse;
    border-spacing: 0;
}

.m-selectbox .foot {
    height: 49px;
    line-height: 30px;
    position: absolute;
    width: 100%;
    box-sizing: border-box;
    padding: 10px;
    background-color: #f6f8f8;
    left: 0;
    border-style: solid;
    border-width: 1px;
    border-color: #d9e2e2;
    bottom: 0;
}

.m-selectbox .left,
.m-selectbox .center,
.m-selectbox .right {
    display: table-cell;
    clear: both;
    height: 100%;
    box-sizing: border-box;
    position: relative;
    padding: 50px 0;
}

.m-selectbox .left .head,
.m-selectbox .center .head,
.m-selectbox .right .head {
    height: 49px;
    line-height: 30px;
    position: absolute;
    width: 100%;
    box-sizing: border-box;
    padding: 10px;
    background-color: #f6f8f8;
    left: 0;
    border-style: solid;
    border-width: 1px;
    border-color: #d9e2e2;
    top: 0;
}

.m-selectbox .left .head .title,
.m-selectbox .center .head .title,
.m-selectbox .right .head .title,
.m-selectbox .left .head .item,
.m-selectbox .center .head .item,
.m-selectbox .right .head .item {
    display: inline-block;
}

.m-selectbox .left .head .title,
.m-selectbox .center .head .title,
.m-selectbox .right .head .title {
    font-size: 14px;
    color: #526a6a;
    padding-right: 10px;
}

.m-selectbox .left .head .item,
.m-selectbox .center .head .item,
.m-selectbox .right .head .item {
    padding: 0 5px;
    height: 30px;
    line-height: 30px;
}

.m-selectbox .left .head .item .item,
.m-selectbox .center .head .item .item,
.m-selectbox .right .head .item .item {
    padding: 0;
}

.m-selectbox .left .body,
.m-selectbox .center .body,
.m-selectbox .right .body {
    height: 100%;
    width: 100%;
    box-sizing: border-box;
    padding: 10px;
    overflow: auto;
    float: left;
    border-left-style: solid;
    border-left-width: 1px;
    border-left-color: #d9e2e2;
    border-right-style: solid;
    border-right-width: 1px;
    border-right-color: #d9e2e2;
}

.m-selectbox .center {
    width: 40px;
    padding: 10px 5px;
    background-color: #fff;
    vertical-align: middle;
}

.m-selectbox .center .u-btn {
    margin: 5px 0;
    display: block;
    float: none;
}

.m-selectbox .left {
    background-color: #fff;
    width: 50%;
}

.m-selectbox .right {
    background-color: #e8eded;
    width: 50%;
}

.m-scroll {
    width: 100%;
    height: 100%;
    overflow: hidden;
    position: relative;
    -webkit-touch-callout: none;
    -webkit-user-select: none;
    -khtml-user-select: none;
    -moz-user-select: none;
    -ms-user-select: none;
    user-select: none;
}

.m-scroll .m-scroll-left {
    width: 100%;
    position: absolute;
    top: 0px;
    left: 0px;
    height: auto !important;
}

.m-scroll .m-scroll-right {
    position: absolute;
    top: 0px;
    right: 0px;
    width: 15px;
    height: 100%;
    background: #eee;
}

.m-scroll .m-scroll-bottom {
    position: absolute;
    left: 0px;
    bottom: 0px;
    width: 100%;
    height: 15px;
    background: #eee;
}

.m-scroll .scroll-ico-top,
.m-scroll .scroll-ico-right,
.m-scroll .scroll-ico-bottom,
.m-scroll .scroll-ico-left {
    position: absolute;
    left: 0px;
    top: 0px;
    width: 15px;
    height: 15px;
    background: #ccc;
    cursor: pointer;
    -webkit-touch-callout: none;
    -webkit-user-select: none;
    -khtml-user-select: none;
    -moz-user-select: none;
    -ms-user-select: none;
    user-select: none;
}

.m-scroll .scroll-ico-top .iconfont,
.m-scroll .scroll-ico-right .iconfont,
.m-scroll .scroll-ico-bottom .iconfont,
.m-scroll .scroll-ico-left .iconfont {
    display: block;
    float: left;
    color: #666;
    width: 15px;
    height: 15px;
    text-align: center;
    line-height: 15px;
}

.m-scroll .scroll-ico-bottom {
    bottom: 0px;
    top: auto;
}

.m-scroll .scroll-ico-right {
    left: auto;
    right: 0px;
}

.m-scroll .scroll-ico-con {
    width: 15px;
    height: 35px;
    position: absolute;
    left: 0px;
    top: 15px;
    background: #BBB;
    cursor: pointer;
    -webkit-touch-callout: none;
    -webkit-user-select: none;
    -khtml-user-select: none;
    -moz-user-select: none;
    -ms-user-select: none;
    user-select: none;
}

.m-scroll .scroll-ico-con:hover {
    background: #999;
}

.m-scroll .m-scroll-bottom .scroll-ico-con {
    width: 35px;
    height: 15px;
    position: absolute;
    left: 15px;
    top: 0px;
    background: #b3b3b3;
    cursor: pointer;
    -webkit-touch-callout: none;
    -webkit-user-select: none;
    -khtml-user-select: none;
    -moz-user-select: none;
    -ms-user-select: none;
    user-select: none;
}

.m-scroll .m-scroll-bottom .scroll-ico-con:hover {
    background: #999;
}

.m-scroll .m-scroll-bg {
    width: 15px;
    height: 15px;
    background: #eee;
    position: absolute;
    bottom: 0px;
    right: 0px;
}

.m-scroll.style01 .m-scroll-right {
    width: 5px;
}

.m-scroll.style01 .scroll-ico-top,
.m-scroll.style01 .scroll-ico-bottom {
    display: none;
}

.m-scroll.style01 .scroll-ico-con {
    width: 5px;
    height: 35px;
    background: #FF971E;
    top: 0px;
    border-radius: 5px;
}

.m-scroll.style01 .m-scroll-bottom {
    height: 5px;
}

.m-scroll.style01 .m-scroll-bottom .scroll-ico-con {
    height: 5px;
    background: #FF971E;
    top: 0px;
    left: 0px;
    border-radius: 5px;
}

.m-scroll.style01 .scroll-ico-left,
.m-scroll.style01 .scroll-ico-right {
    display: none;
}

.m-scrolltabs {
    position: relative;
    padding: 0px 38px;
    box-sizing: border-box;
    width: 100%;
    display: table;
}

.m-scrolltabs .left-bar,
.m-scrolltabs .right-bar {
    position: absolute;
    left: 0px;
    top: 2px;
    width: 35px;
    height: 35px;
    text-align: center;
    line-height: 35px;
    color: #eeeeee;
    cursor: pointer;
    font-size: 35px;
    z-index: 1000;
}

.m-scrolltabs .left-bar:active,
.m-scrolltabs .right-bar:active {
    color: #ffffff;
}

.m-scrolltabs .right-bar {
    left: auto;
    right: 0px;
}

.m-scrolltabs .scrolltabs-con {
    height: 100%;
    overflow: hidden;
    position: relative;
}

.m-scrolltabs.m-scrolltabs-cover {
    padding: 0px;
}


/*timeline*/

.m-timeline {
    margin-left: 10px;
    border-left: #e3e3e3 1px solid;
    box-sizing: border-box;
}

.m-timeline li {
    display: block;
    clear: both;
    position: relative;
    border-left: #eee 1px solid;
    margin-left: -1px;
    padding: 0 0 8px 20px!important;
    overflow: visible !important;
}

.m-timeline li .icon {
    position: absolute;
    left: -10px;
    top: 5px;
    font-size: 19px;
    color: #DBDBDB;
    display: block;
    width: 19px;
    height: 19px;
    line-height: 19px;
    background-color: #fff;
    border-radius: 50%;
    z-index: 120;
}

.m-timeline li .icon:after {
    position: absolute;
    top: 2px;
    left: 15px;
    font-size: 0px;
    width: 1px;
    height: 1px;
    border: 7px solid rgba(255, 255, 255, 0);
    border-right-color: #f3f5f6;
    content: ".";
    border-radius: 0;
    z-index: 2;
}

.m-timeline li .content {
    border-radius: 5px;
    background: #f3f5f6;
    padding: 5px;
    margin: 0;
    color: #5d5a5a;
    transition: margin-left 0.3s;
    -moz-transition: margin-left 0.3s;
    /* Firefox 4 */
    -webkit-transition: margin-left 0.3s;
    /* Safari 和 Chrome */
    -o-transition: margin-left 0.3s;
    /* Opera */
}

.m-timeline li .content ul {
    margin-left: 12px;
}

.m-timeline li .content ul li {
    padding: 3px 0 3px 0!important;
    border: none!important;
    margin: 0!important;
}

.m-timeline li.activate,
.m-timeline li.active {
    border-color: #2bb4d0;
}

.m-timeline li.activate .content,
.m-timeline li.active .content {
    background: #23B7FF;
    color: #ffffff;
}

.m-timeline li.activate .icon,
.m-timeline li.active .icon {
    color: #23B7FF;
    left: -13px;
    font-size: 26px;
    width: 26px;
    height: 26px;
    line-height: 26px;
}

.m-timeline li.activate .icon:after,
.m-timeline li.active .icon:after {
    left: 18px;
    border-right-color: #23B7FF;
}

.m-timeline li.finished {
    border-color: #64B94F;
}

.m-timeline li.finished .icon {
    color: #64B94F;
}

.m-timeline li.finished .content {
    color: #888;
}

.m-timeline li:last-child {
    border-left: #fff 1px solid;
}

.m-timeline li.clickstyle .content:hover {
    cursor: pointer;
    margin-left: -5px;
}

.m-timeline.inline {
    display: table;
    text-align: center;
    margin: 0;
    padding: 0;
    margin-top: 45px;
    border-left: none;
    width: 100%;
}

.m-timeline.inline li {
    display: table-cell;
    clear: inherit;
    border-left: none;
    margin-left: none;
    padding: 0!important;
    border-top: #eee 2px solid;
}

.m-timeline.inline li .content {
    background: none;
}

.m-timeline.inline li .icon {
    top: -31px;
    left: 50%;
    margin-left: -9px;
}

.m-timeline.inline li .icon:after {
    position: absolute;
    top: 15px;
    left: 50%;
    margin-left: -8px;
    font-size: 0px;
    width: 1px;
    height: 1px;
    border: 7px solid rgba(255, 255, 255, 0);
    border-bottom-color: #eee;
    content: ".";
    border-radius: 0;
    z-index: 2;
}

.m-timeline.inline li.activate,
.m-timeline.inline li.active {
    border-color: #FF5700;
}

.m-timeline.inline li.activate .icon,
.m-timeline.inline li.active .icon {
    top: -35px;
    left: 50%;
    margin-left: -13px;
    color: #FF5700;
}

.m-timeline.inline li.activate .icon:after,
.m-timeline.inline li.active .icon:after {
    margin-left: -7px;
    border-bottom-color: #FF5700;
    top: 19px;
}

.m-timeline.inline li.activate .content,
.m-timeline.inline li.active .content {
    color: #FF5700;
}

.m-timeline.inline li.finished {
    border-color: #64B94F;
}

.m-timeline.inline li.finished .icon:after {
    border-bottom-color: #64B94F;
}

.m-sidemenu {
    margin: 0;
    padding: 0;
    position: relative;
}

.m-sidemenu:after {
    clear: both;
    display: block;
    content: '';
}

.m-sidemenu li {
    clear: both;
    display: block;
    overflow: hidden;
    position: relative;
    padding: 0px;
    border-bottom-style: solid;
    border-bottom-width: 1px;
}

.m-sidemenu li a {
    display: block;
    padding: 10px 17px 10px 0;
    width: 100%;
    position: relative;
    font-size: 13px;
    text-indent: 10px;
    line-height: 16px;
    margin-bottom: 0;
    border-bottom-style: solid;
    border-bottom-width: 0px;
    overflow: visible;
    text-overflow: ellipsis;
    white-space: nowrap;
}

.m-sidemenu li a.parent {
    font-weight: normal;
}

.m-sidemenu li a.parent.actived,
.m-sidemenu li a.parent.active {
    font-weight: bold;
}


/*二级菜单子元素图标*/

.m-sidemenu li a .icon {
    font-size: 16px;
    font-weight: bold;
    position: static;
    margin-right: 5px;
}

.m-sidemenu li a .u-point {
    position: absolute;
    right: 15px;
    top: 9px;
    text-indent: 0;
    display: block;
    border-radius: 50%;
    text-align: center;
    font-weight: normal;
}

.m-sidemenu li a .u-point.text {
    padding: 0 5px;
    text-shadow: none!important;
}

.m-sidemenu li a:hover {
    transition: background-color 0.4s, color 0.4s;
    -moz-transition: background-color 0.4s, color 0.4s;
    -webkit-transition: background-color 0.4s, color 0.4s;
    -o-transition: background-color 0.4s, color 0.4s;
}


/*二级菜单点击后加粗*/

.m-sidemenu li a.active {
    border-left: none;
    box-sizing: border-box;
}

.m-sidemenu li a.active:after {
    content: '';
    width: 5px;
    height: 37px;
    position: absolute;
    left: 0;
    top: 0;
}

.m-sidemenu li ul {
    display: none;
    clear: both;
    position: relative;
}

.m-sidemenu li li {
    border-bottom-width: 0px;
    padding: 0;
}

.m-sidemenu li li a {
    text-indent: 23px;
    border-bottom-width: 0px;
}

.m-sidemenu li li li a {
    text-indent: 36px;
}

.m-sidemenu li li li li a {
    text-indent: 49px;
}

.m-sidemenu li li li li li a {
    text-indent: 62px;
}

.m-sidemenu li li li li li li a {
    text-indent: 75px;
}

.m-sidemenu li .arrow {
    position: absolute;
    right: 8px;
    top: 13px;
    font-size: 16px;
    width: 12px;
    height: 12px;
    display: block;
    font-weight: normal;
    line-height: 12px;
    text-indent: 0;
    transition: transform 0.3s;
    -moz-transition: -moz-transform 0.3s;
    /* Firefox 4 */
    -webkit-transition: -webkit-transform 0.3s;
    /* Safari and Chrome */
    -o-transition: -o-transform 0.3s;
    /* Opera */
}

.m-sidemenu li a.active .arrow,
.m-sidemenu li a.actived .arrow {
    transform: rotate(90deg);
    -moz-transform: rotate(90deg);
    /* Firefox */
    -webkit-transform: rotate(90deg);
    /* Safari 和 Chrome */
    -o-transform: rotate(90deg);
    /* Opera */
    top: 10px;
}

.m-sidemenu .noicon a:before {
    content: "○";
    font-size: 13px;
    margin-right: 4px;
}

.m-sidemenu .noicon .icon {
    display: none!important;
}

.m-sidemenu.min {
    width: 39px!important;
    margin: 0;
    border: none;
    padding: 0;
}

.m-sidemenu.min:after,
.m-sidemenu.min:before {
    display: none;
}

.m-sidemenu.min li {
    width: 100%;
    margin-bottom: 0;
    box-shadow: none;
    border: none;
    overflow: visible;
    border-radius: 0;
}

.m-sidemenu.min li a {
    padding: 0 0 0 39px;
    width: 0;
    height: 39px;
    line-height: 39px;
    display: block;
    overflow: hidden;
    text-indent: 0px;
}

.m-sidemenu.min li a:after,
.m-sidemenu.min li a:before {
    display: none;
}

.m-sidemenu.min li a .point {
    right: 0px;
    top: 0px;
}

.m-sidemenu.min li a .icon {
    font-size: 23px;
    text-indent: 0px;
    left: 9px;
    top: 0px;
    position: absolute;
}

.m-sidemenu.min li a .arrow {
    display: none;
}

.m-sidemenu.min li ul {
    width: 200px;
    position: absolute;
    left: 39px;
    top: 0;
    z-index: 9999;
}

.m-sidemenu.min li ul li a {
    padding: 0;
    width: 100%;
    text-indent: 10px;
}

.m-sidemenu.min li ul li a .icon {
    position: static;
    font-size: 13px;
    left: 0;
    top: 0;
}

.m-sidemenu.min li ul li a .point {
    top: 10px;
    right: 18px;
}

.m-sidemenu.min li ul li a .arrow {
    display: block;
    top: 14px!important;
}

.m-sidemenu.min li ul li ul {
    position: static!important;
    width: 100%;
}

.m-sidemenu.min li ul li ul li a {
    text-indent: 28px;
}

.m-sidemenu.min li ul li ul li li a {
    text-indent: 42px;
}


/*二级菜单子元素*/

.m-sidemenu li {
    background-color: #fff;
    color: #333;
    border-color: #d1d1d1;
}

.m-sidemenu li a {
    color: #808080;
}

.m-sidemenu li a:hover {
    color: #999999;
}

.m-sidemenu li a {
    background-color: #fff;
    border-color: #d1d1d1;
    font-size: 14px;
    font-weight: 400;
    color: #333;
}

.m-sidemenu li a a {
    color: #808080;
}

.m-sidemenu li a a:hover {
    color: #999999;
}

.m-sidemenu li a:hover {
    background-color: rgba(167, 122, 62, 0.1);
    color: var(--theme-primary);
}

.m-sidemenu li a:hover a {
    color: var(--theme-primary);
}

.m-sidemenu li a:hover a:hover {
    color: var(--theme-primary);
}

.m-sidemenu li a.active {
    color: var(--theme-primary);
}

.m-sidemenu li a.active:after {
    background-color: var(--theme-primary);
}

.m-sidemenu li a.actived {
    color: var(--theme-primary);
}

@keyframes arrowAc {
    from {
        transform: rotate(0deg);
    }
    to {
        transform: rotate(90deg);
    }
}

@-moz-keyframes arrowAc {
    /* Firefox */
    from {
        transform: rotate(0deg);
    }
    to {
        transform: rotate(90deg);
    }
}

@-webkit-keyframes arrowAc {
    /* Safari 和 Chrome */
    from {
        transform: rotate(0deg);
    }
    to {
        transform: rotate(90deg);
    }
}

@-o-keyframes arrowAc {
    /* Opera */
    from {
        transform: rotate(0deg);
    }
    to {
        transform: rotate(90deg);
    }
}

.m-topmenu {
    padding: 0;
}

.m-topmenu>li {
    float: left;
    display: inline-block;
    display: inline-flex;
    position: relative;
    overflow: visible;
}

.m-topmenu>li a {
    display: block;
    padding: 0 13px;
    white-space: nowrap;
    position: relative;
    overflow: hidden;
    min-width: 65px;
    float: left;
    text-align: center;
}

.m-topmenu>li a:hover .arrow {
    display: inline-table;
    animation: fadeInDown 0.4s;
}

.m-topmenu>li a .icon {
    margin-right: 5px;
    font-size: 18px;
    float: inherit;
}

.m-topmenu>li a .arrow {
    margin-left: 5px;
}

.m-topmenu>li a.active {
    border-bottom-width: 3px;
    border-bottom-style: solid;
    font-weight: bold;
}

.m-topmenu>li a.active i {
    font-weight: normal;
}

.m-topmenu>li ul {
    display: none;
    min-width: 100%;
    position: absolute;
    left: 0;
    z-index: 9999;
    animation: fadeInDown 0.4s;
}

.m-topmenu>li ul li {
    display: block;
    width: 100%;
    position: relative;
    height: 40px;
}

.m-topmenu>li ul li a {
    width: 100%;
    display: block;
    height: 40px;
    line-height: 40px;
    margin-right: 0px;
}

.m-topmenu>li ul li a.active {
    border-bottom-width: 0;
    border-bottom-style: solid;
}

.m-topmenu>li ul li a .arrow {
    margin-left: 5px;
}

.m-topmenu>li ul li a .icon {
    font-size: 14px;
}

.m-topmenu>li ul li a:hover .arrow {
    display: inline-table;
    animation: fadeInLeft 0.4s;
}

.m-topmenu>li ul li ul {
    left: 100%;
    display: none;
    top: 0;
    animation: fadeInLeft 0.4s;
}

.m-topmenu>li a {
    height: 56px;
    line-height: 56px;
    font-size: 14px;
}

.m-topmenu>li a i.iconfont {
    font-size: 14px;
}

.m-topmenu>li ul {
    top: 56px;
}

.m-topmenu>li.active a {
    text-shadow: 2px 2px 2px rgba(0, 0, 0, 0.6);
    display: inline-flex;
}

.m-topmenu>li.active a i.iconfont {
    font-size: 22px;
    margin-left: -5px;
    margin-right: 5px;
    animation: bounceIn 0.8s;
    line-height: 53px;
}

.m-topmenu>li a {
    background-color: var(--theme-primary);
    color: #fef8f8;
    border-color: #e52e21;
}

.m-topmenu>li a a {
    color: #fef8f8;
}

.m-topmenu>li a a:hover {
    color: #ffffff;
}

.m-topmenu>li a:hover {
    background-color: #ec6258;
    color: #fff;
    border-color: #ec6258;
}

.m-topmenu>li a:active {
    background-color: #c72217;
}

.m-topmenu>li a.active {
    border-color: #fff;
    color: #fff;
    background-color: #ed6a61;
}

.m-topmenu>li.active a {
    border-color: #b01f14;
    color: #fff;
    background-color: #ed6a61;
}

.m-topmenu>li ul li a {
    background-color: #e52a1c;
    color: #fad6d3;
    border-color: #c72217;
}

.m-topmenu>li ul li a a {
    color: #fad6d3;
}

.m-topmenu>li ul li a a:hover {
    color: #ffffff;
}

.m-topmenu>li ul li ul li a {
    background-color: #dd261a;
    color: #f9cdca;
    border-color: #bd2116;
}

.m-topmenu>li ul li ul li a a {
    color: #f9cdca;
}

.m-topmenu>li ul li ul li a a:hover {
    color: #fef8f8;
}

.item.f-right.webuploader-container,
.item.f-left.webuploader-container {
    width: inherit;
    position: relative;
}

.item.f-right.webuploader-container *,
.item.f-left.webuploader-container * {
    cursor: pointer!important;
}

.item.f-right.webuploader-container .webuploader-pick,
.item.f-left.webuploader-container .webuploader-pick {
    height: 32px;
    line-height: 30px;
    padding: 0 5px;
    border-radius: 3px;
    border-style: solid;
    border-width: 1px;
    font-size: 13px;
    background-color: var(--theme-primary);
    color: #fef8f8;
    border-color: var(--theme-primary);
    color: #fff!important;
}

.item.f-right.webuploader-container .webuploader-pick:after,
.item.f-left.webuploader-container .webuploader-pick:after {
    content: '+选择文件';
}

.item.f-right.webuploader-container .webuploader-pick a,
.item.f-left.webuploader-container .webuploader-pick a {
    color: #fef8f8;
}

.item.f-right.webuploader-container .webuploader-pick a:hover,
.item.f-left.webuploader-container .webuploader-pick a:hover {
    color: #ffffff;
}

.item.f-right.webuploader-container .webuploader-pick:hover,
.item.f-left.webuploader-container .webuploader-pick:hover {
    background-color: #eb594f;
    color: #ffffff;
    border-color: #e73b2f;
    box-shadow: 0 0 8px var(--theme-primary);
}

.item.f-right.webuploader-container .webuploader-pick:hover a,
.item.f-left.webuploader-container .webuploader-pick:hover a {
    color: #ffffff;
}

.item.f-right.webuploader-container .webuploader-pick:hover a:hover,
.item.f-left.webuploader-container .webuploader-pick:hover a:hover {
    color: #ffffff;
}

.item.f-right.webuploader-container .webuploader-pick:active,
.item.f-left.webuploader-container .webuploader-pick:active,
.item.f-right.webuploader-container .webuploader-pick.active,
.item.f-left.webuploader-container .webuploader-pick.active {
    box-shadow: 0 3px 3px #c72217 inset;
}

.item.f-right.webuploader-container .webuploader-pick:hover,
.item.f-left.webuploader-container .webuploader-pick:hover {
    color: #ffffff;
}

.item.f-right.webuploader-container .webuploader-pick>.u-point,
.item.f-left.webuploader-container .webuploader-pick>.u-point {
    background-color: #fff;
    color: var(--theme-primary);
}

.item.f-right.webuploader-container input,
.item.f-left.webuploader-container input {
    width: 1px;
    height: 1px;
    opacity: 0;
    cursor: pointer!important;
}

.item.f-right.webuploader-container label,
.item.f-left.webuploader-container label {
    position: absolute;
    top: 0;
    left: 0;
}

.upload-box .panel-head {
    background-color: #fff;
}

.upload-box .panel-body {
    padding: 0;
}

.upload-list {
    margin: 0;
    padding: 0;
    display: block;
}

.upload-list li {
    position: relative;
    display: inline;
    float: left;
    width: 160px;
    height: 160px;
    background-color: #fff;
    box-shadow: 0 0 5px rgba(0, 0, 0, 0.2);
    margin: 5px;
    box-sizing: border-box;
    padding: 5px;
}

.upload-list li .info {
    width: 100%;
    height: 110px;
    text-align: center;
    display: block;
    position: relative;
    float: left;
}

.upload-list li .info img {
    width: 100%;
    height: 100%;
    cursor: pointer;
}

.upload-list li .info i {
    width: 100%;
    height: 100%;
    font-size: 90px;
    line-height: 100px;
    text-align: center;
}

.upload-list li .name {
    height: 22px;
    box-sizing: border-box;
    padding: 5px;
    background-color: #EBEBEB;
    color: #9A9A9A;
    line-height: 12px;
    font-size: 12px;
    width: 150px;
    display: block;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    transition: all 0.3s linear;
    -moz-transition: all 0.3s linear;
    -webkit-transition: all 0.3s linear;
    -o-transition: all 0.3s linear;
}

.upload-list li .mb {
    height: 20px;
    line-height: 20px;
    width: 40%;
    font-size: 12px;
    color: #9F9F9F;
    display: none;
    float: left;
    text-align: left;
}

.upload-list li .user {
    height: 20px;
    line-height: 20px;
    width: 60%;
    font-size: 12px;
    color: #9F9F9F;
    display: none;
    float: right;
    text-align: right;
}

.upload-list li .view {
    display: none;
}

.upload-list li .delete {
    position: absolute;
    top: 5px;
    right: 5px;
    height: 16px;
    width: 16px;
    line-height: 16px;
    text-align: center;
    font-size: 12px;
    color: #7F7F7F;
    background-color: #fff;
    border: 1px solid #eee;
    z-index: 3;
    cursor: pointer;
}

.upload-list li:hover .name {
    position: absolute;
    top: 114px;
    left: 5px;
    z-index: 4;
    line-height: 16px;
    background-color: #3F3F3F;
    color: #fff;
    overflow: visible;
    text-overflow: inherit;
    white-space: normal;
    height: auto;
}

.upload-list li.add {
    font-size: 55px;
    color: #cacaca;
    line-height: 81px;
    text-align: center;
    cursor: pointer;
}

.upload-list li.add:after {
    content: "点击上传文件";
    position: absolute;
    bottom: 17px;
    left: 50%;
    margin-left: -3em;
    font-size: 12px;
    line-height: 12px;
}

.upload-list li.add:hover {
    color: #707070;
    background-color: #e5e5e5;
}

.upload-list.table li {
    background-color: #fff;
    display: table;
    width: 100%;
    box-shadow: none;
    margin: 0;
    float: none;
    height: 35px;
    padding: 0;
    line-height: 35px;
    border-bottom: 1px solid #d0d0d0;
}

.upload-list.table li>* {
    display: table-cell;
    height: 35px;
    line-height: 35px;
    font-size: 12px;
    position: inherit;
    background: none;
    padding: 0;
    float: none;
}

.upload-list.table li .info {
    width: 40px;
    padding: 0px;
    position: relative;
}

.upload-list.table li .info i {
    width: inherit;
    height: inherit;
    line-height: inherit;
    font-size: 22px;
    display: block;
    float: left;
    position: absolute;
    top: 0;
    left: 0;
}

.upload-list.table li .info img {
    width: 21px;
    height: 21px;
    display: block;
    position: absolute;
    top: 7px;
    left: 9px;
}

.upload-list.table li .name {
    width: 41%;
    text-align: left;
    position: absolute;
    top: 0;
}

.upload-list.table li .user {
    width: 100px;
    text-align: left;
    float: none;
}

.upload-list.table li .time {
    width: 140px;
    text-align: left;
    float: none;
}

.upload-list.table li .mb {
    width: 60px;
    text-align: left;
    float: none;
}

.upload-list.table li .view {
    position: inherit;
    width: 30px;
    text-align: left;
    float: none;
    font-size: 16px;
    border: none;
    top: 0;
    left: 0;
}

.upload-list.table li .delete {
    position: inherit;
    width: 30px;
    text-align: left;
    float: none;
    font-size: 16px;
    border: none;
    top: 0;
    left: 0;
}

.upload-list.table li:nth-child(even) {
    background-color: #f8f8f8;
}

.upload-list.table li:hover .name {
    display: block;
    width: 41%;
    position: absolute;
    height: 35px;
    float: none;
    background: none;
    top: 0;
    left: inherit;
    line-height: 35px;
    color: #307BC9;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
}

.upload-list.table li.add {
    font-size: 19px;
    line-height: 33px;
    height: 35px;
    background-color: #fff;
    border: 1px dashed #ddd;
    margin-top: 5px;
    color: #333;
    text-indent: -48px;
}

.upload-list.table li.add:after {
    top: 0;
    margin-left: 43px;
    line-height: 35px;
    height: 35px;
}

.upload-list.table li.add:hover {
    background-color: #f0f0f0;
    color: #929292;
}

.m-path {
    height: 45px;
    background-color: #f3f3f3;
    color: #404040;
    border-color: #c5c5c5;
    background-color: transparent;
}

.m-path li {
    float: left;
    padding-left: 8px;
}

.m-path li a {
    display: block;
    position: relative;
}

.m-path li {
    height: 45px;
    line-height: 45px;
    background-image: url(../img/jt-r.png);
    background-position: left 50%;
    background-repeat: no-repeat;
    padding-left: 17px;
}

.m-path li:first-child {
    background-image: url(../img/home.png);
    margin-left: 10px;
    padding-left: 20px;
}

.m-path li a {
    height: 45px;
    line-height: 45px;
}

.m-path li.active a {
    font-size: 15.75px;
}

.m-path li.home a .homeicon {
    font-size: 18px;
    margin-right: 5px;
}

.m-path a {
    color: #737373;
}

.m-path a:hover {
    color: #8d8d8d;
}

.m-path li.active {
    color: var(--theme-primary);
}

.m-path li.active a {
    color: var(--theme-primary);
}

.quarters-loader:not(:required) {
    -moz-animation: three-quarters-loader 700ms infinite linear;
    -webkit-animation: three-quarters-loader 700ms infinite linear;
    animation: three-quarters-loader 700ms infinite linear;
    border: 3px solid var(--theme-primary);
    border-right-color: rgba(0, 0, 0, 0);
    border-radius: 50%;
    box-sizing: border-box;
    display: inline-block;
    position: relative;
    overflow: visible;
    text-indent: -9999px;
    width: 32px;
    height: 32px;
    background: none;
}

.quarters-loader:after {
    content: "\20";
    width: 6px;
    height: 6px;
    font-size: 1px;
    border-radius: 50%;
    display: block;
    position: absolute;
    top: 5px;
    right: -4px;
    background: #fff;
    text-indent: 0;
    overflow: hidden;
    box-shadow: 0 0 9px var(--theme-primary);
}

.quarters-loader {
    width: 84px;
    height: 84px;
    background: url("../img/login.gif") center 50% no-repeat;
    display: block;
    margin-bottom: 10px;
}

.loading_bg .loading_bg_center {
    position: absolute;
    top: 50%;
    left: 50%;
    color: #fff;
    text-align: center;
    width: 90px;
    height: 90px;
    margin-top: -45px;
    margin-left: -45px;
    padding-top: 25px;
}

.loading_bg {
    background: rgba(0, 0, 0, 0.5);
    background: url("../img/bg-bg.png");
    display: table-cell;
    text-align: center;
    height: 100%;
    width: 100%;
    position: fixed;
    z-index: 9999;
    top: 0;
    left: 0;
}

@-moz-keyframes three-quarters-loader {
    0% {
        -moz-transform: rotate(0deg);
        transform: rotate(0deg);
    }
    100% {
        -moz-transform: rotate(360deg);
        transform: rotate(360deg);
    }
}

@-webkit-keyframes three-quarters-loader {
    0% {
        -webkit-transform: rotate(0deg);
        transform: rotate(0deg);
    }
    100% {
        -webkit-transform: rotate(360deg);
        transform: rotate(360deg);
    }
}

@keyframes three-quarters-loader {
    0% {
        -moz-transform: rotate(0deg);
        -ms-transform: rotate(0deg);
        -webkit-transform: rotate(0deg);
        transform: rotate(0deg);
    }
    100% {
        -moz-transform: rotate(360deg);
        -ms-transform: rotate(360deg);
        -webkit-transform: rotate(360deg);
        transform: rotate(360deg);
    }
}

.m-choosePeople .show {
    display: block !important;
}

.m-choosePeople .hide {
    display: none !important;
}

.m-choosePeople .fl {
    float: left;
}

.m-choosePeople .fr {
    float: right;
}

.m-choosePeople .disabled {
    cursor: not-allowed;
}

.m-choosePeople .checkbox,
.m-choosePeople .radio {
    width: 16px;
    height: 16px;
    margin-right: 8px;
    display: inline-block;
    background-image: url("check.png");
    vertical-align: middle;
    cursor: pointer;
}

.m-choosePeople .checkbox {
    background-position: 0 0;
}

.m-choosePeople .checkbox.on {
    background-position: 0 -16px;
}

.m-choosePeople .radio {
    background-position: 0 -32px;
}

.m-choosePeople .radio.on {
    background-position: 0 -48px;
}

.m-choosePeople .clearfix {
    zoom: 1;
}

.m-choosePeople .clearfix:after {
    display: block;
    clear: both;
    content: "";
}

.m-choosePeople .btn {
    height: 30px;
    padding: 0 20px;
    display: inline-block;
    -webkit-border-radius: 3px 3px 3px 3px;
    -moz-border-radius: 3px 3px 3px 3px;
    border-radius: 3px 3px 3px 3px;
    line-height: 30px;
    font-size: 14px;
}

.m-choosePeople .btn.btn-ok {
    margin-right: 10px;
    background: #28a9e5;
    border: 1px solid #28a9e5;
    color: #ffffff;
}

.m-choosePeople .btn.btn-cancel {
    background: #ffffff;
    border: 1px solid #cddff1;
    color: var(--theme-primary);
}

.m-choosePeople .search {
    height: 24px;
    position: relative;
    font-size: 0;
}

.m-choosePeople .search input,
.m-choosePeople .search a {
    font-size: 12px;
    vertical-align: middle;
}

.m-choosePeople .search input {
    width: 100%;
    height: 24px;
    border: 1px solid #a1c3e6;
    border-right: 0;
    background: #ffffff;
    -webkit-border-radius: 3px 0 0 3px;
    -moz-border-radius: 3px 0 0 3px;
    border-radius: 3px 0 0 3px;
    line-height: 24px;
    text-indent: 5px;
    color: #c7c6c8;
}

.m-choosePeople .search a {
    height: 24px;
    padding: 0 10px;
    position: absolute;
    top: 0;
    right: -3px;
    display: inline-block;
    background: #28a9e5;
    border: 1px solid #28a9e5;
    border-left: 0;
    -webkit-border-radius: 0 3px 3px 0;
    -moz-border-radius: 0 3px 3px 0;
    border-radius: 0 3px 3px 0;
    color: #ffffff;
    line-height: 24px;
}

.m-choosePeople .container {
    position: relative;
}

.m-choosePeople .content {
    position: absolute;
    left: 0;
    right: 0;
    top: 0;
    bottom: 50px;
    border: 1px solid #cddff1;
    overflow: auto;
}

.m-choosePeople .left,
.m-choosePeople .right {
    position: absolute;
    top: 0;
    bottom: 0;
    -webkit-box-sizing: border-box;
    -moz-box-sizing: border-box;
    box-sizing: border-box;
}

.m-choosePeople .left {
    width: 30%;
    left: 0;
    border-right: 1px solid #cddff1;
}

.m-choosePeople .left .content_box {
    top: 0 !important;
    overflow: auto;
}

.m-choosePeople .right {
    left: 30%;
    right: 0;
}

.m-choosePeople .right .search_box {
    width: 200px;
}

.m-choosePeople .right input,
.m-choosePeople .right label {
    vertical-align: middle;
}

.m-choosePeople .right .select_all_box {
    line-height: 26px;
    font-size: 0;
}

.m-choosePeople .right .select_all_box .checkbox {
    margin-right: 3px;
}

.m-choosePeople .right .select_all_box label {
    color: var(--theme-primary);
    font-size: 12px;
}

.m-choosePeople .right .right_head {
    padding: 5px 10px;
    background: #e1ebf7;
}

.m-choosePeople .right .all_list_box {
    position: absolute;
    left: 0;
    right: 0;
    top: 0;
    bottom: 120px;
    overflow: auto;
}

.m-choosePeople .right .all_list {
    height: 100%;
    overflow: auto;
}

.m-choosePeople .right .all_list li {
    width: 33.3%;
    height: 28px;
    margin: 5px 0;
    float: left;
    line-height: 28px;
    font-size: 0;
}

.m-choosePeople .right .all_list li.person {
    min-width: 80px;
}

.m-choosePeople .right .all_list li .main_info_box {
    height: 100%;
    padding: 0 20px 0 5px;
    background: #ffffff;
    -webkit-box-sizing: border-box;
    -moz-box-sizing: border-box;
    box-sizing: border-box;
    -webkit-border-radius: 3px 3px 3px 3px;
    -moz-border-radius: 3px 3px 3px 3px;
    border-radius: 3px 3px 3px 3px;
    color: #000;
    overflow: hidden;
    white-space: nowrap;
    text-overflow: ellipsis;
}

.m-choosePeople .right .all_list li .main_info_box.disabled .checkbox,
.m-choosePeople .right .all_list li .main_info_box.disabled .radio,
.m-choosePeople .right .all_list li .main_info_box.disabled .input_label_txt {
    cursor: not-allowed;
    color: #707070;
}

.m-choosePeople .right .all_list li .input_label_txt {
    font-size: 12px;
    cursor: pointer;
}

.m-choosePeople .right .selected_list_box {
    height: 109px;
    padding: 12px 0 5px 11px;
    position: absolute;
    left: 0;
    right: 0;
    bottom: 0;
    background: #edf3fb;
    border-top: 1px solid #cddff1;
}

.m-choosePeople .right .selected_list_box h3 {
    color: #323032;
    font-size: 12px;
}

.m-choosePeople .right .selected_list {
    margin-top: 5px;
    position: absolute;
    top: 20px;
    bottom: 0;
    left: 0;
    right: 0;
    overflow: auto;
}

.m-choosePeople .right .selected_list li {
    width: 33.3%;
    margin: 5px 0px;
    height: 28px;
    float: left;
    line-height: 28px;
}

.m-choosePeople .right .selected_list li .main_info_box {
    height: 100%;
    background: rgba(233, 76, 65, 0.1);
    margin: 0 5px;
    -webkit-border-radius: 3px 3px 3px 3px;
    -moz-border-radius: 3px 3px 3px 3px;
    border-radius: 3px 3px 3px 3px;
    -webkit-box-sizing: border-box;
    -moz-box-sizing: border-box;
    box-sizing: border-box;
    position: relative;
    color: #000;
    overflow: hidden;
    white-space: nowrap;
    text-overflow: ellipsis;
}

.m-choosePeople .right .selected_list li.person {
    min-width: 80px;
}

.m-choosePeople .right .selected_list li:hover .remove {
    display: block;
}

.m-choosePeople .right .selected_list .remove {
    position: absolute;
    right: 5px;
    top: 0;
    display: none;
    font-size: 12px;
    color: #d9d9d8;
    cursor: pointer;
    z-index: 2;
}

.m-choosePeople .right .selected_list .remove:hover {
    color: #e7372a;
}

.m-choosePeople .con_tab {
    height: 100%;
    position: relative;
}

.m-choosePeople .con_tab .con_tab_head {
    padding: 5px 5px 0 5px;
    background: #edf3fb;
    color: #ffffff;
}

.m-choosePeople .con_tab .con_tab_head li {
    height: 30px;
    padding: 0 10px;
    float: left;
    line-height: 30px;
}

.m-choosePeople .con_tab .con_tab_head li.active {
    background: #ffffff;
    border: 1px solid #cddff1;
    border-bottom: 0;
    -webkit-border-radius: 3px 3px 0 0;
    -moz-border-radius: 3px 3px 0 0;
    border-radius: 3px 3px 0 0;
}

.m-choosePeople .con_tab .con_tab_head li a {
    color: var(--theme-primary);
}

.m-choosePeople .con_tab .con_tab_body {
    position: absolute;
    left: 0;
    right: 0;
    bottom: 0;
    top: 35px;
    overflow: hidden;
}

.m-choosePeople .con_tab .con_tab_body .con_tab_content {
    display: none;
}

.m-choosePeople .con_tab .con_tab_body .con_tab_content .content_box {
    position: absolute;
    left: 0;
    right: 0;
    bottom: 0;
    top: 36px;
}

.m-choosePeople .indication_box {
    height: 100%;
}

.m-choosePeople .indication_box .left .content_box {
    position: absolute;
    left: 0px;
    right: 0px;
    top: 0px;
    bottom: 0px;
    overflow: auto;
}

.m-choosePeople .indication_box .right .content_box {
    position: absolute;
    top: 40px;
    bottom: 0px;
    left: 0;
    right: 0;
    overflow: auto;
}

.m-choosePeople .footer {
    height: 40px;
    position: absolute;
    left: 0;
    right: 0;
    bottom: 0;
    line-height: 40px;
    text-align: center;
    font-size: 0;
}


/* LESS Document */

.m-showMemo .memoCon {
    width: 0;
    height: 0;
    border-top: 8px solid var(--theme-primary);
    border-left: 8px solid transparent;
    position: absolute;
    right: 0px;
    top: 0px;
}

.m-showMemo .memoRon {
    width: 10px;
    height: 10px;
    border-radius: 50%;
    position: absolute;
    right: 8px;
    top: 5px;
    z-index: 9999;
}

.m-projectMemo_box {
    position: fixed;
    width: 100%;
    height: 100%;
    top: 0;
    left: 0;
    background: rgba(70, 70, 70, 0.7);
    z-index: 9999;
}

.m-projectMemo_box .minidialog {
    background-color: rgba(140, 140, 150, 0.5);
    word-wrap: break-word;
    position: absolute;
    z-index: 99999;
    border-radius: 5px;
}

.m-projectMemo_box .minidialog .textarea {
    width: auto;
    height: auto;
    min-height: 30px;
    max-width: 200px;
    min-width: 100px;
    max-height: 200px;
    margin-left: auto;
    margin-right: auto;
    padding: 3px;
    outline: 0;
    font-size: 12px;
    line-height: 24px;
    padding: 2px;
    word-wrap: break-word;
    overflow-x: hidden;
    overflow-y: auto;
}

#showTextBox {
    z-index: 99999;
    width: auto;
    height: auto;
    min-height: 30px;
    max-width: 200px;
    min-width: 100px;
    position: absolute;
}


/*2222*/

#laydate-module-m .laydate-table tr td {
    height: 80px;
    width: 120px;
    border: 1px solid;
}

#laydate-module-m .laydate-table tr td:first-child {
    border-left: 0px solid;
}

#laydate-module-m .dateDay {
    width: 30px;
    height: 30px;
}

#laydate-module-m .dateDD {
    line-height: 20px;
    height: 20px;
}

#laydate-module-m .dataText {
    height: 60px !important;
    width: 100px !important;
}

#laydate-module-m .dateTd {
    padding: 5px 10px;
    height: 100px !important;
}

#laydate-module-m .divPic {
    width: 100px;
    height: 50px;
    position: absolute;
    top: 20px;
}

#laydate-module-m .divPic p {
    padding-bottom: 3px;
    line-height: 13px;
    font-size: 13px;
}

#laydate-module-m .m-calendar {
    height: 100%;
    position: relative;
}

#laydate-module-m .leftDate {
    height: 100%;
}

#laydate-module-m .laydate-table {
    width: 100%;
    height: calc(100% - 5px);
}

#laydate-module-m .laydate-table-cover {
    height: 40px;
    width: 100%;
    top: 40px;
    background: #d8d8d8;
    border-bottom: 1px solid #ddd;
}

#laydate-module-m .laydate-table thead {
    height: 40px;
}

#laydate-module-m .laydate-table thead tr {
    height: 40px;
}

#laydate-module-m .laydate-table thead tr th {
    height: 40px;
    width: 220px;
    padding-left: 15px;
    text-align: left;
    font-size: 16px;
    color: #a0a0a0;
}

#laydate-module-m .laydate-table tbody tr:last-child {
    border-bottom: 0px;
}

#laydate-module-m .laydate-table-cover thead {
    height: 40px;
}

#laydate-module-m .laydate-table-cover thead tr {
    height: 40px;
}

#laydate-module-m .laydate-table-cover thead tr th {
    height: 40px;
    width: 220px;
    padding-left: 15px;
    text-align: left;
    font-size: 16px;
    color: #878787;
}

#laydate-module-m .laydate-table tbody td {
    word-break: break-all;
    position: relative;
    border: 1px solid #bababa;
    background: #fff;
    vertical-align: top;
}

#laydate-module-m .laydate-table tbody td:nth-child(1) {
    background: #f4f4f4 !important;
}

#laydate-module-m .laydate-table tbody td:nth-child(7) {
    background: #f4f4f4 !important;
}

#laydate-module-m .laydate-top {
    height: 50px;
    padding: 10px 0;
    background: #fff;
}

#laydate-module-m .laydate-top a {
    color: #333;
    font-size: 20px;
    margin: 0 10px;
}

#laydate-module-m .dateDay {
    width: 60px;
    height: 20px;
}

#laydate-module-m .laydate-nothis .dateDay {
    color: #c1c1c1 !important;
}

#laydate-module-m .laydate-ym {
    width: 300px;
    text-align: center;
    margin: 0 auto;
}

#laydate-module-m .laydate_box {
    height: calc(100% - 40px);
    overflow-x: hidden;
    width: 100%;
}

#laydate-module-m .laydate_box_out {
    height: 100%;
    width: 100%;
}

#laydate-module-m .laydate-center {
    width: calc(100% - 0px);
    height: calc(100% - 52px);
    overflow-y: hidden;
    border: 1px solid #bababa;
}

#laydate-module-m .dateText p {
    line-height: 18px;
    height: 18px;
}

#laydate-module-m .dateText .memoTime {
    float: left;
    width: auto;
    text-align: left;
    padding: 0px 6px;
    background-color: #fdefd5;
    border-radius: 3px;
    margin: 3px 0 3px 0;
}

#laydate-module-m .dateText .memoEvent {
    float: left;
    width: 80px;
    text-align: left;
    cursor: pointer;
    -webkit-touch-callout: none;
    /* iOS Safari */
    -webkit-user-select: none;
    /* Chrome/Safari/Opera */
    -khtml-user-select: none;
    /* Konqueror */
    -moz-user-select: none;
    /* Firefox */
    -ms-user-select: none;
    /* Internet Explorer/Edge */
    user-select: none;
    /* Non-prefixed version, currently
			not supported by any browser */
    padding: 0 0 3px 0;
    border-bottom: 1px #c1c1c1 dotted;
    margin-bottom: 5px;
}

#laydate-module-m .moreMemo {
    position: absolute;
    top: 5px;
    right: 10px;
    width: 20px;
    height: 20px;
}

#laydate-module-m .moreMemo a {
    text-align: center;
    line-height: 20px;
    color: var(--theme-primary);
    font-size: 18px;
}

#laydate-module-m .isToday {
    box-shadow: 0px 0px 23px 0px var(--theme-primary) inset;
    border: 0px !important;
}

#laydate-module-m .isToday .moreMemo a {
    color: #fff;
}

#laydate-module-m .isToday .dateTd {
    padding: 0;
    width: 100% !important;
}

#laydate-module-m .isToday .dateDD {
    padding: 5px 10px 0 10px;
    background: var(--theme-primary);
    width: calc(100% - 20px);
}

#laydate-module-m .isToday .dateDay {
    color: #FFF;
    line-height: 20px;
}

#laydate-module-m .memoAll {
    position: absolute;
    top: 0px;
    left: 0px;
    width: 100%;
    display: none;
    box-shadow: 0px 0px 10px 0px var(--theme-primary) inset;
}

#laydate-module-m .memoAll.act {
    width: calc(100% + 30px);
    min-height: calc(100% + 15px);
    left: -30px !important;
    background: #fff;
    display: block;
    padding: 15px;
    z-index: 99999;
}

#laydate-module-m .memoAll.act p {
    height: 19px;
    line-height: 18px;
}

#laydate-module-m .memoAll.act div {
    width: 100%;
    height: 30px !important;
}

#laydate-module-m .memoAll.act .memoTime {
    float: left;
    width: auto;
    text-align: left;
    padding: 0px 6px;
    background-color: #fdefd5;
    border-radius: 3px;
    margin: 3px 0 3px 0;
}

#laydate-module-m .memoAll.act .memoEvent {
    float: left;
    width: 80px;
    text-align: left;
    cursor: pointer;
    -webkit-touch-callout: none;
    /* iOS Safari */
    -webkit-user-select: none;
    /* Chrome/Safari/Opera */
    -khtml-user-select: none;
    /* Konqueror */
    -moz-user-select: none;
    /* Firefox */
    -ms-user-select: none;
    /* Internet Explorer/Edge */
    user-select: none;
    /* Non-prefixed version, currently
				not supported by any browser */
    padding: 0 0 3px 0;
    border-bottom: 1px #c1c1c1 dotted;
    margin-bottom: 5px;
}

.date_nav_left {
    height: 40px;
    position: absolute;
    top: 4px;
    left: 10px;
    z-index: 99;
    line-height: 40px;
}

.date_nav_left li {
    margin-left: 10px;
    border-radius: 5px;
    margin-top: 5px;
    width: 60px;
    height: 30px;
    float: left;
    background: #c9c9c9;
}

.date_nav_left li.act {
    background: var(--theme-primary);
}

.date_nav_left li a {
    color: #fff;
    float: left;
    width: 100%;
    height: 100%;
    line-height: 30px;
    font-size: 14px;
    text-align: center;
}

.date_nav_right {
    position: absolute;
    height: 40px;
    top: 4px;
    right: 10px;
    z-index: 99;
    line-height: 40px;
}

.date_nav_right li {
    float: left;
    margin: 5px 10px;
    height: 30px;
}

.date_nav_right li a {
    line-height: 30px;
    color: var(--theme-primary);
    font-size: 14px;
}

.layout-head .laydate-top {
    width: 100%;
    height: 50px;
    padding: 10px 0;
}

.layout-head .laydate-top a {
    color: #333;
    font-size: 20px;
    margin: 0 20px;
}

.layout-head .laydate-ym {
    width: 100%;
    text-align: center;
    margin: 0 auto;
}

#laydate-module-d .dateDay {
    width: 20px;
    height: 20px;
}

#laydate-module-d .dataText {
    height: 60px !important;
    width: 100px !important;
}

#laydate-module-d .dateTd {
    position: relative;
    height: 35px;
}

#laydate-module-d .divPic {
    width: 100px;
    height: 50px;
    position: absolute;
    top: 20px;
}

#laydate-module-d .divPic p {
    padding-bottom: 3px;
    line-height: 13px;
    font-size: 13px;
}

#laydate-module-d .m-calendar {
    height: 100%;
}

#laydate-module-d .leftDate {
    height: 100%;
}

#laydate-module-d .laydate-table {
    width: 100%;
    height: 100%;
}

#laydate-module-d .laydate-table thead {
    height: 40px;
}

#laydate-module-d .laydate-table thead tr {
    height: 40px;
}

#laydate-module-d .laydate-table thead tr th {
    height: 40px;
    text-align: center;
    font-size: 16px;
    color: #b4b4b4;
}

#laydate-module-d .laydate-table tbody {
    height: 180px !important;
}

#laydate-module-d .laydate-table tbody tr {
    height: 35px;
}

#laydate-module-d .laydate-table tbody tr td {
    background: #fff;
    height: 35px !important;
    position: relative;
}

#laydate-module-d .laydate-table tbody tr td a {
    float: left;
    width: 100%;
    height: 35px;
    color: #333;
}

#laydate-module-d .dateDay {
    height: 20px;
}

#laydate-module-d .laydate-nothis .dateDay {
    color: #a9a9a9 !important;
}

#laydate-module-d .laydate-ym {
    /*width: 400px;*/
    text-align: center;
    margin: 0 auto;
}

#laydate-module-d .laydate-center {
    height: calc(100% - 50px);
}

#laydate-module-d .dateText p {
    width: 250px;
    line-height: 18px;
    height: 18px;
}

#laydate-module-d .dateText p {
    width: 250px;
    line-height: 18px;
    height: 18px;
}

#laydate-module-d .dateText .memoTime {
    float: left;
    width: 55px;
    text-align: left;
    padding-left: 8px;
}

#laydate-module-d .dateText .memoEvent {
    float: left;
    width: calc(100% - 63px);
    max-width: 170px;
    text-align: left;
}

#laydate-module-d .moreMemo {
    position: absolute;
    bottom: -5px;
    width: 100%;
    text-align: center;
    height: 20px;
    line-height: 20px;
    color: var(--theme-primary);
    font-size: 16px;
}

#laydate-module-d .dateDD {
    width: 26px !important;
    height: 26px !important;
    margin: 0 auto;
    line-height: 25px;
    font-size: 14px;
    text-align: center;
    border-radius: 3px;
    color: #666;
}

#laydate-module-d .isToday .dateDD .dateDay {
    background: var(--theme-primary);
    width: 26px !important;
    height: 26px !important;
    line-height: 25px;
    font-size: 14px;
    text-align: center;
    border-radius: 3px;
    color: #fff;
}

#laydate-module-d .center_left {
    width: 398px;
    height: calc(100% - 2px);
    border: 1px solid #ddd;
    background: #fff;
    float: left;
}

#laydate-module-d .center_right {
    width: calc(100% - 402px);
    height: calc(100% - 2px);
    border: 1px solid #ddd;
    border-left: 0px;
    float: right;
}

#laydate-module-d .the_date p {
    color: #5e5e5e;
    line-height: 34px;
    font-size: 20px;
}

#laydate-module-d .date_day {
    line-height: 130px !important;
    font-size: 110px !important;
}

#laydate-module-d #day_schedule .allDay,
#laydate-module-d #day_schedule .part {
    margin-bottom: 20px;
}

#laydate-module-d #day_schedule li {
    background: #fff;
    width: 100%;
    border-bottom: 1px solid #ddd;
}

#laydate-module-d #day_schedule .part li:last-child {
    border-bottom: 0px;
}

#laydate-module-d #day_schedule .info_time {
    width: 160px;
    padding: 0 20px;
    float: left;
    height: 100%;
}

#laydate-module-d #day_schedule .info_content {
    width: calc(100% - 240px);
    padding: 0 20px;
    float: left;
    height: 100%;
}

#laydate-module-d #day_schedule p {
    line-height: 30px;
    color: #555;
    font-size: 14px;
    width: calc(100% - 40px);
}


/* LESS Document */

#framwork-message {
    height: 0px;
    overflow: hidden;
    position: absolute;
    bottom: 0px;
    display: none;
    right: 10px;
    border-radius: 3px;
}

#framwork-message .m-list li>* {
    height: auto;
}

#framwork-message .msg-clear {
    position: absolute;
    right: 10px;
    bottom: 5px;
    z-index: 9999;
}

#framwork-message .msg-clear framwork-message a {
    color: #fff;
    line-height: 20px;
    float: left;
    text-align: center;
    margin-left: 10px;
    padding: 0 5px;
}

#framwork-message .framwork-message-box {
    position: relative;
    width: 100%;
    height: 100%;
    padding: 10px 10px 30px 10px;
}

#framwork-message #framwork_msg_ul li {
    padding-bottom: 5px;
}

#framwork-message #framwork_msg_ul li p {
    padding-bottom: 10px;
}

#framwork-message #framwork_msg_ul li:last-child {
    padding-bottom: 0px;
}

#framwork-message .panel-foot .foot_btns {
    float: right;
    margin-right: 20px;
    height: 26px;
    width: auto;
}

#framwork-message .panel-foot .foot_btns div {
    height: 20px;
    margin-top: 2px;
    float: left;
    padding: 0 10px;
    border-right: 1px solid #ccc;
}

#framwork-message .panel-foot .foot_btns div:last-child {
    border: 0px;
}

#framwork-message .panel-foot .foot_btns div i {
    margin: 0 5px;
}

.g-framework .framework-head {
    height: 100px;
    background-color: var(--theme-primary);
    background-image: url("../img/sasac/head-bg.png");
    background-position: left 50%!important;
    position: relative;
}

.g-framework .framework-head .logo {
    height: 57px;
    position: absolute;
    left: 10px;
    top: 50%;
    margin-top: -28px;
    z-index: 2;
}

.g-framework .framework-head .logo img {
    max-height: 57px;
}

.g-framework .framework-head .head-info {
    float: right;
    height: 50px;
    margin-top: 25px;
}

.g-framework .framework-head .head-info .head-user-info {
    width: 180px;
    height: 50px;
    float: left;
    color: #fff;
    line-height: 50px;
    font-size: 13px;
}

.g-framework .framework-head .head-info .head-user-info .row .col-5 {
    width: 60px;
}

.g-framework .framework-head .head-info .head-user-info .row .col-7 {
    width: 120px;
}

.g-framework .framework-head .head-info .head-user-info .row .col-7 .username {
    color: #fff;
    width: 100%;
    height: 20px;
    line-height: 20px;
    text-align: left;
    padding-bottom: 5px;
}

.g-framework .framework-head .head-info .head-user-info .row .col-7 .m-combo {
    background-color: transparent;
    min-width: 100%;
}

.g-framework .framework-head .head-info .head-user-info .row .col-7 .m-combo .u-input,
.g-framework .framework-head .head-info .head-user-info .row .col-7 .m-combo .mark {
    background-color: var(--theme-primary) !important;
    color: #fef8f8 !important;
    border-color: #e52e21 !important;
    border: none!important;
}

.g-framework .framework-head .head-info .head-user-info .row .col-7 .m-combo .u-input a,
.g-framework .framework-head .head-info .head-user-info .row .col-7 .m-combo .mark a {
    color: #fef8f8 !important;
}

.g-framework .framework-head .head-info .head-user-info .row .col-7 .m-combo .u-input a:hover,
.g-framework .framework-head .head-info .head-user-info .row .col-7 .m-combo .mark a:hover {
    color: #ffffff !important;
}

.g-framework .framework-head .head-info .head-user-info .row .col-7 .m-combo .m-menu {
    width: 100px;
}

.g-framework .framework-head .head-info .head-user-info .row .col-7 .m-combo .m-menu>li>a,
.g-framework .framework-head .head-info .head-user-info .row .col-7 .m-combo .m-menu ul>li>a {
    height: 30px;
    line-height: 29px;
    background-color: var(--theme-primary);
    color: #fef8f8;
    border-color: #e52e21;
}

.g-framework .framework-head .head-info .head-user-info .row .col-7 .m-combo .m-menu>li>a a,
.g-framework .framework-head .head-info .head-user-info .row .col-7 .m-combo .m-menu ul>li>a a {
    color: #fef8f8;
}

.g-framework .framework-head .head-info .head-user-info .row .col-7 .m-combo .m-menu>li>a a:hover,
.g-framework .framework-head .head-info .head-user-info .row .col-7 .m-combo .m-menu ul>li>a a:hover {
    color: #ffffff;
}

.g-framework .framework-head .head-info .head-user-info .row .col-7 .m-combo .m-menu>li>a:hover,
.g-framework .framework-head .head-info .head-user-info .row .col-7 .m-combo .m-menu ul>li>a:hover {
    background-color: #ec6258;
    color: #ffffff;
    border-color: #e84338;
}

.g-framework .framework-head .head-info .head-user-info .row .col-7 .m-combo .m-menu>li>a:hover a,
.g-framework .framework-head .head-info .head-user-info .row .col-7 .m-combo .m-menu ul>li>a:hover a {
    color: #ffffff;
}

.g-framework .framework-head .head-info .head-user-info .row .col-7 .m-combo .m-menu>li>a:hover a:hover,
.g-framework .framework-head .head-info .head-user-info .row .col-7 .m-combo .m-menu ul>li>a:hover a:hover {
    color: #ffffff;
}

.g-framework .framework-head .head-info .head-user-info .row .col-7 .m-combo .m-menu>li.active>a,
.g-framework .framework-head .head-info .head-user-info .row .col-7 .m-combo .m-menu ul>li.active>a {
    background-color: var(--theme-primary);
    color: #fef8f8;
    border-color: #e52e21;
    border-color: var(--theme-primary);
}

.g-framework .framework-head .head-info .head-user-info .row .col-7 .m-combo .m-menu>li.active>a a,
.g-framework .framework-head .head-info .head-user-info .row .col-7 .m-combo .m-menu ul>li.active>a a {
    color: #fef8f8;
}

.g-framework .framework-head .head-info .head-user-info .row .col-7 .m-combo .m-menu>li.active>a a:hover,
.g-framework .framework-head .head-info .head-user-info .row .col-7 .m-combo .m-menu ul>li.active>a a:hover {
    color: #ffffff;
}

.g-framework .framework-head .head-info .head-user-info .row .col-7 .m-combo .m-menu>li.line,
.g-framework .framework-head .head-info .head-user-info .row .col-7 .m-combo .m-menu ul>li.line {
    background-color: var(--theme-primary);
    color: #fef8f8;
    border-color: #e52e21;
}

.g-framework .framework-head .head-info .head-user-info .row .col-7 .m-combo .m-menu>li.line a,
.g-framework .framework-head .head-info .head-user-info .row .col-7 .m-combo .m-menu ul>li.line a {
    color: #fef8f8;
}

.g-framework .framework-head .head-info .head-user-info .row .col-7 .m-combo .m-menu>li.line a:hover,
.g-framework .framework-head .head-info .head-user-info .row .col-7 .m-combo .m-menu ul>li.line a:hover {
    color: #ffffff;
}

.g-framework .framework-head .head-info .head-user-info .row .col-7 .m-combo .m-menu.items>li,
.g-framework .framework-head .head-info .head-user-info .row .col-7 .m-combo .m-menu.items ul>li {
    margin-bottom: 6px;
}

.g-framework .framework-head .head-info .head-user-info .row .col-7 .m-combo .m-menu.items>li.active>a,
.g-framework .framework-head .head-info .head-user-info .row .col-7 .m-combo .m-menu.items ul>li.active>a {
    background-color: var(--theme-primary);
    color: #fef8f8;
    border-color: #e52e21;
    border-left-color: var(--theme-primary);
    color: var(--theme-primary);
}

.g-framework .framework-head .head-info .head-user-info .row .col-7 .m-combo .m-menu.items>li.active>a a,
.g-framework .framework-head .head-info .head-user-info .row .col-7 .m-combo .m-menu.items ul>li.active>a a {
    color: #fef8f8;
}

.g-framework .framework-head .head-info .head-user-info .row .col-7 .m-combo .m-menu.items>li.active>a a:hover,
.g-framework .framework-head .head-info .head-user-info .row .col-7 .m-combo .m-menu.items ul>li.active>a a:hover {
    color: #ffffff;
}

.g-framework .framework-head .head-info .head-user-info .row .col-7 .m-combo .m-menu li a {
    border-color: var(--theme-primary) !important;
}

.g-framework .framework-head .head-info .head-user-info img {
    width: 50px;
    height: 50px;
    border-radius: 50%;
    float: left;
}


/*用户信息-单位部门信息溢出显示省略号*/

.g-framework .framework-head .head-info .head-user-info .post {
    color: #fff;
    width: 100%;
    height: 20px;
    line-height: 20px;
    font-size: 16px;
    float: left;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
}

.g-framework .framework-head .head-info .head-user-info .post .u-select {
    border: none;
    background-color: #dd261a !important;
    color: #f9cdca !important;
    border-color: #bd2116 !important;
    height: 26px;
    line-height: 26px;
    padding: 0 5px;
    margin-left: -5px;
    margin-top: 5px;
}

.g-framework .framework-head .head-info .head-user-info .post .u-select a {
    color: #f9cdca !important;
}

.g-framework .framework-head .head-info .head-user-info .post .u-select a:hover {
    color: #fef8f8 !important;
}

.g-framework .framework-head .head-date {
    height: 100%;
    padding: 0 10px;
    border-left: 1px solid rgba(255, 255, 255, 0.4);
    float: left;
    margin-left: 20px;
    width: 210px;
}

.g-framework .framework-head .head-date p {
    line-height: 25px;
    color: #fff;
    font-size: 14px;
    width: 100%;
    padding-bottom: 0px;
}

.g-framework .framework-head .head-btn {
    float: left;
    height: 100%;
    padding: 0 10px;
    position: relative;
}

.g-framework .framework-head .head-btn #an_toolbar .u-btn {
    float: left;
    margin: 0 2px;
    border: 0px;
}

.g-framework .framework-head .head-btn #more_combo {
    background: none;
    border: 0px;
}

.g-framework .framework-head .head-btn .u-btn {
    height: 48px;
    background-color: transparent;
}

.g-framework .framework-head .head-btn .u-btn:hover {
    background-color: rgba(255, 255, 255, 0.1);
    box-shadow: none;
}

.g-framework .framework-head .head-btn .u-btn i {
    line-height: 50px;
    font-size: 30px !important;
    float: left;
    color: #fff;
}

.g-framework .framework-head .head-btn .u-btn .u-point {
    color: #fff;
    background-color: #FFBE00;
    border: none;
}

.g-framework .framework-head #head-btn-more {
    background: #fff;
    position: absolute;
    z-index: 9999;
    top: 50px;
    right: 20px;
    display: none;
    border-radius: 5px;
    border: 1px solid #c8412e;
}

.g-framework .framework-head #head-btn-more.act {
    display: block;
}

.g-framework .framework-head #head-btn-more li {
    height: 30px;
    padding: 0 5px;
    border-top: 1px solid #c8412e;
}

.g-framework .framework-head #head-btn-more li:first-child {
    border-top: 0px;
}

.g-framework .framework-head #head-btn-more li a span {
    color: #c8412e;
    line-height: 30px;
    font-size: 14px;
    margin-left: 10px;
}

.g-framework .framework-head #head-btn-more li a i {
    color: #c8412e;
    line-height: 30px;
    font-size: 16px;
}

@media screen and (max-width: 1367px) {
    .g-framework .framework-head {
        height: 56px!important;
    }
    .g-framework .framework-head .logo {
        height: 45px;
        margin-top: -23px;
    }
    .g-framework .framework-head .logo img {
        max-height: 45px;
    }
    .g-framework .framework-head .head-info {
        margin-top: 3px;
    }
    .g-framework .framework-head .head-info .head-user-info img {
        width: 43px;
        height: 43px;
        border-radius: 50%;
        float: left;
        margin-top: 3px;
    }
    .g-framework .framework-head .head-info .head-user-info .row .col-7 {
        padding-top: 4px;
    }
    .g-framework .framework-head .head-info .head-user-info .row .col-7 .m-combo .u-group .item,
    .g-framework .framework-head .head-info .head-user-info .row .col-7 .m-combo .u-group .m-tooltip,
    .g-framework .framework-head .head-info .head-user-info .row .col-7 .m-combo .u-group textarea.u-input,
    .g-framework .framework-head .head-info .head-user-info .row .col-7 .m-combo .u-group textarea.u-textarea,
    .g-framework .framework-head .head-info .head-user-info .row .col-7 .m-combo .u-group select.u-input {
        height: 22px;
        line-height: 20px;
    }
    .g-framework .framework-head .head-info .head-user-info .post .u-select {
        margin-top: 0;
        height: 22px;
        line-height: 22px;
    }
}

.g-framework .framework-left {
    width: 70px;
}

.g-framework .framework-left .left-box {
    height: 100%;
    width: 70px;
    background: #593502;
    position: relative;
    float: left;
}

.g-framework .framework-left .left-box .bar_nav li {
    width: 100%;
    display: block;
}

.g-framework .framework-left .left-box .bar_nav li a {
    display: block;
    width: 100%;
    padding: 8px 0;
    text-align: center;
    color: #fff;
    position: relative;
}

.g-framework .framework-left .left-box .bar_nav li a:hover {
    background-color: rgba(255, 255, 255, 0.1);
}

.g-framework .framework-left .left-box .bar_nav li a .iconfont {
    font-size: 32px;
    line-height: 35px;
}

.g-framework .framework-left .left-box .bar_nav li a .text {
    width: 100%;
    text-align: center;
    padding-top: 5px;
    line-height: 22px;
    text-indent: 0px;
    padding-bottom: 0;
}

.g-framework .framework-left .left-box .bar_nav li a .NewCall {
    width: 8px;
    height: 8px;
    position: absolute;
    right: 6px;
    top: 5px;
    border-radius: 50%;
    background: var(--theme-primary);
}

.g-framework .framework-left .navBox {
    display: none;
    width: 190px;
    height: 100%;
    background: #fff;
    float: left;
    border-right: solid 1px #ddd;
}


/*二级导航栏头部*/

.g-framework .framework-left .navBox .nodular {
    background: var(--theme-primary);
    width: 100%;
    height: 45px;
    position: relative;
}

.g-framework .framework-left .navBox .nodular p {
    font-size: 18px;
    line-height: 50px;
    font-family: 微软雅黑;
    text-align: center;
    font-weight: bold;
    color: #fff;
    padding-bottom: 0;
}

.g-framework .framework-left .navBox .nodular a {
    position: absolute;
    top: 0;
    right: 10px;
}

.g-framework .framework-left .navBox .nodular i {
    font-size: 20px;
    line-height: 45px;
    color: #fff;
}

.g-framework .framework-left .navBox .m-sidemenu {
    margin: 10px;
}

.g-framework .framework-left .navBox .m-sidemenu .NewCall {
    width: 8px;
    height: 8px;
    position: absolute;
    right: 6px;
    top: 5px;
    border-radius: 50%;
    background: var(--theme-primary);
}

.g-framework .framework-center .m-path {
    border-bottom-style: solid;
    border-bottom-width: 1px;
    background: #fff;
}

.g-framework .framework-center .frameworktabs {
    background: #f7f7f7;
}

.g-framework .framework-center .frameworktabs li {
    height: 40px;
    float: left;
}

.g-framework .framework-center .frameworktabs a {
    line-height: 40px;
    color: #414141;
    font-size: 14px;
}

.g-framework .framework-center .frameworktabs a i {
    line-height: 40px;
    color: #414141;
    font-size: 16px;
    margin: 0 10px;
}

.g-framework #an_centerfloat {
    background: #d9d9d9;
}

.g-framework #an_centerfloat li {
    background: #f1f1f1;
    margin-bottom: 10px;
    position: relative;
    min-height: 50px;
    padding: 10px;
}

.g-framework #an_centerfloat li:last-child {
    margin-bottom: 0px;
}

.g-framework #an_centerfloat div {
    color: #000;
    padding: 10px;
}

.g-framework #an_centerfloat a {
    color: #000;
}

.g-framework #an_centerfloat li p {
    padding: 0px;
    margin: 0px;
    line-height: 20px;
    font-size: 13px;
    color: #000;
    width: 100%;
    word-wrap: break-word;
    word-break: break-all;
    overflow: hidden;
}

.g-framework #an_centerfloat a i {
    position: absolute;
    right: -5px;
    top: -8px;
    line-height: 16px;
    height: 16px;
    font-size: 14px;
    width: 16px;
    border-radius: 50%;
    background: #c8412e;
    color: #fff;
    text-align: center;
}

.g-framework #an_centerfloat a div {
    padding: 0px;
    float: right;
    color: #aaa;
    font-size: 12px;
    margin-top: 5px;
}

body {
    overflow: hidden;
}

.login {
    height: 100%;
    width: 100%;
    overflow: hidden;
    display: block;
    background-image: url("../img/sasac/bg1.png");
    background-position: center 50%;
    background-size: 100% 100%;
    background-repeat: no-repeat;
}

.login .login-bg {
    height: 100%;
}

.login .login-bg .f-clear {
    height: 600px;
}

.login .login-bg .f-clear .left-bg {
    height: 100%;
    width: 50%;
}

.login .login-box {
    height: 480px;
    width: 450px;
    margin-left: 150px;
    margin-top: -230px;
    box-shadow: none;
    position: absolute;
    z-index: 5;
    left: 50%;
    top: 50%;
}

.login .login-box .layout-head {
    text-align: center;
}

.login .login-box .layout-head img {
    display: none;
}

.login .login-box .layout-center {
    background-color: #fff;
    box-shadow: 5px 5px 5px rgba(0, 0, 0, 0.2);
}

.login .login-box .layout-center .applogin {
    text-align: center!important;
    padding-top: 50px;
    position: relative;
}

.login .login-box .layout-center .applogin * {
    text-align: center!important;
}

.login .login-box .layout-center .applogin #code {
    text-align: center;
    margin: 50px 0;
}

.login .login-box .layout-center .applogin p {
    text-align: center;
    font-size: 16px;
    color: #8C8C8C;
    position: absolute;
    width: 100%;
    line-height: 50px;
    padding: 0;
    height: 50px;
    top: 0;
    left: 0;
}

.login .login-box .layout-center .applogin img {
    display: initial;
}

.login .login-box .layout-center .applogin a {
    display: block;
    line-height: 30px;
    height: 30px;
    color: #fff;
    text-align: center;
    width: 100%;
}

.login .login-box .layout-center .applogin a:hover {
    color: #F9D900;
}

.login .login-box .layout-center .u-group .mark {
    color: var(--theme-primary);
    background-color: #fff;
    padding-right: 0;
    width: 23px!important;
}

.login .login-box .layout-center .u-group .u-input {
    border-left: 0;
}

.login .login-box .layout-center .u-group .m-tooltip {
    width: auto!important;
}

.login .login-box .layout-center .m-table-form {
    width: 310px;
}

.login .login-box .layout-center .m-table-form tbody tr:nth-child(1) td .u-group .mark,
.login .login-box .layout-center .m-table-form tbody tr:nth-child(1) td .u-group .u-input {
    border-bottom: none;
}

.login .login-box .layout-center .m-table-form tbody tr:nth-child(1) td .u-group .mark {
    border-radius: 5px 0 0 0;
}

.login .login-box .layout-center .m-table-form tbody tr:nth-child(1) td .u-group .u-input {
    border-radius: 0 5px 0 0;
}

.login .login-box .layout-center .m-table-form tbody tr:nth-child(2) td .u-group .mark {
    border-radius: 0 0 0 5px;
}

.login .login-box .layout-center .m-table-form tbody tr:nth-child(2) td .u-group .u-input {
    border-radius: 0 0 5px 0;
}

.login .login-box .layout-foot {
    padding-top: 15px;
}

.login .login-box .layout-foot p {
    padding: 0;
    margin: 0;
    line-height: 18px;
    font-size: 12px;
    color: rgba(255, 255, 255, 0.5);
}

.login .login-box .layout-left,
.login .login-box .layout-right {
    width: 40px;
}

.f-de {
    height: 30px;
    line-height: 30px;
    clear: both;
}

.f-xs {
    height: 20px;
    line-height: 20px;
    clear: both;
}

.f-sm {
    height: 26px;
    line-height: 26px;
    clear: both;
}

.f-md {
    height: 35px;
    line-height: 35px;
    clear: both;
}

.f-lg {
    height: 45px;
    line-height: 45px;
    clear: both;
}

.f-xl {
    height: 50px;
    line-height: 50px;
    clear: both;
}

.f-m {
    margin: 15px;
}

.f-m-sm {
    margin: 10px;
}

.f-m-md {
    margin: 20px;
}

.f-m-lg {
    margin: 30px;
}

.f-m-xl {
    margin: 40px;
}

.f-m-xxl {
    margin: 50px;
}

.f-m-xs {
    margin: 5px;
}

.f-m-xxs {
    margin: 2px;
}

.f-m-n {
    margin: 0px !important;
}

.f-m-l {
    margin-left: 15px;
}

.f-m-l-sm {
    margin-left: 10px;
}

.f-m-l-md {
    margin-left: 20px;
}

.f-m-l-lg {
    margin-left: 30px;
}

.f-m-l-xl {
    margin-left: 40px;
}

.f-m-l-xxl {
    margin-left: 50px;
}

.f-m-l-xs {
    margin-left: 5px;
}

.f-m-l-xxs {
    margin-left: 2px;
}

.f-m-l-n {
    margin-left: 0px !important;
}

.f-m-r {
    margin-right: 15px;
}

.f-m-r-sm {
    margin-right: 10px;
}

.f-m-r-md {
    margin-right: 20px;
}

.f-m-r-lg {
    margin-right: 30px;
}

.f-m-r-xl {
    margin-right: 40px;
}

.f-m-r-xxl {
    margin-right: 50px;
}

.f-m-r-xs {
    margin-right: 5px;
}

.f-m-r-xxs {
    margin-right: 2px;
}

.f-m-r-n {
    margin-right: 0px !important;
}

.f-m-t {
    margin-top: 15px;
}

.f-m-t-sm {
    margin-top: 10px;
}

.f-m-t-md {
    margin-top: 20px;
}

.f-m-t-lg {
    margin-top: 30px;
}

.f-m-t-xl {
    margin-top: 40px;
}

.f-m-t-xxl {
    margin-top: 50px;
}

.f-m-t-xs {
    margin-top: 5px;
}

.f-m-t-xxs {
    margin-top: 2px;
}

.f-m-t-xxss {
    margin-top: 1px;
}

.f-m-t-1 {
    margin-top: -1px;
}

.f-m-t-n {
    margin-top: 0px !important;
}

.f-m-b {
    margin-bottom: 15px;
}

.f-m-b-sm {
    margin-bottom: 10px;
}

.f-m-b-md {
    margin-bottom: 20px;
}

.f-m-b-lg {
    margin-bottom: 30px;
}

.f-m-b-xl {
    margin-bottom: 40px;
}

.f-m-b-xxl {
    margin-bottom: 50px;
}

.f-m-b-xs {
    margin-bottom: 5px;
}

.f-m-b-xxs {
    margin-bottom: 2px;
}

.f-m-b-n {
    margin-bottom: 0px !important;
}

.f-p {
    padding: 15px;
}

.f-p-sm {
    padding: 10px;
}

.f-p-md {
    padding: 20px;
}

.f-p-lg {
    padding: 30px;
}

.f-p-xl {
    padding: 40px;
}

.f-p-xxl {
    padding: 50px;
}

.f-p-xs {
    padding: 5px;
}

.f-p-xxs {
    padding: 2px;
}

.f-p-n {
    padding: 0px !important;
}

.f-p-l {
    padding-left: 15px;
}

.f-p-l-sm {
    padding-left: 10px;
}

.f-p-l-md {
    padding-left: 20px;
}

.f-p-l-lg {
    padding-left: 30px;
}

.f-p-l-xl {
    padding-left: 40px;
}

.f-p-l-xxl {
    padding-left: 50px;
}

.f-p-l-xs {
    padding-left: 5px;
}

.f-p-l-xxs {
    padding-left: 2px;
}

.f-p-l-n {
    padding-left: 0px !important;
}

.f-p-r {
    padding-right: 15px;
}

.f-p-r-sm {
    padding-right: 10px;
}

.f-p-r-md {
    padding-right: 20px;
}

.f-p-r-lg {
    padding-right: 30px;
}

.f-p-r-xl {
    padding-right: 40px;
}

.f-p-r-xxl {
    padding-right: 50px;
}

.f-p-r-xs {
    padding-right: 5px;
}

.f-p-r-xxs {
    padding-right: 2px;
}

.f-p-r-n {
    padding-right: 0px !important;
}

.f-p-t {
    padding-top: 15px;
}

.f-p-t-sm {
    padding-top: 10px;
}

.f-p-t-md {
    padding-top: 20px;
}

.f-p-t-lg {
    padding-top: 30px;
}

.f-p-t-xl {
    padding-top: 40px;
}

.f-p-t-xxl {
    padding-top: 50px;
}

.f-p-t-xs {
    padding-top: 5px;
}

.f-p-t-xxs {
    padding-top: 2px;
}

.f-p-t-n {
    padding-top: 0px !important;
}

.f-p-b {
    padding-bottom: 15px;
}

.f-p-b-sm {
    padding-bottom: 10px;
}

.f-p-b-md {
    padding-bottom: 20px;
}

.f-p-b-lg {
    padding-bottom: 30px;
}

.f-p-b-xl {
    padding-bottom: 40px;
}

.f-p-b-xxl {
    padding-bottom: 50px;
}

.f-p-b-xs {
    padding-bottom: 5px;
}

.f-p-b-xxs {
    padding-bottom: 2px;
}

.f-p-b-n {
    padding-bottom: 0px !important;
}

.f-ng-p,
.u-btn.f-ng-p {
    padding-left: 15px!important;
    padding-right: 15px!important;
}

.f-ng-p-sm,
.u-btn.f-ng-p-sm {
    padding-left: 10px!important;
    padding-right: 10px!important;
}

.f-ng-p-md,
.u-btn.f-ng-p-md {
    padding-left: 20px!important;
    padding-right: 20px!important;
}

.f-ng-p-lg,
.u-btn.f-ng-p-lg {
    padding-left: 30px!important;
    padding-right: 30px!important;
}

.f-ng-p-xl,
.u-btn.f-ng-p-xl {
    padding-left: 40px!important;
    padding-right: 40px!important;
}

.f-ng-p-xxl,
.u-btn.f-ng-p-xxl {
    padding-left: 50px!important;
    padding-right: 50px!important;
}

.f-ng-p-xs,
.u-btn.f-ng-p-xs {
    padding-left: 5px!important;
    padding-right: 5px!important;
}

.f-ng-p-xxs,
.u-btn.f-ng-p-xxs {
    padding-left: 2px!important;
    padding-right: 2px!important;
}

.f-ng-p-n,
.u-btn.f-ng-p-n {
    padding-left: 0px !important;
    padding-right: 0px !important;
}

.f-ng-m {
    margin-left: 15px!important;
    margin-right: 15px!important;
}

.f-ng-m-sm {
    margin-left: 10px!important;
    margin-right: 10px!important;
}

.f-ng-m-md {
    margin-left: 20px;
    margin-right: 20px!important;
}

.f-ng-m-lg {
    margin-left: 30px!important;
    margin-right: 30px!important;
}

.f-ng-m-xl {
    margin-left: 40px!important;
    margin-right: 40px!important;
}

.f-ng-m-xxl {
    margin-left: 50px!important;
    margin-right: 50px!important;
}

.f-ng-m-xs {
    margin-left: 5px!important;
    margin-right: 5px!important;
}

.f-ng-m-xxs {
    margin-left: 2px!important;
    margin-right: 2px!important;
}

.f-ng-m-n {
    margin-left: 0px !important;
    margin-right: 0px !important;
}

.f-oh {
    overflow: hidden!important;
}

.border-box {
    -moz-box-sizing: border-box;
    -webkit-box-sizing: border-box;
    -o-box-sizing: border-box;
    -ms-box-sizing: border-box;
    box-sizing: border-box;
}

.u-formitem label span.f-color-danger {
    line-height: 17px;
}

.f-left {
    float: left!important;
    display: block;
}

.f-left.f-de,
.f-left.f-sm,
.f-left.f-md,
.f-left.f-xs,
.f-left.f-lg,
.f-left.f-xl {
    clear: inherit;
}

.f-right {
    float: right!important;
    display: block;
}

.f-right.f-de,
.f-right.f-sm,
.f-right.f-md,
.f-right.f-xs,
.f-right.f-lg,
.f-right.f-xl {
    clear: inherit;
}

.f-center {
    margin: 0 auto!important;
    float: none;
    display: block;
}

.f-inline {
    display: inline!important;
}

.f-info-l {
    text-align: left;
}

.f-info-r {
    text-align: right;
}

.f-info-t {
    vertical-align: top;
}

.f-info-b {
    vertical-align: text-bottom;
}

.f-info-c {
    text-align: center!important;
}

.f-info-m {
    vertical-align: middle;
}

.f-relative {
    position: relative;
}

.f-fixed {
    position: fixed;
}

.f-absolute {
    position: absolute;
}

.f-pre {
    overflow: hidden;
    text-align: left;
    white-space: pre-wrap;
    word-wrap: break-word;
    word-break: break-all;
}

.f-toe {
    overflow: hidden;
    word-wrap: normal;
    white-space: nowrap;
    text-overflow: ellipsis;
}

.f-hidden,
.hidden {
    display: none!important;
}

.f-show,
.show {
    display: block!important;
}

.f-remove,
.remove {
    position: fixed;
    top: -99999px;
    right: -99999px;
    z-index: -1;
}

.f-textoverflow,
.f-tof,
.m-list li a,
.m-sidemenu li a,
.g-framework .framework-head .title {
    display: block;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
}

.f-textoverflowclose,
.f-tofclose,
.m-list.newsinfo li a {
    display: block;
    overflow: visible;
    text-overflow: inherit;
    white-space: normal;
}

.f-mcp {
    cursor: pointer;
}

.f-mcd {
    cursor: default;
}

.f-mch {
    cursor: help;
}

.f-mcc {
    cursor: crosshair;
}

.f-mct {
    cursor: text;
}

.f-mcw {
    cursor: wait;
}

.f-mcm {
    cursor: move;
}

.f-mcs {
    cursor: s-resize;
}

.f-mcw {
    cursor: w-resize;
}

.f-mcse {
    cursor: se-resize;
}

.f-mcne {
    cursor: ne-resize;
}

.f-r {
    border-radius: 3px;
}

.f-r-md {
    border-radius: 5px;
}

.f-r-lg {
    border-radius: 8px;
}

.f-r-o {
    border-radius: 50%;
}

.f-t-l-n {
    border-top-left-radius: 0;
}

.f-t-r-n {
    border-top-right-radius: 0;
}

.f-b-l-n {
    border-bottom-left-radius: 0;
}

.f-b-r-n {
    border-bottom-right-radius: 0;
}

.f-b {
    border-style: solid;
    border-width: 1px;
    border-color: #ddd;
}

.f-b-l {
    border-left-style: solid;
    border-left-width: 1px;
    border-color: #ddd;
}

.f-b-r {
    border-right-style: solid;
    border-right-width: 1px;
    border-color: #ddd;
}

.f-b-t {
    border-top-style: solid;
    border-top-width: 1px;
    border-color: #ddd;
}

.f-b-b {
    border-bottom-style: solid;
    border-bottom-width: 1px;
    border-color: #ddd;
}

.f-b-n {
    border: none !important;
}

.f-b-n-t,
.f-b-t-n {
    border-top: none !important;
}

.f-b-n-r,
.f-b-r-n {
    border-right: none !important;
}

.f-b-n-b,
.f-b-b-n {
    border-bottom: none !important;
}

.f-b-n-l,
.f-b-l-n {
    border-left: none !important;
}

.f-shadow {
    box-shadow: 3px 3px 5px #eee;
}

ul,
.f-clear,
.u-group,
.u-formitem,
.m-table-form.inline .u-formitem,
.row,
.userbox,
.m-panel,
.panel-head,
.panel-foot,
.panel-h-l,
.panel-h-r,
.m-toolbar,
.m-menu,
.m-menu li,
.m-combo,
.m-pagebar,
.container-1000,
.g-box1000,
.g-box1200,
.u-btn-eject,
.u-inputitem,
.container-1200,
.dateTd,
.f-left,
.f-right,
.f-de,
xmp,
.m-list,
.m-list li,
.dateDD,
.panel-massage-box,
.g-layout,
.layout-head,
.layout-left,
.layout-right,
.layout-center,
.layout-foot,
.col-1,
.col-2,
.col-3,
.col-4,
.col-5,
.col-6,
.col-7,
.col-8,
.col-9,
.col-10,
.col-11,
.col-12 {
    zoom: 1;
}

ul:after,
.f-clear:after,
.u-group:after,
.u-formitem:after,
.m-table-form.inline .u-formitem:after,
.row:after,
.userbox:after,
.m-panel:after,
.panel-head:after,
.panel-foot:after,
.panel-h-l:after,
.panel-h-r:after,
.m-toolbar:after,
.m-menu:after,
.m-menu li:after,
.m-combo:after,
.m-pagebar:after,
.container-1000:after,
.g-box1000:after,
.g-box1200:after,
.u-btn-eject:after,
.u-inputitem:after,
.container-1200:after,
.dateTd:after,
.f-left:after,
.f-right:after,
.f-de:after,
xmp:after,
.m-list:after,
.m-list li:after,
.dateDD:after,
.panel-massage-box:after,
.g-layout:after,
.layout-head:after,
.layout-left:after,
.layout-right:after,
.layout-center:after,
.layout-foot:after,
.col-1:after,
.col-2:after,
.col-3:after,
.col-4:after,
.col-5:after,
.col-6:after,
.col-7:after,
.col-8:after,
.col-9:after,
.col-10:after,
.col-11:after,
.col-12:after {
    content: "";
    display: block;
    height: 0;
    clear: both;
    visibility: hidden;
}

.f-noselected,
.u-btn,
.iconfont,
.u-group>.mark,
*:disabled,
.disabled,
.f-disabled,
.u-switch,

/* e("input:[type=button]"), */

.u-input.u-diseditor {
    user-select: none;
    -ms-user-select: none;
    -o-user-select: none;
    moz-user-select: -moz-none;
    -moz-user-select: none;
    -khtml-user-select: none;
    -webkit-user-select: none;
}

*:disabled,
.disabled,
.f-disabled {
    cursor: not-allowed;
}

.laydate_box,
.laydate_box * {
    box-sizing: content-box !important;
}

.f-bg-gral-info {
    background-color: #d1e8f3;
    background: -moz-linear-gradient(top, #f1f8fb 0%, #d1e8f3 100%);
    background: -webkit-linear-gradient(top, #f1f8fb 0%, #d1e8f3 100%);
    background: linear-gradient(to bottom, #f1f8fb 0%, #d1e8f3 100%);
    filter: progid: DXImageTransform.Microsoft.gradient(startColorstr=#f1f8fb, endColorstr=#d1e8f3, GradientType=0);
    color: #3496c3;
    border-color: #bddeed;
    background-repeat: no-repeat;
}

.f-bg-gral-info a {
    color: #2a779b;
}

.f-bg-gral-info a:hover {
    color: #3496c3;
}

.f-bg-gral-success {
    background-color: #afd4af;
    background: -moz-linear-gradient(top, #afd4af 0%, #8ec28e 100%);
    background: -webkit-linear-gradient(top, #afd4af 0%, #8ec28e 100%);
    background: linear-gradient(to bottom, #afd4af 0%, #8ec28e 100%);
    filter: progid: DXImageTransform.Microsoft.gradient(startColorstr=#afd4af, endColorstr=#8ec28e, GradientType=0);
    color: #ffffff;
    border-color: #9fcb9f;
    background-repeat: no-repeat;
}

.f-bg-gral-success a {
    color: #ffffff;
}

.f-bg-gral-success a:hover {
    color: #ffffff;
}

.f-bg-gral-warning {
    background-color: #f8e1bf;
    background: -moz-linear-gradient(top, #fcf2e3 0%, #f8e1bf 100%);
    background: -webkit-linear-gradient(top, #fcf2e3 0%, #f8e1bf 100%);
    background: linear-gradient(to bottom, #fcf2e3 0%, #f8e1bf 100%);
    filter: progid: DXImageTransform.Microsoft.gradient(startColorstr=#fcf2e3, endColorstr=#f8e1bf, GradientType=0);
    color: #d38817;
    border-color: #f5d6a8;
    background-repeat: no-repeat;
}

.f-bg-gral-warning a {
    color: #a56a12;
}

.f-bg-gral-warning a:hover {
    color: #d38817;
}

.f-bg-gral-danger {
    background-color: #f9e9e8;
    background: -moz-linear-gradient(top, #ffffff 0%, #f9e9e8 100%);
    background: -webkit-linear-gradient(top, #ffffff 0%, #f9e9e8 100%);
    background: linear-gradient(to bottom, #ffffff 0%, #f9e9e8 100%);
    filter: progid: DXImageTransform.Microsoft.gradient(startColorstr=#ffffff, endColorstr=#f9e9e8, GradientType=0);
    color: #d04f46;
    border-color: #f4d6d4;
    background-repeat: no-repeat;
}

.f-bg-gral-danger a {
    color: #b4372e;
}

.f-bg-gral-danger a:hover {
    color: #d04f46;
}

.f-bg-gral-light {
    background-color: #f4f4f4;
    background: -moz-linear-gradient(top, #ffffff 0%, #f4f4f4 100%);
    background: -webkit-linear-gradient(top, #ffffff 0%, #f4f4f4 100%);
    background: linear-gradient(to bottom, #ffffff 0%, #f4f4f4 100%);
    filter: progid: DXImageTransform.Microsoft.gradient(startColorstr=#ffffff, endColorstr=#f4f4f4, GradientType=0);
    color: #8e8e8e;
    border-color: #e8e8e8;
    background-repeat: no-repeat;
}

.f-bg-gral-light a {
    color: #757575;
}

.f-bg-gral-light a:hover {
    color: #8e8e8e;
}

.f-bg-gral-primary {
    background-color: #f9e9e8;
    background: -moz-linear-gradient(top, #ffffff 0%, #f9e9e8 100%);
    background: -webkit-linear-gradient(top, #ffffff 0%, #f9e9e8 100%);
    background: linear-gradient(to bottom, #ffffff 0%, #f9e9e8 100%);
    filter: progid: DXImageTransform.Microsoft.gradient(startColorstr=#ffffff, endColorstr=#f9e9e8, GradientType=0);
    color: #d04f46;
    border-color: #f4d6d4;
    background-repeat: no-repeat;
}

.f-bg-gral-primary a {
    color: #b4372e;
}

.f-bg-gral-primary a:hover {
    color: #d04f46;
}

.f-bg-gral-dark {
    background-color: #9299b1;
    background: -moz-linear-gradient(top, #9299b1 0%, #747d9c 100%);
    background: -webkit-linear-gradient(top, #9299b1 0%, #747d9c 100%);
    background: linear-gradient(to bottom, #9299b1 0%, #747d9c 100%);
    filter: progid: DXImageTransform.Microsoft.gradient(startColorstr=#9299b1, endColorstr=#747d9c, GradientType=0);
    color: #ffffff;
    border-color: #838ba6;
    background-repeat: no-repeat;
}

.f-bg-gral-dark a {
    color: #ffffff;
}

.f-bg-gral-dark a:hover {
    color: #ffffff;
}

.f-bg-gral-black {
    background-color: #7192b2;
    background: -moz-linear-gradient(top, #7192b2 0%, #55789c 100%);
    background: -webkit-linear-gradient(top, #7192b2 0%, #55789c 100%);
    background: linear-gradient(to bottom, #7192b2 0%, #55789c 100%);
    filter: progid: DXImageTransform.Microsoft.gradient(startColorstr=#7192b2, endColorstr=#55789c, GradientType=0);
    color: #ffffff;
    border-color: #6185a9;
    background-repeat: no-repeat;
}

.f-bg-gral-black a {
    color: #ffffff;
}

.f-bg-gral-black a:hover {
    color: #ffffff;
}

.f-bg-grad-info {
    background-color: var(--theme-primary);
    background: -moz-linear-gradient(top, var(--theme-primary) 0%, #188cc1 100%);
    background: -webkit-linear-gradient(top, var(--theme-primary) 0%, #188cc1 100%);
    background: linear-gradient(to bottom, var(--theme-primary) 0%, #188cc1 100%);
    filter: progid: DXImageTransform.Microsoft.gradient(startColorstr=var(--theme-primary), endColorstr=#188cc1, GradientType=0);
    color: #ffffff;
    border-color: #1b9cd7;
    background-repeat: no-repeat;
}

.f-bg-grad-info a {
    color: #ffffff;
}

.f-bg-grad-info a:hover {
    color: #ffffff;
}

.f-bg-grad-success {
    background-color: #339933;
    background: -moz-linear-gradient(top, #339933 0%, #267326 100%);
    background: -webkit-linear-gradient(top, #339933 0%, #267326 100%);
    background: linear-gradient(to bottom, #339933 0%, #267326 100%);
    filter: progid: DXImageTransform.Microsoft.gradient(startColorstr=#339933, endColorstr=#267326, GradientType=0);
    color: #d9f2d9;
    border-color: #2d862d;
    background-repeat: no-repeat;
}

.f-bg-grad-success a {
    color: #ecf9ec;
}

.f-bg-grad-success a:hover {
    color: #ffffff;
}

.f-bg-grad-warning {
    background-color: #FF9900;
    background: -moz-linear-gradient(top, #FF9900 0%, #cc7a00 100%);
    background: -webkit-linear-gradient(top, #FF9900 0%, #cc7a00 100%);
    background: linear-gradient(to bottom, #FF9900 0%, #cc7a00 100%);
    filter: progid: DXImageTransform.Microsoft.gradient(startColorstr=#FF9900, endColorstr=#cc7a00, GradientType=0);
    color: #ffffff;
    border-color: #e68a00;
    background-repeat: no-repeat;
}

.f-bg-grad-warning a {
    color: #ffffff;
}

.f-bg-grad-warning a:hover {
    color: #ffffff;
}

.f-bg-grad-danger {
    background-color: var(--theme-primary);
    background: -moz-linear-gradient(top, var(--theme-primary) 0%, #dd261a 100%);
    background: -webkit-linear-gradient(top, var(--theme-primary) 0%, #dd261a 100%);
    background: linear-gradient(to bottom, var(--theme-primary) 0%, #dd261a 100%);
    filter: progid: DXImageTransform.Microsoft.gradient(startColorstr=var(--theme-primary), endColorstr=#dd261a, GradientType=0);
    color: #ffffff;
    border-color: #e6362a;
    background-repeat: no-repeat;
}

.f-bg-grad-danger a {
    color: #ffffff;
}

.f-bg-grad-danger a:hover {
    color: #ffffff;
}

.f-bg-grad-light {
    background-color: #cecece;
    background: -moz-linear-gradient(top, #e2e2e2 0%, #cecece 100%);
    background: -webkit-linear-gradient(top, #e2e2e2 0%, #cecece 100%);
    background: linear-gradient(to bottom, #e2e2e2 0%, #cecece 100%);
    filter: progid: DXImageTransform.Microsoft.gradient(startColorstr=#e2e2e2, endColorstr=#cecece, GradientType=0);
    color: #686868;
    border-color: #c1c1c1;
    background-repeat: no-repeat;
}

.f-bg-grad-light a {
    color: #4f4f4f;
}

.f-bg-grad-light a:hover {
    color: #686868;
}

.f-bg-grad-primary {
    background-color: var(--theme-primary);
    background: -moz-linear-gradient(top, var(--theme-primary) 0%, #dd261a 100%);
    background: -webkit-linear-gradient(top, var(--theme-primary) 0%, #dd261a 100%);
    background: linear-gradient(to bottom, var(--theme-primary) 0%, #dd261a 100%);
    filter: progid: DXImageTransform.Microsoft.gradient(startColorstr=var(--theme-primary), endColorstr=#dd261a, GradientType=0);
    color: #ffffff;
    border-color: #e6362a;
    background-repeat: no-repeat;
}

.f-bg-grad-primary a {
    color: #ffffff;
}

.f-bg-grad-primary a:hover {
    color: #ffffff;
}

.f-bg-grad-dark {
    background-color: #3a3f51;
    background: -moz-linear-gradient(top, #3a3f51 0%, #252833 100%);
    background: -webkit-linear-gradient(top, #3a3f51 0%, #252833 100%);
    background: linear-gradient(to bottom, #3a3f51 0%, #252833 100%);
    filter: progid: DXImageTransform.Microsoft.gradient(startColorstr=#3a3f51, endColorstr=#252833, GradientType=0);
    color: #bbc0cf;
    border-color: #2f3342;
    background-repeat: no-repeat;
}

.f-bg-grad-dark a {
    color: #caced9;
}

.f-bg-grad-dark a:hover {
    color: #e8e9ef;
}

.f-bg-grad-black {
    background-color: #263646;
    background: -moz-linear-gradient(top, #263646 0%, #141c25 100%);
    background: -webkit-linear-gradient(top, #263646 0%, #141c25 100%);
    background: linear-gradient(to bottom, #263646 0%, #141c25 100%);
    filter: progid: DXImageTransform.Microsoft.gradient(startColorstr=#263646, endColorstr=#141c25, GradientType=0);
    color: #a0b5cb;
    border-color: #1d2935;
    background-repeat: no-repeat;
}

.f-bg-grad-black a {
    color: #b0c2d4;
}

.f-bg-grad-black a:hover {
    color: #d1dce6;
}

.f-bg-white {
    background-color: #ffffff;
    color: #333;
    border-color: #d1d1d1;
}

.f-bg-white a {
    color: #808080;
}

.f-bg-white a:hover {
    color: #999999;
}

.f-bg-light-lt400 {
    background-color: #f7f7f7;
    color: #444444;
    border-color: #c9c9c9;
}

.f-bg-light-lt400 a {
    color: #777777;
}

.f-bg-light-lt400 a:hover {
    color: #919191;
}

.f-bg-light-lt300 {
    background-color: #ededed;
    color: #878787;
    border-color: #c6c6c6;
}

.f-bg-light-lt300 a {
    color: #6d6d6d;
}

.f-bg-light-lt300 a:hover {
    color: #878787;
}

.f-bg-light-lter {
    background-color: #e2e2e2;
    color: #7c7c7c;
    border-color: #cbcbcb;
}

.f-bg-light-lter a {
    color: #636363;
}

.f-bg-light-lter a:hover {
    color: #7c7c7c;
}

.f-bg-light-lt {
    background-color: #d8d8d8;
    color: #727272;
    border-color: #c1c1c1;
}

.f-bg-light-lt a {
    color: #595959;
}

.f-bg-light-lt a:hover {
    color: #727272;
}

.f-bg-light {
    background-color: #cecece;
    color: #686868;
    border-color: #b7b7b7;
}

.f-bg-light a {
    color: #4f4f4f;
}

.f-bg-light a:hover {
    color: #686868;
}

.f-bg-light-dk {
    background-color: #b7b7b7;
    color: #ffffff;
    border-color: #a5a5a5;
}

.f-bg-light-dk a {
    color: #ffffff;
}

.f-bg-light-dk a:hover {
    color: #ffffff;
}

.f-bg-light-dker {
    background-color: #a0a0a0;
    color: #ffffff;
    border-color: #8e8e8e;
}

.f-bg-light-dker a {
    color: #ffffff;
}

.f-bg-light-dker a:hover {
    color: #ffffff;
}

.f-bg-light-dk300 {
    background-color: #898989;
    color: #efefef;
    border-color: #777777;
}

.f-bg-light-dk300 a {
    color: #efefef;
}

.f-bg-light-dk300 a:hover {
    color: #ffffff;
}

.f-bg-light-dk400 {
    background-color: #727272;
    color: #d8d8d8;
    border-color: #606060;
}

.f-bg-light-dk400 a {
    color: #d8d8d8;
}

.f-bg-light-dk400 a:hover {
    color: #f2f2f2;
}

.f-bg-dark-lt400 {
    background-color: #caced9;
    color: #5a627e;
    border-color: #b0b4c6;
}

.f-bg-dark-lt400 a {
    color: #454b60;
}

.f-bg-dark-lt400 a:hover {
    color: #5a627e;
}

.f-bg-dark-lt300 {
    background-color: #989eb5;
    color: #ffffff;
    border-color: #838ba6;
}

.f-bg-dark-lt300 a {
    color: #ffffff;
}

.f-bg-dark-lt300 a:hover {
    color: #ffffff;
}

.f-bg-dark-lter {
    background-color: #656d8c;
    color: #d9dbe4;
    border-color: #565d78;
}

.f-bg-dark-lter a {
    color: #d9dbe4;
}

.f-bg-dark-lter a:hover {
    color: #f7f7f9;
}

.f-bg-dark-lt {
    background-color: #5a627e;
    color: #caced9;
    border-color: #4b5169;
}

.f-bg-dark-lt a {
    color: #caced9;
}

.f-bg-dark-lt a:hover {
    color: #e8e9ef;
}

.f-bg-dark {
    background-color: #3a3f51;
    color: #bbc0cf;
    border-color: #2f3342;
}

.f-bg-dark a {
    color: rgba(217, 219, 228, 0.7);
}

.f-bg-dark a:hover {
    color: #f7f7f9;
}

.f-bg-dark-dk {
    background-color: #2f3342;
    color: #adb2c4;
    border-color: #252833;
}

.f-bg-dark-dk a {
    color: rgba(202, 206, 217, 0.7);
}

.f-bg-dark-dk a:hover {
    color: #e8e9ef;
}

.f-bg-dark-dker {
    background-color: #1e212a;
    color: #959bb3;
    border-color: #14151c;
}

.f-bg-dark-dker a {
    color: rgba(178, 183, 200, 0.7);
}

.f-bg-dark-dker a:hover {
    color: #d0d3dd;
}

.f-bg-dark-dk300 {
    background-color: #0f1116;
    color: #8088a4;
    border-color: #050507;
}

.f-bg-dark-dk300 a {
    color: rgba(158, 164, 185, 0.7);
}

.f-bg-dark-dk300 a:hover {
    color: #bbc0cf;
}

.f-bg-dark-dk400 {
    background-color: #050507;
    color: #717a99;
    border-color: #000000;
}

.f-bg-dark-dk400 a {
    color: rgba(143, 150, 175, 0.7);
}

.f-bg-dark-dk400 a:hover {
    color: #adb2c4;
}

.f-bg-black-lt400 {
    background-color: #b0c2d4;
    color: #ffffff;
    border-color: #99b0c8;
}

.f-bg-black-lt400 a {
    color: #ffffff;
}

.f-bg-black-lt400 a:hover {
    color: #ffffff;
}

.f-bg-black-lt300 {
    background-color: #7897b6;
    color: #fcfdfe;
    border-color: #6185a9;
}

.f-bg-black-lt300 a {
    color: #fcfdfe;
}

.f-bg-black-lt300 a:hover {
    color: #ffffff;
}

.f-bg-black-lter {
    background-color: #4a6988;
    color: #c1cfdd;
    border-color: #3d5771;
}

.f-bg-black-lter a {
    color: #c1cfdd;
}

.f-bg-black-lter a:hover {
    color: #e2e9ef;
}

.f-bg-black-lt {
    background-color: #415c78;
    color: #d1dce6;
    border-color: #385067;
}

.f-bg-black-lt a {
    color: rgba(242, 245, 248, 0.7);
}

.f-bg-black-lt a:hover {
    color: #ffffff;
}

.f-bg-black {
    background-color: #263646;
    color: #a0b5cb;
    border-color: #1d2935;
}

.f-bg-black a {
    color: rgba(193, 207, 221, 0.7);
}

.f-bg-black a:hover {
    color: #e2e9ef;
}

.f-bg-black-dk {
    background-color: #1d2935;
    color: #8fa9c2;
    border-color: #141d25;
}

.f-bg-black-dk a {
    color: rgba(176, 194, 212, 0.7);
}

.f-bg-black-dk a:hover {
    color: #d1dce6;
}

.f-bg-black-dker {
    background-color: #141c25;
    color: #7f9cb9;
    border-color: #0b1014;
}

.f-bg-black-dker a {
    color: rgba(160, 181, 203, 0.7);
}

.f-bg-black-dker a:hover {
    color: #c1cfdd;
}

.f-bg-black-dk300 {
    background-color: #0b1014;
    color: #6e8fb0;
    border-color: #020304;
}

.f-bg-black-dk300 a {
    color: rgba(143, 169, 194, 0.7);
}

.f-bg-black-dk300 a:hover {
    color: #b0c2d4;
}

.f-bg-black-dk400 {
    background-color: #020304;
    color: #5e82a7;
    border-color: #000000;
}

.f-bg-black-dk400 a {
    color: rgba(127, 156, 185, 0.7);
}

.f-bg-black-dk400 a:hover {
    color: #a0b5cb;
}

.f-bg-primary-lt400 {
    background-color: #f9e9e8;
    color: #d04f46;
    border-color: #eab0ab;
}

.f-bg-primary-lt400 a {
    color: #b4372e;
}

.f-bg-primary-lt400 a:hover {
    color: #d04f46;
}

.f-bg-primary-lt300 {
    background-color: #f2c5c2;
    color: #be3329;
    border-color: #eaa19c;
}

.f-bg-primary-lt300 a {
    color: #942820;
}

.f-bg-primary-lt300 a:hover {
    color: #be3329;
}

.f-bg-primary-lter {
    background-color: #ed9f99;
    color: #ffffff;
    border-color: #e7827b;
}

.f-bg-primary-lter a {
    color: #ffffff;
}

.f-bg-primary-lter a:hover {
    color: #ffffff;
}

.f-bg-primary-lt {
    background-color: #ea766e;
    color: #ffffff;
    border-color: #e5594f;
}

.f-bg-primary-lt a {
    color: #ffffff;
}

.f-bg-primary-lt a:hover {
    color: #ffffff;
}


/*一根红线的颜色*/

.f-bg-primary {
    background-color: var(--theme-primary);
    color: #fef8f8;
    border-color: var(--theme-primary);
}

.f-bg-primary a {
    color: #fef8f8;
}

.f-bg-primary a:hover {
    color: #ffffff;
}

.f-bg-primary-dk {
    background-color: #e42518;
    color: #fad1ce;
    border-color: #c42014;
}

.f-bg-primary-dk a {
    color: #fad1ce;
}

.f-bg-primary-dk a:hover {
    color: #fffdfc;
}

.f-bg-primary-dker {
    background-color: #bd1d11;
    color: #f7a9a4;
    border-color: #9c180e;
}

.f-bg-primary-dker a {
    color: #f7a9a4;
}

.f-bg-primary-dker a:hover {
    color: #fbd5d2;
}

.f-bg-primary-dk300 {
    background-color: #94150c;
    color: #f8ada7;
    border-color: #7d110a;
}

.f-bg-primary-dk300 a {
    color: rgba(252, 217, 215, 0.7);
}

.f-bg-primary-dk300 a:hover {
    color: #ffffff;
}

.f-bg-primary-dk400 {
    background-color: #6b0e07;
    color: #f6837b;
    border-color: #530b06;
}

.f-bg-primary-dk400 a {
    color: rgba(249, 176, 171, 0.7);
}

.f-bg-primary-dk400 a:hover {
    color: #fddddb;
}

.f-bg-success-lt400 {
    background-color: #afd4af;
    color: #ffffff;
    border-color: #98c898;
}

.f-bg-success-lt400 a {
    color: #ffffff;
}

.f-bg-success-lt400 a:hover {
    color: #ffffff;
}

.f-bg-success-lt300 {
    background-color: #8dc88d;
    color: #ffffff;
    border-color: #75bd75;
}

.f-bg-success-lt300 a {
    color: #ffffff;
}

.f-bg-success-lt300 a:hover {
    color: #ffffff;
}

.f-bg-success-lter {
    background-color: #69bf69;
    color: #f8fcf8;
    border-color: #50b450;
}

.f-bg-success-lter a {
    color: #f8fcf8;
}

.f-bg-success-lter a:hover {
    color: #ffffff;
}

.f-bg-success-lt {
    background-color: #45b545;
    color: #d6f0d6;
    border-color: #3b9b3b;
}

.f-bg-success-lt a {
    color: #d6f0d6;
}

.f-bg-success-lt a:hover {
    color: #fbfefb;
}

.f-bg-success {
    background-color: #339933;
    color: #b3e6b3;
    border-color: #2a7e2a;
}

.f-bg-success a {
    color: #b3e6b3;
}

.f-bg-success a:hover {
    color: #d9f2d9;
}

.f-bg-success-dk {
    background-color: #2b882b;
    color: #c5edc5;
    border-color: #257425;
}

.f-bg-success-dk a {
    color: rgba(236, 249, 236, 0.7);
}

.f-bg-success-dk a:hover {
    color: #ffffff;
}

.f-bg-success-dker {
    background-color: #237623;
    color: #b0e8b0;
    border-color: #1d621d;
}

.f-bg-success-dker a {
    color: rgba(216, 243, 216, 0.7);
}

.f-bg-success-dker a:hover {
    color: #ffffff;
}

.f-bg-success-dk300 {
    background-color: #1c631c;
    color: #9ce39c;
    border-color: #165016;
}

.f-bg-success-dk300 a {
    color: rgba(195, 238, 195, 0.7);
}

.f-bg-success-dk300 a:hover {
    color: #ebf9eb;
}

.f-bg-success-dk400 {
    background-color: #155115;
    color: #86df86;
    border-color: #103c10;
}

.f-bg-success-dk400 a {
    color: rgba(174, 234, 174, 0.7);
}

.f-bg-success-dk400 a:hover {
    color: #d7f4d7;
}

.f-bg-info-lt400 {
    background-color: #d1e8f3;
    color: #3496c3;
    border-color: #add6e9;
}

.f-bg-info-lt400 a {
    color: #2a779b;
}

.f-bg-info-lt400 a:hover {
    color: #3496c3;
}

.f-bg-info-lt300 {
    background-color: #aad7ec;
    color: #ffffff;
    border-color: #8dc9e5;
}

.f-bg-info-lt300 a {
    color: #ffffff;
}

.f-bg-info-lt300 a:hover {
    color: #ffffff;
}

.f-bg-info-lter {
    background-color: #81c7e7;
    color: #ffffff;
    border-color: #63b9e1;
}

.f-bg-info-lter a {
    color: #ffffff;
}

.f-bg-info-lter a:hover {
    color: #ffffff;
}

.f-bg-info-lt {
    background-color: #56b7e4;
    color: #ffffff;
    border-color: #37aadf;
}

.f-bg-info-lt a {
    color: #ffffff;
}

.f-bg-info-lt a:hover {
    color: #ffffff;
}

.f-bg-info {
    background-color: var(--theme-primary);
    color: #ddf1fb;
    border-color: #1a96ce;
}

.f-bg-info a {
    color: #ddf1fb;
}

.f-bg-info a:hover {
    color: #ffffff;
}

.f-bg-info-dk {
    background-color: #1790c8;
    color: #b4e1f6;
    border-color: #1379a7;
}

.f-bg-info-dk a {
    color: #b4e1f6;
}

.f-bg-info-dk a:hover {
    color: #e1f3fc;
}

.f-bg-info-dker {
    background-color: #1073a0;
    color: #b7e4f8;
    border-color: #0e6289;
}

.f-bg-info-dker a {
    color: rgba(230, 245, 252, 0.7);
}

.f-bg-info-dker a:hover {
    color: #ffffff;
}

.f-bg-info-dk300 {
    background-color: #0b5578;
    color: #8cd4f5;
    border-color: #094560;
}

.f-bg-info-dk300 a {
    color: rgba(187, 230, 249, 0.7);
}

.f-bg-info-dk300 a:hover {
    color: #eaf7fd;
}

.f-bg-info-dk400 {
    background-color: #06384e;
    color: #61c5f3;
    border-color: #042737;
}

.f-bg-info-dk400 a {
    color: rgba(144, 214, 246, 0.7);
}

.f-bg-info-dk400 a:hover {
    color: #bfe8fa;
}

.f-bg-warning-lt400 {
    background-color: #f8e1bf;
    color: #d38817;
    border-color: #f3ce95;
}

.f-bg-warning-lt400 a {
    color: #a56a12;
}

.f-bg-warning-lt400 a:hover {
    color: #d38817;
}

.f-bg-warning-lt300 {
    background-color: #f6ce92;
    color: #ffffff;
    border-color: #f4c071;
}

.f-bg-warning-lt300 a {
    color: #ffffff;
}

.f-bg-warning-lt300 a:hover {
    color: #ffffff;
}

.f-bg-warning-lter {
    background-color: #f7bc64;
    color: #ffffff;
    border-color: #f5ad42;
}

.f-bg-warning-lter a {
    color: #ffffff;
}

.f-bg-warning-lter a:hover {
    color: #ffffff;
}

.f-bg-warning-lt {
    background-color: #faaa33;
    color: #fffdfa;
    border-color: #f99c10;
}

.f-bg-warning-lt a {
    color: #fffdfa;
}

.f-bg-warning-lt a:hover {
    color: #ffffff;
}

.f-bg-warning {
    background-color: #FF9900;
    color: #ffebcc;
    border-color: #db8400;
}

.f-bg-warning a {
    color: #ffebcc;
}

.f-bg-warning a:hover {
    color: #ffffff;
}

.f-bg-warning-dk {
    background-color: #d17d00;
    color: #ffd89e;
    border-color: #ad6800;
}

.f-bg-warning-dk a {
    color: #ffd89e;
}

.f-bg-warning-dk a:hover {
    color: #ffedd1;
}

.f-bg-warning-dker {
    background-color: #a36200;
    color: #ffdaa3;
    border-color: #8a5300;
}

.f-bg-warning-dker a {
    color: rgba(255, 239, 214, 0.7);
}

.f-bg-warning-dker a:hover {
    color: #ffffff;
}

.f-bg-warning-dk300 {
    background-color: #754600;
    color: #ffc875;
    border-color: #5c3700;
}

.f-bg-warning-dk300 a {
    color: rgba(255, 220, 168, 0.7);
}

.f-bg-warning-dk300 a:hover {
    color: #fff1db;
}

.f-bg-warning-dk400 {
    background-color: #472b00;
    color: #ffb647;
    border-color: #2e1c00;
}

.f-bg-warning-dk400 a {
    color: rgba(255, 202, 122, 0.7);
}

.f-bg-warning-dk400 a:hover {
    color: #ffdead;
}

.f-bg-danger-lt400 {
    background-color: #f9e9e8;
    color: #d04f46;
    border-color: #eab0ab;
}

.f-bg-danger-lt400 a {
    color: #b4372e;
}

.f-bg-danger-lt400 a:hover {
    color: #d04f46;
}

.f-bg-danger-lt300 {
    background-color: #f2c5c2;
    color: #be3329;
    border-color: #eaa19c;
}

.f-bg-danger-lt300 a {
    color: #942820;
}

.f-bg-danger-lt300 a:hover {
    color: #be3329;
}

.f-bg-danger-lter {
    background-color: #ed9f99;
    color: #ffffff;
    border-color: #e7827b;
}

.f-bg-danger-lter a {
    color: #ffffff;
}

.f-bg-danger-lter a:hover {
    color: #ffffff;
}

.f-bg-danger-lt {
    background-color: #ea766e;
    color: #ffffff;
    border-color: #e5594f;
}

.f-bg-danger-lt a {
    color: #ffffff;
}

.f-bg-danger-lt a:hover {
    color: #ffffff;
}

.f-bg-danger {
    background-color: var(--theme-primary);
    color: #fef8f8;
    border-color: #e52e21;
}

.f-bg-danger a {
    color: #fef8f8;
}

.f-bg-danger a:hover {
    color: #ffffff;
}

.f-bg-danger-dk {
    background-color: #e42518;
    color: #fad1ce;
    border-color: #c42014;
}

.f-bg-danger-dk a {
    color: #fad1ce;
}

.f-bg-danger-dk a:hover {
    color: #fffdfc;
}

.f-bg-danger-dker {
    background-color: #bd1d11;
    color: #f7a9a4;
    border-color: #9c180e;
}

.f-bg-danger-dker a {
    color: #f7a9a4;
}

.f-bg-danger-dker a:hover {
    color: #fbd5d2;
}

.f-bg-danger-dk300 {
    background-color: #94150c;
    color: #f8ada7;
    border-color: #7d110a;
}

.f-bg-danger-dk300 a {
    color: rgba(252, 217, 215, 0.7);
}

.f-bg-danger-dk300 a:hover {
    color: #ffffff;
}

.f-bg-danger-dk400 {
    background-color: #6b0e07;
    color: #f6837b;
    border-color: #530b06;
}

.f-bg-danger-dk400 a {
    color: rgba(249, 176, 171, 0.7);
}

.f-bg-danger-dk400 a:hover {
    color: #fddddb;
}

.f-bg-primary-a {
    background-color: #e88f3c;
    color: #fef8f3;
    border-color: #e57d1c;
}

.f-bg-primary-a a {
    color: #fef8f3;
}

.f-bg-primary-a a:hover {
    color: #ffffff;
}

.f-bg-primary-b {
    background-color: #e86a38;
    color: #fdf3ef;
    border-color: #e2531a;
}

.f-bg-primary-b a {
    color: #fdf3ef;
}

.f-bg-primary-b a:hover {
    color: #ffffff;
}

.f-bg-primary-c {
    background-color: #e62643;
    color: #fbdce1;
    border-color: #d01834;
}

.f-bg-primary-c a {
    color: #fbdce1;
}

.f-bg-primary-c a:hover {
    color: #ffffff;
}

.f-bg-primary-d {
    background-color: #d4195a;
    color: #f8c1d4;
    border-color: #b4154d;
}

.f-bg-primary-d a {
    color: #f8c1d4;
}

.f-bg-primary-d a:hover {
    color: #fdeff4;
}

.f-color-white {
    color: #ffffff;
}

.f-color-danger {
    color: var(--theme-primary);
}

.f-color-info {
    color: var(--theme-primary);
}

.f-color-success {
    color: #29a329;
}

.f-color-warning {
    color: #ff9900;
}

.f-color-light {
    color: #cecece;
}

.f-color-black {
    color: #263646;
}

.f-color-dark {
    color: #3a3f51;
}

.f-color-primary {
    color: var(--theme-primary);
}

.f-color-yellow {
    color: #FFC600;
}

.f-b-white {
    border-color: #ffffff;
}

.f-b-danger {
    border-color: var(--theme-primary);
}

.f-b-info {
    border-color: var(--theme-primary);
}

.f-b-success {
    border-color: #339933;
}

.f-b-warning {
    border-color: #FF9900;
}

.f-b-light {
    border-color: #cecece;
}

.f-b-black {
    border-color: #263646;
}

.f-b-dark {
    border-color: #3a3f51;
}

.f-b-primary {
    border-color: var(--theme-primary);
}


/*主色按钮颜色修改*/

.u-btn.primary,
.u-group .u-btn.primary {
    background-color: var(--theme-primary);
    color: #fef8f8;
    border-color: var(--theme-primary);
    color: #fff!important;
}

.u-btn.primary a,
.u-group .u-btn.primary a {
    color: #fef8f8;
}

.u-btn.primary a:hover,
.u-group .u-btn.primary a:hover {
    color: #ffffff;
}

.u-btn.primary:hover,
.u-group .u-btn.primary:hover {
    background-color: var(--theme-primary);
    color: #ffffff;
    border-color: var(--theme-primary);
    box-shadow: 0 0 8px var(--theme-primary);
}

.u-btn.primary:hover a,
.u-group .u-btn.primary:hover a {
    color: #ffffff;
}

.u-btn.primary:hover a:hover,
.u-group .u-btn.primary:hover a:hover {
    color: #ffffff;
}

.u-btn.primary:active,
.u-group .u-btn.primary:active,
.u-btn.primary.active,
.u-group .u-btn.primary.active {
    box-shadow: 0 3px 3px var(--theme-primary) inset;
}

.u-btn.primary:hover,
.u-group .u-btn.primary:hover {
    color: #ffffff;
}

.u-btn.primary>.u-point,
.u-group .u-btn.primary>.u-point {
    background-color: #fff;
    color: var(--theme-primary);
}


/*主色按钮颜色修改*/

.u-btn.info,
.u-group .u-btn.info {
    background-color: var(--theme-primary);
    color: #fef8f8;
    border-color: var(--theme-primary);
    color: #fff!important;
}

.u-btn.info a,
.u-group .u-btn.info a {
    color: #fef8f8;
}

.u-btn.info a:hover,
.u-group .u-btn.info a:hover {
    color: #ffffff;
}

.u-btn.info:hover,
.u-group .u-btn.info:hover {
    background-color: var(--theme-primary);
    color: #ffffff;
    border-color: var(--theme-primary);
    box-shadow: 0 0 8px var(--theme-primary);
}

.u-btn.info:hover a,
.u-group .u-btn.info:hover a {
    color: #ffffff;
}

.u-btn.info:hover a:hover,
.u-group .u-btn.info:hover a:hover {
    color: #ffffff;
}

.u-btn.info:active,
.u-group .u-btn.info:active,
.u-btn.info.active,
.u-group .u-btn.info.active {
    box-shadow: 0 3px 3px var(--theme-primary) inset;
}

.u-btn.info:hover,
.u-group .u-btn.info:hover {
    color: #ffffff;
}

.u-btn.info>.u-point,
.u-group .u-btn.info>.u-point {
    background-color: #fff;
    color: var(--theme-primary);
}


/*主色按钮修改*/

.u-btn.success,
.u-group .u-btn.success {
    background-color: var(--theme-primary);
    color: #fef8f8;
    border-color: var(--theme-primary);
    color: #fff!important;
}

.u-btn.success a,
.u-group .u-btn.success a {
    color: #fef8f8;
}

.u-btn.success a:hover,
.u-group .u-btn.success a:hover {
    color: #ffffff;
}

.u-btn.success:hover,
.u-group .u-btn.success:hover {
    background-color: var(--theme-primary);
    color: #ffffff;
    border-color: var(--theme-primary);
    box-shadow: 0 0 8px var(--theme-primary);
}

.u-btn.success:hover a,
.u-group .u-btn.success:hover a {
    color: #ffffff;
}

.u-btn.success:hover a:hover,
.u-group .u-btn.success:hover a:hover {
    color: #ffffff;
}

.u-btn.success:active,
.u-group .u-btn.success:active,
.u-btn.success.active,
.u-group .u-btn.success.active {
    box-shadow: 0 3px 3px var(--theme-primary) inset;
}

.u-btn.success:hover,
.u-group .u-btn.success:hover {
    color: #ffffff;
}

.u-btn.success>.u-point,
.u-group .u-btn.success>.u-point {
    background-color: #fff;
    color: var(--theme-primary);
}

.u-btn.warning,
.u-group .u-btn.warning {
    background-color: #FF9900;
    color: #ffebcc;
    border-color: #db8400;
    color: #fff!important;
}

.u-btn.warning a,
.u-group .u-btn.warning a {
    color: #ffebcc;
}

.u-btn.warning a:hover,
.u-group .u-btn.warning a:hover {
    color: #ffffff;
}

.u-btn.warning:hover,
.u-group .u-btn.warning:hover {
    background-color: #ff9f0f;
    color: #fff1db;
    border-color: #eb8d00;
    box-shadow: 0 0 8px #FF9900;
}

.u-btn.warning:hover a,
.u-group .u-btn.warning:hover a {
    color: #fff1db;
}

.u-btn.warning:hover a:hover,
.u-group .u-btn.warning:hover a:hover {
    color: #ffffff;
}

.u-btn.warning:active,
.u-group .u-btn.warning:active,
.u-btn.warning.active,
.u-group .u-btn.warning.active {
    box-shadow: 0 3px 3px #b36b00 inset;
}

.u-btn.warning:hover,
.u-group .u-btn.warning:hover {
    color: #ffffff;
}

.u-btn.warning>.u-point,
.u-group .u-btn.warning>.u-point {
    background-color: #fff;
    color: #FF9900;
}

.u-btn.danger,
.u-group .u-btn.danger {
    background-color: var(--theme-primary);
    color: #fef8f8;
    border-color: #e52e21;
    color: #fff!important;
}

.u-btn.danger a,
.u-group .u-btn.danger a {
    color: #fef8f8;
}

.u-btn.danger a:hover,
.u-group .u-btn.danger a:hover {
    color: #ffffff;
}

.u-btn.danger:hover,
.u-group .u-btn.danger:hover {
    background-color: #eb594f;
    color: #ffffff;
    border-color: #e73b2f;
    box-shadow: 0 0 8px var(--theme-primary);
}

.u-btn.danger:hover a,
.u-group .u-btn.danger:hover a {
    color: #ffffff;
}

.u-btn.danger:hover a:hover,
.u-group .u-btn.danger:hover a:hover {
    color: #ffffff;
}

.u-btn.danger:active,
.u-group .u-btn.danger:active,
.u-btn.danger.active,
.u-group .u-btn.danger.active {
    box-shadow: 0 3px 3px #c72217 inset;
}

.u-btn.danger:hover,
.u-group .u-btn.danger:hover {
    color: #ffffff;
}

.u-btn.danger>.u-point,
.u-group .u-btn.danger>.u-point {
    background-color: #fff;
    color: var(--theme-primary);
}

.u-btn.light,
.u-group .u-btn.light {
    background-color: #cecece;
    color: #686868;
    border-color: #b7b7b7;
    color: #424242 !important;
}

.u-btn.light a,
.u-group .u-btn.light a {
    color: #4f4f4f;
}

.u-btn.light a:hover,
.u-group .u-btn.light a:hover {
    color: #686868;
}

.u-btn.light:hover,
.u-group .u-btn.light:hover {
    background-color: #d6d6d6;
    color: #707070;
    border-color: #bfbfbf;
    box-shadow: 0 0 8px #cecece;
}

.u-btn.light:hover a,
.u-group .u-btn.light:hover a {
    color: #565656;
}

.u-btn.light:hover a:hover,
.u-group .u-btn.light:hover a:hover {
    color: #707070;
}

.u-btn.light:active,
.u-group .u-btn.light:active,
.u-btn.light.active,
.u-group .u-btn.light.active {
    box-shadow: 0 3px 3px #a8a8a8 inset;
}

.u-btn.light:hover,
.u-group .u-btn.light:hover {
    color: #282828;
}

.u-btn.light>.u-point,
.u-group .u-btn.light>.u-point {
    background-color: #cecece;
    color: #fff;
}

.u-btn.black,
.u-group .u-btn.black {
    background-color: #263646;
    color: #a0b5cb;
    border-color: #1d2935;
    color: #fff!important;
}

.u-btn.black a,
.u-group .u-btn.black a {
    color: rgba(193, 207, 221, 0.7);
}

.u-btn.black a:hover,
.u-group .u-btn.black a:hover {
    color: #e2e9ef;
}

.u-btn.black:hover,
.u-group .u-btn.black:hover {
    background-color: #2b3e50;
    color: #aabdd1;
    border-color: #22313f;
    box-shadow: 0 0 8px #263646;
}

.u-btn.black:hover a,
.u-group .u-btn.black:hover a {
    color: rgba(203, 215, 227, 0.7);
}

.u-btn.black:hover a:hover,
.u-group .u-btn.black:hover a:hover {
    color: #ecf0f5;
}

.u-btn.black:active,
.u-group .u-btn.black:active,
.u-btn.black.active,
.u-group .u-btn.black.active {
    box-shadow: 0 3px 3px #0b1014 inset;
}

.u-btn.black:hover,
.u-group .u-btn.black:hover {
    color: #f2f5f8;
}

.u-btn.black>.u-point,
.u-group .u-btn.black>.u-point {
    background-color: #fff;
    color: #263646;
}

.u-btn.dark,
.u-group .u-btn.dark {
    background-color: #3a3f51;
    color: #bbc0cf;
    border-color: #2f3342;
    color: #fff!important;
}

.u-btn.dark a,
.u-group .u-btn.dark a {
    color: rgba(217, 219, 228, 0.7);
}

.u-btn.dark a:hover,
.u-group .u-btn.dark a:hover {
    color: #f7f7f9;
}

.u-btn.dark:hover,
.u-group .u-btn.dark:hover {
    background-color: #40465a;
    color: #c4c8d5;
    border-color: #363a4b;
    box-shadow: 0 0 8px #3a3f51;
}

.u-btn.dark:hover a,
.u-group .u-btn.dark:hover a {
    color: rgba(226, 228, 234, 0.7);
}

.u-btn.dark:hover a:hover,
.u-group .u-btn.dark:hover a:hover {
    color: #ffffff;
}

.u-btn.dark:active,
.u-group .u-btn.dark:active,
.u-btn.dark.active,
.u-group .u-btn.dark.active {
    box-shadow: 0 3px 3px #1a1c24 inset;
}

.u-btn.dark:hover,
.u-group .u-btn.dark:hover {
    color: #ffffff;
}

.u-btn.dark>.u-point,
.u-group .u-btn.dark>.u-point {
    background-color: #fff;
    color: #3a3f51;
}

.u-btn.white,
.u-group .u-btn.white {
    background-color: #ffffff;
    color: #333;
    border-color: #d1d1d1;
    color: #737373 !important;
}

.u-btn.white a,
.u-group .u-btn.white a {
    color: #808080;
}

.u-btn.white a:hover,
.u-group .u-btn.white a:hover {
    color: #999999;
}

.u-btn.white:hover,
.u-group .u-btn.white:hover {
    background-color: #ffffff;
    color: #333;
    border-color: #d1d1d1;
    box-shadow: 0 0 8px #ffffff;
}

.u-btn.white:hover a,
.u-group .u-btn.white:hover a {
    color: #808080;
}

.u-btn.white:hover a:hover,
.u-group .u-btn.white:hover a:hover {
    color: #999999;
}

.u-btn.white:active,
.u-group .u-btn.white:active,
.u-btn.white.active,
.u-group .u-btn.white.active {
    box-shadow: 0 3px 3px #d9d9d9 inset;
}

.u-btn.white:hover,
.u-group .u-btn.white:hover {
    color: #595959;
}

.u-btn.white>.u-point,
.u-group .u-btn.white>.u-point {
    background-color: #ffffff;
    color: #fff;
}

.u-btn.texture,
.u-group .u-btn.texture {
    background-color: #f4f4f4;
    background: -moz-linear-gradient(top, #ffffff 0%, #f4f4f4 100%);
    background: -webkit-linear-gradient(top, #ffffff 0%, #f4f4f4 100%);
    background: linear-gradient(to bottom, #ffffff 0%, #f4f4f4 100%);
    filter: progid: DXImageTransform.Microsoft.gradient(startColorstr=#ffffff, endColorstr=#f4f4f4, GradientType=0);
    color: #8e8e8e;
    border-color: #e8e8e8;
    background-repeat: no-repeat;
    color: #686868 !important;
}

.u-btn.texture a,
.u-group .u-btn.texture a {
    color: #757575;
}

.u-btn.texture a:hover,
.u-group .u-btn.texture a:hover {
    color: #8e8e8e;
}

.u-btn.texture:hover,
.u-group .u-btn.texture:hover {
    background-color: #fcfcfc;
    background: -moz-linear-gradient(top, #ffffff 0%, #fcfcfc 100%);
    background: -webkit-linear-gradient(top, #ffffff 0%, #fcfcfc 100%);
    background: linear-gradient(to bottom, #ffffff 0%, #fcfcfc 100%);
    filter: progid: DXImageTransform.Microsoft.gradient(startColorstr=#ffffff, endColorstr=#fcfcfc, GradientType=0);
    color: #969696;
    border-color: #efefef;
    background-repeat: no-repeat;
    box-shadow: none;
}

.u-btn.texture:hover a,
.u-group .u-btn.texture:hover a {
    color: #7c7c7c;
}

.u-btn.texture:hover a:hover,
.u-group .u-btn.texture:hover a:hover {
    color: #969696;
}

.u-btn.texture:active,
.u-group .u-btn.texture:active,
.u-btn.texture.active,
.u-group .u-btn.texture.active {
    box-shadow: 0 3px 3px #cecece inset;
}

.u-btn.texture:hover,
.u-group .u-btn.texture:hover {
    color: #4f4f4f;
}

.u-btn.texture.primary,
.u-group .u-btn.texture.primary {
    background-color: #fef8f8;
    background: -moz-linear-gradient(top, #ffffff 0%, #fef8f8 100%);
    background: -webkit-linear-gradient(top, #ffffff 0%, #fef8f8 100%);
    background: linear-gradient(to bottom, #ffffff 0%, #fef8f8 100%);
    filter: progid: DXImageTransform.Microsoft.gradient(startColorstr=#ffffff, endColorstr=#fef8f8, GradientType=0);
    color: var(--theme-primary);
    border-color: #fce3e1;
    background-repeat: no-repeat;
    color: #c72217 !important;
}

.u-btn.texture.primary a,
.u-group .u-btn.texture.primary a {
    color: #dd261a;
}

.u-btn.texture.primary a:hover,
.u-group .u-btn.texture.primary a:hover {
    color: var(--theme-primary);
}

.u-btn.texture.primary:hover,
.u-group .u-btn.texture.primary:hover {
    background-color: #ffffff;
    background: -moz-linear-gradient(top, #ffffff 0%, #ffffff 100%);
    background: -webkit-linear-gradient(top, #ffffff 0%, #ffffff 100%);
    background: linear-gradient(to bottom, #ffffff 0%, #ffffff 100%);
    filter: progid: DXImageTransform.Microsoft.gradient(startColorstr=#ffffff, endColorstr=#ffffff, GradientType=0);
    color: #999999;
    border-color: #f2f2f2;
    background-repeat: no-repeat;
    box-shadow: none;
}

.u-btn.texture.primary:hover a,
.u-group .u-btn.texture.primary:hover a {
    color: #808080;
}

.u-btn.texture.primary:hover a:hover,
.u-group .u-btn.texture.primary:hover a:hover {
    color: #999999;
}

.u-btn.texture.primary:active,
.u-group .u-btn.texture.primary:active,
.u-btn.texture.primary.active,
.u-group .u-btn.texture.primary.active {
    box-shadow: 0 3px 3px #f6b8b3 inset;
}

.u-btn.texture.primary:hover,
.u-group .u-btn.texture.primary:hover {
    color: #991b12;
}

.u-btn.texture.info,
.u-group .u-btn.texture.info {
    background-color: #fef8f8;
    background: -moz-linear-gradient(top, #ffffff 0%, #fef8f8 100%);
    background: -webkit-linear-gradient(top, #ffffff 0%, #fef8f8 100%);
    background: linear-gradient(to bottom, #ffffff 0%, #fef8f8 100%);
    filter: progid: DXImageTransform.Microsoft.gradient(startColorstr=#ffffff, endColorstr=#fef8f8, GradientType=0);
    color: var(--theme-primary);
    border-color: #fce3e1;
    background-repeat: no-repeat;
    color: #c72217 !important;
}

.u-btn.texture.info a,
.u-group .u-btn.texture.info a {
    color: #dd261a;
}

.u-btn.texture.info a:hover,
.u-group .u-btn.texture.info a:hover {
    color: var(--theme-primary);
}

.u-btn.texture.info:hover,
.u-group .u-btn.texture.info:hover {
    background-color: #ffffff;
    background: -moz-linear-gradient(top, #ffffff 0%, #ffffff 100%);
    background: -webkit-linear-gradient(top, #ffffff 0%, #ffffff 100%);
    background: linear-gradient(to bottom, #ffffff 0%, #ffffff 100%);
    filter: progid: DXImageTransform.Microsoft.gradient(startColorstr=#ffffff, endColorstr=#ffffff, GradientType=0);
    color: #999999;
    border-color: #f2f2f2;
    background-repeat: no-repeat;
    box-shadow: none;
}

.u-btn.texture.info:hover a,
.u-group .u-btn.texture.info:hover a {
    color: #808080;
}

.u-btn.texture.info:hover a:hover,
.u-group .u-btn.texture.info:hover a:hover {
    color: #999999;
}

.u-btn.texture.info:active,
.u-group .u-btn.texture.info:active,
.u-btn.texture.info.active,
.u-group .u-btn.texture.info.active {
    box-shadow: 0 3px 3px #f6b8b3 inset;
}

.u-btn.texture.info:hover,
.u-group .u-btn.texture.info:hover {
    color: #991b12;
}

.u-btn.texture.success,
.u-group .u-btn.texture.success {
    background-color: #fef8f8;
    background: -moz-linear-gradient(top, #ffffff 0%, #fef8f8 100%);
    background: -webkit-linear-gradient(top, #ffffff 0%, #fef8f8 100%);
    background: linear-gradient(to bottom, #ffffff 0%, #fef8f8 100%);
    filter: progid: DXImageTransform.Microsoft.gradient(startColorstr=#ffffff, endColorstr=#fef8f8, GradientType=0);
    color: var(--theme-primary);
    border-color: #fce3e1;
    background-repeat: no-repeat;
    color: #c72217 !important;
}

.u-btn.texture.success a,
.u-group .u-btn.texture.success a {
    color: #dd261a;
}

.u-btn.texture.success a:hover,
.u-group .u-btn.texture.success a:hover {
    color: var(--theme-primary);
}

.u-btn.texture.success:hover,
.u-group .u-btn.texture.success:hover {
    background-color: #ffffff;
    background: -moz-linear-gradient(top, #ffffff 0%, #ffffff 100%);
    background: -webkit-linear-gradient(top, #ffffff 0%, #ffffff 100%);
    background: linear-gradient(to bottom, #ffffff 0%, #ffffff 100%);
    filter: progid: DXImageTransform.Microsoft.gradient(startColorstr=#ffffff, endColorstr=#ffffff, GradientType=0);
    color: #999999;
    border-color: #f2f2f2;
    background-repeat: no-repeat;
    box-shadow: none;
}

.u-btn.texture.success:hover a,
.u-group .u-btn.texture.success:hover a {
    color: #808080;
}

.u-btn.texture.success:hover a:hover,
.u-group .u-btn.texture.success:hover a:hover {
    color: #999999;
}

.u-btn.texture.success:active,
.u-group .u-btn.texture.success:active,
.u-btn.texture.success.active,
.u-group .u-btn.texture.success.active {
    box-shadow: 0 3px 3px #f6b8b3 inset;
}

.u-btn.texture.success:hover,
.u-group .u-btn.texture.success:hover {
    color: #991b12;
}

.u-btn.texture.warning,
.u-group .u-btn.texture.warning {
    background-color: #ffebcc;
    background: -moz-linear-gradient(top, #fffbf5 0%, #ffebcc 100%);
    background: -webkit-linear-gradient(top, #fffbf5 0%, #ffebcc 100%);
    background: linear-gradient(to bottom, #fffbf5 0%, #ffebcc 100%);
    filter: progid: DXImageTransform.Microsoft.gradient(startColorstr=#fffbf5, endColorstr=#ffebcc, GradientType=0);
    color: #ff9900;
    border-color: #ffe0b3;
    background-repeat: no-repeat;
    color: #b26b00 !important;
}

.u-btn.texture.warning a,
.u-group .u-btn.texture.warning a {
    color: #cc7a00;
}

.u-btn.texture.warning a:hover,
.u-group .u-btn.texture.warning a:hover {
    color: #ff9900;
}

.u-btn.texture.warning:hover,
.u-group .u-btn.texture.warning:hover {
    background-color: #fff1db;
    background: -moz-linear-gradient(top, #ffffff 0%, #fff1db 100%);
    background: -webkit-linear-gradient(top, #ffffff 0%, #fff1db 100%);
    background: linear-gradient(to bottom, #ffffff 0%, #fff1db 100%);
    filter: progid: DXImageTransform.Microsoft.gradient(startColorstr=#ffffff, endColorstr=#fff1db, GradientType=0);
    color: #ff9f0f;
    border-color: #ffe7c2;
    background-repeat: no-repeat;
    box-shadow: none;
}

.u-btn.texture.warning:hover a,
.u-group .u-btn.texture.warning:hover a {
    color: #db8400;
}

.u-btn.texture.warning:hover a:hover,
.u-group .u-btn.texture.warning:hover a:hover {
    color: #ff9f0f;
}

.u-btn.texture.warning:active,
.u-group .u-btn.texture.warning:active,
.u-btn.texture.warning.active,
.u-group .u-btn.texture.warning.active {
    box-shadow: 0 3px 3px #ffcc80 inset;
}

.u-btn.texture.warning:hover,
.u-group .u-btn.texture.warning:hover {
    color: #7f4d00;
}

.u-btn.texture.danger,
.u-group .u-btn.texture.danger {
    background-color: #fef8f8;
    background: -moz-linear-gradient(top, #ffffff 0%, #fef8f8 100%);
    background: -webkit-linear-gradient(top, #ffffff 0%, #fef8f8 100%);
    background: linear-gradient(to bottom, #ffffff 0%, #fef8f8 100%);
    filter: progid: DXImageTransform.Microsoft.gradient(startColorstr=#ffffff, endColorstr=#fef8f8, GradientType=0);
    color: var(--theme-primary);
    border-color: #fce3e1;
    background-repeat: no-repeat;
    color: #c72217 !important;
}

.u-btn.texture.danger a,
.u-group .u-btn.texture.danger a {
    color: #dd261a;
}

.u-btn.texture.danger a:hover,
.u-group .u-btn.texture.danger a:hover {
    color: var(--theme-primary);
}

.u-btn.texture.danger:hover,
.u-group .u-btn.texture.danger:hover {
    background-color: #ffffff;
    background: -moz-linear-gradient(top, #ffffff 0%, #ffffff 100%);
    background: -webkit-linear-gradient(top, #ffffff 0%, #ffffff 100%);
    background: linear-gradient(to bottom, #ffffff 0%, #ffffff 100%);
    filter: progid: DXImageTransform.Microsoft.gradient(startColorstr=#ffffff, endColorstr=#ffffff, GradientType=0);
    color: #999999;
    border-color: #f2f2f2;
    background-repeat: no-repeat;
    box-shadow: none;
}

.u-btn.texture.danger:hover a,
.u-group .u-btn.texture.danger:hover a {
    color: #808080;
}

.u-btn.texture.danger:hover a:hover,
.u-group .u-btn.texture.danger:hover a:hover {
    color: #999999;
}

.u-btn.texture.danger:active,
.u-group .u-btn.texture.danger:active,
.u-btn.texture.danger.active,
.u-group .u-btn.texture.danger.active {
    box-shadow: 0 3px 3px #f6b8b3 inset;
}

.u-btn.texture.danger:hover,
.u-group .u-btn.texture.danger:hover {
    color: #991b12;
}

.u-btn.texture.black,
.u-group .u-btn.texture.black {
    background-color: #7f9cb9;
    background: -moz-linear-gradient(top, #7f9cb9 0%, #5e83a7 100%);
    background: -webkit-linear-gradient(top, #7f9cb9 0%, #5e83a7 100%);
    background: linear-gradient(to bottom, #7f9cb9 0%, #5e83a7 100%);
    filter: progid: DXImageTransform.Microsoft.gradient(startColorstr=#7f9cb9, endColorstr=#5e83a7, GradientType=0);
    color: #ffffff;
    border-color: #6e8fb0;
    background-repeat: no-repeat;
    color: #fff!important;
}

.u-btn.texture.black a,
.u-group .u-btn.texture.black a {
    color: #ffffff;
}

.u-btn.texture.black a:hover,
.u-group .u-btn.texture.black a:hover {
    color: #ffffff;
}

.u-btn.texture.black:hover,
.u-group .u-btn.texture.black:hover {
    background-color: #89a4bf;
    background: -moz-linear-gradient(top, #89a4bf 0%, #688aad 100%);
    background: -webkit-linear-gradient(top, #89a4bf 0%, #688aad 100%);
    background: linear-gradient(to bottom, #89a4bf 0%, #688aad 100%);
    filter: progid: DXImageTransform.Microsoft.gradient(startColorstr=#89a4bf, endColorstr=#688aad, GradientType=0);
    color: #ffffff;
    border-color: #7897b6;
    background-repeat: no-repeat;
    box-shadow: none;
}

.u-btn.texture.black:hover a,
.u-group .u-btn.texture.black:hover a {
    color: #ffffff;
}

.u-btn.texture.black:hover a:hover,
.u-group .u-btn.texture.black:hover a:hover {
    color: #ffffff;
}

.u-btn.texture.black:active,
.u-group .u-btn.texture.black:active,
.u-btn.texture.black.active,
.u-group .u-btn.texture.black.active {
    box-shadow: 0 3px 3px #537699 inset;
}

.u-btn.texture.black:hover,
.u-group .u-btn.texture.black:hover {
    color: #ffffff;
}

.u-btn.texture.dark,
.u-group .u-btn.texture.dark {
    background-color: #9ea4b9;
    background: -moz-linear-gradient(top, #9ea4b9 0%, #8088a4 100%);
    background: -webkit-linear-gradient(top, #9ea4b9 0%, #8088a4 100%);
    background: linear-gradient(to bottom, #9ea4b9 0%, #8088a4 100%);
    filter: progid: DXImageTransform.Microsoft.gradient(startColorstr=#9ea4b9, endColorstr=#8088a4, GradientType=0);
    color: #ffffff;
    border-color: #8f96af;
    background-repeat: no-repeat;
    color: #fff!important;
}

.u-btn.texture.dark a,
.u-group .u-btn.texture.dark a {
    color: #ffffff;
}

.u-btn.texture.dark a:hover,
.u-group .u-btn.texture.dark a:hover {
    color: #ffffff;
}

.u-btn.texture.dark:hover,
.u-group .u-btn.texture.dark:hover {
    background-color: #a7acc0;
    background: -moz-linear-gradient(top, #a7acc0 0%, #8990aa 100%);
    background: -webkit-linear-gradient(top, #a7acc0 0%, #8990aa 100%);
    background: linear-gradient(to bottom, #a7acc0 0%, #8990aa 100%);
    filter: progid: DXImageTransform.Microsoft.gradient(startColorstr=#a7acc0, endColorstr=#8990aa, GradientType=0);
    color: #ffffff;
    border-color: #989eb5;
    background-repeat: no-repeat;
    box-shadow: none;
}

.u-btn.texture.dark:hover a,
.u-group .u-btn.texture.dark:hover a {
    color: #ffffff;
}

.u-btn.texture.dark:hover a:hover,
.u-group .u-btn.texture.dark:hover a:hover {
    color: #ffffff;
}

.u-btn.texture.dark:active,
.u-group .u-btn.texture.dark:active,
.u-btn.texture.dark.active,
.u-group .u-btn.texture.dark.active {
    box-shadow: 0 3px 3px #717a99 inset;
}

.u-btn.texture.dark:hover,
.u-group .u-btn.texture.dark:hover {
    color: #ffffff;
}

.u-btn.full,
.u-group .u-btn.full {
    background-color: #cecece;
    background: -moz-linear-gradient(top, #e2e2e2 0%, #cecece 100%);
    background: -webkit-linear-gradient(top, #e2e2e2 0%, #cecece 100%);
    background: linear-gradient(to bottom, #e2e2e2 0%, #cecece 100%);
    filter: progid: DXImageTransform.Microsoft.gradient(startColorstr=#e2e2e2, endColorstr=#cecece, GradientType=0);
    color: #686868;
    border-color: #c1c1c1;
    background-repeat: no-repeat;
    color: #424242 !important;
}

.u-btn.full a,
.u-group .u-btn.full a {
    color: #4f4f4f;
}

.u-btn.full a:hover,
.u-group .u-btn.full a:hover {
    color: #686868;
}

.u-btn.full:hover,
.u-group .u-btn.full:hover {
    background-color: #d6d6d6;
    background: -moz-linear-gradient(top, #eaeaea 0%, #d6d6d6 100%);
    background: -webkit-linear-gradient(top, #eaeaea 0%, #d6d6d6 100%);
    background: linear-gradient(to bottom, #eaeaea 0%, #d6d6d6 100%);
    filter: progid: DXImageTransform.Microsoft.gradient(startColorstr=#eaeaea, endColorstr=#d6d6d6, GradientType=0);
    color: #707070;
    border-color: #c9c9c9;
    background-repeat: no-repeat;
    box-shadow: none;
}

.u-btn.full:hover a,
.u-group .u-btn.full:hover a {
    color: #565656;
}

.u-btn.full:hover a:hover,
.u-group .u-btn.full:hover a:hover {
    color: #707070;
}

.u-btn.full:active,
.u-group .u-btn.full:active,
.u-btn.full.active,
.u-group .u-btn.full.active {
    box-shadow: 0 3px 3px #a8a8a8 inset;
}

.u-btn.full:hover,
.u-group .u-btn.full:hover {
    color: #282828;
}

.u-btn.full.primary,
.u-group .u-btn.full.primary {
    background-color: var(--theme-primary);
    background: -moz-linear-gradient(top, var(--theme-primary) 0%, #dd261a 100%);
    background: -webkit-linear-gradient(top, var(--theme-primary) 0%, #dd261a 100%);
    background: linear-gradient(to bottom, var(--theme-primary) 0%, #dd261a 100%);
    filter: progid: DXImageTransform.Microsoft.gradient(startColorstr=var(--theme-primary), endColorstr=#dd261a, GradientType=0);
    color: #ffffff;
    border-color: #e6362a;
    background-repeat: no-repeat;
    color: #fff!important;
}

.u-btn.full.primary a,
.u-group .u-btn.full.primary a {
    color: #ffffff;
}

.u-btn.full.primary a:hover,
.u-group .u-btn.full.primary a:hover {
    color: #ffffff;
}

.u-btn.full.primary:hover,
.u-group .u-btn.full.primary:hover {
    background-color: #eb594f;
    background: -moz-linear-gradient(top, #eb594f 0%, #e52e21 100%);
    background: -webkit-linear-gradient(top, #eb594f 0%, #e52e21 100%);
    background: linear-gradient(to bottom, #eb594f 0%, #e52e21 100%);
    filter: progid: DXImageTransform.Microsoft.gradient(startColorstr=#eb594f, endColorstr=#e52e21, GradientType=0);
    color: #ffffff;
    border-color: #e84338;
    background-repeat: no-repeat;
    box-shadow: none;
}

.u-btn.full.primary:hover a,
.u-group .u-btn.full.primary:hover a {
    color: #ffffff;
}

.u-btn.full.primary:hover a:hover,
.u-group .u-btn.full.primary:hover a:hover {
    color: #ffffff;
}

.u-btn.full.primary:active,
.u-group .u-btn.full.primary:active,
.u-btn.full.primary.active,
.u-group .u-btn.full.primary.active {
    box-shadow: 0 3px 3px #c72217 inset;
}

.u-btn.full.primary:hover,
.u-group .u-btn.full.primary:hover {
    color: #ffffff;
}

.u-btn.full.info,
.u-group .u-btn.full.info {
    background-color: var(--theme-primary);
    background: -moz-linear-gradient(top, var(--theme-primary) 0%, #dd261a 100%);
    background: -webkit-linear-gradient(top, var(--theme-primary) 0%, #dd261a 100%);
    background: linear-gradient(to bottom, var(--theme-primary) 0%, #dd261a 100%);
    filter: progid: DXImageTransform.Microsoft.gradient(startColorstr=var(--theme-primary), endColorstr=#dd261a, GradientType=0);
    color: #ffffff;
    border-color: #e6362a;
    background-repeat: no-repeat;
    color: #fff!important;
}

.u-btn.full.info a,
.u-group .u-btn.full.info a {
    color: #ffffff;
}

.u-btn.full.info a:hover,
.u-group .u-btn.full.info a:hover {
    color: #ffffff;
}

.u-btn.full.info:hover,
.u-group .u-btn.full.info:hover {
    background-color: #eb594f;
    background: -moz-linear-gradient(top, #eb594f 0%, #e52e21 100%);
    background: -webkit-linear-gradient(top, #eb594f 0%, #e52e21 100%);
    background: linear-gradient(to bottom, #eb594f 0%, #e52e21 100%);
    filter: progid: DXImageTransform.Microsoft.gradient(startColorstr=#eb594f, endColorstr=#e52e21, GradientType=0);
    color: #ffffff;
    border-color: #e84338;
    background-repeat: no-repeat;
    box-shadow: none;
}

.u-btn.full.info:hover a,
.u-group .u-btn.full.info:hover a {
    color: #ffffff;
}

.u-btn.full.info:hover a:hover,
.u-group .u-btn.full.info:hover a:hover {
    color: #ffffff;
}

.u-btn.full.info:active,
.u-group .u-btn.full.info:active,
.u-btn.full.info.active,
.u-group .u-btn.full.info.active {
    box-shadow: 0 3px 3px #c72217 inset;
}

.u-btn.full.info:hover,
.u-group .u-btn.full.info:hover {
    color: #ffffff;
}

.u-btn.full.success,
.u-group .u-btn.full.success {
    background-color: var(--theme-primary);
    background: -moz-linear-gradient(top, var(--theme-primary) 0%, #dd261a 100%);
    background: -webkit-linear-gradient(top, var(--theme-primary) 0%, #dd261a 100%);
    background: linear-gradient(to bottom, var(--theme-primary) 0%, #dd261a 100%);
    filter: progid: DXImageTransform.Microsoft.gradient(startColorstr=var(--theme-primary), endColorstr=#dd261a, GradientType=0);
    color: #ffffff;
    border-color: #e6362a;
    background-repeat: no-repeat;
    color: #fff!important;
}

.u-btn.full.success a,
.u-group .u-btn.full.success a {
    color: #ffffff;
}

.u-btn.full.success a:hover,
.u-group .u-btn.full.success a:hover {
    color: #ffffff;
}

.u-btn.full.success:hover,
.u-group .u-btn.full.success:hover {
    background-color: #eb594f;
    background: -moz-linear-gradient(top, #eb594f 0%, #e52e21 100%);
    background: -webkit-linear-gradient(top, #eb594f 0%, #e52e21 100%);
    background: linear-gradient(to bottom, #eb594f 0%, #e52e21 100%);
    filter: progid: DXImageTransform.Microsoft.gradient(startColorstr=#eb594f, endColorstr=#e52e21, GradientType=0);
    color: #ffffff;
    border-color: #e84338;
    background-repeat: no-repeat;
    box-shadow: none;
}

.u-btn.full.success:hover a,
.u-group .u-btn.full.success:hover a {
    color: #ffffff;
}

.u-btn.full.success:hover a:hover,
.u-group .u-btn.full.success:hover a:hover {
    color: #ffffff;
}

.u-btn.full.success:active,
.u-group .u-btn.full.success:active,
.u-btn.full.success.active,
.u-group .u-btn.full.success.active {
    box-shadow: 0 3px 3px #c72217 inset;
}

.u-btn.full.success:hover,
.u-group .u-btn.full.success:hover {
    color: #ffffff;
}

.u-btn.full.warning,
.u-group .u-btn.full.warning {
    background-color: #FF9900;
    background: -moz-linear-gradient(top, #FF9900 0%, #cc7a00 100%);
    background: -webkit-linear-gradient(top, #FF9900 0%, #cc7a00 100%);
    background: linear-gradient(to bottom, #FF9900 0%, #cc7a00 100%);
    filter: progid: DXImageTransform.Microsoft.gradient(startColorstr=#FF9900, endColorstr=#cc7a00, GradientType=0);
    color: #ffffff;
    border-color: #e68a00;
    background-repeat: no-repeat;
    color: #fff!important;
}

.u-btn.full.warning a,
.u-group .u-btn.full.warning a {
    color: #ffffff;
}

.u-btn.full.warning a:hover,
.u-group .u-btn.full.warning a:hover {
    color: #ffffff;
}

.u-btn.full.warning:hover,
.u-group .u-btn.full.warning:hover {
    background-color: #ff9f0f;
    background: -moz-linear-gradient(top, #ff9f0f 0%, #db8400 100%);
    background: -webkit-linear-gradient(top, #ff9f0f 0%, #db8400 100%);
    background: linear-gradient(to bottom, #ff9f0f 0%, #db8400 100%);
    filter: progid: DXImageTransform.Microsoft.gradient(startColorstr=#ff9f0f, endColorstr=#db8400, GradientType=0);
    color: #ffffff;
    border-color: #f59300;
    background-repeat: no-repeat;
    box-shadow: none;
}

.u-btn.full.warning:hover a,
.u-group .u-btn.full.warning:hover a {
    color: #ffffff;
}

.u-btn.full.warning:hover a:hover,
.u-group .u-btn.full.warning:hover a:hover {
    color: #ffffff;
}

.u-btn.full.warning:active,
.u-group .u-btn.full.warning:active,
.u-btn.full.warning.active,
.u-group .u-btn.full.warning.active {
    box-shadow: 0 3px 3px #b36b00 inset;
}

.u-btn.full.warning:hover,
.u-group .u-btn.full.warning:hover {
    color: #ffffff;
}

.u-btn.full.danger,
.u-group .u-btn.full.danger {
    background-color: var(--theme-primary);
    background: -moz-linear-gradient(top, var(--theme-primary) 0%, #dd261a 100%);
    background: -webkit-linear-gradient(top, var(--theme-primary) 0%, #dd261a 100%);
    background: linear-gradient(to bottom, var(--theme-primary) 0%, #dd261a 100%);
    filter: progid: DXImageTransform.Microsoft.gradient(startColorstr=var(--theme-primary), endColorstr=#dd261a, GradientType=0);
    color: #ffffff;
    border-color: #e6362a;
    background-repeat: no-repeat;
    color: #fff!important;
}

.u-btn.full.danger a,
.u-group .u-btn.full.danger a {
    color: #ffffff;
}

.u-btn.full.danger a:hover,
.u-group .u-btn.full.danger a:hover {
    color: #ffffff;
}

.u-btn.full.danger:hover,
.u-group .u-btn.full.danger:hover {
    background-color: #eb594f;
    background: -moz-linear-gradient(top, #eb594f 0%, #e52e21 100%);
    background: -webkit-linear-gradient(top, #eb594f 0%, #e52e21 100%);
    background: linear-gradient(to bottom, #eb594f 0%, #e52e21 100%);
    filter: progid: DXImageTransform.Microsoft.gradient(startColorstr=#eb594f, endColorstr=#e52e21, GradientType=0);
    color: #ffffff;
    border-color: #e84338;
    background-repeat: no-repeat;
    box-shadow: none;
}

.u-btn.full.danger:hover a,
.u-group .u-btn.full.danger:hover a {
    color: #ffffff;
}

.u-btn.full.danger:hover a:hover,
.u-group .u-btn.full.danger:hover a:hover {
    color: #ffffff;
}

.u-btn.full.danger:active,
.u-group .u-btn.full.danger:active,
.u-btn.full.danger.active,
.u-group .u-btn.full.danger.active {
    box-shadow: 0 3px 3px #c72217 inset;
}

.u-btn.full.danger:hover,
.u-group .u-btn.full.danger:hover {
    color: #ffffff;
}

.u-btn.full.black,
.u-group .u-btn.full.black {
    background-color: #263646;
    background: -moz-linear-gradient(top, #263646 0%, #141c25 100%);
    background: -webkit-linear-gradient(top, #263646 0%, #141c25 100%);
    background: linear-gradient(to bottom, #263646 0%, #141c25 100%);
    filter: progid: DXImageTransform.Microsoft.gradient(startColorstr=#263646, endColorstr=#141c25, GradientType=0);
    color: #a0b5cb;
    border-color: #1d2935;
    background-repeat: no-repeat;
    color: #fff!important;
}

.u-btn.full.black a,
.u-group .u-btn.full.black a {
    color: #b0c2d4;
}

.u-btn.full.black a:hover,
.u-group .u-btn.full.black a:hover {
    color: #d1dce6;
}

.u-btn.full.black:hover,
.u-group .u-btn.full.black:hover {
    background-color: #2b3e50;
    background: -moz-linear-gradient(top, #2b3e50 0%, #19242f 100%);
    background: -webkit-linear-gradient(top, #2b3e50 0%, #19242f 100%);
    background: linear-gradient(to bottom, #2b3e50 0%, #19242f 100%);
    filter: progid: DXImageTransform.Microsoft.gradient(startColorstr=#2b3e50, endColorstr=#19242f, GradientType=0);
    color: #aabdd1;
    border-color: #22313f;
    background-repeat: no-repeat;
    box-shadow: none;
}

.u-btn.full.black:hover a,
.u-group .u-btn.full.black:hover a {
    color: #bacada;
}

.u-btn.full.black:hover a:hover,
.u-group .u-btn.full.black:hover a:hover {
    color: #dbe3ec;
}

.u-btn.full.black:active,
.u-group .u-btn.full.black:active,
.u-btn.full.black.active,
.u-group .u-btn.full.black.active {
    box-shadow: 0 3px 3px #0b1014 inset;
}

.u-btn.full.black:hover,
.u-group .u-btn.full.black:hover {
    color: #f2f5f8;
}

.u-btn.full.dark,
.u-group .u-btn.full.dark {
    background-color: #3a3f51;
    background: -moz-linear-gradient(top, #3a3f51 0%, #252833 100%);
    background: -webkit-linear-gradient(top, #3a3f51 0%, #252833 100%);
    background: linear-gradient(to bottom, #3a3f51 0%, #252833 100%);
    filter: progid: DXImageTransform.Microsoft.gradient(startColorstr=#3a3f51, endColorstr=#252833, GradientType=0);
    color: #bbc0cf;
    border-color: #2f3342;
    background-repeat: no-repeat;
    color: #fff!important;
}

.u-btn.full.dark a,
.u-group .u-btn.full.dark a {
    color: #caced9;
}

.u-btn.full.dark a:hover,
.u-group .u-btn.full.dark a:hover {
    color: #e8e9ef;
}

.u-btn.full.dark:hover,
.u-group .u-btn.full.dark:hover {
    background-color: #40465a;
    background: -moz-linear-gradient(top, #40465a 0%, #2b2f3c 100%);
    background: -webkit-linear-gradient(top, #40465a 0%, #2b2f3c 100%);
    background: linear-gradient(to bottom, #40465a 0%, #2b2f3c 100%);
    filter: progid: DXImageTransform.Microsoft.gradient(startColorstr=#40465a, endColorstr=#2b2f3c, GradientType=0);
    color: #c4c8d5;
    border-color: #363a4b;
    background-repeat: no-repeat;
    box-shadow: none;
}

.u-btn.full.dark:hover a,
.u-group .u-btn.full.dark:hover a {
    color: #d3d6e0;
}

.u-btn.full.dark:hover a:hover,
.u-group .u-btn.full.dark:hover a:hover {
    color: #f1f2f5;
}

.u-btn.full.dark:active,
.u-group .u-btn.full.dark:active,
.u-btn.full.dark.active,
.u-group .u-btn.full.dark.active {
    box-shadow: 0 3px 3px #1a1c24 inset;
}

.u-btn.full.dark:hover,
.u-group .u-btn.full.dark:hover {
    color: #ffffff;
}

.u-group.primary .mark {
    background-color: var(--theme-primary);
}

.u-group.primary .u-btn {
    background-color: var(--theme-primary);
    color: #fef8f8;
    border-color: #e52e21;
    color: #fff!important;
}

.u-group.primary .u-btn a {
    color: #fef8f8;
}

.u-group.primary .u-btn a:hover {
    color: #ffffff;
}

.u-group.primary .u-btn:hover {
    background-color: #eb594f;
    color: #ffffff;
    border-color: #e73b2f;
    box-shadow: 0 0 8px var(--theme-primary);
}

.u-group.primary .u-btn:hover a {
    color: #ffffff;
}

.u-group.primary .u-btn:hover a:hover {
    color: #ffffff;
}

.u-group.primary .u-btn:active,
.u-group.primary .u-btn.active {
    box-shadow: 0 3px 3px #c72217 inset;
}

.u-group.primary .u-btn:hover {
    color: #ffffff;
}

.u-group.primary .u-btn>.u-point {
    background-color: #fff;
    color: var(--theme-primary);
}

.u-group.primary .u-btn.active {
    background-color: var(--theme-primary);
    color: #fef8f8;
    border-color: #e52e21;
    border-color: var(--theme-primary) !important;
    color: #fff!important;
    box-shadow: none!important;
}

.u-group.primary .u-btn.active a {
    color: #fef8f8;
}

.u-group.primary .u-btn.active a:hover {
    color: #ffffff;
}

.u-group.primary .u-btn.active:hover {
    background-color: #eb594f;
    color: #ffffff;
    border-color: #e73b2f;
    box-shadow: 0 0 8px var(--theme-primary);
}

.u-group.primary .u-btn.active:hover a {
    color: #ffffff;
}

.u-group.primary .u-btn.active:hover a:hover {
    color: #ffffff;
}

.u-group.primary .u-btn.active:active,
.u-group.primary .u-btn.active.active {
    box-shadow: 0 3px 3px #c72217 inset;
}

.u-group.primary .u-btn.active:hover {
    color: #ffffff;
}

.u-group.primary .u-btn.active>.u-point {
    background-color: #fff;
    color: var(--theme-primary);
}

.u-group.primary .item.u-btn,
.u-group.primary .m-tooltip.u-btn,
.u-group.primary .item.u-input,
.u-group.primary .m-tooltip.u-input,
.u-group.primary .item.u-checkbox,
.u-group.primary .m-tooltip.u-checkbox,
.u-group.primary .item.mark,
.u-group.primary .m-tooltip.mark,
.u-group.primary .item.u-select,
.u-group.primary .m-tooltip.u-select,
.u-group.primary .item.u-textarea,
.u-group.primary .m-tooltip.u-textarea,
.u-group.primary .item.u-switch,
.u-group.primary .m-tooltip.u-switch,
.u-group.primary .item .u-btn,
.u-group.primary .m-tooltip .u-btn,
.u-group.primary .item .u-input,
.u-group.primary .m-tooltip .u-input,
.u-group.primary .item .u-checkbox,
.u-group.primary .m-tooltip .u-checkbox,
.u-group.primary .item .mark,
.u-group.primary .m-tooltip .mark,
.u-group.primary .item .u-select,
.u-group.primary .m-tooltip .u-select,
.u-group.primary .item .u-textarea,
.u-group.primary .m-tooltip .u-textarea,
.u-group.primary .item .u-switch,
.u-group.primary .m-tooltip .u-switch,
.u-group.primary .item.iconfont,
.u-group.primary .m-tooltip.iconfont {
    border-color: var(--theme-primary);
}


/*新增-主页面表格背景色、边框等*/

.ui-jqgrid .ui-jqgrid-labels th,
.ui-jqgrid .ui-jqgrid-labels th div.ui-jqgrid-sortable,
.ui-jqgrid .ui-jqgrid-hdiv,
#chartsTitile,
.m-table.head_table th,
.layui-table thead tr th {
    background-color: var(--theme-primary) !important;
}

.ui-jqgrid .ui-jqgrid-labels th div.ui-jqgrid-sortable,
#jqgh_jqGrid_table_rn,
#chartsTitile,
.m-table.head_table th,
.layui-table thead tr th {
    color: #fff !important;
    font-size: 13px;
    font-weight: 400;
}

.ui-jqgrid div.ui-jqgrid-bdiv,
.ui-jqgrid .ui-jqgrid-pager {
    border: 1px solid #eee !important
}

.ui-jqgrid .ui-jqgrid-hdiv,
#chartsTitile {
    border-color: var(--theme-primary) !important;
    border-radius: 4px 4px 0 0
}

.layui-table thead tr th {
    background-color: var(--theme-primary);
    color: #fff;
    border-right: 1px solid #c0c0c0 !important;
}

.layui-table-view .layui-table td {
    border-right: 1px solid #eee;
}

.ui-jqgrid .ui-jqgrid-labels th {
    border-color: #c0c0c0 !important
}

th[aria-selected=true] {
    background-image: none !important
}


/*表格内边框*/

.ui-jqgrid tr.ui-row-ltr td,
.ui-jqgrid tr.ui-row-rtl td {
    border-right: 1px solid #eee !important;
}


/*主页面表头工具*/

form#searchForm .u-input,
form#searchForm .u-btn,
.m-toolbar .u-input {
    border-color: var(--theme-primary) !important
}

form#searchForm .u-btn {
    background-color: var(--theme-primary) !important;
    color: #fff !important;
}


/*图标之装饰色*/

.module-head>.label {
    background-color: var(--theme-primary) !important
}


/*树状图之选定样式*/

ul.ztree li a.curSelectedNode,
.lay-tree-box div.lay-tree-title.active {
    background-color: rgba(167, 122, 62, 0.1);
    border: 1px var(--theme-primary) solid;
}

.m-table tbody td:hover,
.lay-tree-box div.lay-tree-title:hover {
    background-color: rgba(167, 122, 62, 0.1) !important
}


/*layui输入框*/

.lay-data-table .lay-form-select .layui-select-title input.layui-hide-input,
.lay-data-table .lay-input-box input.layui-input {
    border-color: var(--theme-primary) !important;
}


/*layui组件按钮统一修改*/

.layui-layer-btn .layui-layer-btn0,
.layui-btn-submit {
    background-color: var(--theme-primary) !important;
    color: #fff !important;
    border-color: var(--theme-primary) !important
}


/*layui页码*/

div.layui-laypage input:focus,
div.layui-laypage select:focus,
.layui-table-page div.layui-laypage select:focus {
    border-color: var(--theme-primary) !important
}


/*layui表单元素*/

.layui-form-radio:hover *,
.layui-form-radioed,
.layui-form-radioed>i {
    color: var(--theme-primary) !important;
}

div .layui-form-checked[lay-skin=primary] i {
    border-color: var(--theme-primary)!important;
    background-color: var(--theme-primary);
    color: #fff;
}


/*办理界面按钮宽度*/

button#submitTask {
    max-width: 40% !important;
}

button.u-btn.white[onclick="reject()"] {
    background: var(--theme-primary) !important;
}