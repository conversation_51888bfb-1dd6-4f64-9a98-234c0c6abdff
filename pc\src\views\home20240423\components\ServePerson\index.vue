<template>
	<div
		class="page flex-col"
		:style="{
			transform: `scale(${scale})`
		}"
	>
		<div class="group_1">
			<div class="block_1 flex-col">
				<div class="box_3 card flex-row">
					<headAvator
						:own-id="userInfo.id"
						:user-id="userInfo.id"
						class="section_2 flex-col"
						@click="handleButton('draw')"
					/>
					<div class="section_3 flex-col justify-between">
						<div class="box_4 flex-row">
							<span class="text_4">{{ detail.xm }}</span>
							<div class="text-wrapper_1">
								<span class="text_5">性别&nbsp;-</span>
								<span class="text_6">{{ detail.xbdm || detail.xb }}</span>
							</div>
							<div class="text-wrapper_2">
								<span class="text_7">民族&nbsp;-</span>
								<span class="text_8">{{ detail.mzdm || detail.mzmc }}</span>
							</div>
							<div class="text-wrapper_3">
								<span class="text_9">籍贯&nbsp;-</span>
								<span class="text_10">{{ detail.jgdm || detail.jg }}</span>
							</div>
						</div>
						<div class="box_5 flex-col">
							<div class="block_box flex-row">
								<div class="text-wrapper">
									<span class="text_1">出生日期：</span>
									<span class="text_2">{{ detail.csrq }}</span>
								</div>
								<div class="text-wrapper">
									<span class="text_1">毕业院校：</span>
									<span class="text_2">{{ detail.byyx }}</span>
								</div>
								<div class="text-wrapper">
									<span class="text_1">专业：</span>
									<span class="text_2">{{ detail.zymc }}</span>
								</div>
								<div class="text-wrapper">
									<span class="text_1">学历：</span>
									<span class="text_2">{{ detail.zgxlmc || detail.xlmc }}</span>
								</div>
								<div class="text-wrapper">
									<span class="text_1">职称：</span>
									<span class="text_2">{{ detail.zc }}</span>
								</div>
							</div>
							<div class="block_box flex-row">
								<div class="text-wrapper">
									<span class="text_1">入校时间：</span>
									<span class="text_2">{{ detail.cjgzrq || detail.rxsj }}</span>
								</div>
								<div class="text-wrapper">
									<span class="text_1">政治面貌：</span>
									<span class="text_2">{{ detail.zzmmdm || detail.zzmm }}</span>
								</div>
								<div class="text-wrapper">
									<span class="text_1">所在科室（教研所）：</span>
									<span class="text_2">{{ detail.szks }}</span>
								</div>
								<div class="text-wrapper">
									<span class="text_1">学位：</span>
									<span class="text_2">{{ detail.zgxwmc || detail.xwmc }}</span>
								</div>
								<div class="text-wrapper">
									<span class="text_1">健康状况：</span>
									<span class="text_2">{{ detail.jkzkdm || detail.jkzk }}</span>
								</div>
							</div>
							<div class="block_box flex-row">
								<div class="text-wrapper">
									<span class="text_1">所属部门：</span>
									<span class="text_2">{{ detail.szdw || detail.ssbmmc }}</span>
								</div>
								<div class="text-wrapper">
									<span class="text_1">专业技术职务：</span>
									<span class="text_2">{{ detail.zyjszwmc || detail.zyjszw }}</span>
								</div>
							</div>
						</div>
					</div>
				</div>
				<div class="box_6 flex-row justify-between">
					<div class="box_7 card flex-col">
						<div class="text-wrapper_18 flex-row"><span class="text_39">科研成果</span></div>
						<div class="block_5 flex-row justify-between">
							<div class="section_4 flex-col justify-between">
								<div class="text-wrapper_19 flex-row justify-between">
									<span class="text_40">{{ detail.kyxmsl || 0 }}</span>
									<span class="text_41">个</span>
								</div>
								<span class="text_42">科研项目</span>
							</div>
							<img
								class="image_2"
								referrerpolicy="no-referrer"
								src="./assets/img/SketchPng57261bc5fe2510cf7f535c61708a2d4ea2a7598f833cafa7775a7f12d5bf3820.png"
							/>
							<div class="section_5 flex-col justify-between">
								<div class="text-wrapper_20 flex-row justify-between">
									<span class="text_43">{{ detail.zzsl || 0 }}</span>
									<span class="text_44">部</span>
								</div>
								<span class="text_45">专著</span>
							</div>
							<img
								class="image_3"
								referrerpolicy="no-referrer"
								src="./assets/img/SketchPng57261bc5fe2510cf7f535c61708a2d4ea2a7598f833cafa7775a7f12d5bf3820.png"
							/>
							<div class="section_6 flex-col justify-between">
								<div class="text-wrapper_21 flex-row justify-between">
									<span class="text_46">{{ detail.lwsl || 0 }}</span>
									<span class="text_47">篇</span>
								</div>
								<span class="text_48">论文</span>
							</div>
							<img
								class="image_4"
								referrerpolicy="no-referrer"
								src="./assets/img/SketchPng57261bc5fe2510cf7f535c61708a2d4ea2a7598f833cafa7775a7f12d5bf3820.png"
							/>
							<div class="section_7 flex-col justify-between">
								<div class="text-wrapper_22 flex-row justify-between">
									<span class="text_49">{{ detail.zlsl || 0 }}</span>
									<span class="text_50">项</span>
								</div>
								<span class="text_51">专利</span>
							</div>
							<img
								class="image_5"
								referrerpolicy="no-referrer"
								src="./assets/img/SketchPng57261bc5fe2510cf7f535c61708a2d4ea2a7598f833cafa7775a7f12d5bf3820.png"
							/>
							<div class="section_8 flex-col justify-between">
								<div class="text-wrapper_23 flex-row justify-between">
									<span class="text_52">{{ detail.hjsl || 0 }}</span>
									<span class="text_53">个</span>
								</div>
								<span class="text_54">获奖</span>
							</div>
						</div>
					</div>
					<div class="box_8 card flex-col">
						<div class="text-wrapper_24 flex-row">
							<span class="text_55">教学经验</span>
							<span class="my-schedule" @click="onPopup('课表')">我的课表</span>
						</div>
						<div class="box_9 flex-row">
							<div class="section_9 flex-col justify-between">
								<div class="text-wrapper_25 flex-row justify-between">
									<span class="text_56">{{ detail.sknx || '0' }}</span>
									<span class="text_57">年</span>
								</div>
								<span class="text_58">授课年限</span>
							</div>
							<img
								class="image_6"
								referrerpolicy="no-referrer"
								src="./assets/img/SketchPng804377c1b35da5eba77db6161c012bb55e2c1ecd545325380e684bf3ba442dfc.png"
							/>
							<div class="section_10 flex-col justify-between">
								<div class="text-wrapper_26 flex-row justify-between">
									<span class="text_59">{{ detail.kcsl || '0' }}</span>
									<span class="text_60">节</span>
								</div>
								<span class="text_61">学年授课课程数量</span>
							</div>
							<div class="text-wrapper_27 flex-col"><span class="text_62">学术交流</span></div>
							<div class="section_11 flex-row">
								<div class="group_3 flex-col justify-between">
									<div class="text-wrapper_28 flex-row justify-between">
										<span class="text_63">{{ detail.xsjj || '0' }}</span>
										<span class="text_64">项</span>
									</div>
									<span class="text_65">学术讲座</span>
								</div>
								<img
									class="image_7"
									referrerpolicy="no-referrer"
									src="./assets/img/SketchPng36c2f09bd0ce34ba751d9e94760d0d5158f819869c7cf07cb11c6551623a5a78.png"
								/>
								<div class="group_4 flex-col justify-between">
									<div class="text-wrapper_29 flex-row justify-between">
										<span class="text_66">{{ detail.zssl || '0' }}</span>
										<span class="text_67">人</span>
									</div>
									<span class="text_68">证书数量</span>
								</div>
								<img
									class="image_8"
									referrerpolicy="no-referrer"
									src="./assets/img/SketchPng36c2f09bd0ce34ba751d9e94760d0d5158f819869c7cf07cb11c6551623a5a78.png"
								/>
								<div class="group_5 flex-col justify-between">
									<div class="text-wrapper_30 flex-row justify-between">
										<span class="text_69">{{ detail.gkpk || '0' }}</span>
										<span class="text_70">人</span>
									</div>
									<span class="text_71">观课评课</span>
								</div>
							</div>
						</div>
					</div>
				</div>
				<div class="box_10 card flex-col">
					<div class="text-wrapper_31 flex-row"><span class="text_72">生活信息</span></div>
					<div class="block_6 flex-row justify-between">
						<div class="group_6 flex-col">
							<div class="text-wrapper_32 flex-col"><span class="text_73">消费情况</span></div>
							<div class="group_7 flex-row">
								<img
									class="label_1"
									referrerpolicy="no-referrer"
									src="./assets/img/SketchPngc3de0d48329a3e666eb3648dd509c55e1a7c9ef3014faf50d0549e2ddbf911ad.png"
								/>
								<div class="box_11 flex-col justify-between">
									<div class="text-wrapper_33 flex-row justify-between">
										<span class="text_74" style="width: auto; margin-right: 2px">
											{{ xfqk.rjxf || 0 }}
										</span>
										<span class="text_75">元</span>
									</div>
									<span class="text_76">日均消费</span>
								</div>
								<img
									class="image_9"
									referrerpolicy="no-referrer"
									src="./assets/img/SketchPng62cb513db752305afccdbc8c1ec1ae5f5bb13005be9990ea041f650581b026c5.png"
								/>
								<img
									class="label_2"
									referrerpolicy="no-referrer"
									src="./assets/img/SketchPng4d0790823d5e3f1d35f3c7d5225f687fa9f1b63c3e214d2d17680a01514b5919.png"
								/>
								<div class="box_12 flex-col justify-between">
									<div class="text-wrapper_34 flex-row justify-between">
										<span class="text_77" style="width: auto; margin-right: 2px">
											{{ xfqk.dyxf || 0 }}
										</span>
										<span class="text_78">元</span>
									</div>
									<span class="text_79">当月消费</span>
								</div>
								<img
									class="image_10"
									referrerpolicy="no-referrer"
									src="./assets/img/SketchPng27cfe05b41d8a54f2c957c2607a0acac0b38908a95d961e265e0ce945368ce29.png"
								/>
								<div class="image-text_1 flex-row justify-between">
									<img
										class="label_3"
										referrerpolicy="no-referrer"
										src="./assets/img/SketchPng5af920585f5c51e3d5ce846ee84b17ea362fbe2d7ffbadca35a385c7bc589f44.png"
									/>
									<div class="text-group_1 flex-col justify-between">
										<div class="text-wrapper_35 flex-row justify-between">
											<span class="text_80">{{ xfqk.yjxfcs || 0 }}</span>
											<span class="text_81">次</span>
										</div>
										<span class="text_82">月均消费次数</span>
									</div>
								</div>
								<img
									class="image_11"
									referrerpolicy="no-referrer"
									src="./assets/img/SketchPngdd9c7e1bb8f1ff328033c9c3913fddd189091575f0c966794be859b8ad57dc6f.png"
								/>
								<div class="image-text_2 flex-row justify-between">
									<img
										class="label_4"
										referrerpolicy="no-referrer"
										src="./assets/img/SketchPngad8d84d248fc137292c2033368b0cffcfda48035ba67ee1233c56c58e64832f1.png"
									/>
									<div class="text-group_2 flex-col justify-between">
										<span class="text_83">学工食堂</span>
										<span class="text_84">主要消费地</span>
									</div>
								</div>
							</div>
						</div>
						<div class="group_8 flex-col">
							<div class="text-wrapper_36 flex-col"><span class="text_85">图书借阅</span></div>
							<div class="block_7 flex-row">
								<img
									class="label_5"
									referrerpolicy="no-referrer"
									src="./assets/img/SketchPng283134cce37fe12c58320903f9c203faf04deabe9c13e0164b760f454e016192.png"
								/>
								<div class="box_13 flex-col justify-between">
									<div class="text-wrapper_37 flex-row justify-between">
										<span class="text_86">{{ detail.jrzsl || '0' }}</span>
										<span class="text_87">本</span>
									</div>
									<span class="text_88">借阅总数量</span>
								</div>
								<img
									class="image_12"
									referrerpolicy="no-referrer"
									src="./assets/img/SketchPngf954bc345c8985f45bea360e6709860ecd22c1fc9d2525f83336094aa41c73da.png"
								/>
								<img
									class="label_6"
									referrerpolicy="no-referrer"
									src="./assets/img/SketchPng7300aac54b14412d873a22568edd2a4eb3d6ce12cf009adf41661c46ffbdd323.png"
								/>
								<div class="text-group_3 flex-col justify-between">
									<span class="text_89">{{ detail.dqjysl || '0' }}</span>
									<span class="text_90">当前借阅数量</span>
								</div>
								<img
									class="image_13"
									referrerpolicy="no-referrer"
									src="./assets/img/SketchPng00983101185d41287290d36abeeac1e3c9aea0021055c11b9acb5dc87699c050.png"
								/>
								<div class="image-text_3 flex-row justify-between">
									<img
										class="label_7"
										referrerpolicy="no-referrer"
										src="./assets/img/SketchPng6a20b8180e1d0841da93d99a01c89face7c85c20eeece644f760f3140df262c5.png"
									/>
									<div class="text-group_4 flex-col justify-between">
										<span class="text_91">{{ detail.jrzsj || '《-》' }}</span>
										<span class="text_92">借阅最长书籍</span>
									</div>
								</div>
								<img
									class="image_14"
									referrerpolicy="no-referrer"
									src="./assets/img/SketchPng0f8dcbedda1ee66849ddaa0abfe2ecd141a8af14b7df8b1fdebf64ef96de5f81.png"
								/>
								<img
									class="label_8"
									referrerpolicy="no-referrer"
									src="./assets/img/SketchPngae1977b345875d352b0de0f659dc7a64dbba8f718c73d45031b3089c60a4c623.png"
								/>
								<div class="text-group_5 flex-col justify-between">
									<span class="text_93">{{ detail.ndjrsl || '0' }}</span>
									<span class="text_94">年度借阅数量</span>
								</div>
							</div>
						</div>
					</div>
				</div>
			</div>
			<div class="block_8 card flex-col">
				<div class="text-wrapper_38 flex-row"><span class="text_95">通讯信息</span></div>
				<div class="block_9 flex-row">
					<img
						class="image_15"
						referrerpolicy="no-referrer"
						src="./assets/img/SketchPngc92440202450a3f65f7440ed5e7107831d5d322e9f4c0b2ccace6481a690fbdd.png"
					/>
					<div class="text-group_6 flex-col justify-between">
						<span class="text_96">{{ detail.sjh }}</span>
						<span class="text_97">手机</span>
					</div>
					<img
						class="image_16"
						referrerpolicy="no-referrer"
						src="./assets/img/SketchPng224c2e12ebfea512f5a95ee93bedcaaaf9666d24c677f8fbede9d3a310744270.png"
					/>
					<div class="text-group_7 flex-col justify-between">
						<!-- 待 -->
						<span class="text_98">{{ detail.jjlxdh || '-' }}</span>
						<span class="text_99">紧急联系电话</span>
					</div>
					<img
						class="image_17"
						referrerpolicy="no-referrer"
						src="./assets/img/SketchPng42b4aef69b13e9fd14a0edaddf6001d8a652479bb4e945526b6a3cc4e3cb82ea.png"
					/>
					<div class="text-group_8 flex-col justify-between">
						<span class="text_100">{{ detail.jtdz }}</span>
						<span class="text_101">地址</span>
					</div>
					<img
						class="image_18"
						referrerpolicy="no-referrer"
						src="./assets/img/SketchPng400d88fea332e8ce469f1afe7d1022b65dbfa5b32d25c5950884f00f2a9d9ecc.png"
					/>
					<div class="text-group_9 flex-col justify-between">
						<span class="text_102">{{ detail.dzyx }}</span>
						<span class="text_103">email</span>
					</div>
				</div>
			</div>
		</div>
		<PopEmployment ref="refPopup" :title="title" />
	</div>
</template>
<script>
import { xodbApi, xodbApi2 } from '@/api/xodb';
import headAvator from '../../header/headAvator.vue';
import PopEmployment from '../../components/cockpit/pop-employment.vue';
export default {
	components: {
		headAvator,
		PopEmployment
	},
	data() {
		return {
			detail: {},
			userInfo: {},
			xfqk: {},
			username: '',
			scale: 1,
			title: '' //弹窗的标题
		};
	},
	computed: {
		loginUserInfo() {
			return JSON.parse(localStorage.getItem('loginUserInfo')) || {};
		}
	},
	created() {
		this.scale = this.$utils.toolViewRatio();
		this.userInfo = JSON.parse(localStorage.getItem('userInfo')) || {};
		this.username = this.userInfo && this.userInfo.username ? this.userInfo.username : '';
		this.getInfoN('ads_jg_grhx_jzgqksj_query'); //教职工基本数据_查询
		this.getInfoXf('ads_zc_jzgxfqk_query'); //教职消费情况数据_查询
		// this.getInfo('warehouseConfig_ybzy_dw_dws_jg_t_jzgjbsj_query'); //教职工基本数据_查询
		this.getInfo2('/ybzy/platuser/front/getAllInfo'); //教职工基本数据_查询
		// this.getData('warehouseConfig_ybzy_dw_dwb_ky_jskyxm_query', 'kyxmsl'); //教师科研项目_查询
		// this.getData('warehouseConfig_ybzy_dw_dwb_ky_jszzsl_query', 'zzsl'); //教师专著数量_查询
		// this.getData('warehouseConfig_ybzy_dw_dwb_ky_jslwsl_query', 'lwsl'); //教师论文数量_查询
		// this.getData('warehouseConfig_ybzy_dw_dwb_ky_jszlsl_query', 'zlsl'); //教师专利数量_查询
		// this.getData('warehouseConfig_ybzy_dw_dwb_ky_jshjsl_query', 'hjsl'); //教师获奖数量_查询
		// 特殊处理假数据
		setTimeout(() => {
			this.toolData();
		}, 1200);
	},
	methods: {
		toolData() {
			const name = this.loginUserInfo.name;
			let obj = {};
			if (name === '钟元飞') {
				obj = {
					mzdm: '汉',
					lwsl: '2',
					zzsl: '5',
					hjsl: '5',
					sknx: '31',
					kcsl: '186',
					xsjj: '4',
					zyxqbh: '4',
					zssl: '10',
					gkpk: '500',
					jrzsl: '10',
					dqjysl: '3',
					jrzsj: '《体育概论》',
					ndjrsl: '10',
					szks: '素质教育学院',
					zgxwmc: '学士学位',
					jjlxdh: '13002880380',
					jtdz: '宜宾市翠屏区丽雅江辰',
					dzyx: '<EMAIL>'
				};
			}
			this.detail = { ...this.detail, ...obj };
		},
		async getInfoN(url) {
			try {
				const LeaderRole = localStorage.getItem('LeaderRole');
				const {
					data: { list }
				} = await xodbApi2.post({
					url: url,
					// pageSize: 780,
					params: {
						jgh: this.loginUserInfo.code,
						xyjgbh: LeaderRole === 'xLeader' ? '1' : this.loginUserInfo.orgCode
					}
				});
				const data = list[0];
				this.detail = { ...this.detail, ...data };
			} catch (error) {
				console.error('处理数据失败:', error);
			}
		},
		async getInfoXf(url) {
			const LeaderRole = localStorage.getItem('LeaderRole');
			try {
				const {
					data: { list }
				} = await xodbApi2.post({
					url: url,
					pageSize: 999,
					params: {
						jgh: this.loginUserInfo.code,
						xyjgbh: LeaderRole === 'xLeader' ? '1' : this.loginUserInfo.orgCode
					}
				});
				this.xfqk = list[0] || {};
			} catch (error) {
				console.error('处理数据失败:', error);
			}
		},
		//教职工基本数据_查询
		async getInfo(url) {
			try {
				const {
					dataResult: { dataList }
				} = await xodbApi.get({
					url: url
				});
				const data = dataList?.find(item => item.xm === this.username) || {};
				this.detail = { ...this.detail, ...data };
			} catch (error) {
				console.error('处理数据失败:', error);
			}
		},
		getInfo2(url) {
			this.$request({ url: url, method: 'GET' }).then(res => {
				if (res.rCode === 0) {
					this.detail = { ...this.detail, ...(res.results || {}) };
					return;
				}
			});
		},

		// 查询单个字段匹配对应数据
		async getData(url, key) {
			try {
				const {
					dataResult: { dataList }
				} = await xodbApi.get({
					url
				});
				const data = dataList?.find(item => item.xm === this.username) || {};
				this.detail[key] = data[key];
			} catch (error) {
				console.error(`${key}处理数据失败:`, error);
			}
		},
		onPopup(title) {
			this.title = title;
			this.$refs.refPopup.dialogVisible = true;
		}
	}
};
</script>
<style scoped lang="scss" src="@/assets/style/common.scss" />
<style scoped lang="scss" src="./assets/index.scss" />
