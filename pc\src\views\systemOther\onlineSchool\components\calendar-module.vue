<template>
	<div class="main-center-right">
		<div class="main-center-right-title title">
			<img class="title-img" src="@ast/images/systemOther/online-icon8.png" alt="" />
			<div>我的课程</div>
		</div>
		<!--  日历  -->
		<vCalendar
			class="main-center-right-calendar"
			:attributes="attributes"
			@dayclick="changeDate"
		></vCalendar>
		<!--  打卡详情  -->
		<div class="main-center-right-subTitle">
			<div class="subTitle">全天课程</div>
		</div>
		<div class="schedule">
			<div class="time-slot">
				<div class="time">
					<span>上午</span>
					9:00 ~ 11:00
				</div>
				<div class="info-box">
					<div class="info-box-title">
						<span class="course-name">直播</span>
						<span class="course-teacher">【微积分】</span>
					</div>
					<div class="info-box-describe">某二级学院：商务金融-23级-4班</div>
				</div>
				<i class="dot" :class="isUp ? 'dot--active' : ''"></i>
			</div>
			<div class="time-slot">
				<div class="time">
					<span>下午</span>
					14:00 ~ 16:00
				</div>
				<div class="info-box">
					<div class="info-box-title">
						<span class="course-name">直播</span>
						<span class="course-teacher">【摄影艺术创作】</span>
					</div>
					<div class="info-box-describe">某二级学院：商务金融-23级-4班</div>
				</div>
				<i class="dot" :class="!isUp ? 'dot--active' : ''"></i>
			</div>
		</div>
	</div>
</template>

<script>
export default {
	name: 'CalendarModule',
	data() {
		return {
			/**自定义打卡日期的标记*/
			attributes: [
				{
					key: 'vODay',
					dates: new Date(),
					highlight: {
						fillMode: 'light',
						contentStyle: {
							color: '#ffffff'
						},
						style: {
							backgroundColor: '#0377e8'
						}
					},
					dot: false
					// popover: {
					// 	label: '美好的一天!要开心呦!'
					// }
				},
				{
					key: 'V1Day',
					dates: [
						new Date('2024/7/6'),
						new Date('2024/7/8'),
						new Date('2024/7/10'),
						new Date('2024/7/11'),
						new Date('2024/7/12'),
						new Date('2024/7/13'),
						new Date('2024/7/17'),
						new Date('2024/7/18'),
					],
					dot: {
						style: {
							backgroundColor: '#0377e8',
							width: '8px',
							height: '8px'
						}
					}
				},
				{
					key: 'V2Day',
					dot: {
						style: {
							backgroundColor: '#de7963',
							width: '8px',
							height: '8px'
						}
					},
					dates: [new Date('2024/7/9'),new Date('2024/7/16')]
				}
			],

			moonChart: null // 月考勤统计的表格实例
		};
	},
	computed: {
		// 判断是否为上午，返回true或者false
		isUp() {
			return new Date().getHours() < 12;
		}
	},
	mounted() {},
	methods: {
		/**切换日期*/
		changeDate(date) {
			console.log(date);
		}
	}
};
</script>

<style scoped lang="scss">
@import '@ast/style/public.scss';
/**日历*/
.main-center-right {
	background: #fff;
	flex: 1;
	margin-left: 12px;
	border-radius: 8px;
	padding: 12px 20px;
	&-title {
		margin-bottom: 20px;
		padding-bottom: 12px;
		border-bottom: 1px solid rgba(241, 244, 247, 1);
	}
	&-calendar {
		width: 100%;
		height: 340px;
		background: #fbfaff;
		::v-deep .vc-day {
			min-height: 50px !important;
		}
	}
	&-subTitle {
		margin-top: 20px;
		background: #fbfaff;
		padding: 8px 12px;
		border-radius: 8px;
		.subTitle {
			font-weight: 600;
			margin-bottom: 4px;
		}
	}
	.schedule {
		display: flex;
		flex-direction: column;
		padding-top: 12px;

		.time-slot {
			padding-left: 20px;
			border-left: 1px rgba(241, 244, 247, 1) solid;

			padding-bottom: 13px;
			position: relative;

			.time {
				font-size: 14px;
				font-weight: bold;
				margin-bottom: 5px;

				color: #333;
				span {
					color: #999;
					font-weight: normal;
				}
			}

			.info-box {
				border-radius: 3px;
				font-size: 14px;
				background: #f8f8f8;
				padding: 6px 10px;
				margin-top: 3px;
				.info-box-title {
					.course-name {
						color: #dd6a66;
					}

					.course-teacher {
						font-weight: bold;
						color: #333;
					}
				}

				.info-box-describe {
					font-size: 13px;
					color: #999;
				}
			}
			.dot {
				position: absolute;
				top: 6px;
				left: -4px;
				display: inline-block;
				width: 7px;
				height: 7px;
				border-radius: 50%;
				background: #cfdcf4;
				&--active {
					background: #0377e8;
				}
			}
		}
	}
}
.title {
	display: flex;
	align-items: center;
	font-size: 16px;
	font-weight: 550;
	&-img {
		width: 22px;
		height: 22px;
		margin-right: 8px;
	}
}
</style>
