<template>
	<div class="usullayApp">
		<el-carousel
			arrow="never"
			:interval="5000"
			:autoplay="false"
			indicator-position="outside"
			height="340px"
		>
			<el-carousel-item v-for="(outeritem, outerindex) in myUsullyApp" :key="outerindex">
				<div v-for="(senitem, senindex) in outeritem" :key="senindex" class="rowUsullay">
					<div v-for="(item, index) in senitem" :key="index" class="rowUsullayItem">
						<div v-if="item.name !== 'emptyText'" class="itemUsullay" @click="toOpenWindow(item)">
							<div class="topPicUsullay">
								<img :src="handelPicUrl(item.icons)" alt="" srcset="" width="40px" />
							</div>
							<div class="textUsullay">{{ item.text }}</div>
						</div>
					</div>
				</div>
			</el-carousel-item>
		</el-carousel>

		<es-dialog title="常用功能" size="max" :visible.sync="visible" ref="visible">
			<iframe
				ref="iframe"
				src=""
				width="100%"
				height="100%"
				frameborder="0"
				allowfullscreen
				allow-same-origin
			>
				<div>浏览器不兼容</div>
			</iframe>
		</es-dialog>
	</div>
</template>

<script>
import { alumniUrl } from '@/config';
import { getTransactionList, checkLogin, getEossAuthentication } from '@/api/home.js';
import requestFun from '@/utils/request';
export default {
	data() {
		return {
			loading: false,
			myUsullyApp: [],
			visible: false
		};
	},
	mounted() {
		this.$.ajax({
			url: `/ybzy/platUserCommfunc/front/listJson`
		})
			// requestFun({
			// 	// url: `api/ybzy/platUserCommfunc/front/save`,
			// 	url: `/ybzy/platUserCommfunc/front/listJson`,
			// 	method: 'get'
			// })
			.then(res => {
				// console.log(res.data.results.records.join(','));
				// let str = res.data.results.records.join(',');
				let arrIndex = res?.results?.records || [];
				this.getListData(arrIndex);
			});
	},
	methods: {
		toOpenWindow(item) {
			if (item.id == '11111111') {
				return;
			}
			// window.open(`${picUrl}${item.newDataUrl}${item.code}`);
			// window.open(`${alumniUrl}${item.url}`);
			this.visible = true;
			this.$nextTick(() => {
				this.$refs.iframe.src = `${alumniUrl}${item.url}`;
			});
		},
		//处理图片路径
		handelPicUrl(url) {
			return `${alumniUrl}/static/andyui2.0/css/picIcon/img/${url}`;
		},
		//获取接口数据
		getListData(arrIndex) {
			let userId = localStorage.getItem('mobileUserId') || '';
			/**原有代码基础加上判断，没有授权的话请求授权*/
			if (!userId) {
				// checkLogin()
				this.$.ajax({
					url: checkLogin
				}).then(res => {
					let code = res.results.code;
					localStorage.setItem('ssoCode', res.results.code);
					let data = {
						serverId: 'ybzyDtcSso',
						authType: '6',
						code
					};
					// if (true) {
					if (!(code === null)) {
						// this.$api.serviceCenter
						// 	.getEossAuthentication(data)
							this.$.ajax({
								url: getEossAuthentication,
								method: 'POST',
								data: data
							})
							.then(res => {
								localStorage.setItem('mobileToken', res.results.mobileToken);
								localStorage.setItem('mobileUserId', res.results.userId);
								this.getListData(arrIndex);
							})
							.catch(err => {
								console.log(err, '认证失败err');
								return '认证失败';
							});
					}
				});
			} else {
				this.$.ajax({
					url: getTransactionList + `?menuCode=cygn&userId=${userId}&type=2`
				})
					.then(res => {
						let arr = [];
						let usullayListGet = [];
						res.results.forEach(i => {
							i.children.forEach(item => {
								arr.push(item);
							});
						});
						arrIndex.forEach(item => {
							let found = false;
							for (let i = 0; i < arr.length; i++) {
								if (arr[i].id === item) {
									usullayListGet.push(arr[i]);
									arr.splice(i, 1);
									found = true;

									break;
								}
							}
						});
						let list = usullayListGet.concat(arr);
						for (let i = 0; i < Math.ceil(list.length / 15); i++) {
							let arr = []; //页
							if (i < Math.floor(list.length / 15)) {
								for (let k = 0; k < 3; k++) {
									let itemArr = []; //行
									for (let j = 0; j < 5; j++) {
										itemArr.push(list[i * 15 + k * 5 + j]);
									}
									arr.push(itemArr);
								}
							} else {
								let remain = list.length % 15;
								let remainRow = Math.ceil(remain / 5);
								for (let i = 0; i < remainRow; i++) {
									let itemArr = [];
									for (let k = 0; k < 5; k++) {
										if (list[list.length - remain + i * 5 + k]) {
											itemArr.push(list[list.length - remain + i * 5 + k]);
										} else {
											itemArr.push({
												icons: '',
												text: '',
												id: '11111111'
											});
										}
									}
									arr.push(itemArr);
								}
							}
							this.myUsullyApp.push(arr);

							let newArr = [];
							this.myUsullyApp.forEach(i => {
								if (i.url == '') {
								} else {
									newArr.push(i);
								}
							});
							this.myUsullyApp = newArr;
						}
					})
					.catch(error => {
						console.log(error, 'error');
					});
			}
		}
	}
};
</script>

<style lang="scss" scoped>
.usullayApp {
	width: 100%;
	height: 380px;
	padding: 10px 30px 0 30px;
	background: #fff;
	// overflow: hidden;
	.rowUsullay {
		width: 100%;
		height: 114px;
		display: flex;
		.rowUsullayItem {
			flex: 1;
			height: 100%;
			display: flex;
			justify-content: center;
			.itemUsullay {
				cursor: pointer;
				width: 70px;
				height: 100%;

				.topPicUsullay {
					width: 100%;
					height: 70px;
					border-radius: 50%;
					display: flex;
					justify-content: center;
					align-items: center;
					// background: #4545ef;
				}
				.textUsullay {
					white-space: nowrap;
					overflow: hidden;
					text-overflow: ellipsis;
					width: 100%;
					text-align: center;
					height: 36px;
					line-height: 36px;
					font-size: 14px;
					font-family: Microsoft YaHei;
					font-weight: bold;
					color: #333333;
				}
			}
		}
	}
}
::v-deep .is-active .el-carousel__button {
	// 指示器激活按钮
	background: var(--brand-6, #0076e8);
	height: 10px;
	width: 22px;
	border-radius: 5px;
}
::v-deep .el-carousel__button {
	// 指示器按钮
	width: 10px;
	height: 10px;
	background: #c4c4c6;
	border-radius: 50%;
}
</style>
