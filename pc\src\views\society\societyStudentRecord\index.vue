<template>
  <div class="content" style="height: 100%">
    <div style="width:100%;height: 100%">
      <es-data-table :row-style="tableRowClassName" v-if="true" ref="table" :key="tableCount" :full="true" :fit="true" :thead="thead" :toolbar="toolbar"
                     @btnClick="btnClick" @selection-change="handleSelectionChange" @edit="changeTable"
                     :page="page" :url="dataTableUrl" :param="dataTableParam" :border="true" :numbers="true" checkbox form>
      </es-data-table>
      <el-dialog :title="formTitle" v-if="showInfoPage" :visible.sync="showInfoPage" append-to-body>
        <infoPage ref="infoPage" v-on:activelyClose="closeInfoPage" :base-data="this.formData" :info-page-mode="this.infoPageMode"></infoPage>
      </el-dialog>
    </div>
  </div>
</template>

<script>
import api from '@/http/society/societyStudentRecord/api';
import InfoPage from "@/views/society/societyStudentRecord/infoPage.vue";

export default {
  components: {InfoPage},
  data() {
    return {
      formData: {},
      page: {
        pageSize: 20,
        totalCount: 0,
      },
      infoPageMode: 'allOn',
      selectRowData: [],
      selectRowIds: [],
      showInfoPage: false,
      tableCount: 1,
      dialogType: "",
      dataTableUrl: api.societyStudentRecordList,
      dataTableParam: { orderBy: 'create_time', asc: false, auditStatus: 2 },
      formTitle: '',
      validityOfDateDisable: false,
      thead: [],
      toolbar: [
        {
          type: 'button',
          contents: [
            {
              text: '删除',
              code: 'toolbar',
              type: 'danger'
            },
            {
              text: '查看',
              code: 'toolbar',
              type: 'primary'
            },
          ]
        },
        {
          type: 'search',
          reset: true,
          contents: [
            {
              type: 'text',
              name: 'keyword',
              placeholder: '社团名称',
              clearable: true,
            },
            {
              type: 'select',
              name: 'applyType',
              placeholder: '状态',
              clearable: true,
              data: [
                {
                  value: 1,
                  label: '加入社团'
                },
                {
                  value: 2,
                  label: '退出社团'
                },
              ],
            },
          ]
        },
      ],
      listThead: [
      {
          title: '姓名',
          width: "140px",
          align: 'left',
          field: 'applyUserName',
          sortable: 'custom',
          showOverflowTooltip: true,
        },
        {
          title: '社团名称',
          align: 'left',
          field: 'societyName',
          label:'label',
          sortable: 'custom',
          showOverflowTooltip: true,
        },
				{
					title: '政治面貌',
					align: 'left',
					field: 'politicalStatus',
					sortable: 'custom',
					showOverflowTooltip: true,
          sysCode: 'society_political_status'
				},
        {
          title: '所属学院',
          width: "150px",
          align: 'center',
          field: 'applyUserCollege',
          sortable: 'custom',
          showOverflowTooltip: true,
        },
        {
          title: '所属班级',
          width: "150px",
          align: 'center',
          field: 'applyUserClass',
          sortable: 'custom',
          showOverflowTooltip: true,
        },
        {
          title: '申请时间',
          width: "150px",
          align: 'center',
          field: 'createTime',
          sortable: 'custom',
          showOverflowTooltip: true,
        },
        {
          title: '状态',
          width: "150px",
          align: 'center',
          field: 'applyType',
          sortable: 'custom',
          showOverflowTooltip: true,
          render: (h, param)=>{
            return h(
                'el-tag',
                {props: {type: param.row.applyType === 2? 'danger':''}},
                param.row.applyTypeName
            )
          }
        },

      ],
      btnJson: {
        title: '操作',
        type: 'handle',
        width: 180,
        template: '',
        events: [
          {
            code: 'row',
            text: '查看'
          },
        ]
      },
      tBSelectList: []
    };
  },
  created() {
    //初始化查询待审核列表
    this.thead = this.getListThead(this.btnJson);
  },
  methods: {
    /**
     * 表格行高
     */
    tableRowClassName() {
      return {
        "height": "54px !important"
      };
    },
    btnClick(res){
      let text = res.handle.text;
      let code = res.handle.code;

      if(code === 'row'){
        switch (text){
          case '查看': this.openInfoPage(res.row.id, text); break;
          case '删除': this.deleteRows([res.row.id]); break;
        }
      }else {
        switch (text){
          case '查看':
            if(this.selectRowIds.length>1){
              this.$message.warning('只能选择一个查看');
            }else if(this.selectRowIds.length<1){
              this.$message.warning('请选择一个进行查看');
            }else {this.openInfoPage(this.selectRowIds[0], text);}
            break;
          case '删除':
            if(this.selectRowIds.length<1){
              this.$message.warning('请选择一个删除');
            }else {this.deleteRows(this.selectRowIds);}
            break;
        }
      }
    },
    //打开infoPage
    openInfoPage(id, pageMode){
      this.$request({
        url: api.societyStudentRecordInfo+'/'+id,
        method: 'GET'
      }).then(res => {
        //处理返回数据
        this.formData = {...res.results};
        this.formData.applyType = {value:this.formData.applyType, label:this.formData.applyType===1?'加入社团':'退出社团'}

        this.formTitle = pageMode;
        this.infoPageMode = pageMode;
        this.showInfoPage = true;
      });
    },
    //关闭infoPage
    closeInfoPage(reload){
      this.formData = {};
      this.showInfoPage = false;
      if(reload)
        this.$refs.table.reload();
    },
    // 数据列表多选回调
    handleSelectionChange(data) {
      let ids = [];
      data.forEach(row => {
        ids.push(row.id);
      });
      this.selectRowData = data;
      this.selectRowIds = ids;
    },
    deleteRows(ids){
      this.$confirm('确定要删除选中的数据吗？', '删除', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      })
          .then(() => {
            this.$request({
              url: api.societyStudentRecordDeleteByIds,
              data: { ids: ids.join(',') },
              method: 'POST'
            }).then(res => {
              if (res.rCode === 0) {
                this.$message.success('操作完成');
                this.$refs.table.reload();
              } else {
                this.$message.error(res.msg);
              }
            });
          })
          .catch(() => {});
    },
    getListThead(btnJson){
      let tempThead = [...this.listThead];
      tempThead.push(btnJson);
      return tempThead;
    },
    changeTable(val) {
      switch (val.name){
        case 'status': break;
      }
    },
  },
  computed: {

  }
};
</script>
<style scoped>
::v-deep .el-dialog {
  top: 20%;
}
</style>