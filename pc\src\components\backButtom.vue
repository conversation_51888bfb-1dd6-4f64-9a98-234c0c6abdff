<template>
	<div class="back-box" :class="type === 'transparent' ? 'transparent-style' : ''" @click="onBack">
		<svg
			t="1704873609001"
			class="icon"
			viewBox="0 0 1024 1024"
			version="1.1"
			xmlns="http://www.w3.org/2000/svg"
			p-id="28997"
		>
			<path
				d="M663.84022 221.99707H289.594631V114.235764c0-39.21207-27.01119-53.966099-59.996443-32.787754L28.827559 210.244398c-33.041534 21.182438-33.041534 55.807027 0 76.955696l200.770629 128.826064c33.016974 21.182438 59.996442 6.399757 59.996443-32.813336v-89.924056h356.410386c196.8616 0 302.9498 88.282672 302.9498 285.114597 0 149.32698-58.549487 285.116643-302.9498 285.116642H147.035798c-0.226151 0-0.425695 0.14224-0.6498 0.167823-0.539282-0.025583-1.048889-0.167822-1.589194-0.167823-24.602328 0-44.534286 19.961634-44.534286 44.563962 0 24.604374 19.927865 44.538379 44.534286 44.538379 0.539282 0 1.050935-0.140193 1.589194-0.169869 0.226151 0 0.425695 0.169869 0.6498 0.169869H663.84022c196.8616 0 356.410386-123.92852 356.410386-320.762491V524.947893c0-196.859553-159.544693-302.950823-356.410386-302.950823m0 0"
				p-id="28998"
			></path>
		</svg>
		<span class="back">{{ title }}</span>
	</div>
</template>

<script>
export default {
	name: 'BackButtom',
	props: {
		title: {
			type: String, // 标题
			default: '返回大厅'
		},
		type: {
			type: String, // 组件类型 defaule transparent
			default: 'defaule'
		}
	},
	methods: {
		onBack() {
			this.$router.back();
			this.$emit('click');
		}
	}
};
</script>

<style lang="scss" scoped>
.back-box {
	width: 120px;
	height: 38px;
	background: #ffffff;
	border-radius: 18px;
	border: 1px solid #e3e3e3;
	display: flex;
	align-items: center;
	justify-content: space-between;
	padding: 0 15px;
	cursor: pointer;
	transition: all 0.3s ease;
	.icon {
		width: 16px;
		height: 16px;
		fill: #5a5f6b;
	}
	.back {
		// width: 64px;
		width: 100%;
		margin-left: 6px;
		height: 21px;
		white-space: nowrap;
		text-align: center;
		// 字体大小
		font-size: 16px;
		color: #5a5f6b;
		line-height: 21px;
	}
	&:hover {
		background: #0175e8;
		border: 1px solid #0175e8;
		.icon {
			fill: #fff;
		}
		.back {
			color: #fff;
		}
	}
}
.transparent-style {
	background: rgba(255, 255, 255, 0.15);
	border: 1px solid #ffffff;
	.icon {
		width: 16px;
		height: 16px;
		fill: #ffffff;
	}
	.back {
		color: #ffffff;
	}
	&:hover {
		background: #0175e8;
		border: 1px solid #0175e8;
	}
}
</style>
