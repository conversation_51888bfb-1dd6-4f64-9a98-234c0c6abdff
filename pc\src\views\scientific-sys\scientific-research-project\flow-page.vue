<template>
	<BasicInfo
		v-if="formId"
		:id="formId"
		:contents-key="contentsKey"
		@visible="$emit('visible', false)"
	/>
</template>

<script>
import BasicInfo from '../components/project-info/basic-info';
import { v4 as uuidv4 } from 'uuid';
export default {
	components: {
		BasicInfo
	},
	props: {
		// 路由props参数，用于动态化参数
		contentsKey: {
			type: String,
			required: true
		},
		title: {
			type: String,
			default: '查看'
		}
	},
	data() {
		return {
			queryData: {}
		};
	},
	computed: {
		formId() {
			return this.$route.query?.recordId || this.$route.query?.recordid || uuidv4();
		}
	}
};
</script>

<style></style>
