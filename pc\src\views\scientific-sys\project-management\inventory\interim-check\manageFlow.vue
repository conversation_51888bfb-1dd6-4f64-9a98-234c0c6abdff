<template>
	<div style="height: 100%">
		<es-flow-group :show="show" :contents="contents" :flow="flow" ref="from"></es-flow-group>
	</div>
</template>

<script>
import { getFlowByid, updateInterimByid, getFlowInfo } from '@/api/scientific-sys.js';

export default {
	name: 'interimManageFlow',
	data() {
		return {
			pendingId: '',
			nodeName: '',
			model: '',
			id: '', //业务id
			appId: '', //流程id
			projectId: '', //详细信息查询id 列表id
			ownId: '', //附件id
			show: false,
			isReadonly: true
		};
	},
	computed: {
		flow() {
			return {
				businessId: this.projectId, //业务 id 上传组件共用
				pendingId: this.pendingId,
				nodeName: this.nodeName,
				flowTypeCode: 'inspection',
				btnList: [
					{ text: '提交', event: 'sub', type: 'primary' }
					// { text: '暂存', event: 'save', code: 0 }
				]
			};
		},
		contents() {
			return [
				{
					label: '基本信息',
					contents: {
						type: 'form',
						collapse: true,
						readonly: this.isReadonly,
						model: this.model,
						action: updateInterimByid,
						// modelMethod: 'get',
						contents: [
							{
								name: 'inspectionContent',
								placeholder: '请输入',
								label: '中检说明',
								type: 'textarea',
								col: 12,
								rules: {
									required: true,
									message: '请输入中检说明',
									trigger: 'blur'
								}
							},
							{
								name: 'inspectionTime',
								placeholder: '请选择',
								label: '中检时间',
								type: 'date',
								col: 12,
								rules: {
									required: false,
									message: '请选择中检时间',
									trigger: 'blur'
								}
							},
							{
								name: 'declarationAdjunctFile',
								label: '附件',
								type: 'attachment',
								value: '',
								preview: true,
								download: true,
								ownId: this.model.inspectionFile, // 业务id
								code: 'inspectionfile',
								action: '/main2/mecpfileManagement/upload',
								col: 12
								// rules: {
								// 	required: false,
								// 	message: '请上传附件',
								// 	trigger: 'blur'
								// }
							}
						],
						events: {
							result: val => {
								if (val.id) {
									// this.id = val.id; //业务id
									// this.$.ajax({
									// 	url: getFlowByid,
									// 	params: {
									// 		id: val.projectId
									// 	}
									// })
									// 	.then(res => {
									// 		if (res.results == null) {
									// 		} else {
									// 			this.appId = res.results; //流程id
									// 		}
									// 	})
									// 	.catch(err => {});
								}
							}
						}
					}
				},
				{
					label: '详细信息',
					hide: this.projectId ? false : true,
					contents: {
						type: 'iframe',
						blank: true,
						url: `${location.href.split('#')[0]}#/scientificDeailUrl?projectId=${this.projectId}`
						// url: `http://localhost:8080/#/interimCheck`
					}
				},
				{
					hide: this.appId ? false : true,
					label: '流程图',
					immediate: false,
					contents: {
						type: 'iframe',
						url: '/bpm/task/taskHandle/toFlowChartView.dhtml?appId=' + this.appId
					}
				},
				{
					hide: this.appId ? false : true,
					label: '流程列表',
					contents: [
						{
							type: 'flow-list',
							scrollbar: true,
							showFormBtn: false,
							toolbar: [
								{
									type: 'text',
									contents: '流程列表'
								},
								{
									type: 'search',
									contents: [
										{
											name: 'showSuggest',
											type: 'radio',
											changeSearch: true,
											data: [
												{
													value: 'all',
													name: '显示全部',
													checked: true
												},
												{ value: 'have', name: '显示已填意见' }
											]
										},
										{
											type: 'select',
											placeholder: '请选择',
											name: 'viewType',
											width: '140px',
											changeSearch: true,
											data: [
												{
													value: 1,
													selected: true,
													name: '时间排序'
												},
												{
													value: 2,
													name: '部门排序'
												},
												{
													value: 3,
													name: '树形排序'
												}
											]
										}
									]
								}
							],
							thead: [
								{
									title: '步骤',
									field: 'itemname',
									type: 'text',
									width: '180'
								},
								{
									title: '办理人',
									field: 'userName',
									type: 'text',
									width: '180'
								},
								{
									title: '状态',
									field: 'querykeywords',
									width: '180',
									render: (h, params) => {
										let color = 'orange';
										switch (params.row.pendstate) {
											case '0':
												color = 'red';
												break;
											case '1':
												color = 'green';
												break;
											case '-1':
												color = 'orange';
												break;
											case '2':
												color = 'deepskyblue';
												break;
										}
										return h(
											'span',
											{
												style: {
													color: color
												}
											},
											params.row.querykeywords
										);
									}
								},
								{
									title: '办理意见',
									type: 'text',
									field: 'doresult'
								},
								{
									title: '时间',
									field: 'doetime',
									render: (h, params) => {
										return h('span', {}, [
											this.$.formatDate(params.row.doetime, 'yyyy-MM-dd HH:mm')
										]);
									},
									width: '180'
								},
								{
									title: '代办人',
									field: 'douserid',
									type: 'text',
									width: '180'
								}
							],
							url: '/oa/pendedhistoryManager/pendedhistoryListJson?apprecordid=' + this.id
						}
					]
				}
			];
		}
	},
	created() {
		//办理流程
		let { recordid, pendingId, nodeName, type, node } = this.$.getParams();
		this.pendingId = pendingId;
		this.nodeName = nodeName;
		this.id = recordid;
		if (type == 'edit') {
			this.show = true;
		}
		//驳回 可修改
		if (node == 1) {
			this.isReadonly = false;
		}
		this.getFlowInfo(recordid);
	},
	methods: {
		load(text) {
			return this.$.loading(this.$loading, text);
		},
		async getFlowInfo(id) {
			const loading = this.load('提交中...');
			try {
				let { rCode, msg, results } = await this.$.ajax({
					url: getFlowInfo,
					method: 'get',
					// format: false,
					params: { id }
				});
				if (rCode == 0) {
					this.model = results;
					this.projectId = results.projectId; //详细信息id
					// this.ownId = results.inspectionFile; //附件
					this.getFlowByid(results.projectId);
					// this.model = `${getFlowFormInfo}?projectId=${results}`;
				} else {
					this.$message.error(msg);
				}
			} finally {
				loading.close();
			}
		},
		async getFlowByid(id) {
			const loading = this.load('提交中...');
			try {
				let { rCode, msg, results } = await this.$.ajax({
					url: getFlowByid,
					method: 'get',
					// format: false,
					params: { id }
				});
				if (rCode == 0) {
					this.appId = results; //流程id
				} else {
					this.$message.error(msg);
				}
			} finally {
				loading.close();
			}
		}
	}
};
</script>
