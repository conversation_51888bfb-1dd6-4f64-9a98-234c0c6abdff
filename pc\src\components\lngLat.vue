<script>
import AMapLoader from '@amap/amap-jsapi-loader';
export default {
	name: 'BaseMapLocation',
	model: {
		prop: 'value',
		event: 'update:value'
	},
	props: {
		aMapKey: {
			type: String,
			default: 'f8fa64f4650dc818069bdc703034e10d'
		},
		value: {
			type: Array,
			default: () => []
		}
	},
	data() {
		return {
			searchValue: '',
			map: null,
			AMap: null,
			geoCoder: null,
			markers: [],
			defaultModifiers: { negative: true, short: true, canEmpty: true }
		};
	},
	computed: {
		lng: {
			get() {
				return this.value[0] || '';
			},
			set(val) {
				this.$emit('update:value', [val || '', this.lat || '']);
			}
		},
		lat: {
			get() {
				return this.value[1] || '';
			},
			set(val) {
				this.$emit('update:value', [this.lng || '', val || '']);
			}
		}
	},
	watch: {
		lng(newVal) {
			if (this.AMap && newVal && this.lat) {
				// 更新地图标记的点
				this.debounce(this.handleCreateMarker([this.lng, this.lat]), 500);
			}
			if (this.AMap && !newVal) {
				this.map.remove(this.markers || []);
				this.markers = [];
			}
		},
		lat(newVal) {
			if (this.AMap && newVal && this.lng) {
				// 更新地图标记的点
				this.debounce(this.handleCreateMarker([this.lng, this.lat]), 500);
			}
			if (this.AMap && !newVal) {
				this.map.remove(this.markers || []);
				this.markers = [];
			}
		},
		searchValue(val) {
			if (this.AMap && val) {
				this.debounce(this.handleInitPlaceSearch(val), 500);
			}
		}
	},
	mounted() {
		this.initMap().then(AMap => {
			this.handleInitSearch(AMap);
			if (this.lng && this.lat) {
				// 经纬度同时存在才渲染点
				this.handleCreateMarker([this.lng, this.lat]);
			}
		});
	},
	methods: {
		debounce(fn, delay = 200) {
			let timer;
			return function () {
				const th = this;
				const args = arguments;
				if (timer) {
					clearTimeout(timer);
				}
				timer = setTimeout(function () {
					timer = null;
					fn.apply(th, args);
				}, delay);
			};
		},
		handleInitPlaceSearch(val) {
			const placeSearch = new this.AMap.PlaceSearch({
				pageSize: 5, // 单页显示结果条数
				pageIndex: 1, // 页码
				city: '0831', // 兴趣点城市
				citylimit: false, //是否强制限制在设置的城市内搜索
				map: this.map, // 展现结果的地图实例
				autoFitView: true // 是否自动调整地图视野使绘制的 Marker点都处于视口的可见范围
			});
			placeSearch.search(val); //关键字查询查询
		},
		// 初始化地图
		initMap() {
			return new Promise((resolve, reject) => {
				AMapLoader.load({
					key: this.aMapKey, // 申请好的Web端开发者Key，首次调用 load 时必填
					version: '2.0',
					plugins: ['AMap.Geocoder', 'AMap.AutoComplete', 'AMap.PlaceSearch'] // 需要使用的的插件列表，如比例尺'AMap.Scale'等
				})
					.then(AMap => {
						this.AMap = AMap;
						var position = new AMap.LngLat(104.636784, 28.755936);
						// 初始化地图
						this.map = new AMap.Map('mapContainer', {
							zoom: 10, //初始化地图级别
							center: position
						});
						this.map.setDefaultCursor('pointer');
						this.map.on('click', e => {
							// 只更新数据
							this.form = {
								lng: e.lnglat.lng,
								lat: e.lnglat.lat
							};
							this.$emit('update:value', [e.lnglat.lng, e.lnglat.lat]);
							this.searchValue = '';
						});
						resolve(AMap);
					})
					.catch(e => {
						reject(e);
					});
			});
		},
		// 注册搜索方法
		handleInitSearch(AMap) {
			//输入提示
			const autoSearch = new AMap.AutoComplete({
				input: 'mapInput'
			});
			//注册监听，当选中某条记录时会触发
			autoSearch.on('select', e => {
				// 只更新数据
				this.form = {
					lng: e.poi.location.lng,
					lat: e.poi.location.lat
				};
				this.$emit('update:value', [e.poi.location.lng, e.poi.location.lat]);
			});
		},
		// 创建点标记
		handleCreateMarker(lngLat) {
			// 每次触发标点事件，都先将之前的点移除掉
			this.map.remove(this.markers || []);
			this.markers = [];
			const marker = new this.AMap.Marker({
				map: this.map,
				icon: 'https://webapi.amap.com/theme/v1.3/markers/n/mark_b.png',
				position: lngLat,
				size: new this.AMap.Size(26, 31), //图标大小
				offset: new this.AMap.Pixel(-12, -28)
			});
			this.markers.push(marker);
			this.map.setFitView(marker);
			this.map.setCenter(lngLat);
		}
	},
	render() {
		const lngDirectives = [
			{
				name: 'input-money',
				modifiers: this.defaultModifiers,
				arg: '3_10' // 数字的整数位和小数位
			}
		];
		const latDirectives = [
			{
				name: 'input-money',
				modifiers: this.defaultModifiers,
				arg: '2_10' // 数字的整数位和小数位
			}
		];
		return (
			<div class="map-location">
				<el-row gutter={20}>
					<el-col span={12}>
						<span>经度：</span>
						<el-input
							readonly="readonly"
							v-model={this.lng}
							placeholder="请输入经度"
							min={0}
							{...{
								directives: lngDirectives
							}}
						/>
					</el-col>
					<el-col span={12}>
						<span>纬度：</span>
						<el-input
							readonly="readonly"
							v-model={this.lat}
							placeholder="请输入纬度"
							min={0}
							{...{
								directives: latDirectives
							}}
						/>
					</el-col>
				</el-row>
				<div class="map-location-container " style={`height:${this.$attrs.height}`}>
					<div id="mapContainer"></div>
					<input
						id="mapInput"
						vModel={this.searchValue}
						placeholder="请输入地名关键字"
						clearable
						class="map-location-container__search"
					></input>
				</div>
			</div>
		);
	}
};
</script>

<style lang="scss" scoped>
.map-location {
	.el-row {
		display: flex;
		align-items: center;
		margin-bottom: 10px;
	}
	.el-col {
		display: flex;
		align-items: center;
		span {
			flex-shrink: 0;
		}
	}
	&-container {
		width: 100%;
		height: 350px;
		position: relative;
		&__search {
			width: 300px;
			position: absolute;
			left: 10px;
			top: 10px;
			-webkit-appearance: none;
			background-color: #fff;
			background-image: none;
			border-radius: 3px;
			border: 1px solid #d9d9d9;
			-webkit-box-sizing: border-box;
			box-sizing: border-box;
			color: rgba(0, 0, 0, 0.75);
			display: inline-block;
			font-size: 13px;
			height: 32px;
			line-height: 32px;
			outline: 0;
			padding: 0 15px;
			-webkit-transition: border-color 0.2s cubic-bezier(0.645, 0.045, 0.355, 1);
			transition: border-color 0.2s cubic-bezier(0.645, 0.045, 0.355, 1);
		}
		#mapContainer {
			width: 100%;
			height: 100%;
		}
	}
}
</style>
