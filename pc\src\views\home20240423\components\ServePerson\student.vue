<template>
	<div
		class="page flex-col"
		:style="{
			transform: `scale(${scale})`
		}"
	>
		<div class="group_1">
			<div class="block_1 flex-col">
				<div class="box_3 card flex-row">
					<headAvator
						:own-id="userInfo.id"
						:user-id="userInfo.id"
						class="section_2 flex-col"
						@click="handleButton('draw')"
					/>
					<div class="section_3 flex-col justify-between">
						<div class="box_4 flex-row">
							<span class="text_4">{{ detail.xm }}</span>
							<div class="text-wrapper_1">
								<span class="text_5">性别&nbsp;-</span>
								<span class="text_6">{{ detail.xb }}</span>
							</div>
							<div class="text-wrapper_2">
								<span class="text_7">民族&nbsp;-</span>
								<span class="text_8">{{ detail.mz || detail.mzmc }}</span>
							</div>
							<div class="text-wrapper_3">
								<span class="text_9">生源地&nbsp;-</span>
								<span class="text_10">{{ detail.syd }}</span>
							</div>
						</div>
						<div class="box_5 flex-col">
							<div class="block_box flex-row">
								<div class="text-wrapper">
									<span class="text_1">学号：</span>
									<span class="text_2">{{ detail.xh || userInfo.code }}</span>
								</div>
								<div class="text-wrapper">
									<span class="text_1">出生日期：</span>
									<span class="text_2">{{ detail.csrq }}</span>
								</div>
								<div class="text-wrapper">
									<span class="text_1">政治面貌：</span>
									<span class="text_2">{{ detail.zzmm }}</span>
								</div>
								<div class="text-wrapper">
									<span class="text_1">入学来源：</span>
									<span class="text_2">{{ detail.rxly }}</span>
								</div>
								<div class="text-wrapper">
									<span class="text_1">健康状况：</span>
									<span class="text_2">{{ detail.jkzk }}</span>
								</div>
							</div>
							<div class="block_box flex-row">
								<div class="text-wrapper">
									<span class="text_1">联系电话：</span>
									<span class="text_2">{{ detail.lxdh }}</span>
								</div>
								<div class="text-wrapper">
									<span class="text_1">院系：</span>
									<span class="text_2">{{ detail.yx }}</span>
								</div>

								<div class="text-wrapper">
									<span class="text_1">专业：</span>
									<span class="text_2">{{ detail.zy }}</span>
								</div>
								<div class="text-wrapper">
									<span class="text_1">班级：</span>
									<span class="text_2">{{ detail.bj }}</span>
								</div>
								<div class="text-wrapper">
									<span class="text_1">宿舍：</span>
									<span class="text_2">{{ detail.ss }}</span>
								</div>
							</div>
							<div class="block_box flex-row">
								<div class="text-wrapper">
									<span class="text_1">地址：</span>
									<span class="text_2">{{ detail.dz }}</span>
								</div>
								<div class="text-wrapper">
									<span class="text_1">阳光指数：</span>
									<span class="text_2">
										<el-rate
											v-model="value1"
											allow-half
											disabled
											:colors="['#FF9900', '#FF9900', '#FF9900']"
											:icon-classes="['el-icon-sunny', 'el-icon-sunny', 'el-icon-sunny']"
											void-icon-class="el-icon-sunny"
											disabled-void-icon-class="el-icon-sunny"
											disabled-void-color="#ddd"
										></el-rate>
									</span>
								</div>
								<div class="text-wrapper">
									<span class="text_1">学力指数：</span>
									<span class="text_2">
										<el-rate
											v-model="value2"
											allow-half
											disabled
											:colors="['#FF9900', '#FF9900', '#FF9900']"
											:icon-classes="['el-icon-star-off', 'el-icon-star-off', 'el-icon-star-off']"
											void-icon-class="el-icon-star-off"
											disabled-void-icon-class="el-icon-star-off"
											disabled-void-color="#ddd"
										></el-rate>
									</span>
								</div>
							</div>
						</div>
					</div>
				</div>
			</div>
			<div class="block-box card flex-col">
				<div class="text-wrapper flex-row">
					<span class="text">我的成绩</span>
					<span class="more-bottom" @click="onPopup('成绩')">
						更多
						<i class="el-icon-arrow-right"></i>
					</span>
				</div>
				<div class="content">
					<es-data-table
						:data.sync="tableData"
						:fit="true"
						:thead="thead"
						size="medium "
						:border="'none'"
						:page="false"
						close
					></es-data-table>
				</div>
			</div>
			<div class="block-chart">
				<div class="chart1 card">
					<div class="text-wrapper flex-row">
						<span class="text">综合评价</span>
					</div>
					<div id="myEchart1"></div>
				</div>
				<div class="chart2 card">
					<div class="text-wrapper flex-row">
						<span class="text">消费情况</span>
					</div>
					<div class="value-info">
						<div class="info-item">
							当日消费：
							<span class="value">{{ xfqk.drxf || 0 }}元</span>
						</div>
						<div class="info-item">
							当月消费：
							<span class="value">{{ xfqk.dyxf || 0 }}元</span>
						</div>
						<div class="info-item">
							本年消费：
							<span class="value">{{ xfqk.bnxf || 0 }}元</span>
						</div>
					</div>
					<div class="mydict">
						<div>
							<label
								@change="
									() => {
										typeEchart = 'day';
										toolChart2('myEchart2', 1);
									}
								"
							>
								<input type="radio" name="radio" checked="" />
								<span>天</span>
							</label>
							<label
								@change="
									() => {
										typeEchart = 'month';
										toolChart2('myEchart2', 1);
									}
								"
							>
								<input type="radio" name="radio" />
								<span>月</span>
							</label>
						</div>
					</div>
					<div id="myEchart2"></div>
				</div>
			</div>
		</div>
		<PopEmployment ref="refPopup" :title="title" />
	</div>
</template>
<script>
import { xodbApi2 } from '@/api/xodb';
import headAvator from '../../header/headAvator.vue';
import PopEmployment from '../../components/cockpit/pop-employment.vue';
export default {
	components: {
		headAvator,
		PopEmployment
	},
	data() {
		return {
			value1: 4,
			value2: 4.5,
			detail: {},
			userInfo: {},
			username: '',
			scale: 1,
			title: '', //弹窗的标题
			visible: true,

			thead: [
				{
					title: '学年',
					field: 'xn',
					showOverflowTooltip: true
				},
				{
					title: '学期',
					field: 'xqdm',
					showOverflowTooltip: true
				},
				{
					title: '课程名称',
					field: 'kcmc',
					showOverflowTooltip: true
				},
				{
					title: '学分',
					field: 'xf',
					showOverflowTooltip: true
				},
				{
					title: '成绩',
					field: 'cj',
					showOverflowTooltip: true
				},
				{
					title: '绩点',
					field: 'jd',
					showOverflowTooltip: true
				},
				{
					title: '课程性质',
					field: 'kcxz',
					showOverflowTooltip: true
				},
				{
					title: '考试性质',
					field: 'ksxz',
					showOverflowTooltip: true
				}
			],
			tableData: [],

			typeEchart: 'day', // 天-day 月-month
			dataRes: {},

			myChart: [],
			// 消费情况
			xfqk: {}
		};
	},
	computed: {
		loginUserInfo() {
			return JSON.parse(localStorage.getItem('loginUserInfo')) || {};
		}
	},
	created() {
		this.scale = this.$utils.toolViewRatio();
		this.userInfo = JSON.parse(localStorage.getItem('loginUserInfo')) || {};
		this.username = this.userInfo && this.userInfo.name ? this.userInfo.name : '';
	},
	mounted() {
		this.getInfo('/ybzy/platuser/front/getAllInfo'); //教职工基本数据_查询
		this.getDataN('ads_xx_xsjbqk_query', 'detail'); // 学生基本情况查询服务
		this.getDataN('ads_jx_xskscjb_query', 'tableData'); // 学生考试成绩表查询服务
		this.getDataXf('ads_jx_xsxfqk_query'); //学生消费情况数据_查询
		window.addEventListener('resize', this.onResize);
		this.toolChart('myEchart1', 0);
		this.toolChart2('myEchart2', 1);
		// 特殊处理假数据
		setTimeout(() => {
			this.toolData();
		}, 1200);
	},
	beforeDestroy() {
		window.removeEventListener('resize', this.onResize);
		this.myChart.forEach(item => {
			if (item) {
				item.dispose();
			}
		});
	},
	methods: {
		toolData() {
			const name = this.loginUserInfo.name;
			let obj = {};
			if (name === '杨沁和') {
				obj = {
					syd: '四川省资中县',
					rxly: '全国统考',
					lxdh: '19108342975',
					ss: '梦溪阁',
					dz: '四川省资中县龙结中学'
				};
			}
			this.detail = { ...this.detail, ...obj };
		},
		onResize() {
			this.myChart.forEach(item => {
				if (item) {
					item.resize();
				}
			});
		},
		async getDataXf(url) {
			const LeaderRole = localStorage.getItem('LeaderRole');
			try {
				const {
					data: { list }
				} = await xodbApi2.post({
					url: url,
					pageSize: 999,
					params: {
						xh: this.loginUserInfo.code,
						xyjgbh: LeaderRole === 'xLeader' ? '1' : this.loginUserInfo.orgCode
					}
				});
				this.xfqk = list[0] || {};
			} catch (error) {
				console.error('处理数据失败:', error);
			}
		},
		async getDataN(url, listName) {
			const LeaderRole = localStorage.getItem('LeaderRole');
			try {
				const {
					data: { list }
				} = await xodbApi2.post({
					url,
					params: {
						// xyjgbh: '1',
						// xh: '*********'
						xyjgbh: LeaderRole === 'xLeader' ? '1' : this.loginUserInfo.orgCode,
						xh: this.loginUserInfo.code
					}
				});
				const data = list[0] || {};
				switch (listName) {
					case 'detail':
						this[listName] = { ...this[listName], ...data };
						break;
					case 'tableData':
						this[listName] = list;
						break;
				}
			} catch (error) {
				console.error(`处理数据失败${url}:`, error);
			}
		},
		toolChart(dom, i) {
			let chartDom = document.getElementById(dom);
			if (this.myChart[i]) {
				this.myChart[i].clear();
			} else {
				this.myChart[i] = this.$echarts.init(chartDom);
			}
			const sData1 = [80, 50, 55, 80, 50, 80];
			const sData2 = [70, 80, 65, 60, 70, 30];
			const sData3 = [60, 60, 65, 60, 70, 40];

			const option = {
				color: ['rgba(0,183,238, 1)', 'rgba(86,199,60, 1)'],
				tooltip: {
					show: true,
					trigger: 'item'
				},
				legend: {
					show: true,
					// icon: 'circle',
					right: '5%',
					top: 'center',
					orient: 'vertical',
					itemGap: 30,
					textStyle: {
						fontSize: 15,
						color: 'rgba(64,64,64,1)'
					},
					data: ['个人得分', '班平均分', '班最高分']
				},
				radar: {
					center: ['45%', '55%'],
					radius: '80%',
					startAngle: 90,
					splitNumber: 4,
					// shape: 'circle',
					splitArea: {
						areaStyle: {
							color: ['transparent']
						}
					},
					axisLabel: {
						show: false,
						fontSize: 20,
						color: 'rgba(64,64,64,1)',
						fontStyle: 'normal',
						fontWeight: 'normal'
					},
					axisLine: {
						show: true,
						lineStyle: {
							type: 'dashed',
							color: 'rgba(64,64,64,.7)'
						}
					},
					splitLine: {
						show: true,
						lineStyle: {
							type: 'dashed',
							color: 'rgba(64,64,64,.7)'
						}
					},
					indicator: [
						{
							name: '美德星',
							max: 100
						},
						{
							name: '健体星',
							max: 100
						},
						{
							name: '尚美星',
							max: 100
						},
						{
							name: '智慧星',
							max: 100
						},
						{
							name: '乐劳星',
							max: 100
						}
					]
				},
				series: [
					{
						name: '个人得分',
						type: 'radar',
						symbol: 'circle',
						symbolSize: 8,
						areaStyle: {
							normal: {
								color: 'rgba(86,199,60, .5)'
							}
						},
						itemStyle: {
							color: 'rgba(86,199,60, 1)',
							borderColor: 'rgba(86,199,60, 0.3)',
							borderWidth: 5
						},
						lineStyle: {
							normal: {
								color: 'rgba(86,199,60, 1)',
								width: 1
							}
						},
						data: [sData1]
					},
					{
						name: '班平均分',
						type: 'radar',
						symbol: 'circle',
						symbolSize: 8,
						itemStyle: {
							normal: {
								color: 'rgb(255, 153, 0)',
								borderColor: 'rgba(255, 153, 0,.3)',
								borderWidth: 5
							}
						},
						areaStyle: {
							normal: {
								color: 'rgba(255, 153, 0,.5)'
							}
						},
						lineStyle: {
							normal: {
								color: 'rgb(255, 153, 0)',
								width: 1
							}
						},
						data: [sData2]
					},
					{
						name: '班最高分',
						type: 'radar',
						symbol: 'circle',
						symbolSize: 8,
						itemStyle: {
							normal: {
								color: 'rgba(0,183,238, 1)',
								borderColor: 'rgba(0,183,238, 0.3)',
								borderWidth: 5
							}
						},
						areaStyle: {
							normal: {
								color: 'rgba(0,183,238, .5)'
							}
						},
						lineStyle: {
							normal: {
								color: 'rgba(0,183,238, 1)',
								width: 1
							}
						},
						data: [sData3]
					}
				]
			};
			option && this.myChart[i].setOption(option);
		},
		async toolChart2(dom, i) {
			const LeaderRole = localStorage.getItem('LeaderRole');
			let chartDom = document.getElementById(dom);
			if (this.myChart[i]) {
				this.myChart[i].clear();
			} else {
				this.myChart[i] = this.$echarts.init(chartDom);
			}
			let xData = [];
			let yData = [];
			try {
				if (this.typeEchart === 'day') {
					// 获取进7天的数据
					const {
						data: { list }
					} = await xodbApi2.post({
						url: 'ads_jx_xsjqrxfsj_query',
						pageSize: 999,
						params: {
							xh: this.loginUserInfo.code,
							xyjgbh: LeaderRole === 'xLeader' ? '1' : this.loginUserInfo.orgCode
						}
					});
					if (list && list.length > 0) {
						const data = list[0] || {};
						xData = [data.day6, data.day5, data.day4, data.day3, data.day2, data.day1, data.day0];
						yData = [
							data.xfje6,
							data.xfje5,
							data.xfje4,
							data.xfje3,
							data.xfje2,
							data.xfje1,
							data.xfje0
						];
					}
				} else {
					// 获取进7月的数据
					const {
						data: { list }
					} = await xodbApi2.post({
						url: 'ads_jx_xsjqyxfsj_query',
						pageSize: 999,
						params: {
							xh: this.loginUserInfo.code,
							xyjgbh: LeaderRole === 'xLeader' ? '1' : this.loginUserInfo.orgCode
						}
					});
					if (list && list.length > 0) {
						const data = list[0] || {};
						xData = [data.day6, data.day5, data.day4, data.day3, data.day2, data.day1, data.day0];
						yData = [
							data.xfje6,
							data.xfje5,
							data.xfje4,
							data.xfje3,
							data.xfje2,
							data.xfje1,
							data.xfje0
						];
					}
				}
			} catch (error) {
				console.error('处理数据失败:', error);
			}

			// 数据为空
			if (!xData.length) {
				yData = [0, 0, 0, 0, 0, 0, 0];
				if (this.typeEchart === 'day') {
					// 获取进7天的数据
					const days = 7;
					const dateList = [];

					// 当前日期
					const currentDate = new Date();

					for (let i = 0; i < days; i++) {
						// 创建一个新的日期对象，并设置为当前日期减去i天
						const pastDate = new Date(currentDate);
						pastDate.setDate(pastDate.getDate() - (days - 1 - i)); // 注意这里的变化

						// 格式化日期为 YYYY-MM-DD
						const formattedDate = `${pastDate.getFullYear()}-${(pastDate.getMonth() + 1)
							.toString()
							.padStart(2, '0')}-${pastDate.getDate().toString().padStart(2, '0')}`;

						// 添加到数组中
						dateList.push(formattedDate);
					}

					xData = dateList;
				} else {
					// 获取进7月的数据
					const currentDate = new Date();
					const months = 7;
					const monthList = [];

					for (let i = 0; i < months; i++) {
						// 创建一个新的日期对象，并设置为当前日期减去i月
						const pastDate = new Date(currentDate);
						pastDate.setMonth(currentDate.getMonth() - (months - 1 - i));

						// 格式化日期为 YYYY-MM
						const formattedMonth = `${pastDate.getFullYear()}-${(pastDate.getMonth() + 1)
							.toString()
							.padStart(2, '0')}`;

						// 添加到数组中
						monthList.push(formattedMonth);
					}

					xData = monthList;
				}
			}

			const option = {
				tooltip: {
					trigger: 'axis',
					axisPointer: {
						type: 'shadow'
					}
				},
				grid: {
					top: '25%',
					right: '3%',
					left: '8%',
					bottom: '10%'
				},
				xAxis: [
					{
						type: 'category',
						data: xData,
						axisLine: {
							lineStyle: {
								color: '#D1D9EB'
							}
						},
						axisLabel: {
							margin: 10,
							color: '#A1A7B3',
							textStyle: {
								fontSize: 14
							}
						},
						axisTick: {
							show: false
						}
					}
				],
				yAxis: [
					{
						type: 'value',
						name: '元',
						nameTextStyle: {
							color: '#C1C6CF',
							fontSize: 14,
							align: 'right',
							padding: 5
						},
						axisLabel: {
							formatter: '{value}',
							color: '#A1A7B3',
							fontSize: 14
						},
						axisTick: {
							show: false
						},
						axisLine: {
							show: false
						},
						splitLine: {
							lineStyle: {
								color: '#D1D9EB',
								type: 'dashed'
							}
						}
					}
				],
				series: [
					{
						type: 'bar',
						data: yData,
						barWidth: '24px',
						itemStyle: {
							normal: {
								color: new this.$echarts.graphic.LinearGradient(0, 0, 0, 1, [
									{
										offset: 0,
										color: '#5DBBF2'
									},
									{
										offset: 1,
										color: '#4293FD'
									}
								])
							},
							barBorderRadius: [2, 2, 0, 0]
						}
					}
				]
			};
			option && this.myChart[i].setOption(option);
		},

		getInfo(url) {
			this.$request({ url: url, method: 'GET' }).then(res => {
				if (res.rCode === 0) {
					this.detail = { ...this.detail, ...(res.results || {}) };
					return;
				}
			});
		},
		onPopup(title) {
			this.title = title;
			this.$refs.refPopup.dialogVisible = true;
		}
	}
};
</script>
<style scoped lang="scss" src="@/assets/style/common.scss" />
<style scoped lang="scss" src="./assets/index.scss" />
<style lang="scss" scoped>
// 学生画像
.block-box {
	min-height: 174px;
	width: 1892px;
	margin-left: 14px;
	margin-top: 20px;
	padding: 22px;

	.content {
		margin-top: 15px;
	}
}
.block-chart {
	width: calc(100% - 28px);
	margin: 22px 14px;
	display: flex;
	justify-content: space-between;
	.chart1,
	.chart2 {
		padding: 22px;
		width: 49.5%;
		height: 400px;
		position: relative;
		.value-info {
			position: absolute;
			top: 47px;
			left: 0px;
			right: 0px;
			height: 30px;
			display: flex;
			align-items: center;
			justify-content: center;
			.info-item {
				padding: 5px 10px;
				display: flex;
				margin: 5px;
				cursor: pointer;
				overflow-wrap: break-word;
				color: rgb(41, 77, 121);
				font-size: 16px;
				font-family: MicrosoftYaHei;
				font-weight: NaN;
				text-align: left;
				white-space: nowrap;
				.value {
					color: rgb(69, 69, 69);
				}
			}
		}

		.mydict {
			position: absolute;
			top: 10px;
			right: 10px;
			:focus {
				outline: 0;
				border-color: #2260ff;
				box-shadow: 0 0 0 4px #b5c9fc;
			}
			div {
				display: flex;
				flex-wrap: wrap;
				margin-top: 0.5rem;
				justify-content: center;
			}

			input[type='radio'] {
				clip: rect(0 0 0 0);
				clip-path: inset(100%);
				height: 1px;
				overflow: hidden;
				position: absolute;
				white-space: nowrap;
				width: 1px;
			}

			input[type='radio']:checked + span {
				box-shadow: 0 0 0 0.0625em #0043ed;
				background-color: #dee7ff;
				z-index: 1;
				color: #0043ed;
			}
		}

		label {
			span {
				display: block;
				cursor: pointer;
				background-color: #fff;
				padding: 3px 10px;
				position: relative;
				margin-left: 0.0625em;
				box-shadow: 0 0 0 0.0625em #b5bfd9;
				letter-spacing: 0.05em;
				color: #3e4963;
				text-align: center;
				transition: background-color 0.5s ease;
			}
			&:first-child {
				span {
					border-radius: 5px 0 0 5px;
				}
			}

			&:last-child {
				span {
					border-radius: 0 5px 5px 0;
				}
			}
		}
	}
	#myEchart1,
	#myEchart2 {
		// padding: 22px;
		width: 100%;
		height: calc(100% - 28px);
	}
}
.text-wrapper {
	display: flex;
	justify-content: space-between;
	.text {
		height: 24px;
		overflow-wrap: break-word;
		color: rgba(5, 59, 109, 1);
		font-size: 18px;
		font-family: MicrosoftYaHei-Bold;
		font-weight: 700;
		line-height: 24px;
	}
	.more-bottom {
		padding: 3px 6px 4px;
		background: #409eff;
		border-radius: 4px;
		color: #fff;
		cursor: pointer;
	}
}
::v-deep .content {
	.el-table__row .colseCellName .cell {
		height: 60px;
		overflow: auto;
	}
	.el-table .cell {
		padding: 0 !important;
	}
	.el-table th,
	.el-table tr {
		background-color: #ecf6ff !important;
	}
	.el-table--border th,
	.el-table--border td {
		border: 1px solid #d2dee2;
	}
}
</style>
