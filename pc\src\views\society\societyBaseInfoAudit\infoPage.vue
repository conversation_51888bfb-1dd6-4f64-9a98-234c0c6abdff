<template>
	<div class="sketch_content">
		<el-collapse v-model="activeName">
			<el-collapse-item title="社团信息" :name="1">
				<el-form :model="formData" class="resumeTable" :rules="rules" ref="form">
					<el-row>
						<el-col :span="7">
							<el-form-item label="社团名称" label-width="120px" label-position="left" prop="name">
								<el-input v-model="formData.name" :disabled="infoDisabled"></el-input>
							</el-form-item>
						</el-col>
						<el-col :span="7">
							<el-form-item label="社长" label-width="120px" label-position="left" prop="principal">
								<el-select
									ref="type"
									v-model="formData.principal"
									value-key="value"
									placeholder="输入姓名可查询"
									filterable
									clearable
									remote
									:disabled="infoDisabled"
								></el-select>
							</el-form-item>
						</el-col>
					</el-row>
					<el-row>
						<el-col :span="7">
							<el-form-item
								label="教师所属学院"
								label-width="120px"
								label-position="left"
								prop="college"
							>
								<el-select
									ref="type"
									v-model="formData.college"
									value-key="value"
									:disabled="auditDisabled"
									placeholder="请选择所属学院"
									clearable
									:remote-method="getCollege"
									@change="collegeChange"
									remote
								>
									<el-option
										v-for="item in collegeList"
										:key="item.value"
										:label="item.label"
										:value="item.value"
									></el-option>
								</el-select>
							</el-form-item>
						</el-col>
						<!-- <el-col :span="7">
							<el-form-item label="所属专业" label-width="120px" label-position="left" prop="major">
								<el-select
									ref="type"
									v-model="formData.major"
									value-key="value"
									placeholder="请选择所属专业"
									clearable
									:remote-method="getMajor"
									remote
									:disabled="auditDisabled"
								>
									<el-option
										v-for="item in majorList"
										:key="item.value"
										:label="item.label"
										:value="item.value"
									></el-option>
								</el-select>
							</el-form-item>
						</el-col> -->
						<el-col :span="7">
							<el-form-item
								label="指导老师"
								label-width="120px"
								label-position="left"
								prop="teacherId"
							>
								<el-select
									ref="type"
									multiple
									v-model="formData.teacherId"
									value-key="value"
									:disabled="auditDisabled"
									placeholder="请选择教师"
									clearable
									:remote-method="getTeacher"
									filterable
									remote
								>
									<el-option
										v-for="item in teacherList"
										:key="item.value"
										:label="item.label"
										:value="item.value"
									></el-option>
								</el-select>
							</el-form-item>
						</el-col>
					</el-row>
					<el-row>
						<el-col :span="7">
							<el-form-item
								label="社团类型"
								label-width="120px"
								label-position="left"
								prop="typeId"
							>
								<el-select
									ref="type"
									v-model="formData.typeId"
									value-key="value"
									:disabled="auditDisabled"
									clearable
									:remote-method="getSocType"
									filterable
									remote
								>
									<el-option
										v-for="item in socTypeList"
										:key="item.value"
										:label="item.label"
										:value="item.value"
									></el-option>
								</el-select>
							</el-form-item>
						</el-col>
						<el-col :span="7">
							<el-form-item
								label="人数上限"
								label-width="120px"
								label-position="left"
								prop="memberMax"
							>
								<el-input-number
									v-model="formData.memberMax"
									:disabled="auditDisabled"
								></el-input-number>
							</el-form-item>
						</el-col>
						<el-col :span="7">
							<el-form-item
								label="社团状态"
								label-width="120px"
								label-position="left"
								prop="statusVO"
							>
								<el-input v-model="formData.statusVO" :disabled="true"></el-input>
							</el-form-item>
						</el-col>
						<el-col :span="16">
							<el-form-item label="社团老师申请表" label-width="120px" prop="fileList">
								<es-upload
									v-bind="fileAttrs"
									v-model="formData.fileList"
									:readonly="auditDisabled"
								></es-upload>
							</el-form-item>
						</el-col>
					</el-row>
					<el-row>
						<el-col :span="16">
							<el-form-item
								label="社团封面"
								label-width="120px"
								label-position="left"
								prop="activityContent"
							>
								<es-upload
									:class="{ isShowAdd: formData.coverUrl }"
									v-bind="coverAttrs"
									v-model="formData.coverUrl"
									:disabled="true"
									select-type="icon-plus"
									list-type="picture-card"
									ref="upload"
								></es-upload>
							</el-form-item>
						</el-col>
						<el-col :span="6">
							<el-form-item
								label="申请时间"
								label-width="120px"
								label-position="left"
								prop="createTime"
							>
								<el-date-picker
									v-model="formData.createTime"
									type="datetime"
									:disabled="true"
								></el-date-picker>
							</el-form-item>
						</el-col>
					</el-row>
					<el-row>
						<el-col :span="22">
							<el-form-item
								label="社团简介"
								label-width="90px"
								label-position="left"
								prop="introduction"
							>
								<el-input
									v-model="formData.introduction"
									type="textarea"
									:autosize="true"
								></el-input>
							</el-form-item>
						</el-col>
					</el-row>
					<el-row>
						<el-col :span="3" style="float: right">
							<el-button type="reset" @click="infoPageClose(false)">取消</el-button>
						</el-col>
						<el-col :span="3" style="float: right">
							<el-button
								type="primary"
								@click="handleFormAudit('审核通过')"
								v-show="auditBtnVisible"
							>
								审核通过
							</el-button>
						</el-col>
						<el-col :span="3" style="float: right">
							<el-button type="danger" @click="handleFormAudit('驳回')" v-show="auditBtnVisible">
								驳回
							</el-button>
						</el-col>
					</el-row>
				</el-form>
			</el-collapse-item>
		</el-collapse>
	</div>
</template>
<script>
import api from '@/http/society/societyBaseInfoAudit/api';

export default {
	name: 'infoPage',
	props: {
		baseData: {
			type: Object
		},
		infoPageMode: {
			type: String
		}
	},
	data() {
		return {
			infoDisabled: true,
			auditDisabled: false,
			auditVisible: true,
			auditBtnVisible: false,
			formData: {},
			pageMode: 'allOn',
			activeName: [1],
			socTypeList: [],
			teacherList: [],
			studentSelectList: [],
			collegeList: [],
			majorList: [],
			toolbar: [
				{
					type: 'search',
					reset: true,
					contents: [
						{
							type: 'text',
							name: 'keyword',
							placeholder: '姓名、班级、学院',
							clearable: true
						}
					]
				}
			],
			rules: {
				college: [{ required: true, message: '请选择所属学院', trigger: 'blur' }],
				auditOpinion: [{ required: true, message: '请输入审核意见', trigger: 'blur' }]
			}
		};
	},
	computed: {
		fileAttrs() {
			return {
				code: 'society_base_info_teacher_form',
				// code: 'society_base_info_cover',
				ownId: this.formData.id,
				preview: true,
				download: true
				// operate: true,
			};
		},
		coverAttrs() {
			return {
				code: 'society_base_info_cover',
				ownId: this.formData.id,
				preview: true,
				download: true,
				operate: true
			};
		}
	},
	created() {
		this.formData = { ...this.baseData };
		this.pageMode = this.infoPageMode;
		switch (this.pageMode) {
			case '查看':
				this.infoDisabled = true;
				this.auditVisible = true;
				this.auditDisabled = true;
				this.auditBtnVisible = false;
				break;
			case '审核':
				this.infoDisabled = true;
				this.auditVisible = true;
				this.auditDisabled = false;
				this.auditBtnVisible = true;
				break;
		}
		// if(this.pageMode === '审核') {
		this.getCollege();
		this.getSocType();
		let collegeCode = 'college' in this.formData ? this.formData.college.value : null;
		if (collegeCode != null && collegeCode !== '') {
			this.getMajor(collegeCode);
			this.getTeacher(collegeCode);
		}
		// }
	},
	methods: {
		handleFormAudit(res) {
			//校验
			this.$refs['form'].validate(valid => {
				//开启校验
				if (valid) {
					// 如果校验通过，请求接口
					let btnType = res;
					// 处理请求数据
					let auditData = {};
					auditData.id = this.formData.id;
					auditData.name = this.formData.name;
					if (typeof this.formData.principal == 'object')
						auditData.principal = this.formData.principal.value;
					else auditData.principal = this.formData.principal;
					if (typeof this.formData.college == 'object')
						auditData.collegeId = this.formData.college.value;
					else auditData.collegeId = this.formData.college;
					if (this.formData.typeId != null && typeof this.formData.typeId == 'object')
						auditData.typeId = this.formData.typeId.value;
					else auditData.typeId = this.formData.typeId;
					auditData.auditOpinion = this.formData.auditOpinion;
					auditData.teacherId = this.formData?.teacherId?.join(',');
					this.$confirm('是否确认' + btnType + '？', '审核', {
						confirmButtonText: '确定',
						cancelButtonText: '取消',
						type: 'warning'
					})
						.then(() => {
							// 处理请求数据
							switch (btnType) {
								case '审核通过':
									auditData.auditStatus = 2;
									break;
								case '驳回':
									auditData.auditStatus = 3;
									break;
							}

							if (auditData.auditStatus !== -1) {
								this.$request({
									url: api.societyBaseInfoAuditAudit,
									data: auditData,
									method: 'POST'
								}).then(response => {
									if (response.success) {
										this.$message.success('审核成功');
										this.infoPageClose(true);
									} else {
										this.$message.error(response.msg);
									}
								});
							}
						})
						.catch(() => {});
				} else {
					return false;
				} //校验不通过
			});
		},
		infoPageClose(reload) {
			this.pageMode = 'allOn';
			this.infoDisabled = false;
			this.$emit('activelyClose', reload);
		},
		getSocType(res) {
			if (res !== null) {
				this.$request({
					url: api.societyTypeSelectList,
					params: { typeName: res },
					method: 'GET'
				}).then(result => {
					if (result.success) {
						this.socTypeList = result.results.records;
					} else {
						this.$message.error('操作失败');
					}
				});
			}
		},
		getTeacher(res) {
			this.$request({
				url: api.teacherSelectList,
				params: { xydm: res },
				method: 'GET'
			}).then(result => {
				if (result.success) {
					this.teacherList = result.results;
				} else {
					this.$message.error('操作失败');
				}
			});
		},
		getMajor(res) {
			this.$request({
				url: api.majorSelectList,
				params: { collegeCode: res },
				method: 'POST'
			}).then(result => {
				if (result.success) {
					this.majorList = result.results;
				} else {
					this.$message.error('操作失败');
				}
			});
		},
		getCollege() {
			this.$request({
				url: api.collegeSelectList,
				method: 'POST'
			}).then(result => {
				if (result.success) {
					this.collegeList = result.results;
				} else {
					this.$message.error('操作失败');
				}
			});
		},
		collegeChange(res) {
			this.getMajor(res);
			this.getTeacher(res);
			this.$delete(this.formData, 'teacherId');
		}
	},
	watch: {}
};
</script>
<style lang="scss" scoped>
.sketch_content {
	overflow: auto;
	height: 100%;
	border-top: 1px solid #eff1f4;
	border-bottom: 1px solid #eff1f4;
	padding: 0 30px 11px 27px;
}
.resumeTable td {
	height: 50px;
	font-weight: bold;
}

::v-deep .el-collapse-item__header.is-active {
	border-bottom: 1px solid #ebeef5;
	font-size: 18px;
	font-weight: bold;
}
::v-deep .el-collapse-item__header::before {
	content: '';
	width: 4px;
	height: 18px;
	background-color: #0076e9;
	margin-right: 2px;
}
.isShowAdd {
	::v-deep .el-upload--handle {
		display: none;
	}
}
</style>
