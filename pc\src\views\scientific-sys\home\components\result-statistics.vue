<template>
	<div class="result">
		<div class="result-title">
			<div class="result-title-icon"></div>
			<div class="result-title-text">成果统计</div>
		</div>
		<div class="result-content">
			<echartsStatistics
				:results="resultPies"
				:echartDom="'resultPies'"
				:year="year"
				@transfer="handlePie"
			></echartsStatistics>
			<echartsStatistics
				:results="resultOther"
				:echartDom="'resultOther'"
				@changeDate="handleHistogram"
			></echartsStatistics>
		</div>
	</div>
</template>

<script>
import EchartsStatistics from './e-pie.vue';
import { projectClassifyStatistics, projectStatisticsByGroup } from '@/api/scientific-sys.js';

export default {
	components: {
		EchartsStatistics
	},
	name: 'ResultStatistics',
	data() {
		return {
			// piesResults: {}, //存储第一个饼图数据
			// allResults: {}, //存储第一个柱形图数据
			resultPies:{},
			resultOther:{},
			urlList: '',
			year: '2023'
		};
	},
	mounted() {
		// this.handlePie(); //获取饼形图数据
		// this.handleHistogram(); //获取柱状图数据
	},
	methods: {
		async handlePie(setYear = '2023') {
			const loading = this.load('查询中...');
			try {
				let { rCode, msg, results } = await this.$.ajax({
					url: projectClassifyStatistics,
					method: 'POST',
					data: { year: setYear },
					format: true
				});
				if (rCode == 0) {
					// this.piesResults = resultsTest.results;
					this.piesResults = results;
				} else {
					this.$message.error(msg);
				}
			} catch {
				this.$message.error('查询失败');
			} finally {
				loading.close();
			}
		},
		// 首次加载获取图表数据
		async getData() {
			const loading = this.load('查询中...');
			let arr = ['allResults', 'longitudinalResults', 'transverseResults', 'facultyResults'];
			for (var i = 0; i < 4; i++) {
				await this.handleHistogram(1, '', arr[i]);
			}
			loading.close();
		},
		async handleHistogram(statusYear = 1, statusMonth, keyName) {
			const loading = this.load('查询中...');
			const KEY_NAME = {
				allResults: 0,
				longitudinalResults: 1,
				transverseResults: 2,
				facultyResults: 3
				// longitudinalResults: 1,
				// longitudinalResults: 1
			};
			let histogramType = KEY_NAME[keyName];
			try {
				let { rCode, msg, results } = await this.$.ajax({
					url: projectStatisticsByGroup,
					method: 'POST',
					data: { type: histogramType, year: statusYear, month: statusMonth },
					format: true
				});
				if (rCode == 0) {
					// console.log('测试数据>>>>>', resultsTest2.results);
					switch (histogramType) {
						case 0:
							// results = resultsTest0.results;
							let xData = [],
								seriesData = [
									{ name: '院级项目', data: [] },
									{ name: '横向项目', data: [] },
									{ name: '纵向项目', data: [] }
								];
							results.forEach(item => {
								xData.push(item.year || item.month);
								seriesData[0].data.push(item.collegeCount);
								seriesData[1].data.push(item.crosswiseCount);
								seriesData[2].data.push(item.lengthWaysCount);
							});

							this.allResults = { xData:xData.length?xData:['暂无数据'], seriesData };
							console.log('>>>allResults', this.allResults);
							break;
						case 1:
							let xData1 = [],
								seriesData1 = [
									{ name: '市级项目', data: [] },
									{ name: '省级项目', data: [] },
									{ name: '国家级项目', data: [] }
								];
							results.forEach(item => {
								xData1.push(item.year || item.month);
								seriesData1[0].data.push(item.lengthWaysCityCount);
								seriesData1[1].data.push(item.lengthWaysProvinceCount);
								seriesData1[2].data.push(item.lengthWaysCountryCount);
							});
							this.longitudinalResults = { xData: xData1.length?xData1:['暂无数据'], seriesData: seriesData1 };
							break;
						case 2:
							let xData2 = [],
								seriesData2 = [
									{ name: '技术开发', data: [] },
									{ name: '技术转让', data: [] },
									{ name: '技术资讯', data: [] },
									{ name: '技术服务', data: [] },
									{ name: '其他', data: [] }
								];
							results.forEach(item => {
								xData2.push(item.year || item.month);
								seriesData2[0].data.push(item.crosswiseContractType0Count);
								seriesData2[1].data.push(item.crosswiseContractType1Count);
								seriesData2[2].data.push(item.crosswiseContractType2Count);
								seriesData2[2].data.push(item.crosswiseContractType3Count);
								seriesData2[2].data.push(item.crosswiseContractType4Count);
							});
							this.transverseResults = { xData: xData2.length?xData2:['暂无数据'], seriesData: seriesData2 };
							break;
						case 3:
							let xData3 = [],
								seriesData3 = [
									{ name: '自然', data: [] },
									{ name: '人文', data: [] },
									{ name: '教研', data: [] }
								];
							results.forEach(item => {
								xData3.push(item.year || item.month);
								seriesData3[0].data.push(item.collegeProjectType0Count);
								seriesData3[1].data.push(item.collegeProjectType1Count);
								seriesData3[2].data.push(item.collegeProjectType2Count);
							});
							this.facultyResults = { xData: xData3.length?xData3:['暂无数据'], seriesData: seriesData3 };
							break;
						default:
							break;
					}
				} else {
					this.$message.error(msg);
				}
			} catch (err) {
				this.$message.error('查询失败');
				console.error('>>>err', err);
			} finally {
				loading.close();
			}
		},
		// 页面加载
		load(text) {
			return this.$.loading(this.$loading, text);
		}
	}
};
</script>

<style scoped lang="scss">
.result {
	margin-top: 12px;
	width: 100%;
	&-title {
		display: flex;
		align-items: center;
		height: 40px;
		padding: 0 0 50px 11px;
		border-bottom: 1px solid #ecf0f4;
		&-icon {
			width: 16px;
			height: 16px;
			background: #0076e8;
			margin-right: 8px;
		}
		&-text {
			font-weight: 600;
			font-size: 16px;
		}
	}
	&-content {
		display: flex;
		align-items: center;
		justify-content: space-between;
		padding: 20px 0 32px 11px;
		height: 400px;
		&-chart {
			flex-shrink: 0;
			width: 35%;
			height: 100%;
		}
	}
	&-con {
		background: #ffffff;
		width: 49.5%;
		display: flex;
		align-items: center;
		height: 400px;
		&-chart {
			flex-shrink: 0;
			width: 35%;
			height: 100%;
		}
	}
}
</style>
