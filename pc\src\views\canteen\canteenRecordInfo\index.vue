<template>
	<div class="content">
		<es-data-table
			ref="table"
			:row-style="tableRowClassName"
			:full="true"
			:fit="true"
			:thead="thead"
			:toolbar="toolbar"
			:border="true"
			:page="pageOption"
			:url="dataTableUrl"
			:numbers="true"
			:param="params"
			close
			form
			@btnClick="btnClick"
			@sort-change="sortChange"
			@submit="hadeSubmit"
		></es-data-table>
		<es-dialog
			:title="formTitle"
			:visible.sync="showForm"
			width="500px"
			height="400px"
			:close-on-click-modal="false"
			:destroy-on-close="true"
		>
			<es-form
				ref="form"
				:model="formData"
				:contents="formItemList"
				height="280px"
				:genre="2"
				collapse
				@change="inputChange"
				@submit="handleFormSubmit"
				@reset="showForm = false"
			/>
		</es-dialog>
		<es-dialog
			title="删除"
			:visible.sync="showDelete"
			width="20%"
			:close-on-click-modal="false"
			:middle="true"
			height="140px"
		>
			<div>确定要删除该条数据吗</div>
			<div class="btn-box">
				<div class="btn theme" @click="deleteRow">确定</div>
				<div class="btn" @click="showDelete = false">取消</div>
			</div>
		</es-dialog>
		<es-dialog
			v-if="showCanteenMenu"
			title="菜品管理"
			:visible.sync="showCanteenMenu"
			append-to-body
			width="1340px"
			height="720px"
			:close-on-click-modal="false"
			:destroy-on-close="true"
		>
			<canteenNenu ref="canteenNenu" :canteen-record-id="canteenRecordId"></canteenNenu>
		</es-dialog>
	</div>
</template>

<script>
import interfaceUrl from '@/http/canteen/canteenRecordInfo.js';
import canteenNenu from '@/views/canteen/canteenMenuInfo/index.vue';
import storeroomApi from '@/http/logistics/hqStoeroom.js';

export default {
	components: { canteenNenu },
	data() {
		return {
			ownId: '',
			deleteId: '',
			dataTableUrl: interfaceUrl.listJson,
			showForm: false,
			showRoleForm: false,
			showCanteenMenu: false,
			canteenRecordId: null,
			enpList: [],
			roleList: [],
			showDelete: false,
			formData: {},
			roleFormData: {
				userId: '',
				username: '',
				phone: '',
				selectedRoleList: []
			},
			storeroomOptions: [], //库房选择列表
			formTitle: '编辑',
			editBtn: {
				type: 'submit',
				skin: 'lay-form-btns',
				contents: [
					{
						type: 'primary',
						text: '确定',
						event: 'confirm'
					},
					{
						type: 'reset',
						text: '取消',
						event: 'cancel'
					}
				]
			},
			cancelBtn: {
				type: 'submit',
				skin: 'lay-form-btns',
				contents: [
					{
						type: 'reset',
						text: '取消',
						event: 'cancel'
					}
				]
			},
			toolbar: [
				{
					type: 'button',
					contents: [
						{
							text: '新增',
							code: 'add',
							type: 'primary'
						}
					]
				},
				{
					type: 'search',
					reset: true,
					contents: [
						{
							type: 'select',
							col: 6,
							name: 'canteenCode',
							label: '食堂',
							placeholder: '食堂',
							event: 'multipled',
							sysCode: 'canteen_code',
							'label-key': 'shortName',
							'value-key': 'cciValue',
							clearable: true
						},
						{
							type: 'select',
							col: 6,
							name: 'type',
							label: '类型',
							placeholder: '类型',
							event: 'multipled',
							sysCode: 'canteen_batch_code',
							'label-key': 'shortName',
							'value-key': 'cciValue',
							clearable: true
						},
						{
							type: 'date',
							col: 6,
							name: 'samplingTime',
							label: '菜品留样日期',
							placeholder: '菜品留样日期'
						},
						{ type: 'text', name: 'keyword', placeholder: '关键字查询' }
					]
				}
			],
			thead: [
				{
					title: '食堂',
					align: 'center',
					field: 'canteen'
				},
				{
					title: '库房',
					align: 'center',
					field: 'storeroom'
				},
				{
					title: '类型',
					align: 'center',
					field: 'type'
				},
				{
					title: '菜品留样日期',
					align: 'center',
					field: 'samplingTime'
				},
				{
					title: '取样人',
					align: 'center',
					field: 'createUserName'
				},
				{
					title: '创建时间',
					align: 'center',
					field: 'createTime'
				},
				{
					title: '操作',
					type: 'handle',
					width: 180,
					template: '',
					events: [
						{ code: 'menusBtn', text: '菜品信息管理' },
						{ code: 'view', text: '查看' },
						{ code: 'edit', text: '编辑' },
						{ code: 'delete', text: '删除' }
					]
				}
			],
			pageOption: {
				layout: 'total, prev, pager, next, jumper',
				pageSize: 10,
				// hideOnSinglePage: true,
				position: 'right',
				current: 1,
				pageNum: 1
			},
			params: {
				asc: 'false',
				orderBy: 'createTime'
			}
		};
	},
	computed: {
		formItemList() {
			return [
				{
					type: 'select',
					col: 12,
					name: 'canteen',
					label: '食堂',
					placeholder: '食堂',
					event: 'multipled',
					sysCode: 'canteen_code',
					'label-key': 'shortName',
					'value-key': 'cciValue',
					clearable: true
				},
				{
					type: 'select',
					col: 12,
					name: 'storeroom',
					label: '库房',
					placeholder: '库房',
					event: 'multipled',
					data: this.storeroomOptions,
					'label-key': 'name',
					'value-key': 'id',
					clearable: true
				},
				{
					type: 'select',
					col: 12,
					name: 'type',
					label: '类型',
					placeholder: '类型',
					event: 'multipled',
					sysCode: 'canteen_batch_code',
					'label-key': 'shortName',
					'value-key': 'cciValue',
					clearable: true
				},
				{
					type: 'date',
					col: 12,
					name: 'samplingTime',
					label: '菜品留样日期',
					placeholder: '菜品留样日期'
				}
			];
		}
	},
	watch: {},
	created() {
		this.storeroomList();
	},
	mounted() {},
	methods: {
		/**
		 * 表格行高
		 */
		tableRowClassName({ row, rowIndex }) {
			let styleRes = {
				height: '54px !important'
			};
			return styleRes;
		},
		inputChange(key, value) {},
		hadeSubmit(data) {},
		storeroomList() {
			this.$request({
				url: storeroomApi.listAll,
				method: 'GET'
			}).then(res => {
				if (res.rCode === 0) {
					this.storeroomOptions = res.results;
				}
			});
		},
		/**
		 * 操作按钮点击事件
		 * @param {*} res
		 */
		btnClick(res) {
			let code = res.handle.code;
			switch (code) {
				case 'add':
					// 新增
					this.formTitle = '新增';
					this.editModule(this.formItemList, []);
					this.formData = {};
					this.showForm = true;
					break;
				case 'edit':
					// 编辑
					this.formTitle = '编辑';
					this.ownId = res.row.id;
					this.editModule(this.formItemList, []);
					this.$request({
						url: interfaceUrl.info + '/' + res.row.id,
						method: 'GET'
					}).then(res => {
						if (res.rCode == 0) {
							this.formData = res.results;
							this.showForm = true;
						}
					});
					break;
				case 'view':
					this.formTitle = '查看';
					this.ownId = res.row.id;
					this.readModule(this.formItemList, []);
					this.$request({
						url: interfaceUrl.info + '/' + res.row.id,
						method: 'GET'
					}).then(res => {
						if (res.rCode == 0) {
							this.formData = res.results;
							this.showForm = true;
						}
					});
					break;
				case 'menusBtn':
					this.showCanteenMenu = true;
					this.canteenRecordId = res.row.id;
					break;
				case 'delete':
					this.deleteId = res.row.id;
					this.showDelete = true;
					break;
				default:
					break;
			}
		},
		handleClose() {},
		handleFormItemChange() {},
		handleFormSubmit(data) {
			let formData = data;
			delete formData.extMap;
			let url = '';
			if (this.formTitle == '新增') {
				url = interfaceUrl.save;
			} else {
				url = interfaceUrl.update;
			}
			this.$request({
				url: url,
				data: formData,
				method: 'POST'
			}).then(res => {
				if (res.rCode == 0) {
					this.$message.success('操作成功');
					this.showForm = false;
					this.formData = {};
					this.$refs.table.reload();
				} else {
					this.$message.error(res.msg);
				}
			});
		},
		deleteRow() {
			this.$request({
				url: interfaceUrl.delete,
				data: { id: this.deleteId },
				method: 'POST'
			}).then(res => {
				if (res.rCode == 0) {
					this.$refs.table.reload();
					this.$message.success('删除成功');
				} else {
					this.$message.error(res.msg);
				}
				this.showDelete = false;
			});
		},
		/**
		 * 排序变化事件
		 * @param {*} column
		 * @param {*} prop
		 * @param {*} order
		 */
		sortChange(column, prop, order) {
			if (column.order == 'ascending') {
				//升序
				this.params = {
					asc: 'true',
					orderBy: column.prop
				};
			} else if (column.order == 'descending') {
				//降序
				this.params = {
					asc: 'false',
					orderBy: column.prop
				};
			} else {
				//不排序
				this.params = {
					asc: 'false',
					orderBy: 'createTime'
				};
			}
			this.$refs.table.reload();
		},
		/**
		 * 编辑模式
		 */
		editModule(list, hideField) {
			for (var i in list) {
				var item = list[i];
				item.readonly = false;
				if (undefined != hideField && hideField.length > 0 && hideField.indexOf(item.name) > -1) {
					item.hide = true;
				}
			}
			list.push(this.editBtn);
		},
		/**
		 * 只读模式
		 */
		readModule(list, hideField) {
			for (var i in list) {
				var item = list[i];
				item.readonly = true;
				if (undefined != hideField && hideField.length > 0 && hideField.indexOf(item.name) > -1) {
					item.hide = true;
				}
			}
			list.push(this.cancelBtn);
		}
	}
};
</script>
<style scoped lang="scss">
@import '../../../assets/style/style.scss';

.content {
	display: flex;
	width: 100%;
	height: 100%;

	::v-deep .es-data-table {
		flex: 1;
		display: flex;
		flex-direction: column;

		.es-data-table-content {
			flex: 1;
			height: 0;
			display: flex;
			flex-direction: column;

			.el-table {
				flex: 1;
				// height: 100% !important;
			}

			.es-thead-border {
				.el-table__header {
					th {
						border-right: 1px solid #e1e1e1;
					}
				}
			}
		}
	}

	::v-deep .el-form-item__label {
		background: none;
		border: 0px solid #c2c2c2;
	}
}

.el-dialog__body {
	.btn-box {
		margin-top: 20px;
		display: flex;
		justify-content: flex-end;

		.btn {
			padding: 5px 10px;
			color: #666;
			border: 1px solid #eee;
			cursor: pointer;
			margin-right: 5px;
			border-radius: 4px;
			// &.theme {
			// 	background: $--color-primary;
			// 	color: #fff;
			// 	border-color: $--color-primary;
			// }
		}
	}
}
</style>
