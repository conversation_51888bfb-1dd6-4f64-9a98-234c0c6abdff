<template>
	<div class="Main">
		<es-data-table
			:data="filteredTableData"
			:fit="true"
			:thead="thead"
			:toolbar="toolbar"
			:option-data="optionData"
			size="mini"
			:border="true"
			page
			@cell-click="btnClick"
			@btn-click="ToolbarClick"
			:response="getSearch"
		></es-data-table>

		<el-dialog
			title="提示"
			:visible.sync="dialogVisible"
			:modal="false"
			width="50%"
			:before-close="handleClose"
		>
			<div class="FormBody">
				<es-form
					ref="form"
					:model="form"
					@submit="handleFormSubmit"
					:contents="formItemList"
                    @reset="handleClose"
					label-position="top"
				></es-form>
			</div>
		</el-dialog>
	</div>
</template>

<script>
import { v4 as uuidv4 } from 'uuid';

export default {
	data() {
		return {
			dialogVisible: false,
			toolbar: [
				{
					type: 'button',
					length: 5,
					contents: [
						{
							text: '新增',
							type: 'primary'
						}
					]
				},
				{
					type: 'button',
					length: 5,
					contents: [
						{
							text: '重置',
							type: 'primary'
						}
					]
				},
				{
					type: 'search',
					contents: [
						{
							type: 'text',
							label: '关键字',
							name: 'applicant',
							placeholder: '请输入关键字'
						}
					]
				}
			],
			thead: [
				{
					title: '申请人',
					field: 'applicant',
					align: 'center'
				},
				{
					title: '联系方式',
					field: 'phone',
					align: 'center'
				},
				{
					title: '单位名称',
					field: 'UnitName',
					align: 'center'
				},
				{
					title: '申请事由',
					field: 'Reason',
					align: 'center'
				},
				{
					title: '备注',
					field: 'remark',
					align: 'center'
				},
				{
					title: '附件',
					field: 'fileName',
					align: 'center'
				}
			],
			optionData: {
				isshow: [
					{
						value: 1,
						name: '启用'
					},
					{
						value: 0,
						name: '禁用'
					}
				]
			},
			search: null,
			tableData: [],
			formItemList: [
				{
					name: 'applicant',
					col: 6,
					placeholder: '请输入文字',
					label: '申请人'
				},
				{
					name: 'phone',
					col: 6,
					placeholder: '请输入文字',
					label: '联系方式'
				},
				{
					name: 'UnitName',
					col: 6,
					row: true,
					placeholder: '请输入文字',
					label: '单位名称'
				},
				{
					name: 'Reason',
					col: 6,
					type: 'textarea',
					placeholder: '请输入文字',
					label: '申请事由'
				},

				{
					name: 'remark',
					type: 'textarea',
					col: 6,
					placeholder: '请输入文字',
					label: '备注'
				},
				{
					label: '上传附件',
					type: 'attachment',
					name: 'fj',
					code: 'doc_manage_upload',
					events: {
						'on-success': e => {
							console.log(123123);
						}
					},
					ownId: uuidv4(),
					rules: {
						required: true,
						message: '请上传附件',
						trigger: 'blur'
					}
				}
			],
			form: {}
		};
	},
	computed: {
		filteredTableData() {
			if (!this.search) {
				return this.tableData; // 如果没有搜索词，则返回所有数据
			}
			return this.tableData.filter(item => {
				return item.applicant.toLowerCase().includes(this.search);
			});
		}
	},
    mounted() {
		this.SetLocalData();
		this.getData();
	},
	methods: {

        SetLocalData() {
			let data = [
				{
					id:uuidv4(),
					applicant: '1',
					phone: '13812345678',
					UnitName: '王小虎1',
					Reason: 2,
					remark: '测试测试',
					fileName: 'awdawdad',
					status: '审核中',
					step: 1
				},
				{
					id:uuidv4(),
					applicant: '2',
					phone: '13887654321',
					UnitName: '李小刚2',
					Reason: 0,
					remark: '测试测试',
					fileName: 'awdawdad',
					status: '审核中',
					step: 1
				},
				{
					id:uuidv4(),
					applicant: '3',
					phone: '13823456789',
					UnitName: '张伟3',
					Reason: 1,
					remark: '测试测试',
					fileName: 'awdawdad',
					status: '核发中',
					step: 2
				},
				{
					id:uuidv4(),
					applicant: '4',
					phone: '13834567890',
					UnitName: '刘英4',
					Reason: 3,
					remark: '测试测试',
					fileName: 'awdawdad',
					status: '核发中',
					step: 2
				} 
			];

			let flag = window.sessionStorage.getItem('TableData');
			if (!flag) {
				window.sessionStorage.setItem('TableData', JSON.stringify(data));
			}
		},

		getData() {
			let data = JSON.parse(window.sessionStorage.getItem('TableData'));
            this.tableData = data
		},
        
        UpDateLocalData(formData){
			formData.id = uuidv4()
			formData.step = 1
			formData.status = '审核中'
			let data = JSON.parse(window.sessionStorage.getItem('TableData'));
            data.push(formData) 
			window.sessionStorage.setItem('TableData', JSON.stringify(data));
            this.getData()
        },
	
		handleFormSubmit(data) {
            const { fj, ...restFormData } = data;
			const { name, response } = fj[0];
            this.$utils.deleteFile('doc_manage_upload', response.ownId);
            const formData = {
				...restFormData,
				fileName: name,

			};
            this.UpDateLocalData(formData)
            this.$message.success('创建成功');
            this.dialogVisible = false
		},
		handleClose() {
			this.form = {};
            this.dialogVisible = false
		},
		ToolbarClick(e) {
			let { text } = e.handle;
			if (text == '新增') {
				this.dialogVisible = true;
			}
			if (text == '重置') {
				this.search = null;
			}
		},
		getSearch({ type, data }) {
			let { applicant } = data;
			this.search = applicant;
		},
		btnClick({ row, handle }) {
			console.log(row);
			console.log('按钮点击事件', handle);
		}
	}
};
</script>

<style scoped lang="scss">
.Main {
	width: 100%;
	height: 100%;
	padding: 10px 0px;
	.es-data-table {
		height: 100%;
	}
	.FormBody {
		height: 500px;
	}

}
</style>
