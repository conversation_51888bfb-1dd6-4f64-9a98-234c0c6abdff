<template>
	<!-- 在list.js修改component.name值可改变展示组件 -->
	<component
		:is="component.name"
		ref="dataTable"
		v-bind="table"
		:table="table"
		:tree="treeParam"
		@selection-change="handleSelectionChange"
		@btnClick="btnClick"
		@node-click="treeSelectChange"
		@success="successAfter"
	>
		<es-dialog
			v-if="showForm"
			:title="formTitle"
			:visible.sync="showForm"
			:close-on-click-modal="false"
			:middle="true"
			height="550px"
			class="dialog-form"
		>
			<es-form
				ref="form"
				:model="formData"
				:contents="formItemList"
				:genre="2"
				height="400px"
				collapse
				@change="handleFormItemChange"
				@submit="handleFormSubmit"
				@reset="showForm = false"
			/>
		</es-dialog>
		<es-dialog
			v-if="showView"
			title="详情"
			:visible.sync="showView"
			:close-on-click-modal="false"
			:middle="true"
			height="600px"
			class="dialog-form"
		>
			<es-form
				ref="form"
				:model="viewData"
				:contents="viewItemList"
				:genre="2"
				readonly
				height="500px"
				collapse
				@reset="showView = false"
			/>
		</es-dialog>
	</component>
</template>

<script>
import httpApi from '@/http/lab/labReformNotify/api.js';
import view from '@/http/lab/labReformNotify/view.js';
import list from '@/http/lab/ejxyLabReformNotify/list.js';
import report from '@/http/lab/ejxyLabReformNotify/report.js';

export default {
	name: 'EjxyLabReformNotify',
	mixins: [list, report, view],
	data() {
		return {
			selectRowData: [],
			selectRowIds: [],
			selectTreeData: {},
			formTitle: '回复',
			showForm: false,
			showView: false,
			formData: {},
			viewData: {},
			extraData: {},
			collegeList: []
		};
	},
	watch: {
		showForm(val) {
			if (!val) {
				this.formData = {};
			}
		}
	},
	mounted() {
		this.getSelectList();
	},
	methods: {
		getSelectList() {
			this.$request({
				url: httpApi.platmajorGetCollegeSelectList,
				method: 'POST'
			}).then(result => {
				this.collegeList = result?.results || [];
			});
		},
		// 初始化扩展数据，字典等
		successAfter(data) {
			this.extraData = data.results?.extraData || {};
		},
		// 数据列表多选回调
		handleSelectionChange(data) {
			let ids = [];
			data.forEach(row => {
				ids.push(row.id);
			});
			this.selectRowData = data;
			this.selectRowIds = ids;
		},
		// tree 点击
		treeSelectChange(res, data) {
			this.selectTreeData = data;
			this.$refs.dataTable.reload();
		},
		// 表单变更时, 回调处理
		handleFormItemChange(filed, data) {},
		btnClick(res) {
			let text = res.handle.text;
			switch (text) {
				case '查看':
					this.$request({
						url: httpApi.labReformNotifyInfo,
						params: { id: res.row.id }
					}).then(res => {
						// 处理自定义的数据格式化, extFiled 额外属性名， key、val 数据映射
						this.viewData = res.results;
						// 打开查看弹窗
						this.showView = true;
					});
					break;
				case '回复':
					this.formData = {
						id: res.row.id,
						reformStatus: '1'
					};
					this.showForm = true;
					break;
				default:
					break;
			}
		},
		handleFormSubmit() {
			let formData = { ...this.formData };
			this.$request({
				url: httpApi.labReformNotifyUpdateReport,
				data: formData,
				method: 'POST'
			}).then(res => {
				if (res.rCode == 0) {
					this.$message.success('操作成功');
					this.showForm = false;
					this.formData = {};
					this.$refs.dataTable.reload();
				} else {
					this.$message.error(res.msg);
				}
			});
		}
	}
};
</script>
