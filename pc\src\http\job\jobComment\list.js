import httpApi from '@/http/job/jobComment/api.js';
export default {
	computed: {
		component() {
			return {
				// 纯列表结构
				name: 'es-data-table'
			};
		},
		treeParam() {
			return {
				
			};
		},
		table() {
			return {
				url: httpApi.jobCommentList,
				param: { handlerType: '' },
				full: true,
				fit: true,
				page: true,
				stripe: true,
				numbers: true,
				'row-key': 'id',
				toolbar: [
					{
						type: 'button',
						contents: [
							{
								text: '新增',
								type: 'primary'
							}
						]
					},
					{
						type: 'search',
						reset: true,
						contents: [
							//{
							//	name: 'accountName_like',
							//	placeholder: '配置名称',
							//	label: '配置名称',
							//	col: 6
							//},
							{
								type: 'date',
								col: 6,
								name: 'updateTime_begin',
								label: '更新开始时间',
								placeholder: '更新开始时间'
							},
							{
								type: 'date',
								col: 6,
								name: 'updateTime_end',
								label: '更新结束时间',
								placeholder: '更新结束时间'
							}
						]
					}
				],
				thead: [
					{
						title: '类别',
						align: 'left',
						field: 'type'
					},
					{
						title: '文章编号',
						align: 'left',
						field: 'objId'
					},
					{
						title: '内容',
						align: 'left',
						field: 'content'
					},
					{
						title: '状态(0.停用、1.启用)',
						align: 'left',
						field: 'status'
					},
					{
						title: '审核状态(0.待审核、1.审核通过、2.驳回)',
						align: 'left',
						field: 'auditStatus'
					},
					{
						title: '审核意见',
						align: 'left',
						field: 'auditOpinion'
					},
					{
						title: '创建人编号',
						align: 'left',
						field: 'createUser'
					},
					{
						title: '操作',
						type: 'handle',
						width: 180,
						events: [
							{
								text: '查看'
							},
							{
								text: '编辑'
							},
							{
								text: '删除'
							}
						]
					}
				]
			};
		}
	}
};
