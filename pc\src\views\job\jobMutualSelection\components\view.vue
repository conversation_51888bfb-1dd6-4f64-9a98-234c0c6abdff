<template>
	<es-form
		:key="formData.id"
		ref="form"
		v-loading="loading"
		:model="formData"
		:contents="formItemList"
		label-width="100px"
		:genre="2"
		collapse
		:submit="formTitle !== '查看'"
		@change="inputChange"
		@click="handleFormSubmit"
	/>
</template>

<script>
import interfaceUrl from '@/http/job/jobDualSelect/api.js';
import SelectMore from './select-more.vue';
export default {
	name: 'ResumeView',
	// components: {
	// 	SelectMore
	// },
	props: {
		info: {
			type: Object,
			default: () => {
				return {};
			}
		},
		formTitle: {
			type: String,
			default: ''
		}
	},
	data() {
		return {
			loading: false,
			formData: {},
			attachmentsList: []
		};
	},
	computed: {
		formItemList() {
			const readonly = this.formTitle === '查看';
			const isIssue = this.formData.states == 1; // 发布后某些部分功能才能编辑
			return [
				{
					label: '标题',
					name: 'title',
					readonly: readonly || isIssue ? true : false,
					rules: {
						required: true,
						message: '请输入标题',
						trigger: 'change'
					},
					col: 6
				},
				{
					type: 'daterange',
					label: '双选会开展时间',
					format: 'yyyy-MM-dd HH:mm:ss',
					name: 'meetingStartEndDes',
					readonly,
					placeholder: '',
					col: 6,
					rules: {
						required: true,
						message: '请选择双选会开展时间',
						trigger: 'change'
					}
				},
				{
					label: '双选会开展地址',
					name: 'address',
					readonly,
					rules: {
						required: true,
						message: '请输入开展地址',
						trigger: 'change'
					},
					col: 6
				},
				{
					type: 'daterange',
					label: '企业回复时间',
					format: 'yyyy-MM-dd HH:mm:ss',
					name: 'replyStartEndDes',
					readonly,
					placeholder: '',
					col: 6,
					rules: {
						required: true,
						message: '请选择企业回复时间',
						trigger: 'change'
					}
				},
				{
					type: 'attachment',
					label: '邀请说明',
					code: 'USER_IMG',
					ownId: '666666',
					name: 'explainDes',
					showFileList: false,
					disabled: true,
					render: h => {
						return h('wangeditor', {
							props: {
								readOnly: readonly || isIssue ? true : false,
								value: this.formData.explainDes,
								dataId: this.formData.id
							},
							on: {
								inputChange: val => {
									this.$set(this.formData, 'explainDes', val);
								}
							}
						});
					},
					rules: {
						required: true,
						message: '请填写邀请说明',
						validator: (rule, value, callback) => {
							const explainDes = this.formData.explainDes;
							if (explainDes) {
								callback();
							}
							callback(new Error('请填写邀请说明'));
						},
						trigger: 'change'
					},
					col: 12
				},
				{
					type: 'attachment',
					label: '选择企业',
					name: 'dualSelectMappingList',
					code: 'USER_IMG',
					ownId: '666666',
					showFileList: false,
					disabled: true,
					render: h => {
						let list = this.formData.dualSelectMappingList || [];
						return h(SelectMore, {
							props: { width: '1048px', list: list, readonly: readonly || isIssue ? true : false },
							on: {
								onSelect: val => {
									this.$set(this.formData, 'dualSelectMappingList', val);
									console.log(this.formData.dualSelectMappingList, 'dualSelectMappingList');
								}
							}
						});
					},
					rules: {
						required: true,
						message: '请选择企业',
						validator: (rule, value, callback) => {
							const dualSelectMappingList = this.formData?.dualSelectMappingList;
							if (dualSelectMappingList?.length > 0) {
								callback();
							}
							callback(new Error('请选择企业'));
						},
						trigger: 'change'
					},
					col: 12
				},
				{
					name: 'fj',
					label: '附件',
					type: 'attachment',
					col: 12,
					code: 'job_dual_select_adjunct',
					preview: true,
					readonly: readonly || isIssue ? true : false,
					ownId: this.formData.id // 业务id
				},
				{
					type: 'submit',
					skin: 'lay-form-btns',
					contents: isIssue
						? [
								{
									type: 'primary',
									text: '修改'
								},

								{
									type: 'reset',
									text: '取消'
								}
						  ]
						: [
								{
									type: 'primary',
									plain: true,
									text: '暂存'
								},
								{
									type: 'primary',
									text: '提交'
								},

								{
									type: 'reset',
									text: '取消'
								}
						  ]
				}
			];
		}
	},
	created() {
		this.doHandleFormData(this.info);
	},
	methods: {
		inputChange(key, value) {},
		// 保存
		async handleFormSubmit(e) {
			const valid = await this.$refs.form.validate();
			if (!valid) return;
			this.loading = true;
			const { text } = e;
			const formData = JSON.parse(JSON.stringify(this.formData));
			const params = {
				...formData,
				dualSelectMappingList: formData.dualSelectMappingList.map(item => {
					return {
						enterpriseId: item.id,
						enterpriseName: item.corpName,
						collegeId: item.collegeId || item.recommenderCode,
						meetingId: formData.id
					};
				}),
				states: text === '暂存' ? 0 : 1,
				replyStartDate: formData.replyStartEndDes[0],
				replyEndDate: formData.replyStartEndDes[1],
				meetingStartDate: formData.meetingStartEndDes[0],
				meetingEndDate: formData.meetingStartEndDes[1]
			};

			delete params.replyStartEndDes;
			delete params.meetingStartEndDes;
			delete params.fj;
			this.$request({
				url: this.formTitle === '新增' ? interfaceUrl.saveSelection : interfaceUrl.updateSelection,
				data: params,
				method: 'POST',
				format: false,
				headers: {
					contentType: 'application/json'
				}
			}).then(res => {
				this.loading = false;
				if (res.rCode === 0) {
					this.$emit('close');
					this.$message.success('操作成功');
				} else {
					this.$message.error(res.msg);
				}
			});
		},
		doHandleFormData(newData) {
			if (newData.replyStartDate && newData.replyEndDate) {
				newData.replyStartEndDes = [newData.replyStartDate, newData.replyEndDate];
			}
			if (newData.meetingStartDate && newData.meetingEndDate) {
				newData.meetingStartEndDes = [newData.meetingStartDate, newData.meetingEndDate];
			}
			newData.dualSelectMappingList = newData.dualSelectMappingList?.map?.(item => {
				return {
					id: item.enterpriseId,
					corpName: item.enterpriseName,
					collegeId: item.collegeId
				};
			});
			this.formData = newData;
		}
	}
};
</script>

<style scoped lang="scss">
.el-upload--handle {
	width: 100%;
}
</style>
