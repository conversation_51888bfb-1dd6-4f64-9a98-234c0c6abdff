<template>
  <span v-if="valString">{{ valString }}</span>
  <span v-else>-</span>
</template>

<script>
import interfaceUrl, { systemApi } from '@/http/common/system.js';
export default {
  name: "DictRenderNew",
  props: {
    code: {
      type: String,
      default: ''
    },
    value: {
      type: [String, Number, null],
      default: null
    }
  },
  data() {
    return {
      valString: null
    };
  },
  watch: {
    value: {
      deep: true,
      immediate: true,
      handler(newVal) {
        console.log("字段"+newVal+this.code)
        if (this.code && newVal) {
          this.findCodeData(this.code);
        }
      }
    }
  },
  methods: {
    // 查询数据
    findCodeData(code) {
      this.$request({
        url: interfaceUrl.findSysCode,
        data: {
          sysAppCodes: code
        },
        method: 'POST'
      }).then(res => {
        if (res.rCode == 0) {
          if (res.results) {
            res.results.forEach(list => {
              if (list.cciValue == this.value) {
                this.valString = list.shortName;
              }
            });
          }
        }
      });
    }
  }
}
</script>

<style scoped>

</style>