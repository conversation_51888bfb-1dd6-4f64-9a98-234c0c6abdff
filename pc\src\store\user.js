import util from 'eoss-ui/src/utils/util';
export default {
	namespaced: true,
	state: {
		tenantId: localStorage.getItem('tenantId') || '', //租户id，接口请求时需要的
		logoBj: localStorage.getItem('logoBj') ? JSON.parse(localStorage.getItem('logoBj')) : {} //logo图片，登录图片等配置图片
	},
	mutations: {},
	actions: {
		// 获取用户配置信息
		async getTenantId({ state, commit }) {
			await util
				.ajax({
					url: '/ybzy/platuser/front/getTenantId'
				})
				.then(res => {
					if (res.rCode == 0) {
						state.tenantId = res.results?.tenantId;
						state.logoBj = res.results || {};
						localStorage.setItem('logoBj', JSON.stringify(res.results));
						localStorage.setItem('tenantId', res.results?.tenantId);
					}
				});
		}
	}
};
