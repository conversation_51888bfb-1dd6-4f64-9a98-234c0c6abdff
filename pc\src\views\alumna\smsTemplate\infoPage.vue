<template>
  <div class="sketch_content">
    <el-form :model="formData" class="resumeTable" :rules="rules" ref="form">
      <el-row>
        <el-col :span="11">
          <el-form-item label="短信签名" label-width="90px" label-position="left" prop="sign">
            <el-input v-model="formData.sign" :disabled="infoDisabled"></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="11">
          <el-form-item label="发送场景" label-width="90px" label-position="left" prop="sendCase">
            <el-input v-model="formData.sendCase" :disabled="infoDisabled"></el-input>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row>
        <el-col :span="22">
          <el-form-item label="内容" label-width="90px" label-position="left" prop="content">
            <wangeditor v-model="formData.content" :read-only="infoDisabled"></wangeditor>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row>
        <el-col :span="3" style="float:right">
          <el-button type="reset" @click="infoPageClose(false)">取消</el-button>
        </el-col>
        <el-col :span="3" style="float:right">
          <el-button type="primary" @click="handleFormSubmit" v-show="!infoDisabled">保存</el-button>
        </el-col>
      </el-row>
    </el-form>
  </div>
</template>
<script>

import api from "@/http/alumna/smsTemplate";

export default {
  name: 'infoPage',
  props: {
    baseData: {
      type: Object
    },
    infoPageMode: {
      type: String
    }
  },
  data() {
    return {
      infoDisabled: true,
      formData: {},
      pageMode: 'allOn',
      rules: {
        sign: [{ required: true, message: '请输入签名', trigger: 'blur' }],
        sendCase: [{ required: true, message: '请输入发送场景', trigger: 'blur' }],
        content: [{ required: true, message: '请输入内容', trigger: 'blur' }],
      }
    };
  },
  computed: {
  },
  created() {
    this.formData = { ...this.baseData };
    this.pageMode = this.infoPageMode;
    switch (this.pageMode){
      case'新增':
        this.infoDisabled = false;break;
      case'查看':
        this.infoDisabled = true;break;
      case'编辑':
        this.infoDisabled = false;break;
    }
  },
  methods: {
    handleFormSubmit() {
      //校验
      this.$refs.form.validate((valid) => {
        if(valid){
          let apiUrl = this.pageMode === '编辑'? api.update:api.save;
          let saveData = {...this.formData};
          //处理数据
          if(this.formData.typeId != null && typeof saveData.typeId == 'object')
            saveData.typeId = saveData.typeId.value;
          if(this.formData.teacherId != null && typeof saveData.teacherId == 'object')
            saveData.teacherId = saveData.teacherId.value;
          if(typeof saveData.principal == 'object')
            saveData.principal = saveData.principal.value;
          else saveData.principalId = saveData.principal;

          this.$delete(saveData,'typeName');
          this.$delete(saveData,'teacherName');
          this.$delete(saveData,'college');

          this.$request({
            url: apiUrl,
            data: saveData,
            method: 'POST'
          }).then(res => {
            if (res.rCode === 0) {
              this.$message.success('操作成功');
              this.infoPageClose(true);
            } else {
              this.$message.error(res.msg);
            }
          });
        }
      })
    },
    infoPageClose(reload){
      this.pageMode = 'allOn';
      this.infoDisabled = false;
      this.$emit('activelyClose',reload);
    },
  },
  watch: {
  }
};
</script>
<style scoped>
  .sketch_content {
    overflow: auto;
    height: 100%;
    border-top: 1px solid #eff1f4;
    border-bottom: 1px solid #eff1f4;
    padding: 0 30px 11px 27px;
  }
  .resumeTable td {
    height: 50px;
    font-weight: bold;
  }

  ::v-deep .el-collapse-item__header.is-active {
    border-bottom: 1px solid #ebeef5;
    font-size: 18px;
    font-weight: bold;
  }
  ::v-deep .el-collapse-item__header::before {
    content: "";
    width: 4px;
    height: 18px;
    background-color: #0076E9;
    margin-right: 2px;
  }
</style>