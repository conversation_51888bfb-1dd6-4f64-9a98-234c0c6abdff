<template>
	<div class="content">
		<es-data-table
			ref="table"
			:row-style="tableRowClassName"
			:full="true"
			:fit="true"
			:thead="theadOne"
			:toolbar="toolbarOne"
			:border="true"
			:page="pageOption"
			:url="dataTableUrl"
			:numbers="true"
			:param="params"
			close
			form
			@btnClick="btnClick"
			@sort-change="sortChange"
		></es-data-table>

		<es-dialog
			:key="showAddTable"
			:title="formTitle"
			:visible.sync="showAddTable"
			width="1100px"
			height="630px"
			:drag="false"
			:close-on-click-modal="false"
			:destroy-on-close="true"
			@close="cancel"
		>
			<div v-loading="loading" style="height: 100%">
				<el-form
					ref="form"
					:inline="true"
					label-position="right"
					label-width="110px"
					:model="formData"
					:rules="formRules"
				>
					<el-form-item label="库房" prop="storeroom">
						<el-select v-model="formData.storeroom" placeholder="请选择库房" :disabled="disabled">
							<el-option
								v-for="item in storeroomOptions"
								:key="item.id"
								:label="item.name"
								:value="item.id"
							></el-option>
						</el-select>
					</el-form-item>
					<el-form-item label="原料类型" prop="materialType">
						<el-select
							v-model="formData.materialType"
							placeholder="请选择原料类型"
							:disabled="formTitle != '新增'"
						>
							<el-option
								v-for="item in materialCategoryOptions"
								:key="item.id"
								:label="item.name"
								:value="item.id"
							></el-option>
						</el-select>
					</el-form-item>
				</el-form>
				<el-button
					v-if="!disabled"
					type="primary"
					@click="addMaterial()"
					size="medium"
					style="margin-bottom: 10px"
				>
					添加原料
				</el-button>
				<es-data-table
					ref="processPlantTable"
					:disabled="disabled"
					form
					:data="formData.details"
					:thead="theadTwo"
					:numbers="true"
					@edit="formEdit"
					@dataChange="dataTableChange"
					@btnClick="btnClick"
				></es-data-table>
				<span class="price">合计：{{ count }} 元</span>
				<div class="content-warning-Btn">
					<el-button v-if="!disabled" size="medium" type="primary" @click="submitAll('1')">
						提交
					</el-button>
					<el-button v-if="!disabled" size="medium" type="primary" @click="submitAll('0')">
						暂存
					</el-button>
					<el-button size="medium" @click="cancel">取消</el-button>
				</div>
			</div>
		</es-dialog>
		<!-- 新增选择材料 -->
		<es-dialog
			title="原料列表"
			:visible.sync="showMaterial"
			width="60%"
			:drag="false"
			:close-on-click-modal="false"
			:middle="true"
		>
			<Material
				v-if="showMaterial"
				:categoryId="formData.materialType"
				:visible.sync="showMaterial"
				@selected="selMaterial"
			/>
		</es-dialog>
	</div>
</template>

<script>
import httpApi from '@/http/logistics/purchase';
import storeroomApi from '@/http/logistics/hqStoeroom.js';
import materialCategoryApi from '@/http/logistics/materialcategory.js';
import SnowflakeId from 'snowflake-id';
import Material from '../purchase/components/materialByCategory.vue';
import { host } from '../../../../config/config';
export default {
	components: { Material },
	data() {
		return {
			showMaterial: false,
			loading: false,
			disabled: true,
			dataTableUrl: httpApi.listJson, //采购单列表
			showDisable: false,
			showAddTable: false, //采购明细弹窗
			editId: '',
			formData: {
				id: null,
				code: null,
				storeroom: null,
				materialType: null,
				status: null,
				details: []
			},
			formRules: {
				storeroom: [{ required: true, message: '请选择库房', trigger: 'change' }],
				materialType: [{ required: true, message: '请选择原料类型', trigger: 'change' }]
			},
			formTitle: '编辑',
			materialList: [], //原料集合
			supplierList: [], //供应商集合

			storeroomOptions: [], //库房选择列表
			materialCategoryOptions: [], //原料类型

			editBtn: {
				type: 'submit',
				skin: 'lay-form-btns',
				contents: [
					{
						type: 'primary',
						text: '确定',
						event: 'confirm'
					},
					{
						type: 'reset',
						text: '取消',
						event: 'cancel'
					}
				]
			},
			cancelBtn: {
				type: 'submit',
				skin: 'lay-form-btns',
				contents: [
					{
						type: 'reset',
						text: '取消',
						event: 'cancel'
					}
				]
			},
			toolbarOne: [
				{
					type: 'button',
					contents: [
						{
							text: '新增',
							code: 'add',
							type: 'primary'
						}
					]
				},
				{
					type: 'search',
					reset: true,
					contents: [
						{
							type: 'text', //模糊查询
							name: 'keyword',
							placeholder: '请输入采购单号'
						}
					]
				}
			],
			theadOne: [
				{
					title: '采购单号',
					align: 'center',
					field: 'code'
				},
				{
					title: '采购仓库',
					align: 'center',
					field: 'storeroomName'
				},
				{
					title: '原料类型',
					align: 'center',
					field: 'materialTypeName'
				},
				{
					title: '状态',
					align: 'center',
					field: 'status',
					render: (h, data) => {
						return h('p', {}, data.row.status == '1' ? '已提交' : '待提交');
					}
				},
				{
					title: '创建人',
					align: 'center',
					field: 'createUserName'
				},
				{
					title: '创建时间',
					align: 'center',
					field: 'createTime'
				},
				{
					title: '操作',
					type: 'handle',
					width: 90,
					template: '',
					events: [
						{
							code: 'view',
							text: '查看'
						},
						{
							code: 'edit',
							text: '编辑',
							rules: rows => {
								return rows.status == '0';
							}
						},
						{
							code: 'download',
							text: '下载'
						},
						{
							code: 'delete',
							text: '删除'
						}
					]
				}
			],
			tableData: [],
			pageOption: {
				layout: 'total, sizes, prev, pager, next, jumper',
				pageSize: 10,
				position: 'center',
				current: 1,
				pageNum: 1
			},
			params: { orderBy: 'create_time', asc: false }
		};
	},
	computed: {
		theadTwo() {
			let arr = [
				{
					title: '原料',
					field: 'materialName',
					type: 'text',
					align: 'center',
					readonly: true,
					showOverflowTooltip: true
				},
				{
					title: '规格',
					field: 'materialGg',
					type: 'text',
					controls: false,
					precision: 2,
					min: 0,
					readonly: true,
					align: 'center',
					showOverflowTooltip: true
				},
				{
					title: '供应商',
					field: 'materialPp',
					type: 'text',
					controls: false,
					precision: 2,
					min: 0,
					readonly: true,
					align: 'center',
					showOverflowTooltip: true
				},
				{
					title: '价格',
					field: 'currentPrice',
					type: 'number',
					controls: false,
					precision: 2,
					min: 0,
					readonly: true,
					align: 'center'
				},
				{
					title: '数量',
					field: 'purchaseNum',
					disabled: this.disabled,
					type: 'number',
					precision: 0,
					min: 1,
					rules: {
						required: true,
						message: '请输入数量',
						trigger: 'change'
					}
				},
				{
					title: '总价',
					field: 'totalPrice',
					type: 'text',
					align: 'center',
					readonly: true
				},
				{
					title: '操作',
					type: 'handle',
					width: 90,
					template: '',
					events: [
						{
							code: 'delete-row',
							disabled: this.disabled,
							text: '删除'
						}
					]
				}
			];
			return arr;
		},
		count() {
			// 计算tableData.totalPrice和合计
			let totalPrice = 0;
			totalPrice = this.formData.details.reduce((total, item) => {
				return total + Number(item.totalPrice || 0);
			}, 0);
			return totalPrice.toFixed(2);
		}
	},
	watch: {},
	created() {
		this.storeroomList();
		this.categoryList();
		// this.materialListAll();
		// this.supplierListAll();
	},
	mounted() {},
	methods: {
		storeroomList() {
			this.$request({
				url: storeroomApi.listAll,
				method: 'GET'
			}).then(res => {
				if (res.rCode === 0) {
					this.storeroomOptions = res.results;
				}
			});
		},
		categoryList() {
			this.$request({
				url: materialCategoryApi.firstCategoryList,
				method: 'GET'
			}).then(res => {
				if (res.rCode === 0) {
					this.materialCategoryOptions = res.results;
				}
			});
		},
		formEdit(data) {
			let purchaseNum = data.data.purchaseNum ? Number(data.data.purchaseNum) : 0;
			data.data.totalPrice = (data.data.currentPrice * purchaseNum).toFixed(2);
		},
		addMaterial() {
			if (this.formData.materialType) {
				this.showMaterial = true;
				return;
			}
			this.$message.warning('请选择原料类型！');
		},
		dataTableChange(type, data) {},
		/**
		 * 新增一行采购明细
		 */
		selMaterial(rows) {
			const tableDataIds = this.formData.details.map(item => item.materialId);
			for (let row of rows) {
				let materialId = row.id;
				if (tableDataIds.includes(materialId)) {
					continue;
				}
				this.formData.details.push({
					materialId: row.id, //原料id
					materialName: row.name, //原料id
					materialPp: row.brand, //供应商
					materialGg: row.specification, //原料规格
					supplierId: row.brandId, //供应商id
					currentPrice: row.price, //单价
					purchaseNum: 1, //数量
					totalPrice: row.price //总价
				});
			}
		},
		/**
		 * 表格行高
		 */
		tableRowClassName({ row, rowIndex }) {
			let styleRes = {
				height: '54px !important'
			};
			return styleRes;
		},
		/**
		 * 操作按钮点击事件
		 * @param {*} res
		 */
		async btnClick(res) {
			let code = res.handle.code;
			switch (code) {
				case 'add':
					this.formTitle = '新增';
					this.showAddTable = true;
					this.disabled = false;
					const snowflake = new SnowflakeId();
					this.formData = {
						id: snowflake.generate(),
						code: null,
						storeroom: null,
						materialType: null,
						status: null,
						details: []
					};
					break;
				case 'edit':
					{
						this.showAddTable = true;
						this.loading = true;
						this.formTitle = '编辑';
						this.disabled = false;
						this.$request({
							url: httpApi.info + '/' + res.row.id,
							method: 'GET'
						}).then(res => {
							this.loading = false;
							if (res.rCode == 0) {
								this.formData = res.results;
							} else {
								this.$message.error(res.msg);
							}
						});
					}
					break;
				case 'view':
					{
						this.loading = true;
						this.showAddTable = true;
						this.formTitle = '查看';
						this.disabled = true;
						this.$request({
							url: httpApi.info + '/' + res.row.id,
							method: 'GET'
						}).then(res => {
							this.loading = false;
							if (res.rCode == 0) {
								this.formData = res.results;
							} else {
								this.$message.error(res.msg);
							}
						});
					}
					break;
				case 'download':
					{
						//下载
						window.open(
							host + '/ybzy/hqpurchaseorder/downloadExcel?orderId=' + res.row.id,
							'_self'
						);
					}
					break;
				case 'delete':
					{
						this.$request({
							headers: {
								contentType: 'application/json'
							},
							url: httpApi.deleteById,
							method: 'POST',
							data: {
								id: res.row.id
							}
						}).then(res => {
							if (res.rCode == 0) {
								this.$message.success('删除成功');
								this.$refs.table.reload();
							} else {
								this.$message.error(res.msg);
							}
						});
					}
					break;
				case 'delete-row':
					{
						let index = this.formData.details.indexOf(res.row);
						if (index > -1) {
							this.formData.details.splice(index, 1);
						}
					}
					break;
				default:
					break;
			}
		},
		//校验,重复、数量、条数
		validateEvent() {
			if (!this.formData.storeroom) {
				this.$message.warning('请选择仓库！');
				return false;
			}
			if (!this.formData.materialType) {
				this.$message.warning('请选择原料类型！');
				return false;
			}
			let submitData = this.formData.details;
			if (submitData.length == 0) {
				this.$message.warning('请添加采购记录！');
				return false;
			}
			//提交数据中有采购数量为零的记录
			for (let i = 0; i < submitData.length; i++) {
				if (submitData[i].purchaseNum == 0) {
					this.$message.warning('你有采购数量未添加！');
					return false;
				}
			}
			return true;
		},
		/**
		 * 提交采购
		 */
		submitAll(status) {
			let bool = this.validateEvent();
			if (!bool) {
				return false;
			}
			let url = '';
			if (this.formTitle == '新增') {
				url = httpApi.save;
			} else {
				url = httpApi.update;
			}
			this.formData.status = status;
			this.$request({
				headers: {
					contentType: 'application/json'
				},
				url: url,
				method: 'POST',
				data: this.formData,
				format: false
			}).then(res => {
				if (res.rCode == 0) {
					this.$message.success('操作成功');
					this.showAddTable = false;
					this.formData = {
						id: null,
						code: null,
						storeroom: null,
						materialType: null,
						status: null,
						details: []
					};
					this.$refs.table.reload();
				} else {
					this.$message.error(res.msg);
				}
			});
		},
		/**
		 * 取消
		 */
		cancel() {
			this.showAddTable = false;
		},
		/**
		 * 根据原料id获取原料价格
		 */
		getPriceByMaterialId(id) {
			for (let i = 0; i < this.materialList.length; i++) {
				if (id == this.materialList[i].id) {
					return this.materialList[i];
				}
			}
		},
		/**
		 * 获取所有原料
		 */
		materialListAll() {
			let url = httpApi.materialListAll;
			this.$request({
				url: url,
				method: 'GET'
			}).then(res => {
				if (res.rCode == 0) {
					this.materialList = res.results;
				} else {
					this.$message.error(res.msg);
				}
			});
		},
		/**
		 * 获取所有供应商
		 */
		supplierListAll() {
			let url = httpApi.supplierListAll;
			this.$request({
				url: url,
				method: 'GET'
			}).then(res => {
				if (res.rCode == 0) {
					this.supplierList = res.results;
				} else {
					this.$message.error(res.msg);
				}
			});
		},
		/**
		 * 排序变化事件
		 * @param {*} column
		 * @param {*} prop
		 * @param {*} order
		 */
		sortChange(column, prop, order) {
			if (column.order == 'ascending') {
				//升序
				this.params = {
					asc: 'true',
					orderBy: column.prop
				};
			} else if (column.order == 'descending') {
				//降序
				this.params = {
					asc: 'false',
					orderBy: column.prop
				};
			} else {
				//不排序
				this.params = {
					asc: 'false',
					orderBy: 'createTime'
				};
			}
			this.$refs.table.reload();
		}
	}
};
</script>
<style scoped lang="scss">
@import '../../../assets/style/style.scss';

.content {
	width: 100%;
	height: 100%;
}

.el-dialog__body {
	.content-warning-Btn {
		float: right;
		margin: 10px 0px;
	}
	.btn-box {
		margin-top: 20px;
		display: flex;
		justify-content: flex-end;

		.btn {
			padding: 5px 10px;
			color: #666;
			border: 1px solid #eee;
			cursor: pointer;
			margin-right: 5px;
			border-radius: 4px;
		}
	}
}
.price {
	margin-left: 5px;
	color: #d40f0f;
	font-size: 15px;
}
</style>
