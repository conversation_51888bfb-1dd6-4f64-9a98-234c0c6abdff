<template>
	<es-form
		ref="form"
		:model="formData"
		:contents="formList"
		:submit="
			type === 'addApp' ||
			type === 'changeApp' ||
			type === 'addAppMenu' ||
			type === 'changeAppMenu' ||
			type === 'addType' ||
			type === 'changeType'
		"
		:readonly="isReadOnly"
		v-bind="$attrs"
		@submit="handleSubmit"
		@reset="handleCancel"
	></es-form>
</template>
<script>
import interfaceUrl from '@/http/platform/application.js';
import $ from 'eoss-ui/lib/utils/util';
import request from 'eoss-ui/lib/utils/http';
import SnowflakeId from 'snowflake-id';

export default {
	name: 'AddNew',
	props: {
		// 上级编码ID
		cciPid: {
			type: String
		},
		selectInfo: {
			type: Object
		},
		type: {
			type: String
		},
		isReadOnly: {
			type: Boolean,
			default: false
		},
		formInfo: {
			type: Object,
			default: () => {}
		},
		parentMenuList: {
			type: Array,
			default: () => []
		}
	},
	data() {
		return {
			formData: {
				...this.formInfo
			}
		};
	},
	computed: {
		formList() {
			switch (this.type) {
				case 'addApp': {
					return [
						{
							name: 'name',
							label: '应用名称',
							placeholder: '请输入应用名称',
							col: 6,
							rules: {
								required: true,
								message: '请输入应用名称',
								trigger: 'blur'
							}
						},
						{
							name: 'icon',
							label: 'icon',
							placeholder: '请输入icon',
							col: 6
						},
						{
							name: 'url',
							label: '应用地址',
							placeholder: '请输入应用地址',
							col: 12,
							rules: {
								required: true,
								message: '请输入应用地址',
								trigger: 'blur'
							}
						},
						{
							name: 'urlPe',
							label: '应用地址(PE)',
							placeholder: '请输入应用地址(PE)',
							col: 12
						},
						{
							name: 'logo',
							label: 'logo地址',
							placeholder: '请输入logo地址',
							col: 12,
							rules: {
								required: true,
								message: '请输入logo地址',
								trigger: 'blur'
							}
						},
						{
							name: 'loginoutUrl',
							label: '退出登录地址',
							placeholder: '请输入退出登录地址',
							col: 12
						},
						{
							name: 'callbackUrl',
							label: '回调地址',
							placeholder: '请输入回调地址',
							col: 12
						},
						{
							name: 'ssoUrl',
							label: '单点登录地址',
							placeholder: '请输入单点登录地址',
							col: 12
						},
						{
							name: 'configFormUrl',
							label: '配置表单接口',
							placeholder: '请输入配置表单接口',
							col: 12
						},
						{
							label: '应用归属',
							name: 'appBelong',
							placeholder: '请选择应用归属',
							type: 'radio',
							rules: {
								required: true,
								message: '请选择应用归属',
								trigger: 'blur'
							},
							verify: 'required',
							col: 6,
							sysCode: 'plat_app_belong',
							'label-key': 'shortName',
							'value-key': 'cciValue'
						},
						{
							label: '应用类型',
							name: 'appType',
							placeholder: '请选择应用类型',
							type: 'radio',
							rules: {
								required: true,
								message: '请选择应用类型',
								trigger: 'blur'
							},
							verify: 'required',
							col: 6,
							sysCode: 'plat_app_type',
							'label-key': 'shortName',
							'value-key': 'cciValue'
						},
						{
							label: '打开方式',
							name: 'openType',
							placeholder: '请选择打开方式',
							type: 'radio',
							rules: {
								required: true,
								message: '请选择打开方式',
								trigger: 'blur'
							},
							verify: 'required',
							col: 6,
							sysCode: 'plat_app_open_type',
							'label-key': 'shortName',
							'value-key': 'cciValue'
						},
						{
							label: '是否隐藏',
							name: 'isHidden',
							placeholder: '请选择是否隐藏',
							type: 'radio',
							rules: {
								required: true,
								message: '请选择是否隐藏',
								trigger: 'blur'
							},
							verify: 'required',
							col: 6,
							sysCode: 'yn_tag',
							'label-key': 'shortName',
							'value-key': 'cciValue'
						},
						{
							name: 'appId',
							label: 'appId',
							placeholder: '请输入appId',
							col: 6
						},
						{
							name: 'appSecret',
							label: 'appSecret',
							placeholder: '请输入appSecret',
							col: 6
						},
						{
							label: '排序号',
							name: 'sortNum',
							placeholder: '请输入排序号',
							type: 'number',
							controls: false,
							rules: {
								required: true,
								message: '请输入排序号',
								trigger: 'blur'
							},
							verify: 'required',
							col: 6
						},
						{
							name: 'params',
							label: '其它参数',
							placeholder: '请输入其它参数',
							type: 'textarea',
							col: 12
						},
						{
							name: 'remark',
							label: '说明',
							placeholder: '请输入说明',
							type: 'textarea',
							col: 12
						}
					];
				}
				case 'changeApp': {
					return [
						{
							name: 'name',
							label: '应用名称',
							placeholder: '请输入应用名称',
							col: 6,
							rules: {
								required: true,
								message: '请输入应用名称',
								trigger: 'blur'
							}
						},
						{
							name: 'icon',
							label: 'icon',
							placeholder: '请输入icon',
							col: 6
						},
						{
							name: 'url',
							label: '应用地址',
							placeholder: '请输入应用地址',
							col: 12,
							rules: {
								required: true,
								message: '请输入应用地址',
								trigger: 'blur'
							}
						},
						{
							name: 'urlPe',
							label: '应用地址(PE)',
							placeholder: '请输入应用地址(PE)',
							col: 12
						},
						{
							name: 'logo',
							label: 'logo地址',
							placeholder: '请输入logo地址',
							col: 12,
							rules: {
								required: true,
								message: '请输入logo地址',
								trigger: 'blur'
							}
						},
						{
							name: 'loginoutUrl',
							label: '退出登录地址',
							placeholder: '请输入退出登录地址',
							col: 12
						},
						{
							name: 'callbackUrl',
							label: '回调地址',
							placeholder: '请输入回调地址',
							col: 12
						},
						{
							name: 'ssoUrl',
							label: '单点登录地址',
							placeholder: '请输入单点登录地址',
							col: 12
						},
						{
							name: 'configFormUrl',
							label: '配置表单接口',
							placeholder: '请输入配置表单接口',
							col: 12
						},
						{
							label: '应用归属',
							name: 'appBelong',
							placeholder: '请选择应用归属',
							type: 'radio',
							rules: {
								required: true,
								message: '请选择应用归属',
								trigger: 'blur'
							},
							verify: 'required',
							col: 6,
							sysCode: 'plat_app_belong',
							'label-key': 'shortName',
							'value-key': 'cciValue'
						},
						{
							label: '应用类型',
							name: 'appType',
							placeholder: '请选择应用类型',
							type: 'radio',
							rules: {
								required: true,
								message: '请选择应用类型',
								trigger: 'blur'
							},
							verify: 'required',
							col: 6,
							sysCode: 'plat_app_type',
							'label-key': 'shortName',
							'value-key': 'cciValue'
						},
						{
							label: '打开方式',
							name: 'openType',
							placeholder: '请选择打开方式',
							type: 'radio',
							rules: {
								required: true,
								message: '请选择打开方式',
								trigger: 'blur'
							},
							verify: 'required',
							col: 6,
							sysCode: 'plat_app_open_type',
							'label-key': 'shortName',
							'value-key': 'cciValue'
						},
						{
							label: '是否隐藏',
							name: 'isHidden',
							placeholder: '请选择是否隐藏',
							type: 'radio',
							rules: {
								required: true,
								message: '请选择是否隐藏',
								trigger: 'blur'
							},
							verify: 'required',
							col: 6,
							sysCode: 'yn_tag',
							'label-key': 'shortName',
							'value-key': 'cciValue'
						},
						{
							name: 'appId',
							label: 'appId',
							placeholder: '请输入appId',
							col: 6
							// rules: {
							// 	required: true,
							// 	message: '请输入appId',
							// 	trigger: 'blur'
							// }
						},
						{
							name: 'appSecret',
							label: 'appSecret',
							placeholder: '请输入appSecret',
							col: 6
							// rules: {
							// 	required: true,
							// 	message: '请输入appSecret',
							// 	trigger: 'blur'
							// }
						},
						{
							name: 'fj1',
							label: '数字证书',
							type: 'attachment',
							value: '',
							onPreview: res => {
								console.log(res);
							},
							code: 'taskProject',
							// listType: 'picture-card',
							ownId: this.formData.id,
							limit: 1,
							col: 12,
							// selectType: 'icon-plus',
							param: {
								_tt: '12121'
							}
						},
						{
							type: 'select',
							label: '口令方式',
							placeholder: '请选择',
							name: 'passwordMethod',
							col: 6,
							default: true,
							valueType: 'string',
							data: [
								{
									value: '0',
									name: '静态口令'
								},
								{
									value: '1',
									name: '动态口令'
								}
							],
							events: {
								change: (res, val, index) => {
									console.log(res, val, index);
								}
							}
						},
						{
							label: '排序号',
							name: 'sortNum',
							placeholder: '请输入排序号',
							type: 'number',
							controls: false,
							rules: {
								required: true,
								message: '请输入排序号',
								trigger: 'blur'
							},
							verify: 'required',
							col: 6
						},
						{
							name: 'params',
							label: '其它参数',
							placeholder: '请输入其它参数',
							type: 'textarea',
							col: 12
						},
						{
							name: 'remark',
							label: '说明',
							placeholder: '请输入说明',
							type: 'textarea',
							col: 12
						}
					];
				}
				case 'addAppMenu': {
					return [
						{
							type: 'select',
							label: '父级菜单',
							placeholder: '请选择父级菜单',
							name: 'parentId',
							event: 'multipled',
							tree: true,
							valueKey: 'id',
							data: this.parentMenuList
						},
						{
							name: 'name',
							label: '菜单名称',
							placeholder: '请输入菜单名称',
							col: 12,
							rules: {
								required: true,
								message: '请输入菜单名称',
								trigger: 'blur'
							}
						},
						{
							name: 'url',
							label: '菜单地址',
							placeholder: '请输入菜单地址',
							col: 12
							// rules: {
							// 	required: true,
							// 	message: '请输入菜单地址',
							// 	trigger: 'blur'
							// }
						},
						{
							name: 'urlPe',
							label: '菜单地址(PE)',
							placeholder: '请输入菜单地址(PE)',
							col: 12
						},
						{
							name: 'logo',
							label: 'logo地址',
							placeholder: '请输入logo地址',
							col: 12
							// rules: {
							// 	required: true,
							// 	message: '请输入logo地址',
							// 	trigger: 'blur'
							// }
						},
						{
							name: 'icon',
							label: 'icon',
							placeholder: '请输入icon',
							col: 12
						},
						{
							name: 'componentUrl',
							label: '组件地址',
							placeholder: '请输入组件地址',
							col: 12
						},
						{
							name: 'redirect',
							label: 'redirect',
							placeholder: '请输入redirect',
							col: 12
						},
						{
							name: 'activeMenu',
							label: 'activeMenu',
							placeholder: '请输入activeMenu',
							col: 12
						},
						{
							label: '打开方式',
							name: 'openType',
							placeholder: '请选择打开方式',
							type: 'radio',
							rules: {
								required: true,
								message: '请选择打开方式',
								trigger: 'blur'
							},
							verify: 'required',
							col: 6,
							sysCode: 'plat_app_open_type',
							'label-key': 'shortName',
							'value-key': 'cciValue'
						},
						{
							label: '是否隐藏',
							name: 'isHidden',
							placeholder: '请选择是否隐藏',
							type: 'radio',
							rules: {
								required: true,
								message: '请选择是否隐藏',
								trigger: 'blur'
							},
							verify: 'required',
							col: 6,
							sysCode: 'yes_or_no',
							'label-key': 'shortName',
							'value-key': 'cciValue'
						},
						{
							label: '排序号',
							name: 'sortNum',
							placeholder: '请输入排序号',
							type: 'number',
							controls: false,
							rules: {
								required: true,
								message: '请输入排序号',
								trigger: 'blur'
							},
							verify: 'required',
							col: 12
						},
						{
							name: 'remark',
							label: '说明',
							placeholder: '请输入说明',
							type: 'textarea',
							col: 12
						}
					];
				}
				case 'changeAppMenu': {
					return [
						{
							type: 'select',
							label: '父级菜单',
							placeholder: '请选择父级菜单',
							name: 'parentId',
							event: 'multipled',
							tree: true,
							valueKey: 'id',
							labelKey: 'name',
							data: this.parentMenuList
						},
						{
							name: 'name',
							label: '菜单名称',
							placeholder: '请输入菜单名称',
							col: 12,
							rules: {
								required: true,
								message: '请输入菜单名称',
								trigger: 'blur'
							}
						},
						{
							name: 'url',
							label: '菜单地址',
							placeholder: '请输入菜单地址',
							col: 12
							// rules: {
							// 	required: true,
							// 	message: '请输入菜单地址',
							// 	trigger: 'blur'
							// }
						},
						{
							name: 'urlPe',
							label: '菜单地址(PE)',
							placeholder: '请输入菜单地址(PE)',
							col: 12
						},
						{
							name: 'logo',
							label: 'logo地址',
							placeholder: '请输入logo地址',
							col: 12
							// rules: {
							// 	required: true,
							// 	message: '请输入logo地址',
							// 	trigger: 'blur'
							// }
						},
						{
							name: 'icon',
							label: 'icon',
							placeholder: '请输入icon',
							col: 12
						},
						{
							name: 'componentUrl',
							label: '组件地址',
							placeholder: '请输入组件地址',
							col: 12
						},
						{
							name: 'redirect',
							label: 'redirect',
							placeholder: '请输入redirect',
							col: 12
						},
						{
							name: 'activeMenu',
							label: 'activeMenu',
							placeholder: '请输入activeMenu',
							col: 12
						},
						{
							label: '打开方式',
							name: 'openType',
							placeholder: '请选择打开方式',
							type: 'radio',
							rules: {
								required: true,
								message: '请选择打开方式',
								trigger: 'blur'
							},
							verify: 'required',
							col: 6,
							sysCode: 'plat_app_open_type',
							'label-key': 'shortName',
							'value-key': 'cciValue'
						},
						{
							label: '是否隐藏',
							name: 'isHidden',
							placeholder: '请选择是否隐藏',
							type: 'radio',
							rules: {
								required: true,
								message: '请选择是否隐藏',
								trigger: 'blur'
							},
							verify: 'required',
							col: 6,
							sysCode: 'yes_or_no',
							'label-key': 'shortName',
							'value-key': 'cciValue'
						},
						{
							label: '排序号',
							name: 'sortNum',
							placeholder: '请输入排序号',
							type: 'number',
							controls: false,
							rules: {
								required: true,
								message: '请输入排序号',
								trigger: 'blur'
							},
							verify: 'required',
							col: 12
						},
						{
							name: 'remark',
							label: '说明',
							placeholder: '请输入说明',
							type: 'textarea',
							col: 12
						}
					];
				}
				case 'addType': {
					return [
						{
							name: 'typeName',
							label: '类型名称',
							placeholder: '请输入类型名称',
							col: 6,
							rules: {
								required: true,
								message: '请输入类型名称',
								trigger: 'blur'
							}
						}
					];
				}
				case 'changeType': {
					return [
						{
							name: 'typeName',
							label: '类型名称',
							placeholder: '请输入类型名称',
							col: 6,
							rules: {
								required: true,
								message: '请输入类型名称',
								trigger: 'blur'
							}
						}
					];
				}
				default: {
					return [];
				}
			}
		}
	},
	created() {
		const snowflake = new SnowflakeId();
		let id = this.formInfo.id;
		if (this.type == 'addApp') {
			let typeId = this.formInfo.typeId;
			this.formData = { id: snowflake.generate(), typeId, sortNum: 1, isHidden: '0' };
		} else if (this.type == 'addAppMenu') {
			this.formData = { id: snowflake.generate(), sortNum: 1, isHidden: '0' };
			this.initparentMenu();
		} else if (this.type == 'addType') {
			let pid = this.formInfo.pid;
			this.formData = { id: snowflake.generate(), pid, sortNum: 1, isHidden: '0' };
		} else if (this.type == 'changeApp') {
			// 请求数据详情接口
			this.$request({
				url: interfaceUrl.appInfo + '/' + id,
				method: 'GET'
			}).then(res => {
				if (res.rCode == 0) {
					this.formData = res.results;
				} else {
					this.$message.error(res.msg);
				}
			});
		} else if (this.type == 'changeAppMenu') {
			// 请求数据详情接口
			this.$request({
				url: interfaceUrl.appMenuInfo + '/' + id,
				method: 'GET'
			}).then(res => {
				if (res.rCode == 0) {
					this.formData = res.results;
					this.initparentMenu();
				} else {
					this.$message.error(res.msg);
				}
			});
		} else if (this.type == 'changeType') {
			// 请求数据详情接口
			this.$request({
				url: interfaceUrl.typeInfo + '?id=' + id,
				method: 'GET'
			}).then(res => {
				if (res.rCode == 0) {
					this.formData = res.results;
				} else {
					this.$message.error(res.msg);
				}
			});
		}
	},
	methods: {
		initparentMenu() {
			// 获取父菜单选择列表
			let id = this.formInfo.id;
			let appId = this.formInfo.appId;
			this.$request({
				url: interfaceUrl.getTreeList,
				params: {
					id: id,
					appId: appId
				},
				method: 'GET'
			}).then(res => {
				if (res.rCode == 0) {
					this.parentMenuList = res.results;
				} else {
					this.$message.error(res.msg);
				}
			});
		},
		handleSubmit(data) {
			delete data.extMap;
			const loading = $.loading(this.$loading, '提交中');
			let url = '';
			let method = '';
			let formData = null;
			let flag = 'left'; // 定义左表格刷新还是右表格刷新
			switch (this.type) {
				case 'addType': {
					url = interfaceUrl.typeSave;
					method = 'POST';
					flag = 'tree';
					formData = { ...data };
					break;
				}
				case 'changeType': {
					url = interfaceUrl.typeUpdate;
					method = 'POST';
					formData = { ...data };
					flag = 'tree';
					break;
				}
				case 'changeApp': {
					url = interfaceUrl.appUpdate;
					method = 'POST';
					formData = { ...data };
					flag = 'left';
					break;
				}
				case 'addApp': {
					url = interfaceUrl.appSave;
					method = 'POST';
					flag = 'left';
					formData = { ...data };
					break;
				}
				case 'changeAppMenu': {
					url = interfaceUrl.appMenuUpdate;
					method = 'POST';
					flag = 'right';
					formData = { ...data };
					if (undefined != formData.parentId && undefined != formData.parentId.id) {
						formData.parentId = formData.parentId.id;
					}
					break;
				}
				case 'addAppMenu': {
					url = interfaceUrl.appMenuSave;
					method = 'POST';
					flag = 'right';
					formData = { ...data, appId: this.formInfo.appId };
					if (undefined != formData.parentId && undefined != formData.parentId.id) {
						formData.parentId = formData.parentId.id;
					}
					break;
				}
			}
			request({
				url,
				method,
				data: formData
			}).then(res => {
				loading.close();
				this.$emit('cancel', false);
				if (res.rCode === 0) {
					this.$emit('refresh', flag); // 刷新数据
					this.$message.success(res.msg);
				} else {
					this.$message.error(res.msg);
				}
			});
		},
		// 隐藏对话框
		handleCancel(event) {
			if (event.type === 'reset') {
				this.$emit('cancel', false);
			}
		}
	}
};
</script>
<style lang="scss" scoped>
::v-deep .es-dialog.is-middle .el-dialog {
	border: 1px solid red;
	height: 80%;
}
</style>
