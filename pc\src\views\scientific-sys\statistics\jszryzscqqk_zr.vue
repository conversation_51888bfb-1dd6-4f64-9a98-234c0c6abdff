<template>
	<div v-loading="loading" class="content">
		<es-data-table
			ref="table"
			:row-style="tableRowClassName"
			:thead="thead"
			:toolbar="toolbar"
			:border="true"
			filter
			:page="false"
			:url="basics.dataTableUrl"
			:parse-data="parseData"
			:param="params"
			show-label
			close
			@search="search"
			@reset="search({})"
			@btnClick="btnClick"
		></es-data-table>
	</div>
</template>

<script>
import { host } from '@/../config/config';
import qs from 'qs';
export default {
	data() {
		return {
			basics: {
				dataTableUrl: '/ybzy/paBaseInfo/statisticsScienceAchievementAndPatent', // 列表接口
				download: '/ybzy/paBaseInfo/exportScienceAchievementAndPatent' // 导出
			},
			loading: false,
			params: {},
			selectParams: {},
			pageOption: {
				layout: 'total, prev, pager, next, jumper',
				pageSize: 20,
				hideOnSinglePage: true,
				position: 'center',
				current: 1,
				pageNum: 1
			},
			codesObj: {} //批量数据字典
		};
	},
	computed: {
		thead() {
			return [
				{
					title: '受让方类型',
					align: 'center',
					field: 'transfereeType'
				},
				{
					title: '编号①',
					align: 'center',
					showOverflowTooltip: true,
					width: 80,
					field: 'A1'
				},
				{
					title: '合同数（项）',
					align: 'center',
					showOverflowTooltip: true,
					width: 140,
					field: 'L1'
				},
				{
					title: '合同金额（千元）',
					align: 'center',
					showOverflowTooltip: true,
					width: 140,
					field: 'L2'
				},
				{
					title: '当年实际收入（千元）',
					align: 'center',
					field: 'L3',
					showOverflowTooltip: true,
					width: 200
				},

				{
					align: 'center',
					title: '知识产权类型',
					field: 'iprType'
				},
				{
					title: '编号②',
					align: 'center',
					showOverflowTooltip: true,
					width: 80,
					field: 'B1'
				},
				{
					title: '申请数（项）',
					align: 'center',
					showOverflowTooltip: true,
					width: 140,
					field: 'L4'
				},
				{
					title: '授权数（项）',
					align: 'center',
					width: 140,
					showOverflowTooltip: true,
					field: 'L5'
				},
				{
					title: '专利拥有数（项）',
					align: 'center',
					width: 140,
					showOverflowTooltip: true,
					field: 'L6'
				}
			];
		},
		toolbar() {
			return [
				{
					type: 'button',
					contents: [
						{
							text: '导出',
							exportXls: true,
							icon: 'es-icon-daochu',
							type: 'primary',
							event: () => {
								const params = { ...this.params, ...this.selectParams };
								const url = host + this.basics.download + '?' + qs.stringify(params);
								window.open(url, '_self');
							}
						}
					]
				},
				{
					type: 'search',
					reset: true,
					showLabel: true,
					contents: [
						{
							type: 'daterange',
							name: 'dates',
							label: '时间范围',
							placeholder: '请选择时间范围'
						}
					]
				}
			];
		}
	},
	created() {
		const codes = 'member_nation,hq_person_education,hq_person_state';
		this.$utils.findSysCodeList(codes).then(res => {
			this.codesObj = res;
		});
	},
	methods: {
		//表格行高
		tableRowClassName({ row, rowIndex }) {
			let styleRes = { height: '54px !important' };
			return styleRes;
		},
		//操作按钮点击事件
		btnClick(res) {},
		search(res) {
			const paramsN = { ...res };
			if (paramsN.dates) {
				paramsN.startDate = paramsN.dates[0] || '';
				paramsN.endDate = paramsN.dates[1] || '';
				delete paramsN.dates;
			}
			this.params = {
				...{
					projectType: this.params.projectType,
					orderBy: this.params.orderBy,
					asc: this.params.asc // true 升序，false 降序
				},
				...paramsN
			};
			this.selectParams = paramsN;
		},
		parseData(res) {
			const obj = {
				records: res
			};
			return obj;
		},
		refreshData() {
			this.$refs.table.reload();
		}
	}
};
</script>

<style scoped lang="scss">
.content {
	width: 100%;
	height: 100%;
}
</style>
