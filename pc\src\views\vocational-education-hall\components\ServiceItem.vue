<template>
	<div class="item">
		<div class="header">{{ title }}</div>
		<div class="content" v-if="!(list.length == 0)">
			<div
				class="item-btn"
				v-for="(item, index) in list"
				:key="index"
				@click="toOpenWindow(item.url)"
			>
				<div class="pic-box">
					<img :src="handelPicUrl(item.icons)" alt="" width="28px" class="pic" />
				</div>
				<div class="titleName">{{ item.text }}</div>
			</div>
		</div>
	</div>
</template>

<script>
import { picUrl } from '@/config/index';
export default {
	props: {
		title: {
			typeof: String,
			default: () => {
				return '标题';
			}
		},
		dataList: {
			type: String,
			default: () => {
				return '';
			}
		}
	},
	watch: {
		dataList: {
			handler(val) {
				console.log(JSON.parse(val));
				this.list = JSON.parse(val);
			},
			immediate: true
		}
	},
	data() {
		return {
			list: []
		};
	},
	methods: {
		toOpenWindow(url) {
			window.open(`${picUrl}${url}`);
		},
		//处理图片路径
		handelPicUrl(url) {
			return `${picUrl}/static/andyui2.0/css/picIcon/img/${url}`;
		}
	}
};
</script>

<style lang="scss" scoped>
* {
	margin: 0;
	padding: 0;
}
.item {
	width: 100%;
	padding: 0 12px;
	.header {
		width: 100%;
		height: 54px;
		line-height: 54px;
		font-size: 17px;
		font-family:
			PingFangSC-Semibold,
			PingFang SC;
		font-weight: 600;
		color: #000000;
	}
	.content {
		border-radius: 6px;
		background: #ffffff;
		width: 100%;
		display: flex;
		justify-content: left;
		flex-wrap: wrap;
		padding: 0 0 15px 0;
		.item-btn {
			width: 25%;
			.pic-box {
				margin-top: 15px;
				width: 100%;
				height: 28px;
				overflow: hidden;
				display: flex;
				justify-content: center;
				.pic {
					height: 28px;
				}
			}
			.titleName {
				margin-top: 8px;
				text-align: center;
				height: 22px;
				font-size: 13px;
				font-family:
					PingFangSC-Regular,
					PingFang SC;
				font-weight: 400;
				color: #000000;
				line-height: 22px;
				white-space: nowrap;
				overflow: hidden;
				text-overflow: ellipsis;
			}
		}
	}
}
</style>
