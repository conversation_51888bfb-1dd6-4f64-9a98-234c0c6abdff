<template>
	<div class="statistics">
		<es-toolbar
			class="toolbar-box"
			:contents="contents"
			@search="onSearch"
			@reset="onReset"
			@change="change"
		></es-toolbar>
		<div v-loading="loading" class="chart-box">
			<div id="myEchart1" class="chart1"></div>
			<div id="myEchart2" class="chart2"></div>
			<div id="myEchart3" class="chart3"></div>
		</div>
	</div>
</template>
<script>
import api from '@/http/maintain/applicationApi';
import interfaceUrl from '@/http/common/system.js';
export default {
	data() {
		return {
			loading: false,
			params: {
				reportTime: ''
			},
			myChart: [],
			codeList: {},
			addressList: []
		};
	},
	computed: {
		contents() {
			return [
				{
					type: 'button',
					contents: [
						{
							text: '导出',
							type: 'primary',
							event: 'export',
							icon: 'el-icon-download',
							event: () => {
								let url = `${
									this.$host
								}/ybzy/hqrepairreport/exportStatisticsData?${this.objToUrlParams(this.params)}`;
								debugger;
								window.open(url);
							}
						}
					]
				},
				{
					type: 'search',
					reset: true,
					contents: [
						// {
						// 	type: 'select',
						// 	placeholder: '报修类别',
						// 	name: 'repairType',
						// 	event: 'multipled',
						// 	// sysCode: 'hq_repair_type',
						// 	'label-key': 'shortName',
						// 	'value-key': 'cciValue',
						// 	data: this.codeList.hq_repair_type,
						// 	clearable: true
						// },
						// {
						// 	type: 'select',
						// 	placeholder: '报修地点',
						// 	name: 'addressType',
						// 	event: 'multipled',
						// 	// sysCode: 'hq_repair_address_type',
						// 	'label-key': 'shortName',
						// 	'value-key': 'cciValue',
						// 	data: this.addressList,
						// 	clearable: true
						// },
						// {
						// 	type: 'year',
						// 	label: '日期时间',
						// 	name: 'reportTime',
						// 	placeholder: '报修时间',
						// 	col: 6
						// }
						{
							type: 'daterange',
							name: 'reportTime',
							default: true,
							placeholder: '日期',
							clearable: true
						}
					]
				}
			];
		}
	},
	created() {
		this.init();
		window.addEventListener('resize', this.onResize);
	},
	beforeDestroy() {
		window.removeEventListener('resize', this.onResize);
		if (this.myChart.length) {
			this.myChart.forEach((item, i) => {
				this.myChart[i] && this.myChart[i].dispose();
			});
		}
	},
	methods: {
		change(key, val) {
			this.params[key] = val;
		},
		objToUrlParams(obj) {
			return Object.keys(obj)
				.map(key => encodeURIComponent(key) + '=' + encodeURIComponent(obj[key]))
				.join('&');
		},
		onSearch(params) {
			this.init(params);
		},
		onReset(e) {
			this.params = {};
			this.init({});
		},
		init(params) {
			this.loading = true;
			Promise.all([this.queryStatistics(params)])
				.then(res => {
					this.loading = false;

					const chartData = JSON.parse(JSON.stringify(res[0]));

					this.$nextTick(() => {
						// dataX = ['子企业1', '子企业2', '子企业3', '子企业4', '子企业5'];
						// series = ['13.43', '33.08', '35.67', '11.5', '28.84'];
						this.toolChart(chartData.addressTypeTypeX, chartData.addressTypeTypeY, 'myEchart1', 0);
						this.toolChart(chartData.orgX, chartData.orgY, 'myEchart2', 1);
						this.toolpie(chartData.repairTypeX, chartData.repairTypeY, 'myEchart3', 2);
					});
				})
				.catch(err => {
					this.$message.error(err);
				});
		},
		// 请求报修统计分析
		queryStatistics(params) {
			return new Promise((resolve, reject) => {
				this.$request({
					url: api.statistics,
					params
				}).then(res => {
					if (res.rCode === 0) {
						resolve(res.results);
					} else {
						reject(res.msg);
					}
				});
			});
		},
		// 请求报修统计分析
		getRepairAddressList() {
			return new Promise((resolve, reject) => {
				this.$request({
					url: api.getBuildingParentList + '?states=1'
				}).then(res => {
					if (res.rCode === 0) {
						const newArr = res.results.map(e => {
							return {
								cciValue: e.id,
								shortName: e.roomName
							};
						});
						resolve(newArr);
					} else {
						reject(res.msg);
					}
				});
			});
		},
		// 查询数据字典
		findCodeData() {
			return new Promise((resolve, reject) => {
				// 判断codeList是否为空对象
				if (JSON.stringify(this.codeList) !== '{}') {
					resolve(this.codeList);
					return;
				}
				this.$request({
					url: interfaceUrl.findSysCodeList,
					data: {
						sysAppCodes: 'hq_repair_type,hq_repair_address_type'
					},
					method: 'POST'
				}).then(res => {
					if (res.rCode === 0) {
						resolve(res.results);
					} else {
						reject(res.msg);
					}
				});
			});
		},
		//  处理echarts数据并渲染
		toolChart(dataX = [], series = [], dom, i) {
			console.log(dataX, series, 'dataX,series');
			let chartDom = document.getElementById(dom);
			if (this.myChart[i]) {
				this.myChart[i].clear();
			} else {
				this.myChart[i] = this.$echarts.init(chartDom);
			}

			let showEchart = false;
			let namenum = 0;
			if (dataX.length > 0) {
				namenum = Math.floor(100 / (dataX.length / 3)); //这个3可以顺便调整是用来判断当前视图要显示几个
				console.log(namenum, 'namenum' + i);
				if (dataX.length > (i === 0 ? 12 : 8)) {
					//3也可以调整用来判断是否显示滚动条
					showEchart = true;
				} else {
					showEchart = false;
				}
			}
			const generatedColors = [];
			// 生成随机颜色
			function generateUniqueRandomColor() {
				let color;
				// 生成直到找到一个唯一的颜色
				do {
					// 生成随机rgba颜色
					color = `rgba(${Math.floor(Math.random() * 256)}, ${Math.floor(
						Math.random() * 256
					)}, ${Math.floor(Math.random() * 256)}, 0.85)`;
				} while (generatedColors.includes(color));

				// 将新生成的颜色添加到已生成的颜色列表中
				generatedColors.push(color);

				return color;
			}
			let option = {
				legend: {
					icon: 'rect',
					left: '1%',
					top: '2%',
					itemGap: 20, // 间距
					textStyle: {
						color: '#909cad'
					}
				},
				tooltip: {
					trigger: 'axis',
					axisPointer: {
						type: 'shadow' // 默认为直线，可选为：'line' | 'shadow'
					}
				},
				grid: {
					top: '50',
					left: '35',
					right: '25',
					bottom: '15',
					containLabel: true
				},
				dataZoom: [
					{
						type: 'slider',
						realtime: true,
						start: 0,
						end: namenum, // 数据窗口范围的结束百分比。范围是：0 ~ 100。
						height: 5, //组件高度
						left: 5, //左边的距离
						right: 5, //右边的距离
						bottom: 10, //下边的距离
						show: showEchart, // 是否展示
						fillerColor: 'rgba(17, 100, 210, 0.42)', // 滚动条颜色
						borderColor: 'rgba(17, 100, 210, 0.12)',
						handleSize: 0, //两边手柄尺寸
						showDetail: false, //拖拽时是否展示滚动条两侧的文字
						zoomLock: true, //是否只平移不缩放
						moveOnMouseMove: false, //鼠标移动能触发数据窗口平移
						//zoomOnMouseWheel: false, //鼠标移动能触发数据窗口缩放
						//下面是自己发现的一个问题，当点击滚动条横向拖拽拉长滚动条时，会出现文字重叠，导致效果很不好，以此用下面四个属性进行设置，当拖拽时，始终保持显示六个柱状图，可结合自己情况进行设置。添加这个属性前后的对比见**图二**
						startValue: 0, // 从头开始。
						endValue: i === 0 ? 11 : 7, // 最多六个
						minValueSpan: i === 0 ? 11 : 7, // 放大到最少几个
						maxValueSpan: i === 0 ? 11 : 7 //  缩小到最多几个
					},
					{
						type: 'inside', // 支持内部鼠标滚动平移
						start: 0,
						end: namenum,
						zoomOnMouseWheel: false, // 关闭滚轮缩放
						moveOnMouseWheel: true, // 开启滚轮平移
						moveOnMouseMove: true // 鼠标移动能触发数据窗口平移
					}
				],

				xAxis: {
					data: dataX,
					axisLabel: {
						color: '#909cad',
						interval: 0,
						rotate: 10
					}
				},
				yAxis: {
					type: 'value',
					name: '单位：单',
					nameTextStyle: {
						color: '#909cad'
					},
					axisLabel: {
						color: '#909cad'
					}
				},
				series: [
					{
						type: 'bar',
						barWidth: 20,
						data: series.map((value, index) => {
							return {
								value: value,
								itemStyle: {
									// color: colorList[index] // 假设getColor是一个返回随机颜色的函数
									// 随机颜色不重复
									color: generateUniqueRandomColor()
								}
							};
						})
					}
				]
			};

			option && this.myChart[i].setOption(option);
		},

		//  处理echarts数据并渲染
		toolpie(dataX = [], series = [], dom, i) {
			let chartDom = document.getElementById('myEchart3');
			if (this.myChart[i]) {
				this.myChart[i].clear();
			} else {
				this.myChart[i] = this.$echarts.init(chartDom);
			}
			const dataList = dataX.map((item, index) => {
				return {
					name: item,
					value: series[index]
				};
			});
			// const colorList = ['#4450BB', '#5F6CE6', '#58C9F7', '#F74D4E', '#FAC231'];
			const center = ['50%', '50%'];
			let option = {
				// color: colorList,
				tooltip: {
					trigger: 'item'
				},
				series: [
					// 外边设置
					{
						type: 'pie',
						center: center,
						radius: ['55%', '65%'],
						itemStyle: {
							color: '#F6F7F9'
						},
						label: {
							show: true
						},
						data: [0]
					},
					// 展示层
					{
						type: 'pie',
						center: center,
						radius: ['25%', '60%'],
						itemStyle: {
							borderWidth: 5, //描边线宽
							borderColor: '#fff'
						},
						label: {
							show: true,
							position: 'outside',
							alignTo: 'labelLine',
							backgroundColor: '#fff',
							height: 0,
							width: 0,
							lineHeight: 0,
							distanceToLabelLine: 0,
							borderRadius: 3,
							borderWidth: 1,
							borderColor: 'auto',
							padding: [3, -3, 3, -3],
							formatter: function (params) {
								return `{a|${params.name}}`;
							},
							rich: {
								a: {
									padding: [20, -80, 40, -80],
									fontSize: '12px',
									fontFamily: 'MicrosoftYaHei',
									color: '#091158'
								},
								b: {
									padding: [0, -80, 55, -80],
									fontSize: '15px',
									fontFamily: 'MicrosoftYaHei-Bold, MicrosoftYaHei',
									fontWeight: 'bold',
									color: '#001F3D'
								}
							}
						},
						labelLine: {
							show: false,
							normal: {
								length: 40,
								length2: 85,
								align: 'right',
								lineStyle: {
									width: 1
								}
							}
						},
						data: dataList
					}
				]
			};

			option && this.myChart[i].setOption(option);
		},
		onResize() {
			this.myChart.forEach((item, i) => {
				if (this.myChart[i]) {
					this.myChart[i].resize();
				}
			});
		}
	}
};
</script>
<style lang="scss" scoped>
.statistics {
	width: 100%;
	height: 100%;
	padding: 10px;
	background: rgb(243, 243, 244);
	.toolbar-box {
		border-radius: 8px;
		overflow: hidden;
	}
	.es-toolbar {
		border-bottom: none;
	}
	.chart-box {
		width: 100%;
		height: 100%;
		display: flex;
		flex-wrap: wrap;
		height: calc(100% - 56px);
		background: #fff;
		border-radius: 8px;
		margin-top: 10px;
		.chart1 {
			width: 100%;
			height: 50%;
		}
		.chart2 {
			width: 60%;
			height: 50%;
		}
		.chart3 {
			width: 40%;
			height: 50%;
		}
	}
}
</style>
