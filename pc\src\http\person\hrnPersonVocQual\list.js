import httpApi from './api.js';
export default {
	computed: {
		component() {
			return {
				// 树表结构
				//name: 'es-tree-group'
				// 纯列表结构
				name: 'es-data-table'
			};
		},
		treeParam() {
			return {
				// 不需要左边树 可以
				//url: httpApi.hrnPersonVocQualTree,
				//title: '分类树'
			};
		},
		table() {
			return {
				url: httpApi.hrnPersonVocQualList,
				param: { handlerType: '' },
				full: true,
				fit: true,
				page: true,
				stripe: true,
				numbers: true,
				'row-key': 'id',
				toolbar: [
					{
						type: 'button',
						contents: [
							{
								text: '新增',
								type: 'primary'
							}
						]
					},
					{
						type: 'search',
						reset: true,
						contents: [
							//{
							//	name: 'accountName_like',
							//	placeholder: '配置名称',
							//	label: '配置名称',
							//	col: 6
							//},
							{
								type: 'date',
								col: 6,
								name: 'updateTime_begin',
								label: '更新开始时间',
								placeholder: '更新开始时间'
							},
							{
								type: 'date',
								col: 6,
								name: 'updateTime_end',
								label: '更新结束时间',
								placeholder: '更新结束时间'
							}
						]
					}
				],
				thead: [
					{
						title: '基本信息ID',
						align: 'left',
						field: 'hrnPersonId'
					},
					{
						title: '证书名称',
						align: 'left',
						field: 'diplomaName'
					},
					{
						title: '证书等级',
						align: 'left',
						field: 'certificateLevel'
					},
					{
						title: '发证单位',
						align: 'left',
						field: 'issuedBy'
					},
					{
						title: '获证时间',
						align: 'left',
						field: 'certifiedTime'
					},
					{
						title: '证件附件',
						align: 'left',
						field: 'certifiedFile'
					},
					{
						title: '状态',
						align: 'left',
						field: 'status'
					},
					{
						title: '操作',
						type: 'handle',
						width: 180,
						events: [
							{
								text: '查看'
							},
							{
								text: '编辑'
							},
							{
								text: '删除'
							}
						]
					}
				]
			};
		}
	}
};
