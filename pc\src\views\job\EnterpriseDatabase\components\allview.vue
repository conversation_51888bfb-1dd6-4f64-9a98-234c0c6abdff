<template>
	<div class="template_div">
		<el-menu :default-active="activeMenus" mode="horizontal" class="es-menu" @select="handleSelect">
			<el-menu-item v-for="(item, index) in menus" :key="index" :index="String(index)">
				{{ item.label }}
			</el-menu-item>
		</el-menu>
		<div class="content">
			<enterpriseView
				v-if="showEnterpriseView"
				:select-info="selectInfo"
				type="view"
				style="height: calc(100% - 10px)"
				:form-info="formData"
				:region-data="regionData"
				@refresh="refreshData"
				@cancel="showEnterpriseView = $event"
			></enterpriseView>
			<enpcertapplyView
				v-if="showEnpcertapplyView"
				:select-info="applyInfo"
				type="view"
				style="height: calc(100% - 10px)"
				:form-info="applyFormData"
				@refresh="refreshData"
				@cancel="showEnterpriseView = $event"
			></enpcertapplyView>
			<div v-if="showEnterpriseLog" class="table_div">
				<es-data-table
					ref="table"
					:key="tableCount"
					:row-style="tableRowClassName"
					:full="true"
					:fit="true"
					:thead="thead"
					:border="true"
					:page="pageOption"
					:url="dataTableUrl"
					:numbers="true"
					:param="params"
					close
					@sort-change="sortChange"
				></es-data-table>
			</div>
		</div>
	</div>
</template>
<script>
import interfaceUrl from '@/http/platform/enterprise.js';
// import $ from 'eoss-ui/lib/utils/util';
// import request from 'eoss-ui/lib/utils/http';
import enterpriseView from './aduit.vue';
import enpcertapplyView from '../certapply/component/audit.vue';

export default {
	name: 'Audit',
	// components: { enterpriseView, enpcertapplyView },
	props: {
		selectInfo: {
			type: String
		},
		formInfo: {
			type: Object,
			default: () => {}
		},
		regionData: {
			type: Array,
			default: () => []
		}
	},
	data() {
		return {
			menus: [{ label: '企业信息', type: 'baseInfo' }],
			activeMenus: '0',
			formData: {},
			applyFormData: {},
			showTabIndex: 0,
			showEnterpriseView: true,
			showEnpcertapplyView: false,
			showEnterpriseLog: false,
			applyInfo: '',
			dataTableUrl: interfaceUrl.logListJson,
			tableCount: 1,
			pageOption: {
				layout: 'total, prev, pager, next, jumper',
				pageSize: 10,
				// hideOnSinglePage: true,
				position: 'right',
				current: 1,
				pageNum: 1
			},
			params: {
				asc: 'false',
				orderBy: 'createTime'
			},
			thead: [
				{
					title: '操作人',
					align: 'center',
					field: 'createUserName',
					sortable: 'custom'
				},
				{
					title: '操作时间',
					align: 'center',
					field: 'createTime',
					sortable: 'custom'
				},
				{
					title: '操作内容',
					align: 'left',
					field: 'logContent',
					sortable: 'custom'
				}
			]
		};
	},
	computed: {},

	created() {
		this.getMenus();
		this.formData = this.formInfo;
		this.dataTableUrl = interfaceUrl.logListJson + '?enterpriseId=' + this.selectInfo;
	},
	methods: {
		/**
		 * 表格行高
		 */
		tableRowClassName({ row, rowIndex }) {
			let styleRes = {
				height: '54px !important'
			};
			return styleRes;
		},
		/**
		 * 页签切换
		 * @param {*} res
		 */
		handleSelect(res) {
			let tabIndex = parseInt(res);
			let tabInfo = this.menus[tabIndex];
			if ('0' == res) {
				this.showEnterpriseView = true;
				this.showEnpcertapplyView = false;
				this.showEnterpriseLog = false;
			} else if ('certifiedInfo' == tabInfo.type) {
				this.showEnterpriseView = false;
				this.showEnpcertapplyView = true;
				this.showEnterpriseLog = false;
				this.applyFormData = tabInfo.rowData;
				this.applyInfo = tabInfo.infoId;
			} else {
				this.showEnterpriseView = false;
				this.showEnpcertapplyView = false;
				this.showEnterpriseLog = true;
			}
		},

		// 隐藏对话框
		handleCancel(event) {
			if (event.type === 'reset') {
				this.$emit('refresh'); // 刷新数据
				//this.formData1 = {};
			}
		},
		//获取企业已经提交的认证类型
		getMenus() {
			//行政区划
			this.$request({
				url: interfaceUrl.enterpriseCertifiedList + '?enterpriseId=' + this.selectInfo,
				method: 'get'
			}).then(res => {
				if (res.rCode == 0) {
					let data = res.results;
					for (let i in res.results) {
						let menu = {};
						menu.label = data[i].enterpriseTypeName;
						menu.infoId = data[i].id;
						menu.type = 'certifiedInfo'; //认证信息
						menu.rowData = data[i];
						this.menus.push(menu);
					}
					this.menus.push({ label: '操作日志', type: 'log' });
				}
			});
		},
		refreshData() {
			this.$emit('refresh');
		}
	}
};
</script>
<style lang="scss" scoped>
@import '../../../../assets/style/style';

.template_div {
	height: 100%;
}
.content {
	width: 100%;
	height: calc(100% - 50px);
	.table_div {
		height: calc(100% - 70px);
	}
	::v-deep .es-data-table {
		flex: 1;
		display: flex;
		flex-direction: column;
		height: calc(100% - 58px);

		.es-data-table-content {
			flex: 1;
			height: 0;
			display: flex;
			flex-direction: column;

			.el-table {
				flex: 1;
				// height: 100% !important;
			}

			.es-thead-border {
				.el-table__header {
					th {
						border-right: 1px solid #e1e1e1;
					}
				}
			}
		}
	}
}

.el-dialog__body {
	overflow: auto !important;
	.es-form .es-collapse {
		height: 100%;
	}
	height: 100%;

	.btn-box {
		margin-top: 20px;
		display: flex;
		justify-content: flex-end;

		.btn {
			padding: 5px 10px;
			color: #666;
			border: 1px solid #eee;
			cursor: pointer;
			margin-right: 5px;
			border-radius: 4px;
			// &.theme {
			// 	background: $--color-primary;
			// 	color: #fff;
			// 	border-color: $--color-primary;
			// }
		}
	}
}
</style>
