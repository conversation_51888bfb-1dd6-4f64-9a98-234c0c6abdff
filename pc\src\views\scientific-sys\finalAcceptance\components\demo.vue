<template>
	<es-dialog title="结项" :visible="flowDialog" :drag="false" size="full" @close="close">
		<div class="es-flow-group">
			<es-tabs v-model="activeName" class="es-flow-tabs es-tabs" @tab-click="handleClick">
				<el-tab-pane label="基本信息" name="info">
					<el-form ref="elform" :model="formData" label-width="120px" class="is-form">
						<el-form-item label="结题说明">
							<el-input v-model="formData.specification" type="textarea"></el-input>
						</el-form-item>
						<el-form-item label="项目成果">
							<ul class="table-box">
								<li class="row is-head">
									<div>项目成果类型</div>
									<div>附件</div>
									<div class="btn">
										<es-button
											type="primary"
											size="mini"
											icon="es-icon-jiahao"
											circle
											@click="addTableRow"
										/>
									</div>
								</li>
								<li v-for="(item, index) in tableData" :key="index" class="row is-body">
									<div>
										<es-select
											v-model="item.achievementType"
											placeholder="请选择"
											sys-code="project_close_type"
											value-type="object"
										></es-select>
									</div>
									<div>
										<es-upload v-bind="item.attrs" :multiple="false" :limit="1"></es-upload>
									</div>
									<div class="btn">
										<es-button
											type="danger"
											size="mini"
											icon="es-icon-jianhao"
											circle
											@click="delTableRow(index)"
										/>
									</div>
								</li>
							</ul>
						</el-form-item>
						<el-form-item label="结题佐证材料" prop="upload" required>
							<div class="download" @click="download">
								<i class="el-icon-download"></i>
								下载结题佐证材料模板
							</div>
							<es-upload ref="refUpload" v-bind="attrs"></es-upload>
						</el-form-item>
					</el-form>
				</el-tab-pane>
			</es-tabs>
			<es-flow
				:is-start-flow="true"
				:business-id="businessId"
				:flow-type-code="flowTypeCode"
				:sub-fun="handleSub"
				:btn-list="btnList"
				class="es-group-flow"
				@success="handleSuccess"
			></es-flow>
		</div>
	</es-dialog>
</template>

<script>
import { saveFinalAcceptance } from '@/api/scientific-sys.js';
import { v4 as uuidv4 } from 'uuid';

export default {
	model: {
		prop: 'flowDialog',
		event: 'change'
	},
	props: {
		rowId: {
			type: String,
			required: true
		},
		flowDialog: {
			type: Boolean,
			required: true
		}
	},
	data() {
		return {
			attrs: {
				code: 'transationform_editfile',
				ownId: uuidv4(),
				// 业务id
				// fileSize: '20023KB',
				download: true,
				dragSort: true //是否允许附件列表进行拖拽排序
			},
			dialog: false,
			activeName: 'info',
			formData: {
				specification: '', //结题说明
				projectId: this.rowId, //项目id
				proofAdjunctId: '' //结项佐证材料附件id
			},
			tableData: [
				{
					attrs: {
						code: 'transationform_editfile',
						ownId: uuidv4(),
						// 业务id
						download: true,
						dragSort: true //是否允许附件列表进行拖拽排序
					}
				}
			],

			businessId: uuidv4(),
			// businessId: this.rowId,
			flowTypeCode: 'conclusion',
			btnList: [{ text: '提交', event: 'sub', type: 'primary' }]
		};
	},

	created() {
		// this.formData.projectId = this.rowId;
	},
	methods: {
		// handleChange(val) {
		// 	console.log(val);
		// },
		download() {
			window.open(
				this.$host +
					'/project-ybzy/picture/sys/%E7%A7%91%E6%8A%80%E4%B8%8E%E7%A4%BE%E4%BC%9A%E6%9C%8D%E5%8A%A1%E5%A4%84%E5%85%B3%E4%BA%8E%E5%86%8D%E6%AC%A1%E6%B8%85%E7%90%86%E9%99%A2%E7%BA%A7%E7%A7%91%E7%A0%94%E9%A1%B9%E7%9B%AE%E5%BA%94%E7%BB%93%E9%A2%98%E7%9A%84%E9%80%9A%E7%9F%A524.3.19.rar'
			);
		},
		addTableRow() {
			this.tableData.push({
				attrs: {
					code: 'transationform_editfile',
					ownId: uuidv4(),
					// 业务id
					download: true,
					dragSort: true //是否允许附件列表进行拖拽排序
				}
			});
		},
		delTableRow(index) {
			this.$delete(this.tableData, index);
		},
		close() {
			this.$emit('change', false);
		},
		handleClick(tab, event) {
			console.log(tab, event);
		},
		handleSuccess() {
			console.log('办理成功');
			this.$emit('reload');
			this.close();
		},
		async handleSub(callBank) {
			const refUpload = this.$refs.refUpload.filesTotalSize;
			if (refUpload < 1) {
				this.$message.error('请上传结项佐证材料');
				return;
			}
			await this.submitForm();
			callBank();
		},
		async submitForm() {
			try {
				const loading = this.$.loading(this.$loading, '提交中...');
				// let formData = { ...this.formData };
				// delete formData.transationform_editfile;
				// delete formData.materialsAdjunctFile;
				// delete formData.contractAdjunctFile;
				let tableData = this.tableData.map(item => {
					return {
						achievementName: item.achievementType.shortName, //成果类型中文
						achievementType: item.achievementType.cciValue, //成果类型编码
						adjunctId: item.attrs.ownId //附件id
					};
				});
				let formData = {
					...this.formData,
					id: this.businessId, //业务id
					proofAdjunctId: this.attrs.ownId, //	结项佐证材料附件id
					list: tableData //结题申请列表
				};
				console.log('>>>tableData', formData);
				let { rCode, msg, results } = await this.$.ajax({
					url: saveFinalAcceptance,
					method: 'post',
					format: false,
					data: formData
				});
				loading.close();
				if (rCode == 0) {
					this.$message.success(msg);
					this.$bus.$emit('closeDialog', true);
				} else {
					this.$message.error(msg);
				}
			} catch (error) {
				console.log('>>>error', error);
			}
		}
	}
};
</script>
<style lang="scss" scoped>
@import '@ast/style/public.scss';
$--border-base: 1px solid #d1d1d1;
$--border-color: #d1d1d1;
// ::v-deep .el-dialog__body{
// 	border: 1px solid red;
// 		@include flexBox(flex-start);

// }
// .is-tabs{
// 	width: 600px;
// 	height: 100%;
// }
.es-flow-group {
	height: 100%;
	display: flex;
	border: $--border-base;
	.es-flow-tabs {
		flex: 1;
		box-shadow: none;
		height: 100%;
		position: relative;
		border: 0;
		overflow: auto;

		.el-tabs__content {
			position: absolute;
			top: 45px;
			left: 0;
			right: 0;
			bottom: 0;
			overflow: hidden;
			padding: 0;
			.el-tab-pane {
				height: 100%;
				overflow: hidden;
				padding: 12px;
				.es-tab-pane-list {
					flex: 1;
					position: relative;
					& + .es-tab-pane-list {
						margin-top: 20px;
					}
					.es-tab-pane-main {
						position: absolute;
						left: 0;
						top: 0;
						right: 0;
						bottom: 0;
					}
				}
				.es-form-search-nobtn {
					.el-input__inner {
						border: $--border-base;
						border-radius: 10px;
					}
				}
				.el-form-item__content,
				.es-flow-list {
					.es-toolbar {
						border: $--border-base;
						border-bottom: 0;
					}
				}
				.es-flow-list {
					.es-toolbar {
						background-color: #fff;
					}
					.es-data-table-content {
						padding: 0;
					}
				}
				.es-attachment {
					border: $--border-base;
					.el-upload--handle {
						padding: 6px;
						width: 100%;
						border-bottom: $--border-base;
						text-align: right;
						background-color: #fff;
					}
				}
				.es-flow-group-data-table {
					.es-toolbar {
						border: 0;
					}
					.es-data-table-content {
						padding: 0;
					}
				}
			}
		}
	}
	.es-group-flow {
		width: 360px;
		border-left: $--border-base;
		&.es-flow-narrow {
			background-color: $--border-color;
			.es-flow-title {
				padding: 0;
			}
		}
		.es-flow-title {
			padding: 0 8px;
			height: 44px;
			background-color: $--border-color;
			border-bottom: $--border-base;
		}
	}
	&.is-hide-flow {
		.es-flow-tabs {
			border: 0;
		}
	}
}
.table-box {
	max-height: 400px;
	overflow: auto;
	.row {
		@include flexBox();
		width: 100%;
		height: 80px;
		border: 1px solid $--border-color;
		& > div {
			height: 100%;
			// line-height: 50px;
			// text-align: center;
			padding: 0 10px;
			@include flexBox();

			&:nth-of-type(1) {
				border-right: 1px solid $--border-color;
				width: 180px;
				padding: 0 10px;
			}
			&:nth-of-type(2) {
				@include flexBox(flex-start);
				border-right: 1px solid $--border-color;
				flex: 1;
			}
			&:nth-of-type(3) {
				width: 100px;
			}
		}
	}

	.is-head {
		height: 50px;
		font-weight: 550;
	}
	.btn {
		.el-button {
			width: 26px;
		}
	}
}

::v-deep .el-tab-pane {
	overflow: auto;
}
::v-deep .el-form {
	overflow: auto;
}
::v-deep .el-tabs__header {
	background: #fafafa !important;
}
::v-deep .el-tabs__nav-wrap {
	padding: 4px 8px 0 8px !important;
}
.is-form {
	height: 100%;
	overflow: auto;
	.download {
		color: #107fc9;
		cursor: pointer;
	}
}
</style>
