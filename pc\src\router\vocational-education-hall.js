export default [
	{
		path: '/backlog',
		title: '我的待办',
		name: 'Backlog',
		component: resolve => require(['@/views/vocational-education-hall/backlog.vue'], resolve)
	},
	{
		path: '/usefulapp',
		title: '我的常用应用',
		name: 'Usefulapp',
		component: resolve => require(['@/views/vocational-education-hall/usefulapp.vue'], resolve)
	},
	{
		path: '/guidance',
		title: '办事指南',
		name: 'Guidance',
		component: resolve => require(['@/views/vocational-education-hall/guidance.vue'], resolve)
	},
	{
		path: '/serviceCenter',
		title: '服务中心',
		name: 'ServiceCenter',
		component: resolve => require(['@/views/vocational-education-hall/serviceCenter.vue'], resolve)
	},
	{
		path: '/home',
		title: '主页',
		name: 'Home',
		component: resolve => require(['@/views/home20240423/index.vue'], resolve)
	},
	{
		path: '/home20240110',
		title: '主页',
		name: 'Home20240110',
		component: resolve => require(['@/views/home20240110/index.vue'], resolve)
	},
	{
		path: '/home20240423',
		title: '主页',
		name: 'Homehome20240423',
		component: resolve => require(['@/views/home20240423/index.vue'], resolve)
	},
	{
		path: '/notice',
		title: '通知公告',
		name: 'Notice',
		component: resolve => require(['@/views/home20240423/notice'], resolve)
	},
	{
		path: '/noticeDetail',
		title: '通知公告详情',
		name: 'NoticeDetail',
		component: resolve => require(['@/views/home20240423/notice/noticeDetail'], resolve)
	},
	{
		path: '/serveCar',
		title: '业务直通车',
		name: 'ServeCar',
		component: resolve => require(['@/views/home20240423/serveCar'], resolve)
	},
];
