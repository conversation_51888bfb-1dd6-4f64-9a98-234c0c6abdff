<template>
	<div class="project">
		<div class="project-title">
			<div class="project-title-icon"></div>
			<div class="project-title-text">项目统计</div>
		</div>
		<div class="project-content">
			<e-pie :results="piesResults" :echart-dom="'pies'" @transfer="handlePie"></e-pie>
			<e-bar
				:series-data="allResults.seriesData"
				:x-data="allResults.xData"
				:echart-dom="'allResults'"
				class="project-con"
				:color-list="[
					'#37B3FF',
					'#78DE0D',
					'#12CDA9',
					'#FF7F5F',
					'#FFB952',
					'#FFF200',
					'#8A2BE2',
					'#FF69B4',
					'#2E8B57',
					'#800080'
				]"
				@changeDate="handleHistogram"
			></e-bar>
		</div>
		<div class="project-content2">
			<e-bar
				:series-data="longitudinalResults.seriesData"
				:x-data="longitudinalResults.xData"
				:echart-dom="'longitudinalResults'"
				class="project-con2"
				:color-list="['#37B3FF', '#43E2FF', '#7487F6']"
				title="纵向项目类型统计"
				@changeDate="handleHistogram"
			></e-bar>
			<e-bar
				:series-data="transverseResults.seriesData"
				:x-data="transverseResults.xData"
				:echart-dom="'transverseResults'"
				class="project-con2"
				:color-list="['#FF7F5F', '#0753BE', '#7ED321', '#37B3FF', '#FFB952']"
				title="横向项目类型统计"
				@changeDate="handleHistogram"
			></e-bar>
			<e-bar
				:series-data="facultyResults.seriesData"
				:x-data="facultyResults.xData"
				:echart-dom="'facultyResults'"
				class="project-con2"
				:color-list="['#0D74FE', '#FBAA4E', '#FEDE00']"
				title="院系项目类型统计"
				@changeDate="handleHistogram"
			></e-bar>
		</div>
	</div>
</template>

<script>
import EPie from './e-pie.vue';
import EBar from './e-bar.vue';
import {
	projectClassifyStatistics,
	projectStatisticsByGroup,
	paStatisticsByGroup
} from '@/api/scientific-sys.js';

export default {
	name: 'ProjectStatistics',
	components: {
		EPie,
		EBar
	},
	data() {
		return {
			piesResults: {}, //存储第一个饼图数据
			allResults: {
				xData: [],
				seriesData: []
			}, //存储第一个柱形图数据
			longitudinalResults: {
				xData: [],
				seriesData: []
			}, //存储第二个柱形图数据
			transverseResults: {
				xData: [],
				seriesData: []
			}, //存储第三个柱形图数据
			facultyResults: {
				xData: [],
				seriesData: []
			}, //存储第四个柱形图数据
			urlList: '',
			year: '2023'
		};
	},
	mounted() {
		this.handlePie(); //获取饼形图数据
		// this.handleHistogram(); //获取柱状图数据
		this.getData();
	},
	methods: {
		async handlePie(setYear = '2024') {
			const loading = this.load('查询中...');
			try {
				let { rCode, msg, results } = await this.$.ajax({
					url: projectClassifyStatistics,
					method: 'POST',
					data: { year: setYear },
					format: true
				});
				if (rCode == 0) {
					// this.piesResults = resultsTest.results;
					this.piesResults = results;
				} else {
					this.$message.error(msg);
				}
			} catch {
				this.$message.error('查询失败');
			} finally {
				loading.close();
			}
		},
		// 首次加载获取图表数据
		async getData() {
			const loading = this.load('查询中...');
			let arr = ['allResults', 'longitudinalResults', 'transverseResults', 'facultyResults'];
			for (var i = 0; i < 4; i++) {
				await this.handleHistogram(1, '', arr[i]);
			}
			loading.close();
		},
		async handleHistogram(statusYear = 1, statusMonth, keyName) {
			const loading = this.load('查询中...');
			const KEY_NAME = {
				allResults: 0,
				longitudinalResults: 1,
				transverseResults: 2,
				facultyResults: 3
				// longitudinalResults: 1,
				// longitudinalResults: 1
			};
			let histogramType = KEY_NAME[keyName];
			try {
				let { rCode, msg, results } = await this.$.ajax({
					url: histogramType == 0 ? paStatisticsByGroup : projectStatisticsByGroup,
					method: 'POST',
					data: { type: histogramType, year: statusYear, month: statusMonth },
					format: true
				});
				if (rCode == 0) {
					// console.log('测试数据>>>>>', resultsTest2.results);
					switch (histogramType) {
						case 0:
							{
								// results = resultsTest0.results;
								const base_status_type = [
									{
										create_org: null,
										create_user_id: null,
										create_time: null,
										create_dept: null,
										create_identity: null,
										cci_value_type: '1',
										description: '',
										create_subcenter: null,
										remark: null,
										sort: 1,
										code_id: 'c702a287779d42649a13e8b39d779ab5',
										update_time: null,
										extension3: null,
										update_user_id: null,
										extension1: null,
										extension2: null,
										subsystem_id: null,
										cci_pid: null,
										cci_value: 'academicPaper',
										short_name: '学术论文',
										show_index: null,
										state: 1,
										id: '1685bffbad4146f4be0cfd61c70aef53'
									},
									{
										create_org: null,
										create_user_id: null,
										create_time: null,
										create_dept: null,
										create_identity: null,
										cci_value_type: '1',
										description: '',
										create_subcenter: null,
										remark: null,
										sort: 2,
										code_id: 'c702a287779d42649a13e8b39d779ab5',
										update_time: null,
										extension3: null,
										update_user_id: null,
										extension1: null,
										extension2: null,
										subsystem_id: null,
										cci_pid: null,
										cci_value: 'academicPatent',
										short_name: '学术专利',
										show_index: null,
										state: 1,
										id: 'ce351a6344e04d17921c35bf4407b389'
									},
									{
										create_org: null,
										create_user_id: null,
										create_time: null,
										create_dept: null,
										create_identity: null,
										cci_value_type: '1',
										description: '',
										create_subcenter: null,
										remark: null,
										sort: 3,
										code_id: 'c702a287779d42649a13e8b39d779ab5',
										update_time: null,
										extension3: null,
										update_user_id: null,
										extension1: null,
										extension2: null,
										subsystem_id: null,
										cci_pid: null,
										cci_value: 'academicWriting',
										short_name: '学术著作',
										show_index: null,
										state: 1,
										id: '64b91c9b571b4f4f95589dae774a58f2'
									},
									{
										create_org: null,
										create_user_id: null,
										create_time: null,
										create_dept: null,
										create_identity: null,
										cci_value_type: '1',
										description: '',
										create_subcenter: null,
										remark: null,
										sort: 4,
										code_id: 'c702a287779d42649a13e8b39d779ab5',
										update_time: null,
										extension3: null,
										update_user_id: null,
										extension1: null,
										extension2: null,
										subsystem_id: null,
										cci_pid: null,
										cci_value: 'awards',
										short_name: '科研获奖',
										show_index: null,
										state: 1,
										id: '7c8ef5ffd2ef4fbbbf2876f8faa58d54'
									},
									{
										create_org: null,
										create_user_id: null,
										create_time: null,
										create_dept: null,
										create_identity: null,
										cci_value_type: '1',
										description: '',
										create_subcenter: null,
										remark: null,
										sort: 5,
										code_id: 'c702a287779d42649a13e8b39d779ab5',
										update_time: null,
										extension3: null,
										update_user_id: null,
										extension1: null,
										extension2: null,
										subsystem_id: null,
										cci_pid: null,
										cci_value: 'platformTeam',
										short_name: '平台团队',
										show_index: null,
										state: 1,
										id: 'c67a0682aedb47aab1997d016a72ba9f'
									},
									{
										create_org: null,
										create_user_id: null,
										create_time: null,
										create_dept: null,
										create_identity: null,
										cci_value_type: '1',
										description: '',
										create_subcenter: null,
										remark: null,
										sort: 6,
										code_id: 'c702a287779d42649a13e8b39d779ab5',
										update_time: null,
										extension3: null,
										update_user_id: null,
										extension1: null,
										extension2: null,
										subsystem_id: null,
										cci_pid: null,
										cci_value: 'scienceAchievement',
										short_name: '科技成果转化',
										show_index: null,
										state: 1,
										id: '760cb21675474afdb253623634d07652'
									},
									{
										create_org: null,
										create_user_id: null,
										create_time: null,
										create_dept: null,
										create_identity: null,
										cci_value_type: '1',
										description: '',
										create_subcenter: null,
										remark: null,
										sort: 7,
										code_id: 'c702a287779d42649a13e8b39d779ab5',
										update_time: null,
										extension3: null,
										update_user_id: null,
										extension1: null,
										extension2: null,
										subsystem_id: null,
										cci_pid: null,
										cci_value: 'softwareCopyright',
										short_name: '软著',
										show_index: null,
										state: 1,
										id: '319f899e7e574ed989f12e7ee38d386b'
									},
									{
										create_org: null,
										create_user_id: null,
										create_time: null,
										create_dept: null,
										create_identity: null,
										cci_value_type: '1',
										description: '',
										create_subcenter: null,
										remark: null,
										sort: 8,
										code_id: 'c702a287779d42649a13e8b39d779ab5',
										update_time: null,
										extension3: null,
										update_user_id: null,
										extension1: null,
										extension2: null,
										subsystem_id: null,
										cci_pid: null,
										cci_value: 'technicalProducts',
										short_name: '技术产品',
										show_index: null,
										state: 1,
										id: 'b4fc4f7c846344a2ae0185a14849800a'
									},
									{
										create_org: null,
										create_user_id: null,
										create_time: null,
										create_dept: null,
										create_identity: null,
										cci_value_type: '1',
										description: '',
										create_subcenter: null,
										remark: null,
										sort: 9,
										code_id: 'c702a287779d42649a13e8b39d779ab5',
										update_time: null,
										extension3: null,
										update_user_id: null,
										extension1: null,
										extension2: null,
										subsystem_id: null,
										cci_pid: null,
										cci_value: 'technicalStandard',
										short_name: '技术标准',
										show_index: null,
										state: 1,
										id: 'ada4e93cc430460e82ecee79cf5a3bdc'
									},
									{
										create_org: null,
										create_user_id: null,
										create_time: null,
										create_dept: null,
										create_identity: null,
										cci_value_type: '1',
										description: '',
										create_subcenter: null,
										remark: null,
										sort: 9,
										code_id: 'c702a287779d42649a13e8b39d779ab5',
										update_time: null,
										extension3: null,
										update_user_id: null,
										extension1: null,
										extension2: null,
										subsystem_id: null,
										cci_pid: null,
										cci_value: 'competition',
										short_name: '竞赛获奖',
										show_index: null,
										state: 1,
										id: 'ada4e93cc430460e82ecee79cf5a3bdc'
									}
								];
								let xData = [],
									seriesData = [
										// { name: '院级项目', data: [] },
										// { name: '横向项目', data: [] },
										// { name: '纵向项目', data: [] }
									];
								xData = results.xAxis;
								seriesData = base_status_type.map(item => {
									return { name: item.short_name, data: results[item.cci_value] };
									// xData.push(item.year || item.month);
									// seriesData[0].data.push(item.collegeCount);
									// seriesData[1].data.push(item.crosswiseCount);
									// seriesData[2].data.push(item.lengthWaysCount);
								});

								this.allResults = { xData: xData.length ? xData : ['暂无数据'], seriesData };
								console.log('>>>allResults', this.allResults);
							}
							break;
						case 1:
							{
								let xData1 = [],
									seriesData1 = [
										{ name: '市级项目', data: [] },
										{ name: '省级项目', data: [] },
										{ name: '国家级项目', data: [] }
									];
								results.forEach(item => {
									xData1.push(item.year || item.month);
									seriesData1[0].data.push(item.lengthWaysCityCount);
									seriesData1[1].data.push(item.lengthWaysProvinceCount);
									seriesData1[2].data.push(item.lengthWaysCountryCount);
								});
								this.longitudinalResults = {
									xData: xData1.length ? xData1 : ['暂无数据'],
									seriesData: seriesData1
								};
							}
							break;
						case 2:
							{
								let xData2 = [],
									seriesData2 = [
										{ name: '技术开发', data: [] },
										{ name: '技术转让', data: [] },
										{ name: '技术资讯', data: [] },
										{ name: '技术服务', data: [] },
										{ name: '其他', data: [] }
									];
								results.forEach(item => {
									xData2.push(item.year || item.month);
									seriesData2[0].data.push(item.crosswiseContractType0Count);
									seriesData2[1].data.push(item.crosswiseContractType1Count);
									seriesData2[2].data.push(item.crosswiseContractType2Count);
									seriesData2[2].data.push(item.crosswiseContractType3Count);
									seriesData2[2].data.push(item.crosswiseContractType4Count);
								});
								this.transverseResults = {
									xData: xData2.length ? xData2 : ['暂无数据'],
									seriesData: seriesData2
								};
							}
							break;
						case 3:
							{
								let xData3 = [],
									seriesData3 = [
										{ name: '重大', data: [] },
										{ name: '重点', data: [] },
										{ name: '一般', data: [] }
									];
								results.forEach(item => {
									xData3.push(item.year || item.month);
									seriesData3[0].data.push(item.collegeProjectLevel0Count);
									seriesData3[1].data.push(item.collegeProjectLevel1Count);
									seriesData3[2].data.push(item.collegeProjectLevel2Count);
								});
								this.facultyResults = {
									xData: xData3.length ? xData3 : ['暂无数据'],
									seriesData: seriesData3
								};
							}
							break;
						default:
							break;
					}
				} else {
					this.$message.error(msg);
				}
			} catch (err) {
				this.$message.error('查询失败');
				console.error('>>>err', err);
			} finally {
				loading.close();
			}
		},
		// 页面加载
		load(text) {
			return this.$.loading(this.$loading, text);
		}
	}
};
</script>

<style scoped lang="scss">
.project {
	border-radius: 8px;
	// margin-top: 12px;
	width: 100%;
	&-title {
		display: flex;
		align-items: center;
		height: 40px;
		margin: 5px 0 11px 11px;
		// border-bottom: 1px solid #ecf0f4;
		&-icon {
			width: 16px;
			height: 16px;
			background: #0076e8;
			margin-right: 8px;
		}
		&-text {
			font-weight: 600;
			font-size: 16px;
		}
	}
	&-content {
		display: flex;
		align-items: center;
		justify-content: space-between;
		padding: 20px 0 32px 11px;
		height: 400px;
	}
	&-content2 {
		display: flex;
		align-items: center;
		justify-content: space-between;
		padding: 20px 0 32px 11px;
		height: 500px;
	}
}
.project-con2 {
	background: #ffffff;
	width: 32.7%;
	display: flex;
	align-items: center;
	height: 450px;
	flex-direction: column;
}
</style>
