<template>
	<div class="teacher">
		<div class="floor1">
			<div v-for="(item, k) of teacherList" :key="k" class="floor1-item">
				<div class="item-l">
					<img :src="item.img" alt="" />
				</div>
				<div class="item-r">
					<div class="name">{{ item.name }}</div>
					<div class="title">
						{{ item.count || '-' }}
						<span class="unit">名</span>
					</div>
				</div>
			</div>
		</div>
		<div class="floor2">
			<div class="floor2-name">职称分布</div>
			<div v-for="(item, i) of distributeList" :key="i" class="floor2-info">
				<div class="info-t">
					{{ item.count || '-' }}
					<span class="unit">人</span>
				</div>
				<div class="info-b">{{ item.name || '-' }}</div>
			</div>
		</div>
		<div class="floor3">
			<div class="floor3-title">年龄分布</div>
			<div class="floor3-progress">
				<div v-for="(item, i) of progressList" :key="i" class="floor3-item">
					<div class="progress-br">
						<el-progress
							:width="45"
							class="progress"
							type="circle"
							:stroke-width="8"
							:color="item.color"
							define-back-color="#AFCAEA"
							:text-inside="true"
							:percentage="item.count"
						></el-progress>
					</div>
					<div class="progress-info">
						<div class="val">{{ item.count }}%</div>
						<div class="name">{{ item.name }}</div>
					</div>
				</div>
			</div>
		</div>
	</div>
</template>

<script>
import { xodbApi2 } from '@/api/xodb';
export default {
	name: 'Teacher',
	components: {},
	props: {},
	data() {
		return {
			teacherList: [
				{
					name: '专任教师',
					key: 'zrjsrs',
					count: '',
					img: require('@/assets/images/home20240423/teacher_zz.png')
				},
				{
					name: '博士学位教师',
					key: 'bsxljsrs',
					count: '',
					img: require('@/assets/images/home20240423/teacher_bs.png')
				},
				{
					name: '双师型教师',
					key: 'ssxjsrs',
					count: '',
					img: require('@/assets/images/home20240423/teacher_ssx.png')
				},
				{
					name: '高层次人才数',
					key: 'gccrcrs',
					count: '',
					img: require('@/assets/images/home20240423/teacher_gcc.png')
				}
			],
			distributeList: [
				{
					name: '初级',
					key: 'cjzcrs',
					count: 0
				},
				{
					name: '中级',
					key: 'zjzcrs',
					count: 0
				},
				{
					name: '副高',
					key: 'fgjzcrs',
					count: 0
				},
				{
					name: '正高',
					key: 'zgjzcrs',
					count: 0
				}
			],
			progressList: [
				{
					name: '30岁以下',
					count: 0,
					key: 'jszb_0_30',
					color: '#8984FC'
				},
				{
					name: '30-40岁',
					count: 0,
					key: 'jszb_30_40',
					color: '#F48848'
				},
				{
					name: '40岁-50岁',
					count: 0,
					key: 'jszb_40_50',
					color: '#37B5D7'
				},
				{
					name: '50岁-60岁',
					count: 0,
					key: 'jszb_50_60',
					color: '#F4BF4A'
				},
				{
					name: '60岁以上',
					count: 0,
					key: 'jszb_60',
					color: '#43C82E'
				}
			]
		};
	},
	computed: {
		loginUserInfo() {
			return JSON.parse(localStorage.getItem('loginUserInfo')) || {};
		}
	},
	created() {
		//教职工性别年龄分布_查询
		this.getDataN('ads_jg_xyjsnlfb_query', 'progressList');
		// 教职工职称分布_查询、职工情况数据
		this.getDataN('ads_jg_gxyjsqk_query', 'teacherList');
	},
	methods: {
		async getDataN(url, listName) {
			try {
				const {
					data: { list }
				} = await xodbApi2.post({
					url,
					params: {
						// xyjgbh: this.loginUserInfo.orgCode
						// xyjgbh: '1'
						xyjgbh: this.loginUserInfo.code === 'J06010240' ? this.loginUserInfo.orgCode : '1'
					}
				});
				const data = list[0] || {};
				switch (listName) {
					case 'progressList':
						this[listName].forEach(e => {
							e.count = parseFloat((data[e.key] * 100).toFixed(2));
						});
						break;
					case 'teacherList':
						this[listName].forEach(e => {
							e.count = data[e.key] || '0';
						});
						this.distributeList.forEach(e => {
							e.count = data[e.key] || '0';
						});
						break;
					default:
						this[listName].forEach(e => {
							e.count = data[e.key] || '0';
						});
						break;
				}
			} catch (error) {
				console.error(`处理数据失败${listName}:`, error);
			}
		}
	}
};
</script>

<style lang="scss" scoped>
.teacher {
	display: flex;
	flex-direction: column;
	align-items: center;
	font-family: MicrosoftYaHei, MicrosoftYaHei;

	.floor1 {
		display: flex;
		flex-wrap: wrap;
		padding: 17px 0 0 26px;

		.floor1-item {
			width: 50%;
			// margin-right: 115px;
			margin-bottom: 20px;
			&:nth-child(3),
			&:nth-child(4) {
				margin-bottom: 13px;
			}
			display: flex;
			.item-l {
				margin-right: 19px;
				width: 38px;
				height: 38px;
				img {
					width: 100%;
					height: 100%;
				}
			}

			.item-r {
				font-family: MicrosoftYaHei;
				font-size: 14px;
				color: #454545;
				.name {
					font-family: DINPro, DINPro;
					font-size: 14px;
					color: #0a325b;
					line-height: 33px;
					height: 19px;
					line-height: 19px;
				}

				.title {
					font-family: DINPro, DINPro;
					font-weight: bold;
					font-size: 26px;
					color: #0a325b;
					height: 33px;
					line-height: 33px;
					.unit {
						font-size: 14px;
						color: #294d79;
						line-height: 19px;
						margin-left: 8px;
					}
				}
			}
		}
	}

	.floor2 {
		font-family: MicrosoftYaHei, MicrosoftYaHei;

		margin-bottom: 11px;
		width: 430px;
		height: 64px;
		background: rgba(255, 255, 255, 0.25);
		border-radius: 4px;
		border: 1px solid #ffffff;
		display: flex;
		align-items: center;
		justify-content: space-between;
		padding: 10px 27px 9px 26px;
		.floor2-name {
			min-width: 78px;
			height: 30px;
			line-height: 30px;
			width: 56px;
			font-weight: bold;
			font-size: 14px;
			color: #454545;
			border-right: 1px dotted #a9bed5;
		}

		.floor2-info {
			.info-t {
				font-weight: bold;
				font-size: 18px;
				color: #0a325b;
				line-height: 24px;
				margin-bottom: 2px;
				.unit {
					font-size: 12px;
					color: #294d79;
					line-height: 16px;
					font-weight: normal;
				}
			}

			.info-b {
				font-size: 14px;
				color: #454545;
				line-height: 19px;
				text-align: center;
			}
		}
	}

	.floor3 {
		width: 100%;
		.floor3-title {
			font-weight: bold;
			font-size: 14px;
			color: #0a325b;
			line-height: 19px;
			margin-bottom: 12px;
		}

		.floor3-progress {
			display: flex;
			justify-content: space-between;
			padding: 0 12px;
			width: 100%;
			.floor3-item {
				display: flex;
				flex-direction: column;
				align-items: center;
				.progress-br {
					margin-bottom: 6px;
					width: 60px;
					height: 60px;
					border-radius: 50%;
					background: linear-gradient(
						270deg,
						rgba(233, 255, 252, 0.89) 0%,
						rgba(255, 255, 255, 0) 100%
					);
					// border: 1px solid;
					// border-image: linear-gradient(272deg, rgba(255, 255, 255, 0), rgba(255, 255, 255, 1)) 1 1;
					display: flex;
					align-items: center;
					justify-content: center;
					position: relative;
					&::after {
						content: '';
						position: absolute;
						top: 0;
						left: 0;
						bottom: 0;
						right: 0;
						border: 1px solid #fff;
						border-radius: 50%;
						z-index: -1;
					}
					.progress {
						background: rgba(175, 202, 234, 0.4);
						border-radius: 50%;
						position: relative;
						&::after {
							content: '';
							position: absolute;
							top: -2px;
							left: calc(50% - 5px);
							display: inline-block;
							width: 10px;
							height: 10px;
							background: #ffffff;
							box-shadow: 0px 1px 4px 0px rgba(0, 0, 0, 0.2);
							border-radius: 50%;
						}
					}
				}
				.progress-info {
					.val {
						height: 19px;
						font-weight: bold;
						font-size: 14px;
						color: #0a325b;
						line-height: 19px;
						text-align: center;
					}
					.name {
						height: 19px;
						font-size: 12px;
						color: #454545;
						line-height: 19px;
						text-align: left;
						font-style: normal;
					}
				}
			}
		}
	}
}
</style>
