<template>
	<div class="content">
		<es-data-table
			ref="table"
			:row-style="tableRowClassName"
			:full="true"
			:fit="true"
			:thead="thead"
			:toolbar="toolbar"
			:border="true"
			:page="pageOption"
			:url="dataTableUrl"
			:numbers="true"
			:param="params"
			:option-data="optionData"
			style="width: 100%"
			close
			form
			@btnClick="btnClick"
			@sort-change="sortChange"
			@submit="hadeSubmit"
			@edit="changeTable"
		></es-data-table>
		<!-- 
			parmas:
				archievement_id: 成果id String
				showAchievementForm: 弹出框是否展示 false true
				achievementTitle: 弹出框title String
			methods:
				changeShow: 改变showAchievementForm
		-->
		<achievement-form
			v-if="showAchievementForm"
			:archievement_id="archievement_id"
			:showAchievementForm="showAchievementForm"
			:achievementTitle="achievementTitle"
			@changeShow="changeShow"
		></achievement-form>
		<es-dialog
			:title="formTitle"
			:visible.sync="showForm"
			width="60%"
			:close-on-click-modal="false"
			:destroy-on-close="true"
		>
			<es-form
				ref="form"
				:model="formData"
				:contents="formItemList"
				height="500px"
				style="width: 100%"
				:genre="2"
				collapse
				@change="inputChange"
				@submit="handleFormSubmit"
				@reset="showForm = false"
			/>
		</es-dialog>
		<es-dialog
			title="分配角色"
			:visible.sync="showRoleForm"
			width="60%"
			:close-on-click-modal="false"
			:destroy-on-close="true"
		>
			<es-form
				ref="form"
				:model="roleFormData"
				height="500px"
				:genre="2"
				collapse
				@reset="showRoleForm = false"
			/>
		</es-dialog>
		<es-dialog
			title="删除"
			:visible.sync="showDelete"
			width="20%"
			:close-on-click-modal="false"
			:middle="true"
			height="140px"
		>
			<div>确定要删除该条数据吗</div>
			<div class="btn-box">
				<div class="btn theme" @click="deleteRow">确定</div>
				<div class="btn" @click="showDelete = false">取消</div>
			</div>
		</es-dialog>
	</div>
</template>

<script>
import interfaceUrl from '@/http/project/projectSrAchievement';
import { handleDataMap } from '@/http/project/data';
import achievementForm from '../../../components/form/achievement-form.vue';
import { createNamespacedHelpers } from 'vuex';
const { mapState } = createNamespacedHelpers('getRoleMap');
// import SnowflakeId from 'snowflake-id';
// import { host } from '../../../../config/config';
export default {
	components: { achievementForm },
	data() {
		return {
			ownId: '',
			dataTableUrl: interfaceUrl.listJson + '?type=gxokrzlcuyazmke',
			showForm: false,
			showRoleForm: false,
			showDelete: false,
			formData: {},
			archievement_id: '', //成果id
			achievementTitle: '', //弹出框名称
			showAchievementForm: false, //弹出框是否展示
			roleFormData: {
				userId: '',
				username: '',
				phone: '',
				selectedRoleList: []
			},
			formTitle: '编辑',
			optionData: {
				state: [
					{
						value: 1,
						name: '启用'
					},
					{
						value: 0,
						name: '禁用'
					}
				]
			},
			editBtn: {
				type: 'submit',
				skin: 'lay-form-btns',
				contents: [
					{
						type: 'primary',
						text: '确定',
						event: 'confirm'
					},
					{
						type: 'reset',
						text: '取消',
						event: 'cancel'
					}
				]
			},
			cancelBtn: {
				type: 'submit',
				skin: 'lay-form-btns',
				contents: [
					{
						type: 'reset',
						text: '取消',
						event: 'cancel'
					}
				]
			},
			toolbar: [
				{
					type: 'search',
					contents: [
						{
							type: 'text',
							name: 'queryParams.keyword',
							placeholder: '关键字查询'
						}
					]
				}
			],
			thead: [
				{
					title: '部门名称',
					align: 'center',
					field: 'bumenmingchen',
					sortable: 'custom',
					showOverflowTooltip: true,
					width: '200px'
				},
				{
					title: '项目名称',
					align: 'center',
					field: 'xiangmumingchen',
					sortable: 'custom',
					showOverflowTooltip: true,
					width: '200px'
				},
				{
					title: '项目类型',
					align: 'center',
					field: 'xiangmuleixing',
					sortable: 'custom',
					showOverflowTooltip: true,
					width: '200px'
				},
				{
					title: '项目期限',
					align: 'center',
					field: 'xiangmuqixian',
					sortable: 'custom',
					showOverflowTooltip: true,
					width: '200px'
				},
				{
					title: '是否立项',
					align: 'center',
					field: 'shifulixiang',
					sortable: 'custom',
					showOverflowTooltip: true,
					width: '200px'
				},
				{
					title: '关联纵向项目',
					align: 'center',
					field: 'guanlianzongxiangxi',
					sortable: 'custom',
					showOverflowTooltip: true,
					width: '200px'
				},
				{
					title: '研究期限',
					align: 'center',
					field: 'yanjiuqixian',
					sortable: 'custom',
					showOverflowTooltip: true,
					width: '200px'
				},
				{
					title: '任务明细',
					align: 'center',
					field: 'renwumingxi',
					sortable: 'custom',
					showOverflowTooltip: true,
					width: '200px'
				},
				{
					title: '下达经费',
					align: 'center',
					field: 'xiadajingfei',
					sortable: 'custom',
					showOverflowTooltip: true,
					width: '200px'
				},
				{
					title: '下达方式',
					align: 'center',
					field: 'xiadafangshi',
					sortable: 'custom',
					showOverflowTooltip: true,
					width: '200px'
				},
				{
					title: '已到账经费',
					align: 'center',
					field: 'yidaozhangjingfei',
					sortable: 'custom',
					showOverflowTooltip: true,
					width: '200px'
				},
				// {
				// 	title: '支撑材料',
				// 	align: 'center',
				// 	field: 'zhichengcailiao',
				// 	sortable: 'custom',
				// 	showOverflowTooltip: true,
				// width:'200px'
				// },
				{
					title: '项目级别',
					align: 'center',
					field: 'xiangmujibie',
					sortable: 'custom',
					showOverflowTooltip: true,
					width: '200px'
				},
				{
					title: '负责人',
					align: 'center',
					field: 'fuzeren',
					sortable: 'custom',
					showOverflowTooltip: true,
					width: '200px'
				},
				{
					title: '项目编号',
					align: 'center',
					field: 'xiangmubianhao',
					sortable: 'custom',
					showOverflowTooltip: true,
					width: '200px'
				},
				{
					title: '立项时间',
					align: 'center',
					field: 'lixiangshijian',
					type: 'date',
					sortable: 'custom',
					showOverflowTooltip: true,
					width: '200px'
				},
				{
					title: '自核分值',
					align: 'center',
					field: 'zihefenzhi',
					sortable: 'custom',
					showOverflowTooltip: true,
					width: '200px'
				},
				{
					title: '备注',
					align: 'center',
					field: 'beizhu',
					sortable: 'custom',
					showOverflowTooltip: true,
					width: '200px'
				},
				{
					title: '二级学院核定分值',
					align: 'center',
					field: 'erjixueyuanhedingfe',
					sortable: 'custom',
					showOverflowTooltip: true,
					width: '200px'
				},
				{
					title: '科技处核定分值',
					align: 'center',
					field: 'kejichuhedingfenzhi',
					sortable: 'custom',
					showOverflowTooltip: true,
					width: '200px'
				},
				{
					title: '学院核定分值',
					align: 'center',
					field: 'xueyuanhedingfenzhi',
					sortable: 'custom',
					showOverflowTooltip: true,
					width: '200px'
				},
				{
					title: '平台名称',
					align: 'center',
					field: 'pingtaimingchen',
					sortable: 'custom',
					showOverflowTooltip: true,
					width: '200px'
				},
				{
					title: '级别',
					align: 'center',
					field: 'jibie',
					sortable: 'custom',
					showOverflowTooltip: true,
					width: '200px'
				},

				{
					title: '通过复核时间',
					align: 'center',
					field: 'tongguofuheshijian',
					sortable: 'custom',
					showOverflowTooltip: true,
					width: '200px'
				},
				{
					title: '通过验收时间',
					align: 'center',
					field: 'tongguoyanshoushiji',
					sortable: 'custom',
					showOverflowTooltip: true,
					width: '200px'
				},

				// {
				// 	title: '支撑材料',
				// 	align: 'center',
				// 	field: 'zhichengcailiao',
				// 	sortable: 'custom',
				// 	showOverflowTooltip: true,
				// width:'200px'
				// },

				{
					title: '论文名称',
					align: 'center',
					field: 'lunwenmingchen',
					sortable: 'custom',
					showOverflowTooltip: true,
					width: '200px'
				},
				{
					title: '转换状态',
					align: 'center',
					field: 'transition_status',
					sortable: 'custom',
					showOverflowTooltip: true,
					width: '200px',
					sysCode: 'project_transition_status',
					'label-key': 'shortName',
					'value-key': 'cciValue'
				},
				{
					title: '操作',
					type: 'handle',
					width: 180,
					template: '',
					fixed: 'right',
					events: [
						{
							code: 'view',
							text: '查看'
						},
						{
							code: 'viewAchievement',
							text: '查看成果',
							rules: rows => rows.transition_status == 1
						},
						// {
						// 	code: 'achievementConversion',
						// 	text: '成果转换'
						// }
					]
				}
			],
			pageOption: {
				layout: 'total, prev, pager, next, jumper',
				pageSize: 20,
				// hideOnSinglePage: true,
				position: 'right',
				current: 1,
				pageNum: 1
			},
			params: {
				asc: 'false',
				orderBy: 'createTime'
			}
		};
	},
	computed: {
		formItemList() {
			return [
				{
					label: '部门名称',
					name: 'bumenmingchen',
					placeholder: '请输入部门名称',
					event: 'multipled',
					col: 5
				},
				{
					label: '项目名称',
					name: 'xiangmumingchen',
					placeholder: '请输入项目名称',
					event: 'multipled',
					col: 5
				},
				{
					label: '项目类型',
					name: 'xiangmuleixing',
					placeholder: '请输入项目类型',
					event: 'multipled',
					col: 5
				},
				{
					label: '项目期限',
					name: 'xiangmuqixian',
					placeholder: '请输入项目期限',
					event: 'multipled',
					col: 5
				},
				{
					label: '是否立项',
					name: 'shifulixiang',
					hide: false,
					placeholder: '请输入是否立项',
					event: 'multipled',
					col: 5
				},
				{
					label: '关联纵向项目',
					name: 'guanlianzongxiangxi',
					placeholder: '请输入关联纵向项目',
					controls: false,
					col: 5
				},
				{
					label: '研究期限',
					name: 'yanjiuqixian',
					placeholder: '请输入研究期限',
					col: 5
				},
				{
					label: '任务明细',
					name: 'renwumingxi',
					placeholder: '请输入任务明细',
					col: 5
				},
				{
					label: '下达经费',
					name: 'xiadajingfei',
					placeholder: '请输入下达经费',
					type: 'Number',
					col: 5
				},
				{
					label: '下达方式',
					name: 'xiadafangshi',
					placeholder: '请输入下达方式',
					col: 5
				},
				{
					label: '已到账经费',
					name: 'yidaozhangjingfei',
					placeholder: '请输入已到账经费',
					type: 'Number',
					col: 5
				},
				// {
				// 	label: '支撑材料',
				// 	name: 'zhichengcailiao',
				// 	placeholder: '请输入支撑材料',
				// 	col: 5
				// },
				{
					name: 'xiangmujibie',
					label: '项目级别',
					placeholder: '请输入项目级别',
					col: 5
				},
				{
					name: 'fuzeren',
					label: '负责人',
					placeholder: '请输入负责人',
					col: 5
				},
				{
					name: 'xiangmubianhao',
					label: '项目编号',
					placeholder: '请输入项目编号',
					col: 5
				},
				{
					name: 'lixiangshijian',
					label: '立项时间',
					placeholder: '请输入立项时间',
					col: 5
				},
				{
					name: 'beizhu',
					label: '备注',
					placeholder: '请输入备注',
					col: 5
				},
				{
					name: 'chengguomingchen',
					label: '成果名称',
					placeholder: '请输入成果名称',
					col: 5
				},
				{
					name: 'erjixueyuanhedingfe',
					label: '二级学院核定分值',
					placeholder: '请输入二级学院核定分值',
					col: 5
				},
				{
					name: 'kejichuhedingfenzhi',
					label: '科技处核定分值',
					placeholder: '请输入科技处核定分值',
					col: 5
				},
				{
					name: 'xueyuanhedingfenzhi',
					label: '学院核定分值',
					placeholder: '请输入学院核定分值',
					col: 5
				},
				{
					name: 'pingtaimingchen',
					label: '平台名称',
					placeholder: '请输入平台名称',
					col: 5
				},
				{
					name: 'jibie',
					label: '级别',
					placeholder: '请输入级别',
					col: 5
				},
				{
					name: 'tongguofuheshijian',
					label: '通过复核时间',
					placeholder: '请输入通过复核时间',
					col: 5
				},
				{
					name: 'tongguoyanshoushiji',
					label: '通过验收时间',
					placeholder: '请输入通过验收时间',
					col: 5
				},
				{
					label: '转换状态',
					placeholder: '请选择转换状态',
					name: 'transition_status',
					sysCode: 'project_transition_status',
					'label-key': 'shortName',
					'value-key': 'cciValue',
					clearable: true,
					col: 5
				}
			];
		},
		...mapState(['projectScienceManager'])
	},
	watch: {
		projectScienceManager: {
			immediate: true,
			handler(newVal) {
				// debugger;
				// projectScienceManager：父级传递的身份标识 如果为科技处老师值为true 可进行修改删除操作，反之不可
				if (newVal === false) {
					this.readonly = true;
				}
				if (typeof newVal == 'boolean' && newVal === true) {
					this.thead[this.thead.length - 1].events.push({
						code: 'achievementConversion',
						text: '成果转换'
					});
				}
			}
		}
	},
	created() {},
	mounted() {},
	methods: {
		/**
		 * 表格行高
		 */
		tableRowClassName({ row, rowIndex }) {
			let styleRes = {
				height: '54px !important'
			};
			return styleRes;
		},
		inputChange(key, value) {
			// if (key == 'phone') {
			// }
		},
		// 改变子组件弹窗状态
		changeShow() {
			this.showAchievementForm = this.showAchievementForm == false ? true : false;
		},
		hadeSubmit(data) {},
		/**
		 * 操作按钮点击事件
		 * @param {*} res
		 */
		btnClick(res) {
			let code = res.handle.code;
			this.archievement_id = res.row.archievement_id;
			switch (code) {
				case 'view':
					this.formTitle = '查看';
					this.ownId = res.row.id;
					this.readModule(this.formItemList, ['pwd']);
					this.$request({
						url: interfaceUrl.info + '?id=' + res.row.archievement_id + '&type=gxokrzlcuyazmke',
						method: 'GET'
					}).then(res => {
						if (res.rCode == 0) {
							this.formData = res.results;
							this.formData = handleDataMap(this.formData.dataMap, this.formData);
							this.showForm = true;
						}
					});
					break;
				// 查看成果
				case 'viewAchievement':
					this.achievementTitle = '查看成果';
					this.changeShow();
					break;
				// 成果转换
				case 'achievementConversion':
					this.achievementTitle = '成果转换';
					this.changeShow();
					break;
				default:
					break;
			}
		},
		handleClose() {},
		handleFormItemChange() {},
		handleFormSubmit(data) {},
		deleteRow() {},
		/**
		 * 排序变化事件
		 * @param {*} column
		 * @param {*} prop
		 * @param {*} order
		 */
		sortChange(column, prop, order) {
			if (column.order == 'ascending') {
				//升序
				this.params = {
					asc: 'true',
					orderBy: column.prop
				};
			} else if (column.order == 'descending') {
				//降序
				this.params = {
					asc: 'false',
					orderBy: column.prop
				};
			} else {
				//不排序
				this.params = {
					asc: 'false',
					orderBy: 'createTime'
				};
			}
			this.$refs.table.reload();
		},
		/**
		 * 编辑模式
		 */
		editModule(list, hideField) {
			for (var i in list) {
				var item = list[i];
				item.readonly = false;
				if (undefined != hideField && hideField.length > 0 && hideField.indexOf(item.name) > -1) {
					item.hide = true;
				}
			}
			list.push(this.editBtn);
		},
		/**
		 * 只读模式
		 */
		readModule(list, hideField) {
			for (var i in list) {
				var item = list[i];
				item.readonly = true;
				if (undefined != hideField && hideField.length > 0 && hideField.indexOf(item.name) > -1) {
					item.hide = true;
				}
			}
			list.push(this.cancelBtn);
		},
		changeTable(val) {
			if ('state' === val.name) {
				this.$request({
					url: interfaceUrl.updateState,
					data: {
						id: val.data.id,
						state: val.value
					},
					method: 'POST'
				}).then(res => {
					if (res.rCode == 0) {
						this.$message.success('操作成功');
					} else {
						this.$message.error(res.msg);
					}
				});
			}
		}
	}
};
</script>
<style scoped lang="scss">
@import '../../../assets/style/style.scss';

.content {
	display: flex;
	width: 100%;
	height: 100%;

	::v-deep .es-data-table {
		flex: 1;
		display: flex;
		flex-direction: column;

		.es-data-table-content {
			flex: 1;
			height: 0;
			display: flex;
			flex-direction: column;

			.el-table {
				flex: 1;
				// height: 100% !important;
			}

			.es-thead-border {
				.el-table__header {
					th {
						border-right: 1px solid #e1e1e1;
					}
				}
			}
		}
	}

	::v-deep .el-form-item__label {
		background: none;
		border: 0px solid #c2c2c2;
	}
}

.el-dialog__body {
	.btn-box {
		margin-top: 20px;
		display: flex;
		justify-content: flex-end;

		.btn {
			padding: 5px 10px;
			color: #666;
			border: 1px solid #eee;
			cursor: pointer;
			margin-right: 5px;
			border-radius: 4px;
			// &.theme {
			// 	background: $--color-primary;
			// 	color: #fff;
			// 	border-color: $--color-primary;
			// }
		}
	}
}
</style>
