<template>
	<div class="teacher-pim">
		<es-form
			ref="formRef"
			v-loading="loading"
			label-width="150px"
			:model="form"
			:contents="contents"
			table
			:span="3"
			:readonly="formReadonly"
			@submit="handleFormSubmit"
			@click="handleFormClick"
		></es-form>
	</div>
</template>

<script>
export default {
	data() {
		return {
			form: {
				uname: '', //账号
				fid: '', //课堂所属的单位id
				title: '', //会议标题主题
				remark: '', //会议主题
				type: 0, //会议类型(0立即开始；1预约；2课堂活动会议;3:超星课表类型 4：培训平台使用类型，默认0 9: 仅绑定课程课堂)
				courseId: '', //课程id，type为2、3、4必填
				classId: '', //班级id，type为2、3、4必填
				className: '', //班级名称，type为2、3、4必填
				classChatId: '', //班级群id，type为2、3、4必填"
				reserveStartTime: '', //预约会议开始时间
				reserveEndTime: '', //预约会议结束时间
				classLimit: 0, //创建时是否只允许本班级人加入，0关闭限制 1：打开限制。 默认为0
				onWaitRoom: 0, //创建时是否开启等候室，0关闭 1：打开。 默认为0
				endMeetLeft30Mins: 1, //教师离开课堂30分钟后，自动结束课堂 0-关闭。1-开启，默认开启
				isAllowStudentJoinBeforeTeacher: 0, //允许成员在教师进课前进入课堂 1-允许，0-不允许，默认不允许
				lookBack: 1 //是否打开回看，1打开（当打开入会地址是，课堂进心中进入会议。当课堂结束进入回看页
				// meetIcon: '', //课堂列表icon
				// enterType: '', //表示章节任务
				// knowledgeId: '', //章节id,用于标记不绑定班级,记录章节真实章节Id
				// knowledgeCourseId: '', //章节课程id,用于标记不绑定班级,记录章节真实课程Id
				// knowledgeJobId: '', //章节任务点id,用于标记不绑定班级,记录章节真实JobId
				// percent: '' //章节任务回看百分比0-关闭,1-100开启
				// reviewPercent: '', //章节任务回看百分比0-关闭,1-100开启
				// reviewVideos: '', //章节任务回看视频的JsonArra
				// knowledgeClassInfo: '', //多班信息的JsonArray
				// setting: '' //课堂设置
			},
			loading: false
		};
	},
	computed: {
		formReadonly() {
			return false;
		},
		contents() {
			return [
				// {
				// 	label: '账号',
				// 	name: 'uname',
				// 	col: 3,
				// 	placeholder: '请输入',
				// 	rules: {
				// 		required: true,
				// 		message: '请输入',
				// 		trigger: 'blur'
				// 	}
				// },
				// {
				// 	label: '所属单位Id',
				// 	name: 'fid',
				// 	col: 3,
				// 	placeholder: '请输入',
				// 	rules: {
				// 		required: true,
				// 		message: '请输入',
				// 		trigger: 'blur'
				// 	}
				// },
				{
					label: '会议标题',
					name: 'title',
					col: 3,
					placeholder: '请输入',
					rules: {
						required: true,
						message: '请输入',
						trigger: 'blur'
					}
				},
				{
					label: '会议主题',
					name: 'remark',
					col: 3,
					placeholder: '请输入',
					rules: {
						required: true,
						message: '请输入',
						trigger: 'blur'
					}
				},
				{
					label: '会议类型',
					name: 'type',
					col: 3,
					type: 'select',
					'value-key': 'value',
					'label-key': 'label',
					rules: {
						required: true,
						message: '请选择',
						trigger: 'blur'
					},
					data: [
						{
							label: '立即开始',
							value: 0
						},
						{
							label: '预约',
							value: 1
						},
						{
							label: '课堂活动',
							value: 2
						},
						{
							label: '超星课表类型',
							value: 3
						},
						{
							label: '培训平台使用类型',
							value: 4
						}
					],
					filterable: true
				},
				// {
				// 	label: '课程id',
				// 	name: 'courseId',
				// 	col: 3,
				// 	placeholder: '请输入',
				// 	rules: {
				// 		required: true,
				// 		message: '请输入',
				// 		trigger: 'blur'
				// 	}
				// },
				// {
				// 	label: '班级id',
				// 	name: 'classId',
				// 	col: 3,
				// 	placeholder: '请输入',
				// 	rules: {
				// 		required: true,
				// 		message: '请输入',
				// 		trigger: 'blur'
				// 	}
				// },
				// {
				// 	label: '班级名称',
				// 	name: 'className',
				// 	col: 3,
				// 	placeholder: '请输入',
				// 	rules: {
				// 		required: true,
				// 		message: '请输入',
				// 		trigger: 'blur'
				// 	}
				// },
				// {
				// 	label: '班级群id',
				// 	name: 'classChatId',
				// 	col: 3,
				// 	placeholder: '请输入',
				// 	rules: {
				// 		required: true,
				// 		message: '请输入',
				// 		trigger: 'blur'
				// 	}
				// },
				{
					label: '会议开始时间',
					name: 'reserveStartTime',
					field: 'date',
					type: 'datetime',
					col: 3,
					placeholder: '请输入',
					rules: {
						required: true,
						message: '请输入',
						trigger: 'blur'
					}
				},
				{
					label: '会议结束时间',
					name: 'reserveEndTime',
					col: 3,
					field: 'date',
					type: 'datetime',
					placeholder: '请输入',
					rules: {
						required: true,
						message: '请输入',
						trigger: 'blur'
					}
				},
				{
					label: '是否允许本班级人加入',
					name: 'classLimit',
					col: 3,
					type: 'select',
					'value-key': 'value',
					'label-key': 'label',
					rules: {
						required: false,
						message: '请选择',
						trigger: 'blur'
					},
					data: [
						{
							label: '关闭限制',
							value: 0
						},
						{
							label: '打开限制',
							value: 1
						}
					]
				},
				{
					label: '是否开启等待室',
					name: 'onWaitRoom',
					col: 3,
					type: 'select',
					'value-key': 'value',
					'label-key': 'label',
					rules: {
						required: false,
						message: '请选择',
						trigger: 'blur'
					},
					data: [
						{
							label: '关闭',
							value: 0
						},
						{
							label: '打开',
							value: 1
						}
					]
				},
				{
					label: '教师离开30分钟是否自动结束',
					name: 'endMeetLeft30Mins',
					col: 3,
					type: 'select',
					'value-key': 'value',
					'label-key': 'label',
					rules: {
						required: false,
						message: '请选择',
						trigger: 'blur'
					},
					data: [
						{
							label: '关闭',
							value: 0
						},
						{
							label: '打开',
							value: 1
						}
					]
				},
				{
					label: '允许成员在教师前进入',
					name: 'isAllowStudentJoinBeforeTeacher',
					col: 3,
					type: 'select',
					'value-key': 'value',
					'label-key': 'label',
					rules: {
						required: false,
						message: '请选择',
						trigger: 'blur'
					},
					data: [
						{
							label: '不允许',
							value: 0
						},
						{
							label: '运行',
							value: 1
						}
					]
				},
				{
					label: '是否打开回看',
					name: 'lookBack',
					type: 'select',
					'value-key': 'value',
					'label-key': 'label',
					rules: {
						required: false,
						message: '请选择',
						trigger: 'blur'
					},
					data: [
						{
							label: '关闭',
							value: 0
						},
						{
							label: '打开',
							value: 1
						}
					]
				}
				// {
				// 	label: '课堂列表icon',
				// 	name: 'meetIcon',
				// 	col: 3,
				// 	placeholder: '请输入',
				// 	rules: {
				// 		required: false,
				// 		message: '请输入',
				// 		trigger: 'blur'
				// 	}
				// },
				// {
				// 	label: '章节任务',
				// 	name: 'enterType',
				// 	col: 3,
				// 	placeholder: '请输入',
				// 	rules: {
				// 		required: false,
				// 		message: '请输入',
				// 		trigger: 'blur'
				// 	}
				// },
				// {
				// 	label: '章节ID',
				// 	name: 'knowledgeId',
				// 	col: 3,
				// 	placeholder: '请输入',
				// 	rules: {
				// 		required: false,
				// 		message: '请输入',
				// 		trigger: 'blur'
				// 	}
				// },
				// {
				// 	label: '章节课程id',
				// 	name: 'knowledgeCourseId',
				// 	col: 3,
				// 	placeholder: '请输入',
				// 	rules: {
				// 		required: false,
				// 		message: '请输入',
				// 		trigger: 'blur'
				// 	}
				// },
				// {
				// 	label: '章节任务点id',
				// 	name: 'knowledgeJobId',
				// 	col: 3,
				// 	placeholder: '请输入',
				// 	rules: {
				// 		required: false,
				// 		message: '请输入',
				// 		trigger: 'blur'
				// 	}
				// },
				// {
				// 	label: '章节任务回看百分比',
				// 	name: 'percent',
				// 	col: 3,
				// 	placeholder: '请输入',
				// 	rules: {
				// 		required: false,
				// 		message: '请输入',
				// 		trigger: 'blur'
				// 	}
				// }
				// {
				// 	label: '课堂设置',
				// 	name: 'setting',
				// 	col: 3,
				// 	type: 'select',
				// 	'value-key': 'value',
				// 	'label-key': 'label',
				// 	rules: {
				// 		required: !this.formReadonly,
				// 		message: '请选择',
				// 		trigger: 'blur'
				// 	},
				// 	data: [
				// 		{
				// 			label: '关闭',
				// 			value: 0
				// 		},
				// 		{
				// 			label: '打开',
				// 			value: 1
				// 		}
				// 	]
				// }
			];
		}
	},
	watch: {
		// formId: {
		// 	handler(newVal, oldVal) {
		// 		this.getInfo(newVal);
		// 	},
		// 	immediate: true
		// }
	},
	// created() {},
	methods: {
		handleFormSubmit() {
			const loading = this.$.loading(this.$loading, '提交中...');
			// this.form.puid = 68888;
			this.form.uname = JSON.parse(localStorage.getItem('loginUserInfo')).code;
			this.form.fid = 29454;
			this.$.ajax({
				url: '/ybzy/fanya/meeting/save',
				method: 'post',
				data: this.form,
				format: false
			}).then(res => {
				loading.close();
				if (res.rCode == 0) {
					this.$message.success(res.msg);
					this.$refs.formRef.resetFields();
				} else {
					this.$message.error(res.msg);
				}
			});
		},
		handleFormClick(submitType) {
			this.$refs.formRef.resetFields();
		}
	}
};
</script>

<style lang="scss" scoped>
.teacher-pim {
	width: 100%;
	height: 100%;
}
</style>
