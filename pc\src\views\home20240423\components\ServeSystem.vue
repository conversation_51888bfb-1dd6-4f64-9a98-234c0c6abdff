<template>
	<div class="serve-box">
		<!-- 通知公告 -->
		<div class="notice">
			<!-- 搜索栏部分 -->
			<div class="search">
				<search @onSearch="onSearch" />
			</div>
		</div>
		<!-- 内容区域 -->
		<div v-loading="loading" class="system box-center">
			<div v-for="(app, index) in myAppData" :key="index" class="card-box">
				<div class="system-box">
					<!-- 标题 -->
					<p class="system-title">{{ app.text }}</p>
					<div class="system-list">
						<template v-for="(item, itemK) in app.children">
							<el-popover
								:key="itemK"
								ref="popover1"
								placement="top"
								popper-class="popper-used-box"
								:disabled="!item.children.length > 0"
								trigger="click"
							>
								<div class="used-box">
									<div v-for="(child, k) in item.children" :key="k">
										<div v-if="child" class="used-item" @click="myAppDataBtn(child, false)">
											<img :src="handelPicUrl(child.icons)" class="item-img" />
											<el-tooltip
												class="item"
												:open-delay="1000"
												:content="child.text"
												placement="right"
											>
												<span class="name ellipsis-1">{{ child.text }}</span>
											</el-tooltip>
											<el-tooltip
												class="item"
												:open-delay="1000"
												:content="child.appCode"
												placement="right"
											>
												<span class="desc ellipsis-1">{{ child.appCode }}</span>
											</el-tooltip>
											<img src="@/assets/images/serveHall/active-bg.png" class="active-bg" />
										</div>
									</div>
								</div>
								<div slot="reference" class="system-item" @click="myAppDataBtn(item)">
									<img :src="handelPicUrl(item.icons)" class="item-img" />
									<el-tooltip
										class="item"
										:open-delay="1000"
										:content="item.text"
										placement="right"
									>
										<span class="name ellipsis-1">{{ item.text }}</span>
									</el-tooltip>
									<el-tooltip
										class="item"
										:open-delay="1000"
										:content="item.appCode"
										placement="right"
									>
										<span class="desc ellipsis-1">{{ item.appCode }}</span>
									</el-tooltip>
									<div
										v-if="item.type != 'more'"
										:class="['collect-box', collectedList.includes(item.id) && 'collected']"
										@click.stop="
											collectedList.includes(item.id)
												? deleteBatchIds(item, 0)
												: saveCollect(item, 0)
										"
									>
										<template v-if="collectedList.includes(item.id)">
											<i class="el-icon-star-on icon"></i>
											<span>已收藏</span>
										</template>
										<template v-else>
											<i class="el-icon-star-off icon"></i>
											<span>收藏</span>
										</template>
									</div>
									<img src="@/assets/images/serveHall/active-bg.png" class="active-bg" />
								</div>
							</el-popover>
						</template>
						<Empty v-if="!app.children?.length" text="暂无数据" />
					</div>
				</div>
			</div>
			<Empty v-if="!myAppData.length && !loading" text="暂无数据" />
		</div>
		<!-- 任务中心 和 最近使用 -->
		<div class="task">
			<div class="task-box box-center">
				<!-- 任务中心 -->
				<div class="task-card">
					<div class="task-title">
						<div class="title-box">
							<span>任务中心</span>
							<span class="more-btn" @click="taskMore">more ></span>
						</div>
					</div>
					<div class="task-list">
						<div class="left">
							<div
								v-for="(item, index) in taskType"
								:key="index"
								:class="['left-item', item.code == taskActive && 'left-item-active']"
								@click="taskHandleClick(item)"
							>
								<img :src="item.url" class="item-img" />
								<img :src="item.activeUrl" class="item-img-active" />
								<span>{{ item.name }}</span>
							</div>
						</div>
						<div
							v-loading="taskLoading"
							class="right"
							element-loading-background="rgba(0, 0, 0, 0)"
						>
							<Empty
								v-if="!taskList.length && !taskLoading"
								:text="taskActive == 'todo' ? '真好，您的事情都已经办完了' : '暂无发起的任务哦'"
							/>
							<template v-else>
								<div
									v-for="(item, index) in taskList"
									:key="index"
									class="right-item"
									@click="taskJump(item)"
								>
									<span class="item-time">
										{{ taskActive == 'send' ? item.endtime : item.createtime }}
									</span>
									<span class="item-title ellipsis-1">{{ item.bname }}</span>
									<span class="item-desc">
										<span class="desc-text ellipsis-1">
											{{ taskActive == 'send' ? item.pitemname : item.pendtitle }}
										</span>
										<span v-if="taskActive == 'send'" class="item-status">{{ item.state }}</span>
									</span>
								</div>
							</template>
						</div>
					</div>
				</div>
				<!-- 最新使用 -->
				<div class="task-card last-used">
					<div class="title-box">最近使用</div>
					<div class="used-box">
						<template v-for="(item, index) in recentList">
							<el-popover
								:key="index"
								placement="top"
								popper-class="popper-used-box"
								:disabled="!item?.children?.length > 0"
								style="background: rgba(0, 0, 0, 0)"
								trigger="click"
							>
								<div class="used-box small">
									<div v-for="(child, k) in item?.children" :key="k">
										<div v-if="child" class="used-item" @click="myAppDataBtn(child, false)">
											<img :src="handelPicUrl(child.icons)" class="item-img" />
											<span class="name ellipsis-2">{{ child.text }}</span>
											<img src="@/assets/images/serveHall/active-bg.png" class="active-bg" />
										</div>
									</div>
								</div>
								<div
									v-if="item"
									slot="reference"
									:key="index"
									class="used-item"
									@click="myAppDataBtn(item)"
								>
									<img :src="handelPicUrl(item.icons)" class="item-img" />
									<span class="name ellipsis-2">{{ item.text }}</span>
									<img src="@/assets/images/serveHall/active-bg.png" class="active-bg" />
								</div>
							</el-popover>
						</template>
						<Empty v-if="!recentList.length" text="您暂时还没有已使用的服务" />
					</div>
				</div>
			</div>
		</div>
		<es-dialog
			v-if="visible"
			ref="visible"
			:title="dialogTitle"
			:drag="false"
			size="max"
			:visible.sync="visible"
		>
			<iframe
				v-if="iframeUrl"
				ref="iframe"
				:src="iframeUrl"
				width="100%"
				height="100%"
				frameborder="0"
				allowfullscreen
				allow-same-origin
			>
				<div>浏览器不兼容</div>
			</iframe>
			<!-- 动态组件 -->
			<component
				:is="companyName"
				v-else
				:props-object="propsObject"
				:title="dialogTitle"
				@visible="visible = false"
			></component>
		</es-dialog>
		<Navigation ref="navigation" @myAppDataBtn="myAppDataBtn" />
	</div>
</template>

<script>
import Empty from '@/components/empty.vue';
import search from './search.vue';
import Navigation from './navigation.vue';

import qs from 'qs';
import { tenantId, alumniUrl } from '@/config';
import { getTransactionList, checkLogin, getEossAuthentication } from '@/api/home.js';
export default {
	name: 'ServeHall',
	components: {
		Empty,
		search,
		Navigation,
		Leave: () => import('@/views/sys/leave/flow-page.vue'),
		System: () => import('@/views/sys/system/index.vue'),
		Research: () => import('@/views/sys/research/index.vue')
	},
	filters: {
		// 日期格式切换
		dateShow(str) {
			let strTime = '';
			if (str) {
				let dateArray = str.split(' ')[0].split('-');
				strTime = dateArray[1] + '.' + dateArray[2];
			}
			return strTime;
		}
	},
	data() {
		return {
			loading: false,
			// 内置弹窗组件
			visible: false, //iframe展示判断
			dialogTitle: '业务直通车',
			companyName: '',
			propsObject: '', // 扩展属性
			iframeUrl: '',

			keyWord: '', //关键字
			noticeList: [], //通知公告数据
			myAppDataTotol: [], //系统直通车所有数据
			myAppData: [], //系统直通车展示的数据
			myAppMore: [
				// {
				// 	logo: require('@/assets/images/serveHall/more-serve-icon.png'),
				// 	name: '更多服务',
				// 	remark: 'More',
				// 	url: '/serveCar?type=system',
				// 	type: 'more'
				// }
			], //系统直通车更多的展示数据
			serviceList: [], //业务所有数据
			serviceData: [], //业务当前显示的列表数据
			activeName: '全部', //业务直通车选中分类
			serviceIndex: '0', //当前选中业务分类的下标，用于筛选时的数据展示问题
			taskType: [
				{
					name: '我的待办',
					code: 'todo',
					url: require('@/assets/images/serveHall/task-dealt.png'),
					activeUrl: require('@/assets/images/serveHall/task-dealt-active.png')
				},
				{
					name: '我发起的',
					code: 'send',
					url: require('@/assets/images/serveHall/task-send.png'),
					activeUrl: require('@/assets/images/serveHall/task-send-active.png')
				}
			], //任务中心分类
			taskActive: 'todo', //任务中心当前选中
			taskLoading: false, //任务模块加载动画
			taskList: [], //任务列表
			collectedList: [], //收藏列表id合集
			recentList: [] //访问列表id合集
		};
	},
	computed: {
		// 获取当前登录人信息
		loginUserInfo() {
			return JSON.parse(localStorage.getItem('loginUserInfo') || '{}');
		},
		userinfo() {
			return JSON.parse(localStorage.getItem('userinfo') || '{}');
		}
	},

	async mounted() {
		window.addEventListener('visibilitychange', () =>
			this.interfaceData('/oa/wfPending/wfPendingMerge/list_json.dhtml', true)
		);

		this.interfaceData('/oa/wfPending/wfPendingMerge/list_json.dhtml', true); //我的待办接口
		// await this.getList(); //系统直通车列表数据
		this.getListData(); //业务直通车列表接口
		// await Promise.all[(this.getList(), this.getRecent())];
	},
	// 办理后检查其他窗口关闭并更新
	beforeDestroy() {
		window.removeEventListener('visibilitychange', () =>
			this.interfaceData('/oa/wfPending/wfPendingMerge/list_json.dhtml', true)
		);
	},
	methods: {
		/**
		 * @description 获取公告列表
		 * */
		// 系统直通车列表数据
		getList() {
			this.$.ajax({
				url: '/ybzy/platApplicationType/front/listLevelOne',
				method: 'get'
				// data: data
			}).then(res => {
				if (res.rCode === 0) {
					this.myAppDataTotol = res.results || [];
					// this.myAppData = this.myAppDataTotol.slice(0, 11).concat(this.myAppMore);
					this.myAppData = this.myAppDataTotol;
				} else {
					this.$message.error(res.msg);
				}
			});
		},
		//我的待办接口
		interfaceData(url, need) {
			this.taskLoading = true;
			let data = {
				rows: 3,
				page: 1,
				sord: 'desc'
			};
			if (need) {
				data.query_pendingattr = 0;
			}
			this.$.ajax({
				url: url,
				data: data,
				method: 'POST'
			})
				.then(res => {
					this.taskList = res.data || [];
					let name = 'first';
					window.parent.postMessage(name, '*');
				})
				.finally(() => {
					this.taskLoading = false;
				});
		},
		//业务直通车列表接口
		getListData() {
			this.loading = true;
			let userId = localStorage.getItem('mobileUserId') || '';
			/**原有代码基础加上判断，没有授权的话请求授权*/
			if (!userId) {
				// checkLogin()
				this.$.ajax({
					url: checkLogin
				}).then(res => {
					this.loading = false;
					let code = res.results.code;
					localStorage.setItem('ssoCode', code);
					let data = {
						serverId: 'ybzyDtcSso',
						authType: '6',
						code
					};
					if (!(code === null)) {
						this.$.ajax({
							url: getEossAuthentication,
							method: 'POST',
							data: data
						})
							.then(res => {
								localStorage.setItem('mobileToken', res.results.mobileToken);
								localStorage.setItem('mobileUserId', res.results.userId);
								this.getListData();
							})
							.catch(err => {
								console.log(err, '认证失败err');
								return '认证失败';
							});
					}
				});
			} else {
				this.$.ajax({
					url: getTransactionList + `?menuCode=cygn&userId=${userId}&type=2`
				})
					.then(res => {
						this.loading = false;

						let arr = [];
						let usullayListGet = [];
						res.results.forEach(i => {
							i.children.forEach(item => {
								arr.push(item);
							});
						});
						// this.serviceList = [
						// 	{
						// 		children: arr,
						// 		text: '全部'
						// 	}
						// ];
						// this.serviceList = this.serviceList.concat(res.results || []);
						// this.serviceData = this.serviceList[0].children;
						this.myAppDataTotol = res.results || [];
						// this.myAppData = this.myAppDataTotol.slice(0, 11).concat(this.myAppMore);
						this.myAppData = this.myAppDataTotol;
						this.getCollected(); //查询收藏
						this.getRecent(); // 查找匹配最近访问
						console.log(this.myAppDataTotol[3].children[0], '业务直通车列表接口');
					})
					.catch(error => {
						console.log(error, 'error');
					});
			}
		},
		/**
		 * @description 最近访问--查询
		 * */
		getRecent() {
			this.$.ajax({
				url: '/ybzy/specificHomepageShortcut/getList',
				params: {
					orderBy: 'updateTime',
					asc: false,
					type: 1, // 0, "收藏"  1, "最近访问" 2, "公告已读"
					pageSize: 10
				}
			}).then(res => {
				if (res.rCode == 0) {
					let list = res?.results?.records || [];
					list = list.reduce((acc, cur) => {
						acc.push(cur.sourceId);
						return acc;
					}, []);
					// 开始匹配访问列表
					let totalList = this.myAppDataTotol.reduce((acc, cur) => {
						acc = acc.concat(cur.children || []);
						return acc;
					}, []);
					this.recentList = totalList.reduce((acc, cur) => {
						if (list.includes(cur.id)) {
							let index = list.indexOf(cur.id);
							acc[index] = cur;
						}
						return acc;
					}, []);
					// debugger
				}
			});
		},
		/**
		 * @description 最近访问--创建
		 * */
		saveRecent(item, type) {
			let data = {
				sourceId: item.id, //目标Id
				type: 1, // 0, "收藏"  1, "最近访问" 2, "公告已读"
				sourceType: type //目标类型（0：系统，1：菜单）
			};
			this.$.ajax({
				url: '/ybzy/specificHomepageShortcut/save',
				method: 'POST',
				data: data
			}).then(res => {
				if (res.rCode == 0) {
					setTimeout(() => {
						this.getRecent();
					}, 10000);
				}
			});
		},
		/**
		 * @description 收藏--查询
		 * */
		getCollected() {
			this.$.ajax({
				url: '/ybzy/specificHomepageShortcut/getList',
				params: {
					pageSize: 1000,
					type: 0 // 0, "收藏"  1, "最近访问" 2, "公告已读"
				}
			}).then(res => {
				if (res.rCode == 0) {
					let list = res?.results?.records || [];
					this.collectedList = list.reduce((acc, cur) => {
						acc.push(cur.sourceId);
						return acc;
					}, []);
				}
			});
		},
		/**
		 * @description 收藏--创建
		 * */
		saveCollect(item, type) {
			let data = {
				sourceId: item.id, //目标Id
				type: 0, // 0, "收藏"  1, "最近访问" 2, "公告已读"
				sourceType: type //目标类型（0：系统，1：菜单）
			};
			this.$.ajax({
				url: '/ybzy/specificHomepageShortcut/save',
				method: 'POST',
				data: data
			})
				.then(res => {
					if (res.rCode == 0) {
						this.$message.success(res.msg);
						this.getCollected();
					} else {
						this.$message.info(res.msg);
					}
				})
				.catch(res => {
					this.$message.warning(res.msg);
				});
		},
		/**
		 * @description 取消收藏--查询
		 * */
		deleteBatchIds(item) {
			this.$.ajax({
				url: '/ybzy/specificHomepageShortcut/deleteCollect',
				method: 'POST',
				data: {
					ids: item.id //目标Id
				}
			})
				.then(res => {
					if (res.rCode == 0) {
						this.$message.success(res.msg);
						this.getCollected();
					} else {
						this.$message.info(res.msg);
					}
				})
				.catch(res => {
					this.$message.warning(res.msg);
				});
		},
		// // 跳转公告详情页面
		// onDetail(item) {
		// 	this.$router.push({
		// 		path: '/noticeDetail',
		// 		query: {
		// 			id: item.id
		// 		}
		// 	});
		// },
		// 业务直通车类型切换
		handleClick(event) {
			this.activeName = event.name || '';
			this.serviceIndex = event.index || 0;
			this.serviceData = this.serviceList[this.serviceIndex].children;
			// 看是否需要正在进行筛选
			this.keyWord && this.onSearch(this.keyWord);
		},
		// 任务中心切换事件
		taskHandleClick(tab) {
			this.taskActive = tab.code;
			this.taskList = [];
			switch (tab.code) {
				case 'todo':
					this.interfaceData('/oa/wfPending/wfPendingMerge/list_json.dhtml', true);
					break;
				case 'send':
					this.interfaceData('/oa/task/wfApplication/me_list_json.dhtml', false);
					break;
				default:
					break;
			}
		},
		// 业务直通车跳转判断
		// toOpenWindow(item) {
		// 	if (item.id == '11111111') {
		// 		return;
		// 	}
		// 	this.visible = true;
		// 	this.$nextTick(() => {
		// 		this.$refs.iframe.src = `${alumniUrl}${item.url}`;
		// 	});
		// 	this.saveRecent(item, 1);
		// },
		//处理图片路径
		handelPicUrl(url) {
			return `${this.$host}/static/andyui2.0/css/picIcon/img/${url}`;
		},
		/**
		 * @description 点击跳转对应页面
		 * */
		jumpPage(url) {
			this.$router.push(url);
		},
		// 应用跳转对应链接判断
		myAppDataBtn(item, isRecent = true) {
			// if (item.type != 'more') {
			// 	this.saveRecent(item, 0);
			// } else {
			// 	return this.jumpPage(item.url);
			// }
			((isRecent && item.url) || item?.children.length > 0) && this.saveRecent(item, 0);
			if (item?.children.length > 0) return;
			if (!item.url) {
				this.$notify.info({
					title: '消息',
					message: '该功能正在努力开发中，敬请期待！',
					duration: 2000
				});
				return;
			}

			if (item.appCode === 'OA office') {
				if (!this.userinfo.phone && !this.loginUserInfo.phone) {
					this.$notify.info({
						title: '消息',
						message: '未绑定手机号码！',
						duration: 2000
					});
					return;
				}
				this.$request2({
					headers: {
						Clientid: 'yzy_sso',
						Clientsecret: 'K1M6Q1L4A0'
					},
					type: 'form_data_type',
					url: '/oa-api/SSO/GetjwtToken',
					method: 'POST',
					data: qs.stringify({ PhoneNo: this.userinfo.phone || this.loginUserInfo.phone })
				}).then(res => {
					const _data = res.Data || {};
					window.open(`${item.url}?jwtToken=${_data.jwtToken}&tokenSign=${_data.sign}`);
				});
				return;
			}
			if (item.url.includes('component')) {
				const url = item.url;
				// component=leave&type=1 url是这样的帮我转成对象
				const arr = url.split('&');
				const obj = {
					title: item.text
				};
				arr.forEach(item => {
					obj[item.split('=')[0]] = item.split('=')[1];
				});
				this.onComponent(obj);
				return;
			}
			if (item.url.includes('redirectUri=/project-ybzy/ybzy/index.html')) {
				window.location.href = item.url + '&isTeacher=true';
			} else {
				const openNewWindow = window.open(item.url);
				let version = this.getBrowserVersion();
				const LeaderRole = localStorage.getItem('LeaderRole');
				let data = {
					useIdent: LeaderRole === 'student' ? '1' : '0',
					menuName: item.text,
					menuId: item.id,
					lastIp: '123',
					lastBro: version
				};
				this.$request({
					url: '/ybzy/use/count/put',
					data: data,
					method: 'post',
					format: false
				}).then(res => {
					console.log('');
				});
				// 在新窗口的Vue实例中监听beforeunload事件
				if (openNewWindow) {
					// 检查新窗口是否关闭
					const intervalId = setInterval(() => {
						if (openNewWindow.closed) {
							clearInterval(intervalId);
							this.$request({
								url: '/ybzy/use/count/down',
								data: data,
								method: 'post',
								format: false
							}).then(res => {
								console.log('');
							});
							// console.log('Popup window has been closed');
							// 执行其他逻辑
						}
					}, 5000);
				}
			}
		},
		// 处理组件
		onComponent(obj) {
			this.iframeUrl = '';
			this.companyName = '';
			switch (obj.component) {
				case 'Leave':
					this.dialogTitle = '新增' + obj.title;
					this.companyName = obj.component;
					this.visible = true;
					break;
				case 'System':
					this.dialogTitle = obj.title;
					this.companyName = obj.component;
					this.visible = true;
					break;
				case 'Research':
					this.dialogTitle = obj.title;
					this.companyName = obj.component;
					this.visible = true;
					break;
				default:
					console.log('没有找到组件');
					break;
			}
		},
		// 内容搜索事件
		onSearch(value) {
			// myAppDataTotol // 系统直通车
			// serviceData //业务直通车
			this.keyWord = value;
			if (!value) {
				// 数据恢复
				this.myAppData = this.myAppDataTotol;
			} else {
				let appList = [];
				// 系统直通车数据筛选
				for (let app of this.myAppDataTotol) {
					app.children &&
						app.children.reduce((acc, cur) => {
							if (cur.text.includes(value)) {
								acc.push(cur);
							}
							return acc;
						}, appList);
				}
				this.myAppData = [{ text: '搜索', children: appList }];
			}
		},
		//业务模块更多跳转
		taskMore() {
			let obj = {
				name: this.taskActive == 'todo' ? '我的代办' : '我发起的'
			};
			this.$refs.navigation.onNavClick(obj);
		},
		// 任务待办跳转链接
		taskJump(e) {
			// window.open(alumniUrl + '/oa/wfPending/wfPending/list.dhtml?serverId=ybzyDtcSso&authType=6');
			let url;
			if (this.taskActive == 'send') {
				url = e.readurl.replace(/\[recordid\]/g, e.apprecordid).replace(/\[pendingId\]/g, e.id);
			} else {
				url =
					e.pendingurl.replace(/\[recordid\]/g, e.apprecordid).replace(/\[pendingId\]/g, e.id) +
					`&itemname=${e.itemname} `;
			}
			window.open(alumniUrl + url);
		},
		getBrowserVersion() {
			const userAgent = navigator.userAgent;
			let version = '';

			// 判断是否为 Chrome 浏览器
			if (/Chrome\/(\S+)/.test(userAgent)) {
				version = 'Chrome' + userAgent.match(/Chrome\/(\S+)/)[1];
			}
			// 判断是否为 Firefox 浏览器
			else if (/Firefox\/(\S+)/.test(userAgent)) {
				version = 'Firefox' + userAgent.match(/Firefox\/(\S+)/)[1];
			}
			// 判断是否为 Safari 浏览器
			else if (/Safari\/(\S+)/.test(userAgent)) {
				version = 'Safari' + userAgent.match(/Version\/(\S+)/)[1];
			}
			// 判断是否为 Edge 浏览器
			else if (/Edg\/(\S+)/.test(userAgent)) {
				version = 'Edge' + userAgent.match(/Edg\/(\S+)/)[1];
			}
			// 判断是否为 Internet Explorer 浏览器
			else if (/MSIE (\S+);/.test(userAgent)) {
				version = 'Explorer' + userAgent.match(/MSIE (\S+);/)[1];
			}

			return version;
		}
	}
};
</script>

<style lang="scss" scoped>
.ellipsis-1 {
	width: 100%;
	text-align: center;
	z-index: 10;
	cursor: pointer;
}
$maxWidth: 1200px;
.box-center {
	width: $maxWidth;
	margin: 0 auto;
}
.serve-box {
	width: 100%;
	// height: 100%;
	min-width: 1550px;
	// position: relative;
	// overflow: auto;
	.notice {
		width: 100%;
		position: relative;
		.search {
			padding: 20px 0;
			.search-box {
				margin: 0 auto;
			}
		}
	}
	.collect-box {
		position: absolute;
		right: 5px;
		top: 5px;
		padding: 0 8px;
		height: 22px;
		background: rgba(0, 0, 0, 0.2);
		border-radius: 10px;
		border: 1px solid rgba(255, 255, 255, 0.4);
		color: #ffffff;
		font-size: 12px;
		font-family: MicrosoftYaHei;
		display: flex;
		align-items: center;
		display: none;
		cursor: pointer;
		.icon {
			font-size: 12px;
			margin-right: 4px;
		}
	}
	.collected {
		color: #ffdc86;
	}
	.active-bg {
		width: 124px;
		height: 74px;
		position: absolute;
		right: 10px;
		bottom: 0;
		display: none;
	}
	// 新的布局样式
	.system {
		margin-top: 20px;
		width: $maxWidth;
		margin-bottom: 60px;
		.card-box {
			border-radius: 8px;
			overflow: hidden;
		}
		.system-box {
			position: relative;
			width: 100%;
			min-height: 234px;
			background: linear-gradient(180deg, rgba(239, 255, 255, 0.6) 0%, rgba(229, 250, 255, 0) 100%);
			box-shadow: 0px 2px 10px 0px rgba(70, 145, 217, 0.2);
			border-radius: 8px;
			border: 2px solid transparent;
			border-image: linear-gradient(180deg, rgba(255, 255, 255, 1), rgba(255, 255, 255, 0)) 2 2;
			backdrop-filter: blur(3px);
			margin-bottom: 20px;
			padding: 20px 0px 8px 20px;
			position: relative;
			&::after {
				content: '';
				position: absolute;
				top: -2px;
				left: -2px;
				bottom: 0;
				right: -2px;
				height: 10%;
				border: 2px solid #fff;
				border-radius: 8px 8px 0 0;
				border-bottom: none;
				z-index: -1;
			}
			// &::after {
			// 	position: absolute;
			// 	top: -2px;
			// 	bottom: -2px;
			// 	left: -2px;
			// 	right: -2px;
			// 	background: linear-gradient(red, blue);
			// 	//  linear-gradient(180deg, rgba(255, 255, 255, 1), rgba(255, 255, 255, 0)) 2 2
			// 	content: '';
			// 	z-index: -1;
			// 	border-radius: 8px;
			// }
		}
		.system-title {
			font-family: MicrosoftYaHei, MicrosoftYaHei;
			font-weight: bold;
			font-size: 18px;
			color: #053b6d;
			line-height: 24px;
			margin-bottom: 20px;
		}
		.system-list {
			display: flex;
			flex-wrap: wrap;
		}
		.system-item {
			position: relative;
			display: inline-block;
			width: 183px;
			height: 150px;
			margin-bottom: 12px;
			margin-right: 12px;
			padding-top: 22px;
			background: rgba(255, 255, 255, 0.49);
			border-radius: 8px;
			border: 1px solid #ffffff;
			flex-shrink: 0;
			display: flex;
			flex-direction: column;
			// justify-content: center;
			align-items: center;
			.item-img-active {
				width: 24px;
				height: 23px;
				display: none;
			}
			&:hover {
				background: linear-gradient(180deg, #4fcdff 0%, #0076ec 100%);
				box-shadow: 0px 2px 10px 0px rgba(0, 38, 75, 0.5);
				.collect-box {
					display: inline-flex;
				}
				.active-bg {
					display: block;
				}
				.name,
				.desc {
					color: #ffffff;
				}
			}
		}
		.item-img {
			width: 58px;
			height: 58px;
			margin-bottom: 10px;
		}
		.name {
			font-family: MicrosoftYaHei;
			font-size: 14px;
			color: #000000;
			line-height: 20px;
			max-width: 100%;
		}
		.desc {
			font-family: ArialMT;
			font-size: 12px;
			color: #808890;
			line-height: 18px;
			max-width: 100%;
		}
	}
	.task {
		width: 100%;
		height: 436px;
		background: rgba(16, 58, 100, 0.1);
		background: url('~@/assets/images/serveHall/home-bg3.png') center;
		background-size: cover;
		.task-box {
			height: 100%;
			padding: 40px 0;
			display: flex;
			justify-content: space-between;
		}
		.task-card {
			width: 590px;
			height: 356px;
			background: rgba(0, 0, 0, 0.25);
			border-radius: 8px;
			.title-box {
				height: 24px;
				font-size: 18px;
				font-family: MicrosoftYaHei;
				color: #ffffff;
				line-height: 24px;
				padding-left: 30px;
				position: relative;
				display: flex;
				justify-content: space-between;
				&::after {
					content: '';
					position: absolute;
					left: 0;
					top: 4px;
					width: 16px;
					height: 16px;
					background: #0175e8;
				}
			}
		}
		.task-title {
			width: 100%;
			height: 64px;
			padding: 20px 15px 20px;
			.more-btn {
				font-size: 14px;
				color: #ffffff;
				cursor: pointer;
				&:hover {
					color: #0175e8;
				}
			}
		}
		.task-list {
			display: flex;
			overflow: hidden;
			.left {
				width: 120px;
				height: 292px;
				padding: 20px 0;
				background: rgba(6, 27, 48, 0.2);
				border-radius: 0px 8px 0px 8px;
				&-item {
					display: flex;
					flex-direction: column;
					justify-content: center;
					align-items: center;
					width: 100%;
					height: 115px;
					margin-bottom: 22px;
					cursor: pointer;
					font-size: 14px;
					font-family: MicrosoftYaHei;
					color: #a3b0be;
					line-height: 22px;
					.item-img {
						width: 24px;
						height: 23px;
					}
					.item-img-active {
						width: 24px;
						height: 23px;
						display: none;
					}
					&:hover {
						border-right: 3px solid #0175e8;
						background: linear-gradient(
							90deg,
							rgba(1, 117, 232, 0.15) 0%,
							rgba(1, 117, 232, 0.4) 100%
						);
						font-weight: bold;
						color: #31c5ff;
						.item-img {
							display: none;
						}
						.item-img-active {
							display: block;
						}
					}
				}
				&-item-active {
					border-right: 3px solid #0175e8;
					background: linear-gradient(
						90deg,
						rgba(1, 117, 232, 0.15) 0%,
						rgba(1, 117, 232, 0.4) 100%
					);
					font-weight: bold;
					color: #31c5ff;
					.item-img {
						display: none;
					}
					.item-img-active {
						display: block;
					}
				}
			}
			.right {
				width: 440px;
				height: 292px;
				margin-left: 15px;
				.right-item {
					width: 100%;
					height: 92px;
					margin-bottom: 10px;
					border-bottom: 1px solid rgba(255, 255, 255, 0.2);
					display: flex;
					flex-direction: column;
					cursor: pointer;
					.item-time {
						height: 20px;
						font-size: 14px;
						font-family: ArialMT;
						color: #ffffff;
						line-height: 20px;
					}
					.item-title {
						height: 24px;
						margin-top: 8px;
						font-size: 16px;
						font-family: MicrosoftYaHei, MicrosoftYaHei;
						font-weight: bold;
						color: #ffffff;
						line-height: 24px;
					}
					.item-desc {
						height: 20px;
						margin-top: 8px;
						font-size: 14px;
						font-family: MicrosoftYaHei;
						color: #a0a4ab;
						line-height: 20px;
						display: flex;
						justify-content: space-between;
						// display: inline-block;
					}
					.desc-text {
					}
					.item-status {
						font-size: 14px;
						font-family: MicrosoftYaHei;
						color: #31c5ff;
						line-height: 20px;
						flex-shrink: 0;
					}
					&:hover {
						.item-title {
							color: #51a8ff;
						}
					}
				}
			}
		}
		.last-used {
			padding: 20px;
			.used-box {
				width: 100%;
				height: calc(100% - 20px);
				display: flex;
				flex-wrap: wrap;
				.used-item {
					margin: 20px 8px 0;
					margin-top: 20px;
					width: 94px;
					height: 128px;
					background: rgba(0, 0, 0, 0.25);
					border-radius: 8px;
					// margin-right: 20px;
					cursor: pointer;
					display: flex;
					flex-direction: column;
					align-items: center;
					padding: 15px 0 10px;
					position: relative;
					overflow: hidden;
					&:nth-child(5n) {
						margin-right: 0;
					}
					&:hover {
						background: linear-gradient(180deg, #4fcdff 0%, #0076ec 100%);
						box-shadow: 0px 2px 10px 0px rgba(0, 38, 75, 0.5);
						.active-bg {
							display: block;
							left: 0;
						}
					}
					.item-img {
						width: 58px;
						height: 58px;
						border-radius: 12px;
					}
					.name {
						max-width: 90%;
						margin-top: 5px;
						font-size: 14px;
						color: #ffffff;
						line-height: 20px;
						text-align: center;
					}
				}
			}
		}
	}
}
</style>
<style scoped>
::v-deep .el-tabs__header {
	margin: 0;
}
::v-deep .el-tabs__item {
	height: 36px;
	line-height: normal;
}
::v-deep .el-tabs__nav-wrap::after {
	background-color: transparent;
}
::v-deep .el-tabs__item {
	font-size: 16px;
	color: #3e3e3e;
	line-height: 24px;
}
::v-deep .el-tabs__item:hover {
	color: #0175e8;
}
::v-deep .el-tabs__item.is-active {
	font-weight: bold;
	color: #0175e8;
}
::v-deep .el-tabs__active-bar {
	height: 4px;
	background: #0175e8;
	border-radius: 2px;
}
</style>
<style lang="scss">
.popper-used-box {
	background: linear-gradient(180deg, rgba(239, 255, 255, 1) 0%, rgba(229, 250, 255, 0) 100%);
	box-shadow: 0px 2px 10px 0px rgba(70, 145, 217, 0.5);
	border-radius: 8px;
	border: 2px solid transparent;
	// border-image: linear-gradient(180deg, rgba(255, 255, 255, 1), rgba(255, 255, 255, 0)) 2 2;
	backdrop-filter: blur(5px);
	// position: fixed;
	// z-index: 1060;
	.used-box {
		display: flex;
		flex-wrap: wrap;
		padding: 5px 0;
		.used-item {
			margin: 0 5px;
			// width: 134px;
			// height: 128px;
			width: 172px;
			height: 140px;
			background: rgba(255, 255, 255, 0.79);
			border-radius: 8px;
			border: 1px solid #ffffff;
			cursor: pointer;
			display: flex;
			flex-direction: column;
			align-items: center;
			padding: 15px 0 10px;
			position: relative;
			overflow: hidden;
			.item-img {
				width: 58px;
				height: 58px;
				border-radius: 12px;
			}
			.name {
				max-width: 90%;
				margin-top: 5px;
				font-size: 14px;
				// line-height: 20px;
				// height: auto;
				color: #000000;
				text-align: center;
			}
			.active-bg {
				display: none;
				left: 20px;
				width: 100px;
				top: 60%;
				position: absolute;
			}
			&:hover {
				background: linear-gradient(180deg, #4fcdff 0%, #0076ec 100%);
				box-shadow: 0px 2px 10px 0px rgba(0, 38, 75, 0.5);
				color: #fff;
				.name {
					color: #fff;
				}
				.active-bg {
					display: block;
				}
			}
		}
	}
	.small {
		display: flex;
		flex-wrap: wrap;
		.used-item {
			margin: 0 5px;
			width: 94px;
			height: 123px;
			padding: 10px 0 0;
			.item-img {
				width: 58px;
				height: 58px;
			}
			// .name {
			// 	max-width: 90%;
			// 	margin-top: 5px;
			// 	font-size: 14px;
			// 	// color: #000000;
			// 	color: #fff;
			// 	text-align: center;
			// }
		}
	}
	.popper__arrow {
		border-top-color: rgba(239, 255, 255, 0.8) !important;
		&::after {
			border-top-color: rgba(239, 255, 255, 0.6) !important;
		}
	}
}
</style>
