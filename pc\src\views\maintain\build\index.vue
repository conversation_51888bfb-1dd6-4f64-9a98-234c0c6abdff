<template>
	<div class="content">
		<es-data-table
			ref="table"
			:row-style="tableRowClassName"
			:full="true"
			:fit="true"
			:thead="thead"
			:toolbar="toolbar"
			:border="true"
			:option-data="optionData"
			:page="true"
			:url="dataTableUrl"
			:numbers="true"
			:param="params"
			close
			form
			@edit="changeTable"
			@btnClick="btnClick"
		></es-data-table>

		<!-- <es-data-table
			:data="tableData"
			style="width: 100%; margin-bottom: 20px"
			row-key="id"
			border
			default-expand-all
			:tree-props="{ children: 'children', hasChildren: 'hasChildren' }"
		>
			<el-table-column prop="roomName" label="楼栋/房间" align="center"></el-table-column>
			<el-table-column prop="code" label="编码" align="center"></el-table-column>
			<el-table-column prop="sortNum" label="排序" align="center"></el-table-column>
			<el-table-column label="状态" align="center">
				<template slot-scope="scope">
					<span style="margin-left: 10px">{{ scope.row.states === 1 ? '启用' : '禁用' }}</span>
				</template>
			</el-table-column>
			<el-table-column label="操作" align="center">
				<template slot-scope="scope">
					<el-button type="text" icon="el-icon-view" @click="btnClick('view', scope)">
						查看
					</el-button>
					<el-button type="text" icon="el-icon-edit" @click="btnClick('edit', scope)">
						编辑
					</el-button>
					<el-button type="text" icon="el-icon-delete" @click="btnClick('delelt', scope)">
						删除
					</el-button>
				</template>
			</el-table-column>
		</es-data-table> -->

		<!-- 编辑 -->
		<es-dialog
			:title="formTitle"
			:visible.sync="showForm"
			width="670px"
			height="580px"
			:drag="false"
			:close-on-click-modal="false"
			:destroy-on-close="true"
		>
			<es-form
				v-if="showForm"
				ref="form"
				:model="formData"
				:contents="formItemList"
				:genre="2"
				collapse
				:submit="formTitle !== '查看'"
				@submit="handleFormSubmit"
				:reset="showForm"
			/>
		</es-dialog>
	</div>
</template>

<script>
import interfaceUrl from '@/http/maintain/applicationApi';
import SnowflakeId from 'snowflake-id';
export default {
	data() {
		return {
			ownId: '',
			loading: false,
			dataTableUrl: interfaceUrl.buildListJson,
			dataTreeTableUrl: interfaceUrl.getBuildingTreeList,
			tableData: [],
			showForm: false,
			formData: {},
			roomTableData: [],
			formTitle: '编辑',
			optionData: {
				states: [
					{
						value: 1,
						name: '启用'
					},
					{
						value: 0,
						name: '停用'
					}
				]
			},
			toolbar: [
				{
					type: 'button',
					contents: [
						{
							text: '新增',
							code: 'add',
							type: 'primary'
						}
					]
				},
				{
					type: 'search',
					reset: true,
					contents: [
						{
							type: 'text',
							name: 'keyword',
							placeholder: '关键字查询',
							clearable: true
						}
					]
				}
			],
			thead: [
				{
					title: '楼栋/房间',
					align: 'left',
					field: 'roomName',
					showOverflowTooltip: true
				},
				{
					title: '编码',
					align: 'left',
					field: 'code',
					showOverflowTooltip: true
				},
				{
					title: '排序',
					align: 'center',
					field: 'sortNum',
					showOverfolwTooltip: true
				},
				{
					title: '状态',
					align: 'center',
					field: 'states',
					type: 'switch'
				},
				// {
				// 	title: '状态',
				// 	align: 'center',
				// 	field: 'states',
				// 	render: (h, param) => {
				// 		return h('p', null, param.row.states === 1 ? '启用' : '停用');
				// 	}
				// },
				{
					title: '操作',
					type: 'handle',
					width: 180,
					template: '',
					events: [
						{
							code: 'view',
							text: '查看'
						},
						{
							code: 'edit',
							text: '编辑'
						},
						{
							code: 'delelt',
							text: '删除'
						}
					]
				}
			],
			params: { orderBy: 'sortNum', asc: true }
		};
	},
	computed: {
		formItemList() {
			const readonly = this.formTitle === '查看';
			return [
				{
					label: '名称',
					name: 'roomName',
					placeholder: '请输入名称',
					event: 'multipled',
					readonly: readonly,
					rules: {
						required: true,
						message: '请输入名称',
						trigger: 'blur'
					},
					verify: 'required',
					col: 12
				},
				{
					label: '编号',
					name: 'code',
					placeholder: '请输入编号',
					event: 'multipled',
					readonly: readonly,
					rules: {
						required: true,
						message: '请输入编号',
						trigger: 'blur'
					},
					verify: 'required',
					col: 12
				},
				// {
				// 	type: 'select',
				// 	name: 'parentId',
				// 	label: '所属楼栋',
				// 	placeholder: '请选择所属楼栋',
				// 	readonly: readonly,
				// 	url: interfaceUrl.getBuildingParentList,
				// 	'label-key': 'roomName',
				// 	'value-key': 'id',
				// 	col: 12,
				// 	clearable: true
				// },
				// {
				// 	type: 'select',
				// 	name: 'parentId',
				// 	label: '所属楼栋',
				// 	placeholder: '请选择所属楼栋',
				// 	readonly: readonly,
				// 	tree: true,
				// 	data: this.tableData,
				// 	'label-key': 'roomName',
				// 	'value-key': 'id',
				// 	col: 12,
				// 	clearable: true
				// },
				{
					type: 'select',
					name: 'campus',
					label: '所属校区',
					placeholder: '所属校区',
					event: 'multipled',
					readonly: readonly,
					sysCode: 'hq_campus',
					'label-key': 'shortName',
					'value-key': 'cciValue',
					col: 12,
					clearable: true
				},
				{
					label: '备注',
					type: 'textarea',
					name: 'remark',
					readonly: readonly,
					rows: 5
				},
				{
					label: '排序',
					type: 'number',
					name: 'sortNum',
					placeholder: '请输入排序',
					event: 'multipled',
					readonly: readonly,
					col: 6
				},
				{
					label: '状态',
					name: 'states',
					type: 'switch',
					data: [
						{ value: 1, name: '启用' },
						{ value: 0, name: '停用' }
					],
					disabled: readonly,
					col: 6
				}
			];
		}
	},
	watch: {},
	created() {
		this.getTreeList();
	},
	mounted() {},
	methods: {
		/**
		 * 表格行高
		 */
		tableRowClassName({ row, rowIndex }) {
			let styleRes = {
				height: '54px !important'
			};
			return styleRes;
		},
		async getTreeList() {
			let { rCode, msg, results } = await this.$.ajax({
				url: this.dataTreeTableUrl,
				method: 'GET'
			});
			if (rCode == 0) {
				this.tableData = results;

			} else {
				this.$message.error(msg);
			}
		},
		// resetForm() {
		// 	this.params.keyword = '';
		// 	this.getTreeList();
		// },
		changeTable(val) {
			switch (val.name) {
				case 'states':
					this.changeStatus(val.data.id);
					break;
			}
		},
		//改变数据启用状态
		changeStatus(id) {
			this.$request({
				url: interfaceUrl.buildUpdateStates,
				data: { id: id },
				method: 'POST'
			}).then(res => {
				if (res.success) {
					this.$message.success('操作成功！');
				} else {
					this.$message.error(res.msg);
				}
			});
		},
		/**
		 * 操作按钮点击事件
		 * @param {*} res
		 */
		async btnClick(res) {
			let code = res.handle.code;
			switch (code) {
				case 'add':
					// 新增
					this.formTitle = '新增';
					const snowflake = new SnowflakeId();
					let id = snowflake.generate();
					this.ownId = id;
					this.formData = { id: id };

					this.showForm = true;
					break;
				case 'edit':
				case 'view':
					let { rCode, msg, results } = await this.$.ajax({
						url: interfaceUrl.getBuildInfo + '/' + res.row.id,
						method: 'GET'
					});
					if (rCode == 0) {
						this.formData = results;
						if (code == 'edit') {
							this.formTitle = '编辑';
						} else {
							this.formTitle = '查看';
						}
						this.showForm = true;
					} else {
						this.$message.error(msg);
					}
					break;
				case 'delelt':
					this.deleteRows(res.row.id);
					break;
				default:
					break;
			}
		},
		// async btnClick(code, res) {
		// 	switch (code) {
		// 		case 'add':
		// 			// 新增
		// 			this.formTitle = '新增';
		// 			const snowflake = new SnowflakeId();
		// 			this.formData = { id: snowflake.generate() };
		// 			this.showForm = true;
		// 			break;
		// 		case 'edit':
		// 		case 'view':
		// 			let { rCode, msg, results } = await this.$.ajax({
		// 				url: interfaceUrl.getBuildInfo + '/' + res.row.id,
		// 				method: 'GET'
		// 			});
		// 			if (rCode == 0) {
		// 				this.formData = results;
		// 				if (code == 'edit') {
		// 					this.formTitle = '编辑';
		// 				} else {
		// 					this.formTitle = '查看';
		// 				}
		// 				this.showForm = true;
		// 			} else {
		// 				this.$message.error(msg);
		// 			}
		// 			break;
		// 		case 'delelt':
		// 			this.deleteRows(res.row.id);
		// 			break;
		// 		default:
		// 			break;
		// 	}
		// },
		handleFormSubmit(data) {
			let formData = data;
			let url = '';
			if (this.formTitle == '新增') {
				url = interfaceUrl.buildSave;
			} else {
				url = interfaceUrl.buildUpdate;
			}
			this.$request({
				url: url,
				data: formData,
				method: 'POST'
			}).then(res => {
				if (res.rCode == 0) {
					this.$message.success('操作成功');
					this.showForm = false;
					this.formData = {};
					this.$refs.table.reload();
					this.getTreeList();
				} else {
					this.$message.error(res.msg);
				}
			});
		},
		deleteRows(id) {
			this.$confirm('确定要删除选中的数据吗？', '删除', {
				confirmButtonText: '确定',
				cancelButtonText: '取消',
				type: 'warning'
			})
				.then(() => {
					this.$request({
						url: interfaceUrl.buildDelById,
						data: { id: id },
						method: 'POST'
					}).then(res => {
						if (res.rCode === 0) {
							this.$message.success('操作完成');

							this.$refs.table.reload();
							this.getTreeList();
						} else {
							this.$message.error(res.msg);
						}
					});
				})
				.catch(() => {});
		}
	}
};
</script>
<style scoped lang="scss">
@import '../../../assets/style/style.scss';

.content {
	width: 100%;
	height: 100%;
}

.el-dialog__body {
	.content-warning-Btn {
		float: right;
		margin: 10px 0px;
	}
	.btn-box {
		margin-top: 20px;
		display: flex;
		justify-content: flex-end;

		.btn {
			padding: 5px 10px;
			color: #666;
			border: 1px solid #eee;
			cursor: pointer;
			margin-right: 5px;
			border-radius: 4px;
		}
	}
}
</style>
