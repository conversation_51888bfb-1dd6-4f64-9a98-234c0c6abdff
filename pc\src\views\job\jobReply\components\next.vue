<template>
	<es-dialog
		title="审核"
		:visible.sync="showForm"
		:drag="false"
		height="350px"
		width="600px"
		siz="sm"
		:close-on-click-modal="false"
		:destroy-on-close="true"
	>
		<es-form
			ref="form"
			v-loading="loading"
			:model="formData"
			:contents="formItemList"
			label-width="100px"
			:genre="2"
			collapse
			@submit="handleFormSubmit"
		/>
	</es-dialog>
</template>

<script>
import interfaceUrl from '@/http/job/jobDualSelect/api.js';

export default {
	props: {
		id: {
			type: String,
			default: ''
		}
	},
	data() {
		return {
			formTitle: '编辑',
			//表单数据
			formData: {},
			//表单数据
			showForm: false,
			loading: false
		};
	},
	computed: {
		formItemList() {
			return [
				{
					name: 'auditStates',
					label: '审核状态',
					type: 'radio',
					value: '',
					col: 12,
					data: [
						{ value: 1, label: '通过' },
						{ value: 9, label: '驳回' }
					],
					rules: {
						required: true,
						message: '请选择审核状态',
						trigger: 'change'
					}
				},
				{
					name: 'remark',
					label: '审核意见',
					type: 'textarea',
					value: '',
					rows: 5,
					col: 12,
					rules: {
						required: true,
						message: '请填写审核意见',
						trigger: 'blur'
					}
				}
			];
		}
	},
	methods: {
		handleFormSubmit(formData) {
			this.loading = true;
			//企业回复中职位列表
			this.$request({
				url: interfaceUrl.auditJobReply,
				data: {
					...formData,
					id: this.id
				},
				method: 'POST'
			}).then(res => {
				this.loading = false;

				if (res.rCode === 0) {
					this.$message({
						message: res.msg || '操作成功！',
						type: 'success'
					});
					this.showForm = false;
					this.$emit('closeDialog');
					return;
				}
				this.$message({
					message: res.msg || '操作失败！',
					type: 'error'
				});
			});
		}
	}
};
</script>

<style></style>
