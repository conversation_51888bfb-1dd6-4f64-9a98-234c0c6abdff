export default {
	computed: {
		formItemList() {
			// 基础配置
			const baseItems = [
				{
					name: 'auditStatus',
					label: '审核状态',
					type: 'radio',
					col: 12,
					data: this.isSystemReo
						? [
							{ label: '通过', value: 1 },
							{ label: '驳回', value: 2 }
						]
						: [
							{ label: '通过', value: 11 },
							{ label: '驳回', value: 2 }
						],
					'label-key': 'label',
					'value-key': 'value',
					verify: 'required',
					rules: {
						required: true,
						message: '请选择审核状态'
					}
				},
				{
					label: '审核意见',
					name: 'auditOpinion',
					type: 'textarea',
					placeholder: '请输入审核意见',
					rows: 8,
					col: 12,
					// 初始为空，将在watch中动态设置
					rules: {}
				}
			];

			// 监听审核状态变化，动态设置审核意见的验证规则
			this.$watch('formData.auditStatus', (newVal) => {
				const opinionField = baseItems.find(item => item.name === 'auditOpinion');
				if (opinionField) {
					opinionField.rules = newVal === 2 || newVal === '2' // 驳回状态
						? { required: true, message: '请输入审核意见', trigger: 'blur' }
						: {};
				}
			}, { immediate: true }); // 初始化时执行一次

			return baseItems;
		}
	}
};
