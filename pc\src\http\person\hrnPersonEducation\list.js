import httpApi from './api.js';
export default {
	computed: {
		component() {
			return {
				// 树表结构
				//name: 'es-tree-group'
				// 纯列表结构
				name: 'es-data-table'
			};
		},
		treeParam() {
			return {
				// 不需要左边树 可以
				//url: httpApi.hrnPersonEducationTree,
				//title: '分类树'
			};
		},
		table() {
			return {
				url: httpApi.hrnPersonEducationList,
				param: { handlerType: '' },
				full: true,
				fit: true,
				page: true,
				stripe: true,
				numbers: true,
				'row-key': 'id',
				toolbar: [
					{
						type: 'button',
						contents: [
							{
								text: '新增',
								type: 'primary'
							}
						]
					},
					{
						type: 'search',
						reset: true,
						contents: [
							//{
							//	name: 'accountName_like',
							//	placeholder: '配置名称',
							//	label: '配置名称',
							//	col: 6
							//},
							{
								type: 'date',
								col: 6,
								name: 'updateTime_begin',
								label: '更新开始时间',
								placeholder: '更新开始时间'
							},
							{
								type: 'date',
								col: 6,
								name: 'updateTime_end',
								label: '更新结束时间',
								placeholder: '更新结束时间'
							}
						]
					}
				],
				thead: [
					{
						title: '基本信息ID',
						align: 'left',
						field: 'hrnPersonId'
					},
					{
						title: '学历',
						align: 'left',
						field: 'education'
					},
					{
						title: '学位',
						align: 'left',
						field: 'degree'
					},
					{
						title: '毕业院校',
						align: 'left',
						field: 'graduateInstitutions'
					},
					{
						title: '所学专业',
						align: 'left',
						field: 'major'
					},
					{
						title: '毕业年份',
						align: 'left',
						field: 'graduationYear'
					},
					{
						title: '学历证书',
						align: 'left',
						field: 'educationDiploma'
					},
					{
						title: '学位证书',
						align: 'left',
						field: 'degreeDiploma'
					},
					{
						title: '状态',
						align: 'left',
						field: 'status'
					},
					{
						title: '操作',
						type: 'handle',
						width: 180,
						events: [
							{
								text: '查看'
							},
							{
								text: '编辑'
							},
							{
								text: '删除'
							}
						]
					}
				]
			};
		}
	}
};
