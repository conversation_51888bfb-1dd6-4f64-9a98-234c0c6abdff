<template>
<div class="content">
  <es-tabs full v-model="activeName">
    <el-tab-pane v-for="(item, index) of content" :key="index" :label="item.label" :name="String(index)">
      <es-tabs-panel
        v-if="activeName == String(index)"
        v-bind="item"
        :show="activeName === String(index)"
      >
        <es-data-table
          :display="activeName == String(index)"
          :ref="'table' + index"
          :data="tableData"
          full
          :thead="item.contents.thead"
          :toolbar="item.contents.toolbar"
          :url="item.contents.dataTableUrl"
          :checkbox="index === 0"
          :checked="checked"
          checked-key="id"
          :page="item.contents.pageOption"
          :param="item.contents.params"
          @btnClick="btnClick"
          @selection-change="electionChange"
        ></es-data-table>
      </es-tabs-panel>
    </el-tab-pane>
  </es-tabs>

  <!-- 回复 -->
  <es-dialog
    title="查看回复"
    :visible.sync="showForm"
    width="670px"
    height="400px"
    :drag="false"
    :close-on-click-modal="false"
    :destroy-on-close="true"
  >
    <es-form
      v-if="showForm"
      ref="form"
      :model="formData"
      :contents="formItemList"
      height="290px"
      :genre="2"
      collapse
      @reset="showForm = false"
      :submit="false"
    />
  </es-dialog>

  <!-- 查看提交列表 -->
  <es-dialog
    title="查看明细"
    :visible.sync="showView"
    width="1100px"
    height="630px"
    :drag="false"
    :close-on-click-modal="false"
    :destroy-on-close="true"
  >
    <es-data-table
      v-if="showView"
      :toolbar="details.toolbar"
      :thead="details.thead"
      :data="details.data"
      :border="true"
      @btnClick="btnClick"
    ></es-data-table>
  </es-dialog>

  <es-dialog
    title="查看"
    :visible.sync="visibleBasicInfo"
    :show-scale="false"
    size="full"
    height="100%"
  >
    <BasicInfo
      v-if="visibleBasicInfo"
      :id="formId"
      :contents-key="contentsKey"
      title="查看"
      @visible="visibleBasicInfo = false"
    />
  </es-dialog>
</div>
</template>
<script>

import baseInfoApi from "@/http/achievement/baseInfo";
import baseReplyApi from "@/http/achievement/baseReply";
import BasicInfo from '@/views/scientific-sys/components/achievement-info/basic-info';

export default {
  name: "scientificPayoffsManage",
	components: { BasicInfo },
  computed:{
    content() {
      return [
        {
          label: '待提交',
          contents: {
            toolbar:[
              {
                type: 'button',
                contents: [
                  {
                    text: '提交',
                    code: 'submit',
                    type: 'primary'
                  }
                ]
              },
              {
								type: 'search',
								reset: true,
								contents: [
									{
										type: 'text',
										label: '标题',
										name: 'name',
										placeholder: '请输入标题',
										col: 3
									},
									{
										type: 'select',
										label: '成果类型',
										name: 'type',
										placeholder: '请输入成果类型',
										clearable: true,
                    sysCode: 'base_status_type',
										'value-key': 'cciValue',
										'label-key': 'shortName',
										col: 3
									}
								]
              }
            ],
            thead: [
              {
                title: '标题',
                field: 'name',
                align: 'center',
              },
              {
                title: '成果类型',
                field: 'typeTxt',
                align: 'center',
              },
              {
                title: '申报人',
                field: 'createUserName',
                align: 'center',
              },
              {
                title: '申报时间',
                field: 'createTime',
                align: 'center',
              },
              {
                title: '操作',
                type: 'handle',
                width: 100,
                template: '',
                events: [
                  {
                    code: 'viewInfo',
                    text: '查看详情'
                  },

                ]
              }
            ],
            pageOption: {
              layout: 'total, sizes, prev, pager, next, jumper',
              pageSize: 10,
              position: 'center',
              current: 1,
              pageNum: 1
            },
            params: {
              orderBy: 't1.create_time',
              asc: 'false',
              status: '2',
              replyStatus: '1'
            },
            data: [],
            dataTableUrl: baseInfoApi.listJson,
          }
        },
        {
          label: '提交记录',
          immediate: false,
          contents: {
            toolbar: [
              {
								type: 'search',
								reset: true,
								contents: [
									{
										type: 'select',
										label: '回复状态',
										name: 'status',
										placeholder: '请选择',
										clearable: true,
                    sysCode: 'pa_base_reply_status',
										'value-key': 'cciValue',
										'label-key': 'shortName',
										col: 3
									}
								]
              }
            ],
            thead: [
              {
                title: '操作人',
                field: 'createUserName',
                align: 'center'
              },
              {
                title: '操作时间',
                field: 'createTime',
                align: 'center',
              },
              {
                title: '明细数量',
                field: 'achievementIds',
                align: 'center',
                render: (h, { row }) => {
                  return h('span', null, row.achievementIds ? row.achievementIds.split(',').length : '0');
                }
              },
              {
                title: '回复状态',
                field: 'statusTxt',
                align: 'center',
              },
              {
                title: '操作',
                type: 'handle',
                width: 180,
                template: '',
                events: [
                  {
                    code: 'view',
                    text: '查看明细'
                  },
                  {
                    code: 'rollback',
                    text: '撤回',
                    rules: rows => {
                      return rows.status === '1';
                    }
                  },
                  {
                    code: 'revert',
                    text: '查看回复',
                    rules: rows => {
                      return rows.status === '2';
                    }
                  }
                ]
              }
            ],
            pageOption: {
              layout: 'total, sizes, prev, pager, next, jumper',
              pageSize: 10,
              position: 'center',
              current: 1,
              pageNum: 1
            },
            params: {
              orderBy: 't1.create_time',
              asc: 'false',
              isAudit: 0
            },
            dataTableUrl: baseReplyApi.myListJson,
          }
        },
      ]
    }
  },
  data(){
    return {
      activeName: '0',
      tableData: [],
      checked: [],
      selectOption: [],
      showView: false,
      showForm: false,
      formData: {},
      formItemList: [
        {
          label: '回复人',
          name: 'replyUserName',
          placeholder: '无',
          readonly: true,
          col: 12
        },
        {
          label: '回复时间',
          name: 'replyTime',
          placeholder: '无',
          readonly: true,
          col: 12
        },
        {
          type: 'textarea',
          autosize: { minRows: 2, maxRows: 10 },
          label: '回复内容',
          name: 'replyRemark',
          placeholder: '无',
          readonly: true,
          col: 12
        }
      ],
      details: {
        toolbar: [
          {
            type: 'button',
            contents: [
              {
              text: '导出',
              code: 'itemExport',
              type: 'primary'
              }
            ]
          }
        ],
        thead: [
          {
            title: '标题',
            field: 'name',
            align: 'center',
          },
          {
            title: '成果类型',
            field: 'typeTxt',
            align: 'center',
          },
          {
            title: '申报人',
            field: 'createUserName',
            align: 'center',
          },
          {
            title: '申报时间',
            field: 'createTime',
            align: 'center',
          },
          {
            title: '操作',
            type: 'handle',
            width: 100,
            template: '',
            events: [
              {
                code: 'viewInfo',
                text: '查看详情'
              },

            ]
          }
        ],
        data: []
      },
      visibleBasicInfo: false,
      formId: '',
      contentsKey: '',
      exportId: ''
    }
  },
  methods: {
    /**
     * chekbox勾选事件
     */
    electionChange(selection){
      this.selectOption = selection;
    },
    /**
     * headle按钮事件
     */
    async btnClick(res) {
      let code = res.handle.code;
      switch (code) {
        case 'submit':
          if(this.selectOption.length === 0){
            this.$message.error('请选择数据后再操作！');
            return false;
          }
          let params = {
            id: this.$uuidv4(),
            achievementIds: this.selectOption.map(v => v.id).join(',')
          }
          this.submitR(params);
          break;
        case 'rollback':
          this.deleteRowC(res.row.id);
          break;
        case 'revert':
          this.getrecordById(res.row);
          break;
        case 'view':
          this.getViewById(res.row.id);
          break;
        case 'viewInfo':
          if (res.row.type === 'academicPaper') {
            this.contentsKey = 'academicPaper'
          } else if (res.row.type === 'academicPatent') {
            this.contentsKey = 'academicPatent'
          } else if (res.row.type === 'academicWriting') {
            this.contentsKey = 'academicBook'
          } else if (res.row.type === 'awards') {
            this.contentsKey = 'awardAchievement'
          } else if (res.row.type === 'platformTeam') {
            this.contentsKey = 'platformTeam'
          } else if (res.row.type === 'scienceAchievement') {
            this.contentsKey = 'scientificConversionAchievement'
          } else if (res.row.type === 'softwareCopyright') {
            this.contentsKey = 'softwareAchievement'
          } else if (res.row.type === 'technicalProducts') {
            this.contentsKey = 'technicalProduct'
          } else if (res.row.type === 'technicalStandard') {
            this.contentsKey = 'technicalStandard'
          } else {
            this.contentsKey = ''
          }
          if (!this.contentsKey) {
            return;
          }
          this.formId = res.row.id;
          this.visibleBasicInfo = true;
          break;
        case 'itemExport':
          this.itemExportFile();
          break;
        default:
          break;
      }
    },
    //导出函数
    itemExportFile(id) {
      if (!this.exportId) {
        return;
      }
      let isDev = process.env.NODE_ENV === 'development' ? '/api' : '';
      let url = `${isDev}${baseReplyApi.exportItemData}?exportId=${this.exportId}`;
      window.open(url);
    },
    /**
     * 提交操作
     */
    submitR(params){
      this.$confirm(`您确定要提交数据吗？`, '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      })
        .then(() => {
          const loading = this.$loading();
          this.$request({
            url: baseReplyApi.save,
            data: params,
            method: 'POST',
            format: false
          }).then(res => {
            loading.close();
            if (res.rCode == 0) {
              this.$message.success('提交成功');
              this.$refs.table0[0].reload();
            } else {
              this.$message.error(res.msg);
            }
          });
        })
        .catch(err => {});
    },
    /**
     * 撤回提交
     */
    deleteRowC(id){
      this.$confirm(`您确定要撤回数据吗？`, '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      })
        .then(() => {
          const loading = this.$loading();
          this.$request({
            url: baseReplyApi.revocationById + '?id=' + id,
            method: 'POST'
          }).then(res => {
            loading.close();
            if (res.rCode == 0) {
              this.$message.success('撤回成功');
              this.$refs.table1[0].reload();
            } else {
              this.$message.error(res.msg);
            }
          });
        })
        .catch(err => {});
    },
    /**
     * 查看回复弹窗
     */
    getrecordById(row){
      this.formData = Object.assign({}, {
        replyUserName: row.replyUserName,
        replyRemark: row.replyRemark,
        replyTime: row.replyTime
      });
      this.showForm = true;
    },
    /**
     * 查看弹窗
     */
    getViewById(id){
      this.exportId = id;
      this.$request({
        url: baseReplyApi.info,
        method: 'get',
        params: { id }
      }).then(({ results }) => {
        this.details.data = results?.baseInfoVOList || [];
        this.showView = true;
      });
    }
  }
}
</script>

<style scoped lang="scss">
@import '../../../assets/style/style.scss';
.content {
  width: 100%;
  height: 100%;
}

.el-dialog__body {
  .btn-box {
    margin-top: 20px;
    display: flex;
    justify-content: flex-end;

    .btn {
      padding: 5px 10px;
      color: #666;
      border: 1px solid #eee;
      cursor: pointer;
      margin-right: 5px;
      border-radius: 4px;
    }
  }
}

.el-dialog__body {
  .content-warning-Btn {
    float: right;
    margin: 10px 0px;
  }
  .btn-box {
    margin-top: 20px;
    display: flex;
    justify-content: flex-end;

    .btn {
      padding: 5px 10px;
      color: #666;
      border: 1px solid #eee;
      cursor: pointer;
      margin-right: 5px;
      border-radius: 4px;
    }
  }
}
</style>