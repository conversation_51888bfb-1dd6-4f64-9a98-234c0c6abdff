<!--
 @desc:成果管理
 @author: WH
 @date: 2023/11/14
 -->
<template>
	<ProcesPage :key="id" :flow-data-props="{
		appId: formData.appId, // 流程图id
		businessId: id, // 有 businessId-业务id 则为发起节点
		flowTypeCode: wd.basics.flowTypeCode,
		defaultProcessKey: wd.basics.defaultProcessKey,
		isEdit: !formReadonly, // 是否编辑
		isFlow: !formReadonly // 是否显示右边流程功能
	}" @subFun="save" @handleSuccess="handleSuccess">
		<div class="main">
			<div class="basic-info">
				<title-card title="基础信息"></title-card>
				<es-form :key="formReadonly + '1' + formData.academicPaperType + formData.awardType" ref="formRef"
					label-width="190px" :model="formData" :contents="wd.contents" table :submit="false"
					:readonly="formReadonly"></es-form>
			</div>
			<div :key="formReadonly" class="menber-info">
				<title-card :title="titleinfo"></title-card>
				<div class="is-table">
					<div class="form-title">教师信息</div>
					<es-data-table ref="formRef1" :border="true" :height="formReadonly ? 'auto' : teacherHeight"
						:readonly="formReadonly" style="width: 100%" :thead="theadTeacher" :data="formData.teachers"
						@btnClick="btnClick"></es-data-table>
				</div>
				<div class="is-table">
					<div class="form-title">学生信息</div>
					<es-data-table ref="formRef2" :border="true" :height="formReadonly ? 'auto' : studentHeight"
						:readonly="formReadonly" style="width: 100%" :thead="theadStudent" :data="formData.students"
						@btnClick="btnClick"></es-data-table>
				</div>
			</div>
		</div>
		<es-dialog :title="newTitle" :visible.sync="teacherDialog" width="40%" height="60%">
			<es-form :model="teacherForm"
				:contents="newTitle == '新增教师' || newTitle == '编辑教师' ? teacherConfig : studentConfig"
				@submit="teacherSubmit"></es-form>
		</es-dialog>
	</ProcesPage>
</template>

<script>
import ProcesPage from '@/components/process-page.vue';
import { v4 as uuidv4 } from 'uuid';
import titleCard from '@cpt/scientific-sys/title-card.vue';

import { mixinInfo } from './mixinInfo';
export default {
	components: { ProcesPage, titleCard },
	mixins: [mixinInfo],
	props: {
		id: {
			type: String,
			default: ''
		},
		title: {
			type: String,
			default: '查看' // 查看 编辑 新增
		}
	},
	data() {
		return {
			formReadonly: false,
			basicInfo: {},
			rowData: [],
			codesObj: {
				academicPaper: {},
				academicPatent: {},
				academicBook: {},
				platformTeam: {},
				technicalStandard: {},
				technicalProduct: {},
				awardAchievement: {},
				softwareAchievement: {},
				scientificConversionAchievement: {},
				pa_competition: {},
				selectList1: [],
				selectList2: [],
				selectList3: []
			},
			formData: { students: [], teachers: [] },
			row: {},
			teacherForm: {},
			teacherDialog: false,
			manualInput: false, //是否手动输入
			newTitle: '新增教师',
			editIndex: '',
			deptList: [],
			levelList: []
		};
	},
	computed: {
		titleinfo() {
			if (this.contentsKey === 'academicBook' || this.contentsKey === 'academicPaper') {
				return '作者信息';
			} else if (this.contentsKey === 'academicPatent') {
				return '发明人信息';
			} else {
				return '成员信息';
			}
		},
		teacherHeight() {
			let heightList = this.formData?.teachers?.length || 0;
			return heightList * 54 + 53;
		},
		studentHeight() {
			let heightList = this.formData?.students?.length || 0;
			return heightList * 119 + 53;
		},


		teacherAssumeTypeList() {

			if (this.contentsKey === 'academicPaper') {
				return this.codesObj?.academicPaper?.pa_assume_type_teacher_paper || [];
			} else if (this.contentsKey === 'academicPatent') {
				return this.codesObj?.academicPatent?.pa_assume_type_teacher_patent || [];
			} else if (this.contentsKey === 'academicBook') {
				return this.codesObj?.academicBook?.pa_assume_type_teacher_writing || [];
			} else if (this.contentsKey === 'platformTeam') {
				return this.codesObj?.platformTeam?.pa_assume_type_teacher_platform_team || [];
			} else if (this.contentsKey === 'technicalStandard') {
				return this.codesObj?.technicalStandard?.pa_assume_type_teacher_technical_standard || [];
			} else if (this.contentsKey === 'technicalProduct') {
				return this.codesObj?.technicalProduct?.pa_assume_type_teacher_technical_products || [];
			} else if (this.contentsKey === 'awardAchievement') {
				return this.codesObj?.awardAchievement?.pa_assume_type_teacher_awards || [];
			} else if (this.contentsKey === 'softwareAchievement') {
				return this.codesObj?.softwareAchievement?.pa_assume_type_teacher_software_copyright || [];
			} else if (this.contentsKey === 'scientificConversionAchievement') {
				return (
					this.codesObj?.scientificConversionAchievement
						?.pa_assume_type_teacher_science_achievement || []
				);
			} else if (this.contentsKey === 'pa_competition') {
				return this.codesObj?.pa_competition?.pa_assume_type_teacher_competition || [];
			} else {
				return [];
			}
		},
		teacherConfig() {
			return [
				{
					label: '人员类型',
					name: 'memberType',
					type: 'select',
					data: this.codesObj.pa_member_type,
					'value-key': 'cciValue',
					'label-key': 'shortName',
					rules: {
						required: !this.formReadonly,
						message: '请选择',
						trigger: 'blur'
					},
					events: {
						change: (res, val, index) => {
							console.log(val);
							this.manualInput = val == 2 ? true : false;
							// Object.keys(this.teacherForm).forEach(key => {
							// 	if (key !== 'memberType') this.teacherForm[key] = null;
							// });
						}
					},
					filterable: true
				},
				{
					label: '姓名',
					// name: this.formReadonly ? 'memberName' : 'member',
					name: 'memberName',
					type: 'text',
					placeholder: '请选择',
					multiple: false,
					hide: !(this.teacherForm.memberType == 2),
					filterable: true,
					rules: {
						required: !this.formReadonly,
						message: '请选择',
						trigger: 'blur'
					}
				},
				{
					label: '姓名',
					// name: this.formReadonly ? 'memberName' : 'member',
					name: 'memberName',
					type: 'selector',
					types: ['employee', 'otheremployee'],
					placeholder: '请选择',
					multiple: false,
					hide: this.teacherForm.memberType == 2,
					filterable: true,
					rules: {
						required: !this.formReadonly,
						message: '请选择',
						trigger: 'blur'
					},
					events: {
						confirm: val => {
							const obj = JSON.parse(val[0].attr);
							this.$set(this.teacherForm, 'memberNum', obj.outcode);
							this.$set(this.teacherForm, 'memberName', obj.username);
							this.$set(this.teacherForm, 'member', obj.username);
							this.$set(this.teacherForm, 'deptName', obj.orgName);
							this.$set(this.teacherForm, 'deptId', obj.orgId);
							this.$set(this.teacherForm, 'professional', obj.postName);
							this.$set(this.teacherForm, 'memberPhone', obj.phone);
						}
					}
				},
				{
					label: '教工号',
					name: 'memberNum',
					width: 200,
					type: 'text',
					rules: {
						required: !this.formReadonly & !this.manualInput,
						message: '请选择',
						trigger: 'blur'
					},
					filterable: true
				},
				{
					label: '部门/学院/单位',
					width: 600,
					name: 'deptName',
					type: 'text',
					rules: {
						required: !this.manualInput,
						message: '请选择',
						trigger: 'blur'
					}
				},
				{
					label: '职称',
					name: 'professional',
					width: 150,
					type: 'text',
					rules: {
						required: !this.formReadonly,
						message: '请选择',
						trigger: 'blur'
					}
				},
				{
					label: '联系电话',
					name: 'memberPhone',
					type: 'text',
					rules: {
						required: !this.formReadonly,
						message: '请选择',
						trigger: 'blur'
					},

				},
				{
					label: '承担类型',
					name: 'assumeType',
					'min-width': 150,
					type: 'select',
					rules: {
						required: !this.formReadonly,
						message: '请选择',
						trigger: 'blur'
					},
					data: this.teacherAssumeTypeList,
					rules: {
						required: !this.formReadonly,
						message: '请选择',
						trigger: 'blur'
					},
					// data: this.teacherAssumeTypeList,
					// data: [
					// 	{ cciValue: '0', shortName: '主研（负责人）' },
					// 	{ cciValue: '1', shortName: '主研' },
					// 	{ cciValue: '2', shortName: '参研' }
					// ],
					'label-key': 'shortName',
					'value-key': 'cciValue',
					filterable: true
				}
			];
		},
		theadTeacher() {
			return [
				{
					align: 'center',
					label: '人员类型',
					field: 'memberType',
					type: 'select',
					data: this.codesObj.pa_member_type,
					'value-key': 'cciValue',
					'label-key': 'shortName',
					// width: 150,
					rules: {
						required: !this.formReadonly,
						message: '请选择',
						trigger: 'blur'
					},
					filterable: true
				},
				{
					align: 'center',
					label: '姓名',
					// field: this.formReadonly ? 'memberName' : 'member',
					field: 'memberName',
					type: 'selector',
					types: ['employee', 'otheremployee'],
					placeholder: '请选择',
					// width: 180,
					multiple: false,
					filterable: true,
					rules: {
						required: !this.formReadonly,
						message: '请选择',
						trigger: 'blur'
					},
					events: {
						change: ({ data, item, name, value }) => {
							const obj = {
								...value[0],
								...JSON.parse(value[0]?.attr || '{}')
							};
							this.$set(data, 'memberNum', obj.outcode);
							this.$set(data, 'memberName', obj.username);
							this.$set(data, 'deptName', obj.orgName);
							this.$set(data, 'deptId', obj.orgId);
							this.$set(data, 'professional', obj.postName);
							this.$set(data, 'memberPhone', obj.phone);
						},
						confirm: e => { }
					}
				},
				{
					align: 'center',
					label: '教工号',
					field: 'memberNum',
					// width: 200,
					type: 'text',
					// rules: {
					// 	required: !this.formReadonly,
					// 	message: '请选择',
					// 	trigger: 'blur'
					// },
					rules: {
						required: !this.formReadonly & !this.manualInput,
						message: '请选择',
						trigger: 'blur'
					},
					filterable: true
				},
				{
					align: 'center',
					label: '部门/学院/单位',
					field: 'deptName',
					// width: 600,
					type: 'text',
					rules: {
						required: !this.formReadonly,
						message: '请选择',
						trigger: 'blur'
					}
				},
				{
					align: 'center',
					label: '职称',
					// width: 150,
					field: 'professional',
					type: 'text',
					rules: {
						required: !this.formReadonly,
						message: '请选择',
						trigger: 'blur'
					}
				},
				{
					align: 'center',
					label: '联系电话',
					// width: 200,
					field: 'memberPhone',
					type: 'text',
					rules: {
						required: !this.formReadonly,
						message: '请选择',
						trigger: 'blur'
					}
				},
				{
					label: '承担类型',
					align: 'center',
					// 'min-width': 150,
					field: 'assumeType',
					type: 'select',
					rules: {
						required: !this.formReadonly,
						message: '请选择',
						trigger: 'blur'
					},
					data: this.teacherAssumeTypeList,
					// data: [
					// 	{ cciValue: '0', shortName: '主研（负责人）' },
					// 	{ cciValue: '1', shortName: '主研' },
					// 	{ cciValue: '2', shortName: '参研' }
					// ],
					'label-key': 'shortName',
					'value-key': 'cciValue',
					filterable: true
				},
				{
					title: '操作',
					type: 'handle',
					align: 'center',
					fixed: 'right',
					label: '操作',
					// width: 100,
					hide: this.formReadonly,
					template: '',
					renderHeader: (h, params) => {
						return h('div', [
							h('span', {}, '操作'),
							h('el-button', {
								props: {
									type: 'text',
									icon: 'el-icon-circle-plus-outline'
								},
								on: {
									click: () => {
										this.teacherForm = {};
										this.newTitle = '新增教师';
										this.teacherDialog = true;
										// this.$set(this.formData.teachers, this.formData.teachers.length, {
										// 	id: uuidv4(),
										// 	memberType: '',
										// 	member: [],
										// 	memberNum: '',
										// 	deptName: '',
										// 	deptId: '',
										// 	memberPhone: ''
										// });
									}
								}
							})
						]);
					},
					events: [
						{
							text: '删除',
							size: 'mini',
							type: 'text',
							icon: 'es-icon-shanchu',
							event: ({ $index, row }) => {
								const teachers = this.formData.teachers.filter(item => item.id !== row.id);
								this.$set(this.formData, 'teachers', teachers);
							}
						},
						{
							text: '编辑',
							size: 'mini',
							type: 'text',
							icon: 'es-icon-bianji',
							event: ({ $index, row }) => {
								this.newTitle = '编辑教师';
								this.editIndex = $index;
								this.teacherForm = JSON.parse(JSON.stringify(row));
								this.teacherDialog = true;
							}
						}
					]
				}
			];
		},
		studentConfig() {
			return [
				{
					label: '人员类型',
					name: 'memberType',
					type: 'select',
					data: this.codesObj.pa_member_type,
					'value-key': 'cciValue',
					'label-key': 'shortName',
					rules: {
						required: !this.formReadonly,
						message: '请选择',
						trigger: 'blur'
					},
					events: {
						change: (res, val, index) => {
							this.manualInput = val == 2 ? true : false;
							// Object.keys(this.teacherForm).forEach(key => {
							// 	if (key !== 'memberType' && key !== 'assumeType') this.teacherForm[key] = null;
							// });
						}
					},
					filterable: true
				},
				{
					label: '姓名',
					// name: this.formReadonly ? 'memberName' : 'member',
					name: 'memberName',
					type: 'text',
					placeholder: '请选择',
					multiple: false,
					hide: !(this.teacherForm.memberType == 2),
					filterable: true,
					rules: {
						required: !this.formReadonly,
						message: '请选择',
						trigger: 'blur'
					}
				},
				{
					label: '姓名',
					// name: this.formReadonly ? 'memberName' : 'member',
					name: 'memberName',
					type: 'selector',
					types: ['employee', 'otheremployee'],
					placeholder: '请选择',
					multiple: false,
					hide: this.teacherForm.memberType == 2,
					filterable: true,
					rules: {
						required: !this.formReadonly,
						message: '请选择',
						trigger: 'blur'
					},
					events: {
						confirm: val => {
							const obj = JSON.parse(val[0].attr);
							this.$set(this.teacherForm, 'memberNum', obj.outcode);
							this.$set(this.teacherForm, 'memberName', obj.username);
							this.$set(this.teacherForm, 'member', obj.username);
							this.$set(this.teacherForm, 'deptName', obj.orgName);
							this.$set(this.teacherForm, 'deptId', obj.orgId);
							this.$set(this.teacherForm, 'professional', obj.postName);
							this.$set(this.teacherForm, 'memberPhone', obj.phone);
						}
					}
				},
				{
					label: '学号',
					name: 'memberNum',
					width: 200,
					type: 'text',
					rules: {
						required: !this.formReadonly & !this.manualInput,
						message: '请填写',
						trigger: 'blur'
					}
				},
				{
					label: '部门/学院',
					name: 'deptName',
					width: 400,
					type: 'text',
					rules: {
						required: !this.formReadonly,
						message: '请选择',
						trigger: 'blur'
					}
				},
				{
					label: '联系电话',
					name: 'memberPhone',
					type: 'text',
					rules: {
						required: !this.formReadonly,
						message: '请选择',
						trigger: 'blur'
					},
					filterable: true
				},
				{
					label: '承担类型',
					name: 'assumeType',
					type: 'select',
					// readonly: true,
					// data: this.codesObj.pa_assume_type_student,
					data: [
						// { cciValue: '0', shortName: '主研（负责人）' },
						// { cciValue: '1', shortName: '主研' },
						{ cciValue: '2', shortName: '参研' }
					],
					rules: {
						required: !this.formReadonly,
						message: '请选择',
						trigger: 'blur'
					},
					'value-key': 'cciValue',
					'label-key': 'shortName',
					filterable: true,
					events: {
						change: (res, val, index) => {
							console.log(res, val, index);
						}
					}
				}
			];
		},
		theadStudent() {
			return [
				{
					align: 'center',
					label: '人员类型',
					field: 'memberType',
					type: 'select',
					rules: {
						required: !this.formReadonly,
						message: '请选择',
						trigger: 'blur'
					},
					data: this.codesObj.pa_member_type,
					'value-key': 'cciValue',
					'label-key': 'shortName',
					filterable: true
				},
				{
					align: 'center',
					label: '姓名',
					// field: this.formReadonly ? 'memberName' : 'member',
					field: 'memberName',
					type: 'selector',
					types: ['employee', 'otheremployee'],
					placeholder: '请选择',
					// width: 180,
					multiple: false,
					filterable: true,
					rules: {
						required: !this.formReadonly,
						message: '请选择',
						trigger: 'blur'
					},
					events: {
						change: ({ data, item, name, value }) => {
							const obj = {
								...value[0],
								...JSON.parse(value[0]?.attr || '{}')
							};
							this.$set(data, 'memberNum', obj.outcode);
							this.$set(data, 'memberName', obj.username);
							this.$set(data, 'deptName', obj.orgName);
							this.$set(data, 'deptId', obj.orgId);
							this.$set(data, 'memberPhone', obj.phone);
						},
						confirm: e => { }
					}
				},
				{
					align: 'center',
					label: '学号',
					field: 'memberNum',
					// width: 200,
					type: 'text',
					rules: {
						required: !this.formReadonly & !this.manualInput,
						message: '请填写',
						trigger: 'blur'
					}
				},
				{
					align: 'center',
					label: '部门/学院',
					field: 'deptName',
					type: 'text',
					rules: {
						required: !this.formReadonly,
						message: '请选择',
						trigger: 'blur'
					}
				},
				{
					align: 'center',
					label: '联系电话',
					field: 'memberPhone',
					type: 'text',
					rules: {
						required: !this.formReadonly,
						message: '请选择',
						trigger: 'blur'
					},
					filterable: true
				},
				{
					label: '承担类型',
					align: 'center',
					field: 'assumeType',
					// width: 200,
					type: 'select',
					readonly: true,
					// data: this.codesObj.pa_assume_type_student,
					data: [
						// { cciValue: '0', shortName: '主研（负责人）' },
						// { cciValue: '1', shortName: '主研' },
						{ cciValue: '2', shortName: '参研' }
					],
					rules: {
						required: !this.formReadonly,
						message: '请选择',
						trigger: 'blur'
					},
					'value-key': 'cciValue',
					'label-key': 'shortName',
					filterable: true
				},

				{
					align: 'right',
					label: '证明材料（学生证）',
					field: 'studentFile',
					rules: {
						required: !this.formReadonly,
						message: '请选择',
						trigger: 'blur'
					},
					filterable: true,
					render: (h, params) => {
						const fn = h('es-upload', {
							class: 'upload-box',
							attrs: {
								preview: true,
								showInfo: [],
								code: 'pro_member_student_att',
								ownId: params.row.id, // 业务id
								readonly: this.formReadonly,
								'list-height': this.formReadonly ? '' : '70px',
								fileList: params.row.studentFile,
								onChange: (res, file) => {
									params.row.studentFile = file;
								}
							}
						});
						return fn;
					}
				},
				{
					title: '操作',
					type: 'handle',
					align: 'center',
					fixed: 'right',
					label: '操作',
					hide: this.formReadonly,
					// width: 100,
					renderHeader: (h, params) => {
						return h('div', [
							h('span', {}, '操作'),
							h('el-button', {
								props: {
									type: 'text',
									icon: 'el-icon-circle-plus-outline'
								},
								on: {
									click: () => {
										this.newTitle = '新增学生';
										this.teacherDialog = true;
										// this.teacherForm.assumeType = '1';
										// this.$set(this.formData.students, this.formData.students.length, {
										// 	id: uuidv4(),
										// 	memberType: '',
										// 	member: [],
										// 	memberNum: '',
										// 	deptName: '',
										// 	deptId: '',
										// 	memberPhone: '',
										// 	assumeType: '1',
										// 	studentFile: []
										// });
									}
								}
							})
						]);
					},
					events: [
						{
							text: '删除',
							size: 'mini',
							type: 'text',
							icon: 'es-icon-shanchu',
							event: ({ $index, row }) => {
								const students = this.formData.students.filter(item => item.id !== row.id);
								this.$set(this.formData, 'students', students);
							}
						},
						{
							text: '编辑',
							size: 'mini',
							type: 'text',
							icon: 'es-icon-bianji',
							event: ({ $index, row }) => {
								this.newTitle = '编辑学生';
								this.editIndex = $index;
								this.teacherForm = JSON.parse(JSON.stringify(row));
								this.teacherDialog = true;
							}
						}
					]
				}
			];
		}
	},
	watch: {
		id: {
			handler(newVal, oldVal) {
				if (newVal) {
					this.getPorjectInfo();
				}
			},
			immediate: true
		}
	},
	created() {
		this.getSysCode();
		this.getDiscipline('', '1'); // 获取一级学科
		// 获取部门数据
		this.getDept();
		this.getLevel();
	},
	methods: {
		getLevel() {
			this.$request({
				url: '/ybzy/projectMultilevelDict/selectDictByType?type=projectPaperLevel',
				method: 'get'
			}).then(res => {
				this.levelList = res.results;
			});
		},
		getDept() {
			this.$request({
				url: '/ybzy/sys/common/getOrgList',
				method: 'post'
			}).then(res => {
				this.deptList = res.results;
			});
		},

		teacherSubmit() {
			this.teacherForm.id = uuidv4();
			const isEditing = this.newTitle.includes('编辑');
			const isTeacher = this.newTitle.includes('教师');

			let { memberPhone } = this.teacherForm
			const regex = /^1[3-9]\d{9}$/;
			let PhoneVerify = regex.test(memberPhone);
			if (!PhoneVerify) {
				this.$message.error('请输入正确的联系电话');
				return
			}

			// 处理编辑情况
			if (isEditing) {
				const targetList = isTeacher ? this.formData.teachers : this.formData.students;
				this.$set(targetList, this.editIndex, this.teacherForm);
			} else {
				this.teacherForm.deptId = this.teacherForm.deptId ? this.teacherForm.deptId : '';
				this.teacherForm.memberName = this.teacherForm.member
					? this.teacherForm.member
					: this.teacherForm.memberName;
				// 处理新增情况
				this.formData.teachers = this.formData.teachers || [];
				this.formData.students = this.formData.students || [];
				const targetList = isTeacher ? this.formData.teachers : this.formData.students;

				targetList.push(this.teacherForm);
			}

			// 重置表单和对话框状态
			this.teacherForm = {};
			this.teacherDialog = false;
		},
		// 批量请求数据字典
		getSysCode() {

			const codes =
				'pa_member_type,pa_assume_type_student,academic_type_code,pa_common_yes_no,member_rank';
			this.$utils.findSysCodeList(codes).then(res => {
				this.codesObj = { ...this.codesObj, ...res };
			});
			if (this.contentsKey === 'academicPaper') {
				const codes =
					'pa_assume_type_teacher_paper,achievement_form,achievement_source,pa_publish_scope,pa_research_category,pa_thesis_level,pa_subject_type,pa_retrieval_system';
				this.$utils.findSysCodeList(codes).then(res => {
					this.$set(this.codesObj, 'academicPaper', res);
				});
			}
			if (this.contentsKey === 'academicPatent') {
				const codes = 'patent_ipr_type,pa_assume_type_teacher_patent,patent_type,patent_ca_rank';
				this.$utils.findSysCodeList(codes).then(res => {
					this.$set(this.codesObj, 'academicPatent', res);
				});
			}
			if (this.contentsKey === 'academicBook') {
				const codes =
					'pa_assume_type_teacher_writing,writing_category,writing_type,writing_author_sort,publishing_place';
				this.$utils.findSysCodeList(codes).then(res => {
					this.$set(this.codesObj, 'academicBook', res);
				});
			}
			if (this.contentsKey === 'platformTeam') {
				const codes = 'pa_assume_type_teacher_platform_team,team_level';
				this.$utils.findSysCodeList(codes).then(res => {
					this.$set(this.codesObj, 'platformTeam', res);
				});
			}
			if (this.contentsKey === 'technicalStandard') {
				const codes = 'pa_assume_type_teacher_technical_standard,standard_type';
				this.$utils.findSysCodeList(codes).then(res => {
					this.$set(this.codesObj, 'technicalStandard', res);
				});
			}
			if (this.contentsKey === 'technicalProduct') {
				const codes = 'pa_assume_type_teacher_technical_products';
				this.$utils.findSysCodeList(codes).then(res => {
					this.$set(this.codesObj, 'technicalProduct', res);
				});
			}
			if (this.contentsKey === 'awardAchievement') {
				const codes =
					'pa_assume_type_teacher_awards,reward_category,award_unit_sort,award_level,school_rank,award_prizewinner_rank,award_personal_rank';
				this.$utils.findSysCodeList(codes).then(res => {
					this.$set(this.codesObj, 'awardAchievement', res);
				});
			}
			if (this.contentsKey === 'softwareAchievement') {
				const codes = 'pa_assume_type_teacher_software_copyright';
				this.$utils.findSysCodeList(codes).then(res => {
					this.$set(this.codesObj, 'softwareAchievement', res);
				});
			}
			if (this.contentsKey === 'scientificConversionAchievement') {
				const codes =
					'pa_assume_type_teacher_science_achievement,science_achievement_type,pa_change_way,pa_transferee_type';
				this.$utils.findSysCodeList(codes).then(res => {
					this.$set(this.codesObj, 'scientificConversionAchievement', res);
				});
			}
			if (this.contentsKey === 'pa_competition') {
				const codes =
					'pa_assume_type_teacher_competition,competition_type,competition_level,competition_grade';
				this.$utils.findSysCodeList(codes).then(res => {
					this.$set(this.codesObj, 'pa_competition', res);
				});
			}
		},
		// 请求学科
		getDiscipline(parent = '', level = '') {
			//type 类型  parent 父级编码  level 层级
			this.$request({
				url: '/ybzy/projectMultilevelDict/selectList',
				params: { type: 'discipline', parent, level }
				// method: 'POST'
			}).then(res => {
				if (res.rCode == 0) {
					const arr = res.results;
					switch (level) {
						case '1':
							this.$set(this.codesObj, 'selectList1', arr);
							break;
						case '2':
							this.$set(this.codesObj, 'selectList2', arr);
							break;
						case '3':
							this.$set(this.codesObj, 'selectList3', arr);
							break;
						default:
							break;
					}
				} else {
					this.$message.error(res.msg);
				}
			});
		},
		// 是否只读和编辑
		toolFormReadonly() {
			this.$nextTick(() => {
				const isEdit = window.location.href.includes('isEdit'); // 判断是否流程页面的编辑
				this.formReadonly = this.title.includes('查看') && !isEdit ? true : false;
			});
		},
		//表格修改弹窗操作
		btnClick({ handle, row }) {
			let { text, btnType } = handle;
			this.row = { ...row };
		},
		// 表格行高
		tableRowClassName({ row, rowIndex }) {
			let styleRes = {
				height: '54px !important'
			};
			return styleRes;
		},
		//修改保存
		async save(callBank) {
			if (!callBank) {
				const loading = this.load('提交中...');
				const _students = [];

				if (Array.isArray(this.formData.students)) {
					this.formData.students.forEach((item, index) => {
						const _item = JSON.parse(JSON.stringify(item));
						_item.sortNum = index + 1;
						delete _item.studentFile;
						delete _item.member;
						_students.push(_item);
					});
				}
				const _teachers = [];
				this.formData.teachers.forEach((item, index) => {
					const _item = JSON.parse(JSON.stringify(item));
					_item.sortNum = index + 1;
					delete _item.member;
					_teachers.push(_item);
				});
				let formData = {
					...this.formData,
					students: _students,
					teachers: _teachers
				};
				if (formData.thesisLevel) {
					formData.thesisLevel = formData.thesisLevel.join(',');
				}
				delete formData.fj1;
				if (formData.periodicalPublishDate) {
					formData.periodicalPublishDate = formData.periodicalPublishDate + ' 00:00:00';
				}
				if (formData.declarationDate) {
					formData.declarationDate = formData.declarationDate + ' 00:00:00';
				}
				if (formData.apDate) {
					formData.apDate = formData.apDate + ' 00:00:00';
				}
				if (formData.writingPublishingDate) {
					formData.writingPublishingDate = formData.writingPublishingDate + ' 00:00:00';
				}
				if (formData.projectApprovalDate) {
					formData.projectApprovalDate = formData.projectApprovalDate + ' 00:00:00';
				}
				if (formData.passCheckDate) {
					formData.passCheckDate = formData.passCheckDate + ' 00:00:00';
				}
				if (formData.enactDate) {
					formData.enactDate = formData.enactDate + ' 00:00:00';
				}
				if (formData.affirmDate) {
					formData.affirmDate = formData.affirmDate + ' 00:00:00';
				}
				if (formData.acquisitionTime) {
					formData.acquisitionTime = formData.acquisitionTime + ' 00:00:00';
				}
				if (formData.accomplishDate) {
					formData.accomplishDate = formData.accomplishDate + ' 00:00:00';
				}
				if (formData.oneDate) {
					formData.oneDate = formData.oneDate + ' 00:00:00';
				}
				if (formData.registerDate) {
					formData.registerDate = formData.registerDate + ' 00:00:00';
				}
				this.$.ajax({
					url: this.title.includes('新增') ? this.wd.basics.save : this.wd.basics.edit,
					method: 'post',
					data: formData,
					format: false
				}).then(res => {
					loading.close();
					if (res.rCode == 0) {
						if (callBank) {
							callBank();
						} else {
							this.$message.success('暂存成功');
							this.$emit('visible', true);
							this.$emit('update:visible', true);
							// this.handleSuccess();
						}
					} else {
						this.$message.warning(res.msg);
					}
				});
				return;
			}
			this.$refs.formRef.validate(valid => {
				// this.$refs.formRef1.validate(valid1 => {
				// console.log(124);
				// this.$refs.formRef2.validate(valid2 => {
				// console.log(126);
				//  && valid1 && valid2
				if (valid) {
					if (!this.formData.teachers.length) {
						this.$message.warning('必须有一条教师信息');
						return;
					}
					const loading = this.load('提交中...');
					const _students = [];

					if (Array.isArray(this.formData.students)) {
						this.formData.students.forEach((item, index) => {
							const _item = JSON.parse(JSON.stringify(item));
							_item.sortNum = index + 1;
							delete _item.studentFile;
							delete _item.member;
							_students.push(_item);
						});
					}
					const _teachers = [];
					this.formData.teachers.forEach((item, index) => {
						const _item = JSON.parse(JSON.stringify(item));
						_item.sortNum = index + 1;
						delete _item.member;
						_teachers.push(_item);
					});
					let formData = {
						...this.formData,
						students: _students,
						teachers: _teachers
					};
					if (formData.thesisLevel) {
						formData.thesisLevel = formData.thesisLevel.join(',');
					}
					delete formData.fj1;
					if (formData.periodicalPublishDate) {
						formData.periodicalPublishDate = formData.periodicalPublishDate + ' 00:00:00';
					}
					if (formData.declarationDate) {
						formData.declarationDate = formData.declarationDate + ' 00:00:00';
					}
					if (formData.apDate) {
						formData.apDate = formData.apDate + ' 00:00:00';
					}
					if (formData.writingPublishingDate) {
						formData.writingPublishingDate = formData.writingPublishingDate + ' 00:00:00';
					}
					if (formData.projectApprovalDate) {
						formData.projectApprovalDate = formData.projectApprovalDate + ' 00:00:00';
					}
					if (formData.passCheckDate) {
						formData.passCheckDate = formData.passCheckDate + ' 00:00:00';
					}
					if (formData.enactDate) {
						formData.enactDate = formData.enactDate + ' 00:00:00';
					}
					if (formData.affirmDate) {
						formData.affirmDate = formData.affirmDate + ' 00:00:00';
					}
					if (formData.acquisitionTime) {
						formData.acquisitionTime = formData.acquisitionTime + ' 00:00:00';
					}
					if (formData.accomplishDate) {
						formData.accomplishDate = formData.accomplishDate + ' 00:00:00';
					}
					if (formData.oneDate) {
						formData.oneDate = formData.oneDate + ' 00:00:00';
					}
					if (formData.registerDate) {
						formData.registerDate = formData.registerDate + ' 00:00:00';
					}
					this.$.ajax({
						url: this.title.includes('新增') ? this.wd.basics.save : this.wd.basics.edit,
						method: 'post',
						data: formData,
						format: false
					}).then(res => {
						loading.close();
						if (res.rCode == 0) {
							if (callBank) {
								callBank();
							} else {
								this.$message.success(res.msg);
								this.handleSuccess();
							}
						} else {
							this.$message.warning(res.msg);
						}
					});
				} else {
					this.$message.warning('请先完成必填项！');
					return false;
				}
				// });
				// });
			});
		},
		// 提交成功
		handleSuccess(e) {
			//pendingId则为流程的审核页面，否则是弹窗的流程
			const isFlow = window.location.href.includes('pendingId');
			if (isFlow) {
				window.close();
			} else {
				this.$emit('visible', false);
				this.$emit('update:visible', false);
			}
		},

		// 页面加载
		load(text) {
			return this.$.loading(this.$loading, text);
		},

		getInitTableFlied() {
			const contentsKey = this.contentsKey;
			let Keys;
			switch (contentsKey) {
				// 学术论文
				case 'academicPaper':
					Keys = 'firstAuthor';
					break;
				// 学术专利
				case 'academicPatent':
					Keys = 'declarant';
					break;
				// 学术著作
				case 'academicBook':
					Keys = 'writingOneEditor';
					break;
				// 平台团队
				case 'platformTeam':
					Keys = 'principalUserName';
					break;
				// 技术产品
				case 'technicalProduct':
					Keys = 'applyUserName';
					break;
				// 科技转化成果
				case 'scientificConversionAchievement':
					Keys = 'principal';
					break;
				// 软著
				case 'softwareAchievement':
					Keys = 'authorityUser';
					break;
				// 技术标准
				case 'technicalStandard':
					Keys = 'principalUserName';
					break;
				// 获奖成果 分社科、自然两种类型
				case 'awardAchievement':
					Keys = 'prizewinnerName';
					break;
			}
			return Keys;
		},

		// 初始化教师数据
		InitTeacherInfo() {
			const assumeType = this.teacherAssumeTypeList[0].cciValue;
			const memberType = (this.codesObj?.academicPaper?.pa_member_type?.[0].cciValue ||
				this.codesObj?.pa_member_type?.[0].cciValue ||
				null);
			const tableField = this.getInitTableFlied(); // 获取键名

			// this.$set(this.teacherForm, 'memberType', 2);
			let { deptName, code, name, orgName, phone } = JSON.parse(localStorage.getItem('loginUserInfo') || '{}');
			this.$set(this.formData, tableField, name);

			// 设置教师信息
			this.$set(this.formData.teachers, 0, {
				memberType,
				memberName: name, // 使用动态键名
				memberNum: code,
				professional: deptName,
				deptName: orgName,
				memberPhone: phone,
				assumeType
			});
		},

		async getPorjectInfo() {
			// 等待获取数据字典
			await this.getSysCode();

			if (this.title.includes('新增')) {
				this.formData = {
					id: this.id,
					students: [],
					teachers: []
				};
				this.InitTeacherInfo()

				if (this.contentsKey === 'academicPaper') {

					this.$set(this.formData, 'academicPaperType', '1');
					this.$set(this.formData, 'isForeignPeriodical', '0');
					this.$set(this.formData, 'isUnitAdopt', '0');
					this.$set(this.formData, 'isForeignLanguage', '0');
					this.$set(this.formData, 'studentIsFirst', '0');

				}
				if (this.contentsKey === 'awardAchievement') {
					this.$set(this.formData, 'awardType', '1');
				}
				this.toolFormReadonly();
				return;
			}
			const loading = this.load('加载中...');
			try {
				let { rCode, msg, results } = await this.$.ajax({
					url: this.wd.basics.info,
					method: 'get',
					params: { id: this.id }
				});
				if (rCode == 0) {
					let obj = results;
					this.setTableData(obj || {});
					this.toolFormReadonly();

				} else {
					this.$message.error(msg);
				}
			} finally {
				loading.close();
			}
		},
		setTableData(obj) {
			let formData = {
				...obj,
				teachers: obj.teachers?.map(e => {
					e.member = [
						{
							outcode: e.memberNum,
							username: e.memberName,
							showname: e.memberName,
							orgName: e.deptName,
							orgId: e.deptId,
							postName: e.professional,
							phone: e.memberPhone
						}
					];
					return e;
				}),
				students: obj.students?.map(e => {
					e.member = [
						{
							outcode: e.memberNum,
							username: e.memberName,
							showname: e.memberName,
							orgName: e.deptName,
							orgId: e.deptId,
							phone: e.memberPhone
						}
					];
					return e;
				})
			};
			this.formData = formData;
			this.getDiscipline(formData.firstLevelDiscipline, '2'); // 获取二级学科
		}
	}
};
</script>

<style lang="scss" scoped>
@import '@ast/style/public.scss';

.main {
	// @include flexBox();
	// flex-direction: column;
	height: 100%;
	overflow: auto;
	padding: 0 8px 60px;

	.basic-info {
		flex: 1;
		// height: 400px;
		width: 100%;
		overflow: auto;
	}

	.menber-info {
		margin-bottom: 30px;
		width: 100%;

		.form-title {
			background-color: #f8f8f8;
			// border-color: #e1e1e1;
			border: 1px solid #e1e1e1;
			border-bottom: 0;
			font-size: 14px;
			font-weight: bold;
			color: #747474;
			display: flex;
			align-items: center;
			justify-content: center;
			height: 40px;
			width: 100%;
		}

		.is-table {
			width: 100%;
			margin-bottom: 15px;
		}
	}
}

::v-deep .upload-box {
	width: 100%;
	display: flex;
	flex-direction: column;
	align-items: center;
	justify-content: center;
	.el-scrollbar{
		width: 100%;
	}

	.el-button--medium {
		padding: 2px 6px;
		font-size: 12px;
	}

	.el-upload-list {

		// margin-top: -15px !important;
		.el-upload-list__item-name {
			width: auto;
			top: 0px;
			text-align: center;
			height: 37.7px;
			line-height: 39.7px;
		}
	}

	table {
		border-collapse: collapse;
		/* 合并单元格边框，避免双线 */
		border-spacing: 0;
		/* 当 border-collapse 不起作用时，可以尝试这个 */
	}

	table,
	th,
	td {
		border: none;
		/* 移除表格、表头和单元格的边框 */
	}
}

::v-deep .es-form-content {
	padding: 0 !important;
}

::v-deep .es-table-form {
	width: 100%;

	.es-table-form-label {
		text-align: right;
		color: #747474;
		font-weight: 550;
	}
}

::v-deep .cell {
	color: #747474;
}
</style>
