<template>
	<div class="main-container">
		<i v-if="isMain" class="home-icon es-icon-home" @click="onHome"></i>
		<es-main
			scene="subsystem"
			:theme="theme"
			:logo="logoBj.manageLogo || logo"
			:header-image="headerImage"
			:main-logo="logoBj.manageLogo || logo"
			:hides="{ home: true }"
			:show-notify="false"
		></es-main>
		<!-- :data='data'
		:remote="false" -->
		<!-- 编辑个人资料 -->
		<EditProfile ref="refEditProfile" />
	</div>
</template>
<script>
import { mapState } from 'vuex';
import EditProfile from '@/components/EditProfile';
export default {
	components: {
		EditProfile
	},
	data() {
		return {
			// headerImage: require('@/assets/image/head.png'),
			headerImage: '',
			logo: require('@/assets/image/logo.png'),
			mainLeftImg: require('@/assets/image/menu.png'),

			data: [
				{
					id: '1',
					text: '系统管理',
					icons: '',
					fourthTabs: [],
					children: [
						{
							id: '1-13',
							text: '中期检查',
							icons: '',
							fourthTabs: [],
							children: [],
							url: '#/interimCheck'
						},
						{
							id: '1-12',
							text: '归档项目',
							icons: '',
							fourthTabs: [],
							children: [],
							url: '#/archive'
						},
						{
							id: '1-11',
							text: '项目验收',
							icons: '',
							fourthTabs: [],
							children: [],
							url: '#/finalAcceptance'
						},
						{
							id: '1-10',
							text: '项目清单',
							icons: '',
							fourthTabs: [],
							children: [],
							url: '#/approvalList'
						},
						/**fhh开发start*/
						{
							id: '10-1',
							text: '考勤管理人事',
							icons: '',
							fourthTabs: [],
							children: [],
							url: '#/sys/attendance-management/personnel'
						},
						{
							id: '10-2',
							text: '考勤管理教师',
							icons: '',
							fourthTabs: [],
							children: [],
							url: '#/sys/attendance-management/teacher'
						},
						{
							id: '10-3',
							text: '退休人员管理系统',
							icons: '',
							fourthTabs: [],
							children: [],
							url: '#/sys/retiree-home-teacher'
						},
						{
							id: '10-4',
							text: '社团管理系统-学生',
							icons: '',
							fourthTabs: [],
							children: [],
							url: '#/sys/club-home-student'
						},
						{
							id: '10-5',
							text: '社团管理系统-教职工',
							icons: '',
							fourthTabs: [],
							children: [],
							url: '#/sys/club-home-teacher'
						},
						{
							id: '10-6',
							text: '工会管理',
							icons: '',
							fourthTabs: [],
							children: [],
							url: '#/sys/labor-union-home'
						},
						{
							id: '10-7',
							text: '科研首页',
							icons: '',
							fourthTabs: [],
							children: [],
							url: '#/scientific-sys-home'
						},
						/**fhh开发end*/
						{
							id: '1-2',
							text: '用户管理',
							icons: '',
							fourthTabs: [],
							children: [],
							url: '/UserManagement'
						},
						{
							id: '1-3',
							text: '权限管理',
							icons: '',
							fourthTabs: [],
							children: [],
							url: '/PermissionManagement'
						},
						{
							id: '1-4',
							text: '菜单',
							icons: '',
							fourthTabs: [],
							children: [],
							url: '/MenuManagement'
						}
					]
				}
			],
			data1: [
				{
					id: '1',
					text: '系统管理',
					icons: '',
					fourthTabs: [],
					children: [
						{
							id: '1-1',
							text: '任务管理',
							icons: '',
							fourthTabs: [],
							children: [],
							url: '#/myAssignment'
						},
						{
							id: '1-2',
							text: '我的任务',
							icons: '',
							fourthTabs: [],
							children: [],
							url: '#/myAssignmentView'
						},
						{
							id: '1-3',
							text: '项目变更',
							icons: '',
							fourthTabs: [],
							children: [],
							url: '#/projectChange'
						}
					]
				}
			],
			theme: '#0076E9'
		};
	},
	computed: {
		...mapState('user', ['logoBj']),

		// 是否主页面-用户端过来才显示主页面
		isMain() {
			let userInfo = localStorage.getItem('userInfo');
			return userInfo ? true : false;
		}
	},
	mounted() {
		// 判断地址是否是isAdmin 跳转不同的登录页面
		const isTeacher = this.$route.query.isTeacher;
		const origin = '/';
		if (isTeacher === 'true' || this.isMain) {
			sessionStorage.setItem('loginPage', `${origin}project-ybzy/ybzy_zhxy/index.html#/login`);
		} else {
			sessionStorage.setItem('loginPage', `${origin}project-ybzy/ybzy/index.html#/login`);
		}
		// 获取登录用户信息判断是否为弱密码
		this.$store.dispatch('getInfo/getLoginUserInfo').then(userInfo => {
			localStorage.setItem('mobileUserId', userInfo.userId);
			if (userInfo?.isDefaultPasswork === true) {
				this.$refs.refEditProfile.visible = true;
			}
		});

		// 修改左侧背景图
		let mainLeft = document.getElementsByClassName('es-main-left')[0];
		mainLeft.style.cssText = `background: url(${
			this.logoBj.manageLeftBj || this.mainLeftImg
		}) no-repeat;background-size: 100% 100%;`;
	},
	methods: {
		onHome() {
			this.$router.push('/home');
		}
	}
};
</script>
<style scoped lang="scss">
.main-container {
	width: 100%;
	height: 100%;
	position: relative;
	.home-icon {
		position: absolute;
		left: 345px;
		top: 20px;
		font-size: 20px;
		z-index: 99;
		font-weight: bold;
		color: white;
	}
}
//操作栏按钮居左
::v-deep .es-data-table-content .es-table td.es-table-handle-box .cell {
	justify-content: left !important;
}
::v-deep .es-main-left {
	background: url('../assets/image/menu.png') no-repeat;
	background-size: 100% 100%;

	.es-nav .es-nav-main {
		background-color: transparent !important;
	}

	.es-nav .es-menu,
	.es-nav .el-menu {
		background-color: transparent !important;
	}

	.es-menu.es-menu-vertical .el-submenu__title.is-active,
	.es-menu.es-menu-vertical .es-menu-item.is-active,
	.es-menu.es-menu-vertical .el-menu-item.is-active,
	.es-menu .el-menu .el-submenu__title.is-active,
	.es-menu .el-menu .es-menu-item.is-active,
	.es-menu .el-menu .el-menu-item.is-active,
	.es-sub-menu.es-menu-vertical .el-submenu__title.is-active,
	.es-sub-menu.es-menu-vertical .es-menu-item.is-active,
	.es-sub-menu.es-menu-vertical .el-menu-item.is-active,
	.es-sub-menu .el-menu .el-submenu__title.is-active,
	.es-sub-menu .el-menu .es-menu-item.is-active,
	.es-sub-menu .el-menu .el-menu-item.is-active {
		background-color: #074c8c !important;
	}

	.es-menu.es-menu-vertical .el-menu-item:hover,
	.es-menu.es-menu-vertical .el-menu-item:focus,
	.es-menu.es-menu-vertical .el-submenu__title:hover,
	.es-menu.es-menu-vertical .el-submenu__title:focus,
	.es-menu .el-menu .el-menu-item:hover,
	.es-menu .el-menu .el-menu-item:focus,
	.es-menu .el-menu .el-submenu__title:hover,
	.es-menu .el-menu .el-submenu__title:focus,
	.es-sub-menu.es-menu-vertical .el-menu-item:hover,
	.es-sub-menu.es-menu-vertical .el-menu-item:focus,
	.es-sub-menu.es-menu-vertical .el-submenu__title:hover,
	.es-sub-menu.es-menu-vertical .el-submenu__title:focus,
	.es-sub-menu .el-menu .el-menu-item:hover,
	.es-sub-menu .el-menu .el-menu-item:focus,
	.es-sub-menu .el-menu .el-submenu__title:hover,
	.es-sub-menu .el-menu .el-submenu__title:focus {
		background-color: #074c8c !important;
	}
}
</style>
