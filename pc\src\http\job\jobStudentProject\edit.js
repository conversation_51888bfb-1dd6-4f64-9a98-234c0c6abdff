export default {
	data(){
		return{
			editPageIsEdit: true,
			editPageIsAdd: true,
			editPageAdmitted: null
		}
	},
	computed: {
		formItemList() {
			return [
				{
					name: 'name',
					label: '项目名称',
					value: '',
					col: 12,
					disabled: !this.editPageIsAdd,
					rules: {
						required: true,
						message: '请输入项目名称'
					}
				},
				{
					name: 'introduction',
					label: '项目介绍',
					value: '',
					type: 'textarea',
					col: 12,
					disabled: !this.editPageIsAdd,
					rules: {
						required: true,
						message: '请输入项目介绍'
					}
				},
				{
					name: 'address',
					label: '项目地址',
					value: '',
					col: 12,
					disabled: !this.editPageIsAdd,
					rules: {
						required: true,
						message: '请输入项目地址'
					}
				},
				{
					name: 'longitude',
					label: '经度',
					value: '',
					col: 6,
					disabled: !this.editPageIsAdd,
					rules: {
						required: true,
						message: '请输入经度'
					}
				},
				{
					name: 'latitude',
					label: '纬度',
					value: '',
					col: 6,
					disabled: !this.editPageIsAdd,
					rules: {
						required: true,
						message: '请输入纬度'
					}
				},
				{
					name: 'createUser',
					label: '发起人',
					value: '',
					col: 8,
					disabled: true,
				},
				{
					name: 'telephone',
					label: '电话号码',
					value: '',
					col: 6,
					disabled: !this.editPageIsAdd,
					rules: {
						required: true,
						message: '请输入电话号码'
					}
				},
				{
					name: 'email',
					label: '电子邮箱',
					value: '',
					col: 6,
					disabled: !this.editPageIsAdd,
					rules: {
						required: true,
						message: '请输入电子邮箱'
					}
				},
				{
					name: 'status',
					label: '启用状态',
					value: '',
					col: 4,
					disabled: !this.editPageIsEdit,
					type: 'switch',
					data: [
						{
							value: 1,
							name: '启用'
						},
						{
							value: 0,
							name: '禁用'
						}
					]
				},
				{
					name: 'auditStatus',
					label: '审核发布状态',
					value: '',
					col: 8,
					disabled: !this.editPageIsEdit,
					type: 'radio',
					data: [
						{
							value: 0,
							name: '待审核'
						},
						{
							value: 1,
							name: '已通过',
						},
						{
							value: 2,
							name: '已驳回',
						},
						{
							value: 3,
							name: '已结束',
						}
					]
				},
				{
					name: 'auditOpinion',
					label: '审核意见',
					value: '',
					type: 'textarea',
					col: 12,
					disabled: false,
					rules: {
						required: !(this.editPageIsEdit||this.editPageIsAdd),
						message: '请输入审核意见'
					}
				},
				{
					type: 'submit',
					skin: 'lay-form-btns',
					contents: [
						{
							type: 'primary',
							plain: true,
							text: '保存',
							event: 'submit',
							hide: !this.editPageIsEdit,
						},
						{
							type: 'primary',
							text: '审核通过',
							event: 'click',
							hide: this.editPageIsEdit,
						},
						{
							type: 'danger',
							text: '驳回',
							event: 'click',
							hide: this.editPageIsEdit,
						},
						{
							type: 'reset',
							text: '取消',
							event: this.cancel,
						},
					]
				}
			]
		}
	}
};
