<template>
	<div class="course-situation">
		<div v-for="(item, index) in courseList" :key="index" class="card-item">
			<img class="item-img" :src="item.img" alt="" />
			<div>
				<p>
					<span class="item-num">{{ dataRes[item.key] }}</span>
					<span class="item-unit">{{ item.unit }}</span>
				</p>
				<p class="item-name">{{ item.name }}</p>
			</div>
		</div>
	</div>
</template>

<script>
import { xodbApi2 } from '@/api/xodb';
export default {
	name: '',
	components: {},
	props: {},
	data() {
		return {
			courseList: [
				{
					name: '本学期开课课程',
					value: '0',
					key: 'kkkcsl',
					unit: '门',
					img: require('@/assets/images/home20240423/course-number.png')
				},
				{
					name: '生均课程',
					value: '0',
					key: 'sjkcsl',
					unit: '门',
					img: require('@/assets/images/home20240423/course-average.png')
				},
				{
					name: '思政课程',
					value: '0',
					key: 'szkcsl',
					unit: '门',
					img: require('@/assets/images/home20240423/courses-Ideological.png')
				},
				{
					name: '省市精品在线开放课程',
					value: '0',
					key: 'ssjpzxkfkcsl',
					unit: '门',
					img: require('@/assets/images/home20240423/courses-boutique.png')
				}
			],
			dataRes: {}
		};
	},
	computed: {
		loginUserInfo() {
			return JSON.parse(localStorage.getItem('loginUserInfo')) || {};
		}
	},
	created() {},
	mounted() {
		this.getDataN('ads_jx_kcqksj_query', 'dataRes');
	},
	methods: {
		async getDataN(url, listName) {
			const LeaderRole = localStorage.getItem('LeaderRole');
			try {
				const {
					data: { list }
				} = await xodbApi2.post({
					url,
					params: {
						xyjgbh: LeaderRole === 'xLeader' ? '1' : this.loginUserInfo.orgCode
					}
				});
				const data = list[0] || {};
				switch (listName) {
					case 'dataRes':
						this[listName] = { ...this[listName], ...data };
						break;
				}
				this[listName] = data;
			} catch (error) {
				console.error(`课程情况：处理数据失败${listName}:`, error);
			}
		}
	}
};
</script>

<style lang="scss" scoped>
.course-situation {
	width: 100%;
	width: 100%;
	height: 100%;
	margin-top: 17px;
	display: flex;
	justify-content: space-between;
}
.card-item {
	display: flex;
	align-items: center;
	height: 56px;
	width: 24%;
	padding-left: 26px;
	flex-shrink: 1;
	.item-img {
		width: 42px;
		height: 42px;
		margin-right: 15px;
	}
	.item-num {
		font-family: DINPro, DINPro;
		font-weight: bold;
		font-size: 24px;
		color: #0a325b;
		line-height: 31px;
	}
	.item-unit {
		font-family: MicrosoftYaHei;
		font-size: 14px;
		color: #294d79;
		line-height: 19px;
		margin-left: 8px;
	}
	.item-name {
		font-family: MicrosoftYaHei;
		font-size: 14px;
		color: #454545;
		line-height: 19px;
		margin-top: 6px;
	}
	&:last-child {
		flex-grow: 1;
	}
}
.card-item + .card-item {
	padding-left: 42px;
	border-left: 1px dashed rgba(56, 139, 204, 1);
}
</style>
