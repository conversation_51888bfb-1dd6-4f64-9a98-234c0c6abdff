<!--
 @desc:基本信息
 @author: WH
 @date: 2023/11/14
 -->
<template>
	<ProcesPage
		:flow-data-props="{
			appId: formData.appId, // 流程图id
			businessId: id, // 有 businessId-业务id 则为发起节点
			flowTypeCode: basics.flowTypeCode,
			defaultProcessKey: basics.defaultProcessKey,
			isEdit: !formReadonly, // 是否编辑
			isFlow: !formReadonly // 是否显示右边流程功能
		}"
		@subFun="save"
		@handleSuccess="handleSuccess"
	>
		<div class="main">
			<div class="basic-info">
				<title-card title="基础信息"></title-card>
				<es-form
					:key="id"
					ref="formRef"
					label-width="140px"
					:model="formData"
					:contents="contents"
					table
					:submit="false"
					:readonly="formReadonly"
				></es-form>
			</div>
			<div class="menber-info">
				<title-card title="成员信息"></title-card>
				<div class="is-table">
					<es-data-table
						ref="formRef1"
						form
						:readonly="formReadonly"
						style="width: 100%"
						:thead="theadTeacher"
						:data="formData.teachers"
						@btnClick="btnClick"
					></es-data-table>
				</div>
				<div class="is-table">
					<es-data-table
						ref="formRef2"
						form
						:readonly="formReadonly"
						style="width: 100%"
						:thead="theadStudent"
						:data="formData.students"
						@btnClick="btnClick"
					></es-data-table>
				</div>
			</div>
		</div>
	</ProcesPage>
</template>

<script>
import ProcesPage from '@/components/process-page.vue';
import { v4 as uuidv4 } from 'uuid';
import titleCard from '@cpt/scientific-sys/title-card.vue';

import { mixinData } from './mixinData';
export default {
	components: { ProcesPage, titleCard },
	mixins: [mixinData],
	props: {
		id: {
			type: String,
			default: ''
		},
		title: {
			type: String,
			default: '查看' // 查看 编辑 新增
		},
		basics: {
			type: Object,
			default: () => {
				return {
					info: '/ybzy/paAcademicPaper/info',
					save: '/ybzy/paAcademicPaper/save',
					edit: 'ybzy/paAcademicPaper/update',
					contentsKey: 'academicPaper', // mixinData动态表单配置数据
					flowTypeCode: 'projectAchievement_etipnuuezknypsv', // 流程code
					defaultProcessKey: 'projectAchievement_etipnuuezknypsv' // 默认关联流程 key
				};
			}
		}
	},
	data() {
		return {
			formReadonly: false,
			basicInfo: {},
			rowData: [],
			assumeCode: [
				{
					id: '088f76bbcb0b4498881d744cfa399e62',
					shortName: '主持（负责人）',
					cciValue: '0',
					sort: 0,
					description: '',
					cciPcode: null
				},
				{
					id: 'b6bd9cf7a69a4297bf410dc88e35580f',
					shortName: '主研',
					cciValue: '1',
					sort: 1,
					description: '',
					cciPcode: null
				},
				{
					id: 'd6dd72a0d6c040aa90392b9140f69244',
					shortName: '参研',
					cciValue: '2',
					sort: 2,
					description: '',
					cciPcode: null
				},
				{
					id: '1ec3cef004c4443e909ff4d060bc72bb',
					shortName: '学生',
					cciValue: '3',
					sort: 3,
					description: '',
					disabled: true,
					cciPcode: null
				}
			],

			formData: { students: [], teachers: [] },
			row: {}
		};
	},
	computed: {
		theadTeacher() {
			return [
				{
					label: '教师信息',
					childHead: [
						{
							label: '人员类型',
							field: 'memberType',
							type: 'select',
							sysCode: 'project_member_type',
							width: 120,
							rules: {
								required: !this.formReadonly,
								message: '请选择',
								trigger: 'blur'
							},
							filterable: true
						},
						{
							label: '姓名',
							field: this.formReadonly ? 'memberName' : 'member',
							type: 'selector',
							types: ['employee', 'otheremployee'],
							placeholder: '请选择',
							width: 180,
							multiple: false,
							filterable: true,
							rules: {
								required: !this.formReadonly,
								message: '请选择',
								trigger: 'blur'
							},
							events: {
								change: ({ data, item, name, value }) => {
									const obj = {
										...value[0],
										...JSON.parse(value[0]?.attr || '{}')
									};
									this.$set(data, 'memberNum', obj.outcode);
									this.$set(data, 'memberName', obj.username);
									this.$set(data, 'deptName', obj.orgName);
									this.$set(data, 'deptId', obj.orgId);
									this.$set(data, 'professional', obj.postName);
									this.$set(data, 'memberPhone', obj.phone);
									console.log({ data, obj }, '选中人时');
								},
								confirm: e => {}
							}
						},
						{
							label: '教工号',
							field: 'memberNum',
							width: 110,
							type: 'text',
							rules: {
								required: !this.formReadonly,
								message: '请选择',
								trigger: 'blur'
							},
							filterable: true
						},
						{
							label: '部门/学院',
							field: 'deptName',
							type: 'text',
							rules: {
								required: !this.formReadonly,
								message: '请选择',
								trigger: 'blur'
							}
						},
						{
							label: '职称',
							field: 'professional',
							type: 'text',
							rules: {
								required: !this.formReadonly,
								message: '请选择',
								trigger: 'blur'
							}
						},
						{
							label: '联系电话',
							field: 'memberPhone',
							type: 'text',
							rules: {
								required: !this.formReadonly,
								message: '请选择',
								trigger: 'blur'
							},
							filterable: true
						},
						{
							label: '承担类型',
							field: 'assumeType',
							width: 150,
							type: 'select',
							rules: {
								required: !this.formReadonly,
								message: '请选择',
								trigger: 'blur'
							},
							'label-key': 'shortName',
							'value-key': 'cciValue',
							// sysCode: 'project_assume_type',
							data: this.assumeCode,
							filterable: true
						},
						{
							title: '操作',
							type: 'handle',
							label: '操作',
							width: 80,
							hide: this.formReadonly,
							template: '',
							renderHeader: (h, params) => {
								return h('div', [
									h('span', {}, '操作'),
									h('el-button', {
										props: {
											type: 'text',
											icon: 'el-icon-circle-plus-outline'
										},
										on: {
											click: () => {
												this.$set(this.formData.teachers, this.formData.teachers.length, {
													id: uuidv4(),
													memberType: '',
													member: [],
													memberNum: '',
													deptName: '',
													deptId: '',
													memberPhone: ''
												});
											}
										}
									})
								]);
							},
							events: [
								{
									text: '删除',
									size: 'mini',
									type: 'text',
									icon: 'es-icon-shanchu',
									event: ({ $index, row }) => {
										const teachers = this.formData.teachers.filter(item => item.id !== row.id);
										this.$set(this.formData, 'teachers', teachers);
									}
								}
							]
						}
					]
				}
			];
		},
		theadStudent() {
			return [
				{
					label: '学生信息',
					childHead: [
						{
							label: '人员类型',
							field: 'memberType',
							type: 'select',
							rules: {
								required: !this.formReadonly,
								message: '请选择',
								trigger: 'blur'
							},
							sysCode: 'project_member_type',
							filterable: true
						},
						{
							label: '姓名',
							field: this.formReadonly ? 'memberName' : 'member',
							type: 'selector',
							types: ['employee', 'otheremployee'],
							placeholder: '请选择',
							width: 180,
							multiple: false,
							filterable: true,
							rules: {
								required: !this.formReadonly,
								message: '请选择',
								trigger: 'blur'
							},
							events: {
								change: ({ data, item, name, value }) => {
									const obj = {
										...value[0],
										...JSON.parse(value[0]?.attr || '{}')
									};
									this.$set(data, 'memberNum', obj.outcode);
									this.$set(data, 'memberName', obj.username);
									this.$set(data, 'deptName', obj.orgName);
									this.$set(data, 'deptId', obj.orgId);

									this.$set(data, 'memberPhone', obj.phone);
									// this.$set(data, 'assumeType', '3');
									console.log({ data, obj }, '选中人时');
								},
								confirm: e => {}
							}
						},
						{
							label: '学工号',
							field: 'memberNum',
							width: 110,
							type: 'text',
							rules: {
								required: !this.formReadonly,
								message: '请选择',
								trigger: 'blur'
							}
						},
						{
							label: '部门/学院',
							field: 'deptName',
							type: 'text',
							rules: {
								required: !this.formReadonly,
								message: '请选择',
								trigger: 'blur'
							}
						},
						{
							label: '联系电话',
							field: 'memberPhone',
							type: 'text',
							rules: {
								required: !this.formReadonly,
								message: '请选择',
								trigger: 'blur'
							},
							filterable: true
						},
						{
							label: '承担类型',
							field: 'assumeType',
							width: 100,
							type: 'select',
							readonly: true,
							'label-key': 'shortName',
							'value-key': 'cciValue',
							// sysCode: 'project_assume_type',
							data: this.assumeCode,
							filterable: true
						},
						{
							label: '证明材料（学生证）',
							minWidth: '200px',
							field: 'studentFile',
							rules: {
								required: !this.formReadonly,
								message: '请选择',
								trigger: 'blur'
							},
							align: 'right',
							filterable: true,
							render: (h, params) => {
								const fn = h('es-upload', {
									class: 'upload-box',
									attrs: {
										preview: true,
										showInfo: [],
										code: 'transationform_editfile',
										ownId: params.row.id, // 业务id
										readonly: this.formReadonly,
										fileList: params.row.studentFile,
										onChange: (res, file) => {
											params.row.studentFile = file;
										}
									}
								});
								return fn;
							}
						},
						{
							title: '操作',
							type: 'handle',
							label: '操作',
							hide: this.formReadonly,
							width: 80,
							renderHeader: (h, params) => {
								return h('div', [
									h('span', {}, '操作'),
									h('el-button', {
										props: {
											type: 'text',
											icon: 'el-icon-circle-plus-outline'
										},
										on: {
											click: () => {
												this.$set(this.formData.students, this.formData.students.length, {
													id: uuidv4(),
													memberType: '',
													member: [],
													memberNum: '',
													deptName: '',
													deptId: '',
													memberPhone: '',
													assumeType: '3',
													studentFile: []
												});
											}
										}
									})
								]);
							},
							events: [
								{
									text: '删除',
									size: 'mini',
									type: 'text',
									icon: 'es-icon-shanchu',
									event: ({ $index, row }) => {
										const students = this.formData.students.filter(item => item.id !== row.id);
										this.$set(this.formData, 'students', students);
									}
								}
							]
						}
					]
				}
			];
		}
	},
	watch: {
		id: {
			handler(newVal, oldVal) {
				if (newVal) {
					this.getPorjectInfo();
				}
			},
			immediate: true
		}
	},

	methods: {
		// 是否只读和编辑
		toolFormReadonly() {
			this.$nextTick(() => {
				const isEdit = window.location.href.includes('isEdit'); // 判断是否流程页面的编辑
				this.formReadonly = this.title === '查看' && !isEdit ? true : false;
			});
		},
		//表格修改弹窗操作
		btnClick({ handle, row }) {
			// console.log(">>>", this.$router.resolve({ name: "allotSubmitList" }));
			let { text, btnType } = handle;
			this.row = { ...row, memberType: String(row.memberType), assumeType: String(row.assumeType) };
		},
		// 表格行高
		tableRowClassName({ row, rowIndex }) {
			let styleRes = {
				height: '54px !important'
			};
			return styleRes;
		},
		//修改保存
		async save(callBank) {
			this.$refs.formRef.validate(valid => {
				this.$refs.formRef1.validate(valid1 => {
					this.$refs.formRef2.validate(valid2 => {
						if (valid && valid1 && valid2) {
							const loading = this.load('提交中...');
							let formData = {
								...this.formData,
								students: this.formData.students.map(e => {
									delete e.studentFile;
									delete e.member;
									return e;
								}),
								teachers: this.formData.teachers.map(e => {
									delete e.member;
									return e;
								})
							};
							delete formData.fj1;
							this.$.ajax({
								url: this.title.includes('新增') ? this.basics.save : this.basics.edit,
								method: 'post',
								data: formData,
								format: false
							}).then(res => {
								loading.close();
								if (res.rCode == 0) {
									if (callBank) {
										callBank();
									} else {
										this.$message.success(res.msg);
										this.handleSuccess();
									}
								} else {
									this.$message.warning(res.msg);
								}
							});
						} else {
							return false;
						}
					});
				});
			});
		},
		// 提交成功
		handleSuccess(e) {
			//pendingId则为流程的审核页面，否则是弹窗的流程
			const isFlow = window.location.href.includes('pendingId');
			if (isFlow) {
				window.close();
			} else {
				this.$emit('update:visible', false);
			}
		},

		// 页面加载
		load(text) {
			return this.$.loading(this.$loading, text);
		},
		async getPorjectInfo() {
			this.toolFormReadonly();
			if (this.title.includes('新增')) {
				this.formData = {
					// id: '905d2769-141a-4c7c-b25d-0d1504ac2116',
					// periodicalPublishDate: '2024-07-16',
					// firstAuthor: '张三',
					// thesisName: '量子计算在密码学中的未来应用',
					// thesisUrl: 'www.baidu.com',
					// projectNum: '*********',
					// projectName: '马克思主义研究',
					// periodicalName: '自然',
					// periodicalPageNum: '4',
					// thesisLevel: '1',
					// studentIsFirst: '1',
					// studentName: '韩梅梅',
					// studentNum: '********',
					// fj1: [],
					students: [],
					teachers: []
				};
				this.formData.id = this.id;
				return;
			}
			const loading = this.load('加载中...');
			try {
				let { rCode, msg, results } = await this.$.ajax({
					url: this.basics.info,
					method: 'get',
					params: { id: this.id }
				});
				if (rCode == 0) {
					let obj = results;
					this.setTableData(obj);
				} else {
					this.$message.error(msg);
				}
			} finally {
				loading.close();
			}
		},
		setTableData(obj) {
			let formData = {
				...obj,
				teachers: obj.teachers?.map(e => {
					e.member = [
						{
							outcode: e.memberNum,
							username: e.memberName,
							showname: e.memberName,
							orgName: e.deptName,
							orgId: e.deptId,
							postName: e.professional,
							phone: e.memberPhone
						}
					];
					return e;
				}),
				students: obj.students?.map(e => {
					e.member = [
						{
							outcode: e.memberNum,
							username: e.memberName,
							showname: e.memberName,
							orgName: e.deptName,
							orgId: e.deptId,

							phone: e.memberPhone
						}
					];
					return e;
				})
			};
			this.formData = formData;
		}
	}
};
</script>

<style lang="scss" scoped>
@import '@ast/style/public.scss';
.main {
	// @include flexBox();
	// flex-direction: column;
	height: 100%;
	overflow: auto;
	padding: 0 8px 60px;
	.basic-info {
		flex: 1;
		// height: 400px;
		width: 100%;
		overflow: auto;
	}
	.menber-info {
		margin-bottom: 30px;
		width: 100%;
		.is-table {
			width: 100%;
			margin-bottom: 15px;
		}
	}
}
::v-deep .upload-box {
	width: 100%;
	.el-button--medium {
		padding: 2px 6px;
		font-size: 12px;
	}
	.el-upload-list {
		// margin-top: -15px !important;
		.el-upload-list__item-name {
			width: auto;
			top: 0px;
			text-align: center;
			height: 37.7px;
			line-height: 39.7px;
		}
	}
	table {
		border-collapse: collapse; /* 合并单元格边框，避免双线 */
		border-spacing: 0; /* 当 border-collapse 不起作用时，可以尝试这个 */
	}

	table,
	th,
	td {
		border: none; /* 移除表格、表头和单元格的边框 */
	}
}
::v-deep .es-form-content {
	padding: 0 !important;
}
::v-deep .es-table-form {
	width: 100%;
	.es-table-form-label {
		text-align: right;
		color: #747474;
		font-weight: 550;
	}
}
::v-deep .cell {
	color: #747474;
}
</style>
