<!--
 @desc:申请修改弹窗 只做表单填写内容返回
 @author: WH
 @date: 2023/5/8
 -->
<template>
	<es-form
		ref="applyRuleForm"
		:model="formData"
		:contents="formItemList"
		@click="handleClick"
	></es-form>
</template>

<script>
const OPEN_TYPE_NAME={
  apply:'申请修改理由填报',
  report:'无数据上报理由填报',
  audit:'审核意见',
//   auditApply:'申请修改审核意见',
}
export default {
	props: {
		openType: {
			type: String,
			default: 'submit' // submit提交申请  audit审核申请修改
		}
	},
	data() {
		return {
			formData: {},
			formItemList: [
				{
					title: OPEN_TYPE_NAME[this.openType],
					contents: [
						{
							name: 'reason',
							label: '上报理由',
							type: 'textarea',
							rules: {
								required: true,
								message: '请输入理由',
								trigger: 'blur'
							},
							rows: 5
						}
					]
				},
				{
					type: 'submit',
					skin: 'lay-form-btns',
					contents: [
						{
							type: 'primary',
							btnType: 'submit',
							text: '提交',
							event: (e, formData) => {
								//调用表单验证
								this.$refs.applyRuleForm.validate(valid => {
									if (valid) {
										this.handleClick({ btnType: 'submit' }, formData);
									} else {
										return false;
									}
								});
							}
						},
						{
							type: 'primary',
							btnType: 'esc',
							text: '取消'
						}
					]
				}
			],
		};
	},
	methods: {
		handleClick(handle, formData) {
			const { btnType } = handle;
			const handlers = {
				submit: () => this.$emit('confirm', { type: this.openType, formData }),
				esc: () => this.$emit('close',false)
			};
			const handler = handlers[btnType];
			handler && handler();
		}
	}
};
</script>

<style lang="scss" scoped>
main {
	height: 100%;
	overflow: auto;
}
</style>
