<template>
	<es-dialog
		v-if="dialogVisible"
		:title="''"
		:visible.sync="dialogVisible"
		:drag="false"
		:width="dialogWidth"
		:height="dialogHeight"
		:show-scale="false"
		:close-on-click-modal="false"
		:destroy-on-close="true"
		custom-class="dialog-class"
	>
		<!-- <practiceDialog v-if="title === '实习'" :title="title"></practiceDialog>
		<experimentDialog v-else :title="title"></experimentDialog>
		<ScheduleDialog v-else :title="title"></ScheduleDialog> -->
		<!-- 动态组件 -->
		<component :is="componentActive" :key="componentActive" title="title"></component>
		<i class="el-icon-close close" @click="dialogVisible = false"></i>
	</es-dialog>
</template>

<script>
import experimentDialog from '../dialog/experiment-dialog.vue';
import practiceDialog from '../dialog/practice-dialog.vue';
import ScheduleDialog from '../dialog/schedule-dialog.vue';
import PerformanceDialog from '../dialog/performance-dialog.vue';
export default {
	name: 'PopEmployment',
	components: { experimentDialog, practiceDialog, ScheduleDialog, PerformanceDialog },
	props: {
		title: {
			type: String, // 毕业就业 实习
			default: ''
		}
	},
	data() {
		return {
			dialogVisible: false,
			componentActive: 'experimentDialog', //展示的组件
			dialogWidth: '92.6%', //弹窗的宽度
			dialogHeight: '92.6%', //弹窗的宽度
			navList: {
				毕业就业: {
					name: '毕业就业',
					component: 'experimentDialog',
					width: '92.6%'
				},
				实习: {
					name: '实习',
					component: 'practiceDialog',
					width: '92.6%'
				},
				课表: {
					name: '课表',
					component: 'ScheduleDialog',
					width: '96%'
				},
				成绩: {
					name: '成绩',
					component: 'PerformanceDialog',
					width: '90%',
					height: '85%'
				}
			}
		};
	},
	watch: {
		title(value) {
			this.componentActive = this.navList[value]?.component || '';
			this.dialogWidth = this.navList[value]?.width || '92%';
			this.dialogHeight = this.navList[value]?.height || '95%';
		}
	}
};
</script>

<style lang="scss" scoped>
::v-deep .el-dialog {
	background: url('@/assets/images/home20240423/dialog.png') center center no-repeat;
	background-size: 100% 100%;
	background: #e7f6ff;
	padding-top: 0px !important;
	position: relative;
	overflow: auto;

	.el-dialog__body {
		background: transparent;
		padding: 0px !important;
	}
	.el-dialog__header {
		display: none;
	}
	.close {
		top: 34px;
		right: 31px;
		position: absolute;
		color: #94b2ba;
		font-size: 18px;
		font-weight: bold;
		cursor: pointer;
	}
}
</style>
