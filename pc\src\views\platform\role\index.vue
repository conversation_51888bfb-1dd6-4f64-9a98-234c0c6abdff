<template>
	<div v-loading="loading" class="content">
		<es-data-table
			ref="table"
			:row-style="tableRowClassName"
			:full="true"
			:fit="true"
			:thead="thead"
			:toolbar="toolbar"
			:border="true"
			:page="true"
			:url="dataTableUrl"
			:numbers="true"
			:param="params"
			close
			form
			@btnClick="btnClick"
			@sort-change="sortChange"
			@submit="hadeSubmit"
		></es-data-table>
		<es-dialog
			:title="formTitle"
			:visible.sync="showForm"
			height="75%"
			width="60%"
			:close-on-click-modal="false"
			:destroy-on-close="true"
		>
			<es-form
				ref="form"
				:model="formData"
				:contents="formItemList"
				height="100%"
				:genre="2"
				collapse
				@change="inputChange"
				@submit="handleFormSubmit"
				@reset="showForm = false"
			/>
		</es-dialog>
		<es-dialog
			title="删除"
			:visible.sync="showDelete"
			width="20%"
			:close-on-click-modal="false"
			:middle="true"
			height="140px"
		>
			<div>确定要删除该条数据吗</div>
			<div class="btn-box">
				<div class="btn theme" @click="deleteRow">确定</div>
				<div class="btn" @click="showDelete = false">取消</div>
			</div>
		</es-dialog>
	</div>
</template>

<script>
import interfaceUrl from '@/http/platform/role.js';
import SnowflakeId from 'snowflake-id';
export default {
	data() {
		return {
			loading: false,
			userList: [],
			resourceList: [],
			apiList: [],
			dataTableUrl: interfaceUrl.listJson,
			showForm: false,
			showDelete: false,
			formData: {},
			formTitle: '编辑',
			editBtn: {
				type: 'submit',
				skin: 'lay-form-btns',
				contents: [
					{
						type: 'primary',
						text: '确定',
						event: 'confirm'
					},
					{
						type: 'reset',
						text: '取消',
						event: 'cancel'
					}
				]
			},
			cancelBtn: {
				type: 'submit',
				skin: 'lay-form-btns',
				contents: [
					{
						type: 'reset',
						text: '取消',
						event: 'cancel'
					}
				]
			},
			toolbar: [
				{
					type: 'button',
					contents: [
						{
							text: '新增',
							code: 'add',
							type: 'primary'
						}
					]
				},
				{
					type: 'search',
					contents: [
						{
							type: 'text',
							name: 'keyword',
							placeholder: '关键字查询'
						}
					]
				}
			],
			thead: [
				{
					title: '角色名称',
					align: 'left',
					field: 'name',
					sortable: 'custom',
					showOverflowTooltip: true
				},
				{
					title: '角色编码',
					align: 'left',
					field: 'code',
					sortable: 'custom',
					showOverflowTooltip: true
				},
				{
					width: '100px',
					title: '排序号',
					align: 'center',
					field: 'sortNum',
					sortable: 'custom',
					showOverflowTooltip: true
				},
				{
					title: '更新时间',
					align: 'center',
					field: 'updateTime',
					sortable: 'custom',
					showOverflowTooltip: true
				},
				{
					title: '更新人',
					align: 'center',
					field: 'updateUserName',
					sortable: 'custom',
					showOverflowTooltip: true
				},
				{
					title: '操作',
					type: 'handle',
					width: 180,
					template: '',
					events: [
						{
							code: 'edit',
							text: '编辑'
						},
						{
							code: 'view',
							text: '查看'
						},
						{
							code: 'delete',
							text: '删除'
						}
					]
				}
			],
			params: {
				asc: 'false',
				orderBy: 'sortNum asc,createTime'
			}
		};
	},
	computed: {
		formItemList() {
			return [
				{
					label: '角色名称',
					name: 'name',
					placeholder: '请输入角色名称',
					event: 'multipled',
					rules: {
						required: true,
						message: '请输入角色名称',
						trigger: 'blur'
					},
					verify: 'required',
					col: 6
				},
				{
					label: '角色编码',
					name: 'code',
					placeholder: '请输入角色编码',
					event: 'multipled',
					rules: {
						required: true,
						message: '请输入角色编码',
						trigger: 'blur'
					},
					verify: 'required',
					col: 6
				},
				// {
				//     type: 'select',
				//     placeholder: '',
				//     label: '分配用户',
				//     placeholder: '',
				//     name: 'selectedUserList',
				//     event: 'multipled',
				//     multiple: true,
				//     data: this.userList,
				//     col: 12
				// },
				{
					type: 'select',
					label: '分配资源',
					placeholder: '',
					name: 'selectedResourceList',
					event: 'multipled',
					multiple: true,
					tree: true,
					valueKey: 'id',
					labelKey: 'name',
					'value-type': 'object',
					data: this.resourceList,
					col: 12
				},
				{
					type: 'select',
					label: '分配API',
					placeholder: '',
					name: 'selectedApiList',
					event: 'multipled',
					multiple: true,
					tree: true,
					valueKey: 'id',
					labelKey: 'name',
					data: this.apiList,
					col: 12
				},
				{
					label: '是否默认角色',
					name: 'isUserDefault',
					placeholder: '请选择是否默认角色',
					type: 'radio',
					rules: {
						required: true,
						message: '请选择是否默认角色',
						trigger: 'blur'
					},
					verify: 'required',
					col: 6,
					sysCode: 'yes_or_no',
					'label-key': 'shortName',
					'value-key': 'cciValue'
				},
				{
					label: '默认角色范围',
					name: 'defaultScope',
					type: 'checkbox',
					placeholder: '请选择默认角色范围',
					col: 6,
					sysCode: 'plat_role_default_scope',
					'label-key': 'shortName',
					'value-key': 'cciValue'
				},
				{
					label: '排序号',
					name: 'sortNum',
					placeholder: '请输入排序号',
					type: 'number',
					controls: false,
					rules: {
						required: true,
						message: '请输入排序号',
						trigger: 'blur'
					},
					verify: 'required',
					col: 6
				},
				{
					name: 'remark',
					label: '备注',
					placeholder: '请输入备注',
					type: 'textarea',
					col: 12
				}
			];
		}
	},
	watch: {},
	created() {},
	mounted() {},
	methods: {
		initOptionData() {
			//屏蔽用户初始化，解决页面卡死
			// this.$request({
			//     url: interfaceUrl.userSelectList,
			//     method: 'GET'
			// }).then(res => {
			//     if (res.rCode == 0) {
			//         this.userList = res.results;
			//     }
			// });
			this.$request({
				url: interfaceUrl.resourceTreeList,
				method: 'GET'
			}).then(res => {
				if (res.rCode == 0) {
					this.resourceList = res.results;
				}
			});
			this.$request({
				url: interfaceUrl.apiTreeList,
				method: 'GET'
			}).then(res => {
				if (res.rCode == 0) {
					this.apiList = res.results;
				}
			});
		},
		/**
		 * 表格行高
		 */
		tableRowClassName({ row, rowIndex }) {
			let styleRes = {
				height: '54px !important'
			};
			return styleRes;
		},
		inputChange(key, value) {
			if (key == 'phone') {
			}
		},
		hadeSubmit(data) {},
		/**
		 * 操作按钮点击事件
		 * @param {*} res
		 */
		btnClick(res) {
			this.loading = true;
			let code = res.handle.code;
			switch (code) {
				case 'add':
					// 新增
					this.formTitle = '新增';
					this.initOptionData();
					this.editModule(this.formItemList, []);
					const snowflake = new SnowflakeId();
					let id = snowflake.generate();
					this.formData = { id: id, isUserDefault: '0', sortNum: 1 };
					this.showForm = true;
					this.loading = false;
					break;
				case 'edit':
					// 编辑
					this.formTitle = '编辑';
					this.initOptionData();
					this.editModule(this.formItemList, []);
					this.$request({
						url: interfaceUrl.info + '/' + res.row.id,
						method: 'GET'
					}).then(res => {
						if (res.rCode == 0) {
							this.formData = res.results;
							this.formData.defaultScope =
								undefined != this.formData &&
								undefined != this.formData.defaultScope &&
								this.formData.defaultScope.length > 0
									? this.formData.defaultScope.split(',')
									: [];
							this.showForm = true;
							this.loading = false;
						}
					});
					break;
				case 'view':
					this.formTitle = '查看';
					this.readModule(this.formItemList, []);
					this.$request({
						url: interfaceUrl.info + '/' + res.row.id,
						method: 'GET'
					}).then(res => {
						if (res.rCode == 0) {
							this.formData = res.results;
							this.formData.defaultScope =
								undefined != this.formData &&
								undefined != this.formData.defaultScope &&
								this.formData.defaultScope.length > 0
									? this.formData.defaultScope.split(',')
									: [];
							this.showForm = true;
							this.loading = false;
						}
					});
					break;
				case 'delete':
					this.deleteId = res.row.id;
					this.showDelete = true;
					break;
				default:
					break;
			}
		},
		handleClose() {},
		handleFormItemChange() {},
		handleFormSubmit(data) {
			let formData = data;
			delete formData.extMap;
			let url = '';
			if (this.formTitle == '新增') {
				url = interfaceUrl.save;
			} else {
				url = interfaceUrl.update;
			}

			if (undefined != formData && undefined != formData.defaultScope) {
				formData.defaultScope = formData.defaultScope.join(',');
			}

			const snowflake = new SnowflakeId();

			// 用户处理
			// let userList = [];
			// if (formData.selectedUserList) {
			// 	formData.selectedUserList.forEach((elem, index) => {
			// 		if (undefined != elem) {
			// 			userList.push({
			// 				id: snowflake.generate(),
			// 				roleId: formData.id,
			// 				userId: undefined == elem.value ? elem : elem.value,
			// 				sortNum: index
			// 			});
			// 		}
			// 	});
			// }
			// formData.userList = userList;
			// formData.userList = [];
			// formData.selectedUserList = [];
			delete formData.userList;
			delete formData.selectedUserList;

			// 资源处理
			let resourceList = [];
			formData.selectedResourceList.forEach((elem, index) => {
				if (undefined != elem && undefined != elem.id) {
					resourceList.push({
						id: snowflake.generate(),
						roleId: formData.id,
						resourceId: elem.id,
						resourceType: elem.resourceType,
						sortNum: index
					});
				}
			});
			formData.resourceList = resourceList;

			// api处理
			let apiList = [];
			formData.selectedApiList.forEach((elem, index) => {
				if (undefined != elem) {
					apiList.push({
						id: snowflake.generate(),
						roleId: formData.id,
						apiId: elem instanceof Object ? elem.id : elem,
						apiType: elem.apiType,
						sortNum: index
					});
				}
			});
			formData.apiList = apiList;

			this.$request({
				url: url,
				data: formData,
				method: 'POST',
				format: false
			}).then(res => {
				if (res.rCode == 0) {
					this.$message.success('操作成功');
					this.showForm = false;
					this.formData = {};
					this.$refs.table.reload();
				} else {
					this.$message.error(res.msg);
				}
			});
		},
		deleteRow() {
			this.$request({
				url: interfaceUrl.deleteBatchIds,
				data: { ids: this.deleteId },
				method: 'POST'
			}).then(res => {
				if (res.rCode == 0) {
					this.$refs.table.reload();
					this.$message.success('删除成功');
				} else {
					this.$message.error(res.msg);
				}
				this.showDelete = false;
			});
		},
		/**
		 * 排序变化事件
		 * @param {*} column
		 * @param {*} prop
		 * @param {*} order
		 */
		sortChange(column, prop, order) {
			if (column.order == 'ascending') {
				//升序
				this.params = {
					asc: 'true',
					orderBy: column.prop
				};
			} else if (column.order == 'descending') {
				//降序
				this.params = {
					asc: 'false',
					orderBy: column.prop
				};
			} else {
				//不排序
				this.params = {
					asc: 'false',
					orderBy: 'sortNum asc,createTime'
				};
			}
			this.$refs.table.reload();
		},
		/**
		 * 编辑模式
		 */
		editModule(list, hideField) {
			for (var i in list) {
				var item = list[i];
				item.readonly = false;
				if (undefined != hideField && hideField.length > 0 && hideField.indexOf(item.name) > -1) {
					item.hide = true;
				}
			}
			list.push(this.editBtn);
		},
		/**
		 * 只读模式
		 */
		readModule(list, hideField) {
			for (var i in list) {
				var item = list[i];
				item.readonly = true;
				if (undefined != hideField && hideField.length > 0 && hideField.indexOf(item.name) > -1) {
					item.hide = true;
				}
			}
			list.push(this.cancelBtn);
		}
	}
};
</script>
<style scoped lang="scss">
@import '../../../assets/style/style.scss';

.content {
	width: 100%;
	height: 100%;
}

.el-dialog__body {
	.btn-box {
		margin-top: 20px;
		display: flex;
		justify-content: flex-end;

		.btn {
			padding: 5px 10px;
			color: #666;
			border: 1px solid #eee;
			cursor: pointer;
			margin-right: 5px;
			border-radius: 4px;
			// &.theme {
			// 	background: $--color-primary;
			// 	color: #fff;
			// 	border-color: $--color-primary;
			// }
		}
	}
}
</style>
