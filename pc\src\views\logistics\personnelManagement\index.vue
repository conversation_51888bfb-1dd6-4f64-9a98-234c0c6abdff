<template>
	<div v-loading="loading" class="content">
		<es-data-table
			ref="table"
			:thead="thead"
			:toolbar="toolbar"
			:border="true"
			:page="pageOption"
			:url="basics.dataTableUrl"
			:numbers="true"
			:immediate="true"
			:param="params"
			close
			form
			@btnClick="btnClick"
		></es-data-table>
		<es-dialog
			v-if="showForm"
			:title="formTitle"
			:visible.sync="showForm"
			width="1000px"
			height="60%"
			:close-on-click-modal="false"
			:destroy-on-close="true"
		>
			<resumeView
				v-if="showForm"
				:title="formTitle"
				:form-id="formId"
				:basics="basics"
				@close="
					() => {
						$refs.table.reload();
						showForm = false;
					}
				"
			></resumeView>
		</es-dialog>
	</div>
</template>

<script>
import resumeView from './components/view.vue';
import { host } from '../../../../config/config';
export default {
	components: { resumeView },
	data() {
		return {
			basics: {
				dataTableUrl: '/ybzy/hqPerson/listJson', // 列表接口
				info: '/ybzy/hqPerson/info', // 详情接口
				save: '/ybzy/hqPerson/save', // 新增
				edit: '/ybzy/hqPerson/update', // 修改
				delete: '/ybzy/hqPerson/deleteById', // 删除 removeById逻辑删除 deleteById真删
				revoke: '', // 撤销
				download: '/ybzy/hqPerson/download', // 导出
				importExcel: '/ybzy/hqPerson/importExcel', // 导入
				getTemplate: '/ybzy/hqPerson/getTemplate' // 下载模板
			},
			loading: false,
			showForm: false,
			params: {
				orderBy: 'createTime',
				asc: 'false' // true 升序，false 降序
			},
			pageOption: {
				layout: 'total, prev, pager, next, jumper',
				pageSize: 20,
				// hideOnSinglePage: true,
				position: 'center',
				current: 1,
				pageNum: 1
			},
			formTitle: '查看',
			formId: '',
			cmsmodelCodeObj: {}, // 文章模型编码
			codesObj: {} //批量数据字典
		};
	},
	computed: {
		thead() {
			return [
				{
					title: '姓名',
					align: 'center',
					showOverflowTooltip: true,
					field: 'name'
				},
				{
					title: '性别',
					align: 'center',
					width: 60,
					field: 'sexTxt'
				},
				// {
				// 	title: '身份证',
				// 	align: 'center',
				// 	showOverflowTooltip: true,
				// 	field: 'idCard'
				// },
				// {
				// 	title: '出生年月',
				// 	align: 'center',
				// 	showOverflowTooltip: true,
				// 	width: 100,
				// 	field: 'birthday'
				// },
				{
					title: '年龄',
					align: 'center',
					showOverflowTooltip: true,
					width: 60,
					field: 'age'
				},
				{
					title: '民族',
					align: 'center',
					field: 'nationTxt',
					showOverflowTooltip: true
				},
				// {
				// 	title: '农商行卡号',
				// 	align: 'center',
				// 	field: 'bankNumber'
				// },
				{
					title: '家庭住址',
					align: 'center',
					showOverflowTooltip: true,
					field: 'homeAddress'
				},
				{
					title: '学历',
					align: 'center',
					showOverflowTooltip: true,
					field: 'educationTxt'
				},
				{
					title: '社保',
					align: 'center',
					field: 'insurance'
				},
				{
					title: '岗位',
					align: 'center',
					showOverflowTooltip: true,
					field: 'post'
				},
				{
					title: '联系电话',
					align: 'center',
					showOverflowTooltip: true,
					width: 110,
					field: 'phone'
				},
				{
					title: '进入后勤时间',
					align: 'center',
					width: 110,
					showOverflowTooltip: true,
					field: 'joinDate'
				},
				{
					title: '合同签订时间',
					align: 'center',
					width: 110,
					showOverflowTooltip: true,
					field: 'contractSigningTime'
				},
				{
					title: '状态',
					align: 'center',
					width: 70,
					showOverflowTooltip: true,
					field: 'stateTxt'
				},
				{
					title: '操作',
					type: 'handle',
					template: '',
					width: 180,
					align: 'center',
					fixed: 'right',
					events: [
						{ code: 'view', text: '查看', icon: 'el-icon-view' },
						{ code: 'view', text: '编辑', icon: 'el-icon-edit' },
						{ code: 'view', text: '删除', icon: 'el-icon-delete' }
					]
				}
			];
		},
		toolbar() {
			return [
				{
					type: 'button',
					contents: [
						{
							text: '新增',
							code: 'add',
							icon: 'el-icon-circle-plus-outline',
							type: 'primary'
						},
						{
							text: '导出',
							exportXls: true,
							icon: 'es-icon-daochu',
							event: () => {
								window.open(host + this.basics.download, '_self');
							}
						},
						{
							text: '导入',
							upload: true,
							icon: 'el-icon-upload',
							action: host + this.basics.importExcel
						},
						{
							text: '下载模板',
							exportXls: true,
							icon: 'el-icon-download',
							event: () => {
								window.open(host + this.basics.getTemplate, '_self');
							}
						}
					]
				},
				{
					type: 'search',
					reset: true,
					contents: [{ type: 'text', name: 'keyword', placeholder: '请输入关键字查询' }]
				}
			];
		}
	},
	created() {
		const codes = 'member_nation,hq_person_education,hq_person_state';
		this.$utils.findSysCodeList(codes).then(res => {
			this.codesObj = res;
		});
	},
	methods: {
		//表格行高
		tableRowClassName({ row, rowIndex }) {
			let styleRes = { height: '54px !important' };
			return styleRes;
		},
		inputChange(key, value) {},
		//操作按钮点击事件
		btnClick(res) {
			let text = res.handle.text;
			this.formTitle = text;
			this.formId = res?.row?.id || '';
			switch (text) {
				case '查看':
					this.showForm = true;
					break;
				case '编辑':
					this.showForm = true;
					break;
				case '新增':
					this.showForm = true;
					break;
				case '删除':
					this.$confirm('确定删除该数据？', '提示', {
						confirmButtonText: '确定',
						cancelButtonText: '取消',
						type: 'warning'
					}).then(() => {
						this.$request({
							url: this.basics.delete,
							data: {
								id: this.formId
							},
							method: 'POST'
						}).then(res => {
							if (res.rCode === 0) {
								this.$message.success(res.msg);
								this.$refs.table.reload();
							} else {
								this.$message.error(res.msg);
							}
						});
					});
					break;
				default:
					break;
			}
		},

		refreshData() {
			this.$refs.table.reload();
		}
	}
};
</script>

<style scoped lang="scss">
.content {
	width: 100%;
	height: 100%;
}
</style>
