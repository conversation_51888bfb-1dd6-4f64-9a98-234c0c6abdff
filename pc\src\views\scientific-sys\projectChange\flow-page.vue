<template>
	<BasicInfo
		v-if="formId && contentsKey"
		:id="formId"
		:basics="basics"
		:title="title"
		:form-data-n="formDataN"
		:contents-key="contentsKey"
		@visible="$emit('update:visible', false)"
	/>
</template>

<script>
import BasicInfo from '../components/project-info/basic-info.vue';
export default {
	components: {
		BasicInfo
	},
	props: {
		title: {
			type: String,
			default: '查看变更'
		}
	},
	data() {
		return {
			basics: {
				info: '/ybzy/projectBaseInfo/getChangeInfo',
				edit: '/ybzy/projectBaseInfo/projectChange',
				flowTypeCode: 'change',
				defaultProcessKey: 'changeManage'
			},
			contentsKey: '',
			formDataN: {}, //项目变更表单
			queryData: {}
		};
	},
	computed: {
		formId() {
			return this.$route.query?.recordId || this.$route.query?.recordid || this.$uuidv4();
		}
	},
	mounted() {
		this.getPorjectInfo();
	},
	methods: {
		async getPorjectInfo() {
			let loading = this.load('加载中...');
			try {
				let { rCode, msg, results } = await this.$.ajax({
					url: this.basics.info,
					method: 'get',
					loading,
					params: {
						id: this.formId
					}
				});

				if (rCode == 0) {
					let obj = JSON.parse(results.changeProjectInfo);
					this.formDataN = { ...results, ...obj };
					console.log(this.formDataN, 123);
					this.contentsKey = this.contentsKeyTool(obj.projectClassify);
				} else {
					this.$message.error(msg);
				}
			} catch (error) {
				console.error('>>>error', error);
			}
		},
		// 页面加载
		load(text) {
			return this.$.loading(this.$loading, text);
		},
		contentsKeyTool(projectClassify) {
			// LENTHWAYS("lengthWays", "纵向项目",0),
			// CROSSWISE("crosswise", "横向项目",1),
			// COLLEGE("college", "院级项目",2),
			projectClassify = Number(projectClassify);
			let contentsKey = '';
			switch (projectClassify) {
				case 0:
					contentsKey = 'verticalProject'; // 纵向项目
					break;
				case 1:
					contentsKey = 'crosswiseProject'; // 横向项目
					break;
				case 2:
					contentsKey = 'instituteProject'; // 院级项目
					break;
			}
			return contentsKey;
		}
	}
};
</script>

<style></style>
