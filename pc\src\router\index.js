import Vue from 'vue';
import VueRouter from 'vue-router';
import $ from 'eoss-ui/lib/utils/util';
import request from 'eoss-ui/lib/utils/http';
import children from './children.js';
import scientificSys from './scientific-sys.js';
import openWindow from './open-window.js';
import vocationalEducationHall from './vocational-education-hall.js';
import humanResource from './human_resource.js';
import { alumniUrl } from '@/config';

// const modulesFiles = require.context('./modules', true, /\.js$/);
// // 自动引入module包
// export const modules = modulesFiles.keys().reduce((modules, modulePath) => {
// 	const moduleRouter = modulesFiles(modulePath);
// 	modules = modules.concat(moduleRouter.default);
// 	return modules;
// }, []);

Vue.use(VueRouter);
let routes = [
	{
		path: '/',
		redirect: '/login'
	},
	{
		path: '/404',
		name: '404',
		title: '',
		component: resolve => require(['@/views/public/Error.vue'], resolve)
	},
	{
		path: '/login',
		title: '',
		name: 'login',
		component: resolve => require(['@/views/Login.vue'], resolve)
	},
	{
		path: '/main',
		title: '',
		name: 'main',
		component: resolve => require(['@/views/Main.vue'], resolve),
		children: []
	},
	{
		path: '/hqRepairReportCygn',
		title: '报修管理-常用功能',
		name: 'hqRepairReportCygn',
		component: resolve => require(['@/views/maintain/application/index.vue'], resolve)
	},
	// {
	// 	path: '/sys/labor-union-home',
	// 	title: '工会首页',
	// 	name: 'labor-union-home',
	// 	component: resolve => require(['@/views/sys/labor-union-home.vue'], resolve)
	// },
	...vocationalEducationHall,
	...openWindow,
	...humanResource

	// ...modules
];

if (window.__POWERED_BY_WUJIE__) {
	routes = routes.concat(children);
} else {
	const childrenArr = [...children, ...scientificSys, ...humanResource];
	childrenArr.forEach(item => {
		if (item?.openWindow) {
			routes.push(item);
		} else {
			routes[3].children.push(item);
		}
	});
	// routes[3].children = [...children, ...scientificSys];
}
const router = new VueRouter({
	//mode: 'history', // 去掉链接中的#
	routes
});
router.beforeEach((to, from, next) => {
	console.log(to, from);
	if (from.fullPath == '/home' && to.fullPath == '/login') {
		window.open(`${alumniUrl}/project-ybzy/ybzy_zhxy/index.html#/login`, '_self');
	}
	$.isLogged({ to, from, next, request });
});
const originalPush = VueRouter.prototype.push;
VueRouter.prototype.push = function push(location) {
	return originalPush.call(this, location).catch(err => err);
};
export default router;
