<!--
 @desc: 科研管理系统 立项项目 中期检查 
 @author: WH
 @date: 2023/11/15
 -->
<template>
	<main>
		<es-data-table
			ref="table"
			style="width: 100%"
			:thead="thead"
			:page="true"
			:url="tableUrl"
			:param="param"
			:toolbar="toolbar"
			:response="func"
			method="get"
			close
			@btnClick="btnClick"
		></es-data-table>

		<!-- height="dialogHeight" 不能被识别到具体高度 会根据内容自动撑开 -->
		<es-dialog :title="title" :visible.sync="visible" size="full" :drag="false" class="is-dialog">
			<Detail v-if="visible" :id="row.id" open-type="look" />
		</es-dialog>
		<es-dialog :title="title" :visible.sync="visibleFlow" size="md" :drag="false">
			<flow v-if="visibleFlow" :row-id="row.id" @handleSuccess="handleSuccess"></flow>
		</es-dialog>
	</main>
</template>

<script>
import { getInterimList, getFlowFormInfo } from '@/api/scientific-sys.js';
import $ from 'eoss-ui/lib/utils/util';
import flow from './entryPersonFlow.vue';
import Detail from '../detail.vue';

export default {
	name: 'ApprovalList',
	components: { flow, Detail },
	data() {
		return {
			tableUrl: getInterimList,
			param: {
				type: 0
			},
			row: {},
			toolbar: [
				// {
				// 	type: 'button',
				// 	contents: [
				// 		{
				// 			text: '新增',
				// 			type: 'primary'
				// 		},
				// 		{
				// 			text: '导入',
				// 			//  action: '/gz-property/easyExcel/importExcel',
				// 			code: 'cform_picture_control',
				// 			ownId: 'f3e28bd96ea2b32b361bd3e4690a1f6',
				// 			httpRequest: () => {
				// 				console.log(112);
				// 			},
				// 			type: 'default'
				// 		}
				// 	]
				// },

				{
					type: 'filter',
					contents: [
						{
							type: 'text',
							label: '项目名称',
							name: 'projectName',
							placeholder: '',
							col: 3
						},
						{
							type: 'text',
							label: '所在部门/学院',
							name: 'declareOrgName',
							placeholder: '',
							col: 3
						},
						{
							type: 'text',
							label: '申报人 ',
							name: 'declareUserName',
							placeholder: '',
							col: 3
						},
						{
							type: 'select',
							label: '项目分类',
							placeholder: '',
							name: 'projectClassify',
							sysCode: 'project_classify',
							col: 3
						},
						{
							name: 'projectInitiationTime',
							disabled: this.reissue,
							col: 3,
							label: '立项时间',
							type: 'daterange',
							unlinkPanels: true,
							value: ''
						},
						{
							name: 'updateTime',
							disabled: this.reissue,
							col: 3,
							label: '更新时间',
							type: 'daterange',
							unlinkPanels: true,
							value: ''
						},
						/* 						{
							type: 'text',
							label: '立项编号',
							name: 'projectNumber',
							placeholder: '',
							col: 3
						},
						{
							type: 'text',
							label: '所属任务通知 ',
							name: 'taskName',
							placeholder: '',
							col: 3
						}, */

						{
							type: 'select',
							label: '项目状态',
							name: 'projectStatus',
							sysCode: 'project_status',
							col: 3

							//value: '1',
							//autoComplete: true,
							//url:'../json/select.json',
							// data: [
							// 	{
							// 		value: '0',
							// 		name: '已立项'
							// 	},
							// 	{
							// 		value: '1',
							// 		name: '中期验收'
							// 	},
							// 	{
							// 		value: '2',
							// 		name: '中止'
							// 	},
							// 	{
							// 		value: '3',
							// 		name: '终止'
							// 	},
							// 	{
							// 		value: '4',
							// 		name: '已完成'
							// 	}
							// ],
							// verify: 'required',
						}
					]
				}
			],
			// 弹窗
			visible: false,
			visibleFlow: false,
			// 当前id
			title: '',
			thead: [
				{
					title: '项目编号',
					field: 'projectNumber',
					align: 'left',
					width: 180,
					showOverflowTooltip: true
					// fixed: true
				},
				{
					title: '项目名称',
					field: 'projectName',
					width: 220,
					align: 'left'
				},
				{
					title: '项目分类',
					field: 'projectClassifyTxt',
					align: 'center',
					width: 150
					// required: true,
					// filterable: true,
					// allowCreate: true,
					// defaultFirstOption: true,
					// sysCode: 'project_type',
					// strict: false,
					// config: { controls: false }
					// render: (h, params) => {
					// 	let key = ['自然', '人文', '教研'];
					// 	return h('p', {}, key[Number(params.row.projectType)]);
					// }
				},
				{
					title: '更新时间',
					field: 'updateTime',
					align: 'center',
					width: 180,
					render: (h, params) => {
						// console.log("222222222",params.row.updateTime.substring(0,params.row.updateTime.Length-9));
						// console.log("333333333",params.row.updateTime);
						let updataTimeStr = '';
						if (params.row.updateTime) updataTimeStr = params.row.updateTime.substring(0, 10);
						return h('p', {}, updataTimeStr);
					}
				},
				{
					title: '申报人',
					field: 'declareUserName',
					align: 'center'
				},
				{
					title: '申报人电话',
					field: 'declarePhone',
					align: 'center',
					width: 150
				},
				{
					title: '所在部门/学院',
					field: 'declareOrgName',
					align: 'center',
					width: 180
				},
				/* 				{
					title: '立项编号',
					field: 'projectCode',
					align: 'center',
					width: 180
				}, */
				{
					title: '项目状态',
					field: 'projectStatusTxt',
					align: 'center',
					render: (h, param) => {
						return h(
							'p',
							{},
							param.row.projectChangeStatus == 1
								? param.row.projectStatusTxt + '（变更中）'
								: param.row.projectStatusTxt
						);
					}
				},
				// {
				// 	title: '项目状态',
				// 	field: 'projectStatus',
				// 	// width: '220',
				// 	align: 'center',
				// 	render: (h, params) => {
				// 		let key = ['已立项', '中检审核', '中止', '终止', '已完成'];
				// 		return h('p', {}, key[Number(params.row.companyStatus)]);
				// 	}
				// },

				{
					title: '操作',
					type: 'handle',
					width: '150',
					template: '',
					fixed: 'right',
					align: 'center',

					events: [
						{
							text: '详情',
							btnType: 'look'
						},
						{
							text: '中检申请',
							btnType: 'edit',
							// rules: rows => [1].includes(rows.projectStatus)
							rules: rows => rows.projectStatus == 1 && rows.projectChangeStatus == 0
						}
					]
				}
			]
		};
	},

	methods: {
		handleSuccess() {
			this.$refs.table.reload();
		},
		//row=null  考虑到toolbar按钮不存在row
		btnClick({ handle, row = null }) {
			// console.log(">>>", this.$router.resolve({ name: "allotSubmitList" }));
			let { text, btnType } = handle;
			this.title = text;
			// if (row) {
			// 	this.formId = row.id;
			// 	this.formMonths = row.months;
			// 	this.formYears = row.years;
			// 	this.historyId = row.dataUnique;
			// }
			let btnTask = {
				look: () => {
					// this.row = row;
					// this.lookFn();
					this.row = row;
					this.visible = true;
				},
				edit: () => {
					// this.$router.push({
					// 	path: 'basic',
					// 	query: { id: row.id }
					// });
					// this.showType = 'edit';
					this.row = row;
					// this.openWindow();
					this.visibleFlow = true;
				},
				del: () => {
					let _this = this;
					this.$confirm('是否删除?', '提示', {
						confirmButtonText: '确定',
						cancelButtonText: '取消',
						type: 'warning'
					})
						.then(() => {
							this.delPorjectByid(row.id);
						})
						.catch(() => {});
				}
			};
			btnTask[btnType]();
		},
		async lookFn() {
			const loading = this.load('加载中...');
			let { results, rCode } = await this.$.ajax({
				url: getFlowFormInfo,
				params: {
					projectId: this.row.id
				}
			});

			if (rCode == 0) {
				this.visible = true;
			}

			loading.close();
		},

		// 页面加载
		load(text) {
			return $.loading(this.$loading, text);
		},
		func(res) {
			let { type, data } = res;
			// console.log('>>>res', res);
			return data;
		}
	}
};
</script>
<style lang="scss" scoped>
main {
	width: 100%;
	height: 100%;
}
.is-dialog > ::v-deep .el-dialog {
	height: 100%;
}
</style>
