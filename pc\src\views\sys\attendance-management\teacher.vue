<template>
	<div class="main">
		<!--  头部  -->
		<div class="main-header">
			<div class="title">
				<img class="title-img" src="@ast/images/sys/card.png" alt="" />
				<div>考勤统计</div>
			</div>
			<es-select
				v-model="value"
				placeholder="请选择"
				:data="options"
				value-type="object"
				@change="handleChange"
			></es-select>
		</div>
		<!--  卡片tab切换  -->
		<div class="card-box">
			<mini-card v-for="(item, index) in card" :key="index" :card-data="item" />
		</div>
		<!--  内容区域  -->
		<div class="main-center">
			<!--  内容-左  -->
			<teacherModule></teacherModule>
			<!--  内容-右  -->
			<calendarModule></calendarModule>
		</div>
	</div>
</template>

<script>
import MiniCard from '@/components/show/mini-card.vue';
import calendarModule from '@/views/sys/attendance-management/components/calendar-module';
import teacherModule from '@/views/sys/attendance-management/components/teacher-module';
export default {
	name: 'AttendanceManagementPersonnel',
	components: { MiniCard, calendarModule, teacherModule },
	data() {
		return {
			/**考勤选择时间*/
			options: [
				{
					value: '1',
					label: '本月'
				},
				{
					value: '2',
					label: '本周'
				},
				{
					value: '3',
					label: '今日'
				}
			],
			/**考勤默认时间*/
			value: {
				value: '1'
			},
			/**考勤统计数据展示*/
			card: [
				{
					img: require('@/assets/images/sys/card.png'),
					title: '应签到人数',
					unit: '人',
					num: '0'
				},
				{
					img: require('@/assets/images/sys/card.png'),
					title: '实签到人数',
					unit: '人',
					num: '0'
				},
				{
					img: require('@/assets/images/sys/card.png'),
					title: '请假人数',
					unit: '人',
					num: '0'
				},
				{
					img: require('@/assets/images/sys/card.png'),
					title: '旷课人数',
					unit: '人',
					num: '0'
				}
			]
		};
	},
	methods: {
		/**年月切换下拉选择*/
		handleChange(val) {
			console.log(val);
		}
	}
};
</script>

<style lang="scss" scoped>
@import '@ast/style/public.scss';
.main {
	height: 100%;
	width: 100%;
	background: #f0f2f5;
	padding: 12px;
	overflow-y: scroll;
	/**头部*/
	&-header {
		@include flexBox(space-between);
		background: #fff;
		padding: 6px 20px;
		border-radius: 8px;
		font-size: 16px;
		font-weight: 550;
	}
	/**中间部分*/
	&-center {
		display: flex;
	}
	/**穿透修改卡片组件*/
	.card-box {
		@include flexBox(space-between);
		width: 100%;
		margin: 12px 0;
		background: #f0f2f5;

		.card {
			width: 24.5% !important;
		}
	}
}
.title {
	display: flex;
	justify-content: center;
	align-items: center;
	font-size: 16px;
	font-weight: 550;
	&-img {
		width: 22px;
		height: 22px;
		margin-right: 8px;
	}
}
</style>
