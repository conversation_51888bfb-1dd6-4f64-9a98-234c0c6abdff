/**
 * @Author: <PERSON><PERSON> <EMAIL>
 * @Date: 2023-01-05 08:40:42
 * @LastEditors: <PERSON><PERSON> <EMAIL>
 * @LastEditTime: 2023-11-20 13:43:11
 * @FilePath: /ybzy-screen/src/utils/request-xodb.js
 * @Description:
 */
import axios from 'axios';
import { Message } from 'eoss-element';
// http://***********:8081
const baseURL = origin.includes('localhost') || origin.includes('192.168') ? '/api' : '';
const service = axios.create({
	baseURL: baseURL,
	timeout: 10000
});

// 请求对象
service.interceptors.request.use(
	config => {
		// 设置token
		// if (getToken()) {
		// 	config.headers['X-Token'] = getToken();
		// }

		if (config.method === 'post') {
			if (config.type === 'JSON') {
				config.headers['Content-Type'] = 'application/json;charset=utf-8';
			} else {
				config.headers['Content-Type'] = 'application/x-www-form-urlencoded;charset=UTF-8;';
			}
		}
		return config;
	},
	error => {
		console.log(error);
		return Promise.reject(error);
	}
);
// 响应对象
service.interceptors.response.use(
	response => {
		const res = response.data;
		if (res.rCode !== 0) {
			Message({
				message: res.msg || 'Error',
				type: 'error',
				duration: 2 * 1000
			});
		} else {
			return res.results;
		}
	},
	error => {
		console.log('err' + error);
		Message({
			message: error.message,
			type: 'error',
			duration: 5 * 1000
		});
		return Promise.reject(error);
	}
);

export default service;
