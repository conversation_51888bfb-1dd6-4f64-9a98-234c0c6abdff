<!--
 @desc:基本信息
 @author: WH
 @date: 2023/11/14
 -->
<template>
	<!-- :key="formData.type" -->
	<ProcesPage
		v-if="formData.type"
		:flow-data-props="{
			appId: formData.appId, // 流程图id
			businessId: id, // 有 businessId-业务id 则为发起节点
			flowTypeCode: formData.type === '1' ? basics.flowTypeCode2 : basics.flowTypeCode,
			defaultProcessKey: basics.defaultProcessKey,
			isEdit: !formReadonly, // 是否编辑
			isFlow: !formReadonly // 是否显示右边流程功能
		}"
		page-width="70"
		@subFun="save"
		@handleSuccess="handleSuccess"
	>
		<div class="main">
			<el-button
				v-if="formData.status === '2'"
				class="btn-view"
				type="text"
				icon="es-icon-yulan"
				@click="visiblePrint = true"
			>
				打印预览
			</el-button>
			<div class="basic-info">
				<title-card title="请假信息"></title-card>
				<es-form
					:key="formData.id || Date.now()"
					ref="formRef"
					label-width="140px"
					:model="formData"
					:contents="contents"
					:submit="false"
					:readonly="formReadonly"
				></es-form>
			</div>
			<div v-if="formData.type === '2'" class="table-info">
				<div class="btn-box">
					<el-button
						:disabled="formData.status !== '2'"
						type="warning"
						size="mini"
						@click="goToBooking"
					>
						去订票
					</el-button>
					<el-tooltip v-if="formData.status !== '2'" class="item" effect="dark" placement="top">
						<i class="el-icon-question" style="font-size: 14px; vertical-align: middle"></i>
						<div slot="content">
							<p>订票需要在申请审核完成以后才能操作</p>
						</div>
					</el-tooltip>
				</div>

				<title-card title="订票信息"></title-card>
				<div>
					<el-tabs v-model="activeOrderType" @tab-click="handleOrderTypeChange">
						<el-tab-pane label="火车票" :name="ORDER_TYPES.TRAIN"></el-tab-pane>
						<el-tab-pane label="机票" :name="ORDER_TYPES.TICKET"></el-tab-pane>
						<el-tab-pane label="酒店" :name="ORDER_TYPES.HOTEL"></el-tab-pane>
						<el-tab-pane label="用车" :name="ORDER_TYPES.USECAR"></el-tab-pane>
					</el-tabs>
					<es-data-table
						ref="table"
						:thead="thead"
						:page="false"
						full
						:data="tableData"
						method="get"
						style="width: 100%"
						v-loading="orderLoading"
						element-loading-text="加载订单数据中..."
					></es-data-table>
				</div>
			</div>
			<div v-if="formData.type === '2'" class="rich-text">
				<h4>因公外出说明：</h4>
				<p>
					1、伙食补助费。市境内到翠屏区 5
					个街道办事处（大观楼街道、合江门街道、西郊街道、安阜街道、象鼻街道）、叙州区 4
					个街道（镇）办事处（南岸街道、赵场街道、南广镇、柏溪街道（含城北新区）、临港经开区 3
					个街道办事处（沙坪街道、白沙街道、双城街道）、宜宾机场辖区内执行公务不实行补助。市内 80
					元/人·天，市外省内（甘孜、阿坝、凉山）120 元/人·天，市外省内其他地区 100
					元/人·天；省外（西藏、青海、新疆）120 元/人·天，省外其他地区 100 元/人·天。
				</p>
				<p>2、公杂费。指工作人员因公出差期间发生的城市内交通费、通讯费等费用。</p>
				<p>
					3、工作人员出差结束应及时办理报销手续，报销时应提供外出报批报备表，相关会议通知或文件以及消费发票、公务卡刷卡单等凭证。
				</p>
				<p>4、住宿费、机票、会务费支出等按规定用公务卡结算。</p>
			</div>
		</div>
		<es-dialog
			title="打印文件"
			:visible.sync="visiblePrint"
			:show-scale="false"
			:drag="false"
			size="full"
		>
			<div class="pdf-box">
				<div id="printMe" class="pdf-content">
					<h2 class="title">
						宜宾职业技术学院{{ formData.type === '2' ? '外出' : '请假' }}报批报备表
					</h2>
					<div>
						<es-form
							:key="id"
							ref="formRef"
							label-width="140px"
							height="100%"
							:model="formData"
							table
							:contents="contentsPrint"
							:submit="false"
							:readonly="true"
						></es-form>
					</div>
					<h3 class="opinion-title">审核意见</h3>
					<el-timeline class="opinion-content" :reverse="reverse">
						<el-timeline-item
							v-for="(activity, a) in formData.logs"
							:key="a"
							:timestamp="`${activity.wfUserName}：${activity.remark}`"
						>
							<!-- {{ `${activity.wfTypeName}  ${activity.createTime}` }} -->
							{{ activity.wfTypeName }}
						</el-timeline-item>
					</el-timeline>
				</div>
				<div class="btn-print">
					<el-button
						type="primary"
						plain
						size="small"
						icon="el-icon-download"
						@click="exportToPdf()"
					>
						下载PDF
					</el-button>
				</div>
			</div>
		</es-dialog>
	</ProcesPage>
</template>

<script>
import ProcesPage from '@/components/process-page.vue';
import titleCard from '@cpt/scientific-sys/title-card.vue';
import html2canvas from 'html2canvas';
import jsPDF from 'jspdf';

export default {
	components: { ProcesPage, titleCard },
	props: {
		id: {
			type: String,
			default: ''
		},
		title: {
			type: String,
			default: '查看' // 查看 编辑 新增
		},
		basics: {
			type: Object,
			default: () => {
				return {
					info: '/ybzy/paAcademicPaper/info',
					save: '/ybzy/paAcademicPaper/save',
					edit: 'ybzy/paAcademicPaper/update',
					flowTypeCode: 'projectAchievement_etipnuuezknypsv', // 流程code
					defaultProcessKey: 'projectAchievement_etipnuuezknypsv' // 默认关联流程 key
				};
			}
		}
	},
	data() {
		return {
			account: JSON.parse(localStorage.getItem('loginUserInfo')).code,
			visiblePrint: false,
			formReadonly: false,
			basicInfo: {},
			rowData: [],
			orderLoading: false, // 订单列表加载状态

			formData: { students: [], teachers: [] },
			row: {},
			// 订单类型
			ORDER_TYPES: {
				TRAIN: 'FcOutApi_TrainOrder',
				TICKET: 'FcOutApi_TicketOrder',
				HOTEL: 'FcOutApi_HotelOrder',
				USECAR: 'FcOutApi_UsecarOrder'
			},
			activeOrderType: 'FcOutApi_TrainOrder', // 当前选中的订单类型
			// 列表相关
			tableData: [],
			startingLoading: false,
			startingOptions: [],
			destinationLoading: false,
			destinationOptions: []
		};
	},
	computed: {
		contents() {
			const formReadonly = this.formReadonly;
			const formData = this.formData;

			let arr = [
				{
					name: 'number',
					placeholder: '系统自动生成',
					label: '申请单号',
					disabled: true,
					col: 6,
					rules: {
						required: false // 不需要前端验证，但确保字段存在
					}
				},
				{
					name: 'name',
					placeholder: '请输入',
					label: '申请人',
					rules: {
						required: !formReadonly,
						message: '请输入',
						trigger: 'blur'
					},
					col: 6
				},
				{
					name: 'account',
					placeholder: '请输入',
					label: '教工号',
					rules: {
						required: !formReadonly,
						message: '请输入',
						trigger: 'blur'
					},
					col: 6
				},
				{
					name: 'leaveType',
					placeholder: '请选择',
					col: 6,
					label: '请假类型',
					hide: formData.type !== '1',
					type: 'select',
					rules: {
						required: !formReadonly,
						message: '请选择',
						trigger: 'blur'
					},
					sysCode: 'ybzy_leave_select'
				},

				{
					name: 'deptName',
					placeholder: '请输入',
					label: '部门',
					rules: {
						required: !formReadonly,
						message: '请输入',
						trigger: 'blur'
					},
					col: 6
				},
				{
					name: 'post',
					placeholder: '请输入',
					label: '职称/职务',
					hide: formData.type !== '2',
					rules: {
						required: !formReadonly,
						message: '请输入',
						trigger: 'blur'
					},
					col: 6
				},
				{
					name: 'phone',
					placeholder: '请输入',
					label: '联系电话',
					rules: {
						required: !formReadonly,
						message: '请输入',
						trigger: 'blur'
					},
					col: 6
				},

				{
					name: 'members',
					label: '同行人员',
					type: 'selector',
					types: ['person'],
					placeholder: '点击选择',
					col: 12,
					//readonly: true,
					//multiple: false,
					hide: formData.type !== '2',
					value: [
						{
							showid: 'sf5454asfbfc85fb4385465456b5641',
							showname: '李'
						}
					]
					// rules: {
					// 	required: true,
					// 	message: '请选择',
					// 	trigger: 'blur'
					// }
				},
				{
					name: 'leaveType',
					placeholder: '请选择',
					col: 12,
					label: '出差类型',
					type: 'select',
					hide: formData.type !== '2',
					rules: {
						required: !formReadonly,
						message: '请选择',
						trigger: 'blur'
					},
					sysCode: 'leave_business_trip_type'
				},
				{
					name: 'vehicle',
					placeholder: '请选择',
					col: 12,
					label: '交通工具',
					type: 'radio',
					hide: formData.type !== '2',
					rules: {
						required: !formReadonly,
						message: '请选择',
						trigger: 'blur'
					},
					sysCode: 'leave_application_vehicle_code'
				},
				{
					name: 'startingType',
					placeholder: '请选择',
					col: 6,
					label: '出发地类型',
					type: 'select',
					hide: formData.type !== '2',
					data: [
						{
							label: '国内',
							value: 0
						},
						{
							label: '国际',
							value: 1
						}
					],
					'label-key': 'label',
					'value-key': 'value',
					events: {
						change: value => {
							this.formData.startingCodeTxt = '';
							this.formData.startingCode = '';
							this.queryCityList('', 'starting');
						}
					}
				},
				{
					name: 'startingCodeTxt',
					placeholder: '请选择出发地',
					col: 6,
					label: '出发地',
					type: 'select',
					hide: formData.type !== '2',
					filterable: true,
					remote: true,
					'remote-method': val => {
						this.queryCityList(val, 'starting');
					},
					loading: this.startingLoading,
					clearable: true,
					data: this.startingOptions,
					'label-key': 'label',
					'value-key': 'value',
					events: {
						change: (type, value) => {
							// 选择变化时的处理
							// 找到选中的城市，获取名称
							const selectedCity = this.startingOptions.find(item => item.value === value);
							if (selectedCity) {
								this.formData.startingCodeTxt = selectedCity.label;
								this.formData.startingCode = selectedCity.value;
							} else {
								this.formData.startingCodeTxt = '';
								this.formData.startingCode = '';
							}
						}
					},
					rules: {
						required: !formReadonly,
						message: '请选择',
						trigger: 'blur'
					}
				},
				{
					name: 'destinationType',
					placeholder: '请选择',
					col: 6,
					label: '目的地类型',
					type: 'select',
					hide: formData.type !== '2',
					data: [
						{
							label: '国内',
							value: 0
						},
						{
							label: '国际',
							value: 1
						}
					],
					'label-key': 'label',
					'value-key': 'value',
					events: {
						change: value => {
							this.formData.destinationCodeTxt = '';
							this.formData.destinationCode = '';
							this.queryCityList('', 'destination');
						}
					}
				},
				{
					name: 'destinationCode',
					placeholder: '请选择目的地',
					col: 6,
					label: '目的地',
					type: 'select',
					key: 'destinationCode',
					hide: formData.type !== '2',
					filterable: true,
					remote: true,
					multiple: true,
					'remote-method': val => {
						if (!val) return;
						this.queryCityList(val, 'destination');
					},
					loading: this.destinationLoading,
					clearable: true,
					data: this.destinationOptions,
					'label-key': 'label',
					'value-key': 'value',
					events: {
						change: (type, value) => {
							// 选择变化时的处理
							// 找到选中的城市，获取名称
							if (value && value.length) {
								// 将选中的城市名称和代码转换为逗号分隔的字符串
								const selectedCities = value.map(code => {
									const city = this.destinationOptions.find(item => item.value === code);
									return city ? city.label : code;
								});
								this.formData.destinationCodeTxt = selectedCities.join(',');
								this.formData.destinationCode = value.join(',');
							} else {
								this.formData.destinationCodeTxt = '';
								this.formData.destinationCode = '';
							}
						}
					},
					rules: {
						required: !formReadonly,
						message: '请选择',
						trigger: 'blur'
					}
				},
				{
					name: 'address',
					placeholder: '请输入',
					label: '详细地址',
					hide: formData.type !== '2',
					rules: {
						required: !formReadonly,
						message: '请输入',
						trigger: 'blur'
					},
					col: 6
				},
				{
					name: 'days',
					placeholder: '',
					label: formData.type === '1' ? '请假天数' : '外出天数',
					disabled: true,
					min: 0,
					col: 6
				},
				{
					name: 'reason',
					placeholder: '请输入',
					label: formData.type === '1' ? '请假原因' : '外出事由',
					type: 'textarea',
					'show-word-limit': true,
					maxlength: 255,
					// hide: formData.type !== '1',
					rules: {
						required: !formReadonly,
						message: '请输入',
						trigger: 'blur'
					},
					col: 12
				},

				{
					name: 'startDateTime',
					placeholder: '请选择',
					col: 4.5,
					type: 'date',
					label: formData.type === '1' ? '开始时间' : '离宜时间',
					format: 'yyyy-MM-dd',
					events: {
						change: (e, q) => {
							this.calculateLeaveDays();
						}
					},
					rules: {
						required: !formReadonly,
						message: '请选择',
						trigger: 'blur'
					}
				},

				{
					name: 'isStartAm',
					col: 1.5,
					type: 'radio',
					'value-key': 'value',
					'label-key': 'label',
					events: {
						change: (e, q) => {
							this.calculateLeaveDays();
						}
					},
					data: [
						{
							label: '上午',
							value: '1'
						},
						{
							label: '下午',
							value: '2'
						}
					]
				},
				{
					name: 'endDateTime',
					placeholder: '请选择',
					col: 4.5,
					type: 'date',
					label: formData.type === '1' ? '结束时间' : '返宜时间',
					format: 'yyyy-MM-dd',
					events: {
						change: (e, q) => {
							this.calculateLeaveDays();
						}
					},
					rules: {
						required: !formReadonly,
						message: '请选择',
						trigger: 'blur'
					}
				},

				{
					name: 'isEndAm',
					col: 1.5,
					type: 'radio',
					'value-key': 'value',
					'label-key': 'label',
					events: {
						change: (e, q) => {
							this.calculateLeaveDays();
						}
					},
					data: [
						{
							label: '上午',
							value: '1'
						},
						{
							label: '下午',
							value: '2'
						}
					]
				},
				{
					name: 'remark',
					placeholder: '请输入',
					label: '交接事宜',
					type: 'textarea',
					'show-word-limit': true,
					maxlength: 255,
					hide: formData.type !== '1',
					col: 12
				},

				{
					name: 'emergencyContact',
					placeholder: '请输入',
					label: '紧急联系人',
					hide: formData.type !== '1',
					col: 6
				},
				{
					name: 'fj',
					label: '附件',
					type: 'attachment',
					col: 12,
					code: 'job_dual_select_adjunct',
					preview: true,
					ownId: this.formData.id // 业务id
				}
			];

			return arr;
		},
		contentsPrint() {
			const formReadonly = this.formReadonly;
			const formData = this.formData;
			console.log(typeof formData.startingCode, typeof formData.startingType);

			let arr = [
				{
					name: 'number',
					placeholder: '系统自动生成',
					label: '申请单号',
					disabled: true,
					col: 6,
					rules: {
						required: false // 不需要前端验证，但确保字段存在
					}
				},
				{
					name: 'name',
					placeholder: '请输入',
					label: '申请人',
					rules: {
						required: !formReadonly,
						message: '请输入',
						trigger: 'blur'
					},
					col: 6
				},
				{
					name: 'account',
					placeholder: '请输入',
					label: '教工号',
					rules: {
						required: !formReadonly,
						message: '请输入',
						trigger: 'blur'
					},
					col: 6
				},
				{
					name: 'leaveType',
					placeholder: '请选择',
					col: 6,
					label: '请假类型',
					hide: formData.type !== '1',
					type: 'select',
					rules: {
						required: !formReadonly,
						message: '请选择',
						trigger: 'blur'
					},
					sysCode: 'ybzy_leave_select'
				},

				{
					name: 'deptName',
					placeholder: '请输入',
					label: '部门',
					rules: {
						required: !formReadonly,
						message: '请输入',
						trigger: 'blur'
					},
					col: 6
				},
				{
					name: 'post',
					placeholder: '请输入',
					label: '职称/职务',
					hide: formData.type !== '2',
					rules: {
						required: !formReadonly,
						message: '请输入',
						trigger: 'blur'
					},
					col: 6
				},
				{
					name: 'phone',
					placeholder: '请输入',
					label: '联系电话',
					rules: {
						required: !formReadonly,
						message: '请输入',
						trigger: 'blur'
					},
					col: 6
				},

				{
					name: 'members',
					label: '同行人员',
					type: 'selector',
					types: ['person'],
					placeholder: '点击选择',
					col: 12,
					//readonly: true,
					//multiple: false,
					hide: formData.type !== '2',
					value: [
						{
							showid: 'sf5454asfbfc85fb4385465456b5641',
							showname: '李'
						}
					]
					// rules: {
					// 	required: true,
					// 	message: '请选择',
					// 	trigger: 'blur'
					// }
				},
				{
					name: 'vehicle',
					placeholder: '请选择',
					col: 12,
					label: '交通工具',
					type: 'radio',
					hide: formData.type !== '2',
					rules: {
						required: !formReadonly,
						message: '请选择',
						trigger: 'blur'
					},
					sysCode: 'leave_application_vehicle_code'
				},
				{
					name: 'startingType',
					placeholder: '请选择',
					col: 6,
					label: '出发地类型',
					type: 'select',
					hide: formData.type !== '2',
					data: [
						{
							label: '国内',
							value: 0
						},
						{
							label: '国际',
							value: 1
						}
					],
					'label-key': 'label',
					'value-key': 'value',
					events: {
						change: value => {
							this.formData.startingCodeTxt = '';
							this.formData.startingCode = '';
							this.queryCityList('', 'starting');
						}
					}
				},
				{
					name: 'startingCodeTxt',
					placeholder: '请选择出发地',
					col: 6,
					label: '出发地',
					type: 'select',
					hide: formData.type !== '2',
					filterable: true,
					remote: true,
					'remote-method': val => {
						this.queryCityList(val, 'starting');
					},
					loading: this.startingLoading,
					clearable: true,
					data: this.startingOptions,
					'label-key': 'label',
					'value-key': 'value',
					events: {
						change: (type, value) => {
							// 选择变化时的处理
							// 找到选中的城市，获取名称
							const selectedCity = this.startingOptions.find(item => item.value === value);
							if (selectedCity) {
								this.formData.startingCodeTxt = selectedCity.label;
								this.formData.startingCode = selectedCity.value;
							} else {
								this.formData.startingCodeTxt = '';
								this.formData.startingCode = '';
							}
						}
					},
					rules: {
						required: !formReadonly,
						message: '请选择',
						trigger: 'blur'
					}
				},
				{
					name: 'destinationType',
					placeholder: '请选择',
					col: 6,
					label: '目的地类型',
					type: 'select',
					hide: formData.type !== '2',
					data: [
						{
							label: '国内',
							value: 0
						},
						{
							label: '国际',
							value: 1
						}
					],
					'label-key': 'label',
					'value-key': 'value',
					events: {
						change: value => {
							this.formData.destinationCodeTxt = '';
							this.formData.destinationCode = '';
							this.queryCityList('', 'destination');
						}
					}
				},
				{
					name: 'destinationCode',
					placeholder: '请选择目的地',
					col: 6,
					label: '目的地',
					type: 'select',
					key: 'destinationCode',
					hide: formData.type !== '2',
					filterable: true,
					remote: true,
					multiple: true,
					'remote-method': val => {
						if (!val) return;
						this.queryCityList(val, 'destination');
					},
					loading: this.destinationLoading,
					clearable: true,
					data: this.destinationOptions,
					'label-key': 'label',
					'value-key': 'value',
					events: {
						change: (type, value) => {
							// 选择变化时的处理
							// 找到选中的城市，获取名称
							if (value && value.length) {
								// 将选中的城市名称和代码转换为逗号分隔的字符串
								const selectedCities = value.map(code => {
									const city = this.destinationOptions.find(item => item.value === code);
									return city ? city.label : code;
								});
								this.formData.destinationCodeTxt = selectedCities.join(',');
								this.formData.destinationCode = value.join(',');
							} else {
								this.formData.destinationCodeTxt = '';
								this.formData.destinationCode = '';
							}
						}
					},
					rules: {
						required: !formReadonly,
						message: '请选择',
						trigger: 'blur'
					}
				},
				{
					name: 'address',
					placeholder: '请输入',
					label: '详细地址',
					hide: formData.type !== '2',
					rules: {
						required: !formReadonly,
						message: '请输入',
						trigger: 'blur'
					},
					col: 6
				},
				{
					name: 'days',
					placeholder: '',
					label: formData.type === '1' ? '请假天数' : '外出天数',
					min: 0,
					col: 6
				},

				{
					name: 'emergencyContact',
					placeholder: '请输入',
					label: '紧急联系人',
					hide: formData.type !== '1',
					col: 6
				},

				{
					name: 'startDateTimeTxt',
					placeholder: '请选择',
					col: 6,
					type: 'date',
					label: formData.type === '1' ? '开始时间' : '离宜时间',
					// format: 'yyyy-MM-dd',
					events: {
						change: (e, q) => {
							this.calculateLeaveDays();
						}
					},
					rules: {
						required: !formReadonly,
						message: '请选择',
						trigger: 'blur'
					}
				},
				{
					name: 'endDateTimeTxt',
					placeholder: '请选择',
					col: 6,
					type: 'date',
					label: formData.type === '1' ? '结束时间' : '返宜时间',
					// format: 'yyyy-MM-dd',
					events: {
						change: (e, q) => {
							this.calculateLeaveDays();
						}
					},
					rules: {
						required: !formReadonly,
						message: '请选择',
						trigger: 'blur'
					}
				},
				{
					name: 'reason',
					placeholder: '请输入',
					label: formData.type === '1' ? '请假原因' : '外出事由',
					type: 'textarea',
					'show-word-limit': true,
					maxlength: 255,
					// hide: formData.type !== '1',
					rules: {
						required: !formReadonly,
						message: '请输入',
						trigger: 'blur'
					},
					col: 12
				},
				{
					name: 'remark',
					placeholder: '请输入',
					label: '交接事宜',
					type: 'textarea',
					'show-word-limit': true,
					maxlength: 255,
					hide: formData.type !== '1',
					col: 12
				}
			];

			return arr;
		},
		// 根据当前选中的订单类型返回对应的表头配置
		thead() {
			// 根据订单类型添加特定字段
			let fields = [];

			switch (this.activeOrderType) {
				case this.ORDER_TYPES.TRAIN: // 火车票
					fields = [
						{
							title: '车票号',
							field: 'orderNo',
							align: 'center',
							fixed: false
						},
						{
							title: '乘车人',
							field: 'name',
							align: 'center',
							fixed: false
						},
						{
							title: '车次',
							field: 'trainCode',
							align: 'center',
							fixed: false
						},
						{
							title: '座位',
							align: 'center',
							field: 'zw',
							fixed: false
						},
						{
							title: '出发站',
							field: 'startCity',
							align: 'center',
							fixed: false
						},
						{
							title: '到达站',
							field: 'endCity',
							align: 'center',
							fixed: false
						},
						{
							title: '出发时间',
							field: 'startTime',
							align: 'center',
							fixed: false
						},
						{
							title: '到达时间',
							field: 'endTime',
							align: 'center',
							fixed: false
						},
						{
							title: '价格',
							field: 'amount',
							align: 'center',
							fixed: false
						},
						{
							title: '状态',
							field: 'status',
							align: 'center',
							fixed: false
						}
					];
					break;
				case this.ORDER_TYPES.TICKET: // 机票
					fields = [
						{
							title: '票号',
							field: 'ticketCode',
							align: 'center',
							fixed: false
						},
						{
							title: '乘机人',
							align: 'center',
							field: 'name',
							fixed: false
						},
						{
							title: '航班号',
							align: 'center',
							field: 'hbh',
							fixed: false
						},
						{
							title: '舱位信息',
							align: 'center',
							field: 'cw',
							fixed: false
						},
						{
							title: '出发机场',
							field: 'startCity',
							align: 'center',
							fixed: false
						},
						{
							title: '到达机场',
							field: 'endCity',
							align: 'center',
							fixed: false
						},
						{
							title: '起飞时间',
							field: 'qfsj',
							align: 'center',
							fixed: false
						},
						{
							title: '到达时间',
							field: 'ddsj',
							align: 'center',
							fixed: false
						},
						{
							title: '价格',
							field: 'amount',
							align: 'center',
							fixed: false
						},
						{
							title: '状态',
							field: 'status',
							align: 'center',
							fixed: false
						}
					];
					break;
				case this.ORDER_TYPES.HOTEL: // 酒店
					fields = [
						{
							title: '订单编号',
							field: 'ddbh',
							align: 'center',
							fixed: false
						},
						{
							title: '预定人',
							field: 'name',
							align: 'center',
							fixed: false
						},
						{
							title: '酒店名称',
							field: 'hotelName',
							align: 'center',
							fixed: false
						},
						{
							title: '房型',
							field: 'jdfx',
							align: 'center',
							fixed: false
						},
						{
							title: '入住人',
							align: 'center',
							field: 'passengers',
							minWidth: 100,
							fixed: false,
							render: (h, params) => {
								// 处理换行显示
								if (!params.row.passengers) return h('span', '-');
								const content = params.row.passengers.split('\n').map(text => {
									return h('div', text);
								});
								return h('div', content);
							}
						},
						{
							title: '房间数',
							field: 'fjsl',
							align: 'center',
							fixed: false
						},
						{
							title: '入住时间',
							field: 'startEndTime',
							align: 'center',
							fixed: false
						},
						{
							title: '价格',
							field: 'amount',
							align: 'center',
							fixed: false
						},
						{
							title: '状态',
							field: 'status',
							align: 'center',
							fixed: false
						}
					];
					break;
				case this.ORDER_TYPES.USECAR: // 用车
					fields = [
						{
							title: '预定人',
							align: 'center',
							field: 'name',
							fixed: false
						},
						{
							title: '乘客信息',
							align: 'center',
							field: 'passengers',
							fixed: false,
							render: (h, params) => {
								// 处理换行显示
								if (!params.row.passengers) return h('span', '-');
								const content = params.row.passengers.split('\n').map(text => {
									return h('div', text);
								});
								return h('div', content);
							}
						},
						{
							title: '车型',
							align: 'center',
							field: 'carInfo',
							fixed: false
						},
						{
							title: '出发地',
							field: 'startCity',
							align: 'center',
							fixed: false
						},
						{
							title: '目的地',
							field: 'endCity',
							align: 'center',
							fixed: false
						},
						{
							title: '用车时间',
							field: 'startTime',
							align: 'center',
							fixed: false
						},
						{
							title: '价格',
							field: 'amount',
							align: 'center',
							fixed: false
						},
						{
							title: '状态',
							field: 'status',
							align: 'center',
							fixed: false
						}
					];
					break;
				default:
					fields = [
						{
							title: '预定人',
							align: 'center',
							field: 'name',
							fixed: false
						},
						{
							title: '状态',
							field: 'status',
							align: 'center',
							fixed: false
						}
					];
			}

			return fields;
		}
	},
	watch: {
		id: {
			handler(newVal, oldVal) {
				if (newVal) {
					this.getPorjectInfo();
				}
			},
			immediate: true
		},
		// 监听目的地选项变化，确保正确回显
		destinationOptions: {
			handler(newVal) {
				if (newVal.length > 0 && this.formData.destinationCode) {
					// 强制刷新表单
					this.$nextTick(() => {
						this.$forceUpdate();
					});
				}
			},
			deep: true
		}
	},
	mounted() {
		// 初始化加载出发地和目的地列表
		if (this.title.includes('新增') && this.title.includes('因公')) {
			// 只有在因公外出情况下才需要初始化城市列表
			this.$nextTick(() => {
				this.queryCityList('', 'starting');
				this.queryCityList('', 'destination');
			});
		}
	},
	methods: {
		exportToPdf() {
			const printMe = document.getElementById('printMe'); // 获取需要打印的内容
			html2canvas(printMe, {
				letterRendering: true,
				scale: 2,
				useCORS: true,
				allowTaint: true
			}).then(canvas => {
				const imgData = canvas.toDataURL('image/png');
				const pdf = new jsPDF('p', 'mm', 'a4'); // 设置 PDF 页面大小为 A4

				// 计算图片在 PDF 上的位置和大小
				const imgWidth = pdf.internal.pageSize.width;
				const imgHeight = (canvas.height * imgWidth) / canvas.width;

				// 添加图像到 PDF 上
				pdf.addImage(imgData, 'PNG', 0, 0, imgWidth, imgHeight);

				// 保存 PDF 文件
				pdf.save('sample.pdf');
			});
		},
		calculateLeaveDays() {
			const startDateTime = this.formData.startDateTime;
			const endDateTime = this.formData.endDateTime;
			const isStartAm = (this.formData.isStartAm || 0) * 1; // 1代表上午，2代表下午
			const isEndAm = (this.formData.isEndAm || 0) * 1; // 同上

			if (!startDateTime || !endDateTime) {
				console.error('Start or end date is missing.');
				return;
			}

			const startDate = new Date(startDateTime);
			const endDate = new Date(endDateTime);
			const sameDay = startDate.toDateString() === endDate.toDateString();

			// 检查结束时间是否早于开始时间
			if (endDate < startDate) {
				this.$message.error('结束时间不能小于开始时间');
				this.formData.days = 0;
				this.formData.endDateTime = '';
				return;
			}

			// 计算日期差
			const diff = endDate.getTime() - startDate.getTime();
			const oneDay = 1000 * 60 * 60 * 24;
			let days = Math.floor(diff / oneDay) + 1;

			// 考虑半天的情况
			// 如果同一天
			if (sameDay) {
				if (isStartAm === 2 && isEndAm === 1) {
					days = 0.5; // 如果开始是下午，结束是上午，不计天数
					this.formData.isEndAm = '2';
				} else if (isStartAm === 1 && isEndAm === 1) {
					days = 0.5; // 如果都是上午，计半天
				} else if (isStartAm === 2 && isEndAm === 2) {
					days = 0.5; // 如果都是下午，也计半天
				} else {
					days = 1; // 其他情况（如上午到下午），计全天
				}
			} else {
				// 如果不是同一天且有剩余时间
				if (isStartAm === 1 && isEndAm === 1) {
					days -= 0.5;
				}
				if (isStartAm === 2 && isEndAm === 1) {
					days -= 1;
				}
				if (isStartAm === 2 && isEndAm === 2) {
					days -= 0.5;
				}
			}

			// 将结果赋值给表单数据
			this.$set(this.formData, 'days', days);
			console.log(days, 'days');
		},

		// 是否只读和编辑
		toolFormReadonly() {
			this.$nextTick(() => {
				const isEdit = window.location.href.includes('isEdit'); // 判断是否流程页面的编辑
				this.formReadonly = this.title === '查看' && !isEdit ? true : false;
			});
		},

		//修改保存
		async save(callBank) {
			this.$refs.formRef.validate(valid => {
				if (valid) {
					const loading = this.load('提交中...');
					let formData = {
						...this.formData,
						number: this.formData.number || '', // 确保number字段被包含在提交的数据中
						members:
							this.formData.members?.map?.(item => {
								const attr = JSON.parse(item.attr || '{}');
								return {
									applicationId: this.id, // 请假申请id
									userId: item.showid, // 用户id
									userName: item.showname, // 用户姓名
									userCode: attr.outcode, // 用户编码
									userPhone: attr.phone, // 联系方式
									sex: attr.sex // 性别
								};
							}) || []
					};

					// 确保destinationCode和destinationCodeTxt是逗号分隔的字符串
					if (Array.isArray(formData.destinationCode)) {
						formData.destinationCode = formData.destinationCode.join(',');
					}

					if (Array.isArray(formData.destinationCodeTxt)) {
						formData.destinationCodeTxt = formData.destinationCodeTxt.join(',');
					}

					this.$.ajax({
						url: this.title.includes('新增') ? this.basics.save : this.basics.edit,
						method: 'post',
						data: formData,
						format: false
					}).then(res => {
						loading.close();
						if (res.rCode == 0) {
							if (callBank) {
								callBank();
							} else {
								this.$message.success(res.msg);
								this.handleSuccess();
							}
						} else {
							this.$message.warning(res.msg);
						}
					});
				}
			});
		},
		// 提交成功
		handleSuccess(e) {
			//pendingId则为流程的审核页面，否则是弹窗的流程
			const isFlow = window.location.href.includes('pendingId');
			if (isFlow) {
				window.close();
			} else {
				this.$emit('update:visible', false);
				this.$emit('visible', false);
			}
		},

		// 页面加载
		load(text) {
			return this.$.loading(this.$loading, text);
		},
		async getPorjectInfo() {
			this.toolFormReadonly();
			if (this.title.includes('新增')) {
				const loginUserInfo = JSON.parse(localStorage.getItem('loginUserInfo') || '{}');
				this.formData = {
					type: this.title.includes('因公') ? '2' : '1',
					isStartAm: '1',
					isEndAm: '2',
					name: loginUserInfo.name, // 申请人
					account: this.account, // 教工号
					number: '', // 申请单号，系统自动生成，先设置为空字符串确保字段存在
					deptId: loginUserInfo.deptId,
					deptName: loginUserInfo.deptName,
					phone: loginUserInfo.phone,
					id: this.id,
					startingType: 0, // 默认出发地类型为国内
					destinationType: 0 // 默认目的地类型为国内
				};

				// 如果是因公外出，初始化城市列表
				if (this.title.includes('因公')) {
					this.$nextTick(() => {
						this.queryCityList('', 'starting');
						this.queryCityList('', 'destination');
					});
				}
				return;
			}
			const loading = this.load('加载中...');
			try {
				let { rCode, msg, results } = await this.$.ajax({
					url: this.basics.info,
					method: 'get',
					params: { id: this.id }
				});
				if (rCode == 0) {
					let obj = results;
					this.setTableData(obj);

					// 如果是因公外出且状态为已审批通过，加载订单数据
					if (obj.type === '2' && obj.status === '2') {
						this.getOrdersByType();
					}
				} else {
					this.$message.error(msg);
				}
			} finally {
				loading.close();
			}
		},
		setTableData(obj) {
			let formData = {
				...obj,
				startDateTimeTxt: obj.startDateTime + (obj.isStartAm === '1' ? '-上午' : '-下午'),
				endDateTimeTxt: obj.endDateTime + (obj.isEndAm === '1' ? '-上午' : '-下午'),
				members:
					obj?.members?.map(item => {
						return {
							showid: item.userId,
							showname: item.userName,
							attr: JSON.stringify({
								outcode: item.userCode,
								phone: item.userPhone,
								sex: item.sex
							})
						};
					}) || [],
				startingType: obj.startingType !== undefined ? obj.startingType : 0,
				destinationType: obj.destinationType !== undefined ? obj.destinationType : 0
			};

			// 处理目的地代码，确保在编辑模式下是数组
			if (obj.destinationCode && typeof obj.destinationCode === 'string') {
				formData.destinationCode = obj.destinationCode.split(',');
			}

			this.formData = formData;

			// 编辑状态下，加载城市列表
			if (this.formData.type === '2') {
				this.$nextTick(() => {
					this.queryCityList('', 'starting');
					// 如果有目的地代码，先将其添加到选项中以确保回显正确
					if (obj.destinationCode) {
						// 将目的地代码和文本转为数组
						const destinationCodes =
							typeof obj.destinationCode === 'string'
								? obj.destinationCode.split(',')
								: obj.destinationCode;
						const destinationNames = obj.destinationCodeTxt.split(',');

						// 预先添加已选城市到选项列表
						this.destinationOptions = destinationCodes.map((code, index) => ({
							label: destinationNames[index] || code,
							value: code
						}));

						// 然后再加载完整列表
						this.queryCityList('', 'destination');
					} else {
						this.queryCityList('', 'destination');
					}
				});
			}
		},
		async queryCityList(keyword, type) {
			if (type === 'starting') {
				this.startingLoading = true;
			} else {
				this.destinationLoading = true;
			}

			try {
				const { rCode, msg, results } = await this.$.ajax({
					url: '/ybzy/attendanceLeave/queryCityPage',
					method: 'get',
					params: {
						type: type === 'starting' ? this.formData.startingType : this.formData.destinationType,
						keyword: keyword,
						pageNum: 1,
						pageSize: 100
					}
				});

				if (rCode == 0) {
					const options = results.records.map(item => {
						return {
							label: item.name,
							value: item.code
						};
					});

					if (type === 'starting') {
						// 确保宜宾市选项存在
						const yibinExists = options.some(item => item.value === '10369');
						if (!yibinExists && this.formData.startingType === 0) {
							// 只在国内城市列表中添加宜宾
							options.unshift({
								label: '宜宾',
								value: '10369'
							});
						}

						this.startingOptions = options;

						// 如果是新增模式，设置默认值为宜宾
						if (
							this.title.includes('新增') &&
							!this.formData.startingCode &&
							this.formData.startingType === 0
						) {
							this.$set(this.formData, 'startingCode', '10369');
							this.$set(this.formData, 'startingCodeTxt', '宜宾');
							this.$set(this.formData, 'destinationCode', '');
							this.$set(this.formData, 'destinationCodeTxt', '');
						}
					} else if (type === 'destination') {
						// console.log(8888);

						// 合并新选项与已有选项，确保不会丢失已选城市
						const existingOptions = [...this.destinationOptions];
						const mergedOptions = [...options];

						// 检查已有选项是否在新选项中，如果不在则添加
						existingOptions.forEach(existing => {
							const exists = mergedOptions.some(option => option.value === existing.value);
							if (!exists) {
								mergedOptions.push(existing);
							}
						});

						this.destinationOptions = mergedOptions;
						// this.$forceUpdate();
					}
				} else {
					this.$message.error(msg);
				}
			} finally {
				if (type === 'starting') {
					this.startingLoading = false;
				} else {
					this.destinationLoading = false;
				}
			}
		},
		// 跳转到订票系统
		goToBooking() {
			// 调用SSO认证接口
			const baseUrl = window.location.origin;
			window.open(`${baseUrl}/ybzy/attendanceLeave/authSso`);
		},

		// 切换订单类型
		handleOrderTypeChange() {
			this.getOrdersByType();
		},

		// 获取订单列表
		getOrdersByType() {
			this.orderLoading = true;
			this.tableData = []; // 清空之前的数据

			this.$.ajax({
				url: '/ybzy/attendanceLeave/getOrdersByType',
				method: 'get',
				params: {
					id: this.id,
					type: this.activeOrderType
				}
			})
				.then(res => {
					if (res.rCode === 0) {
						this.tableData = res.results || [];
					} else {
						this.$message.error(res.msg || '获取订单数据失败');
					}
				})
				.finally(() => {
					this.orderLoading = false;
				});
		}
	}
};
</script>

<style lang="scss" scoped>
.main {
	// height: 739px;
	// height: 100%;
	// overflow: auto;
	padding: 0 8px 20px;
	position: relative;
	.btn-view {
		position: absolute;
		top: 5px;
		right: 20px;
	}
	.basic-info {
		flex: 1;
		width: 100%;
		overflow: auto;
	}
	.table-info {
		position: relative;
		.btn-box {
			position: absolute;
			right: 0;
			top: 0;
		}
	}
	.rich-text {
		padding: 20px 0 50px 20px;
		h4 {
			margin-bottom: 5px;
		}
		p {
			padding-left: 10px;
			text-indent: 2em;
			font-size: 12px;
		}
	}
}
.pdf-box {
	display: flex;
	flex-direction: column;
	align-items: center;
	padding-top: 20px;
	.btn-print {
		padding: 13px 14px;
		display: flex;
		widows: 100%;
		justify-content: flex-end;
		align-items: center;
	}
	.title {
		text-align: center;
		padding-top: 30px;
		padding-bottom: 6px;
	}
	.pdf-content {
		width: 794px;
		.opinion-title {
			padding: 20px 15px 15px;
			font-weight: normal;
			border: 1px solid #d9d9d9;
			border-top: none;
			border-bottom: none;
			margin: 0 12px;
			margin-top: -25px;
		}
		.opinion-content {
			padding: 0px 36px;
			border: 1px solid #d9d9d9;
			border-top: none;
			margin: 0 12px;
			padding-bottom: 40px;
		}
	}
}
::v-deep .es-form {
	.es-form-content {
		padding: 0;
	}
	.es-form-button {
		padding: 0;
	}
}
::v-deep .es-table-form {
	width: 100%;
	.es-table-form-label {
		text-align: right;
		color: #747474;
		font-weight: 550;
	}
}
::v-deep .cell {
	color: #747474;
}
</style>
