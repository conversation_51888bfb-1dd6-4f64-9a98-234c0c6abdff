<template>
	<div class="main">
		<div class="body">
			<es-tree-group
				ref="refEsTreeGroup"
				:table="table"
				:tree="{
					title: '全部',
					data: labeldata,
					remote: false,
					showCheckbox: false,
					showSearch: true,
					search: search,
					props: defaultProps
				}"
				:clickedAsSearc="false"
				@btn-click="handleClick"
				:syncKeys="syncKeysOption"
			></es-tree-group>
		</div>
	</div>
</template>

<script>
import api from '@/http/dataManage/data-manager';

export default {
	data() {
		return {
			TreeUrl: api.getClassFyTree,
			search: {
				name: 'journalName',
				placeholder: '请输入关键字筛选'
			},
			table: {
				reload: true,
				immediate: true,
				url: api.getFileList,
				method: 'POST',
				format: false,
				param: {
					pageNum: 1,
					pageSize: 10,
					privates: false
				},
				page: {
					pageSize: 10,
					position: 'right',
					current: 1,
					pageNum: 1
				},
				toolbar: [
					{
						type: 'search',
						contents: [
							{
								type: 'text',
								name: 'fileName',
								placeholder: '请输入文件名'
							}
						]
					}
				],
				thead: [
					{
						title: '文件名',
						field: 'fileName',
						align: 'center',
						fixed: true
					},
					{
						title: '文件格式',
						field: 'fileMat',
						align: 'center',
						fixed: false
					},
					{
						title: '上传人',
						field: 'failName',
						align: 'center'
					},
					{
						title: '文件大小',
						field: 'fileSize',
						align: 'center'
					},
					{
						title: '上传时间',
						field: 'createTime',
						align: 'center'
					},
					{
						title: '操作',
						type: 'handle',
						template: '',
						events: [
							{
								text: '下载'
							},
							{
								text: '收藏',
								rules: rows => {
									return !rows.isCollect;
								}
							},
							{
								text: '取消收藏',
								rules: rows => {
									return rows.isCollect;
								}
							}
						]
					}
				]
			},

			syncKeysOption: {
				classId: 'id'
			},
			labeldata: [],
			defaultProps: {
				children: 'child',
				label: 'journalName'
			}
		};
	},
	computed: {},
	mounted() {
		this.getTreeData();
	},
	methods: {
		getTreeData() {
			this.$request({
				url: api.getClassFyTree,
				method: 'GET'
			}).then(res => {
				if (res.rCode === 0) {
					this.labeldata = res.results;
				} else {
					this.$message.error(res.msg);
				}
			});
		},

		handleClick(data) {
			let { row } = data;
			let handle = data.handle.text;

			switch (handle) {
				case '下载':
					this.DownLoadFile(row);
					break;
				case '收藏':
					this.RecordFile(row, 0);
					break;
				case '取消收藏':
					this.RecordFile(row, 1);
					break;
			}
		},

		async DownLoadFile(row) {
			try {
				const { fileSysId, id } = row;
				let msg = await this.$utils.DownLoadFile('doc_manage_upload', fileSysId);
				if (msg === '您的操作已成功！') {
					this.$request({
						url: `${api.addDownLoadsum}?id=${id}`,
						method: 'GET'
					}).then(res => {
						if (res.rCode === 0) {
							this.$message.success(msg);
						}
					});
				}
			} catch (error) {}
		},

		//   取消收藏/收藏文件
		RecordFile(row, option) {
			const { id } = row;
			const url = option === 0 ? `${api.collectFile}?fileId=${id}` : `${api.clearFile}?id=${id}`;
			this.$request({
				url: url,
				method: 'GET'
			}).then(res => {
				if (res.rCode === 0) {
					this.$refs.refEsTreeGroup.reload();
					this.$message.success(res.msg);
				}
				if (res.rCode === 40) {
					this.$message.warning(res.msg);
				}
			});
		}
	}
};
</script>

<style scoped lang="scss">
.main {
	position: relative;
	width: 100%;
	height: 100%;
	// padding: 10px;
	.body {
		width: 100%;
		height: 100%;
	}
}
</style>
