<template>
	<!-- 历史变更详情 -->
	<BasicInfo :id="'23434'" :contents-key="contentsKey" :is-flow-pattern="false" title="查看" />
</template>

<script>
import BasicInfo from '@/views/scientific-sys/components/project-info/basic-info.vue';

export default {
	components: {
		// ProcesPage,
		BasicInfo
	},
	props: {
		id: {
			type: String,
			default: ''
		},
		contentsKey: {
			type: String,
			default: ''
		}
	},
	data() {
		return {
			visibleBasicInfo: false
		};
	},
	computed: {}
};
</script>

<style></style>
