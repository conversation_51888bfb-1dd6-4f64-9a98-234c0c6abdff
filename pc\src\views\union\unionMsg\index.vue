<template>
	<div class="content">
		<es-tree-group
			:tree="tree"
			@node-click="handleChange"
			:syncKeys="{id:'id', name:'name'}"
		></es-tree-group>
		<es-data-table
			ref="table"
			:row-style="tableRowClassName"
			:full="true"
			:fit="true"
			:thead="thead"
			:toolbar="toolbar"
			:border="true"
			:page="pageOption"
			:url="dataTableUrl"
			:numbers="true"
			:param="params"
			close
			form
			@btnClick="btnClick"
			@sort-change="sortChange"
			@submit="hadeSubmit"
			@edit="changeTable"
		></es-data-table>
		<es-dialog
			:title="formTitle"
			:visible.sync="showForm"
			width="60%"
			:close-on-click-modal="false"
			:destroy-on-close="true"
		>
		<es-form
			ref="form"
			:model="formData"
			:contents="formItemList"
			height="500px"
			:genre="2"
			collapse
			@change="inputChange"
			@submit="handleFormSubmit"
			@reset="showForm = false"
		/>
		</es-dialog>
		<es-dialog
			:title="formTitle"
			:visible.sync="showReplyForm"
			width="60%"
			:close-on-click-modal="false"
			:destroy-on-close="true"
		>
		<es-form
			ref="form"
			:model="formData"
			:contents="replyFormItemList"
			height="500px"
			:genre="2"
			collapse
			@change="inputChange"
			@submit="handleFormSubmit"
			@reset="showReplyForm = false"
		/>
		</es-dialog>
		<es-dialog
			title="删除"
			:visible.sync="showDelete"
			width="20%"
			:close-on-click-modal="false"
			:middle="true"
			height="140px"
		>
			<div>确定要删除该条数据吗</div>
			<div class="btn-box">
				<div class="btn theme" @click="deleteRow">确定</div>
				<div class="btn" @click="showDelete = false">取消</div>
			</div>
		</es-dialog>
	</div>
</template>


<script>
import interfaceUrl from '@/http/union/unionMsg/api.js';
import SnowflakeId from 'snowflake-id';

export default {
	data() {
		return {
			tree: {
				defaultExpandAll:true,
				showSearch: false,
				data: [],
				defaultProps: {
					children: 'children',
					label: 'name'
				}
			},
			unionSelect: {
				label: '请选择所属工会',
				id: 'union-select'
			},
			unionSelectData: [],
			collegeSelectData: [],
			ownId: '',
			deleteId: '',
			dataTableUrl: interfaceUrl.cUserListJson,
			showForm: false,
			showReplyForm: false,
			showRoleForm: false,
			enpList: [],
			showDelete: false,
			formData: {},
			clickNode: '',
			roleFormData: {
				userId: '',
				username: '',
				phone: '',
				selectedRoleList: [],
			},
			formTitle: '编辑',
			editBtn: {
				type: 'submit',
				skin: 'lay-form-btns',
				contents: [
					{
						type: 'primary',
						text: '确定',
						event: 'confirm'
					},
					{
						type: 'reset',
						text: '取消',
						event: 'cancel'
					}
				]
			},
			cancelBtn: {
				type: 'submit',
				skin: 'lay-form-btns',
				contents: [
					{
						type: 'reset',
						text: '取消',
						event: 'cancel'
					}
				]
			},
			toolbar: [
				{
					type: 'button',
					contents: [
						{
							text: '新增',
							code: 'add',
							type: 'primary'
						}
					]
				},
				{
					type: 'search',
					contents: [
						{
							type: 'text',
							name: 'keyword',
							placeholder: '关键字查询'
						}
					]
				}
			],
			thead: [
				{
					title: '留言人',
					align: 'left',
					field: 'memberName',
					sortable: 'custom',
					showOverflowTooltip: true,
				},
				{
					title: '留言内容',
					align: 'left',
					field: 'msgText',
					sortable: 'custom',
					showOverflowTooltip: true,
				},
				{
					title: '留言时间',
					align: 'left',
					field: 'createTime',
					sortable: 'custom',
					showOverflowTooltip: true,
				},
				{
					title: '所属工会',
					align: 'left',
					field: 'unionName',
					sortable: 'custom',
					showOverflowTooltip: true,
				},
				{
					title: '是否匿名',
					align: 'left',
					field: 'isAnonymousName',
					sortable: 'custom',
					showOverflowTooltip: true,
				},
				{
					title: '操作',
					type: 'handle',
					align: 'center',
					width: 180,
					template: '',
					events: [
						{
							code: 'view',
							text: '查看'
						},
						{
							code: 'delete',
							text: '删除'
						},
						{
							code: 'reply',
							text: '回复'
						},
					]
				}
			],
			pageOption: {
				layout: 'total, prev, pager, next, jumper',
				pageSize: 10,
				// hideOnSinglePage: true,
				position: 'right',
				current: 1,
				pageNum: 1
			},
			params: {
				asc: 'false',
				orderBy: 'createTime',
			}
		};
	},
	computed: {
		formItemList() {
			return [
				{
					label: '工会',
					name: 'unionId',
					placeholder: '请选择工会',
					type: 'select',
					tree: true,
					valueKey: 'id',
					labelKey: 'name',
					events: { change: this.unionSelectChange },
					rules: {
						required: true,
						message: '请选择工会',
						trigger: 'blur'
					},
					data: this.unionSelectData,
					valueType: 'string',
					verify: 'required',
					col: 6,
				},
				{
					label: '留言内容',
					type: 'textarea',
					name: 'msgText',
					placeholder: '留言内容',
					readonly: true,
					event: 'multipled',
					col: 12
				},
				{
					label: '是否匿名',
					name: 'isAnonymous',
					placeholder: '请选择是否匿名',
					type: 'radio',
					value: 0,
					rules: {
						required: true,
						message: '请选择是否匿名',
						trigger: 'blur'
					},
					verify: 'required',
					col: 6,
					sysCode: 'yes_or_no',
					'label-key': 'shortName',
					'value-key': 'cciValue'
				},
				{
					label: '回复',
					type: 'textarea',
					name: 'replyText',
					placeholder: '请输入回复',
					event: 'multipled',
					rules: {
						required: true,
						message: '请输入回复',
						trigger: 'blur'
					},
					verify: 'required',
					col: 12
				},
			];
		},
		replyFormItemList() {
			return [
				{
					label: '留言人',
					name: 'memberName',
					readonly: true,
					event: 'multipled',
					col: 6
				},
				{
					label: '留言时间',
					name: 'createTime',
					readonly: true,
					event: 'multipled',
					col: 6
				},
				{
					label: '留言内容',
					type: 'textarea',
					name: 'msgText',
					readonly: true,
					event: 'multipled',
					col: 12
				},
				{
					label: '回复',
					type: 'textarea',
					name: 'replyText',
					placeholder: '请输入回复',
					event: 'multipled',
					rules: {
						required: true,
						message: '请输入回复',
						trigger: 'blur'
					},
					verify: 'required',
					col: 12
				},

			];
		},
	},
	watch: {},
	created() {
		this.initTree();
		this.initCollegeTree();
	},
	mounted() {},
	methods: {
		/**
		 * 表格行高
		 */
		tableRowClassName({ row, rowIndex }) {
			let styleRes = {
				height: '54px !important'
			};
			return styleRes;
		},
		inputChange(key, value) {
			if (key == 'phone') {
			}
		},
		hadeSubmit(data) {},

		/**
		 * 树点击事件
		 */
		handleChange(tree, data) {
			this.clickNode = data.id;
			this.$set(this.params, 'unionId', data.id);
		},
		handleNodeClick(data) {
			console.log(data);
		},

		
		/**
		 * 树点击事件
		 */
		 unionSelectChange(tree, data) {
			console.log(data);
		},

		/**
		 * 操作按钮点击事件
		 * @param {*} res
		 */
		btnClick(res) {
			let code = res.handle.code;
			switch (code) {
				case 'add':
					// 新增
					this.formTitle = '新增';
					this.editModule(this.formItemList, ['replyText'], []);
					const snowflake = new SnowflakeId();
					let id = snowflake.generate();
					this.ownId = id;
					this.formData = { id: id, unionId: this.clickNode };
					this.showForm = true;
					break;
				case 'reply':
					// 回复
					this.formTitle = '回复';
					this.ownId = res.row.id;
					this.editModule(this.replyFormItemList, [], ['msgText','createTime','memberName']);
					this.$request({
						url: interfaceUrl.info + '/' + res.row.id,
						method: 'GET'
					}).then(res => {
						if (res.rCode == 0) {
							this.formData = res.results;
							this.showReplyForm = true;
						}
					});
					break;
				case 'view':
					this.formTitle = '查看';
					this.ownId = res.row.id;
					this.readModule(this.formItemList, []);
					this.$request({
						url: interfaceUrl.info + '/' + res.row.id,
						method: 'GET'
					}).then(res => {
						if (res.rCode == 0) {
							this.formData = res.results;
							this.showForm = true;
						}
					});
					break;
				case 'delete':
					this.deleteId = res.row.id;
					this.showDelete = true;
					break;
				default:
					break;
			}
		},
		handleClose() {},
		handleFormItemChange() {},
		handleFormSubmit(data) {
			let formData = data;
			delete formData.extMap;
			let url = '';
			if (this.formTitle == '新增') {
				url = interfaceUrl.save;
				let Base64 = require('js-base64').Base64;
				formData['password'] = Base64.encode(formData.pwd);
			} else {
				url = interfaceUrl.update;
			}
			this.$request({
				url: url,
				data: formData,
				method: 'POST'
			}).then(res => {
				if (res.rCode == 0) {
					this.$message.success('操作成功');
					this.showForm = false;
					this.showReplyForm = false;
					this.formData = {};
					this.$refs.table.reload();
					this.initTree();
				} else {
					this.formData.state = 1 == this.formData.state ? true : false;
					this.$message.error(res.msg);
				}
			});
		},
		deleteRow() {
			this.$request({
				url: interfaceUrl.deleteBatchIds,
				data: { ids: this.deleteId },
				method: 'POST'
			}).then(res => {
				if (res.rCode == 0) {
					this.$refs.table.reload();
					this.$message.success('删除成功');
				} else {
					this.$message.error(res.msg);
				}
				this.showDelete = false;
				this.initTree();
			});
		},
		/**
		 * 排序变化事件
		 * @param {*} column
		 * @param {*} prop
		 * @param {*} order
		 */
		sortChange(column, prop, order) {
			if (column.order == 'ascending') {
				//升序
				this.params = {
					asc: 'true',
					orderBy: column.prop
				};
			} else if (column.order == 'descending') {
				//降序
				this.params = {
					asc: 'false',
					orderBy: column.prop
				};
			} else {
				//不排序
				this.params = {
					asc: 'false',
					orderBy: 'createTime'
				};
			}
			this.$refs.table.reload();
		},
		/**
		 * 编辑模式
		 */
		editModule(list, hideField, excludeField) {
			for (var i in list) {
				var item = list[i];
				if (undefined != excludeField && excludeField.length > 0 && excludeField.indexOf(item.name) > -1) {
					continue;
				}
				item.readonly = false;
				if (undefined != hideField && hideField.length > 0 && hideField.indexOf(item.name) > -1) {
					item.hide = true;
				}
			}
			list.push(this.editBtn);
		},
		/**
		 * 只读模式
		 */
		readModule(list, hideField) {
			for (var i in list) {
				var item = list[i];
				item.readonly = true;
				if (undefined != hideField && hideField.length > 0 && hideField.indexOf(item.name) > -1) {
					item.hide = true;
				} else {
					item.hide = false;
				}
			}
			list.push(this.cancelBtn);
		},
		changeTable(val) {
			if ('state' === val.name) {
				this.$request({
					url: interfaceUrl.updateState,
					data: {
						id: val.data.id,
						state: val.value
					},
					method: 'POST'
				}).then(res => {
					if (res.rCode == 0) {
						this.$message.success('操作成功');
					} else {
						this.$message.error(res.msg);
					}
				});
			}
		},

		// 初始化左侧树数据
		initTree() {
			this.$request({
				url: interfaceUrl.cUserTreeList,
				method: 'POST'
			}).then(res => {
				if (res.rCode == 0) {
          this.tree.data = res.results.baseTree;
          this.unionSelectData = res.results.baseTree;
				}
			});
		},

		// 初始化学院选择树数据
		initCollegeTree() {
			this.$request({
				url: interfaceUrl.collegeTree,
				method: 'POST'
			}).then(res => {
				if (res.rCode == 0) {
					this.collegeSelectData = res.results;
				}
			});
		},
	}
};
</script>
<style scoped lang="scss">
@import '../../../assets/style/style.scss';

.content {
	display: flex;
	width: 100%;
	height: 100%;

	::v-deep .es-data-table {
		flex: 1;
		display: flex;
		flex-direction: column;

		.es-data-table-content {
			flex: 1;
			height: 0;
			display: flex;
			flex-direction: column;

			.el-table {
				flex: 1;
				// height: 100% !important;
			}

			.es-thead-border {
				.el-table__header {
					th {
						border-right: 1px solid #e1e1e1;
					}
				}
			}
		}
	}

	::v-deep .el-form-item__label {
		background: none;
		border: 0px solid #c2c2c2;
	}
}

.el-dialog__body {
	.btn-box {
		margin-top: 20px;
		display: flex;
		justify-content: flex-end;

		.btn {
			padding: 5px 10px;
			color: #666;
			border: 1px solid #eee;
			cursor: pointer;
			margin-right: 5px;
			border-radius: 4px;
			// &.theme {
			// 	background: $--color-primary;
			// 	color: #fff;
			// 	border-color: $--color-primary;
			// }
		}
	}
}
</style>
