<template>
	<div class="">
		<div v-for="(item, index) in personList" :key="index" class="person-card">
			<p class="card-title">
				<span>{{ item.name }}</span>
				<img
					class="card-img"
					:src="require('@/assets/images/home20240423/information-icon2.png')"
					alt=""
				/>
			</p>
			<div class="card-footer">
				<p class="left">
					<span class="num">{{ item.value }}</span>
					<span class="unit">{{ item.unit }}</span>
				</p>
				<p class="right">
					<span class="desc">较上年 -%</span>
					<img
						v-if="true"
						class="desc-img"
						:src="require('@/assets/images/home20240423/down-icon.png')"
					/>
					<img v-else class="desc-img" :src="require('@/assets/images/home20240423/up-icon.png')" />
				</p>
			</div>
		</div>
	</div>
</template>

<script>
export default {
	components: {},
	props: {
		proprSxqksj: {
			type: Object,
			default: () => {
				return {};
			}
		},
		proprSxblfx: {
			type: Object,
			default: () => {
				return {};
			}
		}
	},
	data() {
		return {
			personList: [
				{
					name: '实习人数',
					value: '',
					key: 'sxrs',
					unit: '人'
				},
				{
					name: '自主联系实习人数',
					value: '',
					key: 'zdyxrs',
					unit: '人'
				},
				{
					name: '统一安排实习人数',
					value: '',
					key: 'tyapxxrs',
					unit: '人'
				},
				{
					name: '实习对口上岸率',
					value: '',
					key: 'sxdkl',
					unit: '%'
				},
				{
					name: '实习稳定率',
					value: '',
					key: 'sxwdl',
					unit: '人'
				},
				{
					name: '实习就业率',
					value: '',
					key: 'sxjyl',
					unit: '%'
				}
			]
		};
	},
	watch: {
		proprSxqksj: {
			handler(val) {
				if (JSON.stringify(val) === '{}') return;
				this.toolList();
			},
			deep: true
		},
		proprSxblfx: {
			handler(val) {
				if (JSON.stringify(val) === '{}') return;
				this.toolList();
			},
			deep: true
		}
	},
	methods: {
		toolList() {
			const obj = { ...this.proprSxqksj, ...this.proprSxblfx };
			this.personList.forEach(item => {
				if (
					item.name === '实习就业率' ||
					item.name === '实习稳定率' ||
					item.name === '实习对口上岸率'
				) {
					obj[item.key] >= 0 && (item.value = obj[item.key] * 100);
				} else {
					item.value = obj[item.key];
				}
			});
		}
	}
};
</script>

<style lang="scss" scoped>
.person-card {
	width: calc(16.6% - 6px);
	height: 130px;
	padding: 22px 17px;
	background: rgba(252, 253, 254, 0.7);
	box-shadow: 0px 0px 10px 0px rgba(5, 32, 70, 0.1);
	border-radius: 8px;
	border: 2px solid #ffffff;
	.card-title {
		font-family: MicrosoftYaHei, MicrosoftYaHei;
		font-weight: bold;
		font-size: 18px;
		color: #454545;
		line-height: 24px;
		display: flex;
		justify-content: space-between;
	}
	.card-img {
		width: 40px;
		height: 44px;
	}
	.card-footer {
		margin-top: 5px;
		display: flex;
		justify-content: space-between;
		align-items: baseline;
		.num {
			font-family: DINPro, DINPro;
			font-weight: bold;
			font-size: 36px;
			color: #0a325b;
			line-height: 46px;
		}
		.unit {
			margin-left: 11px;
			font-family: MicrosoftYaHei;
			font-size: 16px;
			color: #294d79;
			line-height: 21px;
		}
		.desc {
			font-family: MicrosoftYaHei;
			font-size: 16px;
			color: #7b96b1;
			line-height: 21px;
		}
		.desc-img {
			width: 8px;
			margin-left: 5px;
		}
	}
}
</style>
