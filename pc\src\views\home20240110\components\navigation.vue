<template>
	<div>
		<div
			v-show="JSON.stringify(navListActive) === '{}' ? '' : 'contentBox--active'"
			class="mask"
			@click="navListActive = {}"
		></div>
		<div class="navigation">
			<div class="infoBox">
				<div v-for="(item, k) of navLink" :key="k" class="item" @click="onNavClick(item)">
					<el-badge
						:value="item.count"
						:max="99"
						:hidden="item.name === '我的代办' && item.count > 0 ? false : true"
					>
						<img class="img" :src="item.img" alt="" />
						<img class="img--active" :src="item.img2" alt="" />
					</el-badge>
					<span class="text">{{ item.name }}</span>
				</div>
			</div>
			<div
				class="contentBox"
				:class="JSON.stringify(navListActive) === '{}' ? '' : 'contentBox--active'"
			>
				<div class="nav">
					<div
						v-for="(item, k) of navLink.slice(0, 3)"
						:key="k"
						class="nav-item"
						:class="item.name === navListActive.name ? 'nav-item--active' : ''"
						@click="onNavClick(item)"
					>
						<el-badge
							:value="item.count"
							:max="99"
							:hidden="item.name === '我的代办' && item.count > 0 ? false : true"
						>
							<img class="img" :src="item.img" alt="" />
							<img class="img--active" :src="item.img2" alt="" />
						</el-badge>
						<span class="text">{{ item.name }}</span>
					</div>
				</div>
				<div class="btn-r" @click="navListActive = {}">
					<i class="el-icon-arrow-right"></i>
				</div>
				<div :key="navListActive.name" class="list">
					<NavigationList
						v-if="navListActive.name === '我的代办'"
						:item="navLink[0]"
						@total="
							e => {
								todo = e;
							}
						"
					/>
					<NavigationList v-if="navListActive.name === '我发起的'" :item="navLink[1]" />
					<NavigationCollect v-if="navListActive.name === '我的收藏'" @callBack="callBack" />
				</div>
			</div>
		</div>
	</div>
</template>

<script>
import NavigationCollect from './navigationCollect.vue';
import NavigationList from './navigationList.vue';
export default {
	components: { NavigationList, NavigationCollect },
	data() {
		return {
			todo: 0,
			send: 0,
			navListActive: {}
		};
	},

	computed: {
		navLink() {
			return [
				{
					img: require('@/assets/images/serveHall/todo-icon.png'),
					img2: require('@/assets/images/serveHall/todo-active.png'),
					name: '我的代办',
					code: 'todo',
					count: this.todo,
					path: ''
				},
				{
					img: require('@/assets/images/serveHall/send-icon.png'),
					img2: require('@/assets/images/serveHall/send-active.png'),
					name: '我发起的',
					code: 'send',
					count: this.send,
					path: ''
				},
				{
					img: require('@/assets/images/serveHall/collect-icon.png'),
					img2: require('@/assets/images/serveHall/collect-active.png'),
					name: '我的收藏',
					path: ''
				},
				{
					img: require('@/assets/images/serveHall/staging-icon.png'),
					img2: require('@/assets/images/serveHall/staging-active.png'),
					name: '我的工作台',
					path: '/ybzy/platauth/front/loginEoss?redirectUri=/project-ybzy/ybzy/index.html#/main?cookie=1'
				}
			];
		}
	},
	mounted() {
		this.interfaceData('/oa/wfPending/wfPendingMerge/list_json.dhtml', true);
	},
	methods: {
		// 点击左侧导航
		onNavClick(item) {
			if (item.name === '我的工作台') return this.myAppDataBtn(item);
			this.navListActive = item;
		},
		// 应用跳转对应链接判断
		myAppDataBtn(item) {
			if (item.path.includes('redirectUri=/project-ybzy/ybzy/index.html')) {
				window.location.href = item.path + '&isTeacher=true';
			} else {
				window.open(item.path);
			}
		},
		//我的待办接口
		interfaceData(url, need) {
			let data = {
				rows: 10,
				page: 1,
				sord: 'desc'
			};
			if (need) {
				data.query_pendingattr = 0;
			} else {
				delete data.query_pendingattr;
			}
			this.$request({
				url: url,
				data: data,
				method: 'POST'
			}).then(res => {
				this.todo = res.totalrecords;
			});
		},
		// 内部触发的回调
		callBack() {
			this.$emit('callBack');
		}
	}
};
</script>

<style lang="scss" scoped>
.mask {
	width: 100vw;
	height: 100vh;
	position: fixed;
	background: rgba(0, 0, 0, 0.1);
	top: 0;
	left: 0;
	z-index: 20;
}
.navigation {
	position: fixed;
	top: 30vh;
	z-index: 30;
	right: 30px;
	width: 72px;
	background: #ffffff;
	border-radius: 12px;
	.infoBox {
		display: flex;
		flex-direction: column;
		align-items: center;
		background-image: url(~@/assets/images/serveHall/staging-bg.png);
		background-size: 100% auto;
		background-repeat: no-repeat;
		background-position-y: 100%;
		.item {
			width: 100%;
			padding: 10px;
			display: flex;
			flex-direction: column;
			justify-content: center;
			align-items: center;
			margin-top: 5px;
			cursor: pointer;

			.img,
			.img--active {
				width: 24px;
				height: 24px;
				margin-bottom: 4px;
			}
			.img--active {
				display: none;
			}
			.text {
				height: 20px;
				font-size: 12px;
				font-family: MicrosoftYaHei;
				color: #767676;
				line-height: 20px;
				white-space: nowrap;
			}
			&:last-child {
				.text {
					color: #ffffff;
				}
			}
			&:hover {
				.text {
					color: #0175e8;
				}
				.img {
					display: none;
				}
				.img--active {
					display: inline-block;
				}
			}
		}
	}

	.contentBox {
		width: 0px;
		overflow: hidden;
		height: 90vh;
		background: rgba(0, 0, 0, 0.75);
		border-radius: 8px 0px 0px 8px;
		position: fixed;
		margin-right: 0;
		top: 80px;
		right: 0;
		z-index: 40;
		transition: all 0.3s ease;
		.nav {
			display: flex;
			justify-content: center;
			align-items: center;
			margin-bottom: 10px;
			.nav-item {
				display: flex;
				flex-direction: column;
				justify-content: center;
				align-items: center;
				width: 187px;
				height: 94px;
				cursor: pointer;
				.img,
				.img--active {
					width: 24px;
					height: 24px;
					margin-bottom: 10px;
				}
				.img--active {
					display: none;
				}
				.text {
					height: 20px;
					line-height: 20px;
					white-space: nowrap;
					font-size: 14px;
					font-family: MicrosoftYaHei;
					color: #ffffff;
				}
				&:hover {
					.text {
						color: #31c5ff;
					}
					.img {
						display: none;
					}
					.img--active {
						display: inline-block;
					}
				}
			}
			.nav-item--active {
				width: 187px;
				height: 91px;
				background: linear-gradient(
					180deg,
					rgba(1, 117, 232, 0.15) 0%,
					rgba(1, 117, 232, 0.4) 100%
				);
				border-bottom: 3px solid #0175e8;
				.text {
					color: #31c5ff;
				}
				.img {
					display: none;
				}
				.img--active {
					display: inline-block;
				}
			}
		}
		&--active {
			width: 600px;
			overflow: initial;
		}
		.btn-r {
			position: absolute;
			left: -36px;
			top: calc(50% - 36px);
			width: 36px;
			height: 100px;
			background: rgba(81, 167, 252, 0.8);
			border-radius: 8px 0px 0px 8px;
			display: flex;
			justify-content: center;
			align-items: center;
			&:hover {
				background: rgba(81, 167, 252, 0.9);
			}
			i {
				font-size: 20px;
				color: #fff;
				font-weight: bold;
			}
		}
	}
}
</style>
