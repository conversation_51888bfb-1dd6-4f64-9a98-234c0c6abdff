<template>
	<div class="content">
		<div>
			<h2 style="margin-bottom: 20px">对此次的维修服务满意度</h2>
			<el-radio-group v-model="formData.attitude" style="margin-bottom: 20px">
				<el-radio v-for="item in attitudeList" :key="item.cciValue" :label="item.cciValue">
					{{ item.shortName }}
				</el-radio>
			</el-radio-group>
		</div>
		<div>
			<h2 style="margin-bottom: 20px">对此次的维修服务打分</h2>
			<el-rate v-model="formData.serviceScore" show-score style="margin-bottom: 20px"></el-rate>
		</div>
		<div>
			<h2 style="margin-bottom: 20px">对此次的维修服务建议</h2>
			<el-input
				v-model="formData.remark"
				type="textarea"
				:autosize="{ minRows: 5, maxRows: 6 }"
				placeholder="请输入内容"
				style="margin-bottom: 20px"
			></el-input>
		</div>
		<div style="float: right">
			<el-button size="medium" type="primary" @click="handleSubmit">保存</el-button>
			<el-button size="medium" @click="resetPage">取消</el-button>
		</div>
	</div>
</template>
<script>
import api from '@/http/maintain/applicationApi';
import systemApi from '@/http/common/system.js';
import SnowflakeId from 'snowflake-id';

export default {
	name: 'Review',
	props: {
		reportId: {
			type: String
		}
	},
	data() {
		return {
			formData: {},
			attitudeList: []
		};
	},
	computed: {},
	watch: {},
	created() {
		this.getDictOption();
	},
	methods: {
		getDictOption() {
			this.$request({
				url: systemApi.findSysCode,
				params: {
					sysAppCode: 'hq_repair_attitude'
				},
				method: 'GET'
			}).then(res => {
				if (res.rCode == 0) {
					this.attitudeList = res.results;
				} else {
					this.$message.error(res.msg);
				}
			});
		},
		handleSubmit() {
			const snowflake = new SnowflakeId();
			this.formData.id = snowflake.generate();
			this.formData.reportId = this.reportId;
			this.$request({
				url: api.saveReview,
				data: this.formData,
				method: 'POST'
			}).then(res => {
				if (res.rCode === 0) {
					this.$message.success('评价成功！');
					this.$emit('closeReviewPage', 'success');
				} else {
					this.$message.error(res.msg);
				}
			});
		},
		resetPage() {
			this.$emit('closeReviewPage', 'cancel');
		}
	}
};
</script>
<style scoped>
.content {
	height: calc(100% - 21px);
	overflow-y: auto;
	margin: 20px 20px 0;
}
.span-label {
	font-size: 18px;
	font-weight: blod;
}
</style>
