<!--百度地图  选取坐标点-->
<template>
  <el-dialog
    title="地图"
    :visible.sync="showMap"
    v-if="showMap"
    width="80%"
    :append-to-body="true"
    @close="closeMap"
    @open="openMap"
    :close-on-click-modal="false" :close-on-press-escape="false"
  >
      <el-form ref="pointLngLatForm" :model="pointLngLat" style="display: flex;flex-direction: row">
          <el-form-item prop="lng" label="经度" label-width="40px">
            <el-input  size="mini" v-model="pointLngLat.lng" placeholder="经度"></el-input>
          </el-form-item>
          <el-form-item prop="lat" label="纬度" label-width="40px" style="margin-left: 10px">
            <el-input  size="mini" v-model="pointLngLat.lat" placeholder="纬度"></el-input>
          </el-form-item>
          <el-form-item style="margin-left: 20px">
            <el-button size="mini" type="primary" @click="checkedAddress">确 定</el-button>
          </el-form-item>
      </el-form>

    <baidu-map :center="center" :zoom="zoom" @ready="handler" class="baiduMap" @click="onClick"
               :scroll-wheel-zoom='true' v-loading='loadingMap'>
      <bm-view style="width: 100%; height:100%; flex: 1"></bm-view>
      <bm-local-search :keyword="addressKeyword" :auto-viewport="true" style="display: none"></bm-local-search>
      <bm-control>
        <bm-auto-complete v-model="addressKeyword" :sugStyle="{zIndex: 1}">
          <el-input style="margin-top: 10px" v-model="addressKeyword" size="mini" placeholder="请输入地名关键字"></el-input>
        </bm-auto-complete>
        <bm-navigation anchor="BMAP_ANCHOR_TOP_RIGHT"></bm-navigation>
        <bm-geolocation anchor="BMAP_ANCHOR_BOTTOM_RIGHT" :showAddressBar="true" :autoLocation="true"></bm-geolocation>
      </bm-control>
    </baidu-map>
  </el-dialog>
</template>

<script>
export default {
  props:{
    showMap:{
      type: Boolean,
      default: false,
    },
    // 经度
    lng:{
      type: String,
      default: "",
    },
    // 纬度
    lat:{//当父组件传入值时使用，避免父组件只通过v-model变量，使子父组件间循环传递值
      type: String,
      default: "",
    },
  },
  data() {
    return {
      // 地图相关
      loadingMap: false,
      BMap: '',
      map: '',
      showMap: false,
      addressKeyword: '',
      pointLngLat: {
        lng: '',
        lat: ''
      },
			center: {
				lng: 116.395645038,
				lat: 39.9299857781
			},
      zoom: 17
    }
  },
  watch: {
    // 经度变化监听
    'pointLngLat.lng'(val, oldVal) {
      if (val){
        this.addPoint(val,this.pointLngLat.lat)
      }
    },
    // 纬度变化监听
    'pointLngLat.lat'(val, oldVal) {
      if (val){
        this.addPoint(this.pointLngLat.lng,val)
      }
    }
  },
  methods: {
    open(lng,lat) {
      this.showMap = true
      this.$nextTick(() => {
        this.pointLngLat.lng = lng
        this.pointLngLat.lat = lat
      })
    },
    // 地图初始化
    handler({BMap, map}) {
      this.BMap = BMap
      this.map = map
      this.pointLngLat.lng = this.lng
      this.pointLngLat.lat = this.lat
    },
    // 添加点位
    addPoint(lng,lat) {
      let map = this.map
      let BMap = this.BMap
      map.clearOverlays()
      var point = new BMap.Point(lng, lat)
      let zoom = map.getZoom()
      setTimeout(() => {
        map.centerAndZoom(point, zoom)
      }, 0)
      var marker = new BMap.Marker(point) // 创建标注
      map.addOverlay(marker) // 将标注添加到地图中
    },
    // 点击地图
    onClick(e) {
      this.addressKeyword = ""
      this.pointLngLat = {lng: e.point.lng, lat: e.point.lat}
    },
    // 确认选择地址
    checkedAddress(){
      this.$emit("checkedAddress",this.pointLngLat);
      this.pointLngLat.lng = ""
      this.pointLngLat.lat = ""
    },
    closeMap(){
      this.pointLngLat.lng = ""
      this.pointLngLat.lat = ""
      this.$emit("closeMapDialog");
    },
    openMap(){
      this.pointLngLat.lng = this.lng
      this.pointLngLat.lat = this.lat
    }

  }
}
</script>

<style scoped>

.input-with-select {
  background-color: #fff;
}

.baiduMap {
  width: 100%;
  height: 450px;
}

/* 深度作用选择器  */
.el-card /deep/ .el-card__body {
  padding: 0px;
}

.mapBox1 {
  line-height: 550px;
}

.map {
  opacity: 0.9;
  margin-right: 10px;
  height: 550px;
}

.button {
  z-index: 2;
  position: absolute;
  top: 30%;
  left: 88%;
}
</style>
