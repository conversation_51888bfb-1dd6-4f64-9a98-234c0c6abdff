/*  接口地址定义
    方式1：
        //获取xxx信息
        export const getInfoStatistical = '/hr/shycInfo/getInfoStatistical.dhtml';
    调用方式1：
        import {getInfoStatistical} from '@/http/system.js';
        console.log(getInfoStatistical)

    方式2：
        //系统管理模块
        const system = {
            //用户、用户组树
            getDeptTree: '/bootdemo/simplesysDept/getDeptTree.djson',
        }
        export default system;
    调用方式2:
        import system from '@/http/system.js';
        console.log(system.getDeptTree)

*/

//接口地址
// const api = {
// 	// 消息发送
// 	jobCoFinancialServeList: '/ybzy/jobCoFinancialServe/listJson',
// 	jobCoFinancialServeInfo: '/ybzy/jobCoFinancialServe/info',
// 	jobCoFinancialServeSave: '/ybzy/jobCoFinancialServe/save',
// 	jobCoFinancialServeUpdate: '/ybzy/jobCoFinancialServe/update',
// 	jobCoFinancialServeDeleteById: '/ybzy/jobCoFinancialServe/deleteById',
// 	jobCoFinancialServeDeleteByIds: '/ybzy/jobCoFinancialServe/delete',
// 	jobCoFinancialServeTree: '/ybzy/jobCoFinancialServe/Tree',
// 	jobCoFinancialServeAudit: '/ybzy/jobCoFinancialServe/audit',
// 	jobCoFinancialServeChangeStatus: '/ybzy/jobCoFinancialServe/changeStatus',
// };
// export default api;
//
const api = {
	listJson: '/ybzy/hqbasematerial/listJson', // 原料分页列表
    listQuery: '/ybzy/hqbasematerial/listQuery', // 原料列表
	info: '/ybzy/hqbasematerial', // 获取
	save: '/ybzy/hqbasematerial/save', // 保存
	update: '/ybzy/hqbasematerial/update', // 修改
	deleteBatchIds: '/ybzy/hqbasematerial/deleteBatchIds', // 删除
	statusStop: '/ybzy/hqbasematerial/statusStop', // 批量停用
	statusOpen: '/ybzy/hqbasematerial/statusOpen', // 批量启用
	categoryList: '/ybzy/hqbasematerialcategory/listJson', // 分类列表获取
	categoryTree: '/ybzy/hqbasematerialcategory/getTreeList', // 原料分类树
};
export default api;
