<template>
	<div class="amend-standing">
		<div class="content">
			<div class="tree-list">
				<div class="btn-box">
					<el-button @click="typeAdd" type="primary" size="medium">新增分类</el-button>
					<el-button @click="typeEdite" size="medium">编辑分类</el-button>
					<el-button @click="typeDel" size="medium">删除分类</el-button>
				</div>
				<es-tree-group
					:tree="tree"
					@node-click="handleChange"
					:syncKeys="{ id: 'id', name: 'name' }"
				></es-tree-group>
			</div>
			<div class="table-wrapper">
				<es-data-table
					ref="table"
					:fit="true"
					:thead="thead_left"
					:page="true"
					:url="appListUrl"
					:param="tableParams"
					highlight-current-row
					numbers
					border
					full
					checkbox
					:selectable="selectableFunc"
					:toolbar="toolbar_left"
					@row-click="reloadValueLeft"
					@selection-change="handleSelectionChangeLeft"
					@btnClick="btnClickLeft"
				></es-data-table>
			</div>
			<div class="table-wrapper">
				<es-data-table
					ref="tableValue"
					:fit="true"
					:thead="thead_right"
					:page="true"
					:url="appMenuListUrl"
					:param="tableValeParams"
					highlight-current-row
					numbers
					border
					full
					checkbox
					:toolbar="toolbar_right"
					@btnClick="btnClickRight"
					@row-click="reloadValueRight"
					@selection-change="handleSelectionChangeRight"
				></es-data-table>
			</div>
		</div>
		<es-dialog
			v-if="visible"
			:title="title[type]"
			:visible.sync="visible"
			:height="dialogHeight[type]"
		>
			<AddNew
				v-if="visible"
				:id="id"
				:is-read-only="isReadOnly"
				:type="type"
				:select-info="selectInfo"
				:form-info="formData"
				@refresh="refreshData"
				@cancel="visible = $event"
			></AddNew>
		</es-dialog>
	</div>
</template>
<script>
import interfaceUrl from '@/http/platform/application.js';
import $ from 'eoss-ui/lib/utils/util';
import request from 'eoss-ui/lib/utils/http';
import AddNew from './component/AddNew';

export default {
	name: 'applicationList',

	components: { AddNew },
	data() {
		return {
			appListUrl: interfaceUrl.appList,
			appMenuListUrl: interfaceUrl.appMenuList,
			tableParams: {
				asc: 'true',
				orderBy: 'sortNum'
			},
			tableValeParams: {
				appId: '',
				asc: 'false',
				orderBy: 'sortNum'
			},
			tree: {
				defaultExpandAll: true,
				showSearch: false,
				data: [],
				defaultProps: {
					children: 'children',
					label: 'name'
				}
			},
			clickNode: null, //树结构点击事件保存的节点数据
			dialogHeight: {
				addApp: '80%',
				changeApp: '80%',
				addAppMenu: '80%',
				changeAppMenu: '80%',
				addType: '200px',
				changeType: '200px'
			},
			formData: null, // 回显传给AddNew的的数据
			chooseInfoLeftId: [], // 选择左侧表格选项时的id数据
			chooseInfoRightId: [], // 选择右侧表格选项时的id数据
			chooseInfoRight: null, // 选择表格选项时的数据
			preRowLeft: null, // 单点左侧某一行的数据
			preRowRight: null, // 单点右侧某一行的数据
			cciPid: '', // 上级编码ID
			selectInfo: null, // 选择的目录信息
			visible: false,
			isReadOnly: false,
			type: '', // 操作类型
			title: {
				addApp: '新增应用',
				changeApp: '修改应用',
				addAppMenu: '新增菜单',
				changeAppMenu: '修改菜单',
				addType: '新增类型',
				changeType: '修改类型'
			},
			id: '',
			toolbar_left: [
				{
					type: 'button',
					contents: [
						{
							type: 'primary',
							text: '新增应用',
							size: 'mini'
						},
						{
							type: 'default',
							text: '修改应用',
							size: 'mini'
						},
						{
							type: 'default',
							text: '删除',
							size: 'mini'
						}
					]
				},
				{
					type: 'search',
					contents: [
						{
							type: 'text',
							placeholder: '请输入关键字',
							name: 'keyword'
						}
					]
				}
			],
			toolbar_right: [
				{
					type: 'button',
					contents: [
						{
							type: 'primary',
							text: '新增菜单'
						},
						{
							type: 'default',
							text: '修改菜单'
						},
						{
							type: 'default',
							text: '删除'
						}
					]
				},
				{
					type: 'search',
					contents: [
						{
							type: 'text',
							placeholder: '请输入关键字',
							name: 'keyword'
						}
					]
				}
			],
			thead_left: [
				{
					title: '应用名称',
					align: 'left',
					field: 'name',
					showOverflowTooltip: true
				},
				{
					align: 'left',
					title: '应用地址',
					field: 'url',
					showOverflowTooltip: true
				},
				{
					width: '80px',
					align: 'left',
					title: '排序号',
					field: 'sortNum',
					showOverflowTooltip: true
				}
			],
			thead_right: [
				{
					title: '菜单名称',
					align: 'left',
					field: 'name',
					showOverflowTooltip: true
				},
				{
					align: 'left',
					title: '菜单地址',
					field: 'url',
					showOverflowTooltip: true
				},
				{
					width: '80px',
					align: 'left',
					title: '排序号',
					field: 'sortNum',
					showOverflowTooltip: true
				}
			]
		};
	},
	mounted() {
		this.initTree();
	},
	methods: {
		refreshData(falg) {
			if (falg === 'left') {
				this.$refs.table.reload();
			} else if (falg === 'right') {
				this.$refs.tableValue.reload();
			} else {
				this.initTree();
			}
		},
		// 点击行时只能单选 (左侧)
		reloadValueLeft(row, column, event) {
			this.tableValeParams.appId = row.id;
			this.cciPid = row.id;
			if (this.preRowLeft) {
				this.$refs.table.toggleRowSelection(this.preRowLeft, false);
			}
			this.$refs.table.toggleRowSelection(row, true);
			this.preRowLeft = row;
			this.cleaRightSelected(); //清空右表单的选中数据
		},
		// 点击行时只能单选 (右侧)
		reloadValueRight(row, column, event) {
			if (this.preRowRight) {
				this.$refs.tableValue.toggleRowSelection(this.preRowRight, false);
			}
			this.$refs.tableValue.toggleRowSelection(row, true);
			this.tableValeParams.vauleId = row.id;
			this.preRowRight = row;
		},
		// 选中value 行 用于编辑
		chooseValue(row, column, event) {
			this.tableValeParams.vauleId = row.id || '';
		},
		handleSelectionChangeLeft(val) {
			this.chooseInfoLeftId = [];
			val.forEach(item => {
				this.chooseInfoLeftId.push(item.id);
			});
		},
		handleSelectionChangeRight(val) {
			this.chooseInfoRightId = [];
			val.forEach(item => {
				this.chooseInfoRightId.push(item.id);
			});
		},
		btnClickLeft({ handle, row }) {
			switch (handle.text) {
				case '新增应用': {
					if (this.clickNode) {
						this.type = 'addApp';
						this.formData = {};
						this.formData.id = '';
						this.formData.typeId = this.clickNode.id;
						this.visible = true;
					} else {
						this.$confirm('请选择分类', '消息提示', {
							type: 'warning'
						});
					}
					break;
				}
				case '修改应用': {
					if (this.preRowLeft) {
						this.type = 'changeApp';
						this.formData = this.preRowLeft;
						this.visible = true;
					} else {
						this.$confirm('请选择待修改应用', '消息提示', {
							type: 'warning'
						});
					}
					break;
				}
				case '删除': {
					if (this.chooseInfoLeftId.length > 0) {
						this.$confirm('是否删除?', '提示', {
							cancelButtonText: '取消',
							confirmButtonText: '确定',
							type: 'warning'
						})
							.then(() => {
								const loading = $.loading(this.$loading, '加载中...');
								request({
									url: interfaceUrl.appDelete,
									method: 'POST',
									params: { ids: this.chooseInfoLeftId.join(',') }
								}).then(res => {
									loading.close();
									if (res.rCode === 0) {
										this.$message({
											type: 'success',
											message: res.msg
										});
										this.$refs.table.reload();
									} else {
										this.$message({
											type: 'error',
											message: res.msg
										});
									}
								});
							})
							.catch(function () {});
						break;
					} else {
						this.$confirm('请选择待删除应用', '消息提示', {
							type: 'warning'
						});
					}
				}
			}
		},
		btnClickRight({ handle, row }) {
			switch (handle.text) {
				case '新增菜单': {
					if (this.tableValeParams.appId) {
						this.formData = {};
						this.formData.id = '';
						this.formData.appId = this.preRowLeft.id;
						this.type = 'addAppMenu';
						this.isReadOnly = false;
						this.visible = true;
					} else {
						this.$confirm('请选择所属应用', '消息提示', {
							type: 'warning'
						});
					}
					break;
				}
				case '修改菜单': {
					if (this.tableValeParams.vauleId) {
						this.type = 'changeAppMenu';
						this.formData = this.preRowRight;
						this.visible = true;
					} else {
						this.$confirm('请选择待修改菜单', '消息提示', {
							type: 'warning'
						});
					}
					break;
				}
				case '删除': {
					if (this.chooseInfoRightId.length > 0) {
						this.$confirm('是否删除?', '提示', {
							confirmButtonText: '确定',
							cancelButtonText: '取消',
							type: 'warning'
						}).then(() => {
							const loading = $.loading(this.$loading, '加载中...');
							request({
								url: interfaceUrl.appMenuDelete,
								method: 'POST',
								params: { ids: this.chooseInfoRightId.join(',') },
								format: false
							}).then(res => {
								loading.close();
								if (res.rCode === 0) {
									this.$message({
										type: 'success',
										message: res.msg
									});
									this.$refs.tableValue.reload();
								} else {
									this.$message({
										type: 'error',
										message: res.msg
									});
								}
							});
						});
						break;
					} else {
						this.$confirm('请选择待删除菜单', '消息提示', {
							type: 'warning'
						});
					}
				}
			}
		},
		// 清空左侧表单的选中数据
		clearLeftSelected() {
			this.preRowLeft = null;
			this.chooseInfoLeftId = [];
		},
		// 清空右侧表单的选中数据
		cleaRightSelected() {
			this.preRowRight = null;
			this.chooseInfoRightId = [];
			this.tableValeParams.vauleId = '';
		},
		selectChange(val) {
			this.tableParams.pid = val.id;
			this.selectInfo = val;
		},
		handleNodeClick(val) {
			this.tableParams.categoryid = val.id;
			this.selectInfo = {
				label: val.label,
				pid: val.id,
				type: val.type
			};
		},
		selectableFunc(row, index) {
			return row.canDel;
		},
		// 初始化左侧树数据
		initTree() {
			this.clickNode = null;
			this.$request({
				url: interfaceUrl.listTree,
				method: 'get'
			}).then(res => {
				if (res.rCode == 0) {
					this.tree.data = res.results;
					this.unionSelectData = res.results;
				}
			});
		},
		// 树节点的点击事件
		handleChange(tree, data) {
			this.clickNode = data;
			let nodeId = data?.id || '' 
			// 设置应用表格的参数
			this.$set(this.tableParams, 'typeId', nodeId);
			// 清空菜单表单数据
			this.$set(this.tableValeParams, 'appId', nodeId);
			// 对表格选中数据的清空
			this.clearLeftSelected();
			this.cleaRightSelected();
		},
		// 类型数据新增
		typeAdd() {
			this.type = 'addType';
			this.formData = {};
			this.formData.id = '';
			this.formData.pid = this.clickNode?.id || 'root';
			this.visible = true;
		},
		// 类型数据编辑
		typeEdite() {
			if (this.clickNode) {
				this.type = 'changeType';
				this.formData = this.clickNode;
				this.visible = true;
			} else {
				this.$confirm('请选择待修改类型', '消息提示', {
					type: 'warning'
				});
			}
		},
		// 类型数据删除
		typeDel() {
			if (this.clickNode) {
				this.$confirm('是否删除?', '提示', {
					confirmButtonText: '确定',
					cancelButtonText: '取消',
					type: 'warning'
				}).then(() => {
					const loading = $.loading(this.$loading, '加载中...');
					request({
						url: interfaceUrl.typeDel,
						method: 'POST',
						params: { id: this.clickNode.id},
					}).then(res => {
						loading.close();
						if (res.rCode === 0) {
							this.$message({
								type: 'success',
								message: res.msg
							});
							this.initTree();
							this.handleChange(null,null)
						} else {
							this.$message({
								type: 'error',
								message: res.msg
							});
						}
					}).catch(()=>{
						loading.close();
					});
				});
			} else {
				this.$confirm('请选择待删除类型', '消息提示', {
					type: 'warning'
				});
			}
		}
	}
};
</script>
<style scoped lang="scss">
.amend-standing {
	padding: 10px;
	height: 100%;
	display: flex;
	justify-content: space-between;

	.content {
		width: 100%;
		flex: 1;
		display: flex;
		justify-content: space-between;
		height: 100%;
		margin-left: 10px;

		.table-wrapper {
			height: 100%;
			width: 40%;

			.es-data-table {
				width: 100%;
			}
		}
	}

	::v-deep .el-button--medium {
		font-size: 12px;
	}
}
</style>
<style lang="scss" scoped>
::v-deep .es-dialog {
	border: 1px solid red;
	height: 80%;
}
</style>
