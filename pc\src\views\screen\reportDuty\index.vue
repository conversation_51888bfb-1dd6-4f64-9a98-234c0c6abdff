<template>
	<div class="content">
		<es-data-table
			ref="table"
			:row-style="tableRowClassName"
			:full="true"
			:fit="true"
			:thead="thead"
			:toolbar="toolbar"
			:border="true"
			:page="pageOption"
			:url="dataTableUrl"
			:numbers="true"
			:param="params"
			close
			form
			@btnClick="btnClick"
			@sort-change="sortChange"
			@submit="hadeSubmit"
		></es-data-table>
		<!--   编辑  -->
		<es-dialog :drag="false" :title="formTitle" :visible.sync="open" append-to-body>
			<el-form
				v-if="open"
				ref="form"
				:model="form"
				:rules="rules"
				label-width="100px"
				:disabled="formTitle == '查看'"
			>
				<el-form-item label="标题" prop="title">
					<el-input v-model="form.title" placeholder="请输入标题" />
				</el-form-item>
				<el-form-item label="描述" prop="content">
					<el-input
						v-model="form.content"
						type="textarea"
						placeholder="请输入描述"
						:autosize="{ minRows: 4, maxRows: 4 }"
						:style="{ width: '100%' }"
					></el-input>
				</el-form-item>
				<el-form-item label="事件类型" prop="eventType">
					<el-select
						v-model="form.eventType"
						placeholder="请选择事件类型"
						clearable
						:style="{ width: '100%' }"
					>
						<el-option
							v-for="(item, index) in eventTypeOption"
							:key="index"
							:label="item.label"
							:value="item.value"
							:disabled="item.disabled"
						></el-option>
					</el-select>
				</el-form-item>
				<el-form-item label="发生地址" prop="address">
					<el-input v-model="form.address" placeholder="请输入发生地址" />
				</el-form-item>
				<el-form-item label="发生时间" prop="happenTime">
					<el-date-picker
						v-model="form.happenTime"
						type="datetime"
						placeholder="选择日期"
						:picker-options="reportDateOptions"
						style="width: 100%"
						value-format="yyyy-MM-dd HH:mm:ss"
						format="yyyy-MM-dd HH:mm:ss"
					></el-date-picker>
				</el-form-item>
				<el-form-item label="备注" prop="remark">
					<el-input
						v-model="form.remark"
						type="textarea"
						placeholder="请输入备注"
						:autosize="{ minRows: 3, maxRows: 3 }"
						:style="{ width: '100%' }"
					></el-input>
				</el-form-item>
				<el-form-item key="selectDeptList" label="经纬度" prop="lngLat">
					<lng-lat v-model="form.lngLat"></lng-lat>
				</el-form-item>
				<el-form-item label="上传照片">
					<es-upload
						v-bind="attrs"
						ref="upload"
						select-type="icon-plus"
						list-type="picture-card"
					></es-upload>
				</el-form-item>
			</el-form>
			<div v-if="form.flow?.length > 0 && formTitle === '查看'" class="flow-box">
				<div class="flow-title">流程</div>
				<el-timeline :reverse="false" class="flow-timeline">
					<el-timeline-item
						v-for="(activity, index) in form.flow"
						:key="index"
						:timestamp="activity.time"
						:type="form.flow?.length === index + 1 ? 'primary' : ''"
					>
						{{ activity.desc }}
					</el-timeline-item>
				</el-timeline>
			</div>

			<div slot="footer" class="dialog-footer">
				<el-button v-show="formTitle != '查看'" type="primary" @click="submitForm">确 定</el-button>
				<el-button @click="cancel">取 消</el-button>
			</div>
		</es-dialog>
		<!--  派发  -->
		<es-dialog 
			:drag="false"
			:title="formTitle"
			:visible.sync="dispatchOpen"
			width="800px"
			append-to-body
		>
			<el-form ref="dispatchForm" :model="dispatchForm" :rules="rules2" label-width="100px">
				<el-row>
					<el-col :span="12">
						<el-form-item label="是否有效" prop="flag">
							<el-radio-group v-model="dispatchForm.flag">
								<el-radio :label="1">是</el-radio>
								<el-radio :label="2">否</el-radio>
							</el-radio-group>
						</el-form-item>
					</el-col>
					<el-col :span="12">
						<el-form-item label="事件性质" prop="flag2">
							<el-radio-group v-model="dispatchForm.flag2">
								<el-radio label="一般">一般</el-radio>
								<el-radio label="紧急">紧急</el-radio>
							</el-radio-group>
						</el-form-item>
					</el-col>
				</el-row>
				
				<el-form-item label="事件类型" prop="eventType">
					<el-select v-model="dispatchForm.eventType" @change="changeType" style="width: 100%;">
						<el-option v-for="item in typeList" :value="item.cciValue" :label="item.shortName" />
					</el-select>
				</el-form-item>
	
				<el-form-item label="事件等级" prop="eventLevel">
					<el-select v-model="dispatchForm.eventLevel" style="width: 100%;">
						<el-option value="一般" label="一般" />
						<el-option value="较大" label="较大" />
						<el-option value="重大" label="重大" />
						<el-option value="特大" label="特大" />
					</el-select>
				</el-form-item>
				<el-form-item label="启动预案" prop="reservePlan">
					<div style="display: flex;">
						<el-select v-model="dispatchForm.reservePlan" style="width: 100%;">
							<el-option v-for="item in chooseList" :value="item.path" :label="item.name" />
						</el-select>
						<el-button type="primary" @click="showProcess">预览</el-button>
					</div>
				</el-form-item>
			</el-form>
			<div class="dialog-footer" style="position: absolute;bottom: 20px;right: 20px;">
				<el-button type="primary" @click="submitSure">确认</el-button>
				<el-button @click="cancel">取 消</el-button>
			</div>
		</es-dialog>

		<es-dialog 
			:drag="false"
			title="预案预览"
			:visible.sync="processVisible"
			width="1000px"
		>	
			<div style="padding: 20px;margin-bottom: 50px;">
				<el-steps :active="processStep" finish-status="success">
					<el-step :title="processTitle[0]"></el-step>
					<el-step :title="processTitle[1]"></el-step>
					<el-step :title="processTitle[2]"></el-step>
				</el-steps>
				<template v-if="processStep === 0">
					<div style="padding: 10px 0;">
						<el-divider content-position="left">领导小组</el-divider>
					</div>
					<el-table :data="tableData1" style="width: 100%">
						<el-table-column prop="key1" label="姓名" ></el-table-column>
						<el-table-column prop="key2" label="电话" ></el-table-column>
						<el-table-column prop="key3" label="小组职务" ></el-table-column>
						<el-table-column prop="key4" label="部门" ></el-table-column>
						<el-table-column prop="key5" label="岗位" ></el-table-column>
					</el-table>
					<div style="padding: 10px 0;">
						<el-divider content-position="left">
							安全事故报送组
							<el-tooltip effect="dark" content="(接受我校安全事故的信息报告，并及时报告学校食品中毒领导小组组长、医院、区卫生局、区教育局)">
								<i class="el-icon-info"/>
							</el-tooltip>
						</el-divider>
					</div>
					<el-table :data="tableData2" style="width: 100%">
						<el-table-column prop="key1" label="姓名" ></el-table-column>
						<el-table-column prop="key2" label="电话" ></el-table-column>
						<el-table-column prop="key3" label="小组职务" ></el-table-column>
						<el-table-column prop="key4" label="部门" ></el-table-column>
						<el-table-column prop="key5" label="岗位" ></el-table-column>
					</el-table>
					<div style="padding: 10px 0;">
						<el-divider content-position="left">
							事故施救组
							<el-tooltip effect="dark" content="(负责安全事故现场施救及协调将中毒者送往第五人民医院救治。)">
								<i class="el-icon-info"/>
							</el-tooltip>
						</el-divider>
					</div>
					<el-table :data="tableData3" style="width: 100%">
						<el-table-column prop="key1" label="姓名" ></el-table-column>
						<el-table-column prop="key2" label="电话" ></el-table-column>
						<el-table-column prop="key3" label="小组职务" ></el-table-column>
						<el-table-column prop="key4" label="部门" ></el-table-column>
						<el-table-column prop="key5" label="岗位" ></el-table-column>
					</el-table>
				</template>
				<template v-if="processStep === 1">
					<div style="color: #444;font-size: 18px;letter-spacing: 1px;padding-top: 20px;">
						对食物中毒人员，症状属较稍微的，校医院应就地组织力气刚好对发病人员进行应急的对症救治。做好病状纪录并完好留存病人的吐泻物。中毒严峻者，应刚好转到就近的医院，并携带具体的病案记录。校办公室应刚好调配车辆，以最短时间送至医院救治，必要时传呼 120 救援车将患者紧急运输医院救治。
					</div>
				</template>
				<template v-if="processStep === 2">
					<div style="padding: 10px 0;"><el-divider content-position="left">校医院</el-divider></div>
					<div style="color: #444;font-size: 18px;letter-spacing: 1px;padding-top: 10px;">
						中毒事务发生后，校医务室要主动帮助卫生监督人员刚好向中毒人员了解就餐场所、就餐人数、所食食品、发病人数及所出现的症状，现场检查就餐场所的卫生状况，卫生许可证及从业人员健康证的办理状况，分析中毒缘由及可能造成中毒的食品，封存现场及可疑食品，追查食品及原料的来源，追缴售出的可疑食品，对病人的吐泻物及可疑食品进行取样，送上级疫检部门检验
					</div>
					<div style="padding: 10px 0;"><el-divider content-position="left">安全保卫处</el-divider></div>
					<div style="color: #444;font-size: 18px;letter-spacing: 1px;padding-top: 10px;">
						应马上派员进行现场秩序的维持，调查状况分析是否有人为投毒的可能限制可疑人员，必要时报请公安部门介入，并对中毒现场进行隔离和戒严。同时严格审核进货渠道对可疑食品的供货方、渠道缜密进行排查。对不能解除饮用水的因素造成的食物中毒，校总务处应马上停止供水，留样等待检测
					</div>
				</template>
			</div>
			<div class="dialog-footer" style="position: absolute;bottom: 20px;right: 20px;">
				<el-button v-if="processStep > 0"  @click="processNext(-1)">上一步</el-button>
				<el-button v-if="processStep === 2" type="primary"  @click="processDown">下发</el-button>
				<el-button v-else  @click="processNext(1)">下一步</el-button>
				<el-button  @click="processVisible = false">取 消</el-button>
			</div>
		</es-dialog>

		<es-dialog 
			:drag="false"
			title="删除"
			size="sm"
			:visible.sync="showDelete"
			:close-on-click-modal="false"
			:middle="true"
			width="260px"
			height="140px"
		>
			<div>确定要删除该条数据吗</div>
			<div class="btn-box">
				<div class="btn theme" @click="deleteRow">确定</div>
				<div class="btn" @click="showDelete = false">取消</div>
			</div>
		</es-dialog>
	</div>
</template>

<script>
import interfaceUrl from '@/http/screen/reportEvent';
import SnowflakeId from 'snowflake-id';
import LngLat from '@/components/lngLat.vue';

export default {
	name: 'YjRefuge', //避难场所
	components: { LngLat },
	data() {
		return {
			value: [],
			tabs: {
				employee: {
					label: '用户选择',
					name: 'employee',
					url: '/sys/v1/mecpSys/getSelectorOrgTree.dhtml',
					data: [],
					nodeData: '',
					selection: [],
					value: [],
					param: {
						showarea: 1,
						id: 0,
						filid: 'all'
					}
				}
			},
			selection: '/sys/v1/mecpSys/getSelectorOrgDetail.dhtml',
			//发生时间配置
			reportDateOptions: {
				disabledDate(time) {
					return time.getTime() > Date.now();
				}
			},
			eventTypeOption: [
				{ label: '防火', value: '防火' },
				{ label: '安全', value: '安全' }
			],
			ownId: '',
			dataTableUrl: interfaceUrl.listJson,
			showForm: false,
			showDelete: false,
			formData: {},
			formTitle: '编辑',
			editBtn: {
				type: 'submit',
				skin: 'lay-form-btns',
				contents: [
					{
						type: 'primary',
						text: '确定',
						event: 'confirm'
					},
					{
						type: 'reset',
						text: '取消',
						event: 'cancel'
					}
				]
			},
			cancelBtn: {
				type: 'submit',
				skin: 'lay-form-btns',
				contents: [
					{
						type: 'reset',
						text: '取消',
						event: 'cancel'
					}
				]
			},
			toolbar: [
				{},
				{
					type: 'search',
					contents: [
						{
							type: 'text',
							name: 'keyword',
							placeholder: '关键字查询'
						}
					]
				}
			],
			thead: [
				{
					title: '标题',
					align: 'left',
					field: 'title',
					sortable: 'custom',
					showOverflowTooltip: true
				},
				{
					title: '内容',
					align: 'left',
					field: 'content',
					sortable: 'custom',
					showOverflowTooltip: true
				},
				{
					title: '状态',
					align: 'left',
					field: 'status',
					sortable: 'custom',
					showOverflowTooltip: true,
					sysCode: 'zhdd_event_state'
				},
				{
					title: '地址',
					align: 'left',
					field: 'address',
					sortable: 'custom',
					showOverflowTooltip: true
				},
				{
					title: '经度',
					align: 'left',
					field: 'longitude',
					sortable: 'custom',
					showOverflowTooltip: true
				},
				{
					title: '纬度',
					align: 'left',
					field: 'latitude',
					sortable: 'custom',
					showOverflowTooltip: true
				},
				{
					title: '上报时间',
					align: 'left',
					field: 'reportTime',
					sortable: 'custom',
					showOverflowTooltip: true
				},
				{
					title: '发生时间',
					align: 'left',
					field: 'happenTime',
					sortable: 'custom',
					showOverflowTooltip: true
				},
				{
					title: '上报人姓名',
					align: 'left',
					field: 'reportUserName',
					sortable: 'custom',
					showOverflowTooltip: true
				},
				{
					title: '上报人联系电话',
					align: 'left',
					field: 'reportUserTel',
					sortable: 'custom',
					showOverflowTooltip: true
				},
				{
					title: '备注',
					align: 'left',
					field: 'remark',
					sortable: 'custom',
					showOverflowTooltip: true
				},
				{
					title: '操作',
					type: 'handle',
					width: 180,
					template: '',
					events: [
						{
							code: 'sure',
							text: '确认',
						},
						{
							code: 'view',
							text: '查看'
						},
					]
				}
			],
			pageOption: {
				layout: 'total, prev, pager, next, jumper',
				pageSize: 10,
				// hideOnSinglePage: true,
				position: 'right',
				current: 1,
				pageNum: 1
			},
			params: {
				asc: 'true',
				orderBy: 'createTime',
				status: 1,
			},
			open: false,
			dispatchOpen: false,
			// 表单参数
			form: {
				id: null,
				title: null,
				content: null,
				eventType: null,
				status: null,
				address: null,
				longitude: null,
				latitude: null,
				reportTime: null,
				happenTime: null,
				reportUserId: null,
				reportUserName: null,
				reportUserTel: null,
				remark: null,
				lngLat: [],
				pwd: null
			},
			dispatchForm: {
				flag: 1,
				reservePlan: ''
			},
			// 表单校验
			rules: {
				title: [{ required: true, message: '需要填写信息', trigger: 'blur' }],
				content: [{ required: true, message: '需要填写信息', trigger: 'blur' }],
				eventType: [{ required: true, message: '需要填写信息', trigger: 'blur' }],
				address: [{ required: true, message: '需要填写信息', trigger: 'blur' }],
				happenTime: [{ required: true, message: '需要填写信息', trigger: 'blur' }],
				dealContent: [{ required: true, message: '需要填写信息', trigger: 'blur' }]
			},
			rules2: {
				flag: [{ required: true, message: '请选择是否有效', trigger: 'blur' }],
			},
			imgListSize: 1,
			ydlxOptions: [], //用地类型
			typeList: [],
			allEventList: [],
			chooseList: [],
			processVisible: false,
			processTitle: ['相关分组和人员名单','应急处置流程','部门分工'],
			processStep: 0,
			tableData1: [
				{key1:'邹勇', key2: '15892515352',key3: '组长', key4: '校领导', key5: '校长' },
				{key1:'王洪益', key2: '18780669758',key3: '组长', key4: '校领导', key5: '副校长' },
				{key1:'吴强', key2: '18783181314',key3: '组员', key4: '学生工作部', key5: '	部长' },
				{key1:'刘元浩', key2: '15892517778',key3: '组员', key4: '基建后勤处', key5: '部长' },
				{key1:'马飞', key2: '13688290166',key3: '组员', key4: '安全保卫处', key5: '处长' },
			],
			tableData2: [
				{key1:'吴强', key2: '18783181314',key3: '组员', key4: '学生工作部', key5: '	部长' },
				{key1:'郭超', key2: '18990989377',key3: '组员', key4: '学生工作部-学生科', key5: '科长' },
			],
			tableData3: [
				{key1:'刘元浩', key2: '15892517778',key3: '组员', key4: '基建后勤处', key5: '部长' },
				{key1:'曾晗', key2: '13890922697',key3: '副组长', key4: '学生工作部', key5: '副部长' },
				{key1:'马飞', key2: '13688290166',key3: '组员', key4: '安全保卫处', key5: '处长' },
				{key1:'张旭', key2: '13778979170',key3: '组员', key4: '学生工作部-学生科', key5: '科长' },
				{key1:'陈宏', key2: '13990927979',key3: '组员', key4: '学生工作部-宿管中心', key5: '科长' },
				{key1:'夏明', key2: '13419260789',key3: '组员', key4: '安全保卫处-保卫科', key5: '科长' },
			]
		};
	},
	computed: {
		attrs() {
			return {
				code: 'zhdd_report_event',
				ownId: this.ownId,
				preview: true,
				download: true,
				operate: true
			};
		}
	},
	watch: {},
	created() {
		this.getDict()
		this.queryYapzList()
	},
	mounted() {
	},
	methods: {
		processDown() {
			
			this.$confirm(`确认下发？`, '提示', {
				confirmButtonText: '确认',
				cancelButtonText: '取消',
				type: 'warning'
			})
				.then(() => {

					this.processVisible = false
					this.dispatchOpen = false
					this.$message.success('下发成功')
				})
				.catch(() => {});
		},
		processNext(num) {
			this.processStep += num
		},
		showProcess() {
			// if (!this.dispatchForm.reservePlan) {
			// 	this.$message.error('请选择启动预案');
			// 	return
			// }
			this.processStep = 0
			this.processVisible = true
		},
		changeType() {
			const type = this.dispatchForm.eventType
			const list = this.allEventList.filter(item=>item.category == type+'')
			this.dispatchForm.reservePlan = ''
			console.log('list: ', this.allEventList, this.dispatchForm.eventType)
			console.log('list: ', list)
			this.chooseList = list
		},
		getDict() {
			this.$utils.findSysCodeList('zhdd_yuan_type').then(res=>{
				console.log('findSysCodeList: ', res)
				this.typeList = res['zhdd_yuan_type']
			})
			// {
			// 		title: '类型',
			// 	align: 'left',
			// 	field: 'category',
			// 	sortable: 'custom',
			// 	showOverflowTooltip: true,
			// 	sysCode: 'zhdd_yuan_type'
			// },
		},
		// 预案配置列表
		queryYapzList() {
			this.$request({
				url: interfaceUrl.yapzList,
				data: { 
					pageSize: 999,
					pageNo: 1,
				},
				method: 'get'
			}).then(res=>{
				console.log('res:  ',res)
				this.allEventList = res.results.records
			})
		},
		handleConfirm(res) {
			console.log(this.value, res, 3333);
		},
		handleCancel() {},
		/**
		 * 表格行高
		 */
		tableRowClassName({ row, rowIndex }) {
			let styleRes = {
				height: '54px !important'
			};
			return styleRes;
		},
		inputChange(key, value) {},
		hadeSubmit(data) {},
		deleteRow() {
			this.$request({
				url: interfaceUrl.deleteBatchIds,
				data: { ids: this.deleteId },
				method: 'POST'
			}).then(res => {
				if (res.rCode == 0) {
					this.$refs.table.reload();
					this.$message.success('删除成功');
				} else {
					this.$message.error(res.msg);
				}
				this.showDelete = false;
			});
		},
		/**
		 * 排序变化事件
		 * @param {*} column
		 * @param {*} prop
		 * @param {*} order
		 */
		sortChange(column, prop, order) {
			if (column.order == 'ascending') {
				//升序
				this.params = {
					asc: 'true',
					status: 1,
					orderBy: column.prop
				};
			} else if (column.order == 'descending') {
				//降序
				this.params = {
					asc: 'false',
					status: 1,
					orderBy: column.prop
				};
			} else {
				//不排序
				this.params = {
					asc: 'false',
					status: 1,
					orderBy: 'createTime'
				};
			}
			this.$refs.table.reload();
		},
		btnClick(res) {
			let code = res.handle.code;
			switch (code) {
				case 'sure':
					this.formTitle = '确认';
					this.$request({
						url: interfaceUrl.info + '/' + res.row.id,
						method: 'GET'
					}).then(res => {
						if (res.rCode == 0) {
							this.dispatchForm = {
								...res.results,
								flag: 1,
								reservePlan: ''
							}
							this.dispatchOpen = true;
						}
					});
					break;
				case 'add':
					// 新增
					this.reset();
					this.formTitle = '新增';
					const snowflake = new SnowflakeId();
					let id = snowflake.generate();
					this.ownId = id;
					this.form.id = id;
					this.open = true;
					break;
				case 'edit':
					this.reset();
					this.formTitle = '编辑';
					this.ownId = res.row.id;
					this.$request({
						url: interfaceUrl.info + '/' + res.row.id,
						method: 'GET'
					}).then(res => {
						if (res.rCode == 0) {
							this.form = res.results;
							this.$set(this.form, 'lngLat', [this.form.longitude, this.form.latitude]);
							this.open = true;
						}
					});
					break;
				case 'view':
					this.reset();
					this.formTitle = '查看';
					this.ownId = res.row.id;
					this.$request({
						url: interfaceUrl.info + '/' + res.row.id,
						method: 'GET'
					}).then(res => {
						if (res.rCode == 0) {
							this.form = res.results;
							this.$set(this.form, 'lngLat', [this.form.longitude, this.form.latitude]);
							this.open = true;
						}
					});
					break;
				case 'delete':
					this.deleteId = res.row.id;
					this.showDelete = true;
					break;
				default:
					break;
			}
		},
		//提交按钮
		submitForm() {
			this.$refs['form'].validate(valid => {
				if (valid) {
					let url = '';
					if (this.formTitle == '新增') {
						url = interfaceUrl.save;
						let Base64 = require('js-base64').Base64;
						this.form.password = Base64.encode(this.form.pwd);
					} else {
						url = interfaceUrl.update;
					}
					if (this.form.lngLat && this.form.lngLat.length == 2) {
						this.form.longitude = this.form.lngLat[0];
						this.form.latitude = this.form.lngLat[1];
					}
					if (!this.form.longitude || !this.form.latitude) {
						this.$message.error('请正确填写经纬度');
						return false;
					}
					console.log(this.form);
					this.$request({
						url: url,
						data: this.form,
						method: 'POST'
					}).then(res => {
						if (res.rCode == 0) {
							this.$refs.table.reload();
							this.$message.success(this.formTitle + '成功');
							this.cancel();
						} else {
							this.$message.error(res.msg);
						}
						this.showDelete = false;
					});
				}
			});
		},
		//确认按钮
		submitSure() {
			this.$refs['dispatchForm'].validate(valid => {
				if (valid) {
					let url = this.dispatchForm.flag == 1? interfaceUrl.update : interfaceUrl.deleteBatchIds;
					console.log(this.dispatchForm);
					this.$request({
						url: url,
						data: this.dispatchForm.flag == 1 ?  {
							...this.dispatchForm,
							status: 2
						} : { ids: this.dispatchForm.id},
						method: 'POST'
					}).then(res => {
						if (res.rCode == 0) {
							this.$refs.table.reload();
							this.$message.success(this.dispatchForm.flag == 1 ? '确认成功': '取消成功');
							this.cancel();
						} else {
							this.$message.error(res.msg);
						}
						this.showDelete = false;
					});
				}
			});
		},
		//终止按钮
		submitStop() {
			let url = interfaceUrl.breakHandle;
			this.$confirm('确定要终止该事件？', '终止', {
				confirmButtonText: '确定',
				cancelButtonText: '取消',
				type: 'warning'
			})
				.then(() => {
					this.$request({
						url: url,
						data: this.dispatchForm,
						method: 'POST'
					}).then(res => {
						if (res.rCode == 0) {
							this.$refs.table.reload();
							this.$message.success('事件终止成功');
							this.cancel();
						} else {
							this.$message.error(res.msg);
						}
						this.showDelete = false;
					});
				})
				.catch(() => {});
		},
		// 取消按钮
		cancel() {
			this.open = false;
			this.dispatchOpen = false;
			this.reset();
		},
		// 表单重置
		reset() {
			this.form = {
				id: null,
				title: null,
				content: null,
				eventType: null,
				status: null,
				address: null,
				longitude: null,
				latitude: null,
				reportTime: null,
				happenTime: null,
				reportUserId: null,
				reportUserName: null,
				reportUserTel: null,
				remark: null,
				lngLat: [],
				pwd: null
			};
		}
	}
};
</script>
<style scoped lang="scss">
@import '../../../assets/style/style.scss';

.content {
	width: 100%;
	height: 100%;
}

::v-deep {
	.el-dialog {
		// max-height: 100%;
		// top: 35%;
		.el-dialog__body {
			overflow-y: auto;
			.btn-box {
				margin-top: 20px;
				display: flex;
				justify-content: flex-end;

				.btn {
					padding: 5px 10px;
					color: #666;
					border: 1px solid #eee;
					cursor: pointer;
					margin-right: 5px;
					border-radius: 4px;
					// &.theme {
					// 	background: $--color-primary;
					// 	color: #fff;
					// 	border-color: $--color-primary;
					// }
				}
			}
		}
	}
}
.flow-box {
	display: flex;
	.flow-title {
		width: 100px;
		height: 40px;
		line-height: 36px;
		padding: 2px 10px;
		text-align: end;
	}
}
</style>
