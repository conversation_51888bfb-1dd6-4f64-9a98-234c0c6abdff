<template>
	<div class="main-center-left">
		<!--  表格  -->
		<div class="main-center-table">
			<div class="main-center-table-title title">
				<img class="title-img" src="@ast/images/sys/card.png" alt="" />
				<div>考勤记录</div>
			</div>
			<es-data-table
				ref="table"
				:thead="thead"
				:page="true"
				:url="tableUrl"
				:toolbar="toolbar"
				:response="func"
				method="get"
				style="width: 100%"
				height="300px"
				@change="pageSizeChange"
				@current="pageCurrentChange"
			></es-data-table>
		</div>
		<!--  图表  -->
		<div class="main-center-chart">
			<div class="main-center-chart-title title">
				<img class="title-img" src="@ast/images/sys/card.png" alt="" />
				<div>周出勤率</div>
			</div>
			<div id="statisticsEcharts" class="main-center-chart-con"></div>
		</div>
	</div>
</template>

<script>
export default {
	name: 'TableModule',
	data() {
		return {
			tableUrl: '', //表格数据请求地址
			/**表格头部*/
			thead: [
				{
					title: '职工号',
					field: 'formName',
					fixed: false
				},
				{
					title: '姓名',
					field: 'formName',
					fixed: false
				},
				{
					title: '部门',
					field: 'formName',
					fixed: false
				},
				{
					title: '上班时间',
					field: 'formName',
					fixed: false
				},
				{
					title: '下班时间',
					field: 'formName',
					fixed: false
				},
				{
					title: '考勤日期',
					field: 'formName',
					fixed: false
				}
			],
			/**表格搜搜*/
			toolbar: [
				{
					type: 'search',
					reset: true,
					contents: [
						{
							type: 'text',
							name: 'keyword',
							placeholder: '输入关键字搜索'
						},
						{
							type: 'select',
							label: '部门：',
							placeholder: '请选择部门',
							name: 'type',
							event: 'multipled',
							data: [
								{
									value: '1',
									name: 'g'
								},
								{
									value: '11',
									name: 'gg'
								}
							],
							verify: 'required',
							col: 6
						},
						{
							name: 'startdate',
							placeholder: '请选择考勤日期',
							col: 6,
							label: '时间',
							type: 'datetimerange',
							unlinkPanels: true,
							value: ''
						}
					]
				}
			],
			/**考勤统计的表格配置*/
			statisticsOption: {
				tooltip: {
					trigger: 'axis',
					axisPointer: {
						type: 'shadow'
					}
				},
				legend: {
					data: ['出勤率', '出勤数']
				},
				xAxis: [
					{
						type: 'category',
						data: ['10-2', '10-3', '10-4', '10-5', '10-6'],
						axisPointer: {
							type: 'shadow'
						},
						axisTick: {
							show: false
						}
					}
				],
				yAxis: [
					{
						type: 'value',
						name: '单位：人',
						min: 0,
						max: 10000,
						interval: 2000,
						axisLabel: {
							formatter: '{value}'
						},
						nameTextStyle: {
							padding: [0, 65, 0, 0]
						}
					},
					{
						type: 'value',
						name: '单位：%',
						min: 0,
						max: 100,
						interval: 20,
						axisLabel: {
							formatter: '{value}'
						},
						nameTextStyle: {
							padding: [0, 0, 0, 65]
						}
					}
				],
				series: [
					{
						name: '出勤数',
						type: 'bar',
						itemStyle: {
							color: {
								type: 'linear',
								x: 0.5,
								y: 0,
								x2: 0.5,
								y2: 1,
								colorStops: [
									{
										offset: 0,
										color: 'rgba(0, 118, 232, 1)' // 0% 处的颜色
									},
									{
										offset: 1,
										color: 'rgba(233, 243, 253, 1)' // 100% 处的颜色
									}
								],
								global: false // 缺省为 false
							}
						},
						barWidth: 25,
						tooltip: {
							valueFormatter: function (value) {
								return value + '人';
							}
						},
						data: [1800, 3600, 6800, 8000, 9200]
					},
					{
						name: '出勤率',
						type: 'line',
						yAxisIndex: 1,
						symbol: 'circle', //标记的图形为实心圆
						symbolSize: 10, //标记的大小
						itemStyle: {
							normal: {
								color: 'rgb(4 120 232)', // 拐点的颜色
								borderColor: '#ffffff', // 拐点边框颜色
								borderWidth: 2 // 拐点边框大小
							}
						},
						lineStyle: {
							color: 'rgb(4 120 232)'
						},
						tooltip: {
							valueFormatter: function (value) {
								return value + '%';
							}
						},
						data: [18, 36, 68, 80, 92]
					}
				]
			},
			statisticsChart: null // 考勤统计的表格实例
		};
	},
	mounted() {
		this.initStatistics();
	},
	methods: {
		/**初始化考勤统计表格*/
		initStatistics() {
			let charDom = document.getElementById('statisticsEcharts');
			this.statisticsChart = this.$echarts.init(charDom);
			this.statisticsChart.setOption(this.statisticsOption);
			window.addEventListener('resize', () => {
				this.statisticsChart && this.statisticsChart.resize();
			});
		},
		/**表格返回数据*/
		func() {},
		/**表格分页变化派发时间*/
		pageSizeChange() {},
		pageCurrentChange() {}
	}
};
</script>

<style scoped lang="scss">
@import '@ast/style/public.scss';
.main-center {
	&-left {
		width: 67%;
		border-radius: 8px;
	}
	/**表格*/
	&-table {
		width: 100%;
		position: relative;
		background: #fff;
		padding-top: 4px;
		border-radius: 8px;
		&-title {
			position: absolute;
			z-index: 66;
			left: 20px;
			top: 12px;
		}
		::v-deep .es-date-picker-range {
			max-width: 280px !important;
		}
		::v-deep .el-form-item__content {
			line-height: normal !important;
		}
		::v-deep .es-data-table-content {
			background: #fff !important;
			border-radius: 0 0 8px 8px;
		}
		::v-deep .es-data-table {
			border-radius: 8px;
		}
		::v-deep .es-toolbar {
			border-radius: 8px 8px 0 0;
			border-bottom: 1px solid rgba(241, 244, 247, 1) !important;
		}
	}
	/**图表*/
	&-chart {
		background: #fff;
		margin-top: 12px;
		padding: 12px 20px;
		border-radius: 8px;
		&-title {
			justify-content: flex-start !important;
			padding-bottom: 12px;
			border-bottom: 1px solid rgba(241, 244, 247, 1);
			margin-bottom: 12px;
		}
		&-con {
			height: 300px;
			width: 100%;
		}
	}
}
.title {
	display: flex;
	justify-content: center;
	align-items: center;
	font-size: 16px;
	font-weight: 550;
	&-img {
		width: 22px;
		height: 22px;
		margin-right: 8px;
	}
}
</style>
