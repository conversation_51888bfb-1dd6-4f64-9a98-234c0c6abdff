<template>
	<div
		class="es-login-box"
		:style="{ background: `url(${logoBj.manageLoginBg || loginBg}) center no-repeat` }"
	>
		<es-login
			:is-encrypt="false"
			:login-name="logoBj.manageLoginLogo || loginLogo"
			forget="https://ssp.sichuanair.com/self-service/common/resetPassword"
			:on-success="handleGetRoleMap"
		></es-login>
	</div>
</template>
<script>
// import { createNamespacedHelpers } from 'vuex';
// const { mapState, mapActions } = createNamespacedHelpers('getRoleMap');
import { mapState, mapActions } from 'vuex';

import { clearAllcookie } from '@/utils/index.js';

export default {
	data() {
		return {
			loginLogo: require('@/assets/image/login-logo.png'),
			loginBg: require('@/assets/image/login-bg.png')
		};
	},
	computed: {
		...mapState(['projectScienceManager']),
		...mapState('getRoleMap', ['projectScienceManager']),
		...mapState('user', ['logoBj'])
	},
	created() {
		clearAllcookie();
	},
	mounted() {
		localStorage.clear();
	},

	methods: {
		// ...mapActions(['handleGetRoleMap'])
		...mapActions('getRoleMap', ['handleGetRoleMap'])
	}
};
</script>
