<template>
	<div class="main">
		<capital-info class="info-box" @getObjectType="getObjectType"></capital-info>
		<user-detail class="info-box" v-if="[0, 2].includes(objectType)"></user-detail>
		<user-detail-crosswise class="info-box" v-if="objectType == '1'"></user-detail-crosswise>
	</div>
</template>

<script>
import UserDetail from './use-detail.vue';
import CapitalInfo from './capital-info.vue';
import UserDetailCrosswise from './use-detail-crosswise.vue';

export default {
	components: { UserDetail, CapitalInfo, UserDetailCrosswise },
	data() {
		return {
			//存储当前项目类型： 0-纵向项目 1-横向项目 2-院级项目
			objectType: '0'
		};
	},
	created() {},
	computed: {
		// 在 `some/nested/module` 中查找
	},
	methods: {
		getObjectType(objectType) {
			this.objectType = objectType;
		}
	}
};
</script>

<style lang="scss" scoped>
@import '@ast/style/public.scss';
.main {
	@include flexBox();
	flex-direction: column;
	height: 100%;
	padding: 10px;
	.info-box {
		width: 100%;
		flex: 0.5;
	}
}
</style>
