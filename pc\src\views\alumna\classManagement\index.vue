<template>
	<div class="content">
		<es-data-table
			ref="table"
			:row-style="tableRowClassName"
			:full="true"
			:fit="true"
			:thead="thead"
			:toolbar="toolbar"
			:data="dataTable"
			:border="true"
			:page="true"
			:numbers="true"
			:param="params"
			:option-data="optionData"
			close
			form
			@btnClick="btnClick"
			@sort-change="sortChange"
			@submit="hadeSubmit"
			@edit="changeTable"
			@search="search"
		></es-data-table>
		<es-dialog
			:title="formTitle"
			:visible.sync="showForm"
			:close-on-click-modal="false"
			:destroy-on-close="true"
			@close="showForm = false"
		>
			<el-tabs v-if="showForm" v-model="activeKey" @tab-click="handleTabs">
				<el-tab-pane label="活动" name="0">
					<es-data-table
						ref="table2"
						:row-style="tableRowClassName"
						:full="true"
						:fit="true"
						:thead="thead2"
						:toolbar="toolbar2"
						:data="dataTable2"
						:border="true"
						:page="true"
						:numbers="true"
						:param="params"
						:option-data="optionData"
						close
						form
						@btnClick="btnClick"
						@sort-change="sortChange"
						@submit="hadeSubmit"
						@edit="changeTable"
						@search="search2"
					></es-data-table>
				</el-tab-pane>
				<el-tab-pane label="校友" name="1">
					<es-data-table
						ref="table3"
						:row-style="tableRowClassName"
						:full="true"
						:fit="true"
						:thead="thead2"
						:toolbar="toolbar2"
						:data="dataTable2"
						:border="true"
						:page="true"
						:numbers="true"
						:param="params"
						:option-data="optionData"
						close
						form
						@btnClick="btnClick"
						@sort-change="sortChange"
						@submit="hadeSubmit"
						@edit="changeTable"
						@search="search2"
					></es-data-table>
				</el-tab-pane>
			</el-tabs>
		</es-dialog>
		<es-dialog
			title="删除"
			:visible.sync="showDelete"
			width="20%"
			:close-on-click-modal="false"
			:middle="true"
			height="140px"
		>
			<div>确定要删除该条数据吗</div>
			<div class="btn-box">
				<div class="btn theme" @click="deleteRow">确定</div>
				<div class="btn" @click="showDelete = false">取消</div>
			</div>
		</es-dialog>
	</div>
</template>

<script>
import interfaceUrl from '@/http/alumna/communityLabel.js';
import SnowflakeId from 'snowflake-id';
export default {
	data() {
		return {
			orginTable: [
				{
					classNumber: 'w50182180',
					className: '汽修21801',
					year: '2018',
					headTeacherId: '*********',
					headTeacherName: '邵霞',
					classSize: '40',
					membershipCount: '4'
				},
				{
					classNumber: 'w50222180',
					className: '旅游21801',
					year: '2018',
					headTeacherId: '*********',
					headTeacherName: '易强',
					classSize: '40',
					membershipCount: '0'
				},
				{
					classNumber: '560301061',
					className: '机电（四驱）',
					year: '2020',
					headTeacherId: '*********',
					headTeacherName: '张娅',
					classSize: '40',
					membershipCount: '0'
				},
				{
					classNumber: '610202120',
					className: '网络12001',
					year: '2020',
					headTeacherId: 'J03010419',
					headTeacherName: '严瑶',
					classSize: '35',
					membershipCount: '0'
				},
				{
					classNumber: '610202120',
					className: '网络12002',
					year: '2020',
					headTeacherId: 'J10010001',
					headTeacherName: '温燕萌',
					classSize: '40',
					membershipCount: '0'
				},
				{
					classNumber: '610202120',
					className: '网络12003',
					year: '2020',
					headTeacherId: 'J03010202',
					headTeacherName: '郭莉梅',
					classSize: '40',
					membershipCount: '0'
				},
				{
					classNumber: '610202120',
					className: '网络12004',
					year: '2020',
					headTeacherId: 'J03010209',
					headTeacherName: '丁录永',
					classSize: '40',
					membershipCount: '0'
				},
				{
					classNumber: '610202120',
					className: '网络12005',
					year: '2020',
					headTeacherId: 'J08030105',
					headTeacherName: '舒永忠',
					classSize: '35',
					membershipCount: '0'
				},
				{
					classNumber: '610202120',
					className: '网络12006',
					year: '2020',
					headTeacherId: 'J08020160',
					headTeacherName: '罗美琴',
					classSize: '40',
					membershipCount: '0'
				},
				{
					classNumber: '610202120',
					className: '网络12007',
					year: '2020',
					headTeacherId: 'J10010613',
					headTeacherName: '周心浩',
					classSize: '40',
					membershipCount: '0'
				},
				{
					classNumber: '610202120',
					className: '网络12008',
					year: '2020',
					headTeacherId: 'J05010510',
					headTeacherName: '颜玉玲',
					classSize: '35',
					membershipCount: '0'
				},
				{
					classNumber: '610101120',
					className: '电子12001',
					year: '2020',
					headTeacherId: 'J06010301',
					headTeacherName: '刘福华',
					classSize: '40',
					membershipCount: '0'
				},
				{
					classNumber: '610101120',
					className: '电子12002',
					year: '2020',
					headTeacherId: 'J06010319',
					headTeacherName: '赵风',
					classSize: '35',
					membershipCount: '0'
				},
				{
					classNumber: '610101120',
					className: '电子12003',
					year: '2020',
					headTeacherId: 'J04010111',
					headTeacherName: '常茜惠',
					classSize: '40',
					membershipCount: '0'
				},
				{
					classNumber: '610101120',
					className: '电子12004',
					year: '2020',
					headTeacherId: 'J10010020',
					headTeacherName: '李黎',
					classSize: '40',
					membershipCount: '0'
				},
				{
					classNumber: '540101120',
					className: '设计12001',
					year: '2020',
					headTeacherId: 'J06010307',
					headTeacherName: '宫涛',
					classSize: '35',
					membershipCount: '0'
				}
			],
			dataTable: [],

			dataTable2: [],
			ownId: '',
			deleteId: '',
			dataTableUrl: interfaceUrl.listJson,
			showForm: false,
			showRoleForm: false,
			enpList: [],
			roleList: [],
			showDelete: false,
			formData: {},
			roleFormData: {
				userId: '',
				username: '',
				phone: '',
				selectedRoleList: []
			},
			formTitle: '编辑',
			optionData: {
				status: [
					{
						value: 1,
						name: '启用'
					},
					{
						value: 0,
						name: '禁用'
					}
				]
			},
			editBtn: {
				type: 'submit',
				skin: 'lay-form-btns',
				contents: [
					{
						type: 'primary',
						text: '确定',
						event: 'confirm'
					},
					{
						type: 'reset',
						text: '取消',
						event: 'cancel'
					}
				]
			},
			cancelBtn: {
				type: 'submit',
				skin: 'lay-form-btns',
				contents: [
					{
						type: 'reset',
						text: '取消',
						event: 'cancel'
					}
				]
			},
			toolbar: [
				// {
				// 	type: 'button',
				// 	contents: [
				// 		{
				// 			text: '新增',
				// 			code: 'add',
				// 			type: 'primary'
				// 		}
				// 	]
				// },
				{
					type: 'search',
					contents: [
						{
							type: 'text',
							name: 'keyword',
							placeholder: '关键字查询'
						}
					]
				}
			],
			thead: [
				{
					title: '班号',
					align: 'center',
					field: 'classNumber',
					sortable: 'custom',
					showOverflowTooltip: true
				},
				{
					title: '班级名称',
					align: 'center',
					field: 'className',
					showOverflowTooltip: true
				},
				{
					title: '年级',
					align: 'center',
					field: 'year',
					showOverflowTooltip: true
				},
				{
					title: '班主任工号',
					align: 'center',
					field: 'headTeacherId',
					showOverflowTooltip: true
				},
				{
					title: '班主任姓名',
					align: 'center',
					field: 'headTeacherName',
					showOverflowTooltip: true
				},
				{
					title: '班级人数',
					align: 'center',
					field: 'classSize',
					showOverflowTooltip: true
				},
				{
					title: '入会人数',
					field: 'membershipCount',
					align: 'center'
				},
				{
					title: '操作',
					type: 'handle',
					width: 100,
					align: 'center',
					template: '',
					events: [
						{
							code: 'view',
							text: '查看'
						}
					]
				}
			],

			toolbar2: [
				{
					type: 'search',
					contents: [
						{
							type: 'text',
							name: 'keyword',
							placeholder: '关键字查询'
						}
					]
				}
			],
			activeKey: '0',

			params: {
				asc: 'false',
				orderBy: 'createTime'
			}
		};
	},
	computed: {
		orginTable2() {
			let arr = [];
			if (this.activeKey === '0') {
				arr = [
					{
						activityName: '人文与旅游学院开展秋季文化交流会',
						plannedRecruitment: '300',
						successfullyBooked: '125',
						registrationRequirements: '对中外文化有一定了解；需携带学生证。',
						speaker: '李文博教授',
						createDateTime: '2024-09-15 10:00:00',
						publisher: '张丽'
					}
				];
			} else {
				arr = [
					{
						name: '王继琼',
						mobilePhoneNumber: '14780706587',
						gender: '女',
						homeAddress: '四川省宜宾市长宁县开佛镇',
						joinTime: '月初随便'
					},
					{
						name: '凌成芳',
						mobilePhoneNumber: '19960061234',
						gender: '女',
						homeAddress: '四川省泸州市纳溪区棉花坡天泰仁和二期6号楼703',
						joinTime: '月初随便'
					},
					{
						name: '杨昇富',
						mobilePhoneNumber: '13458894567',
						gender: '男',
						homeAddress: '四川省内江市资中县铁佛镇高荣还房3栋2单元303',
						joinTime: '月初随便'
					}
				];
			}

			return arr;
		},
		thead2() {
			let arr = [];
			if (this.activeKey === '0') {
				arr = [
					{
						title: '活动名称',
						align: 'center',
						field: 'activityName',
						sortable: 'custom',
						showOverflowTooltip: true
					},
					{
						title: '计划招募',
						align: 'center',
						field: 'plannedRecruitment',
						showOverflowTooltip: true
					},
					{
						title: '预约成功人数',
						align: 'center',
						field: 'successfullyBooked',
						showOverflowTooltip: true
					},
					{
						title: '报名要求',
						align: 'center',
						field: 'registrationRequirements',
						showOverflowTooltip: true
					},
					{
						title: '活动地址',
						align: 'center',
						field: 'registrationRequirements',
						showOverflowTooltip: true
					},
					{
						title: '主讲人',
						align: 'center',
						field: 'speaker',
						showOverflowTooltip: true
					},
					{
						title: '创建时间',
						field: 'createDateTime',
						align: 'center'
					},
					{
						title: '发布人',
						field: 'publisher',
						align: 'center'
					}
				];
			} else {
				arr = [
					{
						title: '姓名',
						align: 'center',
						field: 'name',
						showOverflowTooltip: true
					},
					{
						title: '手机号',
						align: 'center',
						field: 'mobilePhoneNumber',
						showOverflowTooltip: true
					},
					{
						title: '性别',
						align: 'center',
						field: 'gender',
						showOverflowTooltip: true
					},
					{
						title: '家庭住址',
						align: 'center',
						field: 'homeAddress',
						showOverflowTooltip: true
					},
					{
						title: '入会时间',
						align: 'center',
						field: 'joinTime',
						showOverflowTooltip: true
					}
				];
			}

			return arr;
		}
	},
	watch: {},
	created() {
		this.dataTable = this.orginTable;
	},
	mounted() {},
	methods: {
		handleTabs(e) {
			this.dataTable2 = this.orginTable2;
			// this.activeKey = e;
		},
		search(e) {
			const keyword = e.keyword;
			//模糊匹配orginTable所有字段并赋值给dataTable
			this.dataTable = this.orginTable.filter(item => {
				return Object.keys(item).some(key => {
					return String(item[key]).toLowerCase().indexOf(keyword) > -1;
				});
			});
		},
		search2(e) {
			const keyword = e.keyword;
			//模糊匹配orginTable所有字段并赋值给dataTable
			this.dataTable2 = this.orginTable2.filter(item => {
				return Object.keys(item).some(key => {
					return String(item[key]).toLowerCase().indexOf(keyword) > -1;
				});
			});
		},
		/**
		 * 表格行高
		 */
		tableRowClassName({ row, rowIndex }) {
			let styleRes = {
				height: '54px !important'
			};
			return styleRes;
		},
		inputChange(key, value) {},
		hadeSubmit(data) {},
		/**
		 * 操作按钮点击事件
		 * @param {*} res
		 */
		btnClick(res) {
			let code = res.handle.code;
			switch (code) {
				case 'add':
					// 新增
					this.formTitle = '新增';
					const snowflake = new SnowflakeId();
					let id = snowflake.generate();
					this.ownId = id;
					this.formData = { id: id };
					this.showForm = true;
					break;
				case 'edit':
					// 编辑
					this.formTitle = '编辑';
					this.ownId = res.row.id;
					this.$request({
						url: interfaceUrl.info + '/' + res.row.id,
						method: 'GET'
					}).then(res => {
						if (res.rCode == 0) {
							this.formData = res.results;
							this.showForm = true;
						}
					});
					break;
				case 'view':
					this.formTitle = '查看';
					this.dataTable2 = this.orginTable2;
					this.showForm = true;
					// this.ownId = res.row.id;
					// this.$request({
					// 	url: interfaceUrl.info + '/' + res.row.id,
					// 	method: 'GET'
					// }).then(res => {
					// 	if (res.rCode == 0) {
					// 		this.formData = res.results;
					// 		this.showForm = true;
					// 	}
					// });
					break;
				case 'delete':
					this.deleteId = res.row.id;
					this.showDelete = true;
					break;
				default:
					break;
			}
		},
		handleClose() {},
		handleFormItemChange() {},
		handleFormSubmit(data) {
			let formData = data;
			delete formData.extMap;
			let url = '';
			if (this.formTitle == '新增') {
				url = interfaceUrl.save;
			} else {
				url = interfaceUrl.update;
			}
			this.$request({
				url: url,
				data: formData,
				method: 'POST'
			}).then(res => {
				if (res.rCode == 0) {
					this.$message.success('操作成功');
					this.showForm = false;
					this.formData = {};
					this.$refs.table.reload();
				} else {
					this.$message.error(res.msg);
				}
			});
		},
		deleteRow() {
			this.$request({
				url: interfaceUrl.deleteBatchIds,
				data: { ids: this.deleteId },
				method: 'POST'
			}).then(res => {
				if (res.rCode == 0) {
					this.$refs.table.reload();
					this.$message.success('删除成功');
				} else {
					this.$message.error(res.msg);
				}
				this.showDelete = false;
			});
		},
		/**
		 * 排序变化事件
		 * @param {*} column
		 * @param {*} prop
		 * @param {*} order
		 */
		sortChange(column, prop, order) {
			if (column.order == 'ascending') {
				//升序
				this.params = {
					asc: 'true',
					orderBy: column.prop
				};
			} else if (column.order == 'descending') {
				//降序
				this.params = {
					asc: 'false',
					orderBy: column.prop
				};
			} else {
				//不排序
				this.params = {
					asc: 'false',
					orderBy: 'createTime'
				};
			}
			this.$refs.table.reload();
		},
		/**
		 * 编辑模式
		 */
		editModule(list, hideField) {
			for (var i in list) {
				var item = list[i];
				item.readonly = false;
				if (undefined != hideField && hideField.length > 0 && hideField.indexOf(item.name) > -1) {
					item.hide = true;
				}
			}
			list.push(this.editBtn);
		},

		changeTable(val) {
			if ('status' === val.name) {
				this.$request({
					url: interfaceUrl.update,
					data: {
						id: val.data.id,
						labelName: val.data.labelName,
						status: val.value
					},
					method: 'POST'
				}).then(res => {
					if (res.rCode == 0) {
						this.$message.success('操作成功');
					} else {
						this.$message.error(res.msg);
					}
				});
			}
		}
	}
};
</script>
<style scoped lang="scss">
.content {
	width: 100%;
	height: 100%;
}

.el-dialog__body {
	.btn-box {
		margin-top: 20px;
		display: flex;
		justify-content: flex-end;

		.btn {
			padding: 5px 10px;
			color: #666;
			border: 1px solid #eee;
			cursor: pointer;
			margin-right: 5px;
			border-radius: 4px;
			// &.theme {
			// 	background: $--color-primary;
			// 	color: #fff;
			// 	border-color: $--color-primary;
			// }
		}
	}
}
</style>
