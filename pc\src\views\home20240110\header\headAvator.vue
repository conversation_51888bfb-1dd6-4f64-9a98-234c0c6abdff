<template>
	<div>
		<div class="header-avator">
			<input
				ref="inputFile1"
				type="file"
				style="display: none"
				:accept="accept"
				@change="inputChange"
			/>
			<div v-loading="loading" class="img">
				<img
					v-if="!srcList.length"
					src="@/assets/images/shop-images/default-avatar.png"
					class="img"
					alt=""
					srcset=""
				/>
				<el-image
					v-if="srcList.length"
					class="img"
					:src="srcList[0]"
					:preview-src-list="[srcList[0]]"
					lazy
				></el-image>
			</div>
		</div>
	</div>
</template>

<script>
import requestFun from '@/utils/request';
import { alumniUrl } from '@/config';

export default {
	props: {
		accept: {
			type: String,
			default: 'image/jpeg,image/png,image/jpg'
		},
		code: {
			type: String,
			default: 'platform_user_head_sculpture'
		},
		ownId: {
			type: String,
			default: ''
		},
		userId: {
			type: String,
			default: ''
		}
	},
	data() {
		return {
			loading: false,
			srcList: [],
			selectedFile: {}, // 需要删除的文件
			files: []
		};
	},
	mounted() {
		this.getFiles();
		// this.getFiles();
		// previewFile({ id: 'f87cc977-3422-4967-af44-35b5d8e9f74a' }).then(res => {
		// 	console.log('res', res);
		// });
	},
	methods: {
		chooseImage() {
			this.$refs.inputFile1.click();
		},
		getFiles() {
			this.loading = true;
			this.$.ajax({
				url: '/ybzy/mecpfileManagement/front/getAdjunctFileInfos',
				params: {
					code: this.code,
					ownId: this.ownId,
					userId: this.userId,
					generateRequestId: Date.now().toString()
				}
			})
				.then(async res => {
					this.loading = false;
					if (res.rCode === 0) {
						const imgReqAll = [];
						res.results.reverse();
						res.results.forEach((item, index) => {
							this.srcList.push('');
							if (
								['bmp', 'jpg', 'jpeg', 'png', 'gif', 'svg', 'webp', 'apng'].indexOf(item.suffix) >
								-1
							) {
								// 获取到blob数据
								const req = requestFun({
									url: `/ybzy/mecpfileManagement/front/previewAdjunct.json?adjunctId=${item.adjunctId}`,
									headers: {
										responseType: 'blob'
									},
									responseType: 'blob',
									method: 'get'
								});
								imgReqAll.push(req);
							} else {
								imgReqAll.push(null);
							}
						});
						const resAll = await Promise.all(imgReqAll);
						// console.log(resAll);
						resAll.forEach((item, index) => {
							if (item) {
								this.changeImgToBase64(item.data, index);
							}
						});
						this.files = res.results;
						this.$emit('fileChange', this.files);
					}
				})
				.catch(err => {
					this.loading = false;
					console.log(err, 'err');
				});
		},
		changeImgToBase64(item, index) {
			const that = this;
			let reader = new FileReader();
			reader.onload = function (e) {
				const oItem = that.files[index];
				oItem.imgUrl = e.target.result;
				that.files.splice(index, 1, oItem);
				that.srcList.splice(index, 1, e.target.result);
			};
			reader.readAsDataURL(item);
		},
		// 上传文件
		inputChange(e) {
			if (!e.target.files.length) {
				return;
			}
			if (this.files.length) {
				// 重选文件先删除该文件
				this.$api.personal_api
					.deleteFile({ id: this.files[0].adjunctId, userName: this._userinfo.username })
					.then(res => {
						if (res.rCode === 0) {
							this.selectedFil = {};
						}
					})
					.catch(err => {
						console.log(err);
					});
			}
			// // 利用fileReader对象获取file
			let file = e.target.files[0];
			// this.fileUrl = file;
			let filesize = file.size;
			if (filesize > 5 * 1024 * 1024) {
				this.$message.warning('图片过大，请重新选择！');
				return;
			}
			const formData = new FormData();
			formData.append('file', file);
			formData.append('code', this.code);
			formData.append('ownId', this.ownId);
			formData.append('userId', this._userinfo.id);
			this.$api.personal_api.uploadFile(formData).then(res => {
				if (res.rCode === 0) {
					this.getFiles();
					this.$refs.inputFile1.value = '';
					// 将头像信息传递给父组件，父组件进行个人信息更新
					if (res.results && res.results[0]) {
						this.$emit('uploadPhotoUrl', res.results[0]['adjunctId']);
					}
				}
			});
		}
	}
};
</script>

<style lang="scss" scoped>
.header-avator {
	width: 100%;
	height: 100%;
	background: #f0f0f0;
	border-radius: 50%;
	.img-item {
		width: 100%;
		height: 100%;
	}
	.img {
		width: 100%;
		height: 100%;
		border-radius: 50%;

		img,
		image {
			width: 100%;
			height: 100%;
		}
	}
}
</style>
