<template>
	<div style="height: 100%">
		<div style="width: 100%; height: 100%">
			<es-data-table
				v-if="true"
				ref="table"
				:key="tableCount"
				:row-style="tableRowClassName"
				:full="true"
				:fit="true"
				:thead="thead"
				:toolbar="toolbar"
				:page="page"
				:url="dataTableUrl"
				:param="dataTableParam"
				:border="true"
				:numbers="true"
				form
				@btnClick="btnClick"
			></es-data-table>
			<!-- 新增or编辑 -->
			<es-dialog
				v-if="showAddOrEditPage"
				:title="formTitle"
				:visible.sync="showAddOrEditPage"
				width="800px"
				height="600px"
				:show-scale="false"
				:drag="false"
				append-to-body
			>
				<es-form
					ref="form"
					:model="formData"
					:contents="formItemList"
					:genre="2"
					height="490px"
					collapse
					@submit="handleFormSubmit"
					@reset="closeDialog"
				/>
			</es-dialog>
			<!-- 查看 -->
			<es-dialog
				v-if="showViewPage"
				title="详情"
				:visible.sync="showViewPage"
				width="960px"
				height="710px"
				:show-scale="false"
				:drag="false"
				append-to-body
			>
				<es-form
					ref="form"
					:model="formData"
					:contents="infoItemList"
					:genre="2"
					height="600px"
					collapse
					:submit="false"
					:reset="true"
					@reset="closeDialog"
				/>
			</es-dialog>
		</div>
	</div>
</template>

<script>
import api from '@/http/logistics/supplierFile.js';
import SnowflakeId from 'snowflake-id';

export default {
	components: {},
	data() {
		return {
			page: {
				pageSize: 20,
				totalCount: 0
			},
			//供应商下拉列表数据
			supplierList: [],
			//供应商是否可编辑
			isSupplierEdit: false,

			//弹出框配置
			formTitle: '新增',
			addOrUpdateMode: 'add',
			formData: {},
			showAddOrEditPage: false,
			showViewPage: false,
			//表格数据
			tableCount: 1,
			dataTableUrl: api.listJson,
			dataTableParam: { orderBy: 'create_time', asc: false },
			thead: [],
			toolbar: [],
			editToolbar: [
				{
					type: 'button',
					contents: [
						{
							text: '新增',
							code: 'add',
							type: 'primary'
						}
					]
				},
				{
					type: 'search',
					reset: true,
					contents: [
						{
							type: 'text',
							label: '关键字',
							name: 'keyword',
							placeholder: '请输入关键字'
						}
					]
				}
			],
			listThead: [
				{
					title: '材料名称',
					align: 'left',
					field: 'fileName',
					showOverflowTooltip: true
				},
				{
					title: '供应商名称',
					align: 'center',
					field: 'supplierName',
					showOverflowTooltip: true
				},
				{
					title: '供应商编号',
					align: 'center',
					field: 'supplierCode',
					showOverflowTooltip: true
				},
				{
					title: '供应商联系方式',
					align: 'center',
					field: 'supplierPhone',
					showOverflowTooltip: true
				},
				{
					title: '上传人',
					align: 'center',
					field: 'createUserName',
					showOverflowTooltip: true
				},
				{
					title: '上传时间',
					align: 'center',
					field: 'createTime',
					showOverflowTooltip: true
				}
			],
			btnJson: {
				title: '操作',
				type: 'handle',
				width: 150,
				template: '',
				events: [
					{
						code: 'view',
						text: '查看'
					},
					{
						code: 'edit',
						text: '编辑'
					},
					{
						code: 'delete',
						text: '删除'
					}
				]
			}
		};
	},
	computed: {
		formItemList() {
			return [
				{
					label: '材料名称',
					name: 'fileName',
					placeholder: '请输入名称',
					rules: {
						required: true,
						message: '请输入名称',
						trigger: 'blur'
					},
					maxlength: 250,
					col: 12
				},
				{
					type: 'select',
					name: 'supplierId',
					label: '供应商',
					placeholder: '请选择供应商',
					data: this.supplierList,
					'label-key': 'label',
					'value-key': 'value',
					// url: '/ybzy/hqSupplierFiles/getSuppliers',
					// 'value-key': 'value',
					// 'label-key': 'label',
					readonly: this.isSupplierEdit,
					rules: {
						required: true,
						message: '请选择供应商',
						trigger: 'change'
					},
					col: 12
				},
				{
					label: '附件',
					type: 'attachment',
					code: 'hq_supplier_files',
					ownId: this.formData.id, // 业务id
					dragSort: true,
					limit: 8,
					col: 12
				}
			];
		},
		infoItemList() {
			return [
				{
					label: '材料名称',
					name: 'fileName',
					readonly: true,
					col: 12
				},
				{
					name: 'supplierName',
					label: '供应商',
					readonly: true,
					col: 6
				},
				{
					name: 'supplierCode',
					label: '供应商编号',
					readonly: true,
					col: 6
				},
				{
					name: 'supplierPerson',
					label: '供应商联系人',
					readonly: true,
					col: 6
				},
				{
					name: 'supplierPhone',
					label: '供应商联系方式',
					readonly: true,
					col: 6
				},
				{
					name: 'createUserName',
					label: '上传人',
					readonly: true,
					col: 6
				},
				{
					name: 'createTime',
					label: '上传时间',
					readonly: true,
					col: 6
				},
				{
					label: '附件',
					type: 'attachment',
					code: 'hq_supplier_files',
					readonly: true,
					// 'select-type': 'icon-plus',
					// preview: true,
					// listType: 'picture-card',
					ownId: this.formData.id, // 业务id
					dragSort: true,
					col: 12
				}
			];
		}
	},
	watch: {
		dataTableUrl() {
			this.tableCount++;
		}
	},
	created() {
		//初始化查询待审核列表
		this.thead = this.getListThead(this.btnJson);
		this.toolbar = this.editToolbar;
		this.getSupplierList();
	},
	methods: {
		/**
		 * 表格行高
		 */
		tableRowClassName() {
			return {
				height: '54px !important'
			};
		},
		//获取供应商下拉列表
		getSupplierList() {
			this.$request({
				url: api.getSuppliers,
				method: 'GET'
			}).then(res => {
				if (res.rCode === 0) {
					this.supplierList = res.results;
				}
			});
		},
		// 操作按钮
		btnClick(res) {
			let code = res.handle.code;
			switch (code) {
				case 'add':
					this.isSupplierEdit = false;
					this.openAddOrEditPage(null, code);
					break;
				case 'edit':
					this.isSupplierEdit = true;
					this.openAddOrEditPage(res.row.id, code);
					break;
				case 'view':
					this.openViewPage(res.row.id);
					break;
				case 'delete':
					this.deleteRowById(res.row.id);
					break;
			}
		},
		//打开新增或编辑
		openAddOrEditPage(id, code) {
			if (code !== 'add') {
				this.formTitle = '编辑';
				this.$request({ url: api.info + '/' + id, method: 'GET' }).then(res => {
					if (res.rCode === 0) {
						this.formData = res.results;
					} else {
						this.$message.error(res.msg);
					}
				});
			} else {
				this.formTitle = '新增';
				const snowflake = new SnowflakeId();
				this.formData = { id: snowflake.generate() };
			}
			this.addOrUpdateMode = code;
			this.showAddOrEditPage = true;
			this.showViewPage = false;
		},
		//打开查看
		openViewPage(id) {
			this.$request({ url: api.info + '/' + id, method: 'GET' }).then(res => {
				if (res.rCode === 0) {
					this.formData = res.results;
				} else {
					this.$message.error(res.msg);
				}
			});
			this.showAddOrEditPage = false;
			this.showViewPage = true;
		},
		handleFormSubmit() {
			//校验
			this.$refs.form.validate(valid => {
				if (valid) {
					let apiUrl = this.addOrUpdateMode === 'edit' ? api.update : api.save;
					this.$request({
						url: apiUrl,
						data: { ...this.formData },
						method: 'POST'
					}).then(res => {
						if (res.rCode === 0) {
							this.$message.success('操作成功');
							this.showAddOrEditPage = false;
							this.formData = {};
							this.$refs.table.reload();
						} else {
							this.$message.error(res.msg);
						}
					});
				}
			});
		},
		deleteRowById(id) {
			this.$confirm('确定要删除选中的数据吗？', '删除', {
				confirmButtonText: '确定',
				cancelButtonText: '取消',
				type: 'warning'
			})
				.then(() => {
					this.$request({
						url: api.deleteById,
						data: { id: id },
						method: 'POST'
					}).then(res => {
						if (res.rCode === 0) {
							this.$message.success('操作完成');
							this.$refs.table.reload();
						} else {
							this.$message.error(res.msg);
						}
					});
				})
				.catch(() => {});
		},
		getListThead(btnJson) {
			let tempThead = [...this.listThead];
			tempThead.push(btnJson);
			return tempThead;
		},
		closeDialog(){
			this.showAddOrEditPage = false;
			this.showViewPage = false;
			this.formData = {};
		}
	}
};
</script>
