<template>
	<div :id="id" style="width: 100%; height: 100%"></div>
</template>
<script>
export default {
	props: {
		id: {
			type: String,
			required: true
		},
		// title: {
		// 	type: String,
		// 	required: true
		// },
		// color: {
		// 	type: Array,
		// 	required: true
		// },
		seriesData: {
			type: Array,
			required: true
		},
		legendData: {
			type: Array,
			required: true,
			defalut: () => {
				return [];
			}
		},
		type: {
			type: String,
			defalut: ''
		}
	},
	data() {
		return {
			chart: null,
			show:false
		};
	},
	watch: {
		seriesData() {
			console.log(this.seriesData);
			if(this.seriesData[0]==0 &&this.seriesData[1]==0){
				this.show=true
			}else{
				this.show=false
			}
			this.draw();
		}
	},
	mounted() {
		this.draw();
	},

	methods: {
		draw() {
			let option = {};
			if (!this.type) {
				option = {
					grid: {
						left: '2%',
						right: '2%',
						bottom: '2%',
						containLabel: true
					},
					legend: { top: '20', right: 20, itemWidth: 16 },
					tooltip: {
						trigger: 'axis'
					},
					xAxis: {
						type: 'category',
						data: this.legendData,
						axisLine: {
							show: false //隐藏X轴轴线
						},
						axisTick: {
							show: false //隐藏X轴刻度
						}
					},
					yAxis: {
						name: '单位：元',
						min: 0,
						max: 10000,
						interval: 2000,
						nameTextStyle: {
							padding: [0, 65, 10, 0]
						}
					},
					// Declare several bar series, each will be mapped
					// to a column of dataset.source by default.
					series: [
						{
							type: 'bar',
							name: '收入',
							barGap: 0,
							data: this.seriesData[0] || [],
							barWidth: 20,
							itemStyle: {
								color: new this.$echarts.graphic.LinearGradient(0, 0, 0, 1, [
									{
										offset: 0,
										color: '#0D7DE9'
									},
									{
										offset: 1,
										color: '#E5F1FD'
									}
								]),
								borderRadius: [50, 50, 0, 0]
							}
						},
						{
							type: 'bar',
							barWidth: 20,
							name: '支出',
							data: this.seriesData[1] || [],
							itemStyle: {
								color: new this.$echarts.graphic.LinearGradient(0, 0, 0, 1, [
									{
										offset: 0,
										color: '#ED666F'
									},
									{
										offset: 1,
										color: '#FCE3E3'
									}
								]),
								borderRadius: [50, 50, 0, 0]
							}
						}
					]
				};
			} else {
				let total = 0;
				this.seriesData.forEach(item => {
					item.forEach(item2 => {
						total += item2;
					});
				});
				option = {
					tooltip: {
						trigger: 'item',
						borderColor: 'rgba(255,255,255,.3)',
						borderWidth: 1,
						padding: [5, 8, 5, 8],
						textStyle: {
							color: '#ffffff'
						},
						backgroundColor: 'rgba(128,187,224,0.9)',
						formatter: params => {
							let percent = isNaN(((params.value * 100) / total).toFixed(2))?0:((params.value * 100) / total).toFixed(2);
							let str = params.name + '\n' + params.value + ' (' + percent + '%)';
							return str;
						}
					},
					legend: {
						data: this.legendData,
						icon: 'circle',
						itemWidth: 12,
						itemGap: 20,
						bottom: 16,
						left: 'center'
					},
					polar: {
						radius: ['30%', '75%']
					},
					angleAxis: { show: this.show, startAngle: 270 },
					radiusAxis: {
						show: this.show,
						type: 'category',
						data: this.legendData
					},
					series: [
						{
							type: 'bar',
							name: this.legendData[0],
							data: this.seriesData[0],
							barGap: 0,
							itemStyle: {
								color: '#5FDA8A'
							},
							label: {
								distance: 20,
								show: true,
								position: [60, 180],
								formatter: '{b}: {c}\n\n'
							},
							labelLine: {
								show: true,
								length: 10,
								length2: 80,
								lineStyle: {
									width: 1
								}
							},
							coordinateSystem: 'polar'
						},
						{
							type: 'bar',
							name: this.legendData[1],
							data: this.seriesData[1],
							barGap: 0,
							itemStyle: {
								color: '#0377E8'
							},
							label: {
								distance: 20,
								show: true,
								position: [-60, -40],
								formatter: '{b}: {c}\n\n'
							},
							labelLine: {
								show: true,
								length: 10,
								length2: 80,
								lineStyle: {
									width: 1
								}
							},
							coordinateSystem: 'polar'
						}
					]
				};
			}

			this.chart && this.chart.dispose();
			this.chart = this.$echarts.init(document.getElementById(this.id));
			// console.log('>>>this.chart', this.$echarts);
			this.chart.setOption(option);
			window.addEventListener('resize', () => {
				this.chart && this.chart.resize();
			});
		}
	}
};
</script>
