<template>
	<div class="content">
		<es-data-table
			ref="table"
			:key="tableCount"
			:row-style="tableRowClassName"
			:full="true"
			:fit="true"
			:thead="thead"
			:toolbar="toolbar"
			:border="true"
			:page="pageOption"
			:option-data="optionData"
			:url="dataTableUrl"
			:numbers="true"
			:param="params"
			form
			@edit="changeTable"
			@btnClick="btnClick"
		></es-data-table>
		<es-dialog
			:title="formTitle"
			:visible.sync="showForm"
			width="650px"
			height="50%"
			:drag="false"
			:close-on-click-modal="false"
			:destroy-on-close="true"
		>
			<es-form
				v-if="showForm"
				ref="form"
				v-loading="loading"
				:model="formData"
				:contents="formItemList"
				:genre="2"
				collapse
				@change="inputChange"
				@submit="handleFormSubmit"
				@reset="showForm = false"
			/>
		</es-dialog>
		<es-dialog
			title="删除"
			:visible.sync="showDelete"
			width="20%"
			:close-on-click-modal="false"
			:middle="true"
			height="140px"
		>
			<div>确定要删除该条数据吗</div>
			<div class="btn-box">
				<div class="btn theme" @click="deleteRow">确定</div>
				<div class="btn" @click="showDelete = false">取消</div>
			</div>
		</es-dialog>
	</div>
</template>

<script>
import interfaceUrl from '@/http/health/psyAppointTime.js';
import TimeSelect from '@/components/time-select.vue';
import TimeSelectMore from '@/components/time-select-more.vue';
export default {
	props: ['psyId'],
	data() {
		return {
			loading: false,
			dataTableUrl: interfaceUrl.listJson,
			tableCount: 1,
			showForm: false,
			showDelete: false,
			ownId: null, //数据行Id
			validityOfDateDisable: false,
			personList: [],
			params: {
				psyId: this.psyId
			},
			editBtn: {
				type: 'submit',
				skin: 'lay-form-btns',
				contents: [
					{ type: 'primary', text: '确定', event: 'confirm' },
					{ type: 'reset', text: '取消', event: 'cancel' }
				]
			},
			cancelBtn: {
				type: 'submit',
				skin: 'lay-form-btns',
				contents: [{ type: 'reset', text: '取消', event: 'cancel' }]
			},
			toolbar: [
				{
					type: 'button',
					contents: [
						{
							text: '新增',
							code: 'add'
						}
					]
				},
				{
					type: 'search',
					reset: true,
					contents: [
						{
							type: 'date',
							name: 'startDate',
							label: '开始时间',
							placeholder: '开始时间',
							clearable: true,
							col: 6
						},
						{
							type: 'date',
							name: 'endDate',
							label: '结束时间',
							placeholder: '结束时间',
							clearable: true,
							col: 6
						}
					]
				}
			],
			thead: [],
			listThead: [
				{
					title: '咨询日期',
					align: 'center',
					field: 'treatmentDate'
				},
				{
					title: '时间段',
					align: 'center',
					 showOverflowTooltip: true,
					field: 'time'
				},
				{
					title: '咨询地点',
					align: 'center',
					field: 'address'
				},
				{
					title: '可预约名额',
					align: 'center',
					width: 100,
					field: 'num'
				},
				{
					title: '状态',
					width: 120,
					align: 'center',
					field: 'status',
					type: 'switch'
				}
			],
			viewListBtn: {
				title: '操作',
				type: 'handle',
				width: 180,
				template: '',
				events: [
					{ code: 'view', text: '查看' },
					{ code: 'edit', text: '编辑' },
					{ code: 'delete', text: '删除' }
				]
			},
			optionData: {
				status: [
					{
						value: 1,
						name: '启用'
					},
					{
						value: 0,
						name: '停用'
					}
				]
			},
			pageOption: {
				layout: 'total, prev, pager, next, jumper',
				pageSize: 10,
				// hideOnSinglePage: true,
				position: 'right',
				current: 1,
				pageNum: 1
			},
			formData: {},
			formTitle: '编辑'
		};
	},
	computed: {
		formItemList() {
			const readonly = this.formTitle === '查看';
			return [
				{
					type: 'attachment',
					label: '时间段',
					name: 'time',
					code: 'USER_IMG',
					ownId: '666666',
					showFileList: false,
					disabled: true,
					// hide: this.formTitle !== '新增',
					render: h => {
						const initData = this.formData?.time;
						return h(TimeSelectMore, {
							props: { readonly: readonly, initData: initData },
							on: {
								change: val => {
									this.$set(this.formData, 'time', val);
								}
							}
						});
					},
					rules: {
						required: true,
						message: '请选择时间段',
						validator: (rule, value, callback) => {
							if (this.formData.time) {
								callback();
							}
							callback(new Error('请选择时间段'));
						},
						trigger: 'change'
					},
					col: 12
				},
				// {
				// 	type: 'attachment',
				// 	label: '时间段',
				// 	name: 'time',
				// 	code: 'USER_IMG',
				// 	ownId: '666666',
				// 	showFileList: false,
				// 	disabled: true,
				// 	hide: this.formTitle === '新增',
				// 	render: h => {
				// 		const timeN = this.formData?.time?.split('~');
				// 		const initData = {
				// 			startTime: timeN?.[0] || '',
				// 			endTime: timeN?.[1] || ''
				// 		};
				// 		return h(TimeSelect, {
				// 			props: { readonly: readonly, initData: initData },
				// 			on: {
				// 				change: val => {
				// 					const time = val.startTime + '~' + val.endTime;
				// 					this.$set(this.formData, 'time', time);
				// 					console.log(val, 'time');
				// 				}
				// 			}
				// 		});
				// 	},
				// 	rules: {
				// 		required: true,
				// 		message: '请选择时间段',
				// 		validator: (rule, value, callback) => {
				// 			if (this.formData.time) {
				// 				callback();
				// 			}
				// 			callback(new Error('请选择时间段'));
				// 		},
				// 		trigger: 'change'
				// 	},
				// 	col: 12
				// },
				{
					label: '咨询日期',
					name: 'treatmentDate',
					type: this.formTitle === '新增' ? 'dates' : 'date',
					placeholder: '请输入咨询日期',
					controls: true,
					format: 'yyyy-MM-dd',
					readonly,
					rules: {
						required: true,
						message: '请输入咨询日期',
						trigger: 'blur'
					},
					verify: 'required',
					col: 12
				},
				{
					label: '咨询地点',
					name: 'address',
					readonly,
					placeholder: '请输入咨询地点',
					rules: {
						required: true,
						message: '请输入咨询地点',
						trigger: 'blur'
					},
					maxlength: 100,
					verify: 'required',
					col: 12
				},
				{
					label: '可预约名额',
					name: 'num',
					readonly,
					type: 'number',
					placeholder: '请输入可预约名额',
					rules: {
						required: true,
						message: '请输入可预约名额',
						trigger: 'blur'
					},
					min: 1,
					verify: 'required',
					col: 6
				},
				{
					label: '状态',
					name: 'status',
					readonly,
					type: 'radio',
					data: [
						{ value: 1, name: '启用' },
						{ value: 0, name: '停用' }
					],
					verify: 'required',
					rules: {
						required: true,
						message: '请确定是否启用',
						trigger: 'blur'
					},
					col: 6
				}
			];
		}
	},
	watch: {
		dataTableUrl() {
			this.tableCount++;
		},
		formItemList: {
			handler(newValue, oldValue) {},
			deep: true
		}
	},
	created() {
		this.thead = this.getListThead(this.viewListBtn);
	},
	methods: {
		//表格行高
		tableRowClassName({ row, rowIndex }) {
			let styleRes = { height: '54px !important' };
			return styleRes;
		},
		inputChange(key, value) {},
		//操作按钮点击事件
		btnClick(res) {
			let code = res.handle.code;
			switch (code) {
				case 'add':
					this.formTitle = '新增';
					// this.editModule(this.formItemList);
					this.formData = { psyId: this.psyId };
					this.showForm = true;
					break;
				case 'edit':
					this.formTitle = '编辑';
					// this.editModule(this.formItemList);
					this.$request({ url: interfaceUrl.info + '/' + res.row.id, method: 'GET' }).then(res => {
						if (res.rCode === 0) {
							this.formData = {
								...res.results,
								// treatmentDate: res?.results?.treatmentDate
								// 	? res.results.treatmentDate.split(',')
								// 	: [],
								time: res?.results?.time ? res.results.time.split(',') : []
							};
							this.showForm = true;
						}
					});
					break;
				case 'view':
					this.formTitle = '查看';
					// this.readModule(this.formItemList);
					this.$request({ url: interfaceUrl.info + '/' + res.row.id, method: 'GET' }).then(res => {
						if (res.rCode === 0) {
							this.formData = res.results;
							this.formData.time = res?.results?.time ? res.results.time.split(',') : [];

							this.showForm = true;
						}
					});
					break;
				case 'delete':
					this.deleteId = res.row.id;
					this.showDelete = true;
					break;
				default:
					break;
			}
		},
		//表单提交
		handleFormSubmit(data) {
			this.$refs.form.validate(valid => {
				if (!valid) {
					return;
				}
				this.loading = true;
				let formData = data;
				for (let key in formData) {
					if (key.indexOf('_') >= 0) {
						let indexName = key.replace('_', '.');
						formData[indexName] = formData[key];
					}
				}
				let url = '';
				if (this.formTitle === '新增') {
					url = interfaceUrl.save;
					// formData.startTreatmentDate = formData.treatmentDate[0];
					// formData.endTreatmentDate = formData.treatmentDate[1];
					// delete formData.treatmentDate;
					formData.treatmentDate = formData.treatmentDate.join(',');
					formData.time = formData.time.join(',');
				} else {
					url = interfaceUrl.update;
				}

				this.$request({ url: url, data: formData, method: 'POST' }).then(res => {
					this.loading = false;
					if (res.rCode !== 0) {
						this.$message.error(res.msg);
						return;
					}
					this.$message.success(res.msg ? res.msg : '操作成功');
					this.showForm = false;
					this.formData = {};
					this.$refs.table.reload();
				});
			});
		},
		//删除行
		deleteRow() {
			this.$request({
				url: interfaceUrl.delete,
				data: { id: this.deleteId },
				method: 'POST'
			}).then(res => {
				if (res.rCode === 0) {
					this.$refs.table.reload();
					this.$message.success('删除成功');
				} else {
					this.$message.error(res.msg);
				}
				this.showDelete = false;
			});
		},
		//编辑模式
		editModule(list) {
			for (var i in list) {
				var item = list[i];
				item.readonly = false;
			}
			list.push(this.editBtn);
		},
		//只读模式
		readModule(list) {
			for (var i in list) {
				var item = list[i];
				item.readonly = true;
			}
			list.push(this.cancelBtn);
		},
		getListThead(btnJson) {
			let tempThead = Object.assign([], this.listThead);
			if (tempThead.length > 7) {
				tempThead.pop();
			}
			tempThead.push(btnJson);
			return tempThead;
		},
		refreshData() {
			this.$refs.table.reload();
		},
		changeTable(val) {
			switch (val.name) {
				case 'status':
					this.changeStatus(val.data.id);
					break;
			}
		},
		//改变数据启用状态
		changeStatus(id) {
			this.$request({
				url: interfaceUrl.updateStatus,
				data: { id: id },
				method: 'POST'
			}).then(res => {
				if (res.success) {
					this.$message.success('操作成功！');
				} else {
					this.$message.error(res.msg);
				}
			});
		}
	}
};
</script>
<style scoped lang="scss">
.content {
	width: 100%;
	height: 100%;
}

.el-dialog__body {
	.btn-box {
		margin-top: 20px;
		display: flex;
		justify-content: flex-end;

		.btn {
			padding: 5px 10px;
			color: #666;
			border: 1px solid #eee;
			cursor: pointer;
			margin-right: 5px;
			border-radius: 4px;
			// &.theme {
			// 	background: $--color-primary;
			// 	color: #fff;
			// 	border-color: $--color-primary;
			// }
		}
	}
}
::v-deep .el-upload--handle {
	width: 100%;
	.el-upload {
		width: 100%;
	}
}
</style>
