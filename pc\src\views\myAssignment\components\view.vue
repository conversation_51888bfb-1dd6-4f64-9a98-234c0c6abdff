<template>
	<div>
		<div class="header">
			<div class="top">{{ title }}</div>
			<div class="headerBottom">通知时间：{{ time }}</div>
		</div>
		<div class="content" style="height: 400px" v-html="html"></div>
		<es-form ref="form" :model="formData" :contents="formItemList" :submit="false"></es-form>
		<div class="bottom" v-if="this.type !== 'view'">
			<!-- <button type="button" class="el-button el-button--primary el-button--medium" text="确定">
				确定
			</button> -->
			<button
				type="button"
				class="el-button el-button--reset el-button--medium"
				text="取消"
				@click="cancel()"
			>
				取消
			</button>
		</div>
	</div>
</template>

<script>
export default {
	props: {
		id: {
			type: String,
			default: ''
		}
	},
	components: {},
	data() {
		return {
			html: '',
			title: '',
			time: '',
			formData: {},
			sendAnnex: "",
		};
	},
	computed: {
		formItemList() {
			return [
				{
					name: 'fj',
					readonly: true,
					disabled: true,
					label: '附件上传',
					type: 'attachment',
					code: 'taskProject',
					ownId: this.sendAnnex === null ? "" : this.sendAnnex,
					rules: {
						required: false,
						message: '请上传',
						trigger: 'blur'
					}
				}
			];
		}
	},
	mounted() {
		this.$.ajax({
			url: '/ybzy/projectTask/info',
			params: {
				id: this.id
			}
		})
			.then(res => {
				this.html = res.results.sendContent;
				this.title = res.results.sendTitle;

				this.sendAnnex = res.results.sendAnnex;

				if (res.results.sendTime !== null) {
					this.time = res.results.sendTime;
				}
			})
			.catch(err => {});
	},
	created() {},
	methods: {
		cancel() {
			console.log('取消');
			this.$emit('cancel', '取消');
		}
	}
};
</script>
<style lang="scss" scoped>
.header {
	text-align: center;
	border-bottom: 1px solid #e9e9e9;
	.top {
		width: 100%;
		font-size: 24px;
		line-height: 40px;
		text-align: center;
	}

	.headerBottom {
		width: 100%;
		text-align: center;
		font-size: 14px;
		line-height: 30px;
	}
}
.bottom {
	display: flex;
	justify-content: right;
	align-items: center;
	height: 60px;
	text-align: right;
}
</style>
