<!--
 @desc:审核申请修改
 @author: WH
 @date: 2023/6/30
 -->
<template>
	<es-form
		ref="auditRuleForm"
		:model="formData"
		:contents="formItemList"
		@click="handleClick"
	></es-form>
</template>

<script>
import $ from 'eoss-ui/lib/utils/util';
import { auditApply, getInfoApply } from '@/api/scientific-sys.js';
export default {
	props: {
		formId: {
			type: String,
			default: ''
		}
	},
	data() {
		return {
			formData: {},
			formItemList: [
				{
					title: '申请修改信息',
					contents: [
						{
							name: 'applyUserName',
							placeholder: '',
							label: '申请人',
							readonly: true,
							col: 6
						},
						{
							name: 'applyTime',
							placeholder: '',
							disabled: false,
							col: 6,
							label: '申请时间',
							type: 'date',
							unlinkPanels: true,
							readonly: true
						},
						{
							name: 'applyReason',
							label: '申请理由',
							type: 'textarea',
							rules: {
								// required: true,
								message: '请输入理由',
								trigger: 'blur'
							},
							rows: 4
						}
					]
				},
				{
					title: '审核信息',
					contents: [
						{
							name: 'auditUserName',
							placeholder: '',
							label: '审核人',
							readonly: true,
							col: 6
						},
						{
							name: 'auditTime',
							placeholder: '',
							disabled: false,
							col: 6,
							label: '审核时间',
							type: 'date',
							readonly: true,
							unlinkPanels: true
						},
						{
							name: 'result',
							label: '审核结果',
							type: 'radio',
							col: 6,
							// 状态（0.撤回、1.驳回、2.通过、3.申请修改、4.同意修改、5.驳回修改,6.上传备案表）
							data: [
								{ name: '同意', value: 'agree' },
								{ name: '驳回', value: 'reject' }
							],
							rules: {
								required: true,
								message: '请选择审核结果'
							}
						},
						{
							name: 'reason',
							label: '审核意见',
							type: 'textarea',
							rules: {
								required: true,
								message: '请输入意见',
								trigger: 'blur'
							},
							rows: 4
						}
					]
				},
				{
					type: 'submit',
					skin: 'lay-form-btns',
					contents: [
						{
							type: 'primary',
							btnType: 'submit',
							text: '提交',
							event: (e, formData) => {
								//调用表单验证
								this.$refs.auditRuleForm.validate(valid => {
									if (valid) {
										this.handleClick({ btnType: 'submit' });
									} else {
										return false;
									}
								});
							}
						},
						{
							type: 'primary',
							btnType: 'esc',
							text: '取消'
						}
					]
				}
			]
		};
	},
	mounted() {
		this.initFn();
	},
	methods: {
		async initFn() {
			const loading = $.loading(this.$loading, '加载中......');
			try {
				let { rCode, msg, results } = await this.$.ajax({
					url: getInfoApply,
					method: 'get',
					params: {
						id: this.formId
					}
				});

				if (rCode == 0) {
					let { applyTime, auditUserName, auditTime, applyUserName, applyReason } = results;
					this.formData = {
						applyTime,
						auditUserName,
						auditTime,
						applyUserName,
						applyReason,
					};
				} else {
					this.$message.error(msg);
				}
			} catch (error) {
				console.log('>>>error', error);
			} finally {
				loading.close();
			}
			// let { applyDate, applyUsername, checkDate, checkUsername, applyReason } = this.row;
			// this.formData = {
			// 	checkDate,
			// 	checkUsername,
			// 	applyDate,
			// 	applyUsername,
			// 	applyReason
			// };
		},

		async handleClick(handle) {
			try {
				let { btnType } = handle;
				if (btnType == 'submit') {
					const loading = $.loading(this.$loading, '提交中');
					debugger
					const { result, reason } = this.formData;
					let { rCode, msg, results } = await this.$.ajax({
						url: auditApply,
						method: 'post',
						data: {
							id: this.formId,
							result: result == 'agree' ? true : false, //true审核通过 false驳回修改
							reason:reason
						}
					});
					loading.close();
					if (rCode == 0) {
						this.$message.success(msg);
						this.$emit('confirm');
					} else {
						this.$message.error(msg);
					}
				} else if (btnType == 'esc') {
					this.$emit('close');
				}
			} catch (error) {
				console.log('>>>error', error);
			}
		}
	}
};
</script>

<style lang="scss" scoped></style>
