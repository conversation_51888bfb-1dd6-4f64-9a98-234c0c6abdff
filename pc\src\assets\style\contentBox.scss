.content {
	.row {
		margin-bottom: 20px;
	}

	// .el-form-item__label {
	// 	background: #f0f0f0;
	// 	border: 1px solid #c2c2c2;
	// 	color: #000;
	// }

	.el-col-12 {
		height: 40px;
	}

	.el-input__inner {
		// border-radius: 0px !important;
		border-color: #c2c2c2 !important;
		vertical-align: unset;
		// height: 40px !important;
	}

	.title {
		font-size: 14px;
		font-weight: bold;
		color: #000;
	}

	.title::before {
		content: '';
		display: inline-block;
		height: 14px;
		border-left: 5px solid var(--btnColor);
		margin-right: 5px;
	}

	.dialog-footer {
		text-align: end;
	}

	.el-select {
		width: 100%;
		height: 40px;
	}

	.el-col-24 {
		.el-form-item__label {
			height: 115px;
			display: flex;
			align-items: center;
			line-height: unset;
		}

		.el-textarea__inner {
			border-radius: unset;
			border-color: #c2c2c2;
			padding-bottom: 45px;
			height: 115px;
		}

		.el-input__count {
			bottom: 2px;
			right: 20px;
		}
	}

	.el-date-editor.el-input,
	.el-date-editor.el-input__inner {
		// width: unset;
		width: 100%;
	}
}

.content .time.el-col.el-col-24 .el-form-item__label {
	height: 40px !important;
}

.content .time.el-col.el-col-24 {
	height: 40px;
}

.content .time .el-date-editor.el-input,
.content .el-date-editor.el-input__inner {
	width: 100%;
}