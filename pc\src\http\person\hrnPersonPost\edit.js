export default {
	computed: {
		formItemList() {
			return [
				{
					name: 'hrnPersonId',
					label: '基本信息ID',
					value: '',
					col: 12,
					rules: {
						required: true,
						message: '请输入基本信息ID'
					}
				},
				{
					name: 'orgId',
					label: '部门/学院ID',
					value: '',
					col: 12,
					rules: {
						required: true,
						message: '请输入部门/学院ID'
					}
				},
				{
					name: 'orgName',
					label: '部门/学院',
					value: '',
					col: 12,
					rules: {
						required: true,
						message: '请输入部门/学院'
					}
				},
				{
					name: 'postId',
					label: '岗位名称ID',
					value: '',
					col: 12,
					rules: {
						required: true,
						message: '请输入岗位名称ID'
					}
				},
				{
					name: 'postName',
					label: '岗位名称',
					value: '',
					col: 12,
					rules: {
						required: true,
						message: '请输入岗位名称'
					}
				},
				{
					name: 'postPropertyId',
					label: '岗位性质ID',
					value: '',
					col: 12,
					rules: {
						required: true,
						message: '请输入岗位性质ID'
					}
				},
				{
					name: 'postPropertyName',
					label: '岗位性质',
					value: '',
					col: 12,
					rules: {
						required: true,
						message: '请输入岗位性质'
					}
				},
				{
					name: 'postTypeName',
					label: '岗位类别',
					value: '',
					col: 12,
					rules: {
						required: true,
						message: '请输入岗位类别'
					}
				},
				{
					name: 'postTypeId',
					label: '岗位类别ID',
					value: '',
					col: 12,
					rules: {
						required: true,
						message: '请输入岗位类别ID'
					}
				},
				{
					name: 'postStatus',
					label: '岗位状态（已转岗，在任）',
					value: '',
					col: 12,
					rules: {
						required: true,
						message: '请输入岗位状态（已转岗，在任）'
					}
				},
				{
					name: 'transferId',
					label: '转岗ID',
					value: '',
					col: 12,
					rules: {
						required: true,
						message: '请输入转岗ID'
					}
				},
				{
					name: 'status',
					label: '状态',
					value: '',
					col: 12,
					rules: {
						required: true,
						message: '请输入状态'
					}
				},
			]
		}
	}
};
