<template>
	<div class="serve-hall">
		<!-- 顶部导航栏 -->
		<div class="hall-top">
			<VocationalHeader class="box-center"></VocationalHeader>
		</div>
		<!-- 顶部展示图片 -->
		<img src="@/assets/images/serveHall/top-bg.png" class="navbar-top" />

		<!-- 通知公告 -->
		<div class="notice">
			<div class="title-box">
				<span class="title-info">通知公告</span>
				<span class="title-child">NOTICE & ANNOUNCEMEN</span>
			</div>
			<div class="notice-btn">
				<BackButtom />
			</div>
			<div v-if="articleList.length > 0 || loading" v-loading="loading" class="notice-content">
				<div
					v-for="item in articleList"
					:key="item.id"
					class="notice-content-item"
					@click="onDetail(item)"
				>
					<img class="img" src="@/assets/images/serveHall/list.png" alt="" />
					<p class="floor1">
						<span class="title ellipsis-1">{{ item.title }}</span>
						<span v-if="item.isRead == 0" class="unread">未读</span>
					</p>
					<p class="floor2">
						{{ item.abstract }}
					</p>
					<p class="floor3">
						<span class="title">{{ item.createTime | dateShow }}</span>
						<!-- <span class="position">来源：学院教务处</span> -->
					</p>
				</div>
				<div class="block">
					<el-pagination
						background
						:current-page="currentPage"
						:page-sizes="[10, 20, 30, 40]"
						:page-size="size"
						layout="total, sizes, prev, pager, next, jumper"
						:total="total"
						@size-change="handleSizeChange"
						@current-change="handleCurrentChange"
					></el-pagination>
				</div>
			</div>
			<div v-else class="empty-none">
				<Empty text="暂无公告" />
			</div>
		</div>
	</div>
</template>

<script>
import VocationalHeader from '../header/VocationalHeader.vue';
import BackButtom from '@/components/backButtom.vue';
import Empty from '../components/empty.vue';
import { tenantId } from '@/config';

export default {
	name: 'ServeHall',
	components: {
		VocationalHeader,
		BackButtom,
		Empty
	},
	filters: {
		// 日期格式切换
		dateShow(str) {
			let strTime = '';
			if (str) {
				let dateArray = str.split(' ')[0].split('-');
				strTime = dateArray[0] + '.' + dateArray[1] + '.' + dateArray[2];
			}
			return strTime;
		}
	},
	data() {
		return {
			loading: false,
			currentPage: 1,
			total: 0,
			size: 10,
			articleList: []
		};
	},
	mounted() {
		this.getInformation();
	},
	methods: {
		/**
		 * @description 获取公告列表
		 * */
		getInformation() {
			let data = {
				nodeCode: 'educationServiceNotice',
				tenantId: tenantId,
				pageNum: this.currentPage,
				pageSize: this.size
			};
			this.$.ajax({
				url: '/ybzy/specificHomepageShortcut/paging',
				params: data
			}).then(res => {
				this.articleList = res?.results?.records || [];
				this.total = res?.results?.total || 0;
				this.currentPage = res?.results?.current || 1;
			});
		},
		handleSizeChange(val) {
			this.currentPage = 1;
			this.size = val;
			console.log(`每页 ${val} 条`);
			this.getInformation();
		},
		handleCurrentChange(val) {
			this.currentPage = val;
			console.log(`当前页: ${val}`);
			this.getInformation();
		},
		// 请求接口数据
		queryList() {},
		onDetail(item) {
			this.$router.push({
				path: '/noticeDetail',
				query: {
					id: item.id
				}
			});
		}
	}
};
</script>

<style lang="scss" scoped>
$maxWidth: 1200px;
.box-center {
	width: $maxWidth;
	margin: 0 auto;
}
.serve-hall {
	font-family: MicrosoftYaHei;
	width: 100%;
	height: 100%;
	background: #ecf0f4;
	min-width: 1550px;
	position: relative;
	overflow: auto;
	.hall-top {
		width: 100%;
		background: #0175e8;
	}
	.navbar-top {
		width: 100%;
		height: 200px;
		object-fit: cover;
	}
	.notice {
		min-height: 40vh;
		width: 1200px;
		background: #ffffff;
		border-radius: 8px;
		margin: 20px auto;
		padding: 20px 20px 30px 20px;
		position: relative;
		.title-box {
			cursor: default;
			display: flex;
			flex-direction: column;
			justify-content: center;
			align-items: center;
			margin-bottom: 20px;
			.title-info {
				height: 26px;
				font-size: 22px;
				color: #000000;
				line-height: 26px;
				margin-bottom: 10px;
			}

			.title-child {
				width: 190px;
				height: 22px;
				font-size: 14px;
				font-family: ArialMT;
				color: #a1a5af;
				line-height: 22px;
			}
		}
		.notice-btn {
			position: absolute;
			top: 20px;
			left: 20px;
		}
		.notice-content {
			.notice-content-item {
				position: relative;
				padding: 10px 0;
				margin-left: 23px;
				border-bottom: 2px solid #ececec;
				cursor: pointer;
				.img {
					width: 14px;
					height: 16px;
					position: absolute;
					top: 15px;
					left: -23px;
				}

				.floor1 {
					display: flex;
					align-items: center;
					.title {
						height: 24px;
						font-size: 18px;
						color: #242527;
						line-height: 24px;
						margin-right: 10px;
					}
					.unread {
						width: 40px;
						height: 20px;
						background: #ffffff;
						border-radius: 10px;
						border: 1px solid #cedae5;
						font-size: 12px;
						color: #6e7a85;
						line-height: 18px;
						text-align: center;
						flex-shrink: 0;
					}
				}

				.floor2 {
					margin-top: 10px;
					font-size: 14px;
					color: #8c9099;
					line-height: 22px;
					overflow: hidden;
					text-overflow: ellipsis;
					display: -webkit-box;
					-webkit-box-orient: vertical;
					-webkit-line-clamp: 2;
					white-space: normal;
				}
				.floor3 {
					height: 24px;
					line-height: 24px;
					margin-top: 10px;
					display: flex;
					.title {
						width: 71px;
						font-size: 14px;
						font-family: ArialMT;
						color: #242527;
						margin-right: 40px;
					}
					.position {
						width: 112px;
						font-size: 14px;
						font-family: PingFangSC, PingFang SC;
						font-weight: 400;
						color: #242527;
					}
				}
				&:hover {
					.floor1 {
						.title {
							font-size: 18px;
							font-family: MicrosoftYaHei, MicrosoftYaHei;
							font-weight: bold;
							color: #51a8ff;
						}
					}
				}
			}
			.block {
				display: flex;
				justify-content: flex-end;
				padding-top: 21px;
			}
		}
	}
	::v-deep .empty-none {
		height: 40vh;
		line-height: 30vh;
		.desc {
			color: #999 !important;
		}
	}
}
</style>
