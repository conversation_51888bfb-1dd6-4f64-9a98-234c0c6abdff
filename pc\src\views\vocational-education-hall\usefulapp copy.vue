<template>
	<div class="usullayApp">
		<el-carousel
			arrow="never"
			:interval="5000"
			:autoplay="false"
			indicator-position="outside"
			height="340px"
		>
			<el-carousel-item v-for="(outeritem, outerindex) in myUsullyApp" :key="outerindex">
				<div v-for="(senitem, senindex) in outeritem" :key="senindex" class="rowUsullay">
					<div v-for="(item, index) in senitem" :key="index" class="rowUsullayItem">
						<div v-if="item.name !== 'emptyText'" class="itemUsullay" @click="toOpenWindow(item)">
							<div class="topPicUsullay">
								<img :src="handelPicUrl(item.icons)" alt="" srcset="" width="40px" />
							</div>
							<div class="textUsullay">{{ item.text }}</div>
						</div>
					</div>
				</div>
			</el-carousel-item>
		</el-carousel>
	</div>
</template>

<script>
import { picUrl } from '@/config/index';
export default {
	data() {
		return {
			loading: false,
			myUsullyApp: []
		};
	},
	mounted() {
		this.getListData();
		this.getAddListSort();
	},
	methods: {
		toOpenWindow(item) {
			// window.open(`${picUrl}${item.newDataUrl}${item.code}`);
			window.open(`${picUrl}${item.url}`);
		},
		//处理图片路径
		handelPicUrl(url) {
			return `${picUrl}${url}`;
		},
		//获取接口数据
		getListData() {
			this.loading = true;
			this.$request({
				// url: '/cform/cformAffairDefinition/availableForms.dhtml', //old
				url: '/sys/v1/mecpSys/getSysMenu.dhtml?userId=u6231eda47ddc4714904e686e33111615',
				// url: '/cform/cformAffairDefinition/commonForm.dhtml',

				params: {
					// maxCount: 30
				},
				method: 'GET'
			})
				.then(res => {
					//
					let arr = [];
					res.results.forEach(i => {
						i.children.forEach(item => {
							arr.push(item);
						});
					});
					console.log(arr, 'arr');
					//

					this.loading = false;
					// let list = [
					// 	{
					// 		icon: require('@/assets/image/shan2.png'),
					// 		name: '1',
					// 		code: '1'
					// 	},
					// 	{
					// 		icon: require('@/assets/image/shan2.png'),
					// 		name: '2',
					// 		code: '2'
					// 	},
					// 	{
					// 		icon: require('@/assets/image/shan2.png'),
					// 		name: '3',
					// 		code: '3'
					// 	},
					// 	{
					// 		icon: require('@/assets/image/shan2.png'),
					// 		name: '4',
					// 		code: '4'
					// 	},
					// 	{
					// 		icon: require('@/assets/image/shan2.png'),
					// 		name: '5',
					// 		code: '5'
					// 	},
					// 	{
					// 		icon: require('@/assets/image/shan2.png'),
					// 		name: '6',
					// 		code: '6'
					// 	},
					// 	{
					// 		icon: require('@/assets/image/shan2.png'),
					// 		name: '7',
					// 		code: '7'
					// 	},
					// 	{
					// 		icon: require('@/assets/image/shan2.png'),
					// 		name: '8',
					// 		code: '8'
					// 	},
					// 	{
					// 		icon: require('@/assets/image/shan2.png'),
					// 		name: '9',
					// 		code: '9'
					// 	},
					// 	{
					// 		icon: require('@/assets/image/shan2.png'),
					// 		name: '10',
					// 		code: '10'
					// 	},
					// 	{
					// 		icon: require('@/assets/image/shan2.png'),
					// 		name: '11',
					// 		code: '11'
					// 	},
					// 	{
					// 		icon: require('@/assets/image/shan2.png'),
					// 		name: '12',
					// 		code: '12'
					// 	},
					// 	{
					// 		icon: require('@/assets/image/shan2.png'),
					// 		name: '13',
					// 		code: '13'
					// 	},
					// 	{
					// 		icon: require('@/assets/image/shan2.png'),
					// 		name: '14',
					// 		code: '14'
					// 	},
					// 	{
					// 		icon: require('@/assets/image/shan2.png'),
					// 		name: '15',
					// 		code: '15'
					// 	},
					// 	{
					// 		icon: require('@/assets/image/shan2.png'),
					// 		name: '16',
					// 		code: '16'
					// 	},
					// 	{
					// 		icon: require('@/assets/image/shan2.png'),
					// 		name: '17',
					// 		code: '17'
					// 	},
					// 	{
					// 		icon: require('@/assets/image/shan2.png'),
					// 		name: '18',
					// 		code: '18'
					// 	},
					// 	{
					// 		icon: require('@/assets/image/shan2.png'),
					// 		name: '18',
					// 		code: '19'
					// 	},
					// 	{
					// 		icon: require('@/assets/image/shan2.png'),
					// 		name: '20',
					// 		code: '20'
					// 	},
					// 	{
					// 		icon: require('@/assets/image/shan2.png'),
					// 		name: '21',
					// 		code: '21'
					// 	},
					// 	{
					// 		icon: require('@/assets/image/shan2.png'),
					// 		name: '22',
					// 		code: '22'
					// 	},
					// 	{
					// 		icon: require('@/assets/image/shan2.png'),
					// 		name: '23',
					// 		code: '23'
					// 	},
					// 	{
					// 		icon: require('@/assets/image/shan2.png'),
					// 		name: '24',
					// 		code: '24'
					// 	},
					// 	{
					// 		icon: require('@/assets/image/shan2.png'),
					// 		name: '25',
					// 		code: '25'
					// 	},
					// 	{
					// 		icon: require('@/assets/image/shan2.png'),
					// 		name: '26',
					// 		code: '26'
					// 	},
					// 	{
					// 		icon: require('@/assets/image/shan2.png'),
					// 		name: '27',
					// 		code: '27'
					// 	},
					// 	{
					// 		icon: require('@/assets/image/shan2.png'),
					// 		name: '28',
					// 		code: '28'
					// 	},
					// 	{
					// 		icon: require('@/assets/image/shan2.png'),
					// 		name: '29',
					// 		code: '29'
					// 	},
					// 	{
					// 		icon: require('@/assets/image/shan2.png'),
					// 		name: '30',
					// 		code: '30'
					// 	},
					// 	{
					// 		icon: require('@/assets/image/shan2.png'),
					// 		name: '31',
					// 		code: '31'
					// 	},
					// 	{
					// 		icon: require('@/assets/image/shan2.png'),
					// 		name: '32',
					// 		code: '32'
					// 	},
					// 	{
					// 		icon: require('@/assets/image/shan2.png'),
					// 		name: '33',
					// 		code: '33'
					// 	},
					// 	{
					// 		icon: require('@/assets/image/shan2.png'),
					// 		name: '34',
					// 		code: '34'
					// 	},
					// 	{
					// 		icon: require('@/assets/image/shan2.png'),
					// 		name: '35',
					// 		code: '35'
					// 	},
					// 	{
					// 		icon: require('@/assets/image/shan2.png'),
					// 		name: '36',
					// 		code: '36'
					// 	}
					// ];
					// let list = res.data;
					// let list = arr.splice(0, 30);
					let list = arr;
					for (let i = 0; i < Math.ceil(list.length / 15); i++) {
						let arr = []; //页
						if (i < Math.floor(list.length / 15)) {
							for (let k = 0; k < 3; k++) {
								let itemArr = []; //行
								for (let j = 0; j < 5; j++) {
									itemArr.push(list[i * 15 + k * 5 + j]);
								}
								arr.push(itemArr);
							}
						} else {
							let remain = list.length % 15;
							let remainRow = Math.ceil(remain / 5);
							for (let i = 0; i < remainRow; i++) {
								let itemArr = [];
								for (let k = 0; k < 5; k++) {
									if (!!list[list.length - remain + i * 5 + k]) {
										itemArr.push(list[list.length - remain + i * 5 + k]);
									} else {
										itemArr.push({
											icon: require('@/assets/image/shan2.png'),
											name: 'emptyText',
											code: '36'
										});
									}
								}
								arr.push(itemArr);
							}
						}
						this.myUsullyApp.push(arr);
					}
					let name = 'two';
					window.parent.postMessage(name, '*');
				})
				.catch(error => {
					this.loading = false;
				});
		},
		//获取排序接口
		getListSort() {
			this.$request({
				url: '/ybzy/platUserCommfunc/front/dragSort',
				data: {
					sortNum: '0'
				},
				method: 'POST'
			})
				.then(res => {})
				.catch(error => {});
		},
		//
		getAddListSort() {
			this.$request({
				url: '/ybzy/platUserCommfunc/front/save',
				data: {
					commId: 'fb88eea4ad734ee2a8d04e1221a14d56'
				},
				method: 'POST'
			})
				.then(res => {})
				.catch(error => {});
		}
	}
};
</script>

<style lang="scss" scoped>
.usullayApp {
	width: 100%;
	height: 380px;
	padding: 10px 30px 0 30px;
	background: #fff;
	// overflow: hidden;
	.rowUsullay {
		width: 100%;
		height: 114px;
		display: flex;
		.rowUsullayItem {
			flex: 1;
			height: 100%;
			display: flex;
			justify-content: center;
			.itemUsullay {
				cursor: pointer;
				width: 70px;
				height: 100%;

				.topPicUsullay {
					width: 100%;
					height: 70px;
					border-radius: 50%;
					display: flex;
					justify-content: center;
					align-items: center;
					// background: #4545ef;
				}
				.textUsullay {
					white-space: nowrap;
					overflow: hidden;
					text-overflow: ellipsis;
					width: 100%;
					text-align: center;
					height: 36px;
					line-height: 36px;
					font-size: 14px;
					font-family: Microsoft YaHei;
					font-weight: bold;
					color: #333333;
				}
			}
		}
	}
}
::v-deep .is-active .el-carousel__button {
	// 指示器激活按钮
	background: var(--brand-6, #0076e8);
	height: 10px;
	width: 22px;
	border-radius: 5px;
}
::v-deep .el-carousel__button {
	// 指示器按钮
	width: 10px;
	height: 10px;
	background: #c4c4c6;
	border-radius: 50%;
}
</style>
