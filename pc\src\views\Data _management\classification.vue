<template>
	<div class="container">
		<div class="handleAdd">
			<el-button type="primary" size="small" @click="openAdd">新增</el-button>
			<el-button size="small" @click="handleBack" v-if="ids.length > 1">返回上一级</el-button>
		</div>

		<es-data-table
			:data="tabledata"
			:tree-props="{ children: 'children', hasChildren: 'hasChildren' }"
			v-loading="isLoading"
			@selection-change="selectchange"
		>
			<template slot="append">
				<el-table-column type="selection" align="center"></el-table-column>
				<el-table-column label="分类名称" prop="journalName" align="center"></el-table-column>
				<el-table-column label="父级名称" prop="parentName" align="center"></el-table-column>
				<el-table-column label="创建时间" prop="createTime" align="center"></el-table-column>
				<el-table-column label="状态" align="center">
					<template slot-scope="{ row }">
						<el-switch
							v-model="row.status"
							active-text="启用"
							inactive-text="停用"
							:active-value="1"
							:inactive-value="0"
							@change="switchChange($event, row)"
						></el-switch>
					</template>
				</el-table-column>
				<el-table-column label="操作" align="center">
					<template slot-scope="{ row }">
						<el-button size="small" @click="checkDownLevel(row)">查看下级</el-button>
						<el-button size="small" @click="handleEdit(row)">编辑</el-button>
						<el-button size="small" @click="handleDelete(row)">删除</el-button>
					</template>
				</el-table-column>
			</template>
		</es-data-table>
		<div style="width: 100%; display: flex; justify-content: flex-end">
			<es-pagination :totalCount="totalCount" :pageSize="10"></es-pagination>
		</div>
		<el-dialog :title="title" :visible.sync="dialogFormVisible" width="500px" append-to-body>
			<el-form
				ref="formRef"
				:model="form"
				label-width="60"
				label-position="right"
				:rules="formRules"
			>
				<el-row>
					<el-col>
						<el-form-item label="分类名称" prop="journalName">
							<el-input v-model="form.journalName"></el-input>
						</el-form-item>
					</el-col>
				</el-row>
				<el-form-item label="状态" prop="status">
					<el-select style="width: 100%" v-model="form.status">
						<el-option :value="1" label="启用"></el-option>
						<el-option :value="0" label="停用"></el-option>
					</el-select>
				</el-form-item>
			</el-form>
			<div slot="footer" class="dialog-footer">
				<el-button @click="dialogFormVisible = false">取 消</el-button>
				<el-button type="primary" @click="handleSureAdd">确 定</el-button>
			</div>
		</el-dialog>
	</div>
</template>

<script>
export default {
	data() {
		return {
			tabledata: [],
			tablePage: {
				pageNum: 1,
				pageSize: 10,
				parentId: 0
			},
			totalCount: 0,
			ids: [0],
			isLoading: false,
			title: '新增分类',
			dialogFormVisible: false,
			form: {
				journalName: '',
				parentId: 0,
				status: 1
			},
			selectData: [],
			formRules: {
				journalName: [{ required: true, message: '请填写', trigger: 'blur' }]
			}
		};
	},
	mounted() {
		this.getTableData();
	},
	methods: {
		// 查看列表
		getTableData() {
			this.isLoading = true;
			this.$request({
				url: '/ybzy//journal/list',
				method: 'post',
				data: this.tablePage,
				format: false
			}).then(res => {
				this.tabledata = res.results.records;
				this.totalCount = res.results.total;
				this.isLoading = false;
			});
		},
		// 更改状态
		switchChange(val, row) {
			let data = {
				journalName: row.journalName,
				parentId: row.parentId,
				id: row.id,
				status: val
			};
			this.$request({
				url: '/ybzy/journal/update',
				method: 'post',
				data: data,
				format: false
			}).then(res => {
				this.$message.success('操作成功');
				this.getTableData();
			});
		},
		// 查看下级id
		checkDownLevel(row) {
			this.tablePage.parentId = row.id;
			this.ids.push(row.id);
			this.getTableData();
		},
		handleBack() {
			this.ids.pop();
			this.tablePage.parentId = this.ids[this.ids.length - 1];
			this.getTableData();
		},
		handleDelete(row) {
			let id = [row.id];
			this.$request({
				url: '/ybzy/journal/delete',
				method: 'post',
				data: id,
				format: false
			}).then(res => {
				this.getTableData();
				this.$message.success('删除成功');
			});
		},
		selectchange(val) {
			this.selectData = val;
		},
		openAdd() {
			this.title = '新增分类';
			if (this.selectData.length > 1) {
				this.$message.warning('请选择一项进行新增');
				return;
			}
			// this.$refs.formRef.resetFields();
			this.form = {
				journalName: '',
				parentId: 0,
				status: 1
			};
			this.dialogFormVisible = true;
		},
		handleSureAdd() {
			this.$refs.formRef.validate(valid => {
				if (valid) {
					if (this.title == '新增分类') {
						if (this.selectData.length) {
							this.form.parentId = this.selectData[0].id;
						} else {
							this.form.parentId = 0;
						}
						this.$request({
							url: '/ybzy/journal/create',
							method: 'post',
							data: this.form,
							format: false
						}).then(res => {
							this.$message.success('新增成功');
							this.dialogFormVisible = false;
							this.getTableData();
						});
					} else {
						this.$request({
							url: '/ybzy/journal/update',
							method: 'post',
							data: this.form,
							format: false
						}).then(res => {
							this.$message.success('编辑成功');
							this.dialogFormVisible = false;
							this.getTableData();
						});
					}
				} else {
					return false;
				}
			});
		},
		handleEdit(row) {
			this.title = '编辑分类';
			this.dialogFormVisible = true;
			this.form = JSON.parse(JSON.stringify(row));
		}
	}
};
</script>

<style lang="scss" scoped>
.container {
	padding: 20px;
	height: 100%;
}
::v-deep .es-data-table {
	// height: calc(100% - 80px);
}
.handleAdd {
	margin-bottom: 10px;
	width: 100%;
	display: flex;
}
</style>
