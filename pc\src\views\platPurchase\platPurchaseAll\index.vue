<template>
  <div class="content">
    <es-data-table ref="table" row-key="id" :data="tableData" full :thead="thead" :toolbar="toolbar" :url="dataTableUrl"
      :page="pageOption" :param="params" @btnClick="btnClick" @search="hadeSearch" @reset="hadeReset"></es-data-table>
    <!-- 查看 -->
    <es-dialog title="查看" :visible.sync="showView" width="1300px" :drag="false" :close-on-click-modal="false"
      :destroy-on-close="true" @close="ResetView">
      <es-tabs v-model="activeName" v-if="showView">
        <el-tab-pane label="表单信息" name="first">
          <es-form ref="viewRef" :model="viewData" :contents="viewItemList" table readonly @reset="showView = false"
            label-width='200' :submit="false" />
        </el-tab-pane>
        <el-tab-pane label="审批意见" name="second">
          <es-data-table :data="auditOpinionList" :thead="auditOpinionListThead" stripe>
          </es-data-table>
        </el-tab-pane>
        <el-tab-pane label="补充资料信息" name="third"
          v-if="NowAuditNode === '3' || NowAuditNode === '5.1' || NowAuditNode === '3.2'">
          <div class="Pane-BodyBox">
            <es-form ref="formRef" :model="item" :contents="viewBcFormItemList[index]" table readonly
              v-for="item, index in bcFormDataList" :key="item.id" />
          </div>
        </el-tab-pane>
      </es-tabs>
    </es-dialog>

  </div>
</template>
<script>
import platPurchaseApplyApi from "@/http/plat/platPurchaseApply";
export default {
  name: "platPurchaseAll",
  data() {
    return {
      activeName: 'first',
      deptList: [],
      tableData: [],
      pageOption: {
        layout: 'total, sizes, prev, pager, next, jumper',
        pageSize: 10,
        position: 'center',
        current: 1,
        pageNum: 1
      },
      params: {
        orderBy: 't1.create_time',
        asc: 'false'
      },
      showView: false,
      viewData: {},
      bcFormData: {},
      RowsID: null,
      bcFormDataList: [],
      viewBcFormItemList: [],
      submitFilterParams: {},
      auditOpinionList: [],
      ViewAuditNodeList: {
        "0": '暂存',
        "0.2": '申请驳回',
        "1": '学院审核',
        "2": '处长审核',
        "3": '资料审核',
        "3.3": '完善资料',
        "4": '国资审核 ',
        "5": '审核通过',
        "5.1": '已办结'
      },
      auditNodeList: {
        "0": '暂存',
        "0.2": '申请驳回',
        "1": '待学院审核',
        "2": '待处长审核',
        "3": '资料审核',
        "3.3": '完善资料',
        "3.2": '补充资料驳回',
        "4": '待国资审核 ',
        "5": '审核通过',
        "5.1": '已办结'
      },
      NowAuditNode: '',
      extractionTypeList: [
        { label: '专家抽取表', value: "1" },
        { label: '货物验收单', value: "2" },
        { label: '验收报告', value: "3" },
        { label: '其他资料', value: "4" },
      ],
    }
  },
  watch: {
    showView(val) {
      if (!val) {
        this.bcFormDataList = [];
        this.viewBcFormItemList = [];
      }
    },
  },
  computed: {
    auditOpinionListThead() {
      return [
        {
          align: 'center',
          title: '审核节点',
          field: 'auditNodeName'
        },
        {
          align: 'center',
          title: '审核人',
          field: 'userName'
        },
        {
          align: 'center',
          title: '审核状态',
          field: 'auditStatus'
        },
        {
          align: 'center',
          title: '审批时间',
          field: 'updateTime'
        },
        {
          title: '审批意见',
          align: 'center',
          field: 'auditExplain'
        },
      ]
    },
    toolbar() {
      return [
        {
          type: 'button',
          contents: [
            {
              text: '导出',
              code: 'export',
              type: 'primary'
            }
          ]
        },
        {
          type: 'search',
          reset: true,
          contents: [
            {
              type: 'text',
              label: '项目名称',
              name: 'projectName',
              placeholder: '请输入项目名称',
              col: 3
            },
            // {
            //   type: 'text',
            //   label: '中标供应商名称',
            //   name: 'bidSupplierName',
            //   placeholder: '请输入中标供应商名称',
            //   col: 3
            // },
            // {
            //   type: 'select',
            //   col: 3,
            //   label: '采购部门',
            //   placeholder: '请选择采购部门',
            //   name: 'purchaseDepartment',
            //   filterable: true,
            //   'value-key': 'value',
            //   'label-key': 'label',
            //   clearable: true,
            //   data: this.deptList
            // },
            // {
            //   type: 'select',
            //   col: 3,
            //   label: '预验收部门',
            //   placeholder: '预验收部门',
            //   name: 'predictAcceptanceDepartment',
            //   filterable: true,
            //   'value-key': 'value',
            //   'label-key': 'label',
            //   clearable: true,
            //   data: this.deptList
            // },

          ]
        }
      ]
    },
    thead() {
      return [
        {
          title: '项目名称',
          field: 'projectName',
          align: 'center'
        },
        {
          title: '项目负责人姓名',
          field: 'predictAcceptanceOfficial',
          align: 'center'
        },
        {
          title: '预验收时间',
          field: 'predictAcceptanceDate',
          align: 'center'
        },
        {
          title: '采购部门',
          field: 'purchaseDepartment',
          align: 'center',
          render: (h, { row }) => {
            let { purchaseDepartment } = row
            if (purchaseDepartment) {
              return h('span', null, this.deptList.find(item => item.value == purchaseDepartment).label);
            }
          }
        },
        {
          title: '预验收部门',
          field: 'predictAcceptanceDepartment',
          align: 'center',
          render: (h, { row }) => {
            let { predictAcceptanceDepartment } = row
            if (predictAcceptanceDepartment) {
              return h('span', null, this.deptList.find(item => item.value == predictAcceptanceDepartment).label);
            }
          }
        },
        {
          title: '状态',
          field: 'auditNode',
          align: 'center',
          width: 200,
          render: (h, { row }) => {
            let { auditNode } = row
            let status;
            if (auditNode === '5' || auditNode === '5.1') {
              status = 'success';
            } else if (['0', '1', '2', '3', '4'].includes(auditNode)) {
              status = 'warning';
            } else {
              status = 'danger';
            }
            return h('el-tag', { props: { type: status } }, this.auditNodeList[auditNode]);
          }
        },
        {
          title: '操作',
          type: 'handle',
          width: 100,
          template: '',
          events: [
            {
              code: 'view',
              text: '查看'
            }
          ]
        }
      ]
    },
    viewItemList() {
      return [
        {
          name: 'projectName',
          label: '项目名称',
          col: 6,
          placeholder: '请输入项目名称',
          rules: {
            required: true,
            message: '请输入项目名称'
          }
        },
        {
          name: 'projectNumber',
          label: '项目编号',
          col: 6,
          placeholder: '请输入项目编号',
          rules: {
            required: true,
            message: '请输入项目编号'
          }
        },
        {
          name: 'bidSupplierName',
          label: '中标供应商名称',
          col: 6,
          placeholder: '请输入中标供应商名称',
          rules: {
            required: true,
            message: '请输入中标供应商名称'
          }

        },
        {
          name: 'contractAmount',
          label: '合同金额',
          col: 6,
          placeholder: '请输入合同金额',
          rules: {
            required: true,
            message: '请输入合同金额'
          }
        },

        {
          name: 'purchaseDepartment',
          type: 'select',
          filterable: true,
          'value-key': 'value',
          'label-key': 'label',
          clearable: true,
          label: '采购部门',
          col: 6,
          placeholder: '请选择采购部门',
          rules: {
            required: true,
            message: '请选择采购部门'
          },
          data: this.deptList
        },
        {
          name: 'purchaseContent',
          label: '采购内容',
          col: 6,
          placeholder: '请填写采购内容',
          rules: {
            required: true,
            message: '请填写采购内容'
          }
        },
        {
          name: 'predictAcceptanceDepartment',
          type: 'select',
          filterable: true,
          'value-key': 'value',
          'label-key': 'label',
          clearable: true,
          label: '预验收部门',
          col: 6,
          placeholder: '请输入项目负责人姓名',
          rules: {
            required: true,
            message: '请输入项目负责人姓名'
          },
          data: this.deptList
        },
        {
          name: 'predictAcceptancePersonnel',
          label: '预验收人员(专家)',
          col: 6,
          placeholder: '请输入预验收人员(专家)姓名',
          rules: {
            required: true,
            message: '请输入预验收人员(专家)姓名'
          }
        },
        {
          name: 'predictAcceptanceDate',
          placeholder: '请选择预验收时间',
          col: 6,
          label: '预验收时间',
          valueFormat: 'yyyy-mm-dd',
          type: 'date',
        },
        {
          name: 'predictAcceptanceOfficial',
          label: '预验负责人(项目负责人)',
          col: 6,
          placeholder: '请输入预验负责人(项目负责人)姓名',
          rules: {
            required: true,
            message: '请输入预验负责人(项目负责人)姓名'
          }
        },
        {
          type: 'textarea',
          name: 'applyAcceptanceReason',
          label: '申请验收理由',
          col: 12,
          placeholder: '请输入申请验收理由',
          rules: {
            required: true,
            message: '请输申请验收理由'
          }
        },
        // {
        //   type: 'textarea',
        //   name: 'acceptanceVerdict',
        //   label: '验收结论',
        //   autosize: { minRows: 2, maxRows: 4 },
        //   col: 12,
        //   placeholder: '请输入验收结论',
        //   rules: {
        //     required: true,
        //     message: '请输入验收结论'
        //   }
        // },
        {
          type: 'remark',
          label: '备注',
          name: 'remark',
          autosize: { minRows: 2, maxRows: 4 },
          placeholder: '请输入备注',
          col: 12
        },

        {
          name: 'applyFile',
          label: '附件',
          type: 'attachment',
          col: 12,
          preview: true,
          code: 'plat_purchase_apply_file',
          ownId: this.viewData.id,
          rules: {
            required: true,
            message: '请上传附件'
          },

        },
      ]
    },
    bcFormItemList() {
      return [
        {
          name: 'extractionTypeStr',
          hide: !(this.NowAuditNode === '3' || this.NowAuditNode === '5.1' || this.NowAuditNode === '3.2'),
          label: '补充资料类型',
          multiple: true,
          col: 12,
          placeholder: '请选择补充类型',
          rules: {
            required: true,
            message: '请选择补充资料类型'
          },
        },


        {
          hide: !Array.isArray(this.bcFormData.extractionType) || !this.bcFormData.extractionType.includes('1'),
          name: 'zjFile',
          label: '专家抽取表',
          type: 'attachment',
          value: '',
          col: 12,
          preview: true,
          code: 'plat_purchase_apply_bc_zj_File',
          ownId: this.RowsID,
          rules: {
            required: true,
            message: '请上传专家抽取表'
          },
        },
        {
          hide: !Array.isArray(this.bcFormData.extractionType) || !this.bcFormData.extractionType.includes('1'),
          name: 'acceptanceLeaguer',
          label: '验收组成员',
          value: '',
          col: 12,
          placeholder: '请填写验收组成员',
          rules: {
            required: true,
            message: '请填写验收组成员'
          }
        },
        {
          hide: !Array.isArray(this.bcFormData.extractionType) || !this.bcFormData.extractionType.includes('2'),
          name: 'hwFile',
          label: '货物验收单',
          type: 'attachment',
          value: '',
          col: 12,
          preview: true,
          code: 'plat_purchase_apply_bc_hwfile',
          ownId: this.RowsID,
          rules: {
            required: true,
            message: '请上传货物验收单'
          },
        }, {
          hide: !Array.isArray(this.bcFormData.extractionType) || !this.bcFormData.extractionType.includes('3'),
          name: 'ysFile',
          label: '验收报告',
          type: 'attachment',
          value: '',
          col: 12,
          preview: true,
          code: 'plat_purchase_apply_bc_ysfile',
          ownId: this.RowsID,
          rules: {
            required: true,
            message: '请上传验收报告'
          },
        }, {
          hide: !Array.isArray(this.bcFormData.extractionType) || !this.bcFormData.extractionType.includes('3'),
          valueFormat: 'yyyy-mm-dd',
          type: 'date',
          name: 'acceptanceTime',
          label: '验收时间',
          value: '',
          col: 12,
          placeholder: '请选择验收时间',
          rules: {
            required: true,
            message: '请选择验收时间'
          }
        }, {
          hide: !Array.isArray(this.bcFormData.extractionType) || !this.bcFormData.extractionType.includes('3'),
          type: 'textarea',
          col: 12,
          label: '验收结论',
          name: 'acceptanceConclusion',
          placeholder: '请填写验收结论',
          rules: {
            required: true,
            message: '请填写验收结论'
          }
        }, {
          hide: !Array.isArray(this.bcFormData.extractionType) || !this.bcFormData.extractionType.includes('4'),
          name: 'qtFile',
          label: '其他资料',
          type: 'attachment',
          value: '',
          col: 12,
          preview: true,
          code: 'plat_purchase_apply_bc_qtfile',
          ownId: this.RowsID,
          rules: {
            required: true,
            message: '请上传其他资料'
          },
        },

      ]
    },
    dataTableUrl() {
      return platPurchaseApplyApi.listJson
    }
  },
  mounted() {
    this.getSelectList();
  },
  methods: {
    getSelectList() {
      this.$request({
        url: platPurchaseApplyApi.getOriList,
        method: 'POST'
      }).then(result => {
        this.deptList = result?.results || [];
      });
    },
    ResetView() {
      this.auditOpinionList = []
      this.bcFormData = {}
      this.viewData = {}
      this.activeName = 'first'
    },
    /**
     * headle按钮事件
     */
    async btnClick(res) {
      if (res.row) {
        let { id } = res.row
        this.RowsID = id
      }
      let code = res.handle.code;
      switch (code) {
        case 'view':
          // 查看
          this.NowAuditNode = res.row.auditNode;
          let ids = res.row.id
          this.ResetView()
          this.getViewData(ids)
          break;
        case 'export':
          this.exportFile();
          break;
        default:
          break;
      }
    },
    hadeSearch(e) {
      this.submitFilterParams = e;
    },
    hadeReset() {
      this.submitFilterParams = {};
    },
    //导出函数
    exportFile() {
      let isDev = process.env.NODE_ENV === 'development' ? '/api' : '';
      const paramAll = { ...this.submitFilterParams };
      const paramStr = this.objToUrlParams(paramAll);
      let url = `${isDev}${platPurchaseApplyApi.exportData}${paramStr ? '?' + paramStr : ''}`;
      window.open(url);
    },
    objToUrlParams(obj) {
      return Object.keys(obj)
        .filter(key => obj[key] !== '' && obj[key] !== null)
        .map(key => encodeURIComponent(key) + '=' + encodeURIComponent(obj[key]))
        .join('&');
    },

    // 获取查看信息
    async getViewData(ids) {
      const [infoRes, auditRes, bcData] = await Promise.all([
        this.$request({
          url: platPurchaseApplyApi.info,
          method: 'get',
          params: { id: ids }
        }),
        this.$request({
          url: platPurchaseApplyApi.platPurchaseAuditlist,
          method: 'get',
          params: { applyId: ids }
        }),
        this.$request({
          url: platPurchaseApplyApi.purchaseAuditList,
          method: 'get',
          params: { applyId: ids }
        })
      ]);

      // 处理 infoRes
      this.viewData = infoRes.results || {};
      // 处理 auditRes
      const data = auditRes.results;
      const auditOpinionList = data.map(element => {
        const { auditExplain, userName, auditStatus, updateTime, auditNode } = element;
        return {
          auditNodeName: this.ViewAuditNodeList[auditNode],
          auditExplain,
          userName,
          updateTime,
          auditStatus: auditStatus === '1' ? '通过' : '不通过'
        };
      });
      this.auditOpinionList = auditOpinionList;

      // 处理 bcData
      bcData.results.forEach(items => {
        items.extractionType = items.extractionType.split(',');
        let str = '';
        items.extractionType.forEach((item, index, array) => {
          str += this.extractionTypeList.find(ele => ele.value === item).label;
          if (index < array.length - 1) {
            str += '  ||  ';
          }
        });
        items.extractionTypeStr = str
      })
      this.bcFormDataList = bcData.results
      // 生成各个选项表格结构
      this.renderBcFormItemList(bcData.results)
      this.showView = true;
    },

    renderBcFormItemList(list) {
      const regionList = this.bcFormItemList;
      const finalList = [];
      const elementMapping = {
        zjFile: '1',
        acceptanceLeaguer: '1',
        hwFile: '2',
        ysFile: '3',
        acceptanceTime: '3',
        acceptanceConclusion: '3',
        qtFile: '4'
      };
      list.forEach(items => {
        const typeList = items.extractionType;
        let itemsList = JSON.parse(JSON.stringify(regionList));
        itemsList.forEach(element => {
          const type = elementMapping[element.name];
          if (type) {
            element.hide = !typeList.includes(type);
          }
        });
        finalList.push(itemsList);
      });
      this.viewBcFormItemList = finalList;
    },

  }
}
</script>

<style scoped lang="scss">
@import '../../../assets/style/style.scss';
.content {
  width: 100%;
  height: 100%;
}

.Pane-BodyBox {
  width: 100%;
  height: 100%;
  overflow: scroll;
}
</style>