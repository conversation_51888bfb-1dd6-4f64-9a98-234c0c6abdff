<template>
	<div class="card-box">
		<div class="student-box">
			<div :style="{ width: '100%', height: '110px', marginBottom: '12px' }" class="card-style">
				<p class="card-title">
					实习就业总数
					<span class="num">{{ proprSxqksj.sxjyzs }}</span>
					人
				</p>
				<div class="sex">
					<div
						v-for="(item, index) in [
							{ count: '', ratio: 100, ratioUp: 60, isUp: true },
							{ count: '', ratio: 80, ratioUp: 60, isUp: false }
						]"
						:key="index"
						class="sex-box"
					>
						<div class="l">
							<span class="bg" :style="{ width: item.ratio + '%' }" style=""></span>
						</div>
						<div class="c">
							{{ item.count }}
							<span class="unit">人</span>
						</div>
						<div class="r">
							较上年 {{ item.ratioUp || '-' }}%
							<img
								v-if="item.isUp === true"
								class="desc-img"
								:src="require('@/assets/images/home20240423/up-icon.png')"
								alt=""
							/>
							<img
								v-if="item.isUp === false"
								class="desc-img"
								:src="require('@/assets/images/home20240423/down-icon.png')"
								alt=""
							/>
						</div>
					</div>
				</div>
			</div>
			<div :style="{ width: '100%', height: '110px' }" class="card-style">
				<p class="card-title">
					实习未就业总数
					<span class="num">
						{{ proprSxqksj.sxwjyzs }}
					</span>
					人
				</p>
				<div class="sex">
					<div
						v-for="(item, index) in [
							{ count: '', ratio: 60, ratioUp: 60, isUp: true },
							{ count: '', ratio: 30, ratioUp: 60, isUp: false }
						]"
						:key="index"
						class="sex-box"
					>
						<div class="l">
							<span class="bg" :style="{ width: item.ratio + '%' }"></span>
						</div>
						<div class="c">
							{{ item.count }}
							<span class="unit">人</span>
						</div>
						<div class="r">
							较上年 {{ item.ratioUp || '-' }}%
							<img
								v-if="item.isUp === true"
								class="desc-img"
								:src="require('@/assets/images/home20240423/up-icon.png')"
								alt=""
							/>
							<img
								v-if="item.isUp === false"
								class="desc-img"
								:src="require('@/assets/images/home20240423/down-icon.png')"
								alt=""
							/>
						</div>
					</div>
				</div>
			</div>
		</div>
		<div :style="{ width: '26%', height: '232px' }" class="card-style">
			<p class="card-title">本省实习top5分析</p>
			<p class="card-desc">
				<span>
					本省实习较上年
					<span class="num-text">0%</span>
				</span>
				<img class="desc-img" :src="require('@/assets/images/home20240423/down-icon.png')" alt="" />
			</p>
			<div id="thisProvince"></div>
		</div>
		<div :style="{ width: '26%', height: '232px' }" class="card-style">
			<p class="card-title">外省实习top5分析</p>
			<p class="card-desc">
				<span>
					省外实习较上年
					<span class="num-text">0%</span>
				</span>
				<img class="desc-img" :src="require('@/assets/images/home20240423/up-icon.png')" alt="" />
			</p>
			<div id="otherProvince"></div>
		</div>
		<div :style="{ width: '26%', height: '232px' }" class="card-style">
			<p class="card-title">实习报告</p>
			<p class="card-desc">
				<span>
					实习报告总数
					<span class="num-text">0</span>
					份
				</span>
			</p>
			<div class="report-box">
				<div class="box-item">
					<img
						class="item-img"
						:src="require('@/assets/images/home20240423/submitted.png')"
						alt=""
					/>
					<p class="desc-text">
						<span class="desc-name">已提交报告</span>
						<span class="desc-num">
							<span>0</span>
							<span class="desc-unit">人</span>
						</span>
					</p>
				</div>
				<div class="box-item">
					<img
						class="item-img"
						:src="require('@/assets/images/home20240423/unsubmit.png')"
						alt=""
					/>
					<p class="desc-text">
						<span class="desc-name">未提交报告</span>
						<span class="desc-num">
							<span>0</span>
							<span class="desc-unit">人</span>
						</span>
					</p>
				</div>
			</div>
		</div>
	</div>
</template>

<script>
import * as echarts from 'echarts';
import { xodbApi } from '@/api/xodb';
import { createNamespacedHelpers } from 'vuex';
const { mapState } = createNamespacedHelpers('getRoleMap');
export default {
	props: {
		proprSxqksj: {
			type: Object,
			default: () => {
				return {};
			}
		}
	},
	data() {
		return {
			collegeName: '', //学院名称数据
			thisProvinceCharts: null,
			// 本省
			thisProvinceList: [], //本省实习数据
			otherProvinceCharts: null,
			otherProvinceList: [] //外省实习数据
			// 外省
		};
	},
	computed: {
		...mapState(['collegeInfo']),
		// 外省实习数据图表
		otherProvinceOptions() {
			return {
				title: {
					text: '0人',
					subtext: '外省实习人数',
					left: '34%',
					top: '40%',
					textAlign: 'center',
					textStyle: {
						fontWeight: 'bold',
						fontSize: 16,
						color: ' #454545'
					}
				},
				tooltip: {
					trigger: 'item',
					formatter: '{a} <br/>{b} : {c} ({d}%)'
				},
				legend: {
					top: '10%',
					right: '3%',
					orient: 'vertical',
					itemWidth: 10, // 标志图形的长度
					itemHeight: 8, // 标志图形的宽度
					formatter: (name, value) => {
						// 添加
						let total = 0;
						let target;
						for (let i = 0; i < this.otherProvinceList.length; i++) {
							total += this.otherProvinceList[i].value;
							if (this.otherProvinceList[i].name === name) {
								target = this.otherProvinceList[i].value;
							}
						}
						var arr = [
							name,
							'{a|' + target + '人/' + ((target / total) * 100).toFixed(2) + '%' + '}'
						];
						return arr.join('  ');
					},
					textStyle: {
						fontSize: 12, // 字体大小
						color: '#454545', // 字体颜色
						rich: {
							a: {
								fontSize: 14,
								color: '#0A325B'
							}
						}
					},
					itemGap: 10,
					data: []
				},
				series: {
					name: '外省实习人数',
					type: 'pie',
					label: {
						show: false
					},
					emphasis: {
						label: {
							show: true
						}
					},
					radius: [30, 50],
					center: ['13%', '50%'],
					itemStyle: {
						borderRadius: 2
					},
					data: []
				},
				color: ['#DDED8C', '#91C778', '#304E73', '#50E4F4', '#C392F3']
			};
		},
		// 本省实习数据图标
		thisProvinceOptions() {
			return {
				title: {
					text: '0',
					subtext: '本省实习人数',
					left: '34%',
					top: '40%',
					textAlign: 'center',
					textStyle: {
						fontWeight: 'bold',
						fontSize: 16,
						color: ' #454545'
					}
				},
				tooltip: {
					trigger: 'item',
					formatter: '{a} <br/>{b} : {c} ({d}%)'
				},
				legend: {
					top: '10%',
					right: '1%',
					orient: 'vertical',
					itemWidth: 10, // 标志图形的长度
					itemHeight: 8, // 标志图形的宽度
					formatter: (name, value) => {
						// 添加
						let total = 0;
						let target;
						for (let i = 0; i < this.thisProvinceList.length; i++) {
							total += this.thisProvinceList[i].value;
							if (this.thisProvinceList[i].name === name) {
								target = this.thisProvinceList[i].value;
							}
						}
						var arr = [
							name,
							'{a|' + target + '人/' + ((target / total) * 100).toFixed(2) + '%' + '}'
						];
						return arr.join('  ');
					},
					textStyle: {
						fontSize: 12, // 字体大小
						color: '#454545', // 字体颜色
						rich: {
							a: {
								fontSize: 14,
								color: '#0A325B'
							}
						}
					},
					itemGap: 10,
					data: []
				},
				series: {
					name: '本省实习人数',
					type: 'pie',
					label: {
						show: false
					},
					emphasis: {
						label: {
							show: true
						}
					},
					radius: [30, 50],
					center: ['13%', '50%'],
					itemStyle: {
						borderRadius: 2
					},
					data: []
				},
				color: ['#FFD56D', '#FFFA4E', '#50E4F4', '#FF82E5', '#7AEB81']
			};
		}
	},
	watch: {
		proprSxqksj: {
			handler(val) {
				if (JSON.stringify(val) === '{}') return;
				this.toolList();
			},
			deep: true
		},
		'collegeInfo.name': {
			handler(val) {
				console.log('watch initData', val);
				this.collegeName = val;
				this.reload(); //刷新数据
				// const valClone = JSON.parse(JSON.stringify(val));
				// if (val.name) {
				// 	this.toolSelect(valClone);
				// 	this.inputValue = (valClone.name || '') + (valClone.number || '');
				// }
			},
			deep: true
		}
	},
	async mounted() {
		// this.createThisProvinceOptionsECharts(); //本省就业
		// this.createOtherProvinceOptionsECharts(); //外省就业
		// await this.getSxfxrsList(); //实习人数分析
	},
	methods: {
		// 本省就业人数
		createThisProvinceOptionsECharts(data, legend) {
			if (this.thisProvinceCharts) {
				this.thisProvinceCharts.dispose();
			}
			let chartDom = document.getElementById('thisProvince');
			if (!chartDom) {
				return;
			}
			this.thisProvinceCharts = echarts.init(chartDom);
			let options = this.thisProvinceOptions;
			options.series.data = data;
			options.legend.data = legend;
			this.thisProvinceCharts.setOption(options);
		},
		// 外省就业人数
		createOtherProvinceOptionsECharts(data, legend) {
			if (this.otherProvinceCharts) {
				this.otherProvinceCharts.dispose();
			}
			let chartDom = document.getElementById('otherProvince');
			if (!chartDom) {
				return;
			}
			this.otherProvinceCharts = echarts.init(chartDom);
			let options = this.otherProvinceOptions;
			options.series.data = data;
			options.legend.data = legend;
			this.otherProvinceCharts.setOption(options);
		},
		// 实习人数分析
		async getSxfxrsList() {
			const {
				dataResult: { dataList }
			} = await xodbApi.get({
				url: 'warehouseConfig_ybzy_dw_dwb_jw_sxfxrs_query'
			});
			for (let item of dataList) {
				if (item.ssmc == '四川省') {
					this.thisProvinceOptions.title.text = item.sxrs + '人';
				} else if (item.ssmc == '非四川省') {
					this.otherProvinceOptions.title.text = item.sxrs + '人';
				}
			}
			await this.getWssxfxList(); //外省实习分析
			await this.getBssxfxList(); //本省实习分析
		},
		// 外省实习分析
		async getWssxfxList() {
			let params = {
				xyjgmc: this.collegeName //学院机构名称
			};
			let {
				dataResult: { dataList }
			} = await xodbApi.get({
				url: 'warehouseConfig_ybzy_dw_dwb_jw_wssxfx_query',
				params: JSON.stringify(params)
			});
			this.otherProvinceList = [];
			dataList.sort((a, b) => {
				return b.xssl - a.xssl;
			});
			let List = dataList.slice(0, 5);
			let legend = List.reduce((acc, cur) => {
				this.otherProvinceList.push({
					...cur,
					name: cur.sxddszds,
					value: cur.xssl
				});
				acc.push(cur.sxddszds);
				return acc;
			}, []);
			this.createOtherProvinceOptionsECharts(this.otherProvinceList, legend);
		},
		// 本省实习分析
		async getBssxfxList() {
			let params = {
				xyjgmc: this.collegeName //学院机构名称
			};
			let {
				dataResult: { dataList }
			} = await xodbApi.get({
				url: 'warehouseConfig_ybzy_dw_dwb_jw_bssxfx_query',
				params: JSON.stringify(params)
			});
			this.thisProvinceList = [];
			dataList.sort((a, b) => {
				return b.xssl - a.xssl;
			});
			let List = dataList.slice(0, 5);
			let legend = List.reduce((acc, cur) => {
				this.thisProvinceList.push({
					...cur,
					name: cur.sxddszds,
					value: cur.xssl
				});
				acc.push(cur.sxddszds);
				return acc;
			}, []);
			this.createThisProvinceOptionsECharts(this.thisProvinceList, legend);
		},
		// 刷新
		reload() {
			this.getWssxfxList();//外省实习
			this.getBssxfxList();//省内实习
		}
	}
};
</script>

<style lang="scss" scoped>
.card-title {
	font-family: MicrosoftYaHei, MicrosoftYaHei;
	font-weight: bold;
	font-size: 16px;
	color: #21252b;
	line-height: 21px;
	.num {
		color: #0b6fad;
	}
}
.card-style {
	background: rgba(252, 253, 254, 0.7);
	box-shadow: 0px 0px 10px 0px rgba(5, 32, 70, 0.1);
	border-radius: 8px;
	border: 2px solid #ffffff;
	padding: 14px 12px;
}
.card-desc {
	font-family: MicrosoftYaHei;
	font-size: 14px;
	color: #454545;
	line-height: 19px;
	margin-top: 11px;
	.num-text {
		color: #0a325b;
		font-weight: bold;
	}
	.desc-img {
		width: 8px;
		margin-left: 5px;
	}
}
.student-box {
	width: calc(20% - 6px);
	.sex {
		display: flex;
		flex-direction: column;
		justify-content: space-between;
		margin-top: 10px;

		.sex-box {
			display: flex;
			align-items: center;
			justify-content: space-between;
			.l {
				min-width: 110px;
				height: 14.5px;
				.bg {
					display: inline-block;
					width: 100%;
					height: 100%;
					background-image: url('~@/assets/images/home20240423/man.png');
					background-size: contain;
				}
			}

			.c {
				height: 23px;
				font-family: DINPro, DINPro;
				font-weight: bold;
				font-size: 18px;
				color: #0a325b;
				line-height: 23px;
				.unit {
					font-family: MicrosoftYaHei;
					font-size: 12px;
					color: #294d79;
					line-height: 1;
					font-weight: normal;
				}
			}
			.r {
				font-family: MicrosoftYaHei;
				font-size: 14px;
				color: #7b96b1;
				line-height: 19px;
				.desc-img {
					width: 8px;
					height: 13px;
					margin-left: 5px;
				}
			}
			&:last-child {
				.l {
					.bg {
						background-image: url('~@/assets/images/home20240423/woman.png');
					}
				}
			}
		}
	}
}
#thisProvince,
#otherProvince,
#inProvince {
	width: 100%;
	height: calc(100% - 50px);
	position: relative;
	&::after {
		content: '';
		height: 112px;
		position: absolute;
		border-left: 1px dashed #a9bed5;
		top: 16%;
		left: 52%;
	}
}
.report-box {
	display: flex;
	justify-content: space-between;
	margin-top: 29px;
	height: 100px;
	.box-item {
		width: 50%;
		display: flex;
		align-items: center;
		.item-img {
			width: 74px;
			height: 65px;
			margin-right: 16px;
		}
		.desc-name {
			font-size: 14px;
			color: #454545;
			line-height: 19px;
			display: block;
			margin-bottom: 11px;
		}
		.desc-num {
			font-family: DINPro, DINPro;
			font-weight: bold;
			font-size: 30px;
			color: #0a325b;
			line-height: 38px;
		}
		.desc-unit {
			font-family: MicrosoftYaHei;
			font-size: 16px;
			color: #294d79;
			line-height: 21px;
			margin-left: 6px;
		}
	}
	.box-item + .box-item {
		border-left: 1px dashed #7596bb;
		padding-left: 23px;
	}
}
</style>
