<template>
    <div class="content">
        <es-data-table
            ref="table"
            row-key="id"
			:row-style="tableRowClassName"
            :data="tableData"
            full
            :thead="thead"
            :toolbar="toolbar"
            :url="dataTableUrl"
            :page="pageOption"
            :param="params"
			:border="true"
            close
            :response="func"
            @btnClick="btnClick"
			@submit="hadeSubmit"
            @search="hadeSearch"
            @reset="hadeReset" />
        <es-dialog
            title="查看"
            :visible.sync="visibleBasicInfo"
            :show-scale="false"
            size="full"
            height="100%"
        >
            <BasicInfo
                v-if="visibleBasicInfo"
                :id="formId"
                contents-key="academicPatent"
                title="查看"
                @visible="visibleBasicInfo = false"
            />
        </es-dialog>
    </div>
</template>
<script>
import paAcademicPatentApi from "@/http/achievement/paAcademicPatent";
import baseInfoApi from "@/http/achievement/baseInfo";
import BasicInfo from '@/views/scientific-sys/components/achievement-info/basic-info';
export default {
    name: "mmtradvuivsevspProjectSrWyenftrmyuzfeqh",
	components: { BasicInfo },
    data(){
        return {
            tableData: [],
            pageOption: {
                layout: 'total, sizes, prev, pager, next, jumper',
                pageSize: 20,
                position: 'center',
                current: 1,
                pageNum: 1
            },
            params: {
                orderBy: 't2.create_time',
                asc: 'false'
            },
            submitFilterParams: {},
            visibleBasicInfo: false,
            formId: '',
            showDelBtn: '0'
        }
    },
    computed: {
        toolbar() {
            return [
                {
                    type: 'button',
                    contents: [
                        {
                        text: '导出',
                        code: 'export',
                        type: 'primary'
                        }
                    ]
                },
                {
                    type: 'search',
                    reset: true,
                    contents: [
                        {
                            type: 'text',
                            name: 'keyword',
                            placeholder: '请输入关键字',
                            col: 3
                        }
                    ]
                },
				{
					type: 'filter',
					contents: [
						{
							type: 'year',
							label: '年度',
							name: 'year',
							placeholder: '请选择',
							col: 3
						},
						{
							type: 'text',
							label: '部门/二级学院',
							name: 'declareOrgName',
							placeholder: '请输入',
							col: 3
						},
						{
							type: 'text',
							label: '申报人',
							name: 'declarant',
							placeholder: '请输入',
							col: 3
						},
                        {
                            type: 'select',
                            label: '专利类型',
                            name: 'patentType',
                            placeholder: '请选择',
                            clearable: true,
                            sysCode: 'patent_type',
                            'value-key': 'cciValue',
                            'label-key': 'shortName',
                            col: 3
                        },
						{
							type: 'text',
							label: '科技处备案号',
							name: 'recordNum',
							placeholder: '请输入',
							col: 3
						},
						{
							type: 'text',
							label: '授权专利号',
							name: 'apNum',
							placeholder: '请输入',
							col: 3
						},
						{
							type: 'text',
							label: '代理机构',
							name: 'agency',
							placeholder: '请输入',
							col: 3
						},
						{
							type: 'text',
							label: '费用支出依托项目编号',
							name: 'projectNum',
							placeholder: '请输入',
							col: 3
						},
                        {
                            type: 'select',
                            label: '合作申报的排名',
                            name: 'caRank',
                            placeholder: '请选择',
                            clearable: true,
                            sysCode: 'patent_ca_rank',
                            'value-key': 'cciValue',
                            'label-key': 'shortName',
                            col: 3
                        },
                        {
                            type: 'select',
                            label: '是否授权',
                            name: 'isAuthorize',
                            placeholder: '请选择',
                            clearable: true,
                            sysCode: 'pa_common_yes_no',
                            'value-key': 'cciValue',
                            'label-key': 'shortName',
                            col: 3
                        }
					]
				}
            ]
        },
        thead() {
            return [
                {
                    title: '专利名称',
                    field: 'name',
                    align: 'center',
					showOverflowTooltip: true,
                    minWidth: 240
                },
                {
                    title: '申报人',
                    field: 'declarant',
                    align: 'center',
					showOverflowTooltip: true,
                    width: 100
                },
                {
                    title: '科技处备案号',
                    field: 'recordNum',
                    align: 'center',
					showOverflowTooltip: true,
                    width: 120
                },
                {
                    title: '专利类型',
                    field: 'patentTypeTxt',
                    align: 'center',
                    width: 90
                },
                {
                    title: '授权专利号',
                    field: 'apNum',
                    align: 'center',
					showOverflowTooltip: true,
                    width: 120
                },
                {
                    title: '代理机构',
                    field: 'agency',
                    align: 'center',
					showOverflowTooltip: true,
                    width: 140
                },
                {
                    title: '费用支出依托项目编号',
                    field: 'projectNum',
                    align: 'center',
					showOverflowTooltip: true,
                    width: 170
                },
                {
                    title: '合作申报的排名',
                    field: 'caRankTxt',
                    align: 'center',
                    width: 135
                },
                {
                    title: '是否授权',
                    field: 'isAuthorizeTxt',
                    align: 'center',
                    width: 80
                },
                {
                    title: '创建人',
                    field: 'createUserName',
                    align: 'center',
					showOverflowTooltip: true,
                    width: 100
                },
                {
                    title: '创建时间',
                    field: 'createTime',
                    align: 'center',
                    width: 160
                },
                {
                    title: '操作',
                    type: 'handle',
					fixed: 'right',
                    width: 100,
                    template: '',
                    events: [
                        {
                            code: 'view',
                            text: '查看'
                        },
                        {
                            code: 'del',
                            text: '删除',
                            rules: () => {
                                return this.showDelBtn === '1';
                            }
                        }
                    ]
                }
            ]
        },
        dataTableUrl() {
            return paAcademicPatentApi.authListJson
        }
    },
    mounted() {
        this.getAuth();
    },
    methods: {
		/**
		 * 表格行高
		 */
		tableRowClassName({ row, rowIndex }) {
			let styleRes = {
				height: '54px !important'
			};
			return styleRes;
		},
        getAuth() {
            this.$request({
                url: baseInfoApi.getDelAuth,
                method: 'POST'
            }).then(res => {
                this.showDelBtn = res?.results || '0';
            });
        },
        /**
         * headle按钮事件
         */
        async btnClick(res) {
            let code = res.handle.code;
            switch (code) {
                case 'view':
                    this.formId = res.row.id;
                    this.visibleBasicInfo = true;
                break;
                case 'del':
                    // 删除
                    this.onDeal(res.row.id);
                break;
                case 'export':
                    this.exportFile();
                break;
                default:
                break;
            }
        },
		func({ data }) {
            let _createTimeStart = null;
            let _createTimeEnd = null;
            if (data.createTimeList && data.createTimeList.length === 2) {
                _createTimeStart = `${data.createTimeList[0]} 00:00:00`;
                _createTimeEnd = `${data.createTimeList[1]} 23:59:59`;
            }
			return Object.assign({}, data, {
                createTimeStart: _createTimeStart,
                createTimeEnd: _createTimeEnd
            });
		},
		// 高级搜索确认
		hadeSubmit(e) {
			this.submitFilterParams = Object.assign({}, this.submitFilterParams, e.data);
		},
        hadeSearch(e) {
			this.submitFilterParams = Object.assign({}, this.submitFilterParams, {
                name: e.name
            });
        },
        hadeReset() {
            this.submitFilterParams = {};
        },
        /**
         * 删除
         */
        onDeal(id){
            this.$confirm(`您确定要删除数据吗？`, '提示', {
                confirmButtonText: '确定',
                cancelButtonText: '取消',
                type: 'warning'
            }).then(() => {
                const loading = this.$loading();
                this.$request({
                    url: baseInfoApi.authRemoveById,
                    data: { id },
                    method: 'POST'
                }).then(res => {
                    loading.close();
                    if (res.rCode == 0) {
                        this.$message.success('删除成功');
                        this.$refs.table.reload();
                    } else {
                        this.$message.error(res.msg);
                    }
                });
            }).catch(err => {});
        },
        //导出函数
        exportFile() {
            let isDev = process.env.NODE_ENV === 'development' ? '/api' : '';
            const paramAll = { ...this.submitFilterParams };
            const paramStr = this.objToUrlParams(paramAll);
            let url = `${isDev}${paAcademicPatentApi.exportData}${paramStr ? '?' + paramStr : ''}`;
            window.open(url);
        },
        objToUrlParams(obj) {
            return Object.keys(obj)
                .filter(key => obj[key] !== '' && obj[key] !== null)
                .map(key => encodeURIComponent(key) + '=' + encodeURIComponent(obj[key]))
                .join('&');
        }
    }
}
</script>

<style scoped lang="scss">
@import '../../../assets/style/style.scss';
.content {
    width: 100%;
    height: 100%;
}
</style>