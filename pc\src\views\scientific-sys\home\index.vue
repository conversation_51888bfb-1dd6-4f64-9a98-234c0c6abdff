<template>
	<div class="scientific">
		<!--  项目统计  -->
		<projectStatistics></projectStatistics>
		<!--  成果统计  -->
		<!-- <resultStatistics></resultStatistics> -->
	</div>
</template>

<script>
import projectStatistics from '@/views/scientific-sys/home/<USER>/project-statistics';
// import resultStatistics from '@/views/scientific-sys/home/<USER>/result-statistics';
export default {
	name: 'Index',
	components: {
		projectStatistics
		// resultStatistics
	}
};
</script>

<style scoped lang="scss">
.scientific {
	// padding: 12px;
	background: #eef0f3;
	height: 100%;
	overflow-y: scroll;
}
</style>
