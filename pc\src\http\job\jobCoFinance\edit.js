export default {
	data() {
		return {
			formData: {},
		};
	},
	computed: {
		formItemList() {
			return [
				{
					name: 'name',
					label: '机构名称',
					value: '',
					col: 12,
					rules: {
						required: true,
						message: '请输入机构名称'
					}
				},
				{
					label: '机构LOGO',
					type: 'attachment',
					code: 'job_co_finance_logo',
					ownId: this.formData.id, // 业务id
					height: 'auto',
					portrait: true,
					param: {
						isShowPath: true
					},
					col: 12
				},
				{
					name: 'contactWay',
					label: '联系方式',
					value: '',
					col: 12,
					rules: {
						required: true,
						message: '请输入联系方式'
					}
				},
				{
					name: 'status',
					label: '启用状态',
					value: '',
					col: 3,
					type: 'switch',
					rules: {
						required: true,
					},
					data: [
						{ value: 1, text: '启用'},
						{ value: 0, text: '禁用'}
					]
				},
			]
		}
	}
};
