<template>
	<div class="Main">
		<es-data-table
			:data="filteredTableData"
			:fit="true"
			:thead="thead"
			:toolbar="toolbar"
			size="mini"
			:border="true"
			page
			@btn-click="btnClick"
			:response="getSearch"
		></es-data-table>

		<el-dialog
			title="提示"
			:visible.sync="dialogVisible"
			:modal="false"
			width="50%"
			:before-close="handleClose"
		>
			<div class="FormBody">
				<es-form
					ref="form"
					:model="form"
					@submit="handleFormSubmit"
					:contents="formItemList"
					@reset="handleClose"
					label-position="top"
				></es-form>
			</div>
		</el-dialog>
	</div>
</template>

<script>
import { v4 as uuidv4 } from 'uuid';

export default {
	data() {
		return {
			dialogVisible: false,
			toolbar: [
				{
					type: 'button',
					length: 5,
					contents: [
						{
							text: '重置',
							type: 'primary'
						}
					]
				},
				{
					type: 'search',
					contents: [
						{
							type: 'text',
							label: '关键字',
							name: 'applicant',
							placeholder: '请输入关键字'
						}
					]
				}
			],
			thead: [
				{
					title: '申请人',
					field: 'applicant',
					align: 'center'
				},
				{
					title: '联系方式',
					field: 'phone',
					align: 'center'
				},
				{
					title: '单位名称',
					field: 'UnitName',
					align: 'center'
				},
				{
					title: '申请事由',
					field: 'Reason',
					align: 'center'
				},
				{
					title: '备注',
					field: 'remark',
					align: 'center'
				},
				{
					title: '附件',
					field: 'fileName',
					align: 'center'
				},
				{
					label: '状态',
					field: 'status',
					align: 'center'
				},
				{
					title: '操作',
					type: 'handle',
					events: [
						{
							text: '吊销'
						}
					]
				}
			],
			rowIndex: 0,
			search: null,
			tableData: [],
			formItemList: [
				{
					name: 'Reason',
					col: 6,
					type: 'textarea',
					placeholder: '请输入文字',
					label: '申请事由'
				},

				{
					name: 'remark',
					type: 'textarea',
					col: 6,
					placeholder: '请输入文字',
					label: '备注'
				}
			],
			form: {}
		};
	},
	computed: {
		filteredTableData() {
			if (!this.search) {
				return this.tableData; // 如果没有搜索词，则返回所有数据
			}
			return this.tableData.filter(item => {
				return item.applicant.toLowerCase().includes(this.search);
			});
		}
	},
	mounted(){
		this.getData()
	},
	methods: {

		getData() {
			let data = JSON.parse(window.sessionStorage.getItem('TableData'));
			this.tableData = data.filter(item => item.step === 3 || item.step === 4);
		},

		UpDateLocalData(formData) {
			formData.id = this.tableData[this.rowIndex].id
			let data = JSON.parse(window.sessionStorage.getItem('TableData'));
			data.forEach(element => {
				if (element.id === formData.id) {
					element.step = 4; // 修改对应元素的 step 属性
					element.status = '失效'
					element.Reason = formData.Reason;
					element.remark = formData.remark;
				}
			});
			window.sessionStorage.setItem('TableData', JSON.stringify(data));
			this.getData();
		},


		handleFormSubmit(data) {
			let { Reason,remark } = data;
            this.UpDateLocalData(data)

			this.$message.success('操作成功');
			this.form = {};
			this.dialogVisible = false;
		},
		handleClose() {
			this.form = {};
			this.dialogVisible = false;
		},

		getSearch({ type, data }) {
			let { applicant } = data;
			this.search = applicant;
		},

		btnClick(e) {
			console.log(e);
			this.rowIndex = e.$index;
			let { row, handle } = e;
			let { text } = handle;
			if (text === '吊销') {
				this.dialogVisible = true;
			}
			if (text == '重置') {
				this.search = null;
			}
		}
	}
};
</script>

<style scoped lang="scss">
.Main {
	width: 100%;
	height: 100%;
	padding: 10px 0px;
	.es-data-table {
		height: 100%;
	}
	.FormBody {
		height: 200px;
	}

}
</style>
