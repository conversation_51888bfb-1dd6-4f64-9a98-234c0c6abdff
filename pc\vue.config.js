const { defineConfig } = require('@vue/cli-service');
// const BundleAnalyzerPlugin = require('webpack-bundle-analyzer').BundleAnalyzerPlugin;
const pkg = require('./package.json');
const path = require('path');

function resolve(dir) {
	return path.join(__dirname, dir);
}
const port = process.env.port || 8080;
//开发时请修改moduleName的值，值应对应工程名称
module.exports = {
	...defineConfig({
		lintOnSave: false,
		publicPath: './',
		outputDir: 'dist-ybzy',
		assetsDir: 'static',
		productionSourceMap: false,
		//filenameHashing: false,
		devServer: {
			port: port, //端口号

			proxy: {
				'/api/apia': {
					target: 'http://10.44.4.157:9998', // 正式
					ws: true,
					changeOrigin: true, // 接口跨域 需打开这个参数
					pathRewrite: {
						'^/api/apia': ''
					}
				},
				'/lxx-api': {
					target: 'http://10.44.4.174:19820', // 正式
					ws: true,
					changeOrigin: true, // 接口跨域 需打开这个参数
					pathRewrite: {
						'^/lxx-api': ''
					}
				},

				'/api': {
					//跨域代理配置
					//  target: 'http://222.85.139.166:8099/',
					target: 'http://10.44.4.88:9999/', // 测试
					// target: 'http://10.44.4.160/', // 宜职院-正式
					// target: 'http://10.44.4.88:23104/', // 测试-泸州市纳溪
					// target: 'http://125.64.219.126:8118', // 6/18更换正式
					//target: 'http://127.0.0.1:18082/', //20号下拉最新代码
					// 本地 */
					// target: '192.168.0.53:18082',
					//  secure: false, // 如果是https接口，需要配置这个参数
					ws: true,
					changeOrigin: true, // 接口跨域 需打开这个参数
					pathRewrite: {
						'^/api': ''
					}
				},
				'/oa-api': {
					target: 'http://10.12.65.143/', // 正式
					ws: true,
					changeOrigin: true,
					pathRewrite: {
						'^/oa-api': '/api'
					}
				},
				'/rs-api': {
					target: 'http://192.168.0.232:11107/', // 正式
					ws: true,
					changeOrigin: true,
					pathRewrite: {
						'^/rs-api': ''
					}
				}
			}
			// headers: {
			// 	// 重点1: 允许跨域访问子应用页面
			// 	'Access-Control-Allow-Origin': '*'
			// }
		},
		transpileDependencies: [/node_modules/],
		chainWebpack: config => {
			//定义别名
			config.resolve.alias
				.set('@', resolve('src'))
				.set('@ast', resolve('src/assets'))
				.set('@cpt', resolve('src/components'));
			const entry = config.entry('app');
			entry.add('babel-polyfill').end();
			entry.add('classlist-polyfill').end();
			config.plugin('html').tap(args => {
				args[0].module = pkg.name;
				args[0].title = pkg.description;
				args[0].env = process.env.NODE_ENV !== 'development';
				return args;
			});
		},
		configureWebpack: config => {
			//调试JS
			config.devtool = config.mode === 'production' ? false : 'source-map';
			config.cache = {
				type: 'filesystem', //缓存类型
				buildDependencies: {
					//缓存依赖
					config: [__filename] //构建配置文件
				}
			};

			// config.devtool = 'source-map';
			if (process.env.NODE_ENV === 'development') {
				config.devtool = false; // 禁用 source map
			}
			// 开发环境
			// config.plugins.push(
			// 	new BundleAnalyzerPlugin({
			// 		analyzerMode: 'server',
			// 		analyzerHost: '127.0.0.1',
			// 		analyzerPort: 8088,
			// 		reportFilename: 'report.html',
			// 		defaultSizes: 'parsed',
			// 		openAnalyzer: true,
			// 		generateStatsFile: false,
			// 		statsFilename: 'state.json',
			// 		statsOptions: null,
			// 		logLevel: 'info'
			// 	})
			// );
		}
		// css: {
		// 	//查看CSS属于哪个css文件
		// 	sourceMap: true
		// }
	})
};
