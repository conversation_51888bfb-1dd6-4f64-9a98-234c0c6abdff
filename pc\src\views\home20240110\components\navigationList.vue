<template>
	<div
		v-if="taskList.length > 0 || loading"
		v-loading="loading"
		class="navigation-list"
		element-loading-background="rgba(0, 0, 0, 0.8)"
	>
		<div class="list">
			<div v-for="pro in taskList" :key="pro.id" class="item" @click="taskJump(pro)">
				<div class="floor1">{{ item.code == 'send' ? pro.endtime : pro.createtime }}</div>
				<div class="floor2 ellipsis-1">{{ pro.bname }}</div>
				<div class="floor3">
					<span class="info">{{ item.code == 'send' ? pro.pitemname : pro.pendtitle }}</span>
					<span v-if="item.code == 'send'" class="state">{{ pro.state }}</span>
				</div>
			</div>
		</div>
		<div class="pagination">
			<el-pagination
				background
				:current-page="currentPage"
				:page-sizes="[10, 20, 30, 40]"
				:page-size="size"
				layout="total, sizes, prev, pager, next, jumper"
				:total="total"
				small
				:pager-count="5"
				@size-change="handleSizeChange"
				@current-change="handleCurrentChange"
			></el-pagination>
		</div>
	</div>
	<div v-else class="empty-none">
		<Empty text="暂无事务" />
	</div>
</template>

<script>
import Empty from './empty.vue';
import { alumniUrl } from '@/config';
export default {
	name: 'NavigationList',
	components: {
		Empty
	},
	props: {
		item: {
			type: Object,
			default: () => {
				return {};
			}
		}
	},
	data() {
		return {
			loading: true,
			currentPage: 1,
			total: 0,
			size: 10,
			taskList: []
		};
	},
	created() {
		this.taskHandleClick();
	},
	// 办理后检查其他窗口关闭并更新
	mounted() {
		window.addEventListener('beforeunload', () => this.taskHandleClick());
	},
	beforeDestroy() {
		window.removeEventListener('beforeunload', () => this.taskHandleClick());
	},
	methods: {
		handleSizeChange(val) {
			this.currentPage = 1;
			this.size = val;
			this.taskHandleClick();
		},
		handleCurrentChange(val) {
			this.currentPage = val;
			this.taskHandleClick();
		},
		// 任务中心初始化
		taskHandleClick() {
			this.taskList = [];
			switch (this.item.code) {
				case 'todo':
					this.interfaceData('/oa/wfPending/wfPendingMerge/list_json.dhtml', true);
					break;
				case 'send':
					this.interfaceData('/oa/task/wfApplication/me_list_json.dhtml', false);
					break;
				default:
					break;
			}
		},
		//我的待办接口
		interfaceData(url, need) {
			this.loading = true;
			let data = {
				rows: this.size,
				page: this.currentPage,
				sord: 'desc'
			};
			if (need) {
				data.query_pendingattr = 0;
			} else {
				delete data.query_pendingattr;
			}
			this.$.ajax({
				url: url,
				data: data,
				method: 'POST'
			})
				.then(res => {
					this.total = res.totalrecords;
					this.$emit('total', this.total);
					this.loading = false;
					this.taskList = res.data || [];
					let name = 'first';
					window.parent.postMessage(name, '*');
				})
				.catch(error => {
					this.loading = false;
				});
		},
		// 请求接口数据
		queryList() {},
		// 任务待办跳转链接
		taskJump(e) {
			let url = e.pendingurl
				.replace(/\[recordid\]/g, e.apprecordid)
				.replace(/\[pendingId\]/g, e.id);
			window.open(alumniUrl + url);
		}
	}
};
</script>

<style lang="scss" scoped>
.navigation-list {
	.list {
		width: 100%;
		padding: 0 20px;
		min-height: 100px;
		max-height: calc(90vh - 156px);
		overflow: auto;
		margin-bottom: 10px;
		&::-webkit-scrollbar {
			width: 0px;
			height: 0px;
		}
		.item {
			width: 100%;
			height: 116px;
			background: rgba(0, 0, 0, 0.6);
			border-radius: 8px;
			font-family: MicrosoftYaHei;
			margin-bottom: 10px;
			padding: 16px 20px;
			.floor1 {
				height: 22px;
				font-size: 14px;
				color: #939393;
				line-height: 22px;
			}

			.floor2 {
				height: 24px;
				font-size: 16px;
				color: #ffffff;
				line-height: 24px;
				margin-top: 8px;
			}

			.floor3 {
				height: 22px;
				font-size: 14px;
				color: #636975;
				line-height: 22px;
				margin-top: 8px;
				display: flex;
				justify-content: space-between;
				.info {
					overflow: hidden;
					text-overflow: ellipsis;
					white-space: nowrap;
				}
				.state {
					height: 22px;
					font-size: 14px;
					color: #31c5ff;
					line-height: 22px;
					width: 50px;
					text-align: right;
					flex-shrink: 0;
				}
			}
			&:last-child {
				margin-bottom: 0;
			}
			&:hover {
				box-shadow: 0px 0px 8px 0px rgba(65, 160, 255, 0.4);
				border: 1px solid rgba(73, 165, 255, 0.5);
				cursor: pointer;
				.floor2 {
					font-size: 16px;
					font-family: MicrosoftYaHei, MicrosoftYaHei;
					font-weight: bold;
					color: #51a8ff;
				}
			}
		}
	}

	::v-deep(.pagination) {
		display: flex;
		justify-content: flex-end;
		padding-right: 19px;
		.el-pagination__total,
		.el-pagination__jump {
			font-size: 12px;
			color: #ffffff;
		}
		.el-input--mini .el-input__inner,
		.number,
		.btn-prev,
		.btn-next {
			height: 24px !important;
			line-height: 24px;
			font-weight: 400;
			font-size: 12px;
			color: #5f6165;
		}
		.el-pagination .el-select .el-input {
			width: 60px !important;
		}
	}
}
.empty-none {
	height: 600px;
	line-height: 600px;
}
</style>
