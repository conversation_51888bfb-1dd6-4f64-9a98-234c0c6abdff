<template>
	<div class="content">
		<es-data-table
			ref="table"
			:row-style="tableRowClassName"
			:full="true"
			:fit="true"
			:thead="thead"
			:toolbar="toolbar"
			:border="true"
			:page="pageOption"
			:url="dataTableUrl"
			:numbers="true"
			@btnClick="btnClick"
			@sort-change="sortChange"
			:param="params"
			@submit="hadeSubmit"
			:option-data="optionData"
			@edit="changeTable"
			close
			form
		></es-data-table>
		<es-dialog
			:title="formTitle"
			:visible.sync="showForm"
			width="60%"
			:close-on-click-modal="false"
			:destroy-on-close="true"
		>
			<es-form
				ref="form"
				:model="formData"
				:contents="formItemList"
				height="500px"
				:genre="2"
				collapse
				@change="inputChange"
				@submit="handleFormSubmit"
				@reset="showForm = false"
			/>
		</es-dialog>
		<es-dialog
			title="删除"
			:visible.sync="showDelete"
			width="20%"
			:close-on-click-modal="false"
			:middle="true"
			height="140px"
		>
			<div>确定要删除该条数据吗</div>
			<div class="btn-box">
				<div class="btn theme" @click="deleteRow">确定</div>
				<div class="btn" @click="showDelete = false">取消</div>
			</div>
		</es-dialog>
	</div>
</template>

<script>
import interfaceUrl from '@/http/platform/certstuffconfig.js';
import SnowflakeId from 'snowflake-id';
export default {
	data() {
		return {
			dataTableUrl: interfaceUrl.listJson,
			showForm: false,
			showDelete: false,
			formData: {},
			formTitle: '编辑',
			formConfigHide: true,//选项配置显示控制
			supportFormatHide: true,//支持格式显示控制
			optionData: {
				status: [
					{
						value: 1,
						name: '启用'
					},
					{
						value: 0,
						name: '禁用'
					}
				]
			},
			editBtn: {
				type: 'submit',
				skin: 'lay-form-btns',
				contents: [
					{
						type: 'primary',
						text: '确定',
						event: 'confirm'
					},
					{
						type: 'reset',
						text: '取消',
						event: 'cancel'
					}
				]
			},
			cancelBtn: {
				type: 'submit',
				skin: 'lay-form-btns',
				contents: [
					{
						type: 'reset',
						text: '取消',
						event: 'cancel'
					}
				]
			},
			toolbar: [
				{
					type: 'button',
					contents: [
						{
							text: '新增',
							code: 'add',
							type: 'primary'
						}
					]
				},
				{
					type: 'search',
					contents: [
						{
							type: 'text',
							name: 'keyword',
							placeholder: '关键字查询'
						}
					]
				}
			],
			thead: [
				{
					title: '资料名称',
					align: 'left',
					field: 'dataName',
					sortable: 'custom',
					showOverflowTooltip: true,
				},
				{
					title: '字段类型',
					align: 'left',
					field: 'dataType',
					sortable: 'custom',
					showOverflowTooltip: true,
				},
				{
					title: '支持格式',
					align: 'left',
					field: 'supportFormat',
					sortable: 'custom',
					showOverflowTooltip: true,
				},
				{
					title: '是否必填',
					width: "110px",
					align: 'center',
					field: 'isRequired',
					sortable: 'custom',
					showOverflowTooltip: true,
				},
				{
					title: '优先级',
					width: "100px",
					align: 'center',
					field: 'priority',
					sortable: 'custom',
					showOverflowTooltip: true,
				},
				{
					title: '状态',
					width: "100px",
					field: 'status',
					align: 'center',
					type: 'switch'
				},
				{
					title: '更新时间',
					align: 'center',
					field: 'updateTime',
					sortable: 'custom',
					showOverflowTooltip: true,
				},
				{
					title: '更新人',
					align: 'center',
					field: 'updateUserName',
					sortable: 'custom',
					showOverflowTooltip: true,
				},
				{
					title: '操作',
					type: 'handle',
					width: 180,
					template: '',
					events: [
						{
							code: 'edit',
							text: '编辑'
						},
						{
							code: 'view',
							text: '查看'
						},
						{
							code: 'delete',
							text: '删除'
						}
					]
				}
			],
			pageOption: {
				layout: 'total, prev, pager, next, jumper',
				pageSize: 10,
				// hideOnSinglePage: true,
				position: 'right',
				current: 1,
				pageNum: 1
			},
			params: {
				asc: 'false',
				orderBy: 'priority desc,createTime'
			}
		};
	},
	computed: {
		formItemList() {
			return [
				{
					label: '资料名称',
					name: 'dataName',
					placeholder: '请输入资料名称',
					event: 'multipled',
					rules: {
						required: true,
						message: '请输入资料名称',
						trigger: 'blur'
					},
					verify: 'required',
					col: 5
				},
				{
					label: '优先级',
					name: 'priority',
					placeholder: '请输入优先级',
					type: 'number',
					controls: false,
					rules: {
						required: true,
						message: '请输入优先级',
						trigger: 'blur'
					},
					verify: 'required',
					col: 5
				},
				{
					label: '字段类型',
					name: 'dataType',
					placeholder: '请选择字段类型',
					type: 'radio',
					rules: {
						required: true,
						message: '请选择字段类型',
						trigger: 'change'
					},
					verify: 'required',
					col: 10,
					sysCode: 'plat_bus_cert_stuff_config_data_type',
					'label-key': 'shortName',
					'value-key': 'cciValue'
				},
				{
					label: '是否必填',
					name: 'isRequired',
					placeholder: '请选择是否必填',
					type: 'radio',
					rules: {
						required: true,
						message: '请选择是否必填',
						trigger: 'change'
					},
					verify: 'required',
					col: 5,
					sysCode: 'yes_or_no',
					'label-key': 'shortName',
					'value-key': 'cciValue'
				},
				{
					type: 'switch',
					label: '状态',
					verify: 'required',
					name: 'status',
					placeholder: '',
					rules: {
						required: true,
						message: '请选择状态',
						trigger: 'change'
					},
					verify: 'required',
					col: 5,
					data: [
						{
							value: true,
							name: '启用'
						},
						{
							value: false,
							name: '禁用'
						}
					]
				},
				{
					label: '选项配置',
					name: 'formConfig',
					placeholder: '请输入选项配置,格式为{value: "xh", label: "学号", name: "学号"}',
					type: 'textarea',
					rules: {
						required: true,
						message: '请输入选项配置',
						trigger: 'blur'
					},
					hide: this.formConfigHide,
					verify: 'required',
					col: 10,
				},
				{
					label: '支持格式',
					name: 'supportFormat',
					type: 'checkbox',
					placeholder: '请选择支持格式',
					rules: {
						required: true,
						message: '请选择支持格式',
						trigger: 'change'
					},
					hide: this.supportFormatHide,
					verify: 'required',
					col: 5,
					sysCode: 'plat_bus_cert_stuff_config_support_format',
					'label-key': 'shortName',
					'value-key': 'cciValue',
					col: 10
				},
				{
					name: 'tips',
					label: '提示',
					placeholder: '请输入提示',
					type: 'textarea',
					col: 10
				},
				{
					name: 'remark',
					label: '备注',
					placeholder: '请输入备注',
					type: 'textarea',
					col: 10
				}
			];
		}
	},
	watch: {},
	created() {},
	mounted() {},
	methods: {
		/**
		 * 表格行高
		 */
		tableRowClassName({ row, rowIndex }) {
			let styleRes = {
				height: '54px !important'
			};
			return styleRes;
		},
		inputChange(key, value) {
			if (key == 'dataType') {
				this.changeDataType(value);
			}
		},
		hadeSubmit(data) {},
		/**
		 * 操作按钮点击事件
		 * @param {*} res
		 */
		btnClick(res) {
			let code = res.handle.code;
			switch (code) {
				case 'add':
					// 新增
					this.formTitle = '新增';
					this.editModule(this.formItemList);
					const snowflake = new SnowflakeId();
					this.formData = { id: snowflake.generate(), status: true, priority: 1 };
					this.showForm = true;
					break;
				case 'edit':
					// 编辑
					this.formTitle = '编辑';
					this.editModule(this.formItemList);
					this.$request({
						url: interfaceUrl.info + '/' + res.row.id,
						method: 'GET'
					}).then(res => {
						if (res.rCode == 0) {
							this.formData = res.results;
							this.formData.supportFormat = (undefined != this.formData && undefined != this.formData.supportFormat && this.formData.supportFormat.length > 0) ? this.formData.supportFormat.split(',') : [];
							this.formData.status = 1 == this.formData.status ? true : false;
							this.showForm = true;
							this.changeDataType(this.formData.dataType);
						}
					});
					break;
				case 'view':
					this.readModule(this.formItemList);
					this.formTitle = '查看';
					this.$request({
						url: interfaceUrl.info + '/' + res.row.id,
						method: 'GET'
					}).then(res => {
						if (res.rCode == 0) {
							this.formData = res.results;
							this.formData.supportFormat = (undefined != this.formData && undefined != this.formData.supportFormat && this.formData.supportFormat.length > 0) ? this.formData.supportFormat.split(',') : [];
							this.formData.status = 1 == this.formData.status ? true : false;
							this.showForm = true;
						}
					});
					break;
				case 'delete':
					this.deleteId = res.row.id;
					this.showDelete = true;
					break;
				default:
					break;
			}
		},
		handleClose() {},
		handleFormItemChange() {},
		handleFormSubmit(data) {
			let formData = data;
			delete formData.extMap;
			let url = '';
			if (this.formTitle == '新增') {
				url = interfaceUrl.save;
			} else {
				url = interfaceUrl.update;
			}
			if (undefined != formData && undefined != formData.supportFormat) {
				formData.supportFormat = formData.supportFormat.join(',');
			}
			formData.status = formData.status ? 1 : 0;
			this.$request({
				url: url,
				data: formData,
				method: 'POST'
			}).then(res => {
				if (res.rCode == 0) {
					this.$message.success('操作成功');
					this.showForm = false;
					this.formData = {};
					this.$refs.table.reload();
				} else {
					this.$message.error(res.msg);
				}
			});
		},
		deleteRow() {
			this.$request({
				url: interfaceUrl.deleteBatchIds,
				data: { ids: this.deleteId },
				method: 'POST'
			}).then(res => {
				if (res.rCode == 0) {
					this.$refs.table.reload();
					this.$message.success('删除成功');
				} else {
					this.$message.error(res.msg);
				}
				this.showDelete = false;
			});
		},
		/**
		 * 排序变化事件
		 * @param {*} column
		 * @param {*} prop
		 * @param {*} order
		 */
		sortChange(column, prop, order) {
			if (column.order == 'ascending') {
				//升序
				this.params = {
					asc: 'true',
					orderBy: column.prop
				};
			} else if (column.order == 'descending') {
				//降序
				this.params = {
					asc: 'false',
					orderBy: column.prop
				};
			} else {
				//不排序
				this.params = {
					asc: 'false',
					orderBy: 'priority desc,createTime'
				};
			}
			this.$refs.table.reload();
		},
		/**
		 * 编辑模式
		 */
		editModule(list) {
			for (var i in list) {
				var item = list[i];
				item.readonly = false;
			}
			list.push(this.editBtn);
		},
		/**
		 * 只读模式
		 */
		readModule(list) {
			for (var i in list) {
				var item = list[i];
				item.readonly = true;
			}
			list.push(this.cancelBtn);
		},
		changeTable(val) {
			if ('status' === val.name) {
				this.$request({
					url: interfaceUrl.update,
					data: {
						id: val.data.id,
						status: val.value
					},
					method: 'POST'
				}).then(res => {
					if (res.rCode == 0) {
						this.$message.success('操作成功');
					} else {
						this.$message.error(res.msg);
					}
				});
			}
		},
		changeDataType(val){
			if(val=="text"||val==""||val==null){
				this.formConfigHide = true;
				this.supportFormatHide = true;
				this.formData.supportFormat = [];
				this.formConfig = "";
			}else if(val=="file"){
				this.formConfigHide = true;
				this.supportFormatHide = false;
				this.formConfig = "";
			}else{
				this.formConfigHide = false;
				this.supportFormatHide = true;
				this.formData.supportFormat = [];
			}
		}
	}
};
</script>
<style scoped lang="scss">
@import '../../../assets/style/style.scss';

.content {
	display: flex;
	width: 100%;
	height: 100%;

	::v-deep .es-data-table {
		flex: 1;
		display: flex;
		flex-direction: column;

		.es-data-table-content {
			flex: 1;
			height: 0;
			display: flex;
			flex-direction: column;

			.el-table {
				flex: 1;
				// height: 100% !important;
			}

			.es-thead-border {
				.el-table__header {
					th {
						border-right: 1px solid #e1e1e1;
					}
				}
			}
		}
	}

	::v-deep .el-form-item__label {
		background: none;
		border: 0px solid #c2c2c2;
	}
}

.el-dialog__body {
	.btn-box {
		margin-top: 20px;
		display: flex;
		justify-content: flex-end;

		.btn {
			padding: 5px 10px;
			color: #666;
			border: 1px solid #eee;
			cursor: pointer;
			margin-right: 5px;
			border-radius: 4px;
			// &.theme {
			// 	background: $--color-primary;
			// 	color: #fff;
			// 	border-color: $--color-primary;
			// }
		}
	}
}
</style>
