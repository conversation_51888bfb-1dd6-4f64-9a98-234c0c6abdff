<template>
	<div class="map-location">
		<el-tabs v-model="activeName" @tab-click="handleClick">
			<el-tab-pane label="摆渡车查询" name="0"></el-tab-pane>
			<el-tab-pane label="停车位查询" name="1"></el-tab-pane>
		</el-tabs>
		<div class="map-location-container">
			<div id="mapContainer"></div>
			<!-- <input
				id="mapInput"
				v-model="searchValue"
				placeholder="请输入地名关键字"
				clearable
				class="map-location-container__search"
			/> -->
		</div>
		<div v-show="activeName === '1'" class="search-box">
			<el-select
				slot="prepend"
				v-model="carSelect"
				clearable
				placeholder="请选择片区"
				class="input-with"
			>
				<el-option label="区域1" value="0"></el-option>
				<el-option label="区域2" value="1"></el-option>
				<el-option label="区域3" value="2"></el-option>
			</el-select>
			<el-input v-model="carName" clearable placeholder="川ADK3651" class="input-with">
				<el-button slot="append" icon="el-icon-search" @click="onSearch"></el-button>
			</el-input>
		</div>
	</div>
</template>
<script>
import AMapLoader from '@amap/amap-jsapi-loader';
export default {
	name: 'BaseMapLocation',
	model: {
		prop: 'value',
		event: 'update:value'
	},
	props: {
		aMapKey: {
			type: String,
			default: 'f8fa64f4650dc818069bdc703034e10d'
		},
		value: {
			type: Array,
			default: () => []
		}
	},
	data() {
		return {
			activeName: '0',
			searchValue: '',
			carName: '',
			carSelect: '',
			map: null,
			AMap: null,
			geoCoder: null,
			markers: [],
			pointL: [], //当前位置
			points: [
				[104.939503, 28.831736],
				[104.945458, 28.833204]
			], //其他位置
			polygonPath: [
				[
					[104.939784, 28.8329],
					[104.940049, 28.833213],
					[104.940519, 28.833439],
					[104.941149, 28.833253],
					[104.941293, 28.832701],
					[104.940406, 28.832575]
				],
				[
					[104.943501, 28.830964],
					[104.943878, 28.831234],
					[104.944277, 28.831184],
					[104.944049, 28.830864],
					[104.943627, 28.830724]
				],
				[
					[104.942279, 28.83401],
					[104.942719, 28.83415],
					[104.943243, 28.834156],
					[104.943273, 28.833944],
					[104.942909, 28.833871],
					[104.942553, 28.833851],
					[104.942067, 28.833897]
				]
			],
			polygonPathSet: [],
			polygon: null,
			textMap: null,
			defaultModifiers: { negative: true, short: true, canEmpty: true },
			lng: 104.942348,
			lat: 28.831354,
			polyEditor: null
		};
	},

	watch: {
		lng(newVal) {
			if (this.AMap && newVal && this.lat) {
				this.pointL = [this.lng, this.lat];
				// 更新地图标记的点
				this.debounce(this.handleCreateMarker(), 200);
			}
			if (this.AMap && !newVal) {
				this.map.remove(this.markers || []);
				this.markers = [];
			}
		},
		lat(newVal) {
			if (this.AMap && newVal && this.lng) {
				this.pointL = [this.lng, this.lat];
				// 更新地图标记的点
				this.debounce(this.handleCreateMarker(), 0);
			}
			if (this.AMap && !newVal) {
				this.map.remove(this.markers || []);
				this.markers = [];
			}
		},
		searchValue(val) {
			if (this.AMap && val) {
				this.debounce(this.handleInitPlaceSearch(val), 500);
			}
		}
	},

	mounted() {
		this.initMap().then(AMap => {
			this.handleInitSearch(AMap);
			if (this.lng && this.lat) {
				this.pointL = [this.lng, this.lat];
				// 经纬度同时存在才渲染点
				// this.createPolygon();
				this.handleCreateMarker();
			}
		});
	},
	methods: {
		handleClick(e) {
			console.log(e);
			this.carSelect = '';
			this.carName = '';
			try {
				this.createPolygon();
				this.handleCreateMarker();
			} catch (error) {
				console.log(error, 'handleClick');
			}
		},
		onSearch() {
			if (!this.carName && !this.carSelect) {
				this.carName = '川ADK3651';
			}
			this.createPolygon();
		},
		debounce(fn, delay = 200) {
			let timer;
			return function () {
				const th = this;
				const args = arguments;
				if (timer) {
					clearTimeout(timer);
				}
				timer = setTimeout(function () {
					timer = null;
					fn.apply(th, args);
				}, delay);
			};
		},
		handleInitPlaceSearch(val) {
			const placeSearch = new this.AMap.PlaceSearch({
				pageSize: 5, // 单页显示结果条数
				pageIndex: 1, // 页码
				city: '0831', // 兴趣点城市
				citylimit: true, //是否强制限制在设置的城市内搜索
				map: this.map, // 展现结果的地图实例
				autoFitView: true // 是否自动调整地图视野使绘制的 Marker点都处于视口的可见范围
			});
			placeSearch.search(val); //关键字查询查询
		},
		// 初始化地图
		initMap() {
			return new Promise((resolve, reject) => {
				AMapLoader.load({
					key: this.aMapKey, // 申请好的Web端开发者Key，首次调用 load 时必填
					version: '2.0',
					plugins: ['AMap.Geocoder', 'AMap.AutoComplete', 'AMap.PlaceSearch'] // 需要使用的的插件列表，如比例尺'AMap.Scale'等
				})
					.then(AMap => {
						this.AMap = AMap;
						var position = new AMap.LngLat(104.942348, 28.831354);
						// 初始化地图
						this.map = new AMap.Map('mapContainer', {
							zoom: 10, //初始化地图级别
							center: position
						});
						this.map.setDefaultCursor('pointer');
						this.map.on('click', e => {
							// debugger;
							// 只更新数据
							this.form = {
								lng: e.lnglat.lng,
								lat: e.lnglat.lat
							};
							// this.lat = e.lnglat.lat;
							// this.lng = e.lnglat.lng;
							this.polygonPathSet.push([e.lnglat.lng, e.lnglat.lat]);
							console.log(this.polygonPathSet, 'this.polygonPathSet');
							this.searchValue = '';
						});
						resolve(AMap);
					})
					.catch(e => {
						reject(e);
					});
			});
		},
		// 注册搜索方法
		handleInitSearch(AMap) {
			//输入提示
			const autoSearch = new AMap.AutoComplete({
				input: 'mapInput'
			});
			//注册监听，当选中某条记录时会触发
			autoSearch.on('select', e => {
				// 只更新数据
				this.form = {
					lng: e.poi.location.lng,
					lat: e.poi.location.lat
				};
				this.lat = e.poi.location.lat;
				this.lng = e.poi.location.lng;
			});
		},
		// 创建点标记
		handleCreateMarker(lngLat) {
			// 每次触发标点事件，都先将之前的点移除掉
			if (this.markers) {
				this.markers.forEach(marker => {
					if (marker) {
						marker.setMap(null);
					}
				});
			}

			let pointArr = [];
			if (this.activeName === '1') {
				pointArr = [this.pointL];
			} else {
				pointArr = [this.pointL, ...this.points];
			}
			const markers = [];

			pointArr.forEach((e, i) => {
				const marker = new this.AMap.Marker({
					map: this.map,
					icon:
						i === 0
							? 'https://webapi.amap.com/theme/v1.3/markers/n/mark_b.png'
							: 'http://yszj.ybzy.cn/static/andyui2.0/css/picIcon/img/m-yjcl.png',
					position: e,
					offset: new this.AMap.Pixel(-12, -28),
					size: new this.AMap.Size(26, 31)
				});

				// 生成0~20随机数
				const num = Math.floor(Math.random() * 20);

				// 为标记添加点击事件
				marker.on('click', () => {
					// 使用 setTimeout 等待视口稳定
					setTimeout(() => {
						// 生成并设置标签
						const labelContent = `<div class='info'>0${i}号车 坐标:${e[0]},${e[1]} 速度:${num}</div>`;
						marker.setLabel({
							direction: 'top',
							offset: new this.AMap.Pixel(0, -10), // 设置文本标注偏移量
							content: labelContent // 设置文本标注内容
						});

						// 如果需要在再次点击时隐藏标签，可以添加额外的逻辑
						// 例如，可以在标记上存储一个标志来跟踪标签是否已经显示
						if (marker.showingLabel) {
							marker.setLabel(null); // 隐藏标签
							marker.showingLabel = false;
						} else {
							marker.showingLabel = true;
						}
						let lng = 104.942348;
						let lat = 28.831354;
						// 跟新中心点位置
						this.map.setCenter(new this.AMap.LngLat(lng + 0.00003, lat + 0.00003));
					}, 10); // 100毫秒通常是足够的，但你可以根据实际情况调整
				});

				markers.push(marker);
			});

			this.markers = markers;
			this.map.setFitView(this.markers);
			this.map.setCenter(lngLat);
		},
		// 创建多边形
		createPolygon() {
			if (!this.map) {
				console.error('地图对象未初始化');
				return;
			}

			// 先清空现有的多边形
			if (this.polygon) {
				this.map.remove(this.polygon);
				this.polygon = null; // 清空引用
				this.map.remove(this.textMap);
				this.textMap = null; // 清空引用
			}

			let carK = this.carSelect;
			switch (this.carName) {
				case '川ADK3651':
				case '川ADK3655':
					carK = 0;
					break;
				case '川ADK3652':
				case '川ADK3654':
					carK = 1;
					break;
				case '川ADK3653':
					carK = 3;
					break;
				default:
					break;
			}

			// 获取当前选择的多边形路径
			let polygonPath = this.polygonPath[Number(carK)];

			// 检查路径是否有效
			if (!polygonPath || polygonPath?.length < 3) {
				console.error('无效的多边形路径');
				return;
			}
			if (!this.carName && !this.carSelect) return;
			if (this.activeName === '0') {
				polygonPath = [116.396923, 39.918203];
			} else {
				polygonPath = this.polygonPath[Number(carK)];
			}

			// 创建新的多边形
			this.polygon = new this.AMap.Polygon({
				path: polygonPath,
				strokeColor: '#177ffc',
				strokeWeight: 2,
				strokeOpacity: 0.2,
				fillOpacity: 0.4,
				fillColor: '#1791fc',
				zIndex: 50,
				bubble: true
			});

			// 添加多边形到地图
			this.map.add([this.polygon]);

			// 创建纯文本标记
			this.textMap = new this.AMap.Text({
				text: `停车位${Number(this.carSelect) + 1} 车位情况:12/50`,
				anchor: 'center', // 设置文本标记锚点
				draggable: true,
				cursor: 'pointer',
				offset: new this.AMap.Pixel(60, -35),
				position: polygonPath[0] // 使用多边形路径的第一个点作为文本标记的位置
			});

			// 添加纯文本标记到地图
			this.map.add(this.textMap);

			// 设置中心位置稍微动一下就行
			this.map.setCenter([this.lng + 0.00002, this.lat + 0.00002]);

			// 缩放地图到合适的视野级别
			// this.map.setFitView();
		}
	}
};
</script>

<style lang="scss" scoped>
.map-location {
	position: relative;
	.el-row {
		display: flex;
		align-items: center;
		margin-bottom: 10px;
	}
	.el-col {
		display: flex;
		align-items: center;
		span {
			flex-shrink: 0;
		}
	}
	::v-deep .el-tabs__nav-scroll {
		padding: 0 30px;
	}
	.search-box {
		position: absolute;
		top: 50px;
		right: 10px;
		display: flex;
		.input-with {
			width: 200px;
		}
	}
	&-container {
		width: 100%;
		height: 100vh;
		position: relative;
		&__search {
			width: 300px;
			position: absolute;
			left: 10px;
			top: 10px;
			background-color: #fff;
			background-image: none;
			border-radius: 3px;
			border: 1px solid #d9d9d9;
			box-sizing: border-box;
			color: rgba(0, 0, 0, 0.75);
			display: inline-block;
			font-size: 13px;
			height: 32px;
			line-height: 32px;
			outline: 0;
			padding: 0 15px;
			-webkit-transition: border-color 0.2s cubic-bezier(0.645, 0.045, 0.355, 1);
			transition: border-color 0.2s cubic-bezier(0.645, 0.045, 0.355, 1);
		}
		::v-deep #mapContainer {
			margin-top: -15px;
			width: 100%;
			height: 100%;
			.amap-marker-label,
			.amap-overlay-text-container {
				background: #fff;
				min-width: 150px;
				border-radius: 4px;
				border: 1px solid #ebeef5;
				color: #606266;
				font-weight: bold;
				line-height: 1.4;
				padding: 5px 12px;
				text-align: justify;
				font-size: 12px;
				box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.2);
				word-break: break-all;
				// 箭头
				&::before {
					content: '';
					position: absolute;
					left: 50%;
					bottom: -6px;
					width: 0;
					height: 0;
					border-left: 6px solid transparent;
					border-right: 6px solid transparent;
					border-bottom: 6px solid #fff;
					margin-left: -6px;
					z-index: 10;
					transform: rotate(180deg);
				}
			}
		}
	}
}
</style>
