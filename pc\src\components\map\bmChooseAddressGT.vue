<!--百度地图  选取坐标点-->
<template>
	<el-dialog
		v-if="showMap"
		title="地图"
		:visible.sync="showMap"
		width="80%"
		:append-to-body="true"
		:close-on-click-modal="false"
		:close-on-press-escape="false"
		@close="closeMap"
		@open="openMap"
	>
		<el-form ref="pointLngLatForm" :model="pointLngLat" style="display: flex; flex-direction: row">
			<el-form-item prop="lng" label="经度" label-width="60px">
				<el-input v-model="pointLngLat.lng" size="mini" placeholder="经度"></el-input>
			</el-form-item>
			<el-form-item prop="lat" label="纬度" label-width="60px" style="margin-left: 10px">
				<el-input v-model="pointLngLat.lat" size="mini" placeholder="纬度"></el-input>
			</el-form-item>
			<el-form-item v-if="parseAddr" prop="address" label="" style="margin-left: 10px">
				<span>{{ address }}</span>
			</el-form-item>
			<el-form-item style="margin-left: 20px">
				<el-button size="mini" type="primary" @click="checkedAddress">确 定</el-button>
			</el-form-item>
		</el-form>

		<baidu-map
			v-loading="loadingMap"
			:center="center"
			:zoom="zoom"
			class="baiduMap"
			:scroll-wheel-zoom="true"
			@ready="handler"
			@click="onClick"
		>
			<bm-view style="width: 100%; height: 100%; flex: 1"></bm-view>
			<bm-local-search
				:keyword="addressKeyword"
				:auto-viewport="true"
				style="display: none"
			></bm-local-search>
			<bm-control>
				<bm-auto-complete v-model="addressKeyword" :sug-style="{ zIndex: 1 }">
					<el-input
						v-model="addressKeyword"
						style="margin-top: 10px"
						size="mini"
						placeholder="请输入地名关键字"
					></el-input>
				</bm-auto-complete>
			</bm-control>
		</baidu-map>
	</el-dialog>
</template>

<script>
export default {
	props: {
		showMap: {
			type: Boolean,
			default: false
		},
		gt: {
			//经纬度一起的 = 经度，纬度
			type: String,
			default: ''
		},
		parseAddr: {
			//是否解析经纬度解析地址，默认不解析
			type: Boolean,
			default: false
		},
		addr: {
			//经纬度解析地址
			type: String,
			default: null
		}
	},
	data() {
		return {
			// 地图相关
			loadingMap: false,
			BMap: '',
			map: '',
			showMap: false,
			addressKeyword: '',
			pointLngLat: {
				lng: '',
				lat: ''
			},
			address: null,
			center: {
				lng: 116.395645038,
				lat: 39.9299857781
			},
			zoom: 17
		};
	},
	watch: {
		// 经度变化监听
		'pointLngLat.lng'(val, oldVal) {
			if (val) {
				this.addPoint(val, this.pointLngLat.lat);
			}
		},
		// 纬度变化监听
		'pointLngLat.lat'(val, oldVal) {
			if (val) {
				this.addPoint(this.pointLngLat.lng, val);
			}
		}
	},
	methods: {
		// 地图初始化
		handler({ BMap, map }) {
			this.BMap = BMap;
			this.map = map;
			let gtArr = this.gt.split(',');
			this.pointLngLat.lng = gtArr[0];
			this.pointLngLat.lat = gtArr[1];
			this.address = this.addr;
		},
		// 添加点位
		addPoint(lng, lat) {
			let map = this.map;
			let BMap = this.BMap;
			map.clearOverlays();
			var point = new BMap.Point(lng, lat);
			let zoom = map.getZoom();
			setTimeout(() => {
				map.centerAndZoom(point, zoom);
			}, 0);
			var marker = new BMap.Marker(point); // 创建标注
			map.addOverlay(marker); // 将标注添加到地图中
		},
		// 点击地图 获取到经纬度，并解析经纬度地址
		onClick(e) {
			this.addressKeyword = '';
			this.pointLngLat = {
				lng: e.point.lng,
				lat: e.point.lat
			};
			if (!this.parseAddr) {
				return;
			}
			let geo = new BMap.Geocoder();
			let point = new BMap.Point(this.pointLngLat.lng, this.pointLngLat.lat);
			geo.getLocation(point, res => {
				this.address = res.address;
			});
		},
		// 确认选择地址，若需要经纬度解析地址，则回传
		checkedAddress() {
			if (!this.pointLngLat.lng || !this.pointLngLat.lat) {
				this.$message.warning('经纬度不能为空');
				return;
			}
			let point = this.pointLngLat.lng + ',' + this.pointLngLat.lat;
			this.$emit('checkedAddress', point);
			this.pointLngLat.lng = '';
			this.pointLngLat.lat = '';
			if (this.parseAddr) {
				this.$emit('checkedParseAddress', this.address);
			}
			this.address = null;
		},
		closeMap() {
			this.pointLngLat.lng = '';
			this.pointLngLat.lat = '';
			this.address = null;
			this.$emit('closeMapDialog');
		},
		openMap() {
			let gtArr = this.gt.split(',');
			this.pointLngLat.lng = gtArr[0];
			this.pointLngLat.lat = gtArr[1];
			this.address = this.addr;
		}
	}
};
</script>

<style scoped>
.input-with-select {
	background-color: #fff;
}

.baiduMap {
	width: 100%;
	height: 450px;
}

/* 深度作用选择器  */
.el-card /deep/ .el-card__body {
	padding: 0px;
}

.mapBox1 {
	line-height: 550px;
}

.map {
	opacity: 0.9;
	margin-right: 10px;
	height: 550px;
}

.button {
	z-index: 2;
	position: absolute;
	top: 30%;
	left: 88%;
}
</style>
