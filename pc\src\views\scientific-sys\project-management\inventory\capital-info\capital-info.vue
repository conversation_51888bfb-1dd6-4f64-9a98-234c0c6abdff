<!--
 @desc:资金信息 经费信息
 @author: WH
 @date: 2023/11/13
 -->
<template>
	<main>
		<title-card title="经费信息">
			<es-button type="primary" round @click="add" v-if="!readonly" :disabled="isDisabled">
				新增
			</es-button>
		</title-card>
		<div class="is-table">
			<es-data-table
				style="width: 100%"
				ref="table"
				:checkbox="checkbox"
				:thead="thead"
				:url="tableUrl"
				:param="param"
				:page="{ pageSize: 10 }"
				method="get"
				@btnClick="btnClick"
				@success="handleresults"
			></es-data-table>
		</div>
		<es-dialog
			:title="title"
			:visible.sync="visible"
			:drag="false"
			height="650px"
			width="700px"
			@close="reset"
		>
			<p class="hint"><span>多批次到账修改‘经费下达方式’需要当前列表中只有一条数据</span> <span class="hint-unit">经费单位：万元</span></p>
			<es-form
				v-if="visible"
				height="500px"
				:model="formData"
				:contents="formItemList"
				:readonly="formReadonly"
				@change="handleMessage"
				@submit="submit"
				@reset="reset"
			></es-form>
		</es-dialog>
	</main>
</template>

<script>
import {
	getCapitalInfoList,
	delCapitalInfoByid,
	updateCapitalInfoByid,
	createCapitalInfoByid,
	readCapitalInfoByid,
	getPorjectInfo,
	getAssortAmount
} from '@/api/scientific-sys.js';
import TitleCard from '@cpt/scientific-sys/title-card.vue';
import { v4 as uuidv4 } from 'uuid';
import { createNamespacedHelpers } from 'vuex';
const { mapState } = createNamespacedHelpers('getRoleMap');

export default {
	name: 'approvalList',
	components: { TitleCard },
	inject: ['id', 'openType', 'initActiveName'],
	data() {
		return {
			tableUrl: getCapitalInfoList,
			param: { projectId: this.id },
			dataChange: 0, //是否改变过数据 父级刷新列表用
			checkbox: false, //未上报开启选项框 提供多线批量催报功能
			formId: '',
			// 弹窗
			visible: false,
			// 当前id
			readonly: false,
			formReadonly: false,
			deliveryMethod: '', // 存储经费下达方式
			projectLevelTxt: '', // 存储当前项目级别
			recordsLength: 0, // 存储当前项目经费数据条数
			schoolAmount: 0, // 学校配套限额
			schoolRatio: 0, // 学校配套比例
			commission: 0.0326, // 税率
			projectClassify: 0, //存储当前项目类型： 0-纵向项目 1-横向项目 2-院级项目
			surplusFunds: 0, //已使用的学校配套经费
			isDisabled: false, //新增按钮是否禁用
			title: '',
			thead: [
				{
					title: '经费来源',
					field: 'source',
					align: 'center'
					// fixed: true
				},
				{
					title: '项目经费（万元）',
					field: 'projectExpenditure',
					align: 'right'
				},
				{
					title: '本次到账经费（万元）',
					field: 'arrivalExpenditure',
					align: 'center'
				},
				{
					title: '经费到账时间',
					field: 'arrivalTime',
					align: 'center',
					width: '150',
					render: (h, params) => {
						let isTime = params.row.arrivalTime;
						if (isTime) {
							isTime = isTime.substring(0, 10);
						}
						return h('span', {}, isTime);
					}
				},
				{
					title: '税费（万元）',
					field: 'taxation',
					align: 'right'
				},
				{
					title: '研究经费（万元）',
					field: 'studyExpenditure',
					align: 'right',
					disabled: true
				},
				{
					title: '学校配套经费（万元）',
					field: 'schoolAssortExpenditure',
					align: 'right',
					disabled: true
				},
				{
					title: '经费到账批次',
					field: 'arrivalBatch',
					align: 'right'
				}
			],
			formData: {
				taxation: '',
				studyExpenditure: '',
				schoolAssortExpenditure: '',
				arrivalExpenditure: '',
				deliveryMethod: '',
				projectLevelTxt: ''
			},
			formItemList: [
				{
					name: 'deliveryMethod',
					placeholder: '请选择',
					type: 'select',
					label: '经费下达方式',
					sysCode: 'project_delivery_method',
					disabled: false,
					rules: {
						required: true,
						message: '请选择经费下达方式',
						trigger: 'change'
					}
					// col: 6
				},
				{
					name: 'source',
					placeholder: '请输入',
					label: '经费来源',
					rules: {
						required: true,
						message: '请输入经费来源',
						trigger: 'blur'
					}
					// col: 6
				},
				{
					name: 'projectExpenditure',
					placeholder: '请输入',
					label: '项目经费',
					default:false,
					controls:false,
					type:'number',
					rules: {
						required: true,
						message: '请输入项目经费',
						trigger: 'blur'
					}
					// type: 'number',
					// controls: false
					// col: 6
				},
				{
					name: 'arrivalExpenditure',
					placeholder: '请输入',
					label: '本次到账经费',
					default:false,
					controls:false,
					type:'number',
					rules: {
						required: true,
						message: '请输入本次到账经费',
						trigger: 'blur'
					}
					// col: 6
				},
				{
					name: 'arrivalTime',
					placeholder: '请选择',
					label: '经费到账时间',
					type: 'datetime',
					unlinkPanels: true,
					rules: {
						required: true,
						message: '请选择经经费到账时间',
						trigger: 'blur'
					}
					// col: 6
				},
				{
					name: 'taxation',
					// placeholder: '请输入',
					label: '税费',
					default:false,
					controls:false,
					type:'number',
					// type: 'number',
					// controls: false,
					// rules: {
					// 	required: true,
					// 	message: '请输入税费',
					// 	trigger: 'blur'
					// },
					disabled: true
					// col: 6
				},
				{
					name: 'studyExpenditure',
					// placeholder: '请输入',
					label: '研究经费',
					default:false,
					controls:false,
					type:'number',
					// type: 'number',
					// controls: false,
					// rules: {
					// 	required: true,
					// 	message: '请输入研究经费',
					// 	trigger: 'blur'
					// },
					disabled: true
					// col: 6
				},
				{
					name: 'schoolAssortExpenditure',
					// placeholder: '请输入',
					label: '学校配套经费',
					default:false,
					controls:false,
					type:'number',
					// type: 'number',
					// controls: false,
					// rules: {
					// 	required: true,
					// 	message: '请输入学校配套经费',
					// 	trigger: 'blur'
					// },
					disabled: true
				},
				{
					label: '佐证材料',
					name: 'adjunctName',
					type: 'upload',
					code: 'transationform_editfile',
					ownId: '',
					// selectType: 'icon-plus',
					col: 12
					// this.formData.adjunctId = this.uploadId;
				}
			]
		};
	},
	created() {
		this.handleProjectInfo();
	},
	beforeDestroy() {
		if (this.dataChange != 0) {
			this.$bus.$emit('closeDialog', true);
		}
	},
	computed: {
		...mapState(['projectScienceManager'])
	},
	methods: {
		//增加操作
		addOperate() {
			//初次打开名字是CapitalInfo 才能够编辑经费信息和经费使用明细
			if (this.openType == 'look' || this.initActiveName == 'BasicInfo') {
				this.readonly = true;
			}
			if (!this.readonly) {
				this.thead.push({
					title: '操作',
					type: 'handle',
					template: '',
					fixed: 'right',
					events: [
						{
							text: '查看',
							btnType: 'look'
						},
						{
							text: '修改',
							btnType: 'edit'
						},
						{
							text: '删除',
							btnType: 'del'
						}
					]
				});
			} else {
				this.thead.push({
					title: '操作',
					type: 'handle',
					template: '',
					fixed: 'right',
					events: [
						{
							text: '查看',
							btnType: 'look'
						}
					]
				});
			}
		},
		// 获取表单数据成功回调
		async handleresults(e) {
			this.recordsLength = e.results.records.length;
			if (e.results.records.length == 0) {
				this.readonly = false;
				// this.formItemList[0].disabled = false;
			}
			if (e.results.records.length > 0) {
				if (e.results.records[0].deliveryMethodTxt == '一次性到账') {
					this.readonly = true;
					this.isDisabled = true;
					this.deliveryMethod = '0';
				}
				if (e.results.records[0].deliveryMethodTxt == '多批次到账') {
					this.readonly = false;
					// this.formItemList[0].disabled = true;
					this.formData.deliveryMethod = '1';
					this.deliveryMethod = '1';
				}
			}
		},
		// 捕获是为单批次操作还是多批次操作
		handleDeliveryMethod(v) {
			/* 当切换经费到账方式时，判断是否能切换
				1.暂无数据，两种方式都可选择
				2.有数据，从多批次切换为单批次时需判断数据是否超过一条，超过一条不予切换
			*/
			if (v == '0' && this.recordsLength !== 0) {
				this.$confirm(
					'‘多批次到账’ 切换为 ‘一次性到账’，需保证当前数据不能多于一条，否则切换失败',
					'是否切换?',
					{
						confirmButtonText: '确定',
						cancelButtonText: '取消',
						type: 'warning'
					}
				)
					.then(() => {
						if (this.recordsLength > 1) {
							this.formData.deliveryMethod = '1';
						} else {
							this.formData.deliveryMethod = v;
						}
					})
					.catch(() => {
						this.$message({
							type: 'info',
							message: '已取消'
						});
					});
			}
		},
		// 将百分数转换成小数
		toPoint(percent) {
			var str = percent.replace('%', '');
			str = str / 100;
			return str;
		},
		// 计算税费等数据
		handleMessage(e, v) {
			if (this.projectClassify == 2) return; //院级项目时，取消自动计算功能
			if (e == 'deliveryMethod') {
				this.handleDeliveryMethod(v);
			}
			// 输入本次到账经费后，自动获取税费，研究经费，学校配套经费
			if (e == 'arrivalExpenditure') {
				if (this.formData.deliveryMethod == '0') {
					///////// 一次性到账 /////////
					/* 税费计算：
						税费 = 本次到账经费 * 税率（commission） 
					*/
					this.formData.taxation = (v * this.commission).toFixed(4);
					/* 研究经费计算： 
						研究经费 = 本次到账经费-税费 
					*/
					this.formData.studyExpenditure = (v * (1 - this.commission)).toFixed(2);
					/* 学校配套经费计算：
						学校配套经费 = 本次到账金额（v） * 对应级别的学校配套经费比例（schoolRatio） 
						且如果配套经费（schoolAmount）大于对应级别限额时，显示为限额 
					*/
					this.formData.schoolAssortExpenditure =
						(v * this.schoolRatio).toFixed(2) > this.schoolAmount
							? this.schoolAmount
							: (v * this.schoolRatio).toFixed(2);
				} else {
					///////// 多批次到账 /////////
					/* 税费计算：
						税费 = 本次到账经费 * 税率（commission） 
					*/
					this.formData.taxation = (v * this.commission).toFixed(4);
					/* 研究经费计算： 
						研究经费 = 本次到账经费-税费 
					*/
					this.formData.studyExpenditure = (v * (1 - this.commission)).toFixed(2);
					/* 学校配套经费计算：
						学校配套经费 = 本次到账金额（v） * 对应级别的学校配套经费比例（schoolRatio） 
						且如果配套经费限额（schoolAmount）大于对应级别限额时，显示为限额 
						当前项目学校配套经费已使用金额：surplusFunds
					*/
					this.formData.schoolAssortExpenditure =
						(v * this.schoolRatio).toFixed(2) > this.schoolAmount - this.surplusFunds
							? this.schoolAmount - this.surplusFunds
							: (v * this.schoolRatio).toFixed(2);
				}
			}
		},
		// 纵向项目时新增项目级别节点
		addNode(results) {
			for (let i = this.formItemList.length; i > 0; i--) {
				this.$set(this.formItemList, i, this.formItemList[i - 1]);
			}
			this.$set(this.formItemList, 0, {
				name: 'projectLevelTxt',
				placeholder: '请选择',
				type: 'select',
				label: '项目级别',
				sysCode: 'project_level_other',
				rules: {
					required: true,
					message: '请选择经费下达方式',
					trigger: 'change'
				},
				disabled: true
			});
			this.projectLevelTxt = results.baseInfoMap.projectLevelTxt;
			this.formData.projectLevelTxt = this.projectLevelTxt; // 回显项目级别
		},
		// 查询当前项目配套经费剩余
		async getAssortAmount(id) {
			const loading = this.load('加载中...');
			try {
				let { rCode, msg, results } = await this.$.ajax({
					url: getAssortAmount,
					method: 'post',
					format: true,
					data: { projectId: id }
				});
				if (rCode == 0) {
					// 已使用的学校配套经费
					this.surplusFunds = results;
				} else {
					this.$message.error(msg);
				}
			} finally {
				loading.close();
			}
		},
		// 获取项目类型、项目的总经费等数据
		async handleProjectInfo() {
			const loading = this.load('加载中...');
			try {
				let { rCode, msg, results } = await this.$.ajax({
					url: getPorjectInfo,
					method: 'get',
					params: { id: this.id }
				});
				if (rCode == 0) {
					// 存储当前项目类型： 0-纵向项目 1-横向项目 2-院级项目
					this.projectClassify = results.baseInfoMap.projectClassify;
					console.log('>>>s', results.baseInfoMap.projectClassify);
					this.$emit('getObjectType', results.baseInfoMap.projectClassify);
					switch (results.baseInfoMap.projectClassify) {
						/*
							根据不同项目分类 选择不同学校配套限额和比例
						*/
						case 0: //纵向项目
							this.formItemList.splice(5, 1);
							this.addNode(results);
							let amount1 = results.projectExpenditureSystemParamDTO.lengthWayCountryAmount; //限额
							let ratio = results.projectExpenditureSystemParamDTO.lengthWaysCountryRatio; //比例
							switch (results.baseInfoMap.projectLevel) {
								case 0:
									// 学校配套限额最高不超过50万
									this.schoolAmount = amount1 < 50 ? amount1 : 50;
									// 学校配套比例
									this.schoolRatio = ratio;
									break;
								case 1:
									// 学校配套限额 最高不超过10万
									this.schoolAmount = amount1 < 10 ? amount1 : 10;
									// 学校配套比例
									this.schoolRatio = ratio;
									break;
								case 2:
									// 学校配套限额 最高不超过5万
									this.schoolAmount = amount1 < 5 ? amount1 : 5;
									// 学校配套比例
									this.schoolRatio = ratio;
									break;
								default:
									break;
							}

							break;
						case 1: //横向项目 同时判断当前是一次性还是多批次到账 多批次到账获取已配置经费
							let amount2 = results.projectExpenditureSystemParamDTO.crosswiseAmount; // 横向项目限额
							this.schoolAmount = amount2 < 10 ? amount2 : 10; // 最大不超过10万
							this.schoolRatio = this.toPoint(
								results.projectExpenditureSystemParamDTO.crosswiseRatio
							); // 横向项目的比例
							break;
						case 2: //院级项目 项目类型为院级项目时，删除学校配套经费字段
							this.formItemList.splice(5, 1);
							this.formItemList.splice(6, 1);
							this.formItemList[5].disabled = false;
							this.formItemList[6].disabled = false;
							break;
						default:
							console.log('暂无信息');
							break;
					}
				} else {
					this.$message.error(msg);
				}
			} finally {
				loading.close();
			}
		},
		//row=null  考虑到toolbar按钮不存在row
		btnClick({ handle, row = null }) {
			let { text, btnType } = handle;
			this.title = text;
			row && (this.formId = row.id);
			let btnTask = {
				look: () => {
					this.formReadonly = true;
					this.getInfo();
				},
				edit: () => {
					this.formReadonly = false;
					if (this.projectClassify == 0) {
						this.formItemList[1].disabled = false;
					} else {
						this.formItemList[0].disabled = false;
					}
					// this.formItemList[0].disabled = true;
					this.getInfo();
				},
				del: this.del
			};
			btnTask[btnType]();
		},

		add() {
			this.formData.deliveryMethod = this.deliveryMethod; //回显选择的经费下达方式
			if (this.recordsLength > 0) {
				if (this.projectClassify == 0) {
					this.formItemList[1].disabled = true;
				} else {
					this.formItemList[0].disabled = true;
				}
			}
			this.formData.projectLevelTxt = this.projectLevelTxt; // 回显项目级别
			// this.formItemList[0].disabled = true;
			// 查询当前项目配套经费剩余
			this.getAssortAmount(this.id);
			let uploadId = uuidv4();
			this.formItemList[this.formItemList.length - 1].ownId = uploadId;
			this.formData.adjunctId = uploadId;
			this.title = '新增经费';
			this.visible = true;
			this.formReadonly = false;
		},
		del() {
			this.$confirm('是否删除?', '提示', {
				confirmButtonText: '确定',
				cancelButtonText: '取消',
				type: 'warning'
			})
				.then(() => {
					this.delFn();
				})
				.catch(() => {
					this.$message({
						type: 'info',
						message: '已取消'
					});
				});
		},
		async delFn() {
			const loading = this.load('删除中...');
			try {
				let { rCode, msg } = await this.$.ajax({
					url: delCapitalInfoByid,
					method: 'post',
					data: { id: this.formId }
				});
				if (rCode == 0) {
					this.$message({
						type: 'success',
						message: msg
					});
					this.$refs.table.reload();
				} else {
					this.$message.error(msg);
				}
			} finally {
				loading.close();
			}
		},
		async getInfo() {
			const loading = this.load('加载中...');
			try {
				let { rCode, msg, results } = await this.$.ajax({
					url: readCapitalInfoByid,
					method: 'get',
					params: { id: this.formId }
				});
				if (rCode == 0) {
					this.formData = {
						...results,
						deliveryMethod: String(results.deliveryMethod)
					};
					this.formItemList[this.formItemList.length - 1].ownId = results.adjunctId;
					this.visible = true;
					// this.readonly = true;
					this.formData.projectLevelTxt = this.projectLevelTxt; // 回显项目级别
				} else {
					this.$message.error(msg);
				}
			} catch (error) {
				console.log('>>>error', error);
			} finally {
				loading.close();
			}
		},
		async submit() {
			const loading = this.load('提交中...');
			try {
				let formData = { ...this.formData };
				delete formData.adjunctName;
				let { rCode, msg, results } = await this.$.ajax({
					//有id代表修改 无代表新增
					url: formData.id ? updateCapitalInfoByid : createCapitalInfoByid,
					method: 'post',
					format: false,
					data: { ...formData, projectId: this.id } //projectId==最外层列表id 项目id
				});
				if (rCode == 0) {
					// 当修改为多批次的时 显示新增按钮
					if (this.formData.deliveryMethod == '1') {
						this.readonly = false;
						this.isDisabled = false;
					}
					this.dataChange += 1;
					this.$message.success(msg);
					this.$refs.table.reload();
					this.reset();
				} else {
					this.$message.error(msg);
				}
			} catch (error) {
				console.log('>>>error', error);
			} finally {
				loading.close();
			}
		},
		reset() {
			this.formData = {};
			this.visible = false;
		},
		// 页面加载
		load(text) {
			return this.$.loading(this.$loading, text);
		}
	},
	watch: {
		projectScienceManager: {
			immediate: true,
			handler(newVal) {
				// projectScienceManager：父级传递的身份标识 如果为科技处老师值为true 可进行修改删除操作，反之不可
				if (newVal === false) {
					this.readonly = true;
					console.log(111111111);
				}else{
					console.log(222222222);
				}
				if (typeof newVal == 'boolean') {
					this.addOperate();
				}
			}
		}
	}
};
</script>
<style lang="scss" scoped>
@import '@ast/style/public.scss';
main {
	width: 100%;
	height: 100%;
	.is-table {
		height: calc(100% - 80px);
	}
}
.hint {
	color: red !important;
	padding: 6px 10px;
	display: flex;
	justify-content: space-between;
	.hint-unit{
		color: #111;
		
	}
}
</style>
