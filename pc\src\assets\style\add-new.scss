.el-dialog {
    .es-form-button {
        // 专家捐赠者新增界面按钮居右，故屏蔽
        // text-align: right;
        padding-left: 10px;
        padding-right: 10px;
    }

    .el-dialog__header {
        background-color: #0076E9;

        .el-dialog__title {
            color: #fff;
        }

        .el-dialog__close {
            color: #fff;

            &:hover {
                color: #828483;
            }
        }

    }
}

label[for="responsibility"],
label[for="institutionFinish"],
label[for="lossSaveSituation"] {
    justify-content: center;
    width: 100% !important;
    margin-bottom: -23px;
    border-right: 1px solid #dcdfe6;
}

label[for="responsibility"]+.el-form-item__content,
label[for="institutionFinish"]+.el-form-item__content,
label[for="lossSaveSituation"]+.el-form-item__content {
    display: none;
}

.btns-wrapper {
    display: flex;
    justify-content: center;
    padding: 10px 0;

    .ball-wrapper {
        cursor: pointer;
        text-align: center;
        margin: 0 30px;
    }

    .ball {
        width: 80px;
        height: 80px;
        background-color: #dcdfe6;
        border-radius: 50%;
        margin-bottom: 20px;
        box-shadow: 0px 5px 14px 0px rgba(220, 223, 230, 0.2);

        &.active {
            background-color: #009DD9;
            box-shadow: 0px 5px 14px 0px rgba(0, 157, 217, 0.2);
        }
    }
}

.es-selector-box {
    height: 40px;
}

// 弹框表单样式

// .el-form-item__label.es-justify-end {
//     justify-content: center;
//     border: 1px solid #dcdfe6;
//     border-right: none;
// }

// .es-justify--center {
//     justify-content: center;
// }

// .es-form div.es-input__inner,
// .el-input__inner {
//     border-radius: 0;
// }

// .el-form-item__label {
//     background-color: #fafafa;
// }

// .el-tab-pane .el-form-item__label {
//     background: none;
// }

// .el-textarea__inner {
//     height: 40px;
//     border-radius: 0;
// }