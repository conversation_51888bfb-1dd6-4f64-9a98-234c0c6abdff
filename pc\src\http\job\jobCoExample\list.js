import httpApi from '@/http/job/jobCoExample/api.js';
export default {
	computed: {
		component() {
			return {
				// 纯列表结构
				name: 'es-data-table'
			};
		},
		treeParam() {
			return {
				
			};
		},
		table() {
			return {
				url: httpApi.jobCoExampleList,
				param: { handlerType: '' },
				full: true,
				fit: true,
				page: true,
				stripe: true,
				numbers: true,
				checkbox: true,
				'row-key': 'id',
				toolbar: [
					{
						type: 'button',
						contents: [
							{
								text: '新增',
								code: 'toolbar',
							},
							{
								text: '删除',
								type: 'danger',
								code: 'toolbar',
							},
							{
								text: '查看',
								type: 'primary',
								code: 'toolbar',
							},
						]
					},
					{
						type: 'search',
						reset: true,
						contents: [
							//{
							//	name: 'accountName_like',
							//	placeholder: '配置名称',
							//	label: '配置名称',
							//	col: 6
							//},
							{
								col: 6,
								name: 'exampleName',
								label: '案例名称',
								placeholder: '案例名称'
							},
							{
								type: 'date',
								col: 6,
								name: 'startDate',
								label: '发布时间(开始)',
								placeholder: '发布时间(开始)'
							},
							{
								type: 'date',
								col: 6,
								name: 'endDate',
								label: '发布时间(结束)',
								placeholder: '发布时间(结束)'
							}
						]
					}
				],
				thead: [
					{
						title: '案例名称',
						align: 'left',
						field: 'exampleName'
					},
					{
						title: '发布人',
						align: 'center',
						field: 'createUser'
					},
					{
						title: '发布时间',
						align: 'center',
						field: 'createTime'
					},
					{
						title: '启用状态',
						align: 'center',
						field: 'statusVO',
						width: '100',
						render: (h, params) => {
							return h(
								'el-tag',
								{props:{type: params.row.statusVO === '启用'?'':'danger'}},
								params.row.statusVO)
						}
					},
					{
						title: '操作',
						type: 'handle',
						width: 180,
						events: [
							{
								text: '查看',
								code: 'row',
							},
							{
								text: '编辑',
								code: 'row',
							},
							{
								text: '删除',
								code: 'row',
							}
						]
					}
				]
			};
		}
	}
};
