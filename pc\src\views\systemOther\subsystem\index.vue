<template>
	<iframe :src="url" frameborder="0" class="iframe"></iframe>
</template>

<script>
import { checkLogin } from '@/api/home.js';

export default {
	data() {
		return {
			url: '',
			redirect_uri: ''
		};
	},
	mounted() {
		const query = this.$route.query;
		this.redirect_uri = decodeURIComponent(query.redirect_uri);
		this.getCode();
	},
	methods: {
		getCode() {
			const query = this.$route.query;
			// 将http://**********:18000替换成http://localhost:8089
			//http:yszj.ybzy.cn/ybzy/platauth/loginCheck?appId=ZNh24WN11samZ05TiMG&clientType=pc
			// 接口获取code单点认证
			if (query.type === 'code') {
				this.$.ajax({
					url: checkLogin,
					data: {
						appId: 'ZNh24WN11samZ05TiMG',
						clientType: 'pc'
					}
				}).then(res => {
					let code = res.results.code;
					this.handelUrl(code);
				});
				return;
			}

			// 重定向单点认证
			if (query.type === 'external' || query.appid) {
				const origin = window.location.origin;
				//yszj.ybzy.cn/project-ybzy/auth_page/views/authmanage.html?redirect_uri=http://**********:18000/task/taskBoard&appid=ZNh24WN11samZ05TiMG&clientType=pc
				this.redirect_uri = query.appid
					? `${origin}/project-ybzy/auth_page/views/authmanage.html?redirect_uri=${encodeURIComponent(
							origin + this.redirect_uri
					  )}&appid=${query.appid}&clientType=${query.clientType}`
					: `${origin}/project-ybzy/auth_page/views/authmanage.html?redirect_uri=${this.redirect_uri}`;
			}
			this.handelUrl();
		},
		// 处理url
		handelUrl(code) {
			let url = code ? `${this.redirect_uri}?code=${code}` : this.redirect_uri;
			// console.log(url, 'subsystem url');
			this.url = url;
		}
	}
};
</script>
<style lang="scss" scoped>
.iframe {
	width: 100%;
	height: 100%;
}
</style>
