<template>
	<div class="bodyBox">
		<div class="statsBox">
			<div class="statsItemBox" style="background-color: #65D8FF;">
				<!-- <el-avatar
					shape="square"
					:size="50"
					src="https://cube.elemecdn.com/9/c2/f0ee8a3c7c9638a54940382568c9dpng.png"
				/> -->
				<div>
					<h3>入驻企业</h3>
					<span>{{ enterpriseNum }}家</span>
				</div>
			</div>
			<div class="statsItemBox" style="background-color: #FF6B0C;">
				<div>
					<h3>实习网点</h3>
					<span>{{ baseInfo.practiceNum }}个</span>
				</div>
			</div>
			<div class="statsItemBox" style="background-color: #F7C162;">
				<div>
					<h3>岗位数</h3>
					<span>{{ baseInfo.postNum }}个</span>
				</div>
			</div>
			<div class="statsItemBox" style="background-color: #C6ECB1;">
				<div>
					<h3>简历数</h3>
					<span>{{ baseInfo.resumeNum }}份</span>
				</div>
			</div>
			<div class="statsItemBox" style="background-color: #C0A1FF;">
				<div>
					<h3>面试邀请</h3>
					<span>{{ baseInfo.interview }}份</span>
				</div>
			</div>
		</div>

		<div>
			<div class="echartsTitle">
				<h3>服务信息</h3>
				<el-select
					v-model="serviceYear"
					clearable
					placeholder="指定年份"
					size="mini"
					@change="servicePramsChange"
				>
					<el-option
						v-for="item in serviceYearOptions"
						:key="item.value"
						:label="item.label"
						:value="item.value"
					></el-option>
				</el-select>
				<el-select
					v-model="serviceMonth"
					clearable
					placeholder="指定月份"
					size="mini"
					style="margin-left: 10px"
					@change="servicePramsChange"
				>
					<el-option
						v-for="item in serviceMonthOptions"
						:key="item.value"
						:label="item.label"
						:value="item.value"
					></el-option>
				</el-select>
			</div>
			<div id="serviceStatsECharts"></div>
		</div>

		<div class="echarts1Box">
			<div class="authEChartsBox">
				<h3>企业规模占比</h3>
				<div id="authECharts"></div>
			</div>
			<div class="jobPostEChartsBox">
				<div class="echartsTitle">
					<h3>每月岗位动态信息</h3>
					<el-select
						v-model="jobPostYear"
						placeholder="请选择"
						size="mini"
						@change="jobPostYearChange"
					>
						<el-option
							v-for="item in jobPostYearOptions"
							:key="item.value"
							:label="item.label"
							:value="item.value"
						></el-option>
					</el-select>
				</div>
				<div id="jobPostECharts"></div>
			</div>
		</div>
	</div>
</template>

<script>
import interfaceUrl from '@/http/job/jobHome/stats.js';
import * as echarts from 'echarts';

const serviceStatsOptions = {
	title: {
		// text: '社区活跃度'
	},
	tooltip: {
		trigger: 'axis'
	},
	legend: {
		data: [], //各数据组名称
		show: true,
		left: '85%'
	},
	grid: {
		left: '3%',
		right: '4%',
		bottom: '3%',
		containLabel: true
	},

	xAxis: {
		type: 'category',
		axisTick: {
			alignWithLabel: true
		},
		data: ['类型1', '类型2', '类型3', '类型4', '类型5', '类型6', '类型7']
	},
	yAxis: {
		type: 'value'
	},
	series: {
		name: '服务内容数',
		type: 'bar',
		barWidth: '40%',
		data: [23, 4, 345, 88, 156, 38, 9],
		itemStyle: {
			color: '#41a0f9',
			borderColor: '#ddeef6'
		}
	}
};

const corpScaleEchartsOptions = {
	title: {
		// text: '认证方式占比'
	},
	tooltip: {
		trigger: 'item',
		formatter: '{a} <br/>{b} : {c} ({d}%)'
	},
	legend: {
		data: ['大型企业', '中型企业', '小型企业', '微型企业'],
		top: 'center',
		show: true,
		left: '80%'
	},
	series: {
		name: '企业规模占比',
		type: 'pie',
		label: {
			show: false
		},
		emphasis: {
			label: {
				show: true
			}
		},
		radius: [20, 70],
		center: ['40%', '50%'],
		roseType: 'area',
		itemStyle: {
			borderRadius: 2
		},
		data: [
			{ value: 'big_enp', name: '大型企业' },
			{ value: 'middle_enp', name: '中型企业' },
			{ value: 'small_enp', name: '小型企业' },
			{ value: 'mini_enp', name: '微型企业' }
		]
	},
	color: ['#03E8FA', '#55BBFF', '#15ABFF', '#39BDFF']
};

const jobPostEChartsOptions = {
	title: {
		// text: 'Stacked Line'
	},
	tooltip: {
		trigger: 'axis'
	},
	grid: {
		left: '3%',
		right: '4%',
		bottom: '3%',
		containLabel: true
	},
	legend: {
		data: ['岗位', '投递', '面邀'],
		left: '70%'
	},
	xAxis: {
		type: 'category',
		boundaryGap: false,
		// axisTick: {
		//   alignWithLabel: true
		// },
		data: ['1月', '2月', '3月', '4月', '5月', '6月', '7月', '8月', '9月', '10月', '11月', '12月']
	},
	yAxis: {
		type: 'value'
	},
	series: [
		{
			name: '岗位',
			type: 'line',
			stack: 'Total',
			data: [120, 132, 101, 134, 90, 230, 210,0,0,0,0,0]
		},
		{
			name: '投递',
			type: 'line',
			stack: 'Total',
			data: [220, 182, 191, 234, 290, 330, 310,0,0,0,0,0]
		},
		{
			name: '面邀',
			type: 'line',
			stack: 'Total',
			data: [150, 232, 201, 154, 190, 330, 410,0,0,0,0,0]
		}
	]
};

export default {
	data() {
		return {
			baseInfo: {},
			enterpriseNum: 0,
			serviceCharts: null,
			authCharts: null,
			jobPostCharts: null,
			serviceChartsData: null,
			authChartsData: null,
			jobPostChartsData: null,

			serviceMonth: null,
			serviceYear: null,
			serviceMonthOptions: [
				{ label: '1月', value: 1 },
				{ label: '2月', value: 2 },
				{ label: '3月', value: 3 },
				{ label: '4月', value: 4 },
				{ label: '5月', value: 5 },
				{ label: '6月', value: 6 },
				{ label: '7月', value: 7 },
				{ label: '8月', value: 8 },
				{ label: '9月', value: 9 },
				{ label: '10月', value: 10 },
				{ label: '11月', value: 11 },
				{ label: '12月', value: 12 }
			],
			serviceYearOptions: [],
			jobPostYear: null,
			jobPostYearOptions: []
		};
	},
	created() {
		this.loadjobPostYears();
		this.loadServiceYears();
		this.$nextTick(() => {
			this.init();
		});
	},
	mounted() {
		this.$nextTick(() => {
			window.onresize = () => {
				this.reloadECharts();
			};
		});
	},
	methods: {
		loadjobPostYears() {
			let curDate = new Date();
			let curYear = curDate.getFullYear();
			this.jobPostYear = curYear;
			this.jobPostYearOptions = [];
			let temp = [];
			for (let i = curYear; i >= 2005; i--) {
				temp.push({
					label: i + '年',
					value: i
				});
			}
			this.jobPostYearOptions = temp;
		},
		loadServiceYears() {
			let curDate = new Date();
			let curYear = curDate.getFullYear();
			// this.serviceYear = curYear;
			this.serviceYearOptions = [];
			let temp = [];
			for (let i = curYear; i >= 2005; i--) {
				temp.push({
					label: i + '年',
					value: i
				});
			}
			this.serviceYearOptions = temp;
		},
		init() {
			this.loadServiceStatsECharts();
			this.loadAuthECharts();
			this.loadJobPostECharts();
		},
		reloadECharts() {
			this.reloadServiceStatsECharts();
			this.reloadAuthECharts();
			this.reloadJobPostECharts();
		},

		servicePramsChange() {
			this.loadServiceStatsECharts();
		},

		jobPostYearChange(e) {
			this.loadJobPostECharts();
		},

		createServiceStatsECharts(x, y) {
			if (this.serviceCharts) {
				this.serviceCharts.dispose();
			}
			let chartDom = document.getElementById('serviceStatsECharts');
			if (!chartDom) {
				return;
			}
			this.serviceCharts = echarts.init(chartDom);
			let options = serviceStatsOptions;
			options.xAxis.data = x;
			options.series.data = y;
			this.serviceCharts.setOption(options);
		},
		loadServiceStatsECharts() {
			let params = {
				month: this.serviceMonth,
				year: this.serviceYear
			};
			this.$request({ url: interfaceUrl.serviceStats, params, method: 'GET' }).then(res => {
				if (res.rCode !== 0) {
					return;
				}
				let serviceTypeName = res.results.typeNames;
				let serviceNum = res.results.typeNums;
				this.createServiceStatsECharts(serviceTypeName, serviceNum);
				this.serviceChartsData = {
					serviceTypeName: serviceTypeName,
					serviceNum: serviceNum
				};
			});
		},
		reloadServiceStatsECharts() {
			this.createServiceStatsECharts(
				this.serviceChartsData.serviceTypeName,
				this.serviceChartsData.serviceNum
			);
		},

		createAuthECharts(data) {
			if (this.authCharts) {
				this.authCharts.dispose();
			}
			let chartDom = document.getElementById('authECharts');
			if (!chartDom) {
				return;
			}
			this.authCharts = echarts.init(chartDom);
			let options = corpScaleEchartsOptions;
			options.series.data = data;
			this.authCharts.setOption(options);
		},
		loadAuthECharts() {
			this.$request({ url: interfaceUrl.numStats, method: 'GET' }).then(res => {
				if (res.rCode !== 0) {
					return;
				}
				let resData = res.results.corpScaleCount;
				let data = [];
				for (let key in resData) {
					let name = this.getCorpScaleDes(key);
					data.push({
						name: name,
						value: resData[key]
					});
				}
				this.createAuthECharts(data);
				this.authChartsData = data;
				//填充基本数据
				// this.enterpriseNum = res.results.enterpriseNum;
				// this.baseInfo.practiceNum = res.results.practiceNum;
				// this.baseInfo.postNum = res.results.postNum;
				// this.baseInfo.resumeNum = res.results.resumeNum;
				// this.baseInfo.interview = res.results.interview;
				this.enterpriseNum = res.results.enterpriseNum;
				this.baseInfo.practiceNum = res.results.practiceNum;
				this.baseInfo.postNum = 14;
				this.baseInfo.resumeNum = 150;
				this.baseInfo.interview = 140;

			});
		},
		getCorpScaleDes(code) {
			let des = '';
			switch (code) {
				case 'big_enp':
					des = '大型企业';
					break;
				case 'middle_enp':
					des = '中型企业';
					break;
				case 'small_enp':
					des = '小型企业';
					break;
				case 'mini_enp':
					des = '微型企业';
					break;
			}
			return des;
		},
		reloadAuthECharts() {
			this.createAuthECharts(this.authChartsData);
		},

		createJobPostECharts(params) {
			if (this.jobPostCharts) {
				this.jobPostCharts.dispose();
			}
			let chartDom = document.getElementById('jobPostECharts');
			if (!chartDom) {
				return;
			}
			this.jobPostCharts = echarts.init(chartDom);
			let options = jobPostEChartsOptions;
			options.series[0].data = params.postNum;
			options.series[1].data = params.postResumeNum;
			options.series[2].data = params.interviewNum;
			this.jobPostCharts.setOption(options);
		},
		loadJobPostECharts() {
			let params = {
				year: this.jobPostYear
			};
			this.$request({ url: interfaceUrl.postStats, params, method: 'GET' }).then(res => {
				if (res.rCode !== 0) {
					return;
				}
				let monthVal = res.results;
				this.createJobPostECharts(monthVal);
				this.jobPostChartsData = monthVal;
			});
		},
		reloadJobPostECharts() {
			this.createJobPostECharts(this.jobPostChartsData);
		}
	}
};
</script>

<style scoped lang="scss">
.bodyBox {
	width: 100%;
	height: 100%;
	padding: 10px;
	overflow: auto;
}
.bodyBox > div {
	padding-top: 10px;
	padding-bottom: 10px;
	border-bottom: 1px solid grey;
}

.bodyBox > div:nth-child(3) {
	border-bottom: none;
}

.statsBox {
	display: flex;
	flex-direction: row;
	justify-content: space-around;
}
.statsItemBox {
	width: 19%;
	height: 120px;
	// border: 0px solid grey;
	border-radius: 10px;
	padding: 20px;
	display: flex;
	color: rgb(255, 255, 255);
	flex-direction: row;
}
.statsItemBox > div {
	margin-left: 20px;
	h3 {
		margin-bottom: 10px;
		font-size: 26px;
	}
}

.statsItemBox span {
	// margin-left: 20px;
	font-size: 18px;
}

#serviceStatsECharts {
	width: 100%;
	height: 260px;
}

.echartsTitle {
	display: flex;
	flex-direction: row;
	align-items: center;
}
.echartsTitle > h3 {
	margin-right: 10px;
}

.echarts1Box {
	display: flex;
	flex-direction: row;
	justify-content: space-between;
}
.authEChartsBox {
	width: 35%;
	height: 300px;
	border-right: 1px solid grey;
}
.jobPostEChartsBox {
	width: 64%;
	height: 300px;
}
#authECharts {
	width: 100%;
	height: 260px;
}
#jobPostECharts {
	width: 100%;
	height: 260px;
}
</style>
