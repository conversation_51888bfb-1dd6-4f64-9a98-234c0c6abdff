<!--
 @desc:项目清单立项弹出框
 @author: MRH
 @date: 2023/11/28

parmas:
	approvalId: 项目id String
	showApprovalForm: 弹出框是否展示 false true
methods:
	changeShow: 改变showAchievementForm
 -->
<template>
	<es-dialog
		:title="addFormData.state == 0 ? '立项' : '立项信息'"
		width="800px"
		height="600px"
		:visible="showApprovalForm"
		@closed="changeShow"
	>
		<es-form
			v-if="show"
			ref="form"
			:model="addFormData"
			:contents="formItemList"
			height="480px"
			collapse
			:submit="addFormData.state == 0"
			reset
			@submit="handleAddFormSubmit"
			@change="hanldeChange"
			@reset="changeShow"
		/>
	</es-dialog>
</template>

<script>
import { createBaseInfo } from '@/api/scientific-sys.js';
// import { upload_updateClassify } from 'eoss-ui/lib/config/api';
import { v4 as uuidv4 } from 'uuid';
export default {
	props: {
		showApprovalForm: {
			type: Boolean,
			required: true
		},
		approvalId: {
			type: String,
			required: true
		},
		projectClassify: {
			type: [String, Number],
			required: true
		},
		projectApprovalForm: {
			type: Object,
			required: true
		}
	},
	data() {
		return {
			show: this.showApprovalForm,
			formReadonly: false,
			formItemList: [],
			addFormData: {
				approvalTime: null,
				studyStartDate: null,
				studyEndDate: null,
				id: ''
			}
		};
	},
	mounted() {
		this.handleApprovalId();
	},
	methods: {
		handleApprovalId() {
			this.addFormData.id = this.approvalId;
			//填充研究开始时间，研究结束时间
			this.addFormData = { ...this.projectApprovalForm };
			this.$set(this.formItemList, 0, {
				label: '项目名称',
				name: 'projectName',
				placeholder: '',
				readonly: true,
				event: 'multipled',
				col: 12
			});
			if (this.projectClassify == 0) {
				this.$set(this.formItemList, 1, {
					label: '项目编码',
					name: 'projectNumber',
					placeholder: '',
					readonly: this.projectApprovalForm.isApproval === '1',
					event: 'multipled',
					rules: { required: true, trigger: 'change', message: '请输入项目编码' },
					col: 12
				});
			}
			this.$set(this.formItemList, 2, {
				label: '是否立项',
				disabled: this.projectApprovalForm.state != 0,
				name: 'isApproval',
				type: 'radio',
				event: 'multipled',
				rules: { required: true, trigger: 'change', message: '请选择是否立项' },
				data: [
					{ name: '立项', value: '1' },
					{ name: '不立项', value: '0' }
				],
				col: 12
			});
			this.projectApprovalForm.state != 0 && this.hanldeChange('isApproval', '1');
		},
		hanldeChange(e, h) {
			console.log(e, h);
			if (e == 'isApproval') {
				if (this.formItemList.length > 3) {
					this.formItemList = this.formItemList.slice(0, 3);
				}
				if (h == 1) {
					this.$set(this.formItemList, 3, {
						label: '研究开始时间',
						placeholder: '请选择研究开始时间',
						name: 'studyStartDate',
						type: 'date',
						event: 'multipled',
						readonly: this.projectApprovalForm.state != 0,
						rules: { required: false, trigger: 'blur', message: '请选择研究开始时间' },
						col: 6
					});
					this.$set(this.formItemList, 4, {
						label: '研究结束时间',
						placeholder: '请选择研究结束时间',
						name: 'studyEndDate',
						type: 'date',
						event: 'multipled',
						readonly: this.projectApprovalForm.state != 0,
						rules: { required: false, trigger: 'blur', message: '请选择研究结束时间' },
						col: 6
					});
					this.$set(this.formItemList, 5, {
						label: '立项时间',
						placeholder: '请选择立项时间',
						name: 'approvalTime',
						type: 'date',
						event: 'multipled',
						readonly: this.projectApprovalForm.state != 0,
						rules: { required: true, trigger: 'blur', message: '请选择立项时间' },
						col: 12
					});
					this.$set(this.formItemList, 6, {
						label: '立项通知书',
						name: 'initiationAdjunctName',
						type: 'upload',
						code: 'project_approval_file',
						readonly: this.projectApprovalForm.state != 0,
						ownId: this.addFormData.id,
						// selectType: 'icon-plus',
						col: 12
					});
				}
				if (h == 0) {
					this.$set(this.formItemList, 3, {
						label: '不立项理由',
						name: 'notApprovalReason',
						placeholder: '请输入不立项理由',
						readonly: this.projectApprovalForm.state != 0,
						type: 'textarea',
						event: 'multipled',
						rules: { required: true, trigger: 'blur', message: '请输入不立项理由' },
						col: 12,
						rows: 5
					});
				}
			}
		},
		async handleAddFormSubmit() {
			const loading = this.$.loading(this.$loading, '新增中');
			console.log(this.addFormData);
			try {
				let addFormData = { ...this.addFormData };
				delete addFormData.initiationAdjunctName;
				if (this.projectClassify != 0) {
					delete addFormData.projectNumber;
				}
				let { rCode, msg } = await this.$.ajax({
					url: '/ybzy/projectBaseInfo/projectApproval',
					method: 'post',
					format: false,
					data: { ...addFormData } //id==最外层列表id 成果id achievementName==成果名称
				});
				if (rCode == 0) {
					this.$message.success(msg);
					this.changeShow(1);
					// this.reset();
				} else {
					this.$message.error(msg);
				}
			} catch (error) {
				console.log('>>>error', error);
			} finally {
				loading.close();
			}
		},
		changeShow(addFinish = 0) {
			this.$emit('changeShow', addFinish);
		}
	}
};
</script>
