<template>
	<es-form
		ref="form"
		:model="formData"
		:contents="formItemList"
		label-width="100px"
		height="650px"
		:genre="2"
		collapse
		@change="inputChange"
	/>
</template>

<script>
import { fileAccess } from '@/../config/config';
export default {
	name: 'ResumeView',
	props: {
		info: {
			type: Object,
			default: {}
		}
	},
	data() {
		return {
			formData: {},
			ownId: null,
			attachmentsList: [],
			formItemList: [
				{
					title: '基本信息',
					contents: [
						{
							label: '',
							name: 'head',
							type: 'attachment',
							code: 'job_student_resume_face',
							ownId: this.ownId,
							// size: 100,
							portrait: true,
							readonly: false,
							param: {
								isShowPath: true
							},
							col: 4
						},
						{
							label: '姓名',
							name: 'name',
							readonly: true,
							col: 4
						},
						{
							label: '性别',
							name: 'sexStr',
							readonly: true,
							col: 4
						},
						{
							label: '出生年月',
							name: 'birth',
							readonly: true,
							col: 4
						},
						{
							label: '手机号',
							name: 'phone',
							readonly: true,
							col: 4
						},
						{
							label: '邮箱',
							name: 'email',
							readonly: true,
							col: 4
						},
						{
							label: '微信号',
							name: 'wechat',
							readonly: true,
							col: 4
						},
						{
							label: '所在城市',
							name: 'areaName',
							readonly: true,
							col: 4
						},
						{
							label: '详细地址',
							name: 'address',
							readonly: true,
							col: 8
						},
						{
							label: '个人简介',
							name: 'introduction',
							type: 'textarea',
							readonly: true,
							col: 12
						},
						{
							label: '人物标签',
							name: 'tag',
							readonly: true,
							col: 12
						}
					]
				},

				{
					title: '工作经历',
					contents: [
						{
							name: 'internships',
							type: 'table',
							border: true,
							form: true,
							thead: [
								{
									title: '公司名称',
									field: 'companyName',
									align: 'center',
									type: 'text',
									readonly: true
								},
								{
									title: '公司行业',
									field: 'companyIndustry',
									align: 'center',
									type: 'text',
									readonly: true
								},
								{
									title: '在职时间',
									field: 'endTime',
									align: 'center',
									type: 'text',
									render: (h, param) => {
										return h(
											'p',
											{ calss: { p1: true } },
											this.formatLink(param.row.startTime, param.row.endTime)
										);
									},
									readonly: true
								},
								{
									title: '职位名称',
									field: 'post',
									align: 'center',
									type: 'text',
									readonly: true
								},
								{
									title: '工作地点',
									field: 'address',
									align: 'center',
									type: 'text',
									readonly: true
								}
							]
						}
					]
				},
				{
					title: '教育经历',
					contents: [
						{
							name: 'educations',
							type: 'table',
							border: true,
							form: true,
							thead: [
								{
									title: '学校名称',
									field: 'university',
									align: 'center',
									type: 'text',
									readonly: true
								},
								{
									title: '学历',
									field: 'education',
									align: 'center',
									type: 'text',
									readonly: true
								},
								{
									title: '专业',
									field: 'major',
									align: 'center',
									type: 'text',
									readonly: true
								},
								{
									title: '在校时间',
									field: 'startTime',
									align: 'center',
									type: 'text',
									render: (h, param) => {
										return h(
											'p',
											{ calss: { p1: true } },
											this.formatLink(param.row.startTime, param.row.endTime)
										);
									},
									readonly: true
								}
							]
						}
					]
				},
				{
					title: '简历附件',
					contents: [
						{
							label: '',
							name: 'attachment',
							type: 'upload',
							code: 'job_student_resume_adjunct',
							ownId: this.ownId,
							fileList: this.attachmentsList,
							preview: true,
							readonly: true,
							col: 12
						}
					]
				},
				{
					type: 'submit',
					skin: 'lay-form-btns',
					contents: [{ type: 'reset', text: '取消', event: 'cancel' }]
				}
			]
		};
	},
	watch: {
		info(val, oldVal) {
			this.doHandleFormData(val);
		}
	},
	created() {
		this.doHandleFormData(this.info);
	},
	methods: {
		inputChange(key, value) {},
		//合并表格列
		formatLink(date, time) {
			var des = '';
			if (date && time) {
				des = date + '~' + time;
			} else {
				if (date) {
					des = date + '~至今';
				}
			}
			return des;
		},
		doHandleFormData(newData) {
			this.formData = newData;
			this.ownId = this.formData.id;
			if (this.formData.attachment) {
				let temp = this.formData.attachment.split(',');
				for (let fileId of temp) {
					this.attachmentsList.push(fileAccess + fileId);
				}
				this.formData.qualificationsTemp = this.attachmentsList;
			}
		}
	}
};
</script>

<style scoped lang="scss"></style>
