import httpApi from '@/http/job/jobStudentProject/api.js';
export default {
	computed: {
		component() {
			return {
				// 纯列表结构
				name: 'es-data-table'
			};
		},
		treeParam() {
			return {
				
			};
		},
		table() {
			return {
				url: httpApi.jobStudentProjectList,
				param: { handlerType: '', orderBy: 'update_time' },
				full: true,
				fit: true,
				page: true,
				stripe: true,
				numbers: true,
				checkbox: true,
				'row-key': 'id',
				toolbar: [
					{
						type: 'button',
						contents: [
							{
								text: '导出',
								code: 'toolbar',
								type: 'primary',
								exportXls: true
							},
							{
								text: '查看',
								code: 'toolbar',
							},
							{
								text: '删除',
								code: 'toolbar',
								type: 'danger'
							},
							{
								text: '新增',
								code: 'toolbar',
								type: 'primary'
							}
						]
					},
					{
						type: 'search',
						reset: true,
						contents: [
							//{
							//	name: 'accountName_like',
							//	placeholder: '配置名称',
							//	label: '配置名称',
							//	col: 6
							//},
							{
								name: 'name',
								placeholder: '项目名称',
								label: '项目名称',
								clearable: true,
								col: 6,
							},
							{
								name: 'auditStatus',
								type: 'select',
								label: '项目状态',
								placeholder: '项目状态',
								col: 3,
								clearable: true,
								data: [
									{ pid: '0', value: '0', label: '待审核' },
									{ pid: '1', value: '1', label: '已通过' },
									{ pid: '2', value: '2', label: '已驳回' },
									{ pid: '3', value: '3', label: '已结束' }
								]
							},
							{
								type: 'date',
								col: 6,
								name: 'startDate',
								label: '申请时间(开始)',
								placeholder: '申请时间(开始)'
							},
							{
								type: 'date',
								col: 6,
								name: 'endDate',
								label: '申请时间(结束)',
								placeholder: '申请时间(结束)'
							}
						],
					}
				],
				thead: [
					{
						title: '项目名称',
						align: 'left',
						field: 'name'
					},
					{
						title: '项目地点',
						align: 'left',
						field: 'address'
					},
					{
						title: '发布人',
						align: 'left',
						field: 'createUser'
					},
					{
						title: '启用状态',
						align: 'center',
						field: 'statusVO',
						width: 80,
						render: (h, params)=>{
							return h(
								'el-tag',
								{ props: { type: params.row.statusVO === '启用' ? '' : 'danger' } },
								params.row.statusVO
							)
						}
					},
					{
						title: '审核发布状态',
						align: 'center',
						field: 'auditStatusVO',
						width: 110,
						render: (h, params)=> {
							return h(
								'el-tag',
								{props: {type: params.row.auditStatusVO !== '已驳回' ? '' : 'danger'}},
								params.row.auditStatusVO
							)
						}
					},
					{
						title: '操作',
						type: 'handle',
						width: 180,
						events: [
							{
								text: '查看',
								code: 'row',
							},
							{
								text: '审核',
								code: 'row',
								rules: rows => {
									return rows.auditStatusVO === '待审核';
								}
							},
							{
								text: '编辑',
								code: 'row',
							},
							{
								text: '删除',
								code: 'row',
							}
						]
					}
				],
			};
		}
	}
};
