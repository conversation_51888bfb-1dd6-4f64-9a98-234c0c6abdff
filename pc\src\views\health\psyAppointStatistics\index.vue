<!--
 @desc:社团管理系统-教职工
 @author: 
 @date: 2023/11/13
 -->
<template>
	<div class="main">
		<div class="card-box">
			<mini-card v-for="(item, index) in card" :key="index" :card-data="item" />
		</div>
		<!--  表格  -->
		<div class="main-center-table">
			<div class="main-center-table-title title">
				<img class="title-img" src="@/assets/images/sys/预约统计.png" alt="" />
				<div>心理预约统计</div>
			</div>
			<es-data-table
				ref="table"
				:thead="thead"
				:page="false"
				:url="apis.tableUrl"
				:data="tableData"
				:toolbar="toolbar"
				:response="func"
				:parse-data="parseData"
				total
				total-text="总和"
				@change="pageSizeChange"
				@current="pageCurrentChange"
			></es-data-table>
		</div>
	</div>
</template>

<script>
import MiniCard from '@/components/show/mini-card.vue';

export default {
	components: { MiniCard },

	data() {
		return {
			apis: {
				tableUrl: '/ybzy/psyAppointOrder/statisticalListData',
				info: '/ybzy/psyAppointOrder/statisticalBaseData'
			}, //表格请求接口},
			card: [
				{
					img: require('@/assets/images/sys/gh_icon2.png'),
					title: '咨询师人数',
					unit: '人',
					width: '24.3%',
					field: 'psyNum',
					num: '0'
				},
				{
					img: require('@/assets/images/sys/gh_icon2.png'),
					title: '今日咨询师人数',
					unit: '人',
					width: '24.3%',
					field: 'nowPsyNum',
					num: '0'
				},
				{
					img: require('@/assets/images/sys/gh_icon2.png'),
					title: '预约总数',
					unit: '人',
					width: '24.3%',
					field: 'orderNum',
					num: '0'
				},
				{
					img: require('@/assets/images/sys/gh_icon2.png'),
					title: '今日预约人数',
					unit: '人',
					width: '24.3%',
					field: 'nowOrderSum',
					num: '0'
				}
			], //统计部分卡片展示内容
			thead: [
				{
					label: '所属学院',
					minWidth: 50,
					align: 'center',
					field: 'college'
				},
				{
					label: '人数（人）',
					childHead: [
						{
							label: '男',
							align: 'center',
							field: 'peopleMan'
						},
						{
							label: '女',
							align: 'center',
							field: 'peopleWoman'
						},
						{
							label: '合计',
							align: 'center',
							field: 'peopleCount'
						}
					]
				},
				{
					label: '人次（次）',
					childHead: [
						{
							label: '男',
							align: 'center',
							field: 'orderMan'
						},
						{
							label: '女',
							align: 'center',
							field: 'orderWoman'
						},
						{
							label: '合计',
							align: 'center',
							field: 'orderCount'
						}
					]
				}
			], //表格表头展示内容
			tableData: [
				// {
				// 	dep: '马克思学院',
				// 	manCount: '30',
				// 	womanCount: '40',
				// 	manNumber: '132',
				// 	womanNumber: '69'
				// },
				// {
				// 	dep: '计算机学院',
				// 	manCount: '66',
				// 	womanCount: '88',
				// 	manNumber: '150',
				// 	womanNumber: '190'
				// },
				// 	manCount: '30',
				// 	womanCount: '56',
				// 	manNumber: '132',
				// {
				// 	dep: '智能制造学院',
				// 	womanNumber: '97'
				// },
				// {
				// 	dep: '人工智能学院',
				// 	manCount: '66',
				// 	womanCount: '99',
				// 	manNumber: '150',
				// 	womanNumber: '90'
				// },
				// {
				// 	dep: '汽车学院',
				// 	manCount: '64',
				// 	womanCount: '188',
				// 	manNumber: '150',
				// 	womanNumber: '190'
				// }
			]
		};
	},
	computed: {
		//表格搜索内容
		toolbar() {
			return [
				{
					type: 'search',
					reset: true,
					contents: [
						{
							name: 'dateRange',
							placeholder: '请选择时间段',
							label: '时间',
							type: 'daterange'
						}
					]
				}
			];
		}
	},
	mounted() {
		this.queryInfo();
	},
	methods: {
		parseData(res) {
			const obj = {
				records: res
			};
			return obj;
		},
		queryInfo() {
			this.$request({
				url: this.apis.info
			}).then(res => {
				if (res.rCode == 0) {
					const results = res.results;
					this.card = this.card.map(e => {
						return {
							...e,
							num: results[e.field]
						};
					});
				}
			});
		},
		pageCurrentChange(current) {
			console.log('current', current);
		},
		pageSizeChange(size) {
			console.log('size', size);
		},
		func(res) {
			let { data } = res;
			let param = {};
			for (let i in data) {
				if (i === 'startdate') {
					param['start'] = data[i][0];
					param['end'] = data[i][1];
				} else {
					param[i] = data[i];
				}
			}
			return param;
		}
	}
};
</script>
<style lang="scss" scoped>
@import '@ast/style/public.scss';

.main {
	background: #f0f2f5;
	padding: 12px;
	overflow: auto;
	height: 100%;
	width: 100%;
	& > * {
		background: #fff;
	}
	.card-box {
		@include flexBox(space-between);
		width: 100%;
		margin: 0 0 12px;
		background: #f0f2f5;

		.card {
			width: 24.5%;
		}
	}
	.title {
		display: flex;
		justify-content: center;
		align-items: center;
		font-size: 16px;
		font-weight: 550;
		&-img {
			width: 22px;
			height: 22px;
			margin-right: 8px;
		}
	}
	.main-center-table {
		width: 100%;
		height: calc(100% - 105px);
		position: relative;
		background: #fff;
		padding-top: 4px;
		border-radius: 8px;
		&-title {
			position: absolute;
			z-index: 66;
			left: 20px;
			top: 12px;
		}
		::v-deep .es-date-picker-range {
			max-width: 280px !important;
		}
		::v-deep .el-form-item__content {
			line-height: normal !important;
		}
		::v-deep .es-data-table-content {
			background: #fff !important;
			border-radius: 0 0 8px 8px;
		}
		::v-deep .es-data-table {
			border-radius: 8px;
		}
		::v-deep .es-toolbar {
			border-radius: 8px 8px 0 0;
			border-bottom: 1px solid rgba(241, 244, 247, 1) !important;
		}
	}
	::v-deep {
		.es-toolbar .es-form-search-small .el-form-item__content {
			line-height: 30px !important;
		}
	}
}
</style>
