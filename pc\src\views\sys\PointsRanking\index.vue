<template>
	<div class="body">
		<div class="leftBody">
			<div class="items" v-for="(item, index) in topItems" :key="index">
				<p class="title">{{ item.title }}</p>
				<div class="rankList">
					<div class="rankItem" v-for="(rank, rankIndex) in item.ranks" :key="rankIndex">
						{{ rankIndex + 1 }}、{{ rank }}
					</div>
				</div>
			</div>
		</div>
		<div class="rightBody">
			<div class="BtnBox">
				<div class="inputBox">
					<es-input v-model="formData.name" placeholder="请输入姓名" :maxlength="6"></es-input>
				</div>

				<div class="inputBox">
					<es-select
						v-model="formData.college"
						placeholder="请选择学院"
						@change="handleChange"
						:data="options"
						value-type="string"
					></es-select>
				</div>
				<es-button type="primary" link="a.html">查询</es-button>
				<es-button type="primary" link="a.html">导出</es-button>
			</div>

			<div class="CharsList">
				<div class="items">
					<p class="title">学院积分增值趋势分析</p>
					<div class="Chars" ref="Chars1"></div>
				</div>

				<div class="items">
					<p class="title">积分来源分析</p>
					<div class="Chars" ref="Chars2"></div>
				</div>
			</div>
		</div>
	</div>
</template>

<script>
import interfaceUrl from '@/http/screen/zbManage';

export default {
	data() {
		return {
			formData: {},
			options: [],

			topItems: [
				{
					title: '学院前5top',
					ranks: [
						'电子信息与人工智能学院',
						'智能制造学院',
						'文创与旅游学院',
						'汽车与轨道交通学院',
						'五粮液技术与食品工程学院'
					]
				},
				{
					title: '专业前5top',
					ranks: [
						'电子信息工程技术',
						'物联网应用技术',
						'工业机器人技术',
						'智能控制技术',
						'铁道机车'
					]
				},
				{
					title: '个人前5top',
					ranks: [
						'202110064王皓骏',
						'202119575杨晋菘',
						'202119575杨晋菘',
						'202119425唐嘉竣',
						'202119058陈玟'
					]
				}
			],

			myChart: []
		};
	},
	mounted() {
		this.selectSchool();
		this.toolChart1(0);
		this.toolChart2(1);
		window.addEventListener('resize', this.onResize);
	},
	beforeDestroy() {
		window.removeEventListener('resize', this.onResize);
		this.myChart.forEach(item => {
			if (item) {
				item.dispose();
			}
		});
	},
	methods: {
		onResize() {
			this.myChart.forEach(item => {
				if (item) {
					item.resize();
				}
			});
		},
		handleChange(val) {
			console.log(this.formData);
		},

		selectSchool(res) {
			this.$request({
				url: interfaceUrl.selectSchool
			}).then(res => {
				this.options = res.results;
			});
		},

		getPastWeekDates() {
			const dates = [];
			const today = new Date();

			for (let i = 0; i < 7; i++) {
				const date = new Date(today);
				date.setDate(today.getDate() - i);
				const formattedDate = `${date.getMonth() + 1}.${date.getDate()}`;
				dates.push(formattedDate);
			}

			return dates.reverse(); // 按时间顺序排列
		},

		toolChart1(index) {
			let myChart = this.$echarts.init(this.$refs.Chars1);

			if (this.myChart[index]) {
				this.myChart[index].clear();
			} else {
				this.myChart[index] = myChart;
			}
			const xData = this.getPastWeekDates();
			const yData = [102, 302, 520, 702, 800, 850, 900];
			let option = {
				tooltip: {
					trigger: 'axis'
				},
				grid: {
					left: '5%',
					top: '10%',
					bottom: '8%',
					right: '5%'
				},
				xAxis: [
					{
						type: 'category',
						data: xData,
						axisLine: {
							lineStyle: {
								color: '#999'
							}
						}
					}
				],
				yAxis: [
					{
						type: 'value',
						splitNumber: 4,
						splitLine: {
							lineStyle: {
								type: 'dashed',
								color: '#DDD'
							}
						},
						axisLine: {
							show: false,
							lineStyle: {
								color: '#333'
							}
						},
						nameTextStyle: {
							color: '#999'
						},
						splitArea: {
							show: false
						}
					}
				],
				series: [
					{
						name: '文件数',
						type: 'line',
						data: yData,
						lineStyle: {
							normal: {
								width: 8,
								color: {
									type: 'linear',

									colorStops: [
										{
											offset: 0,
											color: '#0076e8' // 0% 处的颜色
										},
										{
											offset: 1,
											color: '#0076e8' // 100% 处的颜色
										}
									],
									globalCoord: false // 缺省为 false
								}
							}
						},
						itemStyle: {
							normal: {
								color: '#fff',
								borderWidth: 10,
								borderColor: '#A9F387'
							}
						},
						label: {
							show: true, // 显示标签
							position: 'top', // 标签位置在数据点上方
							color: '#000' // 标签文字颜色
						},
						smooth: true
					}
				]
			};

			option && myChart.setOption(option);
		},

		toolChart2(index) {
			let myChart = this.$echarts.init(this.$refs.Chars2);
			if (this.myChart[index]) {
				this.myChart[index].clear();
			} else {
				this.myChart[index] = myChart;
			}
			let data = {
				area: this.getPastWeekDates(),
				legend: ['第二课堂', '图书馆', '运动场'],
				data: [
					[1320, 1302, 901, 634, 1390, 1330, 1320, 1000, 500],
					[320, 302, 301, 334, 390, 330, 320, 100, 50],
					[320, 302, 301, 334, 390, 330, 320, 100, 50]
				]
			};
			let colors = ['#4C98FB', '#83CCE7', '#26C7C8'];
			let option = {
				tooltip: {
					trigger: 'axis'
				},
				color: colors,
				legend: {
					top: 10,
					right: 70,
					itemWidth: 10,
					itemHeight: 10,
					textStyle: {
						fontSize: 14,
						color: '#96A4F4',
						padding: [3, 0, 0, 0]
					},
					data: data.legend
				},
				grid: {
					left: '5%',
					right: '5%',
					bottom: '12%'
				},
				xAxis: {
					type: 'category',
					axisLabel: {
						color: '#96A4F4'
					},
					axisLine: {
						lineStyle: {
							color: '#96A4F4'
						},
						width: 5
					},
					axisTick: {
						show: false
					},
					data: data.area
				},
				yAxis: {
					type: 'value',
					axisLabel: {
						color: '#96A4F4'
					},
					axisLine: {
						lineStyle: {
							color: '#96A4F4'
						},
						width: 5
					},
					axisTick: {
						show: false
					},
					splitLine: {
						lineStyle: {
							color: 'rgba(150, 164, 244, 0.3)'
						}
					}
				},
				series: []
			};
			for (let i = 0; i < data.legend.length; i++) {
				option.series.push({
					name: data.legend[i],
					type: 'bar',
					stack: '总量',
					barWidth: '45%',
					label: {
						show: true,
						position: 'top'
					},
					data: data.data[i]
				});
			}
			option && myChart.setOption(option);
		}
	},
	components: {}
};
</script>

<style scoped lang="scss">
.body {
	height: 100%;
	padding: 20px;
	display: flex;
	justify-content: space-between;
	background-color: #f8f9fa; // 设置背景色使整体更清新
	border-radius: 8px; // 添加圆角
	box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1); // 添加阴影使其立体感更强

	.leftBody {
		width: 20%; // 调整宽度以平衡内容
		height: 100%;
		display: flex;
		flex-direction: column;
		justify-content: space-around;

		.items {
			height: 31%; // 调整高度以适应内容
			background-color: #ffffff; // 设置每个项的背景色
			padding: 15px;
			border-radius: 4px; // 每个项的圆角
			box-shadow: 0 1px 5px rgba(0, 0, 0, 0.1); // 每个项的阴影

			.title {
				font-size: 1.2rem;
				padding-bottom: 10px;
				margin-bottom: 10px;
				color: #333; // 设置标题颜色
				font-weight: 600; // 加粗标题
				border-bottom: 1px solid #000;
			}

			.rankList {
				display: flex;
				flex-direction: column;
				.rankItem {
					padding: 5px 0; // 每项间的上下间距
					color: #666; // 排名项的颜色
					border-bottom: 2px solid #ececec; // 下划线分隔
					margin-bottom: 5px;
				}
			}
		}
	}

	.rightBody {
		width: 79%; // 调整宽度
		height: 100%;
		padding: 20px; // 右侧内容的内边距
		background-color: #ffffff; // 右侧背景色
		border-radius: 4px; // 圆角
		box-shadow: 0 1px 5px rgba(0, 0, 0, 0.1); // 阴影效果

		.BtnBox {
			display: flex;
			justify-content: right;
			.inputBox {
				margin-right: 15px;
			}
		}

		.CharsList {
			height: 95%;

			.items {
				height: 50%;
				margin-top: 20px;
				.title {
					width: 30%;
					font-size: 1.2rem;
					padding-bottom: 10px;
					margin-bottom: 10px;
					color: #333; // 设置标题颜色
					font-weight: 700; // 加粗标题
					border-bottom: 1px solid #000;
				}
				.Chars {
					height: 88%;
					// background: red;
				}
			}
		}
	}
}
</style>
