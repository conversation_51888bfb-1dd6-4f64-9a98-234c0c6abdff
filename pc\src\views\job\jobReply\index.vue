<template>
	<div class="content">
		<es-data-table
			ref="table"
			:key="tableCount"
			v-loading="loading"
			:row-style="tableRowClassName"
			:full="true"
			:fit="true"
			:thead="thead"
			:toolbar="toolbar"
			:border="true"
			:page="pageOption"
			:url="dataTableUrl"
			:numbers="true"
			:param="params"
			close
			form
			@btnClick="btnClick"
			@sort-change="sortChange"
		></es-data-table>
		<!-- 查看 -->
		<es-dialog
			v-if="showForm"
			:title="formTitle"
			:visible.sync="showForm"
			:drag="false"
			width="1000px"
			height="92%"
			:close-on-click-modal="false"
			:destroy-on-close="true"
		>
			<resumeView :info="formData" @closeDialog="refreshData"></resumeView>
		</es-dialog>
		<!-- 审核 -->
		<Next :id="formData.id" ref="nextRef" @closeDialog="refreshData" />
	</div>
</template>

<script>
import interfaceUrl from '@/http/job/jobDualSelect/api.js';
import resumeView from './components/view.vue';
import Next from './components/next.vue';
export default {
	components: { resumeView, Next },
	data() {
		return {
			loading: false,
			dataTableUrl: interfaceUrl.listJsonJobReply,
			tableCount: 1,
			showForm: false,
			ownId: null, //数据行Id
			params: {
				auditStatesList: [0],  //待审核
				// auditStatesList: [1, 9] //已审核（通过、驳回）
			},
			toolbar: [
				{
					type: 'tabs',
					contents: [
						{ text: '待审核', auditStatesList: [0] },
						{ text: '已审核', auditStatesList: [1, 9] }
					]
				},
				{
					type: 'search',
					contents: [{ type: 'text', name: 'keyword', placeholder: '标题关键字查询' }]
				}
			],
			thead: [
				{
					title: '邀请函标题',
					// width: 200,
					align: 'center',
					field: 'title'
				},
				{
					title: '企业名称',
					align: 'center',
					// width: 200,
					field: 'enterpriseName'
				},
				{
					title: '统一社会信息代码',
					minWidth: 80,
					align: 'center',
					field: 'enterpriseCode'
				},
				{
					title: '参加时间',
					align: 'center',
					field: 'joinStartEndDes'
				},
				{
					title: '联系人',
					width: 90,
					align: 'center',
					field: 'contacts'
				},
				{
					title: '联系方式',
					width: 120,
					align: 'center',
					field: 'contactNum'
				},
				{
					title: '审核状态',
					width: 80,
					align: 'center',
					field: 'auditStates',
					render: (h, param) => {
						return h('p', null, this.transf(param.row.auditStates));
					}
				},
				{
					title: '操作',
					type: 'handle',
					width: 100,
					template: '',
					events: [
						{ code: 'view', text: '查看', icon: 'el-icon-view' },
						{
							code: 'next',
							text: '审核',
							icon: 'el-icon-coordinate',
							rules: rows => {
								return rows.auditStates === 0;
							}
						}
					]
				}
			],
			pageOption: {
				layout: 'total, prev, pager, next, jumper',
				pageSize: 10,
				// hideOnSinglePage: true,
				position: 'right',
				current: 1,
				pageNum: 1
			},
			formData: {},
			formTitle: '编辑'
		};
	},
	watch: {
		dataTableUrl() {
			this.tableCount++;
		}
	},
	methods: {
		//表格行高
		tableRowClassName({ row, rowIndex }) {
			let styleRes = { height: '54px !important' };
			return styleRes;
		},
		inputChange(key, value) {},
		//操作按钮点击事件
		btnClick(e) {
			let code = e.handle.code;
			switch (code) {
				case 'view':
					this.formTitle = '查看';
					this.loading = true;
					this.$request({
						url: interfaceUrl.infoReply,
						params: { id: e.row.mappingId },
						method: 'GET'
					}).then(res => {
						this.loading = false;
						if (res.rCode === 0) {
							this.formData = {
								...res.results,
								auditStates: e.row.auditStates, // 0 才可以审核
								flowId: e.row.id || ''
							};
							this.showForm = true;
							return;
						}
						this.$message({
							message: res.msg || '网络异常！',
							type: 'warning'
						});
					});
					break;
				case 'next':
					this.formTitle = '审核';
					this.formData = e.row;
					this.$refs.nextRef.showForm = true;
					break;
				default:
					break;
			}
		},
		transf(states) {
			switch (states) {
				case 0:
					return '待审核';
				case 1:
					return '已审核';
				case 9:
					return '已驳回';
				default:
					return '未知';
			}
		},
		//删除行
		deleteRow() {
			this.$request({
				url: interfaceUrl.deleteBatchIds,
				data: { ids: this.deleteId },
				method: 'POST'
			}).then(res => {
				if (res.rCode === 0) {
					this.$refs.table.reload();
					this.$message.success('删除成功');
				} else {
					this.$message.error(res.msg);
				}
			});
		},
		//排序变化事件
		sortChange(column, prop, order) {
			let asc = this.params.asc;
			let orderBy = this.params.orderBy;
			if (column.order === 'ascending') {
				//升序
				asc = 'true';
				orderBy = column.prop;
			} else if (column.order === 'descending') {
				//降序
				asc = 'false';
				orderBy = column.prop;
			} else {
				//不排序
				asc = 'false';
				orderBy = column.prop;
			}
			this.params.asc = asc;
			this.params.orderBy = orderBy;
			this.$refs.table.reload();
		},
		refreshData() {
			this.$refs.table.reload();
			this.showForm = false;
		}
	}
};
</script>

<style scoped lang="scss">
@import '../../../assets/style/style.scss';
.content {
	width: 100%;
	height: 100%;

	// ::v-deep .es-data-table {
		// flex: 1;
		// display: flex;
		// flex-direction: column;
		// height: calc(100% - 58px);

		// .es-data-table-content {
		// 	flex: 1;
		// 	height: 0;
		// 	display: flex;
		// 	flex-direction: column;

		// .el-table {
			// flex: 1;
			// height: 100% !important;
		// }

		// 	.es-thead-border {
		// 		.el-table__header {
		// 			th {
		// 				border-right: 1px solid #e1e1e1;
		// 			}
		// 		}
		// 	}
		// }
	// }

	// ::v-deep .el-form-item__label {
	// 	background: none;
	// 	border: 0px solid #c2c2c2;
	// }
}
</style>
@/http/job/jobDualSelect/api.js
