<template>
	<div class="main_container">
		<div class="headContainer">
			<el-button type="primary" style="margin-bottom: 10px" size="small" @click="handleAdd">
				新增
			</el-button>
			<el-input style="width: 200px" placeholder="请输入关键字搜索" v-model="search"></el-input>
		</div>
		<es-data-table :data="filteredTableData" :key="tableKey" :border="true">
			<template slot="append">
				<el-table-column label="申请者" prop="applicant"></el-table-column>
				<el-table-column label="权限类型" prop="PermissionType"></el-table-column>
				<el-table-column label="授权时间" prop="AuthorizationTime"></el-table-column>
				<el-table-column label="操作类型" prop="OperationType"></el-table-column>
				<el-table-column label="操作用户" prop="OperationUser"></el-table-column>
				<el-table-column label="授权结果" prop="result"></el-table-column>
				<el-table-column label="更新说明" prop="remark"></el-table-column>
				<el-table-column label="操作">
					<template slot-scope="scope">
						<el-button @click="handleCheck(scope.row)" type="text">查看</el-button>
						<el-button @click="hanldeEdit(scope.row)" type="text">编辑</el-button>
						<el-button @click.native.prevent="deleteRow(scope.$index, tableData)" type="text">
							删除
						</el-button>
					</template>
				</el-table-column>
			</template>
		</es-data-table>
		<el-dialog
			:title="title"
			:visible.sync="dialogFormVisible"
			@open="openForm"
			@close="resetData"
			append-to-body
		>
			<el-form
				:model="form"
				label-width="120px"
				:rules="rules"
				ref="formRef"
				:disabled="isDisabled"
			>
				<el-form-item label="申请者" prop="applicant">
					<el-input v-model="form.applicant"></el-input>
				</el-form-item>
				<el-form-item label="权限类型" prop="PermissionType">
					<el-select v-model="form.PermissionType">
						<el-option label="读写" value="读写"></el-option>
						<el-option label="写入" value="写入"></el-option>
						<el-option label="执行" value="执行"></el-option>
					</el-select>
				</el-form-item>
				<el-form-item label="请选择时间" prop="AuthorizationTime">
					<el-date-picker
						v-model="form.AuthorizationTime"
						type="daterange"
						range-separator="至"
						format="yyyy-MM-dd"
						value-format="yyyy-MM-dd"
						start-placeholder="开始日期"
						end-placeholder="结束日期"
						@change="dateChange"
					></el-date-picker>
				</el-form-item>
				<el-form-item label="操作类型" prop="OperationType">
					<!-- <el-input v-model="form.type"></el-input> -->
					<el-select v-model="form.OperationType">
						<el-option label="授权" value="授权"></el-option>
						<el-option label="撤销授权" value="撤销授权"></el-option>
						<el-option label="失败" value="失败"></el-option>
					</el-select>
				</el-form-item>
				<el-form-item label="操作用户" prop="OperationUser">
					<el-input v-model="form.OperationUser"></el-input>
				</el-form-item>

				<el-form-item label="授权结果" prop="result">
					<el-select v-model="form.result">
						<el-option label="成功" value="成功"></el-option>
						<el-option label="失败" value="失败"></el-option>
					</el-select>
				</el-form-item>

				<el-form-item label="操作用户" prop="remark">
					<el-input v-model="form.remark"></el-input>
				</el-form-item>
			</el-form>
			<div slot="footer" class="dialog-footer">
				<el-button @click="dialogFormVisible = false">取 消</el-button>
				<el-button type="primary" @click="handleSure()">确 定</el-button>
			</div>
		</el-dialog>
	</div>
</template>

<script>
import { v4 as uuidv4 } from 'uuid';

export default {
	data() {
		return {
			search:null,
			tableData: [
				{
					id: '12345678',
					ResourceID: '测试',
					applicant: '测试用户1',
					PermissionType: 'SSL/TLS证书',
					AuthorizationTime: '2020-01-01至2024-12-01',
					OperationType: '有效',
					OperationUser: '管理员',
					result: '2020-01-01',
					remark: '申请'
				}
			],
			form: {},
			dialogFormVisible: false,
			title: '新增',
			rules: {
				applicant: [{ required: true, message: '请填写', trigger: 'blue' }],
				PermissionType: [{ required: true, message: '请填写', trigger: 'blue' }],
				AuthorizationTime: [{ required: true, message: '请填写', trigger: 'blue' }],
				OperationType: [{ required: true, message: '请填写', trigger: 'blue' }],
				OperationUser: [{ required: true, message: '请填写', trigger: 'blue' }],
				result: [{ required: true, message: '请填写', trigger: 'blue' }],
				remark: [{ required: true, message: '请填写', trigger: 'blue' }]
			},
			tableKey: 0,
			isDisabled: false
		};
	},
	computed: {
		filteredTableData() {
			
			console.log(123123);
			
			if (!this.search) {
				return this.tableData; // 如果没有搜索词，则返回所有数据
			}
			return this.tableData.filter(item => {
				// 假设你想在多个字段上搜索，比如 id, user, type 等
				return (
					item.applicant.toLowerCase().includes(this.search.toLowerCase()) 
					// 可以继续添加其他字段
				);
			});
		}
	},
	methods: {
		openForm() {
			if (this.title === '新增') {
				this.form.id = uuidv4();
				this.form.ResourceID = uuidv4();
			}
		},

		resetData() {
			this.form = {};
		},

		deleteRow(index, rows) {
			rows.splice(index, 1);
			this.$message.success('操作成功');
		},
		hanldeEdit(row) {
			this.dialogFormVisible = true;
			this.isDisabled = false;
			this.form = row;
			this.title = '查看/修改';
		},
		dateChange(val) {
			console.log(val);
		},
		handleAdd() {
			this.dialogFormVisible = true;
			this.isDisabled = false;
			this.title = '新增';
		},
		handleSure() {
			this.$refs.formRef.validate(valid => {
				if (valid) {
					if (this.title == '新增') {
						this.form.AuthorizationTime =
							this.form.AuthorizationTime[0] + '至' + this.form.AuthorizationTime[1];
						this.form.handleBy = '管理员';
						this.tableData.push(this.form);
						this.tableKey = Math.random();
						console.log(this.tableData, 789);
						this.dialogFormVisible = false;
						this.form = {};
						this.$message.success('新增成功');
					} else {
						this.$message.success('编辑成功');
						this.dialogFormVisible = false;
					}
				}
			});
		},
		handleCheck(row) {
			this.dialogFormVisible = true;
			this.isDisabled = true;
			this.form = row;
		}
	}
};
</script>

<style lang="scss" scoped>
.main_container {
	padding: 35px 20px;
}
.headContainer {
	display: flex;
	align-items: center;
	justify-content: space-between;
	margin-bottom: 10px;
}
</style>
