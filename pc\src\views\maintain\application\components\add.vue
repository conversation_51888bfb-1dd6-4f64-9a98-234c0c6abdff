<template>
<div v-loading="loading">
	<es-form
		ref="form"
		:model="formData"
		:contents="formItemList"
		:genre="2"
		height="600px"
		collapse
		@submit="handleFormSubmit"
	/>
	</div>
</template>
<script>
import api from '@/http/maintain/applicationApi';
import SnowflakeId from 'snowflake-id';

export default {
	props: {
		id: {
			type: String
		},
		pageMode: {
			type: String
		}
	},
	data() {
		return {
			loading: false,
			buildList: [],
			orgList: [],
			formData: {}
		};
	},
	computed: {
		formItemList() {
			return [
				{
					type: 'select',
					name: 'repairType',
					label: '报修类别',
					placeholder: '请选择报修类别',
					sysCode: 'hq_repair_type',
					'label-key': 'shortName',
					'value-key': 'cciValue',
					rules: {
						required: true,
						message: '请选择报修类别',
						trigger: 'change'
					},
					clearable: true,
					col: 6
				},
				{
					label: '报修人',
					name: 'reportUser',
					placeholder: '请输入报修人',
					rules: {
						required: true,
						message: '请输入报修人',
						trigger: 'blur'
					},
					maxlength: 20,
					col: 6
				},
				{
					type: 'select',
					name: 'reportUserOrg',
					label: '报修人所属机构',
					placeholder: '请选择报修人所属机构',
					data: this.orgList,
					'label-key': 'label',
					'value-key': 'value',
					rules: {
						required: true,
						message: '请选择报修人所属机构',
						trigger: 'change'
					},
					clearable: true,
					col: 6
				},
				{
					label: '报修电话',
					name: 'repairPhone',
					placeholder: '请输入报修电话',
					event: 'multipled',
					rules: {
						required: true,
						trigger: 'blur',
						validator: (rule, value, callback) => {
							if (!rule.required) {
								return callback();
							}
							if (!value) {
								return callback(new Error('联系方式不能为空'));
							}
							if (/^(?:(?:\+|00)86)?1[3-9]\d{9}$|^0[1-9]\d{1,2}-\d{7,8}$/.test(value)) {
								callback();
							} else {
								callback(new Error('请输入正确的电话格式'));
							}
						}
					},
					col: 6
				},
				{
					type: 'datetime',
					name: 'expectDate',
					label: '期待维修时间',
					placeholder: '期待维修时间',
					rules: {
						required: true,
						message: '请选择期待维修时间',
						trigger: 'blur'
					},
					col: 6
				},
				{
					type: 'cascader',
					event: 'multipled',
					name: 'addressTypeArr',
					label: '报修地点',
					placeholder: '请选择报修地点',
					data: this.buildList,
					rules: {
						required: true,
						message: '请选择报修地点',
						trigger: 'change'
					},
					clearable: true,
					col: 6
				},
				{
					label: '详细地址',
					name: 'address',
					placeholder: '请输入详细地址',
					maxlength: 50,
					col: 6
				},
				{
					label: '报修描述',
					type: 'textarea',
					name: 'reportContent',
					rules: {
						required: true,
						message: '请输入报修描述',
						trigger: 'blur'
					},
					maxlength: 300,
					rows: 5
				},
				{
					label: '报修图片(*最多可上传8张图片)',
					type: 'attachment',
					code: 'hq_report_image',
					'select-type': 'icon-plus',
					preview: true,
					listType: 'picture-card',
					ownId: this.formData.id, // 业务id
					limit: 8
				}
			];
		}
	},
	watch: {},
	created() {
		this.getOrgList();
		this.getBuildList();
		switch (this.pageMode) {
			case 'add':
				const snowflake = new SnowflakeId();
				this.formData.id = snowflake.generate();
				break;
			case 'edit':
				this.$request({ url: api.getApplicationInfo + '/' + this.id, method: 'GET' }).then(res => {
					if (res.rCode === 0) {
						this.formData = res.results;
						if (res.results.addressType === null || res.results.addressType === '') {
							this.formData.addressTypeArr = [];
						} else {
							this.formData.addressTypeArr = res.results.addressType.split(',');
						}
					} else {
						this.$message.error(res.msg);
					}
				});
				break;
		}
	},
	methods: {
		getBuildList() {
			this.$request({
				url: api.getBuildingSelectList,
				method: 'GET'
			}).then(res => {
				if (res.rCode === 0) {
					this.buildList = res.results;
				}
			});
		},
		getOrgList() {
			this.loading = true;
			this.$request({
				url: api.getOrgSelectList,
				method: 'GET'
			}).then(res => {
				if (res.rCode === 0) {
					this.orgList = res.results;
				}
				this.loading = false;
			});
		},
		handleFormSubmit() {
			//校验
			this.$refs.form.validate(valid => {
				if (valid) {
					let apiUrl = this.pageMode === 'edit' ? api.updateApplication : api.savaApplication;
					let saveData = { ...this.formData };
					//处理数据
					if (saveData.id === undefined || saveData.id === '') {
						const snowflake = new SnowflakeId();
						saveData.id = snowflake.generate();
					}
					if (saveData.addressTypeArr !== undefined && saveData.addressTypeArr.length > 0) {
						saveData.addressType = saveData.addressTypeArr.join(',');
					}
					//移除addressTypeArr
					delete saveData.addressTypeArr;
					this.$request({
						url: apiUrl,
						data: saveData,
						method: 'POST'
					}).then(res => {
						if (res.rCode === 0) {
							this.$message.success('操作成功');
							this.$emit('closeAddOrEditPage');
						} else {
							this.$message.error(res.msg);
						}
					});
				}
			});
		}
	}
};
</script>
<style scoped>
::v-deep .el-collapse-item__header.is-active {
	border-bottom: 1px solid #ebeef5;
	font-size: 18px;
	font-weight: bold;
}
::v-deep .el-collapse-item__header::before {
	content: '';
	width: 4px;
	height: 18px;
	background-color: #0076e9;
	margin-right: 2px;
}
</style>
