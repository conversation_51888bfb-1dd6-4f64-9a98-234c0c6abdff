<template>
	<div class="content">
		<es-data-table
			ref="table"
			:key="tableCount"
			:row-style="tableRowClassName"
			:full="true"
			:fit="true"
			:thead="thead"
			:toolbar="toolbar"
			:border="true"
			:page="pageOption"
			:option-data="optionData"
			:url="dataTableUrl"
			:numbers="true"
			:param="params"
			close
			form
			@edit="changeTable"
			@btnClick="btnClick"
		></es-data-table>
		<es-dialog
			v-if="showForm"
			:title="formTitle"
			:visible.sync="showForm"
			width="700px"
			:drag="false"
			:close-on-click-modal="false"
			:destroy-on-close="true"
		>
			<es-form
				v-if="showForm"
				ref="form"
				:model="formData"
				:contents="formItemList"
				:genre="2"
				collapse
				:submit="formTitle != '查看'"
				:reset="true"
				@change="inputChange"
				@submit="handleFormSubmit"
			/>
		</es-dialog>
		<es-dialog
			title="删除"
			:visible.sync="showDelete"
			width="20%"
			:close-on-click-modal="false"
			:middle="true"
			:drag="false"
			height="140px"
		>
			<div>确定要删除该条数据吗</div>
			<div class="btn-box">
				<div class="btn theme" @click="deleteRow">确定</div>
				<div class="btn" @click="showDelete = false">取消</div>
			</div>
		</es-dialog>
		<es-dialog
			v-if="showPsyAppointTime"
			title="预约时间段设置"
			:visible.sync="showPsyAppointTime"
			append-to-body
			width="70%"
			height="80%"
			:close-on-click-modal="false"
			:destroy-on-close="true"
		>
			<psyAppointTime ref="psyAppointTime" :psy-id="psyId"></psyAppointTime>
		</es-dialog>
	</div>
</template>

<script>
import interfaceUrl from '@/http/health/psyBaseInfo.js';
import psyAppointTime from '@/views/health/psyAppointTime/appointTime.vue';
import SnowflakeId from 'snowflake-id';
import SelectMore from './select-more.vue';
export default {
	components: { psyAppointTime },
	data() {
		return {
			dataTableUrl: interfaceUrl.listJson,
			tableCount: 1,
			showForm: false,
			showDelete: false,
			ownId: null, //数据行Id
			showPsyAppointTime: false,
			psyId: null,
			personList: [],
			params: {
				orderBy: 'createTime',
				asc: 'false',
				isAudit: 0
			},
			editBtn: {
				type: 'submit',
				skin: 'lay-form-btns',
				contents: [
					{ type: 'primary', text: '确定', event: 'confirm' },
					{ type: 'reset', text: '取消', event: 'cancel' }
				]
			},
			cancelBtn: {
				type: 'submit',
				skin: 'lay-form-btns',
				contents: [{ type: 'reset', text: '取消', event: 'cancel' }]
			},
			toolbar: [
				{
					type: 'button',
					contents: [
						{
							text: '新增',
							code: 'add'
						}
					]
				},
				{
					type: 'search',
					contents: [{ type: 'text', name: 'keyword', placeholder: '姓名查询' }]
				}
			],
			thead: [],
			listThead: [
				{
					title: '姓名',
					align: 'center',
					width: 200,
					field: 'name'
				},
				{
					title: '性别',
					field: 'sex',
					align: 'center',
					width: 120,
					render: (h, param) => {
						return h('p', null, param.row.sex === 0 ? '男' : '女');
					}
				},
				{
					title: '擅长领域',
					field: 'adept',
					align: 'center',
					showOverflowTooltip: true
				},
				{
					title: '状态',
					width: 120,
					align: 'center',
					field: 'status',
					type: 'switch'
				},
				{
					title: '创建时间',
					width: 160,
					align: 'center',
					field: 'createTime'
				}
			],
			viewListBtn: {
				title: '操作',
				type: 'handle',
				width: 180,
				template: '',
				events: [
					{ code: 'view', text: '查看' },
					{ code: 'edit', text: '编辑' },
					{ code: 'appointTimeBtn', text: '编辑预约时间' },
					{ code: 'delete', text: '删除' }
				]
			},
			optionData: {
				status: [
					{
						value: 1,
						name: '启用'
					},
					{
						value: 0,
						name: '停用'
					}
				]
			},
			pageOption: {
				layout: 'total, prev, pager, next, jumper',
				pageSize: 10,
				// hideOnSinglePage: true,
				position: 'right',
				current: 1,
				pageNum: 1
			},
			formData: {},
			formTitle: '编辑'
		};
	},
	computed: {
		formItemList() {
			const readonly = this.formTitle === '查看';
			return [
				{
					label: '头像',
					name: 'imgId',
					type: 'attachment',
					code: 'psy_head_img',
					'select-type': 'icon-plus',
					preview: true,
					listType: 'picture-card',
					ownId: this.ownId, // 业务id
					readonly: readonly,
					limit: 1,
					col: 12
				},
				// {
				// 	label: '头像',
				// 	type: 'attachment',
				// 	code: 'psy_head_img',
				// 	name: 'imgId',
				// 	ownId: this.ownId,
				// 	// size: 100,
				// 	portrait: true,
				// 	readonly: readonly,
				// 	param: {
				// 		isShowPath: true
				// 	},
				// 	// param: { _tt: 1231 },
				// 	col: 12
				// },
				{
					type: 'attachment',
					label: '关联用户',
					name: 'personId',
					showFileList: false,
					disabled: true,
					code: 'USER_IMG',
					ownId: '666666',
					render: h => {
						let initData = {};
						if (this.formData?.personId && this.formData?.userName) {
							initData = {
								userId: this.formData.personId,
								name: this.formData.userName,
								number: this.formData.userNum
							};
						}
						return h(SelectMore, {
							props: { width: '528px', initData: initData, readonly: this.formTitle !== '新增' },
							on: {
								onSelect: val => {
									this.$set(this.formData, 'personId', val.userId);
									this.$set(this.formData, 'userName', val.name);
									this.$set(this.formData, 'userNum', val.number);
									console.log(this.formData.personId, 'personId');
								}
							}
						});
					},
					rules: {
						required: true,
						message: '请选择关联用户',
						validator: (rule, value, callback) => {
							if (this.formData.personId) {
								callback();
							}
							callback(new Error('请选择关联用户'));
						},
						trigger: 'change'
					},
					col: 12
				},
				{
					label: '姓名',
					name: 'name',
					placeholder: '请输心理医生姓名',
					rules: {
						required: true,
						message: '请输心理医生姓名',
						trigger: 'blur'
					},
					maxlength: 50,
					verify: 'required',
					readonly: readonly,
					col: 12
				},
				{
					label: '性别',
					name: 'sex',
					placeholder: '请选择性别',
					type: 'radio',
					data: [
						{ label: '男', value: 0 },
						{ label: '女', value: 1 }
					],
					'label-key': 'label',
					'value-key': 'value',
					rules: {
						required: true,
						message: '请选择性别',
						trigger: 'change'
					},
					readonly: readonly,
					col: 12
				},
				{
					label: '擅长领域',
					name: 'adept',
					type: 'textarea',
					placeholder: '请输入擅长领域',
					rules: {
						required: true,
						message: '请输入擅长领域',
						trigger: 'blur'
					},
					maxlength: 200,
					readonly: readonly,
					rows: 3
				},
				{
					label: '简介',
					name: 'intro',
					placeholder: '请填写简介',
					type: 'textarea',
					maxlength: 800,
					readonly: readonly,
					rows: 5
				},
				{
					label: '状态',
					name: 'status',
					type: 'switch',
					data: [
						{ value: 1, name: '启用' },
						{ value: 0, name: '停用' }
					],
					verify: 'required',
					disabled: readonly,
					// rules: {
					// 	required: true,
					// 	message: '请确定是否启用',
					// 	trigger: 'blur'
					// },
					col: 4
				}
			];
		}
	},
	watch: {
		dataTableUrl() {
			this.tableCount++;
		},
		formItemList: {
			handler(newValue, oldValue) {},
			deep: true
		}
	},
	created() {
		this.getPersonList();
		this.thead = this.getListThead(this.viewListBtn);
	},
	methods: {
		getPersonList() {
			this.$request({
				url: interfaceUrl.getPersonListByTpye,
				// params: { type: 'psychologist' },
				method: 'GET'
			}).then(res => {
				if (res.rCode === 0) {
					// res.results.forEach(obj => {
					// 	let person = {};
					// 	person.value = obj.id;
					// 	person.name = obj.xm;
					// 	this.personList.push(person);
					// });
					this.personList = res.results;
				}
			});
		},
		//表格行高
		tableRowClassName({ row, rowIndex }) {
			let styleRes = { height: '54px !important' };
			return styleRes;
		},
		inputChange(key, value) {},
		//操作按钮点击事件
		btnClick(res) {
			let code = res.handle.code;
			this.ownId = '';
			switch (code) {
				case 'add':
					this.formTitle = '新增';
					const snowflake = new SnowflakeId();
					this.ownId = snowflake.generate();
					this.formData = { id: this.ownId, status: true };
					this.showForm = true;
					break;
				case 'edit':
					this.formTitle = '编辑';
					this.ownId = res.row.id;
					this.$request({ url: interfaceUrl.info + '/' + res.row.id, method: 'GET' }).then(res => {
						if (res.rCode === 0) {
							this.formData = res.results;
							this.$forceUpdate();
							this.showForm = true;
						}
					});
					break;
				case 'view':
					this.formTitle = '查看';
					this.ownId = res.row.id;
					this.$request({ url: interfaceUrl.info + '/' + res.row.id, method: 'GET' }).then(res => {
						if (res.rCode === 0) {
							this.formData = res.results;
							this.$forceUpdate();
							this.showForm = true;
						}
					});
					break;
				case 'delete':
					this.deleteId = res.row.id;
					this.showDelete = true;
					break;
				case 'appointTimeBtn':
					this.showPsyAppointTime = true;
					this.psyId = res.row.id;
					break;
				default:
					break;
			}
		},
		//表单提交
		handleFormSubmit(data) {
			this.$refs.form.validate(valid => {
				if (!valid) {
					return;
				}
				let formData = data;
				for (let key in formData) {
					if (key.indexOf('_') >= 0) {
						let indexName = key.replace('_', '.');
						formData[indexName] = formData[key];
					}
				}
				let url = '';
				if (this.formTitle === '新增') {
					url = interfaceUrl.save;
				} else if (this.formTitle === '审核') {
					url = interfaceUrl.info + '/' + this.ownId + '/audit';
					formData = {
						isAudit: formData.isAudit,
						auditContent: formData.auditContent
					};
				} else {
					url = interfaceUrl.update;
				}

				this.$request({ url: url, data: formData, method: 'POST' }).then(res => {
					if (res.rCode !== 0) {
						this.$message.error(res.msg);
						return;
					}
					this.$message.success('操作成功');
					this.showForm = false;
					this.formData = {};
					this.$refs.table.reload();
				});
			});
		},
		//删除行
		deleteRow() {
			this.$request({
				url: interfaceUrl.delete,
				data: { id: this.deleteId },
				method: 'POST'
			}).then(res => {
				if (res.rCode === 0) {
					this.$refs.table.reload();
					this.$message.success('删除成功');
				} else {
					this.$message.error(res.msg);
				}
				this.showDelete = false;
			});
		},

		getListThead(btnJson) {
			let tempThead = Object.assign([], this.listThead);
			if (tempThead.length > 7) {
				tempThead.pop();
			}
			tempThead.push(btnJson);
			return tempThead;
		},
		refreshData() {
			this.$refs.table.reload();
		},
		changeTable(val) {
			switch (val.name) {
				case 'status':
					this.changeStatus(val.data.id);
					break;
			}
		},
		//改变数据启用状态
		changeStatus(id) {
			this.$request({
				url: interfaceUrl.updateStatus,
				data: { id: id },
				method: 'POST'
			}).then(res => {
				if (res.success) {
					this.$message.success('操作成功！');
				} else {
					this.$message.error(res.msg);
				}
			});
		}
	}
};
</script>

<style scoped lang="scss">
.content {
	width: 100%;
	height: 100%;
}

.el-dialog__body {
	.content-warning-Btn {
		float: right;
		margin: 10px 0px;
	}
	.btn-box {
		margin-top: 20px;
		display: flex;
		justify-content: flex-end;

		.btn {
			padding: 5px 10px;
			color: #666;
			border: 1px solid #eee;
			cursor: pointer;
			margin-right: 5px;
			border-radius: 4px;
		}
	}
}
</style>
