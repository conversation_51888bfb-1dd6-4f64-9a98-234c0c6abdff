<!--
 @desc: 科研管理系统 立项项目 归档项目 
 @author: WH
 @date: 2023/11/16
 -->
<template>
	<main>
		<es-data-table
			ref="table"
			checkbox
			style="width: 100%"
			:row-style="tableRowClassName"
			:thead="thead"
			:page="true"
			:url="tableUrl"
			:param="param"
			:toolbar="toolbar"
			:response="func"
			method="get"
			stripe
			:border="true"
			close
			@btnClick="btnClick"
			@selection-change="selectionChange"
		></es-data-table>
		<es-dialog :title="title" :visible.sync="visible" size="full" :drag="false" class="is-dialog">
			<Detail
				v-if="visible"
				:id="row.id"
				:project-classify="projectClassify"
				:is-flow-pattern="false"
				:open-type="openType"
			/>
		</es-dialog>
	</main>
</template>

<script>
import { getPorjectList } from '@/api/scientific-sys.js';
import { openWindowHref } from '@/utils/index.js';
import Detail from './detail.vue';
// import { savePigeonhole, saveBatchPigeonhole } from '@/api/scientific-sys.js';
import { createNamespacedHelpers } from 'vuex';
const { mapState } = createNamespacedHelpers('getRoleMap');

export default {
	name: 'ApprovalList',
	components: { Detail },
	data() {
		return {
			idArr: [],
			tableUrl: getPorjectList,
			param: {
				orderBy: 'createTime',
				asc: 'false', // true 升序，false 降序
				state: 5
			},
			projectClassify: '', //0 为纵向项目
			row: {},
			visible: false,
			title: '查看',
			toolbar: [
				{
					type: 'search',
					reset: true,
					contents: [
						{
							type: 'text',
							label: '关键字查询',
							name: 'keyword',
							placeholder: '请输入关键字',
							col: 3
						}
					]
				},
				{
					type: 'button',
					contents: [
						{
							text: '批量归档',
							btnType: 'setBtn',
							type: 'primary'
						}
					]
				},
				{
					type: 'filter',
					contents: [
						{
							type: 'monthrange',
							label: '年度',
							name: 'declareTimeStartAndEnd',
							default: true,
							placeholder: '选择年度日期',
							clearable: true,
							col: 3
						},
						{
							type: 'text',
							label: '部门/二级学院',
							name: 'declareOrgName',
							placeholder: '输入部门/二级学院查询',
							col: 3
						},
						{
							type: 'text',
							label: '申报人 ',
							name: 'createUserName',
							placeholder: '输入申报人查询',
							col: 3
						},
						{
							type: 'select',
							label: '项目类型',
							placeholder: '选择查询',
							name: 'projectType',
							sysCode: 'project_type',
							col: 3
						},
						{
							type: 'text',
							label: '项目名称',
							name: 'projectName',
							placeholder: '输入项目名称查询',
							col: 3
						},
						{
							type: 'select',
							label: '项目分类',
							placeholder: '选择查询',
							name: 'projectClassify',
							sysCode: 'project_classify',
							col: 3
						},
						{
							label: '立项时间',
							type: 'daterange',
							name: 'initiationTimeStartAndEnd',
							unlinkPanels: true,
							col: 3
						},
					]
				}
			],
			openType: 'apply', //查看、申请修改、申请修改审核
			// 当前id
			thead: [
				{
					title: '项目编号',
					field: 'projectNumber',
					align: 'center',
					width: 180,
					showOverflowTooltip: true
					// fixed: true
				},
				{
					title: '项目名称',
					field: 'projectName',
					showOverflowTooltip: true,
					width: 220,
					align: 'center'
				},
				{
					title: '项目分类',
					field: 'projectClassifyTxt',
					align: 'center',
					width: 150
					// required: true,
					// filterable: true,
					// allowCreate: true,
					// defaultFirstOption: true,
					// sysCode: 'project_type',
					// strict: false,
					// config: { controls: false }
					// render: (h, params) => {
					// 	let key = ['自然', '人文', '教研'];
					// 	return h('p', {}, key[Number(params.row.projectType)]);
					// }
				},
				{
					title: '申报人',
					field: 'createUserName',
					// width: 100,
					align: 'center'
				},
				{
					title: '申报人电话',
					field: 'declarePhone',
					align: 'center',
					width: 150
				},
				{
					title: '工作单位',
					field: 'declareOrgName',
					align: 'center',
					width: 170
				},
				{
					title: '立项时间',
					field: 'approvalTime',
					align: 'center',
					width: 180
				},
				/* {
					title: '更新时间',
					field: 'updateTime',
					align: 'center',
					width: 180,
					render: (h, params) => {
						// console.log("222222222",params.row.updateTime.substring(0,params.row.updateTime.Length-9));
						// console.log("333333333",params.row.updateTime);
						let updataTimeStr = '';
						if (params.row.updateTime) updataTimeStr = params.row.updateTime.substring(0, 10);
						return h('p', {}, updataTimeStr);
					}
				}, */
				// {
				// 	title: '已使用经费（元）',
				// 	field: 'expenditureAllUse',
				// 	width: 140,
				// 	align: 'center'
				// },
				{
					title: '项目状态',
					align: 'center',
					field: 'stateTxt',
					// width: '90px',
					fixed: 'right',
					render: (h, params) => {
						let stateTxt = params.row.stateTxt;
						let state = params.row.state;
						return h(
							'el-tag',
							{
								props: {
									size: 'small',
									type: this.projecstate(state)
								}
							},
							stateTxt
						);
					}
				},
				{
					title: '操作',
					type: 'handle',
					width: '100',
					template: '',
					fixed: 'right',
					align: 'center',

					events: [
						{
							text: '查看',
							btnType: 'look'
						},
						// {
						// 	text: '编辑',
						// 	btnType: 'edit'
						// 	// rules: rows => [0, 1, 4, 6, 8].includes(rows.projectStatus)
						// },
						{
							text: '归档',
							btnType: 'pigeonhole',
							rules: rows => {
								return rows.state === '5';
							}
						}
						// {
						// 	text: '资金管理',
						// 	btnType: 'editCapital',
						// 	rules: rows => [0, 2].includes(rows.projectStatus)
						// }
						// {
						// 	text: '删除',
						// 	btnType: 'del'
						// }
					]
				}
			]
		};
	},
	computed: {
		...mapState(['projectScienceManager'])
	},
	watch: {
		projectScienceManager: {
			immediate: true,
			handler(newVal) {
				// projectScienceManager：父级传递的身份标识 如果为科技处老师值为true 可进行修改删除操作，反之不可
				if (newVal === false) {
					this.readonly = true;
				}
				if (typeof newVal == 'boolean' && newVal === true) {
					this.thead[this.thead.length - 1].events.push({
						text: '归档',
						btnType: 'pigeonhole',
						rules: rows => {
							return rows.state === '5';
						}
					});
				}
			}
		}
	},
	methods: {
		/**
		 * 表格行高
		 */
		tableRowClassName({ row, rowIndex }) {
			let styleRes = {
				height: '54px !important'
			};
			return styleRes;
		},
		// 获取项目审核状态
		projecstate(state) {
			let stateCurrent = '';
			// 项目状态（字典编码：project_status）0：待立项； 1：已立项； 2：立项不通过； 3：变更审核中； 4：结题审核中； 5：已结题； 6：已归档； 7：终止；
			switch (state) {
				case '0':
					stateCurrent = 'warning';
					break;
				case '3':
				case '4':
					stateCurrent = 'primary';
					break;
				case '1':
				case '5':
				case '6':
					stateCurrent = 'success';
					break;
				// case '2':
				// 	stateCurrent = 'info';
				// 	break;
				case '2':
				case '7':
					stateCurrent = 'danger';
					break;
				default:
					break;
			}
			return stateCurrent;
		},
		//row=null  考虑到toolbar按钮不存在row
		selectionChange(e) {
			this.idArr = e;
		},
		btnClick({ handle, row = null }) {
			let { text, btnType } = handle;
			this.row = row;
			let btnTask = {
				look: () => {
					this.projectClassify = row?.projectClassify;
					console.log(this.projectClassify, 'this.projectClassify');

					this.visible = true;
					this.openType = 'look';
					// this.openWindow();
				},
				pigeonhole: () => {
					this.formSubmit(row.id);
				},
				setBtn: () => {
					this.formSubmit(this.idArr);
				}
			};
			btnTask[btnType]();
		},
		// 页面加载
		load(text) {
			return this.$.loading(this.$loading, text);
		},
		func(res) {
			let { type, data } = res;
			return data;
		},

		//需要配置路由名称
		openWindow() {
			window.open(
				openWindowHref() +
					this.$router.resolve({
						name: 'scientificDeail',
						query: {
							id: this.row.id,
							openType: this.openType
						}
					}).href
			);
		},
		formSubmit(ids) {
			if (ids.length < 1) return this.$message.warning('请选择要归档项目');
			this.$confirm('此操作将, 会将所选项目归档?', '提示', {
				confirmButtonText: '确定',
				cancelButtonText: '取消',
				type: 'warning'
			})
				.then(() => {
					let params = { ids: ids };
					if (ids instanceof Array) {
						params.ids = ids
							.map(e => {
								return e.id;
							})
							.join(',');
					}
					debugger;
					const loading = this.load('归档中...');
					this.$.ajax({
						// url: ids instanceof Array ? saveBatchPigeonhole : savePigeonhole,
						url: '/ybzy/projectBaseInfo/projectArchive',
						method: 'POST',
						format: true,
						data: params
					}).then(res => {
						if (res.rCode == 0) {
							this.$refs.table.reload();
						} else {
							this.$message.error(res.msg);
						}
						loading.close();
					});
				})
				.catch(error => {
					console.log(error);
					this.$message({
						type: 'info',
						message: '已取消操作'
					});
				});
		}
	}
};
</script>
<style lang="scss" scoped>
main {
	width: 100%;
	height: 100%;
}
.is-dialog > ::v-deep .el-dialog {
	height: 100%;
}
</style>
