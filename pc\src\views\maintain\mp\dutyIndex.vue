<template>
	<div class="content">
		<es-data-table
			ref="table"
			:row-style="tableRowClassName"
			:full="true"
			:fit="true"
			:thead="thead"
			:toolbar="toolbar"
			:border="true"
			:page="pageOption"
			:url="dataTableUrl"
			:numbers="true"
			:param="params"
			close
			form
			@btnClick="btnClick"
			@search="hadeSubmit"
			@reset="resetTable"
		></es-data-table>

		<!-- 编辑 -->
		<es-dialog
			:title="formTitle"
			:visible.sync="showForm"
			width="670px"
			height="520px"
			:drag="false"
			:close-on-click-modal="false"
			:destroy-on-close="true"
		>
			<es-form
				v-if="showForm"
				ref="form"
				:model="formData"
				:contents="formItemList"
				:genre="2"
				collapse
				:submit="formTitle !== '查看'"
				@submit="handleFormSubmit"
				:reset="showForm"
			/>
		</es-dialog>
	</div>
</template>

<script>
import interfaceUrl from '@/http/maintain/applicationApi';
import TimeSelectMore from '@/components/time-select-more.vue';
import SnowflakeId from 'snowflake-id';
export default {
	data() {
		return {
			dataTableUrl: interfaceUrl.dutyListJson,
			showForm: false,

			//维修员列表
			maintenancePersonList: [],

			formData: {},
			formTitle: '查看',
			toolbar: [
				{
					type: 'button',
					contents: [
						{
							text: '新增',
							code: 'add',
							type: 'primary'
						},
						{
							text: '导出',
							code: 'export',
							type: 'primary'
						}
					]
				},
				{
					type: 'search',
					reset: true,
					contents: [
						{
							type: 'select',
							name: 'personType',
							placeholder: '人员类型',
							data: [
								{ label: '内部', value: '0' },
								{ label: '外部', value: '1' }
							],
							clearable: true
						},
						{
							type: 'select',
							placeholder: '维修类别',
							name: 'workType',
							sysCode: 'hq_repair_type',
							'label-key': 'shortName',
							'value-key': 'cciValue',
							clearable: true
						},
						{
							type: 'daterange',
							name: 'dutyDateRange',
							default: true,
							placeholder: '日期',
							clearable: true
						},
						{
							type: 'text',
							name: 'keyword',
							placeholder: '关键字查询',
							clearable: true
						}
					]
				}
			],
			thead: [
				{
					title: '编号',
					align: 'left',
					field: 'number'
				},
				{
					title: '姓名',
					align: 'center',
					field: 'name'
				},
				{
					title: '性别',
					align: 'center',
					field: 'sexTxt'
				},
				{
					title: '联系方式',
					align: 'center',
					field: 'phone'
				},
				{
					title: '所属部门/单位',
					align: 'center',
					field: 'unit',
					showOverflowTooltip: true
				},
				{
					title: '维修类型',
					align: 'center',
					field: 'workTypeTxt'
				},
				{
					title: '人员类型',
					align: 'center',
					field: 'typeTxt'
				},
				{
					title: '值班日期',
					align: 'center',
					field: 'dutyDate'
				},
				// {
				// 	title: '值班开始时间',
				// 	align: 'center',
				// 	field: 'startTime',
				// 	showOverflowTooltip: true
				// },
				// {
				// 	title: '值班结束时间',
				// 	align: 'center',
				// 	field: 'endTime',
				// 	showOverflowTooltip: true
				// },
				{
					title: '值班时间',
					align: 'center',
					field: 'startAndEndTime',
					render: (h, param) => {
						return h('p', null, param.row.startTime + '~' + param.row.endTime);
					}
				},
				{
					title: '操作',
					type: 'handle',
					width: 180,
					template: '',
					events: [
						{
							code: 'view',
							text: '查看'
						},
						{
							code: 'edit',
							text: '编辑'
						},
						{
							code: 'delelt',
							text: '删除'
						}
					]
				}
			],
			pageOption: {
				layout: 'total, sizes, prev, pager, next, jumper',
				pageSize: 20,
				// hideOnSinglePage: true,
				position: 'center',
				current: 1,
				pageNum: 1
			},
			params: {},
			submitFilterParams: {}
		};
	},
	computed: {
		formItemList() {
			const readonly = this.formTitle === '查看';
			return [
				{
					name: 'number',
					label: '编号',
					hide: !readonly,
					readonly: true,
					col: 6
				},
				{
					name: 'name',
					label: '姓名',
					hide: !readonly,
					readonly: true,
					col: 6
				},
				{
					name: 'phone',
					label: '联系电话',
					hide: !readonly,
					readonly: true,
					col: 6
				},
				{
					label: '性别',
					name: 'sexTxt',
					hide: !readonly,
					readonly: true,
					col: 6
				},
				{
					name: 'workTypeTxt',
					label: '维修类别',
					hide: !readonly,
					readonly: true,
					col: 12
				},
				{
					label: '所属单位',
					name: 'unit',
					hide: !readonly,
					readonly: true,
					col: 12
				},
				{
					label: '维修员',
					type: 'select',
					name: 'maintenancePersonId',
					data: this.maintenancePersonList,
					labelKey: 'name',
					multiple: true,
					valueKey: 'id',
					rules: {
						required: true,
						message: '请选择维修人',
						trigger: 'change'
					},
					clearable: true,
					hide: readonly,
					readonly: readonly,
					col: 12
				},
				{
					label: '值班日期',
					name: 'dutyDate',
					type: this.formTitle === '新增' ? 'dates' : 'date',
					placeholder: '请选择值班日期',
					format: 'yyyy-MM-dd',
					rules: {
						required: true,
						message: '请选择值班日期',
						trigger: 'change'
					},
					readonly: readonly,
					col: 12
				},
				{
					type: 'attachment',
					label: '值班时间段',
					name: 'time',
					code: 'USER_IMG',
					ownId: '666666',
					showFileList: false,
					disabled: true,
					col: 12,
					render: h => {
						// const initData = {
						// 	startTime: this.formData.startTime || '',
						// 	endTime: this.formData.endTime || ''
						// };
						const initData = this.formData?.time;
						return h(TimeSelectMore, {
							props: {
								readonly: readonly,
								initData: initData,
								defaultTime: { start: '00:00', end: '24:00' }
							},
							on: {
								change: val => {
									// this.$set(this.formData, 'startTime', val.startTime);
									// this.$set(this.formData, 'endTime', val.endTime);
									this.$set(this.formData, 'time', val);
								}
							}
						});
					},
					rules: {
						required: true,
						message: '请选择值班时间段',
						validator: (rule, value, callback) => {
							if (this.formData.time) {
								callback();
							}
							callback(new Error('请选择值班时间段'));
						},
						trigger: 'change'
					}
				}
			];
		}
	},
	watch: {},
	created() {
		this.getMaintenancePersonList();
	},
	mounted() {},
	methods: {
		/**
		 * 表格行高
		 */
		tableRowClassName({ row, rowIndex }) {
			let styleRes = {
				height: '54px !important'
			};
			return styleRes;
		},

		hadeSubmit(e) {
			this.submitFilterParams = e;
		},
		async getMaintenancePersonList() {
			let { rCode, msg, results } = await this.$.ajax({
				url: interfaceUrl.selectMpList,
				method: 'GET'
			});
			if (rCode == 0) {
				this.maintenancePersonList = results;
			} else {
				this.$message.error(msg);
			}
		},
		/**
		 * 操作按钮点击事件
		 * @param {*} res
		 */
		async btnClick(res) {
			let code = res.handle.code;
			switch (code) {
				case 'add':
					// 新增
					this.formTitle = '新增';
					const snowflake = new SnowflakeId();
					this.formData = { id: snowflake.generate() };
					this.showForm = true;
					break;
				case 'edit':
				case 'view':
					{
						let { rCode, msg, results } = await this.$.ajax({
							url: interfaceUrl.dutyInfo,
							params: { id: res.row.id },
							method: 'GET'
						});
						if (rCode == 0) {
							const formData = {
								...results,
								time: [`${results.startTime}~${results.endTime}`],
								// dutyDate: (results.dutyDate || '').split(',').filter(Boolean),
								maintenancePersonId: (results.maintenancePersonId || '').split(',').filter(Boolean)
							};
							this.formData = formData;
							if (code == 'edit') {
								this.formTitle = '编辑';
							} else {
								this.formTitle = '查看';
							}
							this.showForm = true;
						} else {
							this.$message.error(msg);
						}
					}
					break;
				case 'delelt':
					this.deleteRows(res.row.id);
					break;
				case 'export':
					this.exportFile();
					break;
				default:
					break;
			}
		},
		handleFormSubmit(data) {
			let formData = data;
			let url = '';
			if (this.formTitle == '新增') {
				url = interfaceUrl.dutySave;
				formData.time = formData.time.join(',');
				formData.dutyDate = formData.dutyDate.join(',');
				formData.maintenancePersonId = formData.maintenancePersonId.join(',');
			} else {
				url = interfaceUrl.dutyUpdate;
			}
			this.$request({
				url: url,
				data: formData,
				method: 'POST'
			}).then(res => {
				if (res.rCode == 0) {
					this.$message.success('操作成功');
					this.showForm = false;
					this.formData = {};
					this.$refs.table.reload();
				} else {
					this.$message.error(res.msg);
				}
			});
		},
		deleteRows(id) {
			this.$confirm('确定要删除选中的数据吗？', '删除', {
				confirmButtonText: '确定',
				cancelButtonText: '取消',
				type: 'warning'
			})
				.then(() => {
					this.$request({
						url: interfaceUrl.dutyDel,
						data: { id: id },
						method: 'POST'
					}).then(res => {
						if (res.rCode === 0) {
							this.$message.success('操作完成');
							this.$refs.table.reload();
						} else {
							this.$message.error(res.msg);
						}
					});
				})
				.catch(() => {});
		},
		//导出函数
		exportFile() {
			let isDev = process.env.NODE_ENV === 'development' ? '/api' : '';
			const paramAll = { ...this.params, ...this.submitFilterParams };
			let url = `${isDev}/ybzy/hqMaintenanceDutyRoster/downloadExcel`;
			let urlParams = this.objToUrlParams(paramAll);
			if (urlParams) {
				url += '?' + urlParams;
			}
			window.open(url, '_self');
		},
		objToUrlParams(obj) {
			return Object.keys(obj)
				.filter(key => obj[key] !== '' && obj[key] !== null)
				.map(key => encodeURIComponent(key) + '=' + encodeURIComponent(obj[key]))
				.join('&');
		},
		resetTable() {
			this.submitFilterParams = {};
			this.$refs.table.resetTable();
		}
	}
};
</script>
<style scoped lang="scss">
.content {
	width: 100%;
	height: 100%;
}

.el-dialog__body {
	.content-warning-Btn {
		float: right;
		margin: 10px 0px;
	}
	.btn-box {
		margin-top: 20px;
		display: flex;
		justify-content: flex-end;

		.btn {
			padding: 5px 10px;
			color: #666;
			border: 1px solid #eee;
			cursor: pointer;
			margin-right: 5px;
			border-radius: 4px;
		}
	}
}
::v-deep .el-upload--handle {
	width: 100%;
	.el-upload {
		width: 100%;
	}
}
</style>
