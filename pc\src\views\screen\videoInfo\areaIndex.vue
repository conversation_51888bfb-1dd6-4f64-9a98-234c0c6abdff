<template>
	<div class="content">
		<es-data-table
			ref="table"
			:row-style="tableRowClassName"
			:full="true"
			:fit="true"
			:thead="thead"
			:toolbar="toolbar"
			:border="true"
			:option-data="optionData"
			:page="pageOption"
			:url="dataTableUrl"
			:numbers="true"
			:param="params"
			close
			form
			@btnClick="btnClick"
			@edit="changeTable"
		></es-data-table>
		<es-dialog
			:title="formTitle"
			:visible.sync="showForm"
			width="30%"
			height="320px"
			:drag="false"
			:close-on-click-modal="false"
			:destroy-on-close="true"
		>
			<es-form
				ref="form"
				:model="formData"
				:contents="formItemList"
				:genre="2"
				collapse
				:submit="formTitle !== '查看'"
				@submit="handleFormSubmit"
				:reset="showForm"
			/>
		</es-dialog>
		<es-dialog
			title="删除"
			:visible.sync="showDelete"
			width="20%"
			:close-on-click-modal="false"
			:middle="true"
			:drag="false"
			height="140px"
		>
			<div>确定要删除该条数据吗</div>
			<div class="btn-box">
				<div class="btn theme" @click="deleteRow">确定</div>
				<div class="btn" @click="showDelete = false">取消</div>
			</div>
		</es-dialog>
	</div>
</template>

<script>
import interfaceUrl from '@/http/screen/videoInfo/api';
import SnowflakeId from 'snowflake-id';
export default {
	data() {
		return {
			ownId: '',
			dataTableUrl: interfaceUrl.areaListJson,
			showForm: false,
			showDelete: false,
			formData: {},
			formTitle: '编辑',
			toolbar: [
				{
					type: 'button',
					contents: [
						{
							text: '新增',
							code: 'add',
							type: 'primary'
						}
					]
				},
				{
					type: 'search',
					reset: true,
					contents: [
						{
							type: 'text',
							name: 'keyword',
							placeholder: '关键字查询'
						}
					]
				}
			],
			thead: [
				{
					title: '区域名称',
					align: 'center',
					field: 'areaName'
				},
				{
					title: '区域编码',
					align: 'center',
					field: 'areaCode'
				},
				{
					label: '状态',
					align: 'center',
					field: 'status',
					type: 'switch'
				},
				{
					title: '创建时间',
					align: 'center',
					field: 'createTime'
				},
				{
					title: '创建人',
					align: 'center',
					field: 'createUserName'
				},
				{
					title: '操作',
					type: 'handle',
					events: [
						{
							code: 'view',
							text: '查看'
						},
						{
							code: 'edit',
							text: '编辑'
						},
						{
							code: 'delete',
							text: '删除'
						}
					]
				}
			],
			optionData: {
				status: [
					{
						value: 1,
						name: '启用'
					},
					{
						value: 0,
						name: '停用'
					}
				]
			},
			pageOption: {
				layout: 'total, prev, pager, next, jumper',
				pageSize: 20,
				hideOnSinglePage: false,
				position: 'center',
				current: 1,
				pageNum: 1
			},
			params: {
				asc: 'false',
				orderBy: 'sortNum'
			}
		};
	},
	computed: {
		formItemList() {
			const readonly = this.formTitle === '查看';
			return [
				{
					label: '区域名称',
					name: 'areaName',
					placeholder: '请输入区域名称',
					event: 'multipled',
					rules: {
						required: true,
						message: '请输入区域名称',
						trigger: 'blur'
					},
					verify: 'required',
					maxlength: 100,
					readonly: readonly,
					col: 12
				},
				{
					label: '区域编码',
					name: 'areaCode',
					placeholder: '请输入区域编码',
					event: 'multipled',
					rules: {
						required: true,
						message: '请输入区域编码',
						trigger: 'blur'
					},
					verify: 'required',
					maxlength: 10,
					readonly: readonly,
					col: 12
				},
				{
					type: 'radio',
					label: '启用状态',
					name: 'status',
					event: 'multipled',
					rules: {
						required: false,
						trigger: 'blur'
					},
					verify: 'required',
					data: [
						{
							value: 0,
							name: '停用'
						},
						{
							value: 1,
							name: '启用'
						}
					],
					readonly: readonly,
					col: 12
				}
			];
		}
	},
	watch: {},
	created() {},
	mounted() {},
	methods: {
		/**
		 * 表格行高
		 */
		tableRowClassName({ row, rowIndex }) {
			let styleRes = {
				height: '54px !important'
			};
			return styleRes;
		},
		changeTable(val) {
			switch (val.name) {
				case 'status':
					this.changeStatus(val.data.id, val.data.status);
					break;
			}
		},
		//改变数据启用状态
		changeStatus(id) {
			this.$request({
				url: interfaceUrl.areaUpdateStatus,
				data: { id: id },
				method: 'POST'
			}).then(res => {
				if (res.success) {
					this.$message.success('操作成功！');
				} else {
					this.$message.error(res.msg);
				}
			});
		},
		/**
		 * 操作按钮点击事件
		 * @param {*} res
		 */
		btnClick(res) {
			let code = res.handle.code;
			switch (code) {
				case 'add':
					// 新增
					this.formTitle = '新增';
					const snowflake = new SnowflakeId();
					this.ownId = snowflake.generate();
					this.formData = {
						id: this.ownId,
						status: 0
					};
					this.showForm = true;
					break;
				case 'edit':
					// 编辑
					this.formTitle = '编辑';
					this.ownId = res.row.id;
					this.$request({
						url: interfaceUrl.areaInfo + res.row.id,
						method: 'GET'
					}).then(res => {
						if (res.rCode == 0) {
							this.formData = res.results;
							this.showForm = true;
						}
					});
					break;
				case 'starStatus':
					break;
				case 'view':
					this.formTitle = '查看';
					this.ownId = res.row.id;
					this.$request({
						url: interfaceUrl.areaInfo + res.row.id,
						method: 'GET'
					}).then(res => {
						if (res.rCode == 0) {
							this.formData = res.results;
							this.showForm = true;
						}
					});
					break;
				case 'delete':
					this.deleteId = res.row.id;
					this.showDelete = true;
					break;
				default:
					break;
			}
		},
		handleFormSubmit(data) {
			let url = '';
			if (this.formTitle == '新增') {
				url = interfaceUrl.areaSave;
			} else {
				url = interfaceUrl.areaUpdate;
			}
			this.$request({
				url: url,
				data: data,
				method: 'POST'
			}).then(res => {
				if (res.rCode == 0) {
					this.$message.success('操作成功');
					this.showForm = false;
					this.formData = {};
					this.$refs.table.reload();
				} else {
					this.$message.error(res.msg);
				}
			});
		},
		deleteRow() {
			this.$request({
				url: interfaceUrl.areaDeleteById,
				data: { id: this.deleteId },
				method: 'POST'
			}).then(res => {
				if (res.rCode == 0) {
					this.$refs.table.reload();
					this.$message.success('删除成功！');
				} else {
					this.$message.error(res.msg);
				}
				this.showDelete = false;
			});
		}
	}
};
</script>
<style scoped lang="scss">
@import '../../../assets/style/style.scss';
.el-row,
.el-col,
.table-box {
	height: 100%;
	width: 100%;
}
.content {
	width: 100%;
	height: 100%;
}

.el-dialog__body {
	.btn-box {
		margin-top: 20px;
		display: flex;
		justify-content: flex-end;

		.btn {
			padding: 5px 10px;
			color: #666;
			border: 1px solid #eee;
			cursor: pointer;
			margin-right: 5px;
			border-radius: 4px;
			// &.theme {
			// 	background: $--color-primary;
			// 	color: #fff;
			// 	border-color: $--color-primary;
			// }
		}
	}
}
</style>
