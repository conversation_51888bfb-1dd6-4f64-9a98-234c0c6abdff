<template>
	<div v-loading="loading" class="content">
		<es-data-table
			ref="table"
			:row-style="tableRowClassName"
			:thead="thead"
			:toolbar="toolbar"
			:border="true"
			filter
			:page="pageOption"
			:url="basics.dataTableUrl"
			:numbers="true"
			:param="params"
			show-label
			close
			@search="search"
			@reset="search({})"
			@btnClick="btnClick"
		></es-data-table>
	</div>
</template>

<script>
import { host } from '@/../config/config';
import qs from 'qs';
export default {
	data() {
		return {
			basics: {
				dataTableUrl: '/ybzy/paBaseInfo/statisticsAwardNature', // 列表接口
				download: '/ybzy/paBaseInfo/exportAwardNature' // 导出
			},
			loading: false,
			params: {},
			selectParams: {},
			pageOption: {
				layout: 'total, prev, pager, next, jumper',
				pageSize: 20,
				hideOnSinglePage: true,
				position: 'center',
				current: 1,
				pageNum: 1
			},
			codesObj: {} //批量数据字典
		};
	},
	computed: {
		thead() {
			return [
				{
					title: '获奖项目名称',
					field: 'awardTitle',
					align: 'center',
					// width: 200,
					showOverflowTooltip: true
				},
				{
					title: '获奖单位排序',
					field: 'awardUnitSort',
					align: 'center',
					// minWidth: 120
				},
				{
					title: '奖励类别',
					field: 'rewardCategory',
					align: 'center',
					showOverflowTooltip: true,
					// minWidth: 100
				},
				{
					title: '奖励名称',
					field: 'rewardName',
					align: 'center',
					showOverflowTooltip: true,
					// minWidth: 140
				},
				{
					title: '奖励等级',
					field: 'awardLevel',
					align: 'center',
					showOverflowTooltip: true,
					// minWidth: 140
				},
				{
					title: '学科分类',
					field: 'subjectType',
					align: 'center',
					showOverflowTooltip: true,
					// minWidth: 120
				}
			];
		},
		toolbar() {
			return [
				{
					type: 'button',
					contents: [
						{
							text: '导出',
							exportXls: true,
							icon: 'es-icon-daochu',
							type: 'primary',
							event: () => {
								const params = { ...this.params, ...this.selectParams };
								const url = host + this.basics.download + '?' + qs.stringify(params);
								window.open(url, '_self');
							}
						}
					]
				},
				{
					type: 'search',
					reset: true,
					showLabel: true,
					contents: [
						{
							type: 'daterange',
							name: 'dates',
							label: '时间范围',
							placeholder: '请选择时间范围'
						},
						{ type: 'text', label: '关键字', name: 'keyword', placeholder: '请输入关键字查询' }
					]
				}
			];
		}
	},
	created() {
		const codes = 'member_nation,hq_person_education,hq_person_state';
		this.$utils.findSysCodeList(codes).then(res => {
			this.codesObj = res;
		});
	},
	methods: {
		//表格行高
		tableRowClassName({ row, rowIndex }) {
			let styleRes = { height: '54px !important' };
			return styleRes;
		},
		//操作按钮点击事件
		btnClick(res) {},
		search(res) {
			const paramsN = { ...res };
			if (paramsN.dates) {
				paramsN.startDate = paramsN.dates[0] || '';
				paramsN.endDate = paramsN.dates[1] || '';
				delete paramsN.dates;
			}
			this.params = {
				...{
					projectType: this.params.projectType,
					orderBy: this.params.orderBy,
					asc: this.params.asc // true 升序，false 降序
				},
				...paramsN
			};
			this.selectParams = paramsN;
		},
		refreshData() {
			this.$refs.table.reload();
		}
	}
};
</script>

<style scoped lang="scss">
.content {
	width: 100%;
	height: 100%;
}
</style>
