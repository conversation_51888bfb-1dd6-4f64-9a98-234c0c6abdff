<template>
	<div class="content" style="height: 100%">
		<div style="width: 100%; height: 100%">
			<es-data-table
				v-if="true"
				ref="table"
				:key="tableCount"
				:row-style="tableRowClassName"
				:full="true"
				:fit="true"
				:thead="thead"
				:toolbar="toolbar"
				:page="page"
				:url="dataTableUrl"
				:param="dataTableParam"
				:border="true"
				:numbers="true"
				checkbox
				form
				@btnClick="btnClick"
				@selection-change="handleSelectionChange"
				@edit="changeTable"
			></es-data-table>
			<es-dialog
				v-if="showInfoPage"
				:title="formTitle"
				height="430px"
				width="500px"
				:visible.sync="showInfoPage"
			>
				<infoPage
					ref="infoPage"
					:base-data="formData"
					:info-page-mode="infoPageMode"
					@activelyClose="closeInfoPage"
				></infoPage>
			</es-dialog>
		</div>
	</div>
</template>

<script>
import api from '@/http/society/societyType/api';
import InfoPage from '@/views/society/societyType/infoPage.vue';

export default {
	components: { InfoPage },
	data() {
		return {
			formData: {},
			page: {
				pageSize: 20,
				totalCount: 0
			},
			infoPageMode: 'allOn',
			selectRowData: [],
			selectRowIds: [],
			showInfoPage: false,
			tableCount: 1,
			dialogType: '',
			dataTableUrl: api.societyTypeList,
			dataTableParam: { orderBy: 'create_time', asc: false },
			formTitle: '',
			validityOfDateDisable: false,
			thead: [],
			toolbar: [],
			editToolbar: [
				{
					type: 'button',
					contents: [
						{
							text: '新增',
							code: 'toolbar'
						},
						{
							text: '删除',
							code: 'toolbar',
							type: 'danger'
						},
						{
							text: '查看',
							code: 'toolbar',
							type: 'primary'
						}
					]
				},
				{
					type: 'search',
					reset: true,
					contents: [
						{
							type: 'text',
							name: 'keyword',
							placeholder: '类型名称',
							clearable: true
						}
					]
				}
			],
			listThead: [
				{
					title: '名称',
					align: 'left',
					field: 'name',
					sortable: 'custom',
					showOverflowTooltip: true
				},
				{
					title: '创建人',
					width: '150px',
					align: 'center',
					field: 'createUserName',
					sortable: 'custom',
					showOverflowTooltip: true
				},
				{
					title: '状态',
					width: '150px',
					align: 'center',
					field: 'status'
				},
				{
					title: '创建时间',
					width: '150px',
					align: 'center',
					field: 'createTime',
					sortable: 'custom',
					showOverflowTooltip: true
				}
			],
			btnJson: {
				title: '操作',
				type: 'handle',
				width: 180,
				template: '',
				events: [
					{
						code: 'row',
						text: '查看'
					},
					{
						code: 'row',
						text: '编辑'
					},
					{
						code: 'row',
						text: '删除'
					}
				]
			}
		};
	},
	created() {
		//初始化查询待审核列表
		this.thead = this.getListThead(this.btnJson);
		this.toolbar = this.editToolbar;
	},
	methods: {
		/**
		 * 表格行高
		 */
		tableRowClassName() {
			return {
				height: '54px !important'
			};
		},
		btnClick(res) {
			let text = res.handle.text;
			let code = res.handle.code;

			if (code === 'row') {
				switch (text) {
					case '查看':
					case '编辑':
						this.openInfoPage(res.row.id, text);
						break;
					case '删除':
						this.deleteRows([res.row.id]);
						break;
				}
			} else {
				switch (text) {
					case '新增':
						this.openInfoPage(null, text);
						break;
					case '查看':
						if (this.selectRowIds.length > 1) {
							this.$message.warning('只能选择一个查看');
						} else if (this.selectRowIds.length < 1) {
							this.$message.warning('请选择一个进行查看');
						} else {
							this.openInfoPage(this.selectRowIds[0], text);
						}
						break;
					case '删除':
						if (this.selectRowIds.length < 1) {
							this.$message.warning('请选择一个删除');
						} else {
							this.deleteRows(this.selectRowIds);
						}
						break;
				}
			}
		},
		//打开infoPage
		openInfoPage(id, pageMode) {
			if (pageMode !== '新增') {
				this.$request({
					url: api.societyTypeInfo + '/' + id,
					method: 'GET'
				}).then(res => {
					this.formTitle = pageMode;
					this.formData = { ...res.results };
					// this.formData.status = this.formData.status === 1;
					this.infoPageMode = pageMode;
					this.showInfoPage = true;
				});
			} else {
				this.formTitle = pageMode;
				this.formData = {};
				this.formData.status = true;
				this.infoPageMode = pageMode;
				this.showInfoPage = true;
			}
		},
		//关闭infoPage
		closeInfoPage(reload) {
			this.formData = {};
			this.showInfoPage = false;
			if (reload) this.$refs.table.reload();
		},
		// 数据列表多选回调
		handleSelectionChange(data) {
			let ids = [];
			data.forEach(row => {
				ids.push(row.id);
			});
			this.selectRowData = data;
			this.selectRowIds = ids;
		},
		deleteRows(ids) {
			this.$confirm('确定要删除选中的数据吗？', '删除', {
				confirmButtonText: '确定',
				cancelButtonText: '取消',
				type: 'warning'
			})
				.then(() => {
					this.$request({
						url: api.societyTypeDeleteByIds,
						data: { ids: ids.join(',') },
						method: 'POST'
					}).then(res => {
						if (res.rCode === 0) {
							this.$message.success(res.msg);
							this.$refs.table.reload();
						} else {
							this.$message.error(res.msg);
						}
					});
				})
				.catch(() => {});
		},
		getListThead(btnJson) {
			let tempThead = [...this.listThead];
			tempThead.push(btnJson);
			return tempThead;
		},
		changeTable(val) {
			switch (val.name) {
				case 'status':
					break;
			}
		}
	}
};
</script>
