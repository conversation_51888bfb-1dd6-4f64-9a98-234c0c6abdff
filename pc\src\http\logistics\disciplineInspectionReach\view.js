export default {
	computed: {
		viewItemList() {
			return [
				{
					name: 'title',
					label: '标题',
					value: '',
					col: 6
				},
				{
					name: 'isAnonymity',
					label: '是否匿名',
					type: 'radio',
					col: 6,
					data: [
						{ value: 0, name: '实名' },
						{ value: 1, name: '匿名' }
					]
				},
				{
					name: 'reporterName',
					label: '举报人姓名',
					value: '',
					col: 6
				},
				{
					name: 'reporterIdcard',
					label: '举报人身份证号',
					value: '',
					col: 6
				},
				{
					name: 'reporterPhone',
					label: '举报人联系方式',
					value: '',
					col: 6
				},
				{
					name: 'beReporterName',
					label: '被举报人姓名',
					value: '',
					col: 6
				},
				{
					name: 'beReporterDepartment',
					label: '被举报人单位',
					value: '',
					col: 6
				},
				{
					name: 'beReporterJob',
					label: '被举报人职务',
					value: '',
					col: 6
				},
				{
					name: 'content',
					label: '内容',
					value: '',
					col: 12
				},
				// {
				// 	name: 'filelist',
				// 	label: '文件',
				// 	value: '',
				// 	col: 6
				// },
				{
					name: 'fj',
					label: '附件',
					type: 'attachment',
					value: '',
					preview: true,
					code: 'plat_discipline_inspection_reach',
					ownId: this.formData.id
				}
			];
		}
	}
};
