<template>
	<div class="content">
		<es-data-table
			ref="table"
			:row-style="tableRowClassName"
			:full="true"
			:fit="true"
			:thead="thead"
			:toolbar="toolbar"
			:border="true"
			:page="pageOption"
			:url="dataTableUrl"
			:numbers="true"
			@btnClick="btnClick"
			@sort-change="sortChange"
			:param="params"
			@submit="hadeSubmit"
			close
			form
		></es-data-table>
		<el-dialog :title="formTitle" :visible.sync="open" width="800px" append-to-body>
			<el-form ref="form" :model="form" :rules="rules" label-width="100px">
        <el-form-item label="父级" prop="pid" >
          <el-select style="width: 100%"
              v-model="pidValue"
              placeholder="请选择"
              tree
              :data="pidData"
              value-key="id"
              :props="{children: 'children', label:'name'}"
              labelKey="name"
              @change="handleExpand"
          ></el-select>
        </el-form-item>

				<el-form-item label="名称" prop="name">
					<el-input v-model="form.name" placeholder="请输入名称" />
				</el-form-item>
<!--				<el-form-item label="类型" prop="type">-->
<!--				<el-select v-model="form.type" placeholder="请选择类型" clearable :style="{width: '100%'}">-->
<!--					<el-option v-for="(item, index) in zhddScreenType" :key="index" :label="item.label"-->
<!--							:value="item.value" :disabled="item.disabled"></el-option>-->
<!--				</el-select>-->
<!--				</el-form-item>-->
				<el-form-item label="排序" prop="sortNum">
				<el-input v-model="form.sortNum" placeholder="请输入排序" />
				</el-form-item>
				<el-form-item label="备注" prop="remark">
				<el-input v-model="form.remark" placeholder="请输入备注" />
				</el-form-item>
				<el-form-item label="" prop="lngLat" key="selectDeptList">
					<lng-lat v-model="form.lngLat"></lng-lat>
				</el-form-item>
				<el-form-item label="上传照片" prop="filelist">
					<el-upload
						class="avatar-uploader"
						:action="updUrl"
						multiple
						:limit="8"
						:accept="acceptImg"
						list-type="picture-card"
						:data="{
							ownId: form.id,
							code: 'zhdd_building_photo'
						}"
						:file-list="imageList"
						:before-upload="beforeImageUpload"
						:on-success="handleImageSuccess"
						:on-exceed="onExceed"
						:on-remove="uploadImageRemove"
					>
						<i class="el-icon-plus" size="44"></i>
					</el-upload>
				</el-form-item>
			</el-form>
			<div slot="footer" class="dialog-footer">
				<el-button v-show="formTitle != '查看'" type="primary" @click="submitForm">确 定</el-button>
				<el-button @click="cancel">取 消</el-button>
			</div>
		</el-dialog>
		<es-dialog
			title="删除"
			:visible.sync="showDelete"
			:close-on-click-modal="false"
			:middle="true"
			height="140px"
			@close="imageList = []"
		>
			<div>确定要删除该条数据吗</div>
			<div class="btn-box">
				<div class="btn theme" @click="deleteRow">确定</div>
				<div class="btn" @click="showDelete = false">取消</div>
			</div>
		</es-dialog>
	</div>
</template>

<script>
import interfaceUrl from '@/http/screen/buildingFacilities';
import SnowflakeId from 'snowflake-id';
import LngLat from '@/components/lngLat.vue';
import systemApi from '@/http/common/system.js';

export default {
	name: 'BuildingFacilities', //避难场所
	components: { LngLat },
	data() {
		return {
      allList: [],
      pidValue: '',
      pidData: [],
      type: 1,//层级：1楼栋；2楼层；3房间
      level: 1,//层级：1楼栋；2楼层；3房间
      pid: '',
			ownId: '',
			dataTableUrl: interfaceUrl.listJson,
			showForm: false,
			showDelete: false,
			formData: {},
			formTitle: '编辑',
			editBtn: {
				type: 'submit',
				skin: 'lay-form-btns',
				contents: [
					{
						type: 'primary',
						text: '确定',
						event: 'confirm'
					},
					{
						type: 'reset',
						text: '取消',
						event: 'cancel'
					}
				]
			},
			cancelBtn: {
				type: 'submit',
				skin: 'lay-form-btns',
				contents: [
					{
						type: 'reset',
						text: '取消',
						event: 'cancel'
					}
				]
			},
			toolbar: [
				{
					type: 'button',
					contents: [
						{
							text: '新增',
							code: 'add',
							type: 'primary'
						}
					]
				},
				{
					type: 'search',
					contents: [
            {
              type: 'select',
              label: '下拉选择',
              placeholder: '请选择类型',
              name: 'type',
              event: 'multipled',
              sysCode: 'zhdd_building_type',
              verify: 'required',
              col: 6
            },
						{
							type: 'text',
							name: 'keyword',
							placeholder: '关键字查询'
						}
					]
				}
			],
			thead: [
        {
          title: '父级',
          align: 'left',
          field: 'pidName',
          sortable: 'custom',
          showOverflowTooltip: true
        },
				{
					title: '名称',
					align: 'left',
					field: 'name',
					sortable: 'custom',
					showOverflowTooltip: true
				},
				{
					title: '经度',
					align: 'center',
					field: 'longitude',
					sortable: 'custom',
					showOverflowTooltip: true
				},
				{
					title: '纬度',
					align: 'center',
					field: 'latitude',
					sortable: 'custom',
					showOverflowTooltip: true
				},
				
				{
					title: '操作',
					type: 'handle',
					width: 180,
					template: '',
					events: [
						{
							code: 'edit',
							text: '编辑'
						},
						{
							code: 'view',
							text: '查看'
						},
						{
							code: 'delete',
							text: '删除'
						}
					]
				}
			],
			pageOption: {
				layout: 'total, prev, pager, next, jumper',
				pageSize: 10,
				// hideOnSinglePage: true,
				position: 'right',
				current: 1,
				pageNum: 1
			},
			params: {
				asc: 'true',
				orderBy: 'sortNum'
			},
			open: false,
			// 表单参数
			form: {
				id: null,
				name: null,
				filelist: null,
				longitude: null,
				latitude: null,
				remark: null,
				lngLat: [],
				pwd: null,
        pid: null,
        sortNum: 1,
        level:1,
        type: 1,
			},
			// 表单校验
			rules: {
				name: [{ required: true, message: '需要填写信息', trigger: 'blur' }],
        type: [{ required: true, message: '需要填写信息', trigger: 'blur' }],
        sortNum: [{ required: true, message: '需要填写信息', trigger: 'blur' },
          {
            validator: (rule, value, callback) => {
              if (/^(?:[1-9]\d*)$/.test(value) == false) {
                callback(new Error("请输入正整数"));
              } else {
                callback();
              }
            },
            trigger: "blur",
          }],
			},
			acceptImg: '.jpg,.gif,.jpeg,.png,.JPG,.GIF,.JPEG,.PNG', //上传图片格式
			imgListSize: 1,
			imageList: [],
			updUrl: '/api/ybzy/mecpfileManagement/front/upload', // 文件上传地址
			zhddScreenType: [],//类型
		};
	},
	computed: {},
	watch: {},
	created() {
    this.getDictOption('zhdd_building_type');
    this.getPidData();
    this.getListAll();
  },
	mounted() {},
	methods: {
    /**
     * pid下拉树回调方法
     * @param data
     * @param node
     */
    handleExpand(data, node) {
      console.log(data, node);
      this.level = data.level+1;
      this.type = data.level+1;
      this.pid = data.id;
    },
    /**
     * 获取pid树选项数据
     */
    getPidData(){
      this.pidData = [];
      this.$request({
        url: interfaceUrl.getPidData,
        method: 'GET'
      }).then(res => {
        if (res.rCode == 0 && res.results) {
          this.pidData = res.results;
        }else {
          this.pidData = [];
        }
      });
    },
    /**
     * 获取类型字典
     * @param code
     */
    getDictOption(code){
      this.$request({
        url: systemApi.findSysCodeList,
        data: {
          sysAppCodes: code
        },
        method: 'POST'
      }).then(res => {
        if (res.rCode == 0) {
          let arr = res.results[code];
          for (let i = 0; i < arr.length; i++) {
            this.zhddScreenType.push({label:arr[i].shortName,value:arr[i].cciValue})
          }
        }
      });
    },
		/**
		 * 表格行高
		 */
		tableRowClassName({ row, rowIndex }) {
			let styleRes = {
				height: '54px !important'
			};
			return styleRes;
		},
		inputChange(key, value) {},
		hadeSubmit(data) {},
		/**
		 * 排序变化事件
		 * @param {*} column
		 * @param {*} prop
		 * @param {*} order
		 */
		sortChange(column, prop, order) {
			if (column.order == 'ascending') {
				//升序
				this.params = {
					asc: 'true',
					orderBy: column.prop
				};
			} else if (column.order == 'descending') {
				//降序
				this.params = {
					asc: 'false',
					orderBy: column.prop
				};
			} else {
				//不排序
				this.params = {
					asc: 'false',
					orderBy: 'createTime'
				};
			}
			this.$refs.table.reload();
		},
		btnClick(res) {
			let code = res.handle.code;
			switch (code) {
				case 'add':
					// 新增
          this.reset();
					this.formTitle = '新增';
					const snowflake = new SnowflakeId();
					let id = snowflake.generate();
					this.ownId = id;
					this.form.id = id;
					this.imageList = [];
          this.pidValue = '';
					this.open = true;
					break;
				case 'edit':
					// 编辑
					this.reset();
					this.formTitle = '编辑';
					this.ownId = res.row.id;
					this.$request({
						url: interfaceUrl.info + '/' + res.row.id,
						method: 'GET'
					}).then(res => {
						if (res.rCode == 0) {
              debugger
							this.form = res.results;
              this.pidValue = res.results.pid;
              // this.pid = res.results.pid;
              this.level = res.results.level;
              this.type = res.results.level;
							this.imageList = this.form?.filelist?.split(',').map(adjunctId => ({
								name: adjunctId,
								url: `/api/ybzy/mecpfileManagement/front/previewAdjunct.json?adjunctId=${adjunctId}`
							}));
							this.open = true;
						}
					});
					break;
				case 'view':
					this.reset();
					this.formTitle = '查看';
					this.ownId = res.row.id;
					this.$request({
						url: interfaceUrl.info + '/' + res.row.id,
						method: 'GET'
					}).then(res => {
						if (res.rCode == 0) {
							this.form = res.results;
              this.pidValue = res.results.pid;
              // this.pid = res.results.pid;
              this.level = res.results.level;
              this.type = res.results.type;
							this.imageList = this.form?.filelist?.split(',').map(adjunctId => ({
								name: adjunctId,
								url: `/api/ybzy/mecpfileManagement/front/previewAdjunct.json?adjunctId=${adjunctId}`
							}));
							this.open = true;
						}
					});
					break;
				case 'delete':
					this.deleteId = res.row.id;
					this.showDelete = true;
					break;
				default:
					break;
			}
		},
		//提交按钮
		submitForm() {
			this.$refs['form'].validate(valid => {
				if (valid) {
					let url = '';
					if (this.formTitle == '新增') {
						url = interfaceUrl.save;
						let Base64 = require('js-base64').Base64;
						this.form.password = Base64.encode(this.form.pwd);
					} else {
						url = interfaceUrl.update;
					}

          this.form.pid = this.pidValue;
          this.form.level = this.level;
          this.form.type = this.type;

          if (this.form.lngLat && this.form.lngLat.length == 2) {
            this.form.longitude = this.form.lngLat[0];
            this.form.latitude = this.form.lngLat[1];
          }
          //只有类型为楼栋时才强行要求经纬度
          if (this.form.type == 1){
            if(!this.form.longitude || !this.form.latitude){
              this.$message.error('请正确填写经纬度');
              return false;
            }
          }

					this.$request({
						url: url,
						data: this.form,
						method: 'POST'
					}).then(res => {
						if (res.rCode == 0) {
							this.$refs.table.reload();
							this.$message.success(this.formTitle + '成功');
							this.imageList = [];
              this.getPidData();
              this.getListAll();
							this.cancel();
						} else {
							this.$message.error(res.msg);
						}
						this.showDelete = false;
					});
				}
			});
		},
    /**
     * 删除
     */
    deleteRow() {
      this.$request({
        url: interfaceUrl.deleteBatchIds,
        data: { ids: this.deleteId },
        method: 'POST'
      }).then(res => {
        if (res.rCode == 0) {
          this.$refs.table.reload();
          this.$message.success('删除成功');
          this.getPidData();
          this.getListAll();
        } else {
          this.$message.error(res.msg);
        }
        this.showDelete = false;
      });
    },
		// 取消按钮
		cancel() {
			this.open = false;
			this.reset();
		},
		// 表单重置
		reset() {
			this.form = {
        pid: null,
        id: null,
        name: null,
        type: 1,
        filelist: null,
        longitude: null,
        latitude: null,
        remark: null,
        lngLat: [],
        pwd: null,
        sortNum: 1,
        level:1,
			};
      this.pid = '';
      this.level = 1;
      this.type = 1;
		},
    /**
     * 获取所有对象
     */
    getListAll(){
      this.$request({
        url: interfaceUrl.getListAll,
        method: 'GET'
      }).then(res => {
        if (res.rCode == 0) {
          this.allList = res.results
        }
      });
    },
    /**
     * 根据id获取对象全部属性，因为select选择器回调方法没有生效，才这么做
     * @param id
     */
    getInfoById(id){
      for (let i = 0; i < this.allList.length; i++) {
        if(id==this.allList[i].id){
          return this.allList[i];
        }
      }
    },
		beforeImageUpload(file) {
			const isImgSize = file.size / 1024 / 1024 < this.imgListSize;
			if (!isImgSize) {
				this.$message.error(`上传图片大小不能超过 ${this.imgListSize}MB!`);
			}
			return isImgSize;
		},
		/**
		 * @description 上传图片集合文件
		 */
		handleImageSuccess(res, file, fileList) {
			if (res.success) {
				this.form.filelist = fileList
					.map(file => {
						if (file.response) {
							return file.response.results[0].adjunctId;
						}
						return file.name;
					})
					.join(',');
			} else {
				this.$message.warning('上传失败!');
			}
		},
		// 监听文件删除
		uploadImageRemove(file, fileList) {
			this.form.filelist = fileList
				.map(file => {
					if (file.response) {
						return file.response.results[0].adjunctId;
					}
					return file.name;
				})
				.join(',');
		},
		onExceed(file, fileList) {
			this.$message.error(`超过最多上传数量!`);
		}
	}
};
</script>
<style scoped lang="scss">
@import '../../../assets/style/style.scss';

.content {
	display: flex;
	width: 100%;
	height: 100%;

	::v-deep .es-data-table {
		flex: 1;
		display: flex;
		flex-direction: column;

		.es-data-table-content {
			flex: 1;
			height: 0;
			display: flex;
			flex-direction: column;

			.el-table {
				flex: 1;
				// height: 100% !important;
			}

			.es-thead-border {
				.el-table__header {
					th {
						border-right: 1px solid #e1e1e1;
					}
				}
			}
		}
	}

	::v-deep .el-form-item__label {
		background: none;
		border: 0px solid #c2c2c2;
	}
}

::v-deep {
	.el-dialog {
		max-height: 100%;
		top: 35%;
		.el-dialog__body {
			max-height: 70vh;
			overflow-y: auto;
			.btn-box {
				margin-top: 20px;
				display: flex;
				justify-content: flex-end;

				.btn {
					padding: 5px 10px;
					color: #666;
					border: 1px solid #eee;
					cursor: pointer;
					margin-right: 5px;
					border-radius: 4px;
					// &.theme {
					// 	background: $--color-primary;
					// 	color: #fff;
					// 	border-color: $--color-primary;
					// }
				}
			}
		}
	}
}
</style>
