<template>
    <div class="content">
        <div style="width:100%">
            <el-menu :default-active="activeMenus" mode="horizontal" class="es-menu" @select="handleSelect">
                <el-menu-item v-for="(item, index) in menus" :key="index" :index="String(index)">
                    {{ item.label }}
                </el-menu-item>
            </el-menu>
            <es-data-table :row-style="tableRowClassName" v-if="activeMenus === '0'" ref="table" :key="tableCount" :full="true" :fit="true" :thead="thead" :toolbar="toolbar"
                :border="true" :page="pageOption" :url="dataTableUrl" :numbers="true" @btnClick="btnClick"
                @sort-change="sortChange" :param="params" @submit="hadeSubmit" close>
            </es-data-table>
            <es-data-table :row-style="tableRowClassName"  v-if="activeMenus === '1'" ref="table" :key="tableCount" :full="true" :fit="true" :thead="thead" :toolbar="toolbar"
                :border="true" :page="pageOption" :url="dataTableUrl" :numbers="true" @btnClick="btnClick"
                @sort-change="sortChange" :param="params" @submit="hadeSubmit" close>
            </es-data-table>
        </div>
        <es-dialog
			:title="formTitle"
			:visible.sync="showForm"
			width="60%"
      height="auto"
			:close-on-click-modal="false"
			:destroy-on-close="true"
		>
		<es-form
			ref="form"
			:model="formData"
			:contents="formItemList"
			height="auto"
			:genre="2"
			collapse
			@change="inputChange"
			@submit="handleFormSubmit"
			@reset="showForm = false"
		/>
		</es-dialog>
		<es-dialog
			title="删除"
			:visible.sync="showDelete"
			width="20%"
			:close-on-click-modal="false"
			:middle="true"
			height="140px"
		>
			<div>确定要删除该条数据吗</div>
			<div class="btn-box">
				<div class="btn theme" @click="deleteRow">确定</div>
				<div class="btn" @click="showDelete = false">取消</div>
			</div>
		</es-dialog>
    </div>
</template>

<script>
import interfaceUrl from '@/http/union/unionFinanceType/api.js';
import SnowflakeId from 'snowflake-id';
// import { host } from '../../../../config/config';
export default {
    data() {
        return {
            visible: false,
            addFlag: false,
            auditFlag: false,
            viewFlag: false,
            showDelete: false,
            deleteId: '',
            dialogTitle: "",
            menus: [{ label: '收入' }, { label: '支出' }],
			activeMenus: '0',
			showForm: false,
            queryStatus:[],
            dataTableUrl: interfaceUrl.listJson,
            treeData: [],
            tableCount: 1,
            selectInfo: null,//选中的数据值
            selectRow: null,
            typeDicData:this.typeDicData,
            search: {
                name: 'keyword',
                placeholder: '请输入关键字筛选'
            },
			formTitle: '编辑',
			formData: {},
			editBtn: {
				type: 'submit',
				skin: 'lay-form-btns',
				contents: [
					{
						type: 'primary',
						text: '确定',
						event: 'confirm'
					},
					{
						type: 'reset',
						text: '取消',
						event: 'cancel'
					}
				]
			},
			cancelBtn: {
				type: 'submit',
				skin: 'lay-form-btns',
				contents: [
					{
						type: 'reset',
						text: '取消',
						event: 'cancel'
					}
				]
			},
            toolbar: [
				{
					type: 'button',
					contents: [
						{
							text: '新增',
							code: 'add',
							type: 'primary'
						}
					]
				},
				{
					type: 'search',
					contents: [
						{
							type: 'text',
							name: 'keyword',
							placeholder: '关键字查询'
						}
					]
				}
			],
			thead: [
				{
					title: '名称',
					align: 'left',
					field: 'typeName',
					sortable: 'custom',
					showOverflowTooltip: true,
				},
				{
					title: '收支类型',
					align: 'left',
					field: 'moduleCodeName',
					sortable: 'custom',
					showOverflowTooltip: true,
				},
				{
					title: '创建人',
					align: 'left',
					field: 'createUserName',
					sortable: 'custom',
					showOverflowTooltip: true,
				},
				{
					title: '创建时间',
					align: 'center',
					field: 'createTime',
					sortable: 'custom',
					showOverflowTooltip: true,
				},
				{
					title: '操作',
					type: 'handle',
					width: 180,
					template: '',
					events: [
						{
							code: 'edit',
							text: '编辑'
						},
						{
							code: 'view',
							text: '查看'
						},
						{
							code: 'delete',
							text: '删除'
						}
					]
				}
			],
			pageOption: {
				layout: 'total, prev, pager, next, jumper',
				pageSize: 10,
				// hideOnSinglePage: true,
				position: 'right',
				current: 1,
				pageNum: 1
			},
			params: {
				asc: 'false',
				orderBy: 'createTime',
				moduleCode: '1',
			}
        };
    },
    computed: {
		formItemList() {
			return [
				{
					label: '分类名称',
					name: 'typeName',
					placeholder: '请输入分类名称',
					event: 'multipled',
					rules: {
						required: true,
						message: '请输入分类名称',
						trigger: 'blur'
					},
					verify: 'required',
					col: 11
				},
				{
					label: '所属类别',
					name: 'moduleCode',
					placeholder: '请选择所属类别',
					type: 'radio',
					rules: {
						required: true,
						message: '请选择所属类别',
						trigger: 'blur'
					},
					verify: 'required',
					col: 11,
          disabled: true,
					sysCode: 'union_finance_module',
					'label-key': 'shortName',
					'value-key': 'cciValue'
				},

			];
		},
    },
    watch: {
        dataTableUrl() {
            this.tableCount++;
        }
    },
    created() {
		
    },
    mounted() {

    },
    methods: {
        /**
         * 页签切换
         * @param {*} res 
         */
        handleSelect(res) {
          this.params["moduleCode"] = parseInt(res) + 1;
          this.tableCount++;
        },
        hadeSubmit(data) {
            console.log("hadeSubmit", data);
        },
        deleteRow() {
			this.$request({
				url: interfaceUrl.deleteBatchIds,
				data: { ids: this.deleteId },
				method: 'POST'
			}).then(res => {
				if (res.rCode == 0) {
					this.$refs.table.reload();
					this.$message.success('删除成功');
				} else {
					this.$message.error(res.msg);
				}
				this.showDelete = false;
			});
        },
		inputChange(key, value) {
			
		},
        /**
         * 表格行高
         */
        tableRowClassName({row, rowIndex}) {
            let styleRes = {
                "height": "54px !important"
            }
            return styleRes
            
        },
		/**
		 * 操作按钮点击事件
		 * @param {*} res
		 */
		btnClick(res) {
			let code = res.handle.code;
			switch (code) {
				case 'add':
					// 新增
					this.formTitle = '新增';
					this.editModule(this.formItemList, []);
					const snowflake = new SnowflakeId();
					let id = snowflake.generate();
					this.ownId = id;
          console.log(this.params);
					this.formData = { id: id, unionId: this.clickNode, moduleCode: this.params.moduleCode.toString() };
          console.log(this.formData);
					this.showForm = true;
					break;
				case 'edit':
					// 编辑
					this.formTitle = '编辑';
					this.ownId = res.row.id;
					this.editModule(this.formItemList, ['pwd']);
					this.$request({
						url: interfaceUrl.info + '/' + res.row.id,
						method: 'GET'
					}).then(res => {
						if (res.rCode == 0) {
							this.formData = res.results;
							this.showForm = true;
						}
					});
					break;
				case 'view':
					this.formTitle = '查看';
					this.ownId = res.row.id;
					this.readModule(this.formItemList, ['pwd']);
					this.$request({
						url: interfaceUrl.info + '/' + res.row.id,
						method: 'GET'
					}).then(res => {
						if (res.rCode == 0) {
							this.formData = res.results;
							this.showForm = true;
						}
					});
					break;
				case 'setRole':
					this.showRoleForm = true;
					this.roleFormData.userId = res.row.id;
					this.roleFormData.username = res.row.username;
					this.roleFormData.phone = res.row.phone;
					// 获取企业选择列表
					this.$request({
						url: interfaceUrl.enpSelectList,
						method: 'GET',
						params: {
							userId: res.row.id,
						}
					}).then(res => {
						if (res.rCode == 0) {
							this.enpList = res.results;
						}
					});
					break;
				case 'delete':
					this.deleteId = res.row.id;
					this.showDelete = true;
					break;
				default:
					break;
			}
		},
		handleClose() {},
		handleFormItemChange() {},
		handleFormSubmit(data) {
			let formData = data;
			delete formData.extMap;
			let url = '';
			if (this.formTitle == '新增') {
				url = interfaceUrl.save;
			} else {
				url = interfaceUrl.update;
			}
			this.$request({
				url: url,
				data: formData,
				method: 'POST'
			}).then(res => {
				if (res.rCode == 0) {
					this.$message.success('操作成功');
					this.showForm = false;
					this.formData = {};
					this.$refs.table.reload();
				} else {
					this.formData.state = 1 == this.formData.state ? true : false;
					this.$message.error(res.msg);
				}
			});
		},
		/**
		 * 编辑模式
		 */
		editModule(list, hideField) {
			for (var i in list) {
				var item = list[i];
				item.readonly = false;
				if (undefined != hideField && hideField.length > 0 && hideField.indexOf(item.name) > -1) {
					item.hide = true;
				}
			}
			list.push(this.editBtn);
		},
		/**
		 * 只读模式
		 */
		readModule(list, hideField) {
			for (var i in list) {
				var item = list[i];
				item.readonly = true;
				if (undefined != hideField && hideField.length > 0 && hideField.indexOf(item.name) > -1) {
					item.hide = true;
				}
			}
			list.push(this.cancelBtn);
		},
		changeTable(val) {
			if ('state' === val.name) {
				this.$request({
					url: interfaceUrl.updateState,
					data: {
						id: val.data.id,
						state: val.value
					},
					method: 'POST'
				}).then(res => {
					if (res.rCode == 0) {
						this.$message.success('操作成功');
					} else {
						this.$message.error(res.msg);
					}
				});
			}
		},
        /**
         * 排序变化事件
         * @param {*} column 
         * @param {*} prop 
         * @param {*} order 
         */
        sortChange(column, prop, order) {
            console.log(column);
            if (column.order == 'ascending') {//升序
                this.params = {
                    asc: "true",
                    orderBy: column.prop
                }

            } else if (column.order == 'descending') {//降序 
                this.params = {
                    asc: "false",
                    orderBy: column.prop
                }
            } else { //不排序
                this.params = {
                    asc: "false",
                    orderBy: "t1.applyTime"
                }
            }
            this.params["statusArray"] = this.queryStatus;
            this.$refs.table.reload()
        },
        dialogCancel(){
            this.visible = false;
            this.addFlag = false;
            this.auditFlag = false;
            this.viewFlag = false;
        },
        refreshData() {
            this.dialogCancel();
            this.$refs.table.reload()
		},
        //获取列表thead
        getListThead(btnJson){
            let tempThead = this.listThead;
            if(tempThead.length>7){
                tempThead.pop();
            }
            tempThead.push(btnJson);
            return tempThead;
        },
        /**
         * 获取下拉字典
         */
         getDictionary(){
             
         },
    }
};
</script>
<style scoped lang="scss">
@import '../../../assets/style/style.scss';

.content {
    display: flex;
    width: 100%;
    height: 100%;

    ::v-deep .es-data-table {
        flex: 1;
        display: flex;
        flex-direction: column;
        height: calc(100% - 58px);

        .es-data-table-content {
            flex: 1;
            height: 0;
            display: flex;
            flex-direction: column;

            .el-table {
                flex: 1;
                // height: 100% !important;
            }

            .es-thead-border {
                .el-table__header {
                    th {
                        border-right: 1px solid #E1E1E1;
                    }
                }
            }
        }
    }

    ::v-deep .el-form-item__label {
        background: none;
        border: 0px solid #c2c2c2;
    }
}

.el-dialog__body {
    .btn-box {
        margin-top: 20px;
        display: flex;
        justify-content: flex-end;

        .btn {
            padding: 5px 10px;
            color: #666;
            border: 1px solid #eee;
            cursor: pointer;
            margin-right: 5px;
            border-radius: 4px;
            // &.theme {
            // 	background: $--color-primary;
            // 	color: #fff;
            // 	border-color: $--color-primary;
            // }
        }
    }


}

::v-deep .el-radio__input.is-disabled + .el-radio__label {
  color: #5a5a5a !important;
}

::v-deep .el-radio__input.is-disabled.is-checked + .el-radio__label {
  color: #1890ff !important;
}

::v-deep .el-radio__input.is-disabled.is-checked .el-radio__inner {
  background-color: #b7e4f8 !important;
  border-color: #1890ff !important;
}
</style>
