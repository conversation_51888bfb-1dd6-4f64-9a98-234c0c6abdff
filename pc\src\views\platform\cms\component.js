/**
 * 模型管理相关接口
 */
import { host } from '../../../../config/config';
const action = host + '/ybzy/cmsinfo/singleUpload';
const propsHttp = {
	res: 'results',
	name: 'sourceName',
	url: 'fileOppositePath',
	home: host + 'ybzyfile'
};
const props = { label: 'title', value: 'value' };
const componentList = [
	{
		value: 'input',
		label: '文本输入框',
		option: { overHidden: true }
	},
	{
		value: 'number',
		label: '数字输入',
		option: {}
	},
	{
		value: 'ueditor',
		label: '富文本',
		option: { formslot: true }
	},
	{
		value: 'date',
		label: '日期',
		option: { format: 'yyyy-MM-dd', valueFormat: 'yyyy-MM-dd' }
	},
	{
		value: 'time',
		label: '时间',
		option: { format: 'HH:mm:ss', valueFormat: 'HH:mm:ss' }
	},
	{
		value: 'datetime',
		label: '日期时间',
		option: { format: 'yyyy-MM-dd HH:mm:ss', valueFormat: 'yyyy-MM-dd HH:mm:ss' }
	},
	{
		value: 'upload-fileBtn',
		label: '多文件上传-点击上传',
		option: {
			type: 'upload',
			listType: 'default',
			loadText: '文件上传中，请稍等',
			limit: 10,
			action: action,
			props: props,
			propsHttp: propsHttp
		}
	},
	{
		value: 'upload-fileDrag',
		label: '多文件上传-拖拽或点击上传',
		option: {
			type: 'upload',
			listType: 'default',
			drag: true,
			limit: 10,
			action: action,
			props: props,
			propsHttp: propsHttp
		}
	},
	{
		value: 'upload-picture',
		label: '多图片上传-缩略图回显',
		option: {
			type: 'upload',
			listType: 'picture',
			limit: 10,
			action: action,
			props: props,
			propsHttp: propsHttp
		}
	},
	{
		value: 'upload-pictureCard',
		label: '多图片上传-图回显',
		option: {
			type: 'upload',
			listType: 'picture-card',
			limit: 10,
			action: action,
			props: props,
			propsHttp: propsHttp
		}
	},
	{
		value: 'upload-pictureImg',
		label: '单图片上传-图回显',
		option: {
			type: 'upload',
			listType: 'picture-img',
			action: action,
			props: props,
			propsHttp: propsHttp
		}
	},
	{
		value: 'map',
		label: '地图经纬度选择',
		option: { formslot: true }
	},
	{
		value: 'select-info',
		label: '信息选择',
		option: {
			type: 'select',
			filterable: true,
			props: { label: 'title', value: 'id' }
		}
	},
	{
		value: 'select-node',
		label: '栏目选择',
		option: {
			type: 'tree',
			filterable: true,
			checkStrictly: true,
			props: { label: 'name', value: 'id' }
		}
	}
];
export default componentList;
