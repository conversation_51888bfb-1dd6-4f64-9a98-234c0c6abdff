<template>
	<el-dialog
		:title="title"
		width="650px"
		:visible.sync="visible"
		:modal-append-to-body="false"
		@close="beforeClose"
	>
		<el-form :model="form" label-width="80px">
			<el-form-item label="报表名称">
				<el-input v-model="form.name" placeholder="请填写"></el-input>
			</el-form-item>
			<el-form-item label="填报周期">
				<el-select v-model="form.circle" style="width: 100%" placeholder="请选择">
					<el-option label="日报" value="0"></el-option>
					<el-option label="周报" value="1"></el-option>
					<el-option label="月报" value="2"></el-option>
					<el-option label="年报" value="3"></el-option>
				</el-select>
			</el-form-item>
			<el-form-item label="报表状态">
				<el-select v-model="form.status" style="width: 100%" placeholder="请选择">
					<el-option label="待填报" value="0"></el-option>
					<el-option label="已填报" value="1"></el-option>
				</el-select>
			</el-form-item>
			<el-form-item label="填报期限" placeholder="请选择">
				<el-date-picker
					style="width: 100%"
					v-model="form.limit"
					type="daterange"
					range-separator="至"
					start-placeholder="开始日期"
					end-placeholder="结束日期"
				></el-date-picker>
			</el-form-item>
			<el-form-item label="填报时间" placeholder="请选择">
				<el-date-picker
					style="width: 100%"
					v-model="form.time"
					type="date"
					placeholder="选择日期"
				></el-date-picker>
			</el-form-item>
		</el-form>
		<div slot="footer" class="dialog-footer">
			<el-button @click="handleCancle">取 消</el-button>
			<el-button type="primary" @click="handleSure">确 定</el-button>
		</div>
	</el-dialog>
</template>

<script>
export default {
	props: {
		visible: {
			type: Boolean,
			default: false
		},
		title: {
			type: String,
			default: '信息查看'
		}
	},
	data() {
		return {
			form: {}
		};
	},
	methods: {
		handleSure() {
			console.log(123);
			this.$emit('closeDialog');
		},
		handleCancle() {
			this.$emit('closeDialog');
		}
	}
};
</script>

<style lang="scss" scoped>
::v-deep {
	.el-dialog {
		max-height: 100%;
		top: 20%;
		.el-dialog__body {
			max-height: 70vh;
			overflow-y: auto;
		}
	}
}
</style>
