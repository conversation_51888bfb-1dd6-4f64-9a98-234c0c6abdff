<template>
	<div v-loading="loading" class="process-page">
		<div class="process-page-con" :style="{ width: isShrink ? pageWidth + '%' : '100%' }">
			<el-menu
				v-if="flowData.pendingId || !isEdit"
				:default-active="activeIndex"
				class="el-menu-demo"
				mode="horizontal"
				@select="tabClick"
			>
				<el-menu-item index="1">流程详情</el-menu-item>
				<el-menu-item index="2">签批意见</el-menu-item>
				<el-menu-item index="3">流程追踪</el-menu-item>
			</el-menu>

			<div ref="refFlowBox" class="flowBox">
				<div v-show="activeIndex === '1'" class="formbox">
					<div class="contentBox">
						<slot></slot>
					</div>
				</div>
				<es-flow-list
					v-if="activeIndex === '2'"
					:business-id="flowData.businessId || flowData.recordId || flowData.recordid"
					style="height: 100%"
				></es-flow-list>
				<iframe
					v-if="activeIndex === '3' && iframeFlowUrl"
					id="diagramFrame"
					name="diagramFrame"
					frameborder="0"
					width="100%"
					scrolling="no"
					style="height: 90vh"
					:src="iframeFlowUrl"
				></iframe>
			</div>
		</div>
		<es-flow
			v-if="isFlow"
			:style="{ width: isShrink ? 100 - pageWidth * 1 + '%' : '50px' }"
			class="flow-popup"
			:is-start-flow="flowData.businessId ? true : false"
			:pending-id="flowData.pendingId"
			:flow-type-code="flowData.flowTypeCode"
			:business-id="flowData.businessId"
			:default-process-key="flowData.defaultProcessKey"
			:btn-list="isEdit ? btnList : [{ text: '提交', event: 'sub', type: 'primary' }]"
			:sub-fun="subFun"
			@success="handleSuccess"
			@save="handleSave"
			@shrink="
				e => {
					isShrink = !e;
				}
			"
		></es-flow>
	</div>
</template>

<script>
export default {
	name: 'ProcessPage',
	props: {
		// 展示表单|页面宽度 单位-百分比 字符串或者数子
		pageWidth: {
			type: [String, Number],
			default: 75
		},
		btnList: {
			type: Array,
			default: () => [
				{ text: '提交', event: 'sub', type: 'primary' },
				{ text: '暂存', event: 'save', disabled: true }
			]
		},
		flowDataProps: {
			//流程配置
			type: Object,
			default: () => {
				return {
					appId: '', // 流程图id
					pendingId: '', // 待办 id (办理流程必传,发起流程不用)  pendingId
					flowTypeCode: '', // 流程code码 没有pendingId时候必传 (isStartFlow 会为 true)
					businessId: '', // 业务id 有 businessId 则为发起节点 isStartFlow=true  businessId=form.id=recordId
					defaultProcessKey: '', // 默认关联流程 key
					isEdit: true, // 是否编辑
					isFlow: true // 是否显示侧边流程操作
					// itemname: '' // 没有itemname说明走完流程后，不显右侧操作
				};
			}
		}
	},
	data() {
		return {
			title: '',
			loading: false,
			keyI: 0,
			activeIndex: '1',
			flowData: {
				appId: '',
				pendingId: '',
				flowTypeCode: '',
				businessId: '',
				defaultProcessKey: ''
			},
			isEdit: true, // 组件是否编辑
			isFlow: true, // 是否显示右边流程功能
			isShrink: true // 是否收缩-发起流程
		};
	},
	computed: {
		// 流程图
		iframeFlowUrl() {
			// return '/api/bpm/task/taskHandle/toFlowChartView.dhtml?appId=edb8c38b5480431ba4ab197b83a43bba';
			return (
				this.$host +
				'/bpm/task/taskHandle/toFlowChartView.dhtml?businessId=' +
				(this.flowData.businessId || this.flowData.recordId || this.flowData.recordid) +
				'&appId=' +
				this.flowData.appId
			);
		}
	},
	watch: {
		flowDataProps: {
			handler(val) {
				this.initPage();
			},
			deep: true
		}
	},
	created() {
		this.tabClick('1');
		this.initPage();
	},

	methods: {
		// 初始化页面
		initPage() {
			this.flowData =
				this.$route.query.pendingId !== undefined ? this.$route.query : this.flowDataProps;
			this.flowData.appId = this.flowDataProps.appId;
			// this.flowData = {
			// 	...this.flowDataProps,
			// 	...(this.$route?.query || {})
			// };
			// this.flowData.pendingId = this.flowData.pendingId || this.flowData.id;
			// 编辑功能
			if (this.flowData?.isEdit + '' === 'true') {
				this.isEdit = true;
			} else {
				this.isEdit = false;
			}

			let isFlow = this.flowData?.isFlow ? true : false; //是否显示右边流程功能
			// 有pendingId说明是流程的审核页面，否则是弹窗的流程
			if (this.flowData?.pendingId) {
				// 没有itemname说明走已经走完流程，isLook为true
				isFlow = this.flowData?.itemname ? true : false;
			}
			this.isShrink = isFlow;
			this.isFlow = isFlow;
			// /project-ybzy/ybzy/index.html#/leaveFlow?recordId=[recordid]&pendingId=[pendingId]&isEdit=true
			// /service/dist-project/#/process-page?url=/project/add-project-todo&flowTypeCode=produce&defaultProcessKey=produce_add&isEdit=true
		},
		tabClick(e) {
			this.activeIndex = e;
		},
		// 保存
		subFun(callBank) {
			// 是编辑的话交给页面处理完，再回调
			if (this.isEdit) {
				this.$emit('subFun', callBank);
				return;
			}
			callBank();
		},
		// 提交成功
		handleSuccess(e) {
			this.$emit('handleSuccess', e);
		},
		// 暂存
		handleSave(id, type) {
			this.$emit('subFun');
		}
	}
};
</script>

<style lang="scss" scoped>
.process-page {
	padding-top: 0;
	padding-bottom: 0;
	height: 100%;
	// overflow: hidden;
	overflow-y: scroll;
	display: flex;

	.process-page-con {
		width: 100%;

		.flowBox {
			height: calc(100vh - 45px);
			.formbox {
				// display: flex;
				width: 100%;
				height: 100%;
				padding-left: 20px;
				.contentBox {
					height: 100%;
				}
			}
		}
	}
	.flow-popup {
		border-left: 1px solid #e6eaf0;
	}
}
</style>
