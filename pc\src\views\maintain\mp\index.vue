<template>
	<div style="width: 100%; height: 100%">
		<es-data-table
			v-if="true"
			ref="table"
			:key="tableCount"
			:row-style="tableRowClassName"
			:full="true"
			:fit="true"
			:thead="thead"
			:toolbar="toolbar"
			:page="page"
			:url="dataTableUrl"
			:param="dataTableParam"
			:border="true"
			:numbers="true"
			form
			@btnClick="btnClick"
		></es-data-table>
		<!-- 新增内部维修员 -->
		<es-dialog
			v-if="showAddPage"
			title="新增内部维修员"
			:visible.sync="showAddPage"
			width="1000px"
			height="660px"
			:show-scale="false"
			:drag="false"
			append-to-body
		>
			<addMpPage @closeAddPage="closeAddPage"></addMpPage>
		</es-dialog>
		<!-- 查看or编辑 -->
		<es-dialog
			v-if="showViewPage"
			:title="formTitle"
			:visible.sync="showViewPage"
			width="640px"
			height="580px"
			:show-scale="false"
			:drag="false"
			append-to-body
		>
			<div v-loading="customLoading">
				<es-form
					v-if="showViewPage"
					ref="form"
					:model="formData"
					:contents="formItemList"
					collapse
					@submit="handleFormSubmit"
					@reset="showViewPage = false"
				/>
			</div>
		</es-dialog>
		<!-- 自定义内部维修人员 -->
		<es-dialog
			v-if="showAddCustomInPage"
			title="自定义内部维修员"
			:visible.sync="showAddCustomInPage"
			width="640px"
			height="580px"
			:show-scale="false"
			:drag="false"
			append-to-body
		>
			<div v-loading="customLoading">
				<es-form
					v-if="showAddCustomInPage"
					ref="form"
					:model="formData"
					:contents="formCustomInItemList"
					collapse
					@submit="handleCustomInFormSubmit"
					@reset="showAddCustomInPage = false"
				/>
			</div>
		</es-dialog>
	</div>
</template>

<script>
import api from '@/http/maintain/applicationApi';
import addMpPage from '@/views/maintain/mp/components/manage.vue';
import SnowflakeId from 'snowflake-id';

export default {
	components: { addMpPage },
	data() {
		return {
			page: {
				pageSize: 20,
				totalCount: 0
			},
			//弹出配置
			showAddPage: false,
			showViewPage: false,

			formTitle: '新增外部维修员',
			formData: null,

			tableCount: 1,
			dataTableUrl: api.getListOfMp,
			dataTableParam: {
				roleId: 'b230f6bcb6c343da9606d74a51ad1cc9',
				orderBy: 'create_time',
				asc: false
			},
			thead: [],
			toolbar: [],
			editToolbar: [
				{
					type: 'button',
					contents: [
						{
							text: '新增内部维修员',
							code: 'addIn',
							type: 'primary'
						},
						{
							text: '自定义内部维修员',
							code: 'addCustomIn',
							type: 'primary'
						},
						{
							text: '新增外部维修员',
							code: 'addOut',
							type: 'primary'
						}
					]
				},
				{
					type: 'search',
					reset: true,
					contents: [
						{
							type: 'text',
							name: 'keyword',
							placeholder: '关键字查询',
							clearable: true
						}
					]
				}
			],
			listThead: [
				{
					title: '编号',
					align: 'left',
					field: 'number',
					showOverflowTooltip: true
				},
				{
					title: '姓名',
					align: 'center',
					field: 'name',
					showOverflowTooltip: true
				},
				{
					title: '性别',
					align: 'center',
					field: 'sex',
					showOverflowTooltip: true,
					render: (h, param) => {
						return h('p', null, param.row.sex == '0' ? '男' : '女');
					}
				},
				{
					title: '联系方式',
					align: 'center',
					field: 'phone',
					showOverflowTooltip: true
				},
				{
					title: '所属部门/单位',
					align: 'center',
					field: 'unit',
					showOverflowTooltip: true
				},
				{
					title: '维修类型',
					align: 'center',
					field: 'workTypeTxt',
					showOverflowTooltip: true
				},
				{
					title: '人员类型',
					align: 'center',
					field: 'type',
					render: (h, param) => {
						return h('p', null, param.row.type == '0' ? '内部人员' : '外部人员');
					}
				},
				{
					title: '创建时间',
					align: 'center',
					field: 'createTime',
					showOverflowTooltip: true
				}
			],
			btnJson: {
				title: '操作',
				type: 'handle',
				width: 150,
				template: '',
				events: [
					{
						code: 'view',
						text: '查看'
					},
					{
						code: 'edit',
						text: '编辑'
					},
					{
						code: 'delete',
						text: '删除'
					}
				]
			},
			// 自定义内部维修员数据
			customLoading: false,
			showAddCustomInPage: false,
			formCustomInItemList: [
				{
					name: 'number',
					label: '编号',
					placeholder: '请输入编号',
					maxlength: 20,
					col: 12
				},
				{
					name: 'name',
					label: '姓名',
					placeholder: '请输入姓名',
					rules: {
						required: true,
						message: '请输入姓名',
						trigger: 'blur'
					},
					maxlength: 20,
					col: 12
				},
				{
					name: 'phone',
					label: '联系电话',
					placeholder: '请输入联系电话',
					rules: {
						required: true,
						trigger: 'blur',
						validator: (rule, value, callback) => {
							if (!rule.required) {
								return callback();
							}
							if (!value) {
								return callback(new Error('请输入联系电话'));
							}
							if (/^(?:(?:\+|00)86)?1[3-9]\d{9}$|^0[1-9]\d{1,2}-\d{7,8}$/.test(value)) {
								callback();
							} else {
								callback(new Error('请输入正确的电话格式'));
							}
						}
					},
					col: 12
				},
				{
					name: 'idCard',
					label: '身份证号',
					placeholder: '请输入身份证号',
					rules: {
						required: true,
						message: '请输入身份证号',
						trigger: 'blur'
					},
					maxlength: 18,
					col: 12
				},
				{
					label: '性别',
					name: 'sex',
					placeholder: '请选择性别',
					type: 'select',
					data: [
						{ label: '男', value: '0' },
						{ label: '女', value: '1' }
					],
					'label-key': 'label',
					'value-key': 'value',
					rules: {
						required: true,
						message: '请选择性别',
						trigger: 'change'
					},
					col: 12
				},
				{
					type: 'select',
					name: 'workType',
					label: '维修类别',
					placeholder: '请选择维修类别',
					sysCode: 'hq_repair_type',
					'label-key': 'shortName',
					'value-key': 'cciValue',
					rules: {
						required: true,
						message: '请选择维修类别',
						trigger: 'change'
					},
					col: 12
				},
				{
					label: '备注',
					type: 'textarea',
					name: 'remark',
					placeholder: '请输入...',
					maxlength: 250
				}
			]
		};
	},
	computed: {
		formItemList() {
			const readonly = this.formTitle === '查看';
			const isInPerson = this.formData.type == '0';
			return [
				{
					name: 'number',
					label: '编号',
					placeholder: '请输入编号',
					rules: {
						required: true,
						message: '请输入编号',
						trigger: 'blur'
					},
					readonly: readonly || isInPerson,
					maxlength: 20,
					col: 12
				},
				{
					name: 'name',
					label: '姓名',
					placeholder: '请输入姓名',
					rules: {
						required: true,
						message: '请输入姓名',
						trigger: 'blur'
					},
					readonly: readonly || isInPerson,
					maxlength: 20,
					col: 12
				},
				{
					name: 'phone',
					label: '联系电话',
					placeholder: '请输入联系电话',
					rules: {
						required: true,
						trigger: 'blur',
						validator: (rule, value, callback) => {
							if (!rule.required) {
								return callback();
							}
							if (!value) {
								return callback(new Error('请输入联系电话'));
							}
							if (/^(?:(?:\+|00)86)?1[3-9]\d{9}$|^0[1-9]\d{1,2}-\d{7,8}$/.test(value)) {
								callback();
							} else {
								callback(new Error('请输入正确的电话格式'));
							}
						}
					},
					readonly: readonly,
					col: 12
				},
				{
					label: '性别',
					name: 'sex',
					placeholder: '请选择性别',
					type: 'select',
					data: [
						{ label: '男', value: '0' },
						{ label: '女', value: '1' }
					],
					'label-key': 'label',
					'value-key': 'value',
					rules: {
						required: true,
						message: '请选择性别',
						trigger: 'change'
					},
					readonly: readonly,
					col: 12
				},
				{
					type: 'select',
					name: 'workType',
					label: '维修类别',
					placeholder: '请选择维修类别',
					sysCode: 'hq_repair_type',
					'label-key': 'shortName',
					'value-key': 'cciValue',
					rules: {
						required: true,
						message: '请选择维修类别',
						trigger: 'change'
					},
					readonly: readonly,
					col: 12
				},
				{
					label: '所属单位',
					name: 'unit',
					placeholder: '请输入所属单位',
					readonly: readonly || isInPerson,
					col: 12
				},
				{
					label: '备注',
					type: 'textarea',
					name: 'remark',
					placeholder: '请输入...',
					readonly: readonly,
					maxlength: 250
					// rows: 5
				},
				{
					type: 'submit',
					skin: 'lay-form-btns',
					contents: [
						{
							type: 'primary',
							text: '确定',
							event: 'confirm',
							hide: readonly
						},
						{
							type: 'reset',
							text: '取消',
							event: 'cancel'
						}
					]
				}
			];
		}
	},
	created() {
		//初始化查询待审核列表
		this.thead = this.getListThead(this.btnJson);
		this.toolbar = this.editToolbar;
	},
	methods: {
		/**
		 * 表格行高
		 */
		tableRowClassName() {
			return {
				height: '54px !important'
			};
		},
		getListThead(btnJson) {
			let tempThead = [...this.listThead];
			tempThead.push(btnJson);
			return tempThead;
		},
		// 操作按钮
		btnClick(res) {
			let code = res.handle.code;
			switch (code) {
				case 'view':
					this.openViewPage(res.row.id);
					break;
				case 'addIn':
					this.openManagePage();
					break;
				case 'addCustomIn':
					this.openAddCustomInPage();
					break;
				case 'addOut':
					this.openAddOutPage();
					break;
				case 'edit':
					this.openEditPage(res.row.id);
					break;
				case 'delete':
					this.handleDel(res.row);
					break;
			}
		},
		//打开新增内部维修员弹窗
		openManagePage() {
			this.showAddPage = true;
			this.showViewPage = false;
		},
		//打开查看
		openViewPage(id) {
			this.getPersonInfoById(id);
			this.formTitle = '查看';
			this.showAddPage = false;
			this.showViewPage = true;
		},
		//打开新增外部维修员弹窗
		openAddOutPage() {
			this.formData = {};
			this.formTitle = '新增外部维修员';
			this.showAddPage = false;
			this.showViewPage = true;
		},
		//打开修改外部维修员弹窗
		openEditPage(id) {
			this.formData = {};
			this.getPersonInfoById(id);
			this.formTitle = '编辑';
			this.showAddPage = false;
			this.showViewPage = true;
		},
		handleFormSubmit() {
			this.customLoading = true;
			let url = '';
			if (this.formTitle == '新增外部维修员') {
				const snowflake = new SnowflakeId();
				this.formData.id = snowflake.generate();
				url = api.saveMp;
			} else {
				url = api.updataMp;
			}
			this.$request({
				url: url,
				data: this.formData,
				method: 'POST'
			}).then(res => {
				this.customLoading = false;
				if (res.rCode == 0) {
					this.$message.success('操作成功');
					this.formData = {};
					this.showViewPage = false;
					this.$refs.table.reload();
				} else {
					this.$message.error(res.msg);
				}
			});
		},
		//查询维修员详情
		getPersonInfoById(id) {
			this.formData = {};
			this.customLoading = true;
			this.$request({ url: api.getMpInfoById, params: { id: id }, method: 'GET' }).then(res => {
				this.customLoading = false;
				if (res.rCode === 0) {
					this.formData = res.results;
				} else {
					this.$message.error(res.msg);
				}
			});
		},
		handleDel(row) {
			this.$confirm('确定要删除该维修人员吗？', '提示', {
				confirmButtonText: '确定',
				cancelButtonText: '取消',
				type: 'warning'
			})
				.then(async () => {
					let { rCode, msg } = await this.$.ajax({
						url: api.deleteMpById,
						data: { id: row.id },
						method: 'POST'
					});
					if (rCode === 0) {
						this.$message.success('操作成功！');
						this.$refs.table.reload();
					} else {
						this.$message.error(msg);
					}
				})
				.catch(() => {});
		},
		closeAddPage(code) {
			this.showAddPage = false;
			if (code == 'success') {
				this.$refs.table.reload();
			}
		},
		// 自定义内部维修员
		//打开自定义内部维修员弹窗
		openAddCustomInPage() {
			this.formData = {};
			this.showAddPage = false;
			this.showViewPage = false;
			this.showAddCustomInPage = true;
		},
		handleCustomInFormSubmit() {
			this.customLoading = true;
			this.$request({
				url: api.saveCustomIn,
				data: this.formData,
				method: 'POST'
			}).then(res => {
				this.customLoading = false;
				if (res.rCode == 0) {
					this.$message.success('操作成功');
					this.formData = {};
					this.showAddCustomInPage = false;
					this.$refs.table.reload();
				} else {
					this.$message.error(res.msg);
				}
			});
		}
	}
};
</script>
<style scoped></style>
