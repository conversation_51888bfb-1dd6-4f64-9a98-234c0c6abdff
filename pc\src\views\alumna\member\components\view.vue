<template>
	<es-form ref="form" :model="formData" :contents="formItemList" height="65vh" :genre="2" collapse
		@change="inputChange" label-position="top" />
</template>

<script>
export default {
	name: 'memberView',
	props: {
		info: {
			type: Object,
			default: {}
		}
	},
	data() {
		return {
			formData: {},
			ownId: null,
			qualificationsList: [],
			formItemList: [
				{
					title: '个人信息',
					contents: [
						{
							label: '所在地区：',
							name: 'areaCode',
							readonly: true,
							col: 4
						},
						{
							label: '校友名称',
							name: 'memberName',
							readonly: true,
							col: 4
						},
						{
							label: '出生年月：',
							name: 'birthday',
							readonly: true,
							col: 4
						},

					]
				},

				{
					title: '基础信息',
					contents: [
						{
							label: '到校(入学)时间：',
							name: 'arrivalTime',
							readonly: true,
							col: 4,
							type: 'daterange',
							events: {
								change: (a) => {
									console.log(a, 8888);
								},
							}
						},
						{
							label: '离校(毕业)时间：',
							name: 'offlineTime',
							readonly: true,
							col: 4
						},
						{
							label: '身份：',
							name: 'identityType',
							readonly: true,
							col: 4
						},
						{
							label: '进修培训项目：',
							name: 'continuingEducationProgram',
							readonly: true,
							col: 4
						},
						{
							label: '所在部门：',
							name: 'whereDept',
							readonly: true,
							col: 4
						},
						{
							label: '所在学院：',
							name: 'whereSchool',
							readonly: true,
							col: 4
						},
						{
							label: '所在专业：',
							name: 'whereMajor',
							readonly: true,
							col: 4
						},
						{
							label: '所在班级：',
							name: 'whereClass',
							readonly: true,
							col: 4
						},
						{
							label: '联系方式：',
							name: 'phoneNum',
							readonly: true,
							col: 4
						},
						{
							label: '所在单位职称或任职情况：',
							name: 'whereCompany',
							readonly: true,
							col: 4
						},

						{
							label: '科技创新成果类型：',
							name: 'whereResultType',
							readonly: true,
							col: 4
						},
					]
				},
				{
					title: '校友资质',
					contents: [

						{
							name: 'qualificationsTemp',
							type: 'attachment',
							code: 'alumna_member_qualifications',
							ownId: this.ownId,
							'select-type': 'icon-plus',
							'list-type': 'picture-card',
							preview: true,
							download: true,
							operate: true,
							disabled: true,
						}

					]
				},
				// {
				// 	title: '教育背景',
				// 	contents: [
				// 		{
				// 			name: 'educations',
				// 			type: 'table',
				// 			form: true,
				// 			thead: [
				// 				{
				// 					title: '学校名称',
				// 					field: 'schoolName',
				// 					type: 'text',
				// 					readonly: true
				// 				},
				// 				{
				// 					title: '学历',
				// 					field: 'schoolEducation',
				// 					type: 'text',
				// 					readonly: true
				// 				},
				// 				{
				// 					title: '专业',
				// 					field: 'professionalName',
				// 					type: 'text',
				// 					align: 'center',
				// 					readonly: true
				// 				},
				// 				{
				// 					title: '在校时间',
				// 					field: 'schoolTime',
				// 					type: 'text',
				// 					readonly: true
				// 				}
				// 			]
				// 		}
				// 	]
				// },
				// {
				// 	title: '工作经历',
				// 	contents: [
				// 		{
				// 			name: 'workExperiences',
				// 			type: 'table',
				// 			form: true,
				// 			thead: [
				// 				{
				// 					title: '公司名称',
				// 					field: 'companyName',
				// 					type: 'text',
				// 					readonly: true
				// 				},
				// 				{
				// 					title: '公司行业',
				// 					field: 'companyIndustry',
				// 					type: 'text',
				// 					readonly: true
				// 				},
				// 				{
				// 					title: '在职时间',
				// 					field: 'workTime',
				// 					type: 'text',
				// 					align: 'center',
				// 					readonly: true
				// 				},
				// 				{
				// 					title: '职位名称',
				// 					field: 'positionName',
				// 					type: 'text',
				// 					readonly: true
				// 				},
				// 				{
				// 					title: '工作地点',
				// 					field: 'companyAddr',
				// 					type: 'text',
				// 					readonly: true
				// 				}
				// 			]
				// 		}
				// 	]
				// },
				{
					type: 'submit',
					skin: 'lay-form-btns',
					contents: [{ type: 'reset', text: '取消', event: 'cancel' }]
				}
			]
		};
	},
	watch: {
		info(val, oldVal) {
			this.doHandleFormData(val);
		}
	},
	created() {
		this.doHandleFormData(this.info);
	},
	methods: {

	
		inputChange(key, value) { },
		doHandleFormData(newData) {
			console.log(newData);

			newData.arrivalTime = this.fomaterData(newData.arrivalTime)
			newData.offlineTime = this.fomaterData(newData.offlineTime)

			this.formData = newData;

			this.ownId = this.formData.id;
			// if(this.formData.qualifications){
			// 	let temp = this.formData.qualifications.split(',');
			// 	for(let fileId of temp){
			// 		this.qualificationsList.push(fileAccess + fileId);
			// 	}
			// 	this.formData.qualificationsTemp = this.qualificationsList;
			// }
		},

		fomaterData(dateString) {
			const date = new Date(dateString);
			const year = date.getFullYear();
			const month = String(date.getMonth() + 1).padStart(2, '0'); // 月份从0开始
			const day = String(date.getDate()).padStart(2, '0');
			const formattedDate = `${year}-${month}-${day}`;
			return formattedDate
		},

	}
};
</script>

<style scoped lang='scss'></style>