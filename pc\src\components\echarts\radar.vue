<template>
	<div :id="id" style="width: 100%; height: 100%"></div>
</template>
<script>
export default {
	props: {
		id: {
			type: String,
			required: true
		},
		// title: {
		// 	type: String,
		// 	required: true
		// },
		// color: {
		// 	type: Array,
		// 	required: true
		// },
		seriesData: {
			type: Array,
			required: true
		},
	
	},
	data() {
		return {
			chart: null,
			 dataInfo: [20, 20, 80],
			 names: [{ name: '计划慰问' }, { name: '实际慰问' }, { name: '经费支出' }]
		};
	},
	watch: {
		seriesData() {
			this.draw();
		},
	},
	mounted() {
		this.draw();
	},

	methods: {
		// lookDetail() {
		// 	this.$emit('getDialogList', 'dwzsfz', this.title);
		// },
		/**
		 * @description: 绘制图形
		 * @param {String} id -dom id
		 * @param {Arrayj} data -数据源
		 **/
		draw() {
			let total =this.dataInfo.reduce((total, item) => {
				return (total += item);
			});
			let option = {
				color: ['#0377e8'],
				legend: {},
				radar: [
					{
						indicator: this.names=this.names.map((item)=>{
							let max = Math.max(...this.dataInfo)
							return {...item,max:max}
						}),
						center: ['50%', '50%'],
						radius: '55%',
						startAngle: 90,
						splitNumber: 4,
						shape: 'circle',
						axisName: {
							formatter: '{value}',
							color: '#333'
						},
						splitArea: {
							// areaStyle: {
							// 	color: ['#77EADF', '#26C3BE', '#64AFE9', '#428BD4'],
							// 	shadowColor: 'rgba(0, 0, 0, 0.2)',
							// 	shadowBlur: 10
							// }
						},
						axisLine: {
							// lineStyle: {
							// 	color: 'rgba(211, 253, 250, 0.8)'
							// }
						},
						splitLine: {
							// lineStyle: {
							// 	color: 'rgba(211, 253, 250, 0.8)'
							// }
						}
					}
				],
				tooltip: {
					trigger: 'item',
					backgroundColor: 'rgba(128,187,224,0.9)',
					textStyle: {
						color: '#ffffff'
					},
					borderWidth: 1,
					padding: [5, 8, 5, 8],
					formatter: params => {
						let percent = [];
						let str = '';
						params.value.forEach((item, index) => {
							percent.push(isNaN((item * 100) / total.toFixed(2))?0:(item * 100) / total.toFixed(2));
							str += `<div>${this.names[index].name}:${item}(${percent[index]}%)</div>`;
							// str += this.names[index].name + '：' + item + '(' + percent[index] + '%)';
						});
						return str;
					}
				},
				series: [
					{
						type: 'radar',
						emphasis: {
							lineStyle: {
								width: 4
							}
						},
						itemStyle: {
							borderWidth: 2,
							color: '#ffffff',
							borderColor: '#0377e8'
						},
						data: [
							{
								value: this.dataInfo,
								// name: 'Data A'
								areaStyle: {
									color: '#0377e8'
								}
							},
							// {
							// 	value: this.dataInfo,
							// 	// name: 'Data B',
							// 	areaStyle: {
							// 		color: 'rgba(255, 228, 52, 0.6)'
							// 	}
							// }
						]
					}
				]
			};
			// if (this.axisData.length === 0) return;
			this.chart && this.chart.dispose();
			this.chart = this.$echarts.init(document.getElementById(this.id));
			this.chart.setOption(option);
			window.addEventListener('resize', () => {
				this.chart && this.chart.resize();
			});
		}
	}
};
</script>
