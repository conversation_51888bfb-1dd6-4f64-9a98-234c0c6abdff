<template>
	<div class="bodyBox">
		<div class="statsBox">
			<div class="statsItemBox" style="background-color: #65D8FF;">
				<div>
					<h3>校友人数</h3>
					<span>{{ memberNum }}人</span>
				</div>
			</div>
			<div class="statsItemBox" style="background-color: #C0A1FF;">
				<div>
					<h3>校友会</h3>
					<span>{{ community.customNum + community.classNum }}个</span>
				</div>
			</div>
			<div class="statsItemBox" style="background-color: #F7C162;">
				<div>
					<h3>专业社区</h3>
					<span>{{ community.customNum }}个</span>
				</div>
			</div>
			<div class="statsItemBox" style="background-color: #C6ECB1;">
				<div>
					<h3>班级社区</h3>
					<span>{{ community.classNum }}个</span>
				</div>
			</div>
		</div>

		<div>
			<div class="echartsTitle">
				<h3>社区活跃度</h3>
				<el-select v-model="levelMonth" clearable @change="levelPramsChange" placeholder="指定月份" size="mini">
					<el-option v-for="item in levelMonthOptions" :key="item.value" :label="item.label" :value="item.value"></el-option>
				</el-select>
				<el-select v-model="levelDepart" clearable @change="levelPramsChange" placeholder="指定院系" size="mini">
					<el-option v-for="item in levelDepartOptions" :key="item.value" :label="item.label" :value="item.value"></el-option>
				</el-select>
			</div>
			<div id="communityActiveLevelECharts"></div>
		</div>

		<div class="echarts1Box">
			<div class="authEChartsBox">
				<h3>认证方式占比</h3>
				<div id="authECharts"></div>
			</div>
			<div class="addMemberEChartsBox">
				<div class="echartsTitle">
					<h3>每月新增校友</h3>
					<el-select v-model="addMemberYear" @change="addMemberYearChange" placeholder="请选择" size="mini">
						<el-option v-for="item in addMemberYearOptions" :key="item.value" :label="item.label" :value="item.value"></el-option>
					</el-select>
				</div>
				<div id="addMemberECharts"></div>
			</div>
		</div>
	</div>
</template>

<script>
	import interfaceUrl from '@/http/alumna/stats.js';
	import { collegeSelectList } from '@/http/common/system';
	import * as echarts from "echarts";

	const communityActiveLevelOptions = {
		title: {
			// text: '社区活跃度'
		},
		tooltip: {
			trigger: "axis",
		},
		legend: {
			data: [],//各数据组名称
			show: true,
			left: "85%",
		},
		grid: {
			left: "3%",
			right: "4%",
			bottom: "3%",
			containLabel: true,
		},

		xAxis: {
			type: "category",
			axisTick: {
				alignWithLabel: true,
			},
			data: ["社区1","社区2","社区3","社区4","社区5","社区6","社区7"],
		},
		yAxis: {
			type: "value",
		},
		series: {
			name: "互动次数",
			type: "bar",
			barWidth: "40%",
			data: [23,4,345,88,156,38,9],
			itemStyle: {
				color: "#41a0f9",
				borderColor: "#ddeef6",
			},
		},
	};

	const memberAuthEchartsOptions = {
		title: {
			// text: '认证方式占比'
		},
		tooltip: {
			trigger: "item",
			formatter: "{a} <br/>{b} : {c} ({d}%)",
		},
		legend: {
			data: ["人工认证", "实名认证"],
			top: "center",
			show: true,
			left: "80%",
		},
		series: {
			name: "认证方式占比",
			type: "pie",
			label: {
				show: false,
			},
			emphasis: {
				label: {
					show: true,
				},
			},
			radius: [20, 70],
			center: ["40%", "50%"],
			roseType: "area",
			itemStyle: {
				borderRadius: 2,
			},
			data: [{ value: 135, name: "人工认证" },{ value: 568, name: "实名认证" }]
		},
		color: ["#03E8FA", "#55BBFF"],
	};

	const addMemberEChartsOptions = {
		tooltip: {
			trigger: "axis",
		},
		grid: {
			left: "3%",
			right: "4%",
			bottom: "3%",
			containLabel: true,
		},
		legend: { //各数据组名称
			data: [],
			show: true,
			left: "85%",
		},
		xAxis: {
			type: "category",
			boundaryGap: false,
			// axisTick: {
			//   alignWithLabel: true
			// },
			data: ["1月","2月","3月","4月","5月","6月","7月","8月","9月","10月","11月","12月"],
		},
		yAxis: {
			type: "value",
		},
		series: {
			name: "每月新增校友",
			type: "line",
			stack: "Total",
			data: [23,4,345,88,156,38,9,156,38,9,76,98],
			areaStyle: {},
			lineStyle: {
				color: "#0099ff",
			},
		},
		color: {
			type: "linear",
			x: 0,
			y: 0,
			x2: 0,
			y2: 1,
			colorStops: [
				{
					offset: 0.2,
					color: "#acdaff", // 0% 处的颜色
				},
				{
					offset: 1,
					color: "#eff9ff", // 100% 处的颜色
				},
			],
			global: false, // 缺省为 false
		},
	};

	export default {
		data(){
			return {
				levelCharts:null,
				authCharts:null,
				addMemberCharts:null,
				levelChartsData:null,
				authChartsData:null,
				addMemberChartsData:null,

				levelMonth:null,
				levelDepart:null,
				levelMonthOptions:[
					{label:'1月',value:1},{label:'2月',value:2},{label:'3月',value:3},{label:'4月',value:4},{label:'5月',value:5}
					,{label:'6月',value:6},{label:'7月',value:7},{label:'8月',value:8},{label:'9月',value:9},{label:'10月',value:10}
					,{label:'11月',value:11},{label:'12月',value:12}
				],
				levelDepartOptions:[],
				addMemberYear:null,
				addMemberYearOptions:[],

				memberNum:0,	//三个数据是在authECharts(认证方式占比)数据获取时填充的
				community:{
					customNum:0,
					classNum:0,
				}
			}
		},
		created() {
			this.loadAddMemberYears();
			this.loadCollegeList();
			this.$nextTick(() => {
				this.init();
			});
		},
		mounted() {
			this.$nextTick(() => {
				window.onresize = () => {
					this.reloadECharts();
				};
			});
		},
		methods:{
			loadAddMemberYears(){
				let curDate = new Date();
				let curYear = curDate.getFullYear();
				this.addMemberYear = curYear;
				this.addMemberYearOptions = [];
				let temp = [];
				for(let i = curYear ; i >= 2005 ; i--){
					temp.push({
						label: i + '年',
						value: i
					});
				}
				this.addMemberYearOptions = temp;
			},
			loadCollegeList(){
				this.$request({url: collegeSelectList,method: 'POST'}).then(res => {
					if(res.rCode !== 0){
						return;
					}
					this.levelDepartOptions = res.results;
				});
			},
			init(){
				this.loadCommunityActiveLevelECharts();
				this.loadAuthECharts();
				this.loadAddMemberECharts();
			},
			reloadECharts(){
				this.reloadCommunityActiveLevelECharts();
				this.reloadAuthECharts();
				this.reloadAddMemberECharts();
			},

			levelPramsChange(){
				this.loadCommunityActiveLevelECharts();
			},

			addMemberYearChange(e){
				this.loadAddMemberECharts();
			},

			createCommunityActiveLevelECharts(x, y) {
				if(this.levelCharts){
					this.levelCharts.dispose();
				}
				let chartDom = document.getElementById("communityActiveLevelECharts");
				if(!chartDom){
					return;
				}
				this.levelCharts = echarts.init(chartDom);
				let options = communityActiveLevelOptions;
				options.xAxis.data = x;
				options.series.data = y;
				this.levelCharts.setOption(options);
			},
			loadCommunityActiveLevelECharts(){
				let params = {
					month:this.levelMonth,
					depart:this.levelDepart
				}
				this.$request({url: interfaceUrl.communityActiveLevel,params,method: 'GET'}).then(res => {
					// console.log('社区活跃度=',res);
					if(res.rCode !== 0){
						return;
					}
					let communityName = res.results.communityName;
					let level = res.results.level;
					this.createCommunityActiveLevelECharts(communityName,level);
					this.levelChartsData = {
						communityName:communityName,
						level:level
					};
				});
			},
			reloadCommunityActiveLevelECharts(){
				this.createCommunityActiveLevelECharts(this.levelChartsData.communityName,this.levelChartsData.level);
			},

			createAuthECharts(data){
				if (this.authCharts) {
					this.authCharts.dispose();
				}
				let chartDom = document.getElementById("authECharts");
				if(!chartDom){
					return;
				}
				this.authCharts = echarts.init(chartDom);
				let options = memberAuthEchartsOptions;
				options.series.data = data;
				this.authCharts.setOption(options);
			},
			loadAuthECharts(){
				this.$request({url: interfaceUrl.numStats,method: 'GET'}).then(res => {
					// console.log('校友/社区数量=',res);
					if(res.rCode !== 0){
						return;
					}
					let resData = res.results.memberNumByAuditType;
					let data = [];
					for(let key in resData){
						let name = key === 'auto' ? '实名认证' : '人工认证'
						data.push({
							name: name,
							value: resData[key]
						});
					}
					this.createAuthECharts(data);
					this.authChartsData = data;
					//填充校友社区数量
					this.memberNum = res.results.memberNum;
					this.community.customNum = res.results.communityNumByType.typeCustom;
					this.community.classNum = res.results.communityNumByType.typeClass;
				});
			},
			reloadAuthECharts(){
				this.createAuthECharts(this.authChartsData);
			},

			createAddMemberECharts(y) {
				if(this.addMemberCharts){
					this.addMemberCharts.dispose();
				}
				let chartDom = document.getElementById("addMemberECharts");
				if(!chartDom){
					return;
				}
				this.addMemberCharts = echarts.init(chartDom);
				let options = addMemberEChartsOptions;
				options.series.data = y;
				this.addMemberCharts.setOption(options);
			},
			loadAddMemberECharts(){
				let params = {
					year:this.addMemberYear
				}
				this.$request({url: interfaceUrl.addMemberStats,params,method: 'GET'}).then(res => {
					// console.log('每月新增校友=',res);
					if(res.rCode !== 0){
						return;
					}
					let monthVal = res.results;
					this.createAddMemberECharts(monthVal);
					this.addMemberChartsData = monthVal;
				});
			},
			reloadAddMemberECharts(){
				this.createAddMemberECharts(this.addMemberChartsData);
			}
		}
	}
</script>

<style scoped lang='scss'>
.bodyBox{
	width: 100%;
	height: 100%;
	padding: 10px;
	overflow: auto;
}
.bodyBox > div{
	padding-top: 10px;
	padding-bottom: 10px;
	border-bottom: 1px solid grey;
}

.bodyBox > div:nth-child(3){
	border-bottom: none;
}

.statsBox{
	display: flex;
	flex-direction: row;
	justify-content: space-around;
}
.statsItemBox{
	width: 24%;
	height: 120px;
	// border: 1px solid grey;
	border-radius: 10px;
	color: rgb(255, 255, 255);
	padding: 20px;
	display: flex;
	flex-direction: row;
}
.statsItemBox > div{
	margin-left: 20px;
	h3{
		margin-bottom: 10px;
		font-size: 26px;
	}
}
.statsItemBox span {
	// margin-left: 20px;
	font-size: 18px;
}

#communityActiveLevelECharts{
	width: 100%;
	height: 260px;
}

.echartsTitle{
	display: flex;
	flex-direction: row;
	align-items: center;
}
.echartsTitle > h3{
	margin-right: 10px;
}

.echarts1Box{
	display: flex;
	flex-direction: row;
	justify-content: space-between;
}
.authEChartsBox{
	width: 35%;
	height: 300px;
	border-right: 1px solid grey;
}
.addMemberEChartsBox{
	width: 64%;
	height: 300px;
}
#authECharts{
	width: 100%;
	height: 260px;
}
#addMemberECharts{
	width: 100%;
	height: 260px;
}
</style>