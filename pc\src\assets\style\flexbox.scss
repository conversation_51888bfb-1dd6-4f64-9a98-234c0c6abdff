.layout {
	display: flex;
}

.layout-inline {
	display: inline-flex;
}

.flex {
	flex: 1;
}

.layout-wrap {
	flex-wrap: wrap;
}

.layout-reverse {
	flex-wrap: wrap-reverse;
}

.row {
	flex-direction: row;
}

.column {
	flex-direction: column;
}

.x-start {
	justify-content: flex-start;
}

.x-center {
	justify-content: center;
}

.x-end {
	justify-content: flex-end;
}

.space-between {
	justify-content: space-between;
}

.space-arround {
	justify-content: space-arround;
}

.y-start {
	align-items: flex-start;
}

.y-center {
	align-items: center;
}

.y-end {
	align-items: flex-end;
}

/* start--iPhoneX底部安全区定义--start */
.safe-area-inset-bottom {
	padding-bottom: 0;
	padding-bottom: constant(safe-area-inset-bottom);
	padding-bottom: env(safe-area-inset-bottom);
}

// 单行文本省略号
.ellipsis-1 {
	overflow: hidden; //溢出隐藏
	white-space: nowrap; //禁止换行
	text-overflow: ellipsis; //...
}

// 单行文本省略号
.ellipsis-2 {
	display: -webkit-box !important;
	overflow: hidden;
	text-overflow: ellipsis;
	word-break: break-all;
	-webkit-line-clamp: 2;
	-webkit-box-orient: vertical !important;
}

.pointer {
	cursor: pointer;
}