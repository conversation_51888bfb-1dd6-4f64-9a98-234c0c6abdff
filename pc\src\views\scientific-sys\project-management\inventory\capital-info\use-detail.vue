<!--
 @desc:资金信息 使用明细
 @author: WH
 @date: 2023/11/13
 -->
<template>
	<main>
		<title-card title="经费使用明细">
			<es-button type="primary" round @click="add" v-if="!readonly">新增</es-button>
		</title-card>
		<div class="is-table">
			<es-data-table
				style="width: 100%"
				ref="table"
				:checkbox="checkbox"
				:thead="thead"
				:url="tableUrl"
				:page="{ pageSize: 10 }"
				:param="param"
				method="get"
				@btnClick="btnClick"
			></es-data-table>
		</div>
		<es-dialog
			:title="title"
			:visible.sync="visible"
			:drag="false"
			height="700px"
			width="700px"
			@close="reset"
		>
			<es-form
				v-if="visible"
				:model="formData"
				:contents="formItemList"
				:readonly="formReadonly"
				@submit="submit"
				@reset="reset"
			></es-form>
		</es-dialog>
	</main>
</template>

<script>
import {
	getCapitalUseList,
	delCapitalUseByid,
	updateCapitalUseByid,
	createCapitalUseByid,
	readCapitalUseByid
} from '@/api/scientific-sys.js';
import TitleCard from '@cpt/scientific-sys/title-card.vue';
import { v4 as uuidv4 } from 'uuid';
import { createNamespacedHelpers } from 'vuex';
const { mapState } = createNamespacedHelpers('getRoleMap');

export default {
	name: 'approvalList',
	components: { TitleCard },
	inject: ['id', 'openType', 'initActiveName'],
	data() {
		return {
			tableUrl: getCapitalUseList,
			param: { projectId: this.id },
			checkbox: false, //未上报开启选项框 提供多线批量催报功能
			// 弹窗
			visible: false,
			dataChange: 0, //是否改变过数据 父级刷新列表用
			readonly: false,
			formReadonly: false,
			formId: '',
			// 当前id
			title: '',
			thead: [
				{
					title: '设备购置费（元）',
					field: 'originalEquipmentExpenses',
					align: 'right'
					// fixed: true
				},
				{
					title: '差旅费（元）',
					field: 'travelExpense',
					align: 'right'
				},
				{
					title: '试验、检测费（元）',
					field: 'experimentalDetectionExpenses',
					align: 'right'
				},
				{
					title: '材料费（元）',
					field: 'materialsExpenses',
					align: 'right'
				},
				{
					title: '资料印刷出版费（元）',
					field: 'dataPublishingExpenses',
					align: 'right'
				},
				{
					title: '会务费（元）',
					field: 'meetingAffairExpenses',
					align: 'right'
				},
				{
					title: '专家咨询费（元）',
					field: 'expertConsultationExpenses',
					align: 'right'
				},
				{
					title: '其他费用（元）',
					field: 'otherExpenses',
					align: 'right'
				},
				{
					title: '合计支出费（元）',
					field: 'totalExpenses',
					align: 'right'
				}

				// {
				// 	title: '附件',
				// 	field: 'adjunctId',
				// 	align: 'center',
				// 	width: 120,
				// },
			],
			formData: {},
			formItemList: [
				{
					name: 'originalEquipmentExpenses',
					placeholder: '',
					label: '设备购置费（元）',
					default:false,
					controls:false,
					type:'number',
					// col: 6,
					rules: {
						required: true,
						message: '请输入设备购置费',
						trigger: 'blur'
					}
				},
				{
					name: 'travelExpense',
					placeholder: '',
					label: '差旅费（元）',
					default:false,
					controls:false,
					type:'number',
					// col: 6,
					rules: {
						required: true,
						message: '请输入差旅费',
						trigger: 'blur'
					}
				},
				{
					name: 'experimentalDetectionExpenses',
					placeholder: '',
					label: '试验、检测费（元）',
					default:false,
					controls:false,
					type:'number',
					// col: 6,
					rules: {
						required: true,
						message: '请输入试验、检测',
						trigger: 'blur'
					}
				},
				{
					name: 'materialsExpenses',
					placeholder: '',
					label: '材料费（元）',
					default:false,
					controls:false,
					type:'number',
					// col: 6,
					rules: {
						required: true,
						message: '请输入材料费',
						trigger: 'blur'
					}
				},
				{
					name: 'dataPublishingExpenses',
					placeholder: '',
					label: '资料印刷出版费（元）',
					default:false,
					controls:false,
					type:'number',
					// col: 6,
					rules: {
						required: true,
						message: '请输入资料印刷出版费',
						trigger: 'blur'
					}
				},
				{
					name: 'meetingAffairExpenses',
					placeholder: '',
					label: '会务费（元）',
					default:false,
					controls:false,
					type:'number',
					// col: 6,
					rules: {
						required: true,
						message: '请输入会务费',
						trigger: 'blur'
					}
				},
				{
					name: 'expertConsultationExpenses',
					placeholder: '',
					label: '专家咨询费（元）',
					default:false,
					controls:false,
					type:'number',
					// col: 6,
					rules: {
						required: true,
						message: '请输入专家咨询费',
						trigger: 'blur'
					}
				},
				{
					name: 'otherExpenses',
					placeholder: '',
					label: '其他费用（元）',
					default:false,
					controls:false,
					type:'number',
					// col: 6,
					type:'number',
					rules: {
						required: true,
						message: '请输入其他费用',
						trigger: 'blur'
					}
				},
				{
					name: 'adjunctName',
					label: '附件',
					type: 'attachment',
					value: '',
					colspan: 12,
					code: 'transationform_editfile',
					preview: true,
					download: true,
					ownId: ' ', // 业务id 传个空格 防止报错
					action: '/main2/mecpfileManagement/upload'
					// rules: {
					// 	required: false,
					// 	message: '请上传附件',
					// 	trigger: 'blur'
					// }
				}
			]
		};
	},
	beforeDestroy() {
		if (this.dataChange != 0) {
			this.$bus.$emit('closeDialog', true);
		}
	},
	methods: {
		//增加操作
		addOperate() {
			//初次打开名字是CapitalInfo 才能够编辑经费信息和经费使用明细
			if (this.openType == 'look' && this.initActiveName == 'BasicInfo') {
				this.readonly = true;
			}
			if (!this.readonly) {
				this.thead.push({
					title: '操作',
					type: 'handle',
					template: '',
					fixed: 'right',
					events: [
						{
							text: '查看',
							btnType: 'look'
						},
						{
							text: '修改',
							btnType: 'edit'
						},
						{
							text: '删除',
							btnType: 'del'
						}
					]
				});
			} else {
				this.thead.push({
					title: '操作',
					type: 'handle',
					template: '',
					fixed: 'right',
					events: [
						{
							text: '查看',
							btnType: 'look'
						}
					]
				});
			}
		},
		//row=null  考虑到toolbar按钮不存在row
		btnClick({ handle, row = null }) {
			let { text, btnType } = handle;
			this.title = text;
			row && (this.formId = row.id);
			let btnTask = {
				look: () => {
					this.formReadonly = true;
					this.getInfo();
				},
				edit: () => {
					this.formReadonly = false;
					this.getInfo();
				},
				del: this.del
			};
			btnTask[btnType]();
		},

		add() {
			let uploadId = uuidv4();
			this.formItemList[this.formItemList.length - 1].ownId = uploadId;
			this.formData.adjunctId = uploadId;
			this.title = '新增经费';
			this.visible = true;
		},
		del() {
			this.$confirm('是否删除?', '提示', {
				confirmButtonText: '确定',
				cancelButtonText: '取消',
				type: 'warning'
			})
				.then(() => {
					this.delFn();
				})
				.catch(() => {
					this.$message({
						type: 'info',
						message: '已取消'
					});
				});
		},
		async delFn() {
			const loading = this.load('删除中...');
			try {
				let { rCode, msg } = await this.$.ajax({
					url: delCapitalUseByid,
					method: 'post',
					data: { id: this.formId }
				});
				if (rCode == 0) {
					this.$message({
						type: 'success',
						message: msg
					});
					this.$refs.table.reload();
				} else {
					this.$message.error(msg);
				}
			} finally {
				loading.close();
			}
		},
		async getInfo() {
			const loading = this.load('加载中...');
			try {
				let { rCode, msg, results } = await this.$.ajax({
					url: readCapitalUseByid,
					method: 'get',
					params: { id: this.formId }
				});
				if (rCode == 0) {
					let uploadId = uuidv4();
					this.formItemList[this.formItemList.length - 1].ownId = results.adjunctId || uploadId;
					this.formData = { ...results, adjunctId: results.adjunctId || uploadId };
					this.visible = true;
				} else {
					this.$message.error(msg);
				}
			} catch (error) {
				console.log('>>>error', error);
			} finally {
				loading.close();
			}
		},
		async submit() {
			const loading = this.load('提交中...');
			try {
				let formData = { ...this.formData };
				delete formData.adjunctName;
				let { rCode, msg, results } = await this.$.ajax({
					//有id代表修改 无代表新增
					url: formData.id ? updateCapitalUseByid : createCapitalUseByid,
					method: 'post',
					format: false,
					data: { ...formData, projectId: this.id } //projectId==最外层列表id 项目id
				});
				if (rCode == 0) {
					this.dataChange += 1;

					this.$message.success(msg);
					this.$refs.table.reload();
					this.reset();
				} else {
					this.$message.error(msg);
				}
			} catch (error) {
				console.log('>>>error', error);
			} finally {
				loading.close();
			}
		},
		reset() {
			this.formData = {};
			this.visible = false;
		},
		// 页面加载
		load(text) {
			return this.$.loading(this.$loading, text);
		}
	},
	computed: {
		...mapState(['projectScienceManager'])
	},
	watch: {
		projectScienceManager: {
			immediate: true,
			handler(newVal) {
				// projectScienceManager：父级传递的身份标识 如果为科技处老师值为true 可进行修改删除操作，反之不可
				if (newVal == false) {
					this.readonly = true;
				}
				if(typeof newVal == 'boolean') {
					this.addOperate();
				}
			}
		}
	}
};
</script>
<style lang="scss" scoped>
@import '@ast/style/public.scss';
main {
	width: 100%;
	height: 100%;
	.is-table {
		height: calc(100% - 80px);
	}
}
</style>
