<template>
	<div class="content">
<!--		<es-tree-group-->
<!--			:tree="tree"-->
<!--			:sync-keys="{ id: 'id', name: 'name' }"-->
<!--			@node-click="handleChange"-->
<!--		></es-tree-group>-->
		<es-data-table
			ref="table"
			:row-style="tableRowClassName"
			:full="true"
			:fit="true"
			:thead="thead"
			:toolbar="toolbar"
			:border="true"
			:page="pageOption"
			:url="dataTableUrl"
			:numbers="true"
			:param="params"
			close
			form
			@btnClick="btnClick"
			@sort-change="sortChange"
			@submit="hadeSubmit"
			@edit="changeTable"
		></es-data-table>
		<es-dialog
			:title="formTitle"
			:visible.sync="showForm"
			width="40%"
			height="400px"
			:drag="false"
			:close-on-click-modal="false"
			:destroy-on-close="true"
		>
			<es-form
				ref="form"
				:model="formData"
				:contents="formItemList"
				height="260px"
				:genre="2"
				collapse
				@change="inputChange"
				@submit="handleFormSubmit"
				@reset="showForm = false"
			/>
		</es-dialog>
		<es-dialog
			title="删除"
			:visible.sync="showDelete"
			width="20%"
			:close-on-click-modal="false"
			:middle="true"
			:drag="false"
			height="140px"
		>
			<div>确定要删除该条数据吗</div>
			<div class="btn-box">
				<div class="btn theme" @click="deleteRow">确定</div>
				<div class="btn" @click="showDelete = false">取消</div>
			</div>
		</es-dialog>
	</div>
</template>

<script>
import httpApi from '@/http/logistics/materialcategory.js';
import SnowflakeId from 'snowflake-id';
export default {
	data() {
		return {
			tree: {
				defaultExpandAll: true,
				showSearch: false,
				data: [],
				defaultProps: {
					children: 'children',
					label: 'name'
				}
			},
			nodeSelectData: [],
			selectedTreeNodeValue: '',
			clickNode: '',
			level: 1,//默认一级分类
			editType: 'edit',
			ownId: '',
			dataTableUrl: httpApi.categoryList,
			showForm: false,
			showDelete: false,
			formData: {},
			formTitle: '编辑',
			editBtn: {
				type: 'submit',
				skin: 'lay-form-btns',
				contents: [
					{
						type: 'primary',
						text: '确认',
						event: 'submit'
					},
					{
						type: 'reset',
						text: '取消',
						event: 'cancel'
					}
				]
			},
			cancelBtn: {
				type: 'submit',
				skin: 'lay-form-btns',
				contents: [
					{
						type: 'reset',
						text: '取消',
						event: 'cancel'
					}
				]
			},
			toolbar: [
				{
					type: 'button',
					contents: [
						{
							text: '新增',
							code: 'add',
							type: 'primary'
						}
					]
				},
				{
					type: 'search',
					contents: [
						{
							type: 'text',
							name: 'keyword',
							placeholder: '关键字查询'
						}
					]
				}
			],
			thead: [
				// {
				// 	title: '上级分类',
				// 	align: 'left',
				// 	field: 'parentName',
				// 	sortable: 'custom',
				// 	showOverflowTooltip: true
				// 	// sysCode: this.allCategoryList
				// },
				{
					title: '分类名称',
					align: 'left',
					field: 'name',
					sortable: 'custom',
					showOverflowTooltip: true
				},
				{
					title: '操作时间',
					align: 'left',
					field: 'createTime',
					sortable: 'custom',
					showOverflowTooltip: true
				},
				{
					title: '操作人',
					align: 'left',
					field: 'createUserName',
					sortable: 'custom',
					showOverflowTooltip: true
				},
				{
					label: '状态',
					field: 'status',
					type: 'switch',
					data: [
						{ value: 1, name: '启用' },
						{ value: 0, name: '停用' }
					],
					verify: 'required',
					rules: {
						required: true,
						message: '请确定是否启用',
						trigger: 'blur'
					},
					col: 4
				},
				{
					title: '操作',
					type: 'handle',
					width: 180,
					template: '',
					events: [
						{
							code: 'edit',
							text: '编辑'
						},
						{
							code: 'view',
							text: '查看'
						},
						{
							code: 'delete',
							text: '删除'
						}
					]
				}
			],
			pageOption: {
				layout: 'total, prev, pager, next, jumper',
				pageSize: 10,
				hideOnSinglePage: false,
				position: 'center',
				current: 1,
				pageNum: 1
			},
			params: {
				asc: 'false',
				orderBy: 'createTime',
        level: 1
			},
			allCategoryList: []
		};
	},
	computed: {
		formItemList() {
			return [
				// {
				// 	label: '上级分类',
				// 	type: 'select',
				// 	name: 'parentId',
				// 	placeholder: '请选择原料分类',
				// 	event: 'multipled',
				// 	// rules: {
				// 	//   required: true,
				// 	//   message: '请选择原料分类',
				// 	//   trigger: 'blur'
				// 	// },
				// 	data: this.nodeSelectData,
				// 	valueKey: 'id',
				// 	labelKey: 'name',
				// 	verify: 'required',
				// 	disabled: true,
				// 	col: 11
				// },
				{
					label: '分类名称',
					name: 'name',
					placeholder: '请输入分类名称',
					event: 'multipled',
					rules: {
						required: true,
						message: '请输入分类名称',
						trigger: 'blur'
					},
					verify: 'required',
					maxlength: 100,
					col: 11
				},
				{
					type: 'radio',
					label: '启用状态',
					name: 'status',
					event: 'multipled',
					rules: {
						required: false,
						trigger: 'blur'
					},
					verify: 'required',
					col: 11,
					data: [
						{
							value: 1,
							name: '启用'
						},
						{
							value: 0,
							name: '禁用'
						}
					]
				},
				{
					label: '分类说明',
					name: 'remark',
					placeholder: '请输入分类说明',
					event: 'multipled',
					rules: {
						required: false,
						message: '请输入分类说明',
						trigger: 'blur'
					},
					maxlength: 250,
					col: 11
				}
			];
		}
		// formItemList() {
		// 	return this.editType === 'edit'
		// 		? [...this.arr.slice(0, -2), this.editBtn]
		// 		: [...this.arr, this.cancelBtn];
		// },
	},
	watch: {
		showForm(val) {
			if (!val) {
				this.formData = {};
			} else {
				this.selectAllList();
			}
		}
	},
	created() {
		this.initTree();
		this.selectAllList();
	},
	mounted() {},
	methods: {
		initTree() {
			this.$request({
				url: httpApi.categoryTree,
				method: 'GET'
			}).then(res => {
				if (res.rCode == 0) {
					this.tree.data = res.results;
				}
			});
		},
		/**
		 * 树点击事件
		 */
		// handleChange(tree, data) {
		// 	this.nodeSelectData = [];
		// 	let node = { id: data.id, name: data.name, selected: true };
		// 	this.nodeSelectData.push(node);
		// 	this.selectedTreeNodeValue = data.id;
		// 	this.clickNode = data.id;
		// 	this.$set(this.params, 'parentId', data.id);
		// 	//不点击树时新增；this.level=1，点击以后：this.level=++(data.level)；
		// 	this.level = ++data.level;
		// },
		handleNodeClick(data) {},
		/**
		 * 表格行高
		 */
		tableRowClassName({ row, rowIndex }) {
			let styleRes = {
				height: '54px !important'
			};
			return styleRes;
		},
		inputChange(key, value) {},
		hadeSubmit(data) {},
		changeTable(val) {
			switch (val.name) {
				case 'status':
					this.changeStatus(val.data.id, val.data.status);
					break;
			}
		},
		//改变数据启用状态
		changeStatus(id, status) {
			this.$request({
				url: httpApi.categoryUpdateStatus,
				params: { ids: id, status: status },
				method: 'GET'
			}).then(res => {
				if (res.success) {
					this.$message.success('操作成功！');
				} else {
					this.$message.error(res.msg);
				}
			});
		},
		/**
		 * 操作按钮点击事件
		 * @param {*} res
		 */
		btnClick(res) {
			let code = res.handle.code;
			switch (code) {
				case 'add':
					// 新增
					this.formTitle = '新增';
					this.editModule(this.formItemList);
					const snowflake = new SnowflakeId();
					let id = snowflake.generate();
					this.ownId = id;
					this.formData = {
						id: id,
            level: 1,//默认新增第一级
						name: null,
						createTime: null,
						status: 1,
						address: null,
						remark: null
					};
					this.showForm = true;
					break;
				case 'edit':
					// 编辑
					this.formTitle = '编辑';
					this.ownId = res.row.id;
					this.editModule(this.formItemList);
					this.$request({
						url: httpApi.info + '/' + res.row.id,
						method: 'GET'
					}).then(res => {
						if (res.rCode == 0) {
							//编辑时,要对树对象进行处理
							let parentId = res.results.parentId;
							this.nodeSelectData = [];
							if (parentId) {
								let tempName = this.getParentName(parentId);
								let node = { id: parentId, name: tempName, selected: true };
								this.nodeSelectData.push(node);
							}
							this.formData = res.results;
							this.showForm = true;
						}
					});
					break;
				case 'starStatus':
					break;
				case 'view':
					this.formTitle = '查看';
					this.ownId = res.row.id;
					this.readModule(this.formItemList);
					this.$request({
						url: httpApi.info + '/' + res.row.id,
						method: 'GET'
					}).then(res => {
						if (res.rCode == 0) {
							this.formData = res.results;
							this.showForm = true;
						}
					});
					break;
				case 'delete':
					this.deleteId = res.row.id;
					this.showDelete = true;
					break;
				default:
					break;
			}
		},
		getParentName(parentId) {
			let reName = '';
			for (let i = 0; i < this.allCategoryList.length; i++) {
				if (this.allCategoryList[i].value == parentId) {
					reName = this.allCategoryList[i].label;
				}
			}

			return reName;
		},
		handleClose() {},
		handleFormItemChange() {},
		handleFormSubmit(data) {
			let formData = data;
			delete formData.extMap;
			let url = '';
			if (this.formTitle == '新增') {
				url = httpApi.save;
				formData.level = this.level;
			} else {
				url = httpApi.update;
			}
			this.$request({
				url: url,
				data: formData,
				method: 'POST'
			}).then(res => {
				if (res.rCode == 0) {
					this.$message.success('操作成功'); //
					this.showForm = false;
					this.formData = {};
					this.$refs.table.reload();
					this.initTree();
					this.selectAllList();
				} else {
					this.formData.state = 1 == this.formData.state ? true : false;
					this.$message.error(res.msg);
				}
			});
		},
		deleteRow() {
			this.$request({
				url: httpApi.categoryDel,
				data: { id: this.deleteId },
				method: 'POST'
			}).then(res => {
				if (res.rCode == 0) {
					this.$refs.table.reload();
					this.$message.success('删除成功');
					this.initTree();
					this.selectAllList();
				} else {
					this.$message.error(res.msg);
				}
				this.showDelete = false;
			});
		},
		/**
		 * 获取所有分类keyvalue集合
		 * @param res
		 */
		selectAllList() {
			this.$request({
				url: httpApi.getListAllKeyValue,
				method: 'GET'
			}).then(res => {
				this.allCategoryList = res.results;
			});
		},
		/**
		 * 排序变化事件
		 * @param {*} column
		 * @param {*} prop
		 * @param {*} order
		 */
		sortChange(column, prop, order) {
			if (column.order == 'ascending') {
				//升序
				this.params = {
					asc: 'true',
					orderBy: column.prop
				};
			} else if (column.order == 'descending') {
				//降序
				this.params = {
					asc: 'false',
					orderBy: column.prop
				};
			} else {
				//不排序
				this.params = {
					asc: 'false',
					orderBy: 'createTime'
				};
			}
			this.$refs.table.reload();
		},
		/**
		 * 编辑模式
		 */
		editModule(list) {
			list.forEach((item, index) => {
				item.readonly = false;
			});
			this.editType = 'edit';
		},
		/**
		 * 只读模式
		 */
		readModule(list, hideField) {
			for (var i in list) {
				var item = list[i];
				item.readonly = true;
			}
			this.editType = 'read';
		}
	}
};
</script>
<style scoped lang="scss">
@import '../../../assets/style/style.scss';
.el-row,
.el-col,
.table-box {
	height: 100%;
	width: 100%;
}
.content {
	display: flex;
	width: 100%;
	height: 100%;

	::v-deep .es-data-table {
		flex: 1;
		display: flex;
		flex-direction: column;

		.es-data-table-content {
			flex: 1;
			height: 0;
			display: flex;
			flex-direction: column;

			.el-table {
				flex: 1;
				// height: 100% !important;
			}

			.es-thead-border {
				.el-table__header {
					th {
						border-right: 1px solid #e1e1e1;
					}
				}
			}
		}
	}

	::v-deep .el-form-item__label {
		background: none;
		border: 0px solid #c2c2c2;
	}
}

.el-dialog__body {
	.btn-box {
		margin-top: 20px;
		display: flex;
		justify-content: flex-end;

		.btn {
			padding: 5px 10px;
			color: #666;
			border: 1px solid #eee;
			cursor: pointer;
			margin-right: 5px;
			border-radius: 4px;
			// &.theme {
			// 	background: $--color-primary;
			// 	color: #fff;
			// 	border-color: $--color-primary;
			// }
		}
	}
}
</style>
