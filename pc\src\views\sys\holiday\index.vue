<template>
	<div>
		<el-calendar v-model="dateValue">
			<template slot="dateCell" slot-scope="{ data }">
				<div :class="cellClass(data)" @click="handleDateClick(data)">
					<div class="date-text">
						<div>{{ getDay(data) }}</div>
						<div>{{ getLunarDate(data) }}</div>
					</div>
					<div class="info">
						<div class="holiday-text">{{ getHolidayText(data) }}</div>
						<div class="remark-div" :title="getRemarkText(data)">{{ getRemarkText(data) }}</div>
					</div>
				</div>
			</template>
		</el-calendar>

		<el-dialog
			title="修改"
			:visible.sync="setDayDialogVisible"
			:append-to-body="true"
			width="30%"
			height="auto"
			center
		>
			<el-form ref="editDayForm" v-loading="formLoading" :rules="oneDayRules" :model="editDayData">
				<el-form-item prop="date" label="日期" label-width="80px">
					<el-date-picker
						v-model="editDayData.date"
						type="date"
						placeholder="选择日期"
					></el-date-picker>
				</el-form-item>
				<el-form-item prop="dateCondition" label="类型" label-width="80px">
					<el-radio-group v-model="editDayData.dateCondition">
						<el-radio label="0">工作日</el-radio>
						<el-radio label="1">休息日</el-radio>
						<el-radio label="2">法定假日</el-radio>
					</el-radio-group>
				</el-form-item>
				<el-form-item prop="remark" label="备注" label-width="80px">
					<el-input v-model="editDayData.remark" placeholder="请输入备注"></el-input>
				</el-form-item>
			</el-form>
			<span slot="footer" class="dialog-footer">
				<el-button @click="setDayDialogVisible = false">取 消</el-button>
				<el-button type="primary" :disabled="formLoading" @click="update">确 定</el-button>
			</span>
		</el-dialog>

		<el-dialog
			title="批量设置"
			:visible.sync="setRangeDataDialogVisible"
			:append-to-body="true"
			width="30%"
			height="auto"
			center
		>
			<el-form
				ref="rangeDataForm"
				v-loading="formLoading"
				:rules="rangeDayRules"
				:model="rangeData"
			>
				<el-form-item prop="date" label="日期" label-width="80px">
					<el-date-picker
						v-model="rangeData.date"
						type="daterange"
						value-format="yyyy-MM-dd"
						placeholder="选择日期"
					></el-date-picker>
				</el-form-item>
				<el-form-item prop="dateCondition" label="类型" label-width="80px">
					<el-radio-group v-model="rangeData.dateCondition">
						<el-radio label="0">工作日</el-radio>
						<el-radio label="1">休息日</el-radio>
						<el-radio label="2">法定假日</el-radio>
					</el-radio-group>
				</el-form-item>
				<el-form-item prop="remark" label="备注" label-width="80px">
					<el-input v-model="rangeData.remark" placeholder="请输入备注"></el-input>
				</el-form-item>
			</el-form>
			<span slot="footer" class="dialog-footer">
				<el-button @click="setRangeDataDialogVisible = false">取 消</el-button>
				<el-button type="primary" :disabled="formLoading" @click="updateRangeData">确 定</el-button>
			</span>
		</el-dialog>
	</div>
</template>
<script>
import request from '@/utils/request-ybzy';
import LunarCalendar from 'lunar-calendar';
export default {
	name: 'Holiday',
	data() {
		return {
			dateValue: new Date(),
			formLoading: false,
			editDayData: {
				id: '',
				date: this.formatDate(new Date()),
				dateCondition: '0',
				remark: ''
			},
			rangeData: {
				date: [],
				dateCondition: '0',
				remark: ''
			},
			holidayDataMap: {},
			setDayDialogVisible: false,
			setRangeDataDialogVisible: false,
			oneDayRules: {
				dateCondition: [{ required: true, message: '请选择类型', trigger: 'blur' }],
				date: [{ required: true, message: '请选择日期', trigger: 'blur' }]
			},
			rangeDayRules: {
				dateCondition: [{ required: true, message: '请选择类型', trigger: 'blur' }],
				date: [{ required: true, message: '请选择日期', trigger: 'blur' }]
			}
		};
	},
	watch: {
		dateValue(newVal) {
			this.getHolidayData(newVal);
		}
	},
	mounted() {
		this.getHolidayData(this.dateValue);

		// 获取原生按钮组父容器
		const btnGroup = document.querySelector('.el-calendar__button-group');

		// 添加自定义按钮
		const customBtn = document.createElement('button');
		customBtn.className = 'el-button el-button--primary el-button--mini btn-set-range';
		customBtn.innerHTML = '批量设置';
		customBtn.onclick = () => {
			/* 处理点击事件 */
			this.rangeData = {
				date: [this.dateValue, this.dateValue],
				dateCondition: '0',
				remark: ''
			};
			this.setDayDialogVisible = false;
			this.setRangeDataDialogVisible = true;
		};

		btnGroup.appendChild(customBtn);
	},
	methods: {
		//每一格显示的日期
		getDay(data) {
			let dayPart = data.day.split('-')[2];
			dayPart = dayPart.replace(/^0+/, '');
			return dayPart;
		},
		getHolidayText(data) {
			const holidayData = this.holidayDataMap[data.day];
			return holidayData ? holidayData.name : '';
		},
		getRemarkText(data) {
			const holidayData = this.holidayDataMap[data.day];
			return holidayData ? holidayData.remark : '';
		},
		//显示农历日期
		getLunarDate(data) {
			const date = new Date(data.day);
			const year = date.getFullYear();
			const month = date.getMonth() + 1;
			const day = date.getDate();
			const lunarData = LunarCalendar.solarToLunar(year, month, day);
			return lunarData.lunarDayName === '初一' ? lunarData.lunarMonthName : lunarData.lunarDayName;
		},
		//动态获取日期单元格样式
		cellClass(data) {
			let classes = 'full-cell';
			if (data.isSelected) {
				classes += ' is-selected';
			}
			let thisMonthDateStr = this.formatDate(this.dateValue).substring(0, 7);
			let thisDayDateStr = data.day.substring(0, 7);
			const holidayData = this.holidayDataMap[data.day];
			if (holidayData && holidayData.dateCondition === '2') {
				if (thisMonthDateStr === thisDayDateStr) {
					classes += ' holiday';
				} else {
					classes += ' holiday-grey';
				}
			}
			if (holidayData && holidayData.dateCondition === '1') {
				if (thisMonthDateStr === thisDayDateStr) {
					classes += ' weekend';
				} else {
					classes += ' weekend-grey';
				}
			}
			return classes;
		},
		//日期单元格点击事件
		handleDateClick(data) {
			this.setRangeDataDialogVisible = false;
			this.setDayDialogVisible = true;
			this.editDayData.date = data.day;
			const holidayData = this.holidayDataMap[data.day];
			this.editDayData.id = holidayData.id;
			this.editDayData.name = holidayData.name;
			this.editDayData.remark = holidayData.remark;
			this.editDayData.dateCondition = holidayData.dateCondition;
		},
		formatDate(date) {
			const y = date.getFullYear();
			const m = String(date.getMonth() + 1).padStart(2, '0');
			const d = String(date.getDate()).padStart(2, '0');
			return `${y}-${m}-${d}`;
		},
		//更新日期设定
		async update() {
			this.formLoading = true;
			this.$refs['editDayForm'].validate(async valid => {
				if (valid) {
					try {
						const resp = await request({
							url: '/ybzy/holiday/update',
							method: 'post',
							type: 'JSON',
							data: {
								id: this.editDayData.id,
								dateCondition: this.editDayData.dateCondition,
								remark: this.editDayData.remark
							}
						});
						this.$message.success('修改成功');
						const dayData = this.holidayDataMap[this.editDayData.date];
						//修改后更新绑定数据，刷新样式
						if (dayData && resp) {
							dayData.dateCondition = this.editDayData.dateCondition;
							dayData.remark = this.editDayData.remark;
						}
					} finally {
						this.formLoading = false;
						this.setDayDialogVisible = false;
					}
				} else {
					this.formLoading = false;
					return false;
				}
			});
		},
		//按照日期范围批量更新设置
		async updateRangeData() {
			this.formLoading = true;
			this.$refs['rangeDataForm'].validate(async valid => {
				if (valid) {
					try {
						const resp = await request({
							url: '/ybzy/holiday/set-by-range',
							method: 'post',
							type: 'JSON',
							data: {
								startDate: this.rangeData.date[0],
								endDate: this.rangeData.date[1],
								dateCondition: this.rangeData.dateCondition,
								remark: this.rangeData.remark
							}
						});
						this.$message.success('修改成功');
						if (resp && resp > 0) {
							this.getHolidayData(this.dateValue);
						}
					} finally {
						this.formLoading = false;
						this.setRangeDataDialogVisible = false;
					}
				} else {
					this.formLoading = false;
					return false;
				}
			});
		},
		//获取日期设置
		async getHolidayData(dateStr) {
			const date = new Date(dateStr);
			const year = date.getFullYear();
			const month = date.getMonth();

			// 获取当月第一天
			const firstDay = new Date(year, month, 1);
			//前7天
			firstDay.setDate(firstDay.getDate() - 7);
			const firstDayStr = this.formatDate(firstDay);

			// 获取当月最后一天
			const lastDay = new Date(year, month + 1, 0);
			//后7天
			lastDay.setDate(lastDay.getDate() + 7);
			const lastDayStr = this.formatDate(lastDay);
			const resp = await request({
				url: '/ybzy/holiday/data',
				method: 'get',
				params: { startDate: firstDayStr, endDate: lastDayStr }
			});
			if (resp && resp.length > 0) {
				this.holidayDataMap = resp.reduce((obj, item) => {
					obj[item['dateline']] = item;
					return obj;
				}, {});
			}
		}
	}
};
</script>

<style lang="scss" scoped>
::v-deep .btn-set-range {
	margin-left: 30px;
}
.full-cell {
	width: 100%;
	height: 100%;
	display: flex;
	padding-left: 20px;
	align-items: center;
	justify-content: space-between;
	cursor: pointer;
}
.date-text {
	font-size: 14px;
}
.info {
	width: 100px;
	padding-right: 10px;
	text-align: center;
}
.holiday-text {
	font-size: 12px;
	color: red;
}
.remark-div {
	font-size: 12px;
	white-space: nowrap; /* 禁止换行 */
	overflow: hidden; /* 隐藏溢出内容 */
	text-overflow: ellipsis; /* 显示省略号 */
	max-width: 100px; /* 根据实际需求调整 */
	display: inline-block; /* 需要指定宽度才生效 */
}
::v-deep .el-calendar-day {
	padding: 0px;
}
.weekend {
	background-color: rgb(0, 170, 255);
}
.weekend:hover {
	background-color: rgb(0, 150, 255);
}
.holiday {
	background-color: rgb(0, 255, 166);
}
.holiday:hover {
	background-color: rgb(0, 235, 166);
}
.weekend-grey {
	background-color: rgb(0, 170, 255, 0.4);
}
.holiday-grey {
	background-color: rgb(0, 255, 166, 0.4);
}

/* 隐藏全屏按钮 */
::v-deep .el-dialog__headerbtn > [aria-label='Scale'] {
	display: none !important;
}
</style>
