<template>
	<div class="container">
		<div class="tableContainer">
			<div class="headerContainer">
				<el-form :model="form" label-width="80px" label-position="right">
					<el-row>
						<el-col :span="18" v-show="isHandleAuthority">
							<el-button type="primary" size="small" @click="handleAdd">上传</el-button>
						</el-col>
						<el-col :span="6">
							<el-form-item label="关键词" size="small" style="width: 300px">
								<el-input
									@input="getTableData"
									v-model="form.keyword"
									size="small"
									placeholder="关键词"
								></el-input>
							</el-form-item>
						</el-col>
					</el-row>
				</el-form>
			</div>
			<es-data-table :data="tableData" v-loading="isLoading">
				<template slot="prepend">
					<el-table-column
						prop="fileName"
						label="文件名称"
						align="center"
						show-overflow-tooltip
					></el-table-column>
					<!-- <el-table-column prop="type" label="文件类型" align="center">
						<template slot-scope="{ row }">
							{{ fileTypeOptions.find(option => option.value == row.type)?.label }}
						</template>
					</el-table-column> -->
					<el-table-column
						prop="levelTxt"
						v-if="title == '政策法规'"
						label="文件等级"
						align="center"
					></el-table-column>
					<el-table-column prop="content" label="文件描述" align="center"></el-table-column>
					<!-- <el-table-column prop="createUserName" label="创建人" align="center"></el-table-column> -->
					<el-table-column prop="createTime" label="创建时间" align="center"></el-table-column>
					<el-table-column label="操作" align="center" width="200">
						<template slot-scope="{ row }">
							<el-button type="text" @click="handleCheck(row.filePath, row.fileName)">
								查看
							</el-button>
							<el-button type="text" @click="handleDownload(row.filePath, row.fileName)">
								下载
							</el-button>
							<!-- v-if="isHandleAuthority" -->
							<el-button type="text" v-if="isHandleAuthority" @click="handleDelete(row.id)">
								删除
							</el-button>
						</template>
					</el-table-column>
				</template>
			</es-data-table>
			<es-pagination
				:totalCount="totalCount"
				style="float: right"
				:pageSize="10"
				@current="currentChange"
				@change="sizeChange"
			></es-pagination>
		</div>
		<el-dialog
			title="上传"
			:visible.sync="dialogVisible"
			width="30%"
			modal-append-to-body
			append-to-body
		>
			<el-form
				ref="uploadInfo"
				:model="uploadInfo"
				:rules="rules"
				label-width="95px"
				label-position="right"
			>
				<el-form-item label="文件上传" prop="filePath">
					<el-upload
						:http-request="handleUpload"
						:limit="2"
						:on-change="handleChange"
						:file-list="fileList"
						action=""
					>
						<!-- v-if="isHandleAuthority" -->
						<el-button type="primary" size="small">上传</el-button>
					</el-upload>
				</el-form-item>

				<el-form-item label="文件名称" prop="fileName">
					<el-input v-model="uploadInfo.fileName" placeholder="请输入文件名称" />
				</el-form-item>

				<!-- <el-form-item label="文件类型" prop="type">
					<el-select
						v-model="uploadInfo.type"
						disabled
						style="width: 100%"
						placeholder="请选择文件类型"
					>
						<el-option
							v-for="option in fileTypeOptions"
							:key="option.value"
							:label="option.label"
							:value="option.value"
						></el-option>
					</el-select>
				</el-form-item> -->
				<el-form-item label="文件级别" v-if="uploadInfo.type == 2" prop="level">
					<el-select v-model="uploadInfo.level" style="width: 100%" placeholder="请选择文件级别">
						<el-option label="国家级" :value="1"></el-option>
						<el-option label="省级" :value="2"></el-option>
						<el-option label="校级" :value="3"></el-option>
					</el-select>
				</el-form-item>
				<el-form-item label="文件描述" prop="content">
					<el-input
						type="textarea"
						v-model="uploadInfo.content"
						placeholder="请输入标题名"
						:maxlength="255"
						show-word-limit
					/>
				</el-form-item>
			</el-form>
			<div slot="footer" class="dialog-footer">
				<el-button @click="dialogVisible = false">取消</el-button>
				<el-button type="primary" @click="handleSubmit">确定</el-button>
			</div>
		</el-dialog>
	</div>
</template>

<script>
import axios from 'axios';
export default {
	props: {
		title: {
			type: String,
			default: ''
		}
	},
	data() {
		return {
			dialogVisible: false,
			form: {
				keyword: '',
				pageNum: 1,
				pageSize: 10,
				type: ''
			},
			isLoading: false,

			tableData: [],
			// 上传信息
			uploadInfo: {
				type:
					this.title == '科研公告'
						? 1
						: this.title == '政策法规'
							? 2
							: this.title == '学术交流'
								? 3
								: this.title == '办事指南'
									? 4
									: 5,
				content: '',
				level: '',
				filePath: '',
				fileName: ''
			},
			totalCount: 1, //分页总数
			userInfo: {
				account: '',
				roleCode: 'project_science_manager' //写死
			},
			isHandleAuthority: false,
			rules: {
				content: [{ required: true, message: '请输入标题名', trigger: 'blur' }],
				fileName: [{ required: true, message: '请输入文件名称', trigger: 'blur' }],
				type: [{ required: true, message: '请选择文件类型', trigger: 'blur' }],
				level: [{ required: true, message: '请选择文件级别', trigger: 'blur' }],
				filePath: [{ required: true, message: '请上传文件', trigger: 'blur' }]
			},
			fileList: []
		};
	},
	computed: {
		proxyApi() {
			return process.env.NODE_ENV === 'development' ? '/apia' : '/college-base';
		},
		fileTypeOptions() {
			if (this.title == '科研公告') {
				return [{ label: '通知公告', value: 1 }];
			} else if (this.title == '政策法规') {
				return [{ label: '政策法规', value: 2 }];
			} else if (this.title == '学术交流') {
				return [{ label: '学术交流', value: 3 }];
			} else if (this.title == '办事指南') {
				return [{ label: '办事指南', value: 4 }];
			} else if (this.title == '下载中心') {
				return [{ label: '下载中心', value: 5 }];
			}
		}
	},
	mounted() {
		this.getUserAuthority();
		this.getTableData();
	},
	methods: {
		handleChange(file, fileList) {
			this.fileList = fileList.slice(-1);
		},
		handleSubmit() {
			this.$refs.uploadInfo.validate(valid => {
				if (valid) {
					this.$request({
						url: `/ybzy/projectInformation/save`,
						data: this.uploadInfo,
						method: 'post',
						format: false
					}).then(res => {
						if (res.rCode == 0) {
							this.$message.success('上传成功');
							this.getTableData();
							this.dialogVisible = false;
						} else {
							this.$message.warning(res.msg);
						}
					});
				}
			});
		},

		handleAdd() {
			this.uploadInfo = {
				type:
					this.title == '科研公告'
						? 1
						: this.title == '政策法规'
							? 2
							: this.title == '学术交流'
								? 3
								: this.title == '办事指南'
									? 4
									: 5,
				content: '',
				level: '',
				filePath: '',
				fileName: ''
			};
			this.fileList = [];
			this.dialogVisible = true;
		},
		// 获取角色信息
		getUserAuthority() {
			// 获取登录用户信息
			this.userInfo.account = JSON.parse(localStorage.getItem('loginUserInfo')).code;
			this.$request({
				url: '/ybzy/platuser/front/checkRole',
				data: this.userInfo,
				method: 'post'
			}).then(res => {
				this.isHandleAuthority = res.results[this.userInfo.roleCode];
			});
		},
		getTableData() {
			this.isLoading = true;
			// const data = {
			// 	current: this.form.current,
			// 	size: this.form.size,
			// 	fileName: this.form.fileName,
			// 	departmentCode: this.uploadInfo.departmentCode
			// };
			if (this.title == '科研公告') {
				this.form.type = 1;
			} else if (this.title == '政策法规') {
				this.form.type = 2;
			} else if (this.title == '学术交流') {
				this.form.type = 3;
			} else if (this.title == '办事指南') {
				this.form.type = 4;
			} else if (this.title == '下载中心') {
				this.form.type = 5;
			}
			this.$request({
				url: `/ybzy/projectInformation/listJson`,
				method: 'get',
				params: this.form
				// format: false
			}).then(res => {
				this.tableData = res.results.records;
				this.totalCount = res.results.total;
				this.isLoading = false;
			});
		},
		// 获取机构列表
		getTreeData() {
			return new Promise((resolve, reject) => {
				this.$.ajax({
					// http://yszj.ybzy.cn/ybzy-hr-api/person/hrnCorrelation/getJobCascade
					url: 'http://yszj.ybzy.cn/ybzy-hr-api/person/hrnDepartment/getDepartmentCascade',
					method: 'get'
				}).then(res => {
					resolve(res.data);
				});
			});
		},

		filterNode(value, data) {
			if (!value) return true;
			return data.name.indexOf(value) !== -1;
		},

		// 上传文件
		handleUpload(data) {
			let formData = new FormData();
			formData.append('file', data.file);
			this.uploadInfo.fileName = data.file.name.split('.').shift();
			this.uploadInfo.title = this.uploadInfo.fileName;
			// /api/apia
			// axios.post('/file/minio/file/bucket/pull/1', formData)
			this.$request({
				url: `${this.proxyApi}/file/minio/file/bucket/pull/1`,
				data: formData,
				method: 'post',
				format: false
			}).then(res => {
				if (res.code == 200) {
					this.uploadInfo.filePath = res.data;
					// this.$request({
					// 	url: `${this.proxyApi}/academy/rule/create`,
					// 	data: this.uploadInfo,
					// 	method: 'post',
					// 	format: false
					// }).then(res => {
					// 	if (res.code == 200) {
					// 		this.$message.success('上传成功');
					// 		this.getTableData();
					// 	} else {
					// 		this.$message.warning(res.msg);
					// 	}
					// });
				} else {
					this.$message.warning(res.data.msg);
				}
			});
		},
		// 查看
		handleCheck(url, fileName) {
			window.open(url, '_blank');
		},
		// 下载
		handleDownload(url, fileName) {
			axios({
				url: url,
				method: 'GET',
				responseType: 'blob' // 设置响应的数据类型为二进制流
			})
				.then(response => {
					const blobUrl = window.URL.createObjectURL(
						new Blob([response.data], { type: response.headers['content-type'] })
					); // 指定 MIME 类型
					const contentDisposition = response.headers['content-disposition'];
					let downloadFileName = fileName; // 默认文件名

					// 解析 Content-Disposition 头部以获取文件名
					if (contentDisposition && contentDisposition.indexOf('attachment') !== -1) {
						const matches = /filename[^;=\n]*=((['"]).*?\2|([^;\n]*))/i.exec(contentDisposition);
						if (matches != null && matches[1]) {
							downloadFileName = matches[1].replace(/['"]/g, ''); // 去掉引号
						}
					}

					const link = document.createElement('a');
					link.href = blobUrl;
					link.download = downloadFileName; // 设置下载的文件名
					document.body.appendChild(link);
					link.click();
					document.body.removeChild(link);
					window.URL.revokeObjectURL(blobUrl); // 释放URL对象
					this.$message.success('下载成功');
				})
				.catch(error => {
					this.$message.error('下载失败');
				});
		},
		// 删除
		handleDelete(id) {
			this.$confirm('此操作将永久删除该文件, 是否继续?', '提示', {
				confirmButtonText: '确定',
				cancelButtonText: '取消',
				type: 'warning'
			})
				.then(() => {
					this.$request({
						url: `/ybzy/projectInformation/deleteById?id=${id}`,
						method: 'post'
					}).then(res => {
						this.form.pageNum = 1;
						this.getTableData();
						this.tableData = this.$message({
							type: 'success',
							message: '删除成功!'
						});
					});
				})
				.catch(() => {});
		},
		sizeChange(val) {
			this.form.size = val;
			this.getTableData();
		},
		currentChange(val) {
			this.form.current = val;
			this.getTableData();
		}
	}
};
</script>

<style lang="scss" scoped>
.container {
	display: flex;
	height: 100%;
	width: 100%;
	.treeContainer {
		height: 100%;
		border-right: 1px solid #f0f0f0;
		padding-right: 5px;
		width: 25%;
		overflow: auto;
		.filter-tree {
			height: calc(100% - 40px);
			width: 100%;
			overflow: auto;
		}
	}
	.search {
		margin-bottom: 5px;
	}
	.tableContainer {
		height: 100%;
		width: 100%;
		padding-left: 5px;
		.headerContainer {
			.handleBut {
				display: flex;
				justify-content: flex-end;
			}
		}
	}
}
</style>
