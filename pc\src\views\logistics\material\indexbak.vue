<template>
  <!-- 在list.js修改component.name值可改变展示组件 -->
  <el-col :span="20">
    <component
        :is="component.name"
        ref="dataTable"
        class="table-box"
        v-bind="table"
        :table="table"
        form
        @selection-change="handleSelectionChange"
        @btnClick="btnClick"
        @success="successAfter"
        @edit="changeTable"
    >
      <!-- 表格内容部分 -->
      <es-dialog
          v-if="showForm"
          :title="formTitle"
          :visible.sync="showForm"
          :close-on-click-modal="false"
          :middle="true"
          :drag="false"
          class="dialog-form"
      >
        <es-form
            ref="form"
            :model="formData"
            :contents="formItemList"
            :genre="2"
            height="500px"
            width="400px"
            collapse
            @change="handleFormItemChange"
            @submit="handleFormSubmit"
            @click="handleFormAudit"
            @reset="showForm = false"
        />
      </es-dialog>
      <es-dialog
          v-if="showView"
          title="详情"
          :visible.sync="showView"
          :close-on-click-modal="false"
          :middle="true"
          :drag="false"
          height="500px"
          class="dialog-form"
      >
        <es-form
            ref="form"
            :model="viewData"
            :contents="viewItemList"
            :genre="2"
            height="400px"
            width="250px"
            collapse
            @reset="showView = false"
        />
      </es-dialog>
    </component>
  </el-col>
</template>

<script>
import httpApi from '@/http/logistics/material/api.js';
import list from '@/http/logistics/material/list.js';
import edit from '@/http/logistics/material/edit.js';
import view from '@/http/logistics/material/view.js';
// import interfaceUrl from '@/http/logistics/logistics.js';
import SnowflakeId from 'snowflake-id';
export default {
  name: 'Material',
  mixins: [list, edit, view],
  data() {
    return {
      selectRowData: [],
      selectRowIds: [],
      formTitle: '',
      showForm: false,
      showView: false,
      formData: {},
      viewData: {},
      extraData: {},
      typeList: [],
      // adddTypeInput: false, //是否新增分类
      // typeId: '',
      // typeInput: '', //分类名称
      // editTypeIndex: 0, //当前选中编辑状态的分类下标
      // itemTypeName: '', //当前选中编辑状态的分类名称
      // itemTypeBeforeName: '', //当前选中编辑状态的分类名称修改前的名称
      editBtn: {
        //编辑可选中
        type: 'submit',
        skin: 'lay-form-btns',
        contents: [
          {
            type: 'primary',
            text: '确定',
            event: 'confirm'
          },
          {
            type: 'reset',
            text: '取消',
            event: 'cancel'
          }
        ]
      },
      cancelBtn: {
        //查看不可选中
        type: 'submit',
        skin: 'lay-form-btns',
        contents: [
          {
            type: 'reset',
            text: '取消',
            event: 'cancel'
          }
        ]
      }
    };
  },
  watch: {
    showForm(val) {
      if (!val) {
        this.formData = {};
      } else {
        this.selectData();
      }
    }
  },
  mounted() {
    this.getCategoryList();
  },
  methods: {
    handleFormAudit(val) {
      console.log(val);
    },
    // 初始化扩展数据，字典等
    successAfter(data) {
      this.extraData = data.results.extraData;
    },
    // 数据列表多选回调
    handleSelectionChange(data) {
      let ids = [];
      data.forEach(row => {
        ids.push(row.id);
      });
      this.selectRowData = data;
      this.selectRowIds = ids;
    },
    // 表单变更时, 回调处理
    handleFormItemChange(filed, data) {
      // 处理表单字段systemCode的变更
      // if (filed == 'systemCode') {
      //	this.formData.systemCode = data.id;
      //	this.formData.systemName = data.name;
      // }
    },
    btnClick(res) {
      let text = res.handle.text;
      let code = res.handle.code;
      if (code === 'row') {
        switch (text) {
          case '查看':
            this.viewPageLoad(res.row.id);
            break;
          case '编辑':
            this.editPageLoad(res.row.id);
            break;
          case '删除':
            this.deleteId([res.row.id]);
            break;
          case '禁用':
            this.stopSatus(res.row.id);
            break;
          case '启用':
            this.starSatus(res.row.id);
            break;
          default:
            break;
        }
      } else {
        switch (text) {
          case '新增':
            this.addPageLoad();
            break;
            // case '批量启用':
            // 	if (this.selectRowIds.length < 1) {
            // 		this.$message.warning('未选择数据');
            // 	} else {
            // 		this.statusOpen(this.selectRowIds.join(','));
            // 	}

            // 	break;
            // case '批量停用':
            // 	if (this.selectRowIds.length < 1) {
            // 		this.$message.warning('未选择数据');
            // 	} else {
            // 		this.statusStop(this.selectRowIds.join(','));
            // 	}
            // 	break;
            // case '批量导入':
            // 	// this.addPageLoad();
            // 	console.log('批量导入');
            // 	break;
            // case '查看':
            // 	if (this.selectRowIds.length > 1) {
            // 		this.$message.warning('只能选择一个查看');
            // 	} else if (this.selectRowIds.length < 1) {
            // 		this.$message.warning('请选择一个进行查看');
            // 	} else {
            // 		this.viewPageLoad(this.selectRowIds[0]);
            // 	}
            // 	break;
            // case '删除':
            // 	this.deleteId(this.selectRowIds);
            // 	break;
          default:
            break;
        }
      }
    },
    addPageLoad() {
      this.formTitle = '新增';
      const snowflake = new SnowflakeId();
      let id = snowflake.generate();
      this.formData = {
        id: id,
        status: '1'
        // useStatus: 1,
      };
      this.editPageMode = 'add';
      this.showForm = true;
    },
    viewPageLoad(id) {
      this.$request({
        url: httpApi.info + '/' + id,
        method: 'GET'
      }).then(res => {
        // 处理自定义的数据格式化, extFiled 额外属性名， key、val 数据映射
        // this.viewData = this.formatExtraData(res.results[*], 'extFiled', 'key', 'val');
        this.viewData = res.results;
        this.editPageMode = 'view';
        this.readModule(this.viewItemList);
        // 打开查看弹窗
        this.showView = true;
      });
    },
    editPageLoad(id) {
      this.formTitle = '编辑';
      this.$request({
        url: httpApi.info + '/' + id,
        method: 'GET'
      }).then(res => {
        // 处理自定义的数据格式化, extFiled 额外属性名， key、val 数据映射
        // this.formData = this.formatExtraData(res.results[*], 'extFiled', 'key', 'val');
        this.formData = res.results;
        this.editPageMode = 'edit';
        this.showForm = true;
      });
    },
    stopSatus(id) {
      // 将启用变成禁用
      this.$request({
        url: httpApi.statusStop,
        params: { ids: id }
      }).then(res => {
        if (res.rCode == 0) {
          this.$message.success(res.msg);
          this.$refs.dataTable.reload();
        } else {
          this.$message.error(res.msg);
        }
      });
    },
    //
    starSatus(id) {
      this.$request({
        url: httpApi.statusOpen,
        params: { ids: id }
      }).then(res => {
        if (res.rCode == 0) {
          this.$message.success(res.msg);
          this.$refs.dataTable.reload();
        } else {
          this.$message.error(res.msg);
        }
      });
    },
    handleFormSubmit() {
      let formData = { ...this.formData };
      // 可额外处理formData中的数据
      if (typeof formData.orgIdVO != 'object') formData.orgId = formData.orgIdVO;
      else formData.orgId = formData.orgIdVO.value;
      Reflect.deleteProperty(formData, 'orgIdVO');

      this.$request({
        url: this.formTitle === '新增' ? httpApi.save : httpApi.update,
        data: formData,
        method: 'POST'
      }).then(res => {
        if (res.rCode === 0) {
          this.$message.success('操作成功');
          this.showForm = false;
          this.formData = {};
          this.$refs.dataTable.reload();
        } else {
          this.$message.error(res.msg);
        }
      });
    },
    deleteId(ids) {
      this.$confirm('确定要删除选中的数据吗？', '删除', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      })
          .then(() => {
            this.$request({
              url: httpApi.deleteBatchIds,
              data: { ids: ids.join(',') },
              method: 'POST'
            }).then(res => {
              if (res.rCode === 0) {
                this.$message.success('操作完成');
                this.$refs.dataTable.reload();
              } else {
                this.$message.error(res.msg);
              }
            });
          })
          .catch(() => {});
    },
    // 根据列表额外数据返回的 字典信息，格式化数据
    formatExtraData(data, extFiled, key, val) {
      let text = '未知';
      this.extraData[extFiled].forEach(dict => {
        if (dict[key] === data) {
          text = dict[val];
        }
      });
      return text;
    },
    /**
     * 编辑模式
     */
    edfitModule(list) {
      for (var i in list) {
        var item = list[i];
        item.readonly = false;
      }
      list.push(this.editBtn);
    },
    /**
     * 只读模式
     */
    readModule(list, hideField) {
      for (var i in list) {
        var item = list[i];
        item.readonly = true;
      }
      list.push(this.cancelBtn);
    },
    // 分类列表获取
    getCategoryList() {
      this.$request({
        url: httpApi.categoryList,
        method: 'GET'
      }).then(res => {
        console.log(res);
        let list = res.results?.records || [];
        this.typeList = list.reduce((acc, cur) => {
          let obj = {
            ...cur,
            edit: false
          };
          acc.push(obj);
          return acc;
        }, []);
      });
      console.log('InFunction', this.typeList);
    }
    // // 新增分类
    // saveCategory() {
    // 	this.$request({
    // 		url: httpApi.categorySave,
    // 		params: { id: this.typeId, name: this.typeInput },
    // 		method: 'POST'
    // 	}).then(res => {
    // 		if (res.rCode == 0) {
    // 			this.$message.success(res.msg);
    // 			this.typeList = [];
    // 			this.cancel();
    // 			this.getCategoryList();
    // 		} else {
    // 			this.$message.error(res.msg);
    // 		}

    // 		// this.typeList = res.results?.records || []
    // 	});
    // },
    // // 展示新增分类的输入操作
    // showAddType() {
    // 	const snowflake = new SnowflakeId();
    // 	this.typeId = snowflake.generate();
    // 	this.adddTypeInput = true;
    // },
    // // 取消新增分类
    // cancel() {
    // 	(this.typeInput = ''), (this.adddTypeInput = false);
    // 	this.typeId = '';
    // },
    // // 编辑分类
    // editType(item, index) {
    // 	this.typeList[this.editTypeIndex].edit = false;
    // 	item.edit = true;
    // 	this.editTypeIndex = index;
    // 	this.itemTypeName = item.name;
    // 	this.itemTypeBeforeName = item.name;
    // },
    // // 保存编辑分类信息
    // confirmType(item, index) {
    // 	if (this.itemTypeBeforeName == this.itemTypeName) {
    // 		this.$message.success('您的操作已成功！');
    // 		item.edit = false;
    // 		item.name = this.itemTypeName;
    // 		this.itemTypeName = '';
    // 		this.itemTypeBeforeName = '';
    // 		return false;
    // 	}
    // 	this.$request({
    // 		url: httpApi.categoryUpdate,
    // 		params: { name: this.itemTypeName, id: item.id },
    // 		method: 'POST'
    // 	}).then(res => {
    // 		if (res.rCode == 0) {
    // 			this.$message.success(res.msg);
    // 			item.edit = false;
    // 			item.name = this.itemTypeName;
    // 			this.itemTypeName = '';
    // 			// this.getCategoryList();
    // 		} else {
    // 			this.$message.error(res.msg);
    // 		}
    // 	});
    // },
    // // 删除分类
    // delType(item) {
    // 	console.log(item);
    // 	this.$confirm('确定要删除选中的数据吗？', '删除', {
    // 		confirmButtonText: '确定',
    // 		cancelButtonText: '取消',
    // 		type: 'warning'
    // 	})
    // 		.then(() => {
    // 			this.$request({
    // 				url: httpApi.categoryDel,
    // 				params: { id: item.id },
    // 				method: 'POST'
    // 			}).then(res => {
    // 				if (res.rCode == 0) {
    // 					this.$message.success(res.msg);
    // 					this.typeList = [];
    // 					this.getCategoryList();
    // 				} else {
    // 					this.$message.error(res.msg);
    // 				}
    // 			});
    // 		})
    // 		.catch();
    // },
    // 批量停用
    // statusStop(ids) {
    // 	this.$request({
    // 		url: httpApi.statusStop,
    // 		params: { ids: ids }
    // 	}).then(res => {
    // 		if (res.rCode == 0) {
    // 			this.$message.success(res.msg);
    // 			this.$refs.dataTable.reload();
    // 		} else {
    // 			this.$message.error(res.msg);
    // 		}
    // 	});
    // },
    // // 批量启用
    // statusOpen(ids) {
    // 	this.$request({
    // 		url: httpApi.statusOpen,
    // 		params: { ids: ids }
    // 	}).then(res => {
    // 		if (res.rCode == 0) {
    // 			this.$message.success(res.msg);
    // 			this.$refs.dataTable.reload();
    // 		} else {
    // 			this.$message.error(res.msg);
    // 		}
    // 	});
    // }
  }
};
</script>
<style scoped lang="scss">
.content {
  width: 100%;
  height: 100%;
  padding: 10px;
}
.el-row,
.el-col,
.table-box {
  height: 100%;
  width: 100%;
}
</style>
<style>
.el-dialog__body {
  overflow: auto;
}
</style>
