<!-- 科研首页 图表创建组件 -->
<template>
	<div class="project-con">
		<div class="project-con-btn">
			<es-select
				v-model="selectData"
				placeholder="选择年度"
				:data="options"
				@change="changeYear"
			></es-select>
		</div>
		<div :id="echartDom" class="project-con-chart"></div>
	</div>
</template>
<script>
export default {
	props: {
		// 表格数据
		results: {
			type: Object,
			required: true
		},
		// 用于创建canvas的节点名称
		echartDom: {
			type: String,
			required: true
		}
	},
	data() {
		return {
			statisticsChart: null,
			histogramType: 0,
			options: [
				{
					value: '2019',
					label: '2019年度'
				},
				{
					value: '2020',
					label: '2020年度'
				},
				{
					value: '2021',
					label: '2021年度'
				},
				{
					value: '2022',
					label: '2022年度'
					// disabled: true
				},
				{
					value: '2023',
					label: '2023年度'
				}
			],
			selectData: '2023'
		};
	},
	mounted() {
		this.draw(); //配置echarts参数
		this.getOptions();
	},
	watch: {
		results: {
			deep: true,
			handler() {
				this.draw(); //配置echarts参数
			}
		}
	},
	methods: {
		getOptions() {
			// 生成从2019年到当前年份的数据
			let now = new Date();
			let yearS = 2019;
			let yearE = now.getFullYear();
			const yearArr = [];

			// 包括当前年份
			while (yearS <= yearE) {
				yearArr.push({
					value: yearS + '',
					label: yearS + '年度'
					// disabled: true
				});
				yearS++;
			}
			this.selectData = yearArr[yearArr.length - 1].value;
			this.options = yearArr; // 返回生成的数组
		},
		draw() {
			if (this.statisticsChart != null) {
				this.statisticsChart.dispose();
			}
			let dom = document.getElementById(this.echartDom);
			this.statisticsChart = this.$echarts.init(dom);
			let optionList = {
				color: ['#12CDA9', '#78DE0D', '#37B3FF'],
				tooltip: {
					trigger: 'item',
					formatter: '{b} : {c} ({d}%)'
				},
				grid: {
					left: '2%',
					right: '2%',
					bottom: '2%',
					top: '2%',
					containLabel: true
				},
				series: [
					{
						type: 'pie',
						radius: '70%',
						center: ['50%', '50%'],
						selectedMode: 'single',
						label: {
							formatter: '{b}\n\n{c}  ( {d}% )'
							// padding: [0, 0]
						},
						labelLine: {
							length: 20
						},
						emphasis: {
							itemStyle: {
								shadowBlur: 10,
								shadowOffsetX: 0,
								shadowColor: 'rgba(0, 0, 0, 0.5)'
							}
						},
						data: [
							{
								value: this.results.lengthWaysCount,
								name: '纵向项目'
							},
							{
								value: this.results.crosswiseCount,
								name: '横向项目'
							},
							{
								value: this.results.collegeCount,
								name: '院级项目'
							}
						]
					}
				]
			};

			this.statisticsChart.setOption(optionList);
			window.addEventListener('resize', () => {
				this.statisticsChart && this.statisticsChart.resize();
			});
		},
		changeYear(e) {
			this.$emit('transfer', e);
		}
	}
};
</script>

<style scoped lang="scss">
.project-con {
	background: #ffffff;
	width: 49.5%;
	display: flex;
	flex-direction: column;
	align-items: center;
	height: 400px;
	&-chart {
		flex-shrink: 0;
		width: 100%;
		height: 70%;
	}
	&-btn {
		margin-top: 10px;
	}
}
</style>
