<template>
	<div :id="id" style="width: 100%; height: 100%"></div>
</template>
<script>
export default {
	props: {
		id: {
			type: String,
			required: true
		},
		seriesData: {
			type: Array,
			required: true
		},
		legendData: {
			type: Array,
			required: true,
			defalut: () => {
				return [];
			}
		},
		seriesOptions: {
			type: Object,
			required: false,
			defalut: () => {
				return {};
			}
		}
	},
	data() {
		return {
			chart: null
		};
	},
	watch: {
		seriesData() {
			this.draw();
		}
	},
	mounted() {
		this.draw();
	},

	methods: {
		/**
		 * @description: 绘制图形
		 * @param {String} id -dom id
		 * @param {Arrayj} data -数据源
		 **/
		draw() {
			var colorList = ['#5FDA8A', '#DE7963', '#0377E8'];
			let option = {
				tooltip: {
					trigger: 'item',
					borderColor: 'rgba(255,255,255,.3)',
					borderWidth: 1,
					padding: [5, 8, 5, 8],
					textStyle: {
						color: '#ffffff'
					},
					backgroundColor: 'rgba(128,187,224,0.9)',
					formatter: function (parms) {
						var str =
							parms.marker +
							'' +
							parms.data.name +
							'</br>' +
							parms.data.value +
							'(' +
							parms.percent +
							'%)';
						return str;
					}
				},
				legend: {
					type: 'scroll',
					icon: 'circle',
					left: 'center',
					align: 'auto',
					bottom: '10',
					textStyle: {
						color: '#000'
					},
					data: this.legendData
				},
				series: {
					type: 'pie',
					z: 3,
					center: ['50%', '50%'],
					radius: this.seriesOptions?.radius || ['35%', '55%'],
					itemStyle: {
						color: function (params) {
							return colorList[params.dataIndex];
						}
					},
					roseType: this.seriesOptions?.roseType || '',
					label: {
						formatter: '{b}\n\n',
						show: true,
						position: 'outside',
						padding: [0, -80]
					},
					labelLine: {
						length: 10,
						length2: 80,
						lineStyle: {
							width: 1
						}
					},
					data: this.seriesData
				}
			};
			this.chart && this.chart.dispose();
			this.chart = this.$echarts.init(document.getElementById(this.id));
			this.chart.setOption(option);
			window.addEventListener('resize', () => {
				this.chart && this.chart.resize();
			});
		}
	}
};
</script>
