import util from 'eoss-ui/src/utils/util';
import { getRoleMap } from '@/api/scientific-sys.js';
export default {
  namespaced: true,
  state: {
    projectScienceManager: !!localStorage.getItem('projectScienceManager'),
    xodbToken: "",
    isLogin: true,
    collegeInfo:{}// 保存选择的学院信息,用于接口切换
  },
  mutations: {
    // clearRoleMap(state) {
    //     state.projectScienceManager = '';
    // }
    SET_LOGIN_STATE(state, data) {
      state.isLogin = data;
    },
    SET_XODB_TOKEN: (state, token) => {
      state.xodbToken = token;
    },
    SET_COLLEGE_INFO:(state, token)=>{
      state.collegeInfo = token;
    }
  },
  actions: {
    async handleGetRoleMap({ state, commit }) {
      // const loading = this.load('加载中...');
      localStorage.removeItem('projectScienceManager');
      try {
        let { rCode, msg, results } = await util.ajax({
          url: getRoleMap,
          method: 'post',
          format: true
        });
        if (rCode == 0) {
          console.log('projectScienceManager查询结果>>>>', results.project_science_manager);
          // 是否为科技处老师
          // state.projectScienceManager = true;
          state.projectScienceManager = results.project_science_manager;
          localStorage.setItem('projectScienceManager', JSON.stringify(results.project_science_manager));

        }
      } finally {
        // loading.close();
      }
    },
    // 存储登录状态
    setLoginState({ commit }, data) {
      commit('SET_LOGIN_STATE', data);
    },
    xodbLogin({ commit }, userInfo) {
      return new Promise((resolve, reject) => {
        userApi
          .xodbLogin()
          .then(response => {
            const { results } = response;
            commit('SET_XODB_TOKEN', results);
            // setToken(token);
            resolve();
          })
          .catch(error => {
            reject(error);
          });
      });
    },
  },
  getters: {
    state() {
      return state.xodbToken;
    }
  }
}