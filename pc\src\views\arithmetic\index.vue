<template>
	<div v-loading="loading" class="content">
		<es-data-table
			ref="table"
			:key="tableCount"
			:row-style="tableRowClassName"
			:full="true"
			:fit="true"
			:thead="thead"
			:toolbar="toolbar"
			:border="true"
			:page="false"
			:data="tableData"
			:numbers="true"
			:param="params"
			close
			form
			@btnClick="btnClick"
		></es-data-table>
		<div class="pagination">
			<el-pagination
				:background="false"
				:current-page="currentPage"
				:page-sizes="[10, 20, 30, 40]"
				:layout="pageOption.layout"
				:total="pageOption.total"
				:pager-count="pageOption.total"
				@size-change="
					e => {
						pageOption.pageSize = e;
						queryList();
					}
				"
				@current-change="
					e => {
						pageOption.pageNum = e;
						queryList();
					}
				"
			></el-pagination>
		</div>
		<es-dialog
			v-if="showForm"
			:title="formTitle"
			:visible.sync="showForm"
			:drag="false"
			size="sm"
			:close-on-click-modal="false"
			:destroy-on-close="true"
		>
			<resumeView
				:form-title="formTitle"
				:info="formData"
				@close="
					() => {
						// $refs.table.reload();
						pageOption.pageNum = 1;
						queryList();
						showForm = false;
					}
				"
			></resumeView>
		</es-dialog>
	</div>
</template>

<script>
import interfaceUrl from '@/api/arithmetic.js';
import resumeView from './components/view.vue';
import SnowflakeId from 'snowflake-id';

export default {
	components: { resumeView },
	data() {
		return {
			tableData: [],
			loading: false,
			dataTableUrl: interfaceUrl.classifyInfo,
			tableCount: 1,
			showForm: false,
			params: {
				// orderBy: 'createTime',
				// asc: 'false'
			},
			toolbar: [
				{
					type: 'button',
					contents: [
						{
							text: '新增',
							code: 'add',
							icon: 'el-icon-circle-plus-outline',
							type: 'primary'
						}
					]
				}
				// {
				// 	type: 'search',
				// 	contents: [{ type: 'text', name: 'keyword', placeholder: '标题关键字查询' }]
				// }
			],
			thead: [
				{
					title: '算法名称',
					minWidth: 80,
					align: 'center',
					showOverflowTooltip: true,
					field: 'title'
				},
				{
					title: '算法介绍',
					minWidth: 100,
					align: 'center',
					showOverflowTooltip: true,
					field: 'introduce'
				},
				{
					title: '算法版本',
					minWidth: 100,
					align: 'center',
					showOverflowTooltip: true,
					field: 'version'
				},
				{
					title: '生产日期',
					align: 'center',
					field: 'build_time'
				},
				// {
				// 	title: '文件大小',
				// 	align: 'center',
				// 	field: 'size'
				// },
				{
					title: '创建日期',
					align: 'center',
					field: 'create_time'
				},
				// {
				// 	title: '分类id',
				// 	align: 'center',
				// 	field: 'classes_id'
				// },

				{
					title: '操作',
					type: 'handle',
					width: 210,
					template: '',
					align: 'center',
					events: [
						{ code: 'view', text: '查看', icon: 'el-icon-view' },
						{
							code: 'edit',
							text: '编辑',
							icon: 'el-icon-edit',
							rules: rows => {
								return true;
							}
						},
						{
							code: 'delete',
							text: '删除',
							icon: 'el-icon-delete',
							rules: rows => {
								return true;
							}
						},
						{
							code: 'back',
							text: '撤回',
							icon: 'el-icon-back',
							rules: rows => {
								return rows.isRecall === 'YES' && rows.states === 1;
							}
						}
					]
				}
			],
			pageOption: {
				layout: 'total, prev, pager, next, jumper',
				pageSize: 10,
				total: 50,
				// // hideOnSinglePage: true,
				position: 'center',
				pageNum: 1
			},
			formData: {},
			formTitle: '查看'
		};
	},
	watch: {
		dataTableUrl() {
			this.tableCount++;
		}
	},
	created() {
		this.queryList();
	},
	methods: {
		// 请求列表
		queryList() {
			this.loading = true;
			this.$request2({
				url: interfaceUrl.info,
				data: {
					_page: this.pageOption.pageNum,
					_pageCount: this.pageOption.pageSize
				},
				method: 'POST',
				type: 'JSON'
			}).then(res => {
				this.loading = false;
				if (res.code === 200) {
					this.pageOption.total = res.data.total;
					this.pageOption.pageNum = res.data.current;
					this.tableData = res.data.records;
				}
			});
		},

		//表格行高
		tableRowClassName({ row, rowIndex }) {
			let styleRes = { height: '54px !important' };
			return styleRes;
		},
		inputChange(key, value) {},
		//操作按钮点击事件
		btnClick(res) {
			let code = res.handle.code;
			switch (code) {
				case 'add':
					this.formTitle = '新增';
					this.formData = { target: new SnowflakeId().generate(), status: true };
					this.showForm = true;
					this.loading = false;
					break;
				case 'edit':
					this.formTitle = '编辑';
					this.formData = res.row;
					this.showForm = true;
					break;
				case 'view':
					this.formTitle = '查看';
					this.formData = res.row;
					this.showForm = true;

					break;
				case 'back':
					this.$confirm(`确定要撤回“${res.row.title}”吗？`, '提示', {
						confirmButtonText: '确定',
						cancelButtonText: '取消',
						type: 'warning'
					})
						.then(() => {
							this.loading = true;
							this.$request({
								url: interfaceUrl.recallSelection,
								data: { id: res.row.id },
								method: 'POST'
							}).then(res => {
								this.loading = false;
								if (res.rCode === 0) {
									this.$refs.table.reload();
									this.$message.success('撤回成功');
								} else {
									this.$message.error(res.msg);
								}
							});
						})
						.catch(() => {});

					break;
				case 'delete':
					// 确认提示
					this.$confirm(`确定要删除“${res.row.title}”吗？`, '提示', {
						confirmButtonText: '确定',
						cancelButtonText: '取消',
						type: 'warning'
					})
						.then(() => {
							this.loading = true;
							this.$request2({
								url: interfaceUrl.delete,
								data: { _id: res.row._id },
								method: 'POST',
								type: 'JSON'
							}).then(res => {
								this.loading = false;
								if (res.code === 200) {
									// this.$refs.table.reload();
									this.pageOption.pageNum = 1;
									this.queryList();
									this.$message.success('删除成功');
								} else {
									this.$message.error(res.msg);
								}
							});
						})
						.catch(() => {});
					break;
				default:
					break;
			}
		},
		//删除行
		deleteRow() {
			this.$request({
				url: interfaceUrl.delete,
				data: { id: this.deleteId },
				method: 'POST'
			}).then(res => {
				if (res.rCode === 0) {
					this.$refs.table.reload();
					this.$message.success('删除成功');
				} else {
					this.$message.error(res.msg);
				}
			});
		},
		refreshData() {
			this.$refs.table.reload();
		}
	}
};
</script>

<style scoped lang="scss">
::v-deep .content {
	width: 100%;
	height: 100%;
	.el-dialog__body {
		overflow: auto;
	}
}
::v-deep .pagination {
	display: flex;
	justify-content: right;
	padding: 0 16px 16px;
	background-color: #fafafa;
	.btn-next,
	.el-pager li,
	.btn-prev {
		background-color: #fafafa;
	}
}
</style>
