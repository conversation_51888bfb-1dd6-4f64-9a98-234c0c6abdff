<template>
  <div class="sketch_content">
    <el-form :model="formData" class="resumeTable" :rules="rules" ref="form">
      <el-row>
        <el-col :span="11">
          <el-form-item label="社团" label-width="90px" label-position="left" prop="societyName">
            <el-input v-model="formData.societyName" :disabled="infoDisabled"></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="11">
          <el-form-item label="申请人" label-width="90px" label-position="left" prop="applyUserName">
            <el-input v-model="formData.applyUserName" :disabled="infoDisabled"></el-input>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row>
        <el-col :span="11">
          <el-form-item label="申请" label-width="90px" label-position="left" prop="applyType">
            <el-select v-model="formData.applyType" :disabled="infoDisabled">
              <el-option value="1" label="入社"></el-option>
              <el-option value="2" label="退社"></el-option>
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="11">
          <el-form-item label="申请时间" label-width="90px" label-position="left" prop="operateTime">
            <el-date-picker v-model="formData.operateTime" type="datetime" :disabled="infoDisabled"></el-date-picker>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row>
        <el-col :span="11">
          <el-form-item label="所属学院" label-width="90px" label-position="left" prop="userCollege">
            <el-input v-model="formData.applyUserCollege" :disabled="infoDisabled"></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="11">
          <el-form-item label="所属班级" label-width="90px" label-position="left" prop="userClass">
            <el-input v-model="formData.applyUserClass" :disabled="infoDisabled"></el-input>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row>
        <el-col :span="22" v-show="auditVisible" :disabled="auditDisabled" >
          <el-form-item label="审核意见" label-width="90px" label-position="left" prop="auditOpinion">
            <el-input v-model="formData.auditOpinion" :disabled="auditDisabled" type="textarea" ></el-input>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row><br></el-row>
      <el-row>
        <el-col :span="3" style="float:right">
          <el-button type="reset" @click="infoPageClose(false)">取消</el-button>
        </el-col>
      </el-row>
    </el-form>
  </div>
</template>
<script>

export default {
  name: 'infoPage',
  props: {
    baseData: {
      type: Object
    },
    infoPageMode: {
      type: String
    }
  },
  data() {
    return {
      infoDisabled: false,
      auditDisabled: true,
      auditVisible: true,
      auditBtnVisible: false,
      formData: {},
      pageMode: 'allOn',
      tpSelectList: [],
      sociSelectList: [],
      uploadFileList: [],
      dateformat: 'yyyy-MM-dd HH:mm:ss',
    };
  },
  computed: {
    rules() {
      return{
        auditOpinion: [{ required: this.auditBtnVisible, message: '请输入审核意见', trigger: 'blur' }],
      }
    },
  },
  created() {
    this.formData = { ...this.baseData };
    this.pageMode = this.infoPageMode;
    switch (this.pageMode){
      case '审核':
        this.infoDisabled = true;
        this.auditDisabled = false;
        this.auditVisible = true;
        this.auditBtnVisible = true;
        break;
      case 'allOn': case '新增':
        this.infoDisabled = false;
        this.auditDisabled = true;
        this.auditVisible = false;
        this.auditBtnVisible = false;
        break;
      case '查看':
        this.infoDisabled = true;
        this.auditDisabled = true;
        this.auditVisible = true;
        this.auditBtnVisible = false;
        break;
    }
  },
  methods: {
    infoPageClose(reload){
      this.pageMode = 'allOn';
      this.infoDisabled = false;
      this.$emit('activelyClose',reload);
    },
    closeMapDialog() {
      this.showMapIntLo = false;
    },
  },
  watch: {
  }
};
</script>
<style scoped>
  .sketch_content {
    overflow: auto;
    height: auto;
    border-top: 1px solid #eff1f4;
    border-bottom: 1px solid #eff1f4;
    padding: 0 30px 11px 27px;
  }
  .resumeTable td {
    height: 50px;
    font-weight: bold;
  }
</style>