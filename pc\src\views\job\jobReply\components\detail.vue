<template>
	<es-dialog
		title="查看"
		:visible.sync="showForm"
		:drag="false"
		:close-on-click-modal="false"
		:destroy-on-close="true"
	>
		<div v-if="JSON.stringify(formData) != '{}'" :key="showForm" class="sketch_content">
			<table class="resumeTable">
				<tr>
					<th>企业名称：</th>
					<td>{{ formData?.enterprise?.corpName || '' }}</td>
					<th>岗位名称：</th>
					<td>{{ formData.name }}</td>
				</tr>
				<tr>
					<th>岗位类型：</th>
					<td>{{ formData.postTypeDes }}</td>
					<th>工作性质：</th>
					<td>{{ formData.jobNature }}</td>
				</tr>
				<tr>
					<th>学历要求：</th>
					<td>{{ formData.education }}</td>
					<th>工作经验：</th>
					<td>{{ formData.workExperience }}</td>
				</tr>
				<tr>
					<th>薪资：</th>
					<td colspan="3">{{ formData.salaryStructure }}</td>
				</tr>
				<tr>
					<th valign="top">岗位介绍：</th>
					<td valign="top" colspan="3"><div v-html="formData.introduce"></div></td>
				</tr>
				<tr>
					<th>所属地区：</th>
					<td colspan="3">{{ formData.areaName }}</td>
				</tr>
				<tr>
					<th>详细地址：</th>
					<td colspan="3">{{ formData.address }}</td>
				</tr>
				<tr v-if="formData.lon && formData.lat">
					<th></th>
					<td colspan="3">
						<baidu-map
							:center="center"
							:zoom="zoom"
							class="baiduMap"
							:scroll-wheel-zoom="true"
							@ready="handler"
						>
							<bm-view style="width: 80%; height: 280px; flex: 1"></bm-view>
						</baidu-map>
					</td>
				</tr>
				<tr>
					<th>岗位标签：</th>
					<td colspan="3">
						<el-tag v-for="(tag, index) in formData.tags" :key="index">{{ tag }}</el-tag>
					</td>
				</tr>
				<tr>
					<th>发布人：</th>
					<td>{{ formData.createUserName }}</td>
					<th>发布时间：</th>
					<td>{{ formData.createTime }}</td>
				</tr>
				<tr>
					<th>状态：</th>
					<td>{{ formData.statusStr }}</td>
					<th>审核状态：</th>
					<td>{{ formData.auditStatusStr }}</td>
				</tr>
				<!-- <tr>
					<th>审核意见：</th>
					<td colspan="3">{{ formData.auditOpinion }}</td>
				</tr> -->
			</table>
		</div>
	</es-dialog>
</template>

<script>
export default {
	neme: 'Detail',
	props: {
		formData: {
			type: Object,
			default: () => {
				return {};
			}
		}
	},
	data() {
		return {
			formTitle: '查看',
			// 地图相关
			BMap: '',
			map: '',
			center: {
				lng: 116.395645038,
				lat: 39.9299857781
			},
			zoom: 17,
			//表单数据
			showForm: false
		};
	},
	methods: {
		// 地图初始化
		handler({ BMap, map }) {
			this.BMap = BMap;
			this.map = map;
			this.addPoint(this.formData.lon, this.formData.lat);
		},
		// 添加点位
		addPoint(lng, lat) {
			let newLng = lng ? lng : this.center.lng;
			let newLat = lat ? lat : this.center.lat;
			let map = this.map;
			let BMap = this.BMap;
			map.clearOverlays();
			var point = new BMap.Point(newLng, newLat);
			let zoom = map.getZoom();
			this.$nextTick(() => {
				map.centerAndZoom(point, zoom);
			});
			var marker = new BMap.Marker(point); // 创建标注
			map.addOverlay(marker); // 将标注添加到地图中
		}
	}
};
</script>

<style lang="scss" scoped>
::v-deep .detail {
	padding: 10px 15px;
	.rich-text {
		width: 100%;
	}
}
.sketch_content {
	overflow: auto;
	height: 100%;
	border-top: 1px solid #eff1f4;
	border-bottom: 1px solid #eff1f4;
	padding: 0px 30px 11px 27px;
}

.sketch_content::-webkit-scrollbar {
	width: 3px;
}

.sketch_content::-webkit-scrollbar-thumb {
	background: #8798af;
	border-radius: 2px;
}

.sketch_content::-webkit-scrollbar-track {
	background: transparent;
}

.resumeTable {
	border-bottom: none;
	border-top: none;
	width: 800px;
	margin: 10px auto;
}

.resumeTable th {
	width: 10%;
}

.resumeTable td {
	height: 50px;
	width: 40%;
	/* font-weight: bold; */
}
.baiduMap {
	width: 100%;
}
</style>
