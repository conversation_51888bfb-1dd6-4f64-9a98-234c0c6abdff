/* 表单宽度及剧中展示 */
.mainContainer {
  /*width: 800px;*/
  margin: 0 auto;
  height: 100%;
}
/* 表格基本样式 */
.table-content {
  background-color: #fff;
  padding: 10px;
}
.table-content .m-table-form {
  background-color: #f2f2f2;
}
.m-table-form td {
  height: 38px;
  text-align: center;
}
.m-table-form td {
  border: 1px solid #d7d7d7;
  text-align: center;
  vertical-align:middle;
  padding: 0;
}
.m-table-form td.table-head {
  width: 182px;
  text-align: left;
  padding: 0px 0px 0px 15px;
  vertical-align:middle;
}
.m-table-form td .u-input {
  width: 100%;
  height: 38px;
  border: none;
  /* text-align: center; */
}
.m-table-form tr td:last-child {
  padding: 0;
}
.m-table-form tr .m-tooltip {
  height: 100%;
}
.m-table-form {
  margin-bottom: 20px;
}
/* 表头上报单位部分 */
.form-header td {
  border: none;
  background-color: #fff;
}
.form-header td:nth-child(2n+1) {
  width: 10%;
  text-align: right;
}
.form-header td span {
  display: inline-block;
  width: 100%;
  height: 100%;
  line-height: 37px;
  background-color: #e52e21;
  border: 1px solid #d7d7d7;
}
.form-header td .u-input {
  width: 100%;
  height: 38px;
  border: 1px solid #d7d7d7;
  border-radius: 0;
}
/* 表格标题部分 */

/* .table-title{
  height: 30px;
  margin: 30px 0;
} */
.table-title p {
  font-size: 16px;
  font-weight: bold;
  line-height: 65px;
  /*height: 41px; */
  padding: 0;
}
.table-title p span {
  float: left;
  width: 5px;
  height: 25px;
  background-color: #e52e21;
  margin-top: 20px;
  margin-right: 10px;
}
/* 除表单头部以外的表格 */
.m-table-form .inner-select {
  width: 100%;
  height: 100%;
  border: none;
}
.m-table-form :focus {
  outline: none;
}
.m-table-form .select-button {
  float: right;
  width:10%;
  height: 100%;
  line-height: 37px;
  border: 1px solid #cce0fe;
  color: #467fdc;
  background-color: #e8f2ff;
  border-radius: 0;
}
.m-table-form tr td .display-span {
  display: inline-block;
  height: 100%;
  text-align: center;
  width: 90%;
  line-height: 38px;
  background-color: #fff;
}
/* 提交按钮部分  */
.save-box {
  overflow: hidden;
  background-color: #ffffff;
  border-top: 1px solid #e1dede;
}
.save-box-notline {
  overflow: hidden;
  background-color: #ffffff;
}
.save-box .u-btn {
  float: right;
  margin: 18px 10px 15px 10px;
  border: 1px solid #cce0fe;
  background-color: #e94c41;
  color: #fff!important;
  padding: 0px 30px;
}
.u-input:disabled, .u-label:disabled, .u-checkbox:disabled, .u-select:disabled, .u-textarea:disabled {
  background-image: inherit;
  background: #FFFFFF;
}
body {
  width: 100%;
  height: 100%;
  color: #333;
  background-color: #ffffff;
}
.title-border {
  text-align: left;
  border-bottom:1px solid #e6e6e6f5;
  position: relative;
  padding-top: 20px;
}
/* 横线样式 */
hr {
  height: 15px;
  margin: 0px -20px;
}
/* 内容高度 */
#bodyTableDiv {
  height:92% !important;
}
/*新增内容 zhaohui*/

/*导出按钮样式 浅蓝色*/
.e-btn.success {
  background-color: #E8F2FF;
  border-color: #CCE0FE;
  color: #467FDC!important;
}
.e-btn.success:hover {
  background-color: #DFEBFF !important;
  color: #467FDC;
  border-color: #C8DFFD !important;
  box-shadow: 0 0 8px #96cbf7;
}
/*普通按钮样式 白色*/
.w-btn.success {
  background-color: #ffffff;
  border-color: #A9A9A9;
  color: #313131!important;
}
.w-btn.success:hover {
  background-color: #ffffff;
  !important;
  color: #313131;
  border-color: #A9A9A9;
  !important;
  box-shadow: 0 0 8px #96cbf7;
}
/*审核轨迹按钮样式 黄色*/
.y-btn.success {
  background-color: #FFDF25;
  border-color: #FFDF25;
  color: #2b2a2a!important;
}
.y-btn.success:hover {
  background-color: #FFDF25;
  !important;
  color: #313131;
  border-color: #FFDF25;
  ;
  !important;
  box-shadow: 0 0 8px #FFDF25;
}
/*操作按钮样式 蓝色（查看新增编辑等）*/
.v-btn {
  background-color: #ffffff;
  border-color: #1b82d7 !important;
  color: #1b82d7 !important;
}
/*操作按钮样式 红色（删除驳回等）*/
.d-btn {
  background-color: #ffffff;
  border-color: #FF7575 !important;
  color: #FF7575 !important;
}