<template>
	<div class="home">
		<div class="top">
			<div class="nav">
				<div
					v-for="(i, ind) in navList"
					:key="ind"
					class="nav-item-box"
					@click="navBtnFunc(i, ind)"
				>
					<div :class="navListActive == ind ? 'nav-item nav-item-active' : ' nav-item'">
						<div class="cube"></div>
						<div class="text">{{ i.name }}</div>
					</div>
				</div>
			</div>
			<div class="choose">
				<div class="title">统一身份认证平台 - 全部功能</div>
				<div class="content">
					<div v-for="(item, index) in itemList" :key="index" class="item">
						<div class="pic">
							<div class="picBox">
								<img :src="handelPicUrl(item.icons)" alt="" height="100%" />
							</div>
							<div class="add" @click="addList(item)">+</div>
						</div>
						<div class="app-name">{{ item.text }}</div>
					</div>
				</div>
			</div>
		</div>
		<div class="bottom">
			<div class="title">
				置顶功能
				<span>（最多可设置15个置顶功能，可通过拖拽进行排列）</span>
			</div>
			<div v-if="isShow" id="itemChoose" class="choose">
				<div v-for="(item, index) in usullayList" :key="index" class="item">
					<div class="pic">
						<div class="picBox">
							<img :src="handelPicUrl(item.icons)" alt="" height="100%" />
						</div>
						<div class="add" @click="redList(index)">-</div>
					</div>
					<div class="app-name">{{ item.text }}</div>
				</div>
			</div>
		</div>
		<div class="save">
			<div class="btn" @click="save()">保存</div>
			<div class="btn" @click="cancle()">取消</div>
		</div>
	</div>
</template>

<script>
import Sortable from 'sortablejs';
import { alumniUrl } from '@/config';
import { getTransactionList,checkLogin, getEossAuthentication } from '@/api/home.js';
import requestFun from '@/utils/request';
export default {
	props: {},
	data() {
		return {
			navListActive: 0,
			navList: [],
			usullayList: [],
			newList: [],
			sortTable: null,
			isShow: true,
			list: [],
			itemList: []
		};
	},
	mounted() {
		this.startSortTable();
		// requestFun({
		// 	// url: `api/ybzy/platUserCommfunc/front/save`,
		// 	url: `/ybzy/platUserCommfunc/front/listJson`,
		// 	method: 'get'
		// })
		this.$.ajax({
			url: `/ybzy/platUserCommfunc/front/listJson`
		}).then(res => {
			// console.log(res.data.results.records.join(','));
			// let str = res.data.results.records.join(',');
			let arrIndex = res?.results?.records || [];
			// this.getListData(arrIndex);
			this.getListData(arrIndex);
		});
	},
	methods: {
		save() {
			let arr = [];
			this.usullayList.forEach(item => {
				arr.push(item.id);
			});
			let string = arr.join(','); // 使用逗号作为分隔符
			requestFun({
				url: `/ybzy/platUserCommfunc/front/save`,
				method: 'post',
				data: {
					commIds: string
				}
			}).then(res => {
				this.$emit('cancleUuslly', { type: '保存' });
			});
		},
		//处理图片路径
		handelPicUrl(url) {
			return `${alumniUrl}/static/andyui2.0/css/picIcon/img/${url}`;
		},
		navBtnFunc(i, ind) {
			this.navListActive = ind;
			this.list.forEach((item, index) => {
				if (item.text == i.name) {
					this.itemList = this.list[ind].children;
				}
			});
		},
		//获取接口数据
		getListData(arrIndex) {
			let userId = localStorage.getItem('mobileUserId') || '';
			/**原有代码基础加上判断，没有授权的话请求授权*/
			if (!userId) {
				// checkLogin()
				this.$.ajax({
					url: checkLogin
				}).then(res => {
					let code = res.results.code;
					localStorage.setItem('ssoCode', res.results.code);
					let data = {
						serverId: 'ybzyDtcSso',
						authType: '6',
						code
					};
					// if (true) {
					if (!(code === null)) {
						// this.$api.serviceCenter
						// 	.getEossAuthentication(data)
						this.$.ajax({
							url: getEossAuthentication,
							method: 'POST',
							data: data
						})
							.then(res => {
								localStorage.setItem('mobileToken', res.results.mobileToken);
								localStorage.setItem('mobileUserId', res.results.userId);
								this.getListData(arrIndex);
							})
							.catch(err => {
								console.log(err, '认证失败err');
								return '认证失败';
							});
					}
				});
			} else {
				this.$.ajax({
					url: getTransactionList + `?menuCode=cygn&userId=7f90da83941048a5abd31c4940019a34&type=2`
				})
					.then(res => {
						let arrAll = [];
						res.results.forEach(i => {
							i.children.forEach(item => {
								arrAll.push(item);
							});
						});
						arrIndex.forEach(item => {
							let found = false;
							for (let i = 0; i < arrAll.length; i++) {
								if (arrAll[i].id === item) {
									console.log(arrAll[i]);
									this.usullayList.push({
										icons: arrAll[i].icons,
										text: arrAll[i].text,
										id: arrAll[i].id
									});
									found = true;
									break;
								}
							}
						});

						this.list = res.results;
						this.itemList = res.results[0].children;
						// let arr = [];
						// this.list[0].children &&
						// 	this.list[0].children.forEach(children => {
						// 		if (children.url == '') {
						// 			console.log(children);
						// 		} else {
						// 			arr.push(children);
						// 		}
						// 	});
						// this.itemList = arr;
						this.navList = [];
						res.results.forEach(item => {
							let obj = {};
							obj.name = item.text;
							obj.id = item.id;
							this.navList.push(obj);
						});
					})
					.catch(error => {});
			}
		},
		addList(item) {
			if (this.usullayList.length >= 15) {
				this.$message({
					message: '最后只能添加15个置顶功能',
					type: 'warning'
				});
				return;
			}
			let bool = false;
			this.usullayList.forEach(i => {
				if (i.id == item.id) {
					bool = true;
				}
			});
			if (bool) {
				this.$message({
					message: '不能重复添加',
					type: 'error'
				});
				return;
			}
			this.usullayList.push(item);
		},
		redList(index) {
			this.usullayList.splice(index, 1); // 删除索引为2的元素，并返回被删除的元素
		},
		onEnd(evt) {
			if (evt.oldIndex === evt.newIndex) {
				return;
			}

			this.newList = this.usullayList.slice();
			var item = this.newList.splice(evt.oldIndex, 1); // 删除索引为2的元素，并返回被删除的元素
			this.newList.splice(evt.newIndex, 0, item[0]); // 在索引为1的位置插入被删除的元素
			this.usullayList = this.newList.slice();
			this.isShow = false;
			this.$nextTick(() => {
				// 重新渲染之后，重新进行Sortable绑定
				this.isShow = true;
				this.$nextTick(() => {
					// console.log(this.usullayList);
					new Sortable(document.querySelector('#itemChoose'), {
						animation: 150,
						onEnd: (/**Event*/ evt) => {
							this.onEnd(evt);
						}
					});
				});
			});
		},
		//拖拽初始化
		startSortTable() {
			this.sortTable = new Sortable(document.querySelector('#itemChoose'), {
				animation: 150,
				// 拖拽时预览图样式
				// 结束拖拽
				//? 如何替换原数组-替换原数组会发生两次变换
				onEnd: (/**Event*/ evt) => {
					this.onEnd(evt);
				}
			});
		},
		cancle() {
			this.$emit('cancleUuslly', { type: '取消' });
		}
	}
};
</script>
<style lang="scss" scoped>
.home {
	width: 100%;
	height: 100%;
	.top {
		width: 100%;
		height: 60%;
		display: flex;
		border-bottom: 1px solid #ebeef5;
		.nav {
			height: 100%;
			width: 260px;
			background: #ffffff;
			overflow: auto;
			padding: 0 0 10px 0;
			.nav-item-box {
				padding: 0 10px 0 0;
				.nav-item-active {
					background: #ebecf0;
					border-radius: 6px;
				}
				.nav-item {
					cursor: pointer;
					padding: 0 10px;
					display: flex;
					justify-content: left;
					align-items: center;

					.cube {
						width: 16px;
						height: 16px;
						background: #0076e8;
					}
					.text {
						width: 150px;
						white-space: nowrap;
						overflow: hidden;
						text-overflow: ellipsis;
						margin-left: 10px;
						height: 36px;
						font-size: 14px;
						font-family: Microsoft YaHei;
						font-weight: 400;
						color: #7a8392;
						line-height: 36px;
					}
				}
			}
		}
		.choose {
			overflow: auto;
			height: 100%;
			flex: 1;
			.title {
				width: 100%;
				height: 46px;
				font-size: 14px;
				font-family: Microsoft YaHei;
				font-weight: bold;
				color: #333333;
				line-height: 46px;
				text-indent: 2em;
			}
			.content {
				display: flex;
				justify-content: left;
				flex-wrap: wrap;
				.item {
					height: 100px;
					width: 70px;
					margin: 13px 10px;
					.pic {
						width: 70px;
						height: 70px;
						background: #ffffff;
						border: 1px solid #c5d1e0;
						border-radius: 10px;
						position: relative;
						display: flex;
						justify-content: center;
						align-items: center;
						.picBox {
							// border: 1px solid red;
							overflow: hidden;
							border-radius: 14px;
							width: 70px;
							height: 70px;
						}
						.add {
							cursor: pointer;
							position: absolute;
							width: 16px;
							height: 16px;
							border-radius: 50%;
							background: #0076e8;
							text-align: center;
							line-height: 16px;
							color: #ffffff;
							font-style: 14px;
							right: -8px;
							top: -8px;
						}
					}
					.app-name {
						font-size: 14px;
						font-family: Microsoft YaHei;
						font-weight: 400;
						color: #7a8392;
						text-align: center;
						width: 100%;
						white-space: normal;
						text-overflow: ellipsis;
						overflow: hidden;
						display: -webkit-box;
						-webkit-line-clamp: 2;
						-webkit-box-orient: vertical;
					}
				}
			}
		}
	}
	.bottom {
		width: 100%;
		height: 35%;
		.title {
			width: 100%;
			height: 46px;
			font-size: 14px;
			font-family: Microsoft YaHei;
			font-weight: bold;
			color: #333333;
			line-height: 46px;
			span {
				color: #666666;
			}
		}
		.choose {
			cursor: pointer;
			width: 100%;
			display: flex;
			justify-content: left;
			flex-wrap: wrap;
			.item {
				height: 100px;
				width: 70px;
				margin: 10px 14px;
				.pic {
					width: 70px;
					height: 70px;
					background: #ffffff;
					border: 1px solid #c5d1e0;
					border-radius: 10px;
					position: relative;
					display: flex;
					justify-content: center;
					align-items: center;
					.picBox {
						// border: 1px solid red;
						overflow: hidden;
						border-radius: 14px;
						width: 70px;
						height: 70px;
					}
					.add {
						cursor: pointer;
						position: absolute;
						width: 16px;
						height: 16px;
						border-radius: 50%;
						background: #aaaaaa;
						text-align: center;
						line-height: 16px;
						color: #ffffff;
						font-style: 14px;
						right: -8px;
						top: -8px;
					}
				}
				.app-name {
					font-size: 14px;
					font-family: Microsoft YaHei;
					font-weight: 400;
					color: #7a8392;
					text-align: center;
					width: 100%;
					white-space: normal;
					text-overflow: ellipsis;
					overflow: hidden;
					display: -webkit-box;
					-webkit-line-clamp: 2;
					-webkit-box-orient: vertical;
				}
			}
		}
	}
	.save {
		width: 100%;
		bottom: -30px;
		left: 0%;
		width: 100%;
		height: 36px;
		// background: #f2f8fe;
		border-radius: 6px;
		display: flex;
		justify-content: center;
		align-items: center;

		.btn {
			cursor: pointer;
			margin: 0 8px;
			text-align: center;
			width: 57px;
			height: 28px;
			background: #0076e8;
			border-radius: 4px;

			font-size: 14px;
			font-family: Microsoft YaHei;
			font-weight: 400;
			color: #ffffff;
			line-height: 28px;
			&:nth-child(2) {
				background: #c5d1e0;
				color: #666666;
			}
		}
	}
}
</style>
