<template>
	<div class="content">
		<div style="width: 100%">
			<es-data-table
				ref="table"
				:row-style="tableRowClassName"
				:full="true"
				:fit="true"
				:thead="listThead"
				:toolbar="viewToolbar"
				:border="true"
				:page="pageOption"
				:url="dataTableUrl"
				:numbers="true"
				:param="params"
				close
				@btnClick="btnClick"
				@search="search"
				@reset="search({})"
				@sort-change="sortChange"
				@submit="hadeSubmit"
			></es-data-table>
			<!-- 查看企业详细信息 -->
			<es-dialog
				v-if="showAllView"
				:visible.sync="showAllView"
				:title="formTitle"
				height="80%"
				width="80%"
			>
				<allview
					v-if="showAllView"
					:select-info="selectInfo"
					style="height: calc(100% - 40px)"
					:form-info="formData"
					:region-data="regionData"
					@cancel="showAllView = $event"
				></allview>
			</es-dialog>

		</div>
	</div>
</template>

<script>
import interfaceUrl from '@/http/platform/enterprise.js';
import SnowflakeId from 'snowflake-id';
import { host } from '@/../config/config';
import allview from '../../platform/enterprise/allview.vue'
import qs from 'qs';
export default {
  components:{allview},
	data() {
		return {
			queryStatus: '',
			dataTableUrl: interfaceUrl.listJson,
			showForm: false,
			showDelete: false,
			showAudit: false,
			showAllView: false,
			dialogType: '',
			selectInfo: null, //选中的数据值
			formData: {},
			formTitle: '编辑',
			regionData: [],
			typeDicData: [],
			validityOfDateDisable: false,
			isSystemReo: false, //true招就处，false二级学院
			// 审核状态：0:待审核（二级学院审核）, 11:待招就处审核（二级学院审核通过）, 1:已审核（招就处审核通过）, 2:审核未通过, 9:草稿
			viewToolbar: [
				{
					type: 'button',
					contents: [
						{
							text: '导出',
							exportXls: true,
							icon: 'es-icon-daochu',
							type: 'primary',
							event: () => {
								const searchValue = this.$refs.table?._data?.advanceWhere || {};
								const params = { ...this.params, ...this.selectParams, ...searchValue };
								const url =
									host + '/ybzy/platenterprise/exportEnterpriseList' + '?' + qs.stringify(params);
								window.open(url, '_self');
							}
						}
					]
				},
				{
					type: 'search',
					reset: true,
					contents: [
						{
							type: 'text',
							name: 'keyword',
							placeholder: '关键字查询'
						}
					]
				},
				{
					type: 'filter',
					contents: [
						{
							type: 'text',
							label: '企业名称',
							placeholder: '请输入企业名称',
							name: 'corpName',
							clearable: true,
							col: 4
						},
						{
							type: 'select',
							label: '认证来源',
							placeholder: '请选择认证来源',
							name: 'certifyFrom',
							event: 'multipled',
							sysCode: 'plat_enp_cert_apply_certify_from',
							'label-key': 'shortName',
							'value-key': 'cciValue',
							clearable: true,
							col: 4
						},
						{
							type: 'text',
							label: '法定代表人',
							placeholder: '请输入法定代表人',
							name: 'lawPerson',
							clearable: true,
							col: 4
						},
						{
							type: 'select',
							label: '企业性质',
							placeholder: '请选择企业性质',
							name: 'corpType',
							event: 'multipled',
							sysCode: 'plat_enp_quality',
							'label-key': 'shortName',
							'value-key': 'cciValue',
							clearable: true,
							col: 4
						},
						{
							type: 'text',
							label: '统一社会信用代码',
							placeholder: '请输入统一社会信用代码',
							name: 'socialCode',
							clearable: true,
							col: 4
						},
						{
							width: 120,

							type: 'date',
							label: '申请时间',
							placeholder: '请输入申请时间',
							name: 'createTime',
							clearable: true,
							col: 4
						}
					]
				}
			],
			listThead: [
				{
					title: '企业名称',
					align: 'left',
					field: 'corpName',
					sortable: 'custom',
					showOverflowTooltip: true
				},
				{
					title: '统一社会信用代码',
					width: '160px',
					align: 'left',
					field: 'socialCode',
					sortable: 'custom',
					showOverflowTooltip: true
				},
				{
					title: '法定代表人',
					width: '120px',
					align: 'center',
					field: 'lawPerson',
					sortable: 'custom',
					showOverflowTooltip: true
				},
				{
					title: '企业性质',
					width: '110px',
					align: 'left',
					field: 'corpType',
					sortable: 'custom',
					showOverflowTooltip: true
				},
				{
					title: '注册资本',
					width: '110px',
					align: 'left',
					field: 'registeredCapital',
					sortable: 'custom',
					showOverflowTooltip: true
				},
				{
					title: '认证来源',
					width: '110px',
					align: 'center',
					field: 'certifyFrom',
					sortable: 'custom',
					showOverflowTooltip: true
				},
				{
					width: 120,
					title: '申请时间',
					align: 'center',
					field: 'updateTime',
					sortable: 'custom',
					showOverflowTooltip: true
				},
				{
					title: '审核状态',
					width: '120px',
					align: 'center',
					field: 'status',
					sortable: 'custom',
					showOverflowTooltip: true,
					render: (h, params) => {
						let status = params.row.status;
						let statusText = '';
						let statusClass = '';
						switch (status) {
							case 0:
								statusText = '待学院审核';
								statusClass = 'warning';
								break;
							case 11:
								statusText = '待招就处审核';
								statusClass = 'primary';
								break;
							case 1:
								statusText = '已审核';
								statusClass = 'success';
								break;
							case 2:
								statusText = '审核未通过';
								statusClass = 'danger';
								break;
							case 9:
								statusText = '草稿';
								statusClass = 'info';
								break;
							default:
								statusText = '未知状态';
								statusClass = 'info';
						}

						return h(
							'el-tag',
							{
								props: {
									size: 'mini',
									type: statusClass
								}
							},
							statusText
						);
					}
				},
				{
					type: 'switch',
					label: '启用',
					align: 'center',
					field: 'status',
					fixed: 'right',
					width: 100,

					hide: true,
					render: (h, params) => {
						return h('el-switch', {
							props: {
								value: params.row.accountStatus + '',
								activeValue: '1',
								inactiveValue: '0'
							},
							on: {
								change: val => {
									this.$request({
										url: `${interfaceUrl.updateAccountStatus}/${val}/${params.row.id}`,
										method: 'GET'
									}).then(res => {
										if (res.rCode == 0) {
											params.row.accountStatus = val;
											this.$message.success('操作成功');
										} else {
											this.$message.error(res.msg);
										}
									});
								}
							}
						});
					}
				},
         {
            width: 150,
              title: '操作',
              type: 'handle',
              fixed: 'right',
              template: '',
              events: [
              {
                code: 'view',
                text: '查看'
              }
            ]
          },
			],
			viewListBtn: {
				width: 150,
				title: '操作',
				type: 'handle',
				fixed: 'right',
				template: '',
				events: [
					{
						code: 'view',
						text: '查看'
					}
				]
			},
			allViewListBtn: {
				title: '操作',
				width: 150,
				type: 'handle',
				fixed: 'right',
				template: '',
				events: [
					{
						code: 'allview',
						text: '查看'
					}
				]
			},
			pageOption: {
				// layout: 'total, prev, pager, next, jumper',
				pageSize: 10,
				// hideOnSinglePage: true,
				position: 'right',
				current: 1,
				pageNum: 1
			},
			selectParams: {},
			params: {
				asc: 'false',
				orderBy: 'updateTime',
        status:'audited'
			}
		};
	},
	computed: {
		formItemList() {
			return [
				{
					title: '企业工商信息',
					contents: [
						{
							label: '企业性质',
							name: 'corpType',
							type: 'select',
							placeholder: '请选择企业性质',
							rules: {
								required: true,
								message: '请选择企业性质',
								trigger: 'blur'
							},
							verify: 'required',
							col: 5,
							sysCode: 'plat_enp_quality',
							'label-key': 'shortName',
							'value-key': 'cciValue'
						},
						{
							label: '企业规模',
							name: 'corpScale',
							placeholder: '请选择企业规模',
							type: 'select',
							controls: false,
							rules: {
								required: true,
								message: '请选择企业规模',
								trigger: 'blur'
							},
							verify: 'required',
							col: 5,
							sysCode: 'plat_enp_scale',
							'label-key': 'shortName',
							'value-key': 'cciValue'
						},
						{
							label: '所属行业',
							name: 'corpIndustry',
							placeholder: '请选择所属行业',
							type: 'select',
							rules: {
								required: true,
								message: '请选择所属行业',
								trigger: 'blur'
							},
							verify: 'required',
							col: 10,
							sysCode: 'plat_enp_industry',
							'label-key': 'shortName',
							'value-key': 'cciValue'
						},
						{
							name: 'fj',
							label: '营业执照',
							type: 'attachment',
							value: '',
							code: 'plat_enterprise_validity',
							'select-type': 'icon-plus',
							preview: true,
							listType: 'picture-card',
							// onPreview: res => {
							// console.log(res);
							// },
							ownId: this.formData.id // 业务id
							//rules: {
							//required: true,
							//    message: '请上传营业执照',
							//    trigger: 'blur'
							//}
						},
						{
							label: '企业名称',
							minWidth: 200,
							name: 'corpName',
							placeholder: '请输入企业名称',
							rules: {
								required: true,
								message: '请输入企业名称',
								trigger: 'blur'
							},
							verify: 'required',
							col: 5
						},
						{
							label: '统一社会信用代码',
							name: 'socialCode',
							placeholder: '请输入统一社会信用代码',
							rules: {
								required: true,
								message: '请输入统一社会信用代码',
								trigger: 'blur'
							},
							verify: 'required',
							col: 5
						},
						{
							label: '企业成立日期',
							name: 'establishDate',
							placeholder: '请输入企业成立日期',
							type: 'date',
							rules: {
								required: true,
								message: '请输入企业成立日期',
								trigger: 'blur'
							},
							verify: 'required',
							col: 5
						},
						{
							label: '注册资本(万元)',
							type: 'number',
							controls: false,
							default: false,
							name: 'registeredCapital',
							placeholder: '请输入注册资本',
							rules: {
								required: true,
								message: '请输入注册资本',
								trigger: 'blur'
							},
							verify: 'required',
							col: 5
						},
						{
							label: '法定代表人',
							name: 'lawPerson',
							placeholder: '请输入法定代表人',
							rules: {
								required: true,
								message: '请输入法定代表人',
								trigger: 'blur'
							},
							verify: 'required',
							col: 5
						},
						{
							label: '企业注册地址',
							name: 'registeredAddress',
							placeholder: '请输入企业注册地址',
							rules: {
								required: true,
								message: '请输入企业注册地址',
								trigger: 'blur'
							},
							verify: 'required',
							col: 5
						},
						{
							label: '营业执照有效期',
							name: 'validityOfDate',
							placeholder: '请输入营业执照有效期',
							type: 'daterange',
							disabled: this.validityOfDateDisable,
							unlinkPanels: true,
							rules: {
								required: false,
								message: '请输入营业执照有效期',
								trigger: 'change'
							},
							col: 5
						},
						{
							label: '',
							name: 'validityOfLicense',
							type: 'select',
							clearable: true,
							placeholder: '营业执照有效期为长期请选择',
							rules: {
								required: false,
								message: '请选择营业执照有效期',
								trigger: 'blur'
							},
							col: 5,
							sysCode: 'plat_enp_validity',
							'label-key': 'shortName',
							'value-key': 'cciValue'
						},
						{
							name: 'businessScope',
							label: '经营范围',
							placeholder: '请输入经营范围',
							type: 'textarea',
							rows: 5,
							rules: {
								required: true,
								message: '请输入经营范围',
								trigger: 'blur'
							},
							verify: 'required',
							col: 10
						}
					]
				},
				{
					title: '企业联系信息',
					contents: [
						{
							type: 'ganged',
							label: '企业所属地区',
							name: 'regionId',
							col: 10,
							ganged: 3,
							filtrateKey: 'parentId',
							valueKey: 'code',
							filtrate: '100000000000',
							labelKey: 'name',
							value: '',
							separator: '-',
							symbol: ',',
							group: false,
							data: this.regionData,
							verify: 'required',
							rules: {
								required: true,
								message: '请输入企业所属地区',
								trigger: 'change'
							}
						},
						{
							label: '通讯地址',
							name: 'contactAddress',
							placeholder: '请输入通讯地址',
							rules: {
								required: true,
								message: '请输入通讯地址',
								trigger: 'blur'
							},
							verify: 'required',
							col: 5
						},
						{
							label: '企业联系人',
							name: 'linkMan',
							placeholder: '请输入企业联系人',
							rules: {
								required: true,
								message: '请输入企业联系人',
								trigger: 'blur'
							},
							verify: 'required',
							col: 5
						},
						{
							label: '联系电话',
							name: 'linkPhone',
							placeholder: '请输入联系电话',
							rules: {
								required: true,
								message: '请输入联系电话',
								trigger: 'blur'
							},
							verify: 'required',
							col: 5
						},
						{
							label: '联系人电子邮箱',
							name: 'linkEmail',
							placeholder: '请输入联系人电子邮箱',
							rules: {
								required: false,
								message: '请输入联系人电子邮箱',
								trigger: 'blur'
							},
							verify: 'email',
							col: 5
						}
					]
				},
				{
					title: '审核意见',
					contents: [
						{
							label: '学院审核信息',
							name: 'collegeAuditCommentInfo',
							type: 'textarea',
							readonly: true,
							rows: 3,
							col: 10,
							hide: this.formData.status === 0
						},
						{
							label: '招就处审核信息',
							name: 'auditCommentInfo',
							type: 'textarea',
							readonly: true,
							rows: 3,
							col: 10,
							hide: this.formData.status === 0 || this.formData.status === 11
						}
					]
				}

			];
		}
	},

	created() {
		this.getUserAuthority();
		this.getDictionary();
	},
	mounted() {},
	methods: {
		// 获取角色信息
		getUserAuthority() {
			// 获取登录用户信息
			const userInfo = JSON.parse(localStorage.getItem('loginUserInfo'));
			this.$request({
				url: '/ybzy/platuser/front/checkRole',
				data: {
					account: userInfo.code,
					roleCode: 'system_reo' //招就处true
				},
				method: 'post'
			}).then(res => {
				this.isSystemReo = res.results.system_reo;
				// this.isSystemReo = false;
			});
		},
		/**
		 * 表格行高
		 */
		tableRowClassName({ row, rowIndex }) {
			let styleRes = {
				height: '54px !important'
			};
			return styleRes;
		},
		inputChange(key, value) {
			if (key == 'validityOfLicense') {
				if (value == '') {
					this.validityOfDateDisable = false;
				} else {
					this.validityOfDateDisable = true;
				}
			}
		},
		hadeSubmit(data) {},
		/**
		 * 操作按钮点击事件
		 * @param {*} res
		 */
		btnClick(res) {
			let code = res.handle.code;
			switch (code) {
				case 'view':
					this.formTitle = '查看';
					this.$request({
						url: interfaceUrl.info + '/' + res.row.id,
						method: 'GET'
					}).then(res => {
						if (res.rCode == 0) {
              console.log(res,'*****************');
							this.formData = res.results;
							this.handleFormData(res);
							this.dialogType = 'view';
							this.selectInfo = res.results.id;
							this.showAllView = true;
						}
					});
					break;
				case 'allview':
					this.formTitle = '查看';
					this.$request({
						url: interfaceUrl.info + '/' + res.row.id,
						method: 'GET'
					}).then(res => {
						if (res.rCode == 0) {
							this.formData = res.results;
							this.dialogType = 'view';
							this.selectInfo = res.results.id;
							this.showForm = false;
							this.showAudit = false;
							this.showAllView = true;
						}
					});
					break;
				default:
					break;
			}
		},

		search(res) {
			const paramsN = { ...res };
			this.params = {
				asc: 'false',
				orderBy: 'updateTime',
				status: this.queryStatus,
				...paramsN
			};
			this.selectParams = paramsN;
		},
		handleClose() {},
		handleFormItemChange() {},
		handleFormSubmit(data) {
			let formData = data;
			delete formData.extMap;
			let url = '';
			if (this.formTitle == '新增') {
				url = interfaceUrl.save;
			} else {
				url = interfaceUrl.update;
			}
			let validityOfDate = data.validityOfDate;
			if (undefined != validityOfDate && validityOfDate.length == 2) {
				data.validityStartDate = validityOfDate[0];
				data.validityEndDate = validityOfDate[1];
				data.validityOfDate = '';
			}
			let validityOfLicense = data.validityOfLicense;

			//处理认证数据
			if (undefined != validityOfLicense && validityOfLicense == true) {
				data.validityOfLicense = validityOfLicense[0];
			}

			//处理法定代表身份证有效期
			let platEnterpriseLawpersonDTO_certDate = data['platEnterpriseLawpersonDTO_certDate'];
			if (
				undefined != platEnterpriseLawpersonDTO_certDate &&
				platEnterpriseLawpersonDTO_certDate.length == 2
			) {
				data['platEnterpriseLawpersonDTO_certStartDate'] = platEnterpriseLawpersonDTO_certDate[0];
				data['platEnterpriseLawpersonDTO_certEndDate'] = platEnterpriseLawpersonDTO_certDate[1];
				data.platEnterpriseLawpersonDTO_certDate = '';
			}

			for (let key in data) {
				if (key.indexOf('_') >= 0) {
					let indexName = key.replace('_', '.');
					data[indexName] = data[key];
				}
			}

			let regionIds = data.regionId;
			let regionId = '';
			for (var i in regionIds) {
				if (regionIds[i].code != '') {
					regionId += ',' + regionIds[i].code;
				}
			}
			data.regionId = regionId.substring(1);

			if (undefined != formData && undefined != formData.dataType && formData.dataType.length > 0) {
				formData.dataType = formData.dataType.join(',');
			}
			formData.status = formData.status ? '1' : '0';
			this.$request({
				url: url,
				data: formData,
				method: 'POST'
			}).then(res => {
				if (res.rCode == 0) {
					this.$message.success('操作成功');
					this.showForm = false;
					this.formData = {};
					this.$refs.table.reload();
				} else {
					data.regionId = regionIds;
					data.validityOfDate = validityOfDate;
					this.$message.error(res.msg);
				}
			});
		},
		/**
		 * 排序变化事件
		 * @param {*} column
		 * @param {*} prop
		 * @param {*} order
		 */
		sortChange(column, prop, order) {
			if (column.order == 'ascending') {
				//升序
				this.params = {
					asc: 'true',
					orderBy: column.prop
				};
			} else if (column.order == 'descending') {
				//降序
				this.params = {
					asc: 'false',
					orderBy: column.prop
				};
			} else {
				//不排序
				this.params = {
					asc: 'false',
					orderBy: 'createTime'
				};
			}
			this.params['status'] = this.queryStatus;
			this.$refs.table.reload();
		},

		/**
		 * 页签切换
		 * @param {*} res
		 */
		handleSelect(res) {

		},
		/**
		 * 处理formData数据为可展示数据
		 */
		handleFormData(res) {
			if (res.results.validityStartDate != undefined && res.results.validityEndDate != undefined) {
				let validityOfDate = [res.results.validityStartDate, res.results.validityEndDate];
				this.formData.validityOfDate = validityOfDate;
			}

			if (
				res.results.validityOfLicense != undefined &&
				res.results.validityOfLicense != 'longperiod'
			) {
				this.formData.validityOfLicense = '';
			} else if (
				res.results.validityOfLicense != undefined &&
				res.results.validityOfLicense == 'longperiod'
			) {
				this.validityOfDateDisable = true;
			}

			//this.formData.status = '1' === this.formData.status ? true : false;
			let selectRegionArray = [];
			if (res.results.regionId != undefined && res.results.regionId != '') {
				let regionIdArray = res.results.regionId.split(',');
				for (var i in regionIdArray) {
					var region = {};
					region.code = regionIdArray[i];
					selectRegionArray.push(region);
				}
				console.log(regionIdArray, 'add');
			}
			this.formData.regionId = selectRegionArray;
			this.formData.registeredCapital = res.results.registeredCapital + '';

			let platEnterpriseEntrustVO = this.formData.platEnterpriseEntrustVO;
			if (platEnterpriseEntrustVO != undefined && platEnterpriseEntrustVO['isUse'] != undefined) {
				this.formData['platEnterpriseEntrustDTO_isUse'] = platEnterpriseEntrustVO['isUse'] + '';
				this.formData['platEnterpriseEntrustDTO_adminName'] =
					platEnterpriseEntrustVO['adminName'] + '';
			}
			this.formData.platEnterpriseEntrustVO = '';

			let platEnterpriseBankVO = this.formData.platEnterpriseBankVO;
			if (platEnterpriseBankVO != undefined && platEnterpriseEntrustVO['isUse'] != undefined) {
				this.formData['platEnterpriseBankDTO_isUse'] = platEnterpriseEntrustVO['isUse'] + '';
				this.formData['platEnterpriseBankDTO_bankName'] = platEnterpriseBankVO['bankName'] + '';
				this.formData['platEnterpriseBankDTO_bankAddress'] =
					platEnterpriseBankVO['bankAddress'] + '';
				this.formData['platEnterpriseBankDTO_branchName'] = platEnterpriseBankVO['branchName'] + '';
				this.formData['platEnterpriseBankDTO_cardNo'] = platEnterpriseBankVO['cardNo'] + '';
			}
			this.formData.platEnterpriseBankVO = '';

			let platEnterpriseLawpersonVO = this.formData.platEnterpriseLawpersonVO;
			if (
				platEnterpriseLawpersonVO != undefined &&
				platEnterpriseLawpersonVO['isUse'] != undefined
			) {
				this.formData['platEnterpriseLawpersonDTO_isUse'] = platEnterpriseLawpersonVO['isUse'] + '';
				this.formData['platEnterpriseLawpersonDTO_legalName'] =
					platEnterpriseLawpersonVO['legalName'] + '';
				this.formData['platEnterpriseLawpersonDTO_legalTel'] =
					platEnterpriseLawpersonVO['legalTel'] + '';
				this.formData['platEnterpriseLawpersonDTO_legalEmail'] =
					platEnterpriseLawpersonVO['legalEmail'] + '';
				this.formData['platEnterpriseLawpersonDTO_idCard'] =
					platEnterpriseLawpersonVO['idCard'] + '';
				this.formData['platEnterpriseLawpersonDTO_certStartDate'] =
					platEnterpriseLawpersonVO['certStartDate'] + '';
				this.formData['platEnterpriseLawpersonDTO_certEndDate'] =
					platEnterpriseLawpersonVO['certEndDate'] + '';
				let certDate = [
					platEnterpriseLawpersonVO['certStartDate'],
					platEnterpriseLawpersonVO['certEndDate']
				];
				this.formData['platEnterpriseLawpersonDTO_certDate'] = certDate;
			}
			this.formData.platEnterpriseLawpersonVO = '';
			this.formData.collegeAuditCommentInfo = `${this.formData.collegeAuditTime} ${this.formData.collegeAuditComment}`;
			this.formData.auditCommentInfo = `${this.formData.collegeAuditTime} ${this.formData.auditComment}`;
		},
		/**
		 * 只读模式
		 */
		readModule(list) {
			for (var i in list) {
				var indexList = list[i].contents;
				for (var k in indexList) {
					var item = indexList[k];
					if (item.name === 'templateDownload' || item.name === 'dataExport') {
						continue;
					}

					item.readonly = true;
					if (item.type && item.type == 'submit') {
						item.contents = [
							{
								type: 'reset',
								text: '取消',
								event: 'cancel'
							}
						];
					}
				}
			}
			this.formItemList = list;
		},
		getListThead(btnJson) {
			let tempThead = this.listThead;
			if (tempThead[tempThead.length - 1].title === '操作') {
				tempThead.pop();
			}
			tempThead.push(btnJson);
			return tempThead;
		},
		/**
		 * 获取下拉字典
		 */
		getDictionary() {
			//行政区划
			this.$request({
				url: interfaceUrl.region,
				method: 'get'
			}).then(res => {
				if (res.rCode == 0) {
					this.regionData = res.results;
				}
			});

			this.$request({
				url: interfaceUrl.typeDic,
				method: 'get'
			}).then(res => {
				if (res.rCode == 0) {
					this.typeDicData = res.results;
				}
			});
		},

	}
};
</script>
<style scoped lang="scss">
@import '../../../assets/style/style.scss';

.content {
	display: flex;
	width: 100%;
	height: 100%;

	::v-deep .es-data-table {
		flex: 1;
		display: flex;
		flex-direction: column;
		height: calc(100% - 58px);

		.es-data-table-content {
			flex: 1;
			height: 0;
			display: flex;
			flex-direction: column;

			.el-table {
				flex: 1;
				// height: 100% !important;
			}

			.es-thead-border {
				.el-table__header {
					th {
						border-right: 1px solid #e1e1e1;
					}
				}
			}
		}
	}

	::v-deep .el-form-item__label {
		background: none;
		border: 0px solid #c2c2c2;
	}
}

.el-dialog__body {
	.btn-box {
		margin-top: 20px;
		display: flex;
		justify-content: flex-end;

		.btn {
			padding: 5px 10px;
			color: #666;
			border: 1px solid #eee;
			cursor: pointer;
			margin-right: 5px;
			border-radius: 4px;
			// &.theme {
			// 	background: $--color-primary;
			// 	color: #fff;
			// 	border-color: $--color-primary;
			// }
		}
	}
}
</style>
