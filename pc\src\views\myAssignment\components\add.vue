<template>
  <div style="width: 100%; height: 100%">
    <es-form ref="form" :model="formData" :contents="formItemList" :submit="false">
      <!-- <template v-slot:other>
        <div class="xxxx1">
          <div v-if="type === 'view'" v-html="html"></div>
          <myEditor ref="myEditor" v-else class="myEditor"></myEditor>
        </div>
      </template> -->
    </es-form>
    <div v-if="type === 'view'" class="myEditorStyle">
      <div class="left">任务/通知内容：</div>
      <div class="viewContent" v-html="html"></div>
    </div>
    <div v-else class="myEditorStyle">
      <div class="left">任务/通知内容：</div>
      <div class="viewContent">
        <myEditor ref="myEditor" class="myEditor"></myEditor>
      </div>
    </div>

    <!-- <div class="myEditorStyle">
      <div class="left">任务/通知内容：</div>
      <myEditor ref="myEditor" class="myEditor"></myEditor>
    </div> -->
    <div style="width: 100%; height: 20px"></div>
    <div class="bottom" v-if="this.type !== 'view'">
      <button
          type="button"
          class="el-button el-button--primary el-button--medium"
          text="确定"
          @click="handleFormSubmit"
      >
        确定
      </button>
      <button
          type="button"
          class="el-button el-button--reset el-button--medium"
          text="取消"
          @click="cancel()"
      >
        取消
      </button>
    </div>
    <!-- <div class="a">
      <div class="b">xxxx</div>
    </div> -->
  </div>
</template>

<script>
import myEditor from './myEditor.vue';
import { v4 as uuidv4 } from 'uuid';
export default {
  props: {
    type: {
      type: String,
      default: ''
    },
    id: {
      type: String,
      default: ''
    }
  },
  components: {
    myEditor
  },
  data() {
    return {
      html: '',
      sendAnnex: '',
      content: '',
      formData: {
        sendTitle: '',
        sendType: '1',
        sendOrgNames: [],
        sendTime: ''
      }
    };
  },
  computed: {
    formItemList() {
      return [
        {
          type: 'text',
          label: '通知标题',
          name: 'sendTitle',
          placeholder: '请输入关键字',
          col: 12,
          readonly: this.type === 'view',
          rules: {
            required: true,
            message: '请选择通知标题',
            trigger: 'change'
          }
        },
        {
          type: 'select',
          label: '通知类型:',
          placeholder: '',
          name: 'sendType',
          col: 12,
          readonly: this.type === 'view',
          sysCode: 'send_type',
          rules: {
            required: true,
            message: '请选择通知类型',
            trigger: 'change'
          }
        },
        {
          name: 'sendOrgNames',
          label: '单位',
          readonly: this.type === 'view',
          type: 'selector',
          types: ['enterprise'],
          placeholder: '请选择单位',
          col: 12,
          multiple: true,
          rules: {
            required: true,
            message: '请选择单位对象',
            trigger: 'blur'
          }
        },
        {
          name: 'sendTime',
          disabled: this.type === 'view',
          placeholder: '请选择申报截止时间',
          col: 12,
          label: '申报截止时间',
          type: 'date',
          // value: '',
          rules: {
            required: true,
            message: '请选择申报截止时间',
            trigger: 'change'
          }
        },
        {
          name: 'fj',
          readonly: this.type === 'view',
          disabled: this.type === 'view',
          label: '附件上传',
          type: 'attachment',
          code: 'taskProject',
          // ownId: 'be61a764-f2a8-4e34-b3f5-5763fbd6750d',
          ownId: this.sendAnnex,

          rules: {
            required: false,
            message: '请上传附件',
            trigger: 'blur'
          }
        }
      ];
    }
  },
  mounted() {
    // const parent = document.querySelector('.es-form-padding');
    // const dom = document.querySelector('.myEditor');
    // // const dom = document.createElement('div');
    // dom.style.width = '200px';
    // dom.style.height = '200px';
    // dom.style.backgroundColor = 'red';
    // const refChild = document.querySelector('.el-form-item');
    // console.log(parent, dom, refChild);
    // parent.insertBefore(dom, refChild);
    // if (this.type !== 'view') {
    // 	const parent = document.querySelector('.xxxx1');
    // 	const a = document.querySelector('.es-form-button');
    // 	parent.appendChild(a);
    // }

    if (this.type === 'view' || this.type === 'edit') {
      this.$.ajax({
        url: '/ybzy/projectTask/info',
        params: {
          id: this.id
        }
      })
          .then(res => {
            this.html = res.results.sendContent;
            this.formData.sendTitle = res.results.sendTitle;
            if (res.results.sendType !== null) {
              this.formData.sendType = res.results.sendType.toString();
            }

            if (res.results.sendTime !== null) {
              this.formData.sendTime = res.results.sendTime;
            }

            // console.log(res.results.sendTime.slice(0, 10));
            // this.formData.sendTime = '2022-11-11';

            this.sendAnnex = res.results.sendAnnex;

            // console.log(res.results.sendOrgNames.split(','));
            // this.formData.sendOrgNames = res.results.sendOrgNames.split(',');
            let showname = res.results.sendOrgNames.split(',');
            let showid = res.results.sendOrgIds.split(',');
            let arr = [];
            showname.forEach((i, index) => {
              let obj = {};
              obj.showid = showid[index];
              obj.showname = showname[index];
              arr.push(obj);
            });
            this.formData.sendOrgNames = arr;
            console.log(this.formData.sendOrgNames, 'this.formData.sendOrgNames');
            // this.formData.sendOrgNames = [
            // 	{
            // 		showid: 'end9ad29b5b1e049c5b5c5b5da553c3175',
            // 		showname: '党政办'
            // 	},
            // 	{
            // 		showid: 'enaa4a52c7691140ba883bf5f0e05e017c',
            // 		showname: '院领导'
            // 	}
            // ];

            if (this.type === 'edit') {
              this.$refs.myEditor.html = res.results.sendContent;
            }

            // this.$refs.myEditor.html = res.results.sendContent;
          })
          .catch(err => {});
    }
  },
  created() {
    if (this.type === 'add') {
      this.sendAnnex = uuidv4();
    }
  },
  methods: {
    cancel() {
      console.log('取消');
      this.$emit('cancel', '取消');
    },
    handleFormSubmit(data) {
      console.log(this.$refs.form);
      this.$refs.form.validate(vaild => {
        if (vaild) {
          if (this.type === 'edit') {
            let ids = [];
            let names = [];
            this.formData.sendOrgNames.forEach(i => {
              ids.push(i.showid);
              names.push(i.showname);
            });

            this.$.ajax({
              url: '/ybzy/projectTask/update',
              method: 'POST',
              data: {
                sendContent: this.$refs.myEditor.html,
                sendTitle: this.formData.sendTitle,
                sendType: this.formData.sendType,
                sendTime: this.formData.sendTime,
                // sendTime: '2022-01-11 00:00:00',
                sendAnnex: this.sendAnnex,
                id: this.id,
                sendOrgIds: ids.join(','),
                sendOrgNames: names.join(',')
              }
            })
                .then(res => {
                  this.$emit('closeDia', '修改');
                  this.$message.success('修改成功');
                })
                .catch(err => {});
          } else {
            let ids = [];
            let names = [];
            this.formData.sendOrgNames.forEach(i => {
              ids.push(i.showid);
              names.push(i.showname);
            });
            this.$.ajax({
              url: '/ybzy/projectTask/save',
              method: 'POST',
              data: {
                sendContent: this.$refs.myEditor.html,
                sendTitle: this.formData.sendTitle,
                sendType: this.formData.sendType,
                // sendTime: '2022-01-22 00:00:00',
                sendTime: this.formData.sendTime,
                sendAnnex: this.sendAnnex,
                sendOrgIds: ids.join(','),
                sendOrgNames: names.join(',')
              }
            })
                .then(res => {
                  this.$emit('closeDia', '新增');
                  this.$message.success('新增成功');
                })
                .catch(err => {});
          }
        }
      });
    },
    changeWangValue(data) {}
  }
};
</script>
<style lang="scss" scoped>
::v-deep .red {
  color: red;
}
.bottom {
  display: flex;
  justify-content: right;
  align-items: center;
  height: 50px;
  text-align: right;
  padding: 0 30px 0 0;
}
.myEditorStyle {
  padding: 0 30px 0 0;
  display: flex;
  .left {
    width: 150px;
    text-align: right;
  }
  .viewContent {
    width: 100%;
    height: 300px;
    border: 1px solid #e9e9e9;
    overflow-y: auto;
  }
}
</style>
