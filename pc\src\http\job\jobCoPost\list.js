import httpApi from '@/http/job/jobCoPost/api.js';
import { host } from '@/../config/config';
import qs from 'qs';
export default {
	computed: {
		component() {
			return {
				// 树表结构
				//name: 'es-tree-group'
				// 纯列表结构
				name: 'es-data-table'
			};
		},
		treeParam() {
			return {
				// 不需要左边树 可以
				//url: httpApi.jobCoPostTree,
				//title: '分类树'
			};
		},
		table() {
			return {
				url: httpApi.jobCoPostList,
				param: {},
				full: true,
				fit: true,
				page: true,
				stripe: true,
				numbers: true,
				border: true,
				'row-key': 'id',
				toolbar: [
					{
						type: 'button',
						contents: [
							// {
							// 	text: '新增',
							// 	type: 'primary'
							// }
							{
								text: '导出',
								exportXls: true,
								icon: 'es-icon-daochu',
								type: 'primary',
								event: () => {
									const params = { ...this.params, ...this.selectParams };
									const url =
										host + '/ybzy/jobCoPost/exportEnterpriseList' + '?' + qs.stringify(params);
									window.open(url, '_self');
								}
							}
						]
					},
					{
						type: 'search',
						reset: true,
						contents: [
							// {
							// 	type: 'select',
							// 	placeholder: '审核状态查询',
							// 	name: 'auditStatus',
							// 	event: 'multipled',
							// 	sysCode: 'job_audit_status',
							// 	'label-key': 'shortName',
							// 	'value-key': 'cciValue',
							// 	clearable: true,
							// 	col: 4
							// },
							{
								type: 'text',
								name: 'keyword',
								placeholder: '关键字查询'
							}
						]
					}
				],
				thead: [
					{
						title: '岗位',
						align: 'center',
						field: 'name'
					},
					{
						title: '学院',
						align: 'center',
						field: 'targetCollegeName'
						// showOverflowTooltip: true
					},
					{
						title: '专业',
						align: 'center',
						field: 'targetMajorName',
						minWidth: 100
						// showOverflowTooltip: true
					},
					{
						title: '岗位类型',
						align: 'center',
						field: 'postType',
						sysCode: 'post_job_type'
					},
					{
						title: '薪资范围',
						align: 'center',
						field: 'salaryStructure'
					},
					// {
					// 	title: '地址',
					// 	align: 'center',
					// 	showOverflowTooltip: true,
					// 	minWidth: 100,
					// 	field: 'address'
					// },
					{
						title: '工作时间',
						align: 'center',
						showOverflowTooltip: true,
						minWidth: 100,
						field: 'workTime'
					},
					{
						title: '发布人',
						align: 'center',
						width: 80,
						field: 'createUserName'
					},
					{
						title: '发布时间',
						align: 'center',
						width: 170,
						field: 'createTime'
						// valueFormat: "yyyy-MM-dd"
					},
					// {
					// 	title: '面向学院',
					// 	align: 'center',
					// 	width: 180,
					// 	field: 'orgName'
					// },
					{
						title: '审核状态',
						width: '120px',
						align: 'center',
						field: 'auditStatus',
						sortable: 'custom',
						showOverflowTooltip: true,
						render: (h, params) => {
							let status = params.row.auditStatus;
							let statusText = '';
							let statusClass = '';
							switch (status) {
								case 0:
									statusText = '待学院审核';
									statusClass = 'warning';
									break;
								case 11:
									statusText = '待招就处审核';
									statusClass = 'primary';
									break;
								case 1:
									statusText = '已审核';
									statusClass = 'success';
									break;
								case 2:
									statusText = '审核未通过';
									statusClass = 'danger';
									break;
								case 9:
									statusText = '草稿';
									statusClass = 'info';
									break;
								default:
									statusText = '未知状态';
									statusClass = 'info';
							}

							return h(
								'el-tag',
								{
									props: {
										size: 'mini',
										type: statusClass
									}
								},
								statusText
							);
						}
					},
					{
						type: 'switch',
						label: '启用',
						align: 'center',
						width: 90,
						field: 'status',
						render: (h, params) => {
							return h('el-switch', {
								props: {
									value: params.row.status + '',
									activeValue: '1',
									inactiveValue: '0'
								},
								on: {
									change: val => {
										this.$request({
											// url: `${interfaceUrl.updateAccountStatus}/${val}/${params.row.id}`,
											url: httpApi.updatePostStatus + `/${val}/${params.row.id}`,
											method: 'GET'
										}).then(res => {
											if (res.rCode == 0) {
												params.row.status = val;
												this.$message.success('操作成功');
											} else {
												this.$message.error(res.msg);
											}
										});
									}
								}
							});
						}
					},
					// {
					// 	title: '审核状态',
					// 	align: 'center',
					// 	// width: 120,
					// 	width: 90,
					// 	field: 'auditStatusStr',
					// 	render: (h, params) => {
					// 		let typeStr;
					// 		// auditStatus  '审核状态：0:待审核,1:已审核,2:审核未通过,9草稿',
					// 		switch (params.row.auditStatus) {
					// 			case 0:
					// 				typeStr = '';
					// 				break;
					// 			case 9:
					// 				typeStr = 'info';
					// 				break;
					// 			case 1:
					// 				typeStr = 'success';
					// 				break;
					// 			case 2:
					// 				typeStr = 'danger';
					// 				break;
					// 		}
					// 		return h(
					// 			'el-tag',
					// 			{ props: { type: typeStr, size: 'mini' } },
					// 			params.row.auditStatusStr
					// 		);
					// 	}
					// },
					{
						title: '操作',
						type: 'handle',
						width: 120,
						fixed: 'right',
						// 审核状态auditStatus：0:待审核（二级学院审核）, 11:待招就处审核（二级学院审核通过）, 1:已审核（招就处审核通过）, 2:审核未通过, 9:草稿
						events: [
							{
								code: 'view',
								text: '查看'
							},
							{
								code: 'audit',
								text: '审核',
								rules: rows => {
									return this.isSystemReo ? rows.auditStatus === 11 : rows.auditStatus === 0;
								}
							},
							{
								code: 'postResumePageBtn',
								text: '投递情况',
								rules: rows => {
									return rows.auditStatus == 1;
								}
							}
							// {
							// 	code: 'delete',
							// 	text: '删除'
							// }
						]
					}
				]
			};
		}
	}
};
