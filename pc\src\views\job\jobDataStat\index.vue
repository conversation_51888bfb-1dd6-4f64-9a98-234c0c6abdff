<template>
	<div v-loading="loading" class="statDataBody">
		<el-row>
			<el-col :span="24">
				<el-card shadow="never">
					<div slot="header" class="statCardHeader">
						<div class="statGroupName">基本统计</div>

						<div class="statGroupSearchInput">
							<el-cascader
								key="areaFilter"
								v-model="filterArea"
								size="mini"
								clearable
								:options="addressOptions"
								placeholder="请筛选项目地点"
								@change="hanldeAreaChange"
							></el-cascader>

							<el-date-picker
								v-model="filterDate"
								style="margin-left: 10px"
								size="mini"
								type="daterange"
								unlink-panels
								value-format="yyyy-MM-dd 00:00:00"
								align="right"
								range-separator="至"
								start-placeholder="开始日期"
								end-placeholder="结束日期"
								:picker-options="pickerOptions"
								@change="hanldeDateChange"
							></el-date-picker>
						</div>
						<!-- <el-button style="margin-left: 30px" size="mini" type="primary" @click="countAll">
							统计
						</el-button> -->
					</div>
					<div class="statsBox">
						<div class="statsItemBox" style="background-color: #65d8ff">
							<div>
								<h3>企业总数</h3>
								<span>{{ statData.enterpriseNum }}</span>
							</div>
						</div>
						<div class="statsItemBox" style="background-color: #ff6b0c">
							<div>
								<h3>招聘岗位总数</h3>
								<span>{{ statData.postNum }}</span>
							</div>
						</div>
						<div class="statsItemBox" style="background-color: #65d8ff">
							<div v-if="statData.areaNum != null">
								<h3>地点招聘数</h3>
								<span>{{ statData.areaNum }}</span>
							</div>
						</div>
						<div class="statsItemBox" style="background-color: #ff6b0c">
							<div v-if="statData.dateRangeNum != null">
								<h3>时段招聘数</h3>
								<span>{{ statData.dateRangeNum }}</span>
							</div>
						</div>
					</div>
				</el-card>
			</el-col>
		</el-row>
		<el-row>
			<el-col :span="24">
				<el-card shadow="never">
					<div slot="header" class="statCardHeader">
						<div class="statGroupName">岗位地点分布</div>
						<div class="statGroupSearchInput">
							<el-date-picker
								v-model="chartFilterDate"
								style="margin-left: 10px"
								size="mini"
								type="daterange"
								unlink-panels
								value-format="yyyy-MM-dd 00:00:00"
								align="right"
								range-separator="至"
								start-placeholder="开始日期"
								end-placeholder="结束日期"
								:picker-options="pickerOptions"
								@change="countPostAreaDistData"
							></el-date-picker>
						</div>
					</div>
					<div ref="postAreaDistChartContainer" style="width: 100%; height: 280px"></div>
				</el-card>
			</el-col>
		</el-row>
	</div>
</template>

<script>
import dataStatApi from '@/http/job/jobDataStat/api.js';
import * as echarts from 'echarts';
export default {
	data() {
		return {
			loading: false,
			statData: {
				enterpriseNum: 0,
				postNum: 0,
				areaNum: null,
				dateRangeNum: null,
				areaDistName: [],
				areaDistNum: []
			},
			filterArea: [],
			filterDate: [],
			chartFilterDate: [],
			addressOptions: [],
			areaTree: [],
			pickerOptions: {
				shortcuts: [
					{
						text: '最近一周',
						onClick(picker) {
							const end = new Date();
							const start = new Date();
							start.setTime(start.getTime() - 3600 * 1000 * 24 * 7);
							picker.$emit('pick', [start, end]);
						}
					},
					{
						text: '最近一个月',
						onClick(picker) {
							const end = new Date();
							const start = new Date();
							start.setTime(start.getTime() - 3600 * 1000 * 24 * 30);
							picker.$emit('pick', [start, end]);
						}
					},
					{
						text: '最近三个月',
						onClick(picker) {
							const end = new Date();
							const start = new Date();
							start.setTime(start.getTime() - 3600 * 1000 * 24 * 90);
							picker.$emit('pick', [start, end]);
						}
					}
				]
			}
		};
	},
	watch: {},
	async mounted() {
		await this.getRegion();
		this.countAll();
	},
	beforeDestroy() {
		window.removeEventListener('resize', () => {});
	},
	methods: {
		countAll() {
			this.countBasicData();
			this.countPostAreaDistData();
		},
		hanldeAreaChange(val) {
			this.loading = true;
			if (!val || val.length == 0) {
				this.statData.areaNum = null;
				this.loading = false;
				return;
			}
			this.$request({
				url: dataStatApi.countBasicData,
				data: { area: this.filterArea },
				method: 'POST',
				format: false,
				headers: {
					contentType: 'application/json'
				}
			})
				.then(res => {
					this.loading = false;
					if (res.rCode === 0) {
						const respData = res.results;
						if (respData) {
							this.statData.areaNum = respData.postNum;
						}
					} else {
						this.$message.error(res.msg);
					}
				})
				.catch(() => {
					this.loading = false;
					this.$message({
						message: '网络异常,请检查网络！',
						type: 'error'
					});
				});
		},
		hanldeDateChange(val) {
			this.loading = true;
			if (!val || val.length == 0) {
				this.statData.dateRangeNum = null;
				this.loading = false;
				return;
			}
			this.$request({
				url: dataStatApi.countBasicData,
				data: { date: this.filterDate },
				method: 'POST',
				format: false,
				headers: {
					contentType: 'application/json'
				}
			})
				.then(res => {
					this.loading = false;
					if (res.rCode === 0) {
						const respData = res.results;
						if (respData) {
							this.statData.dateRangeNum = respData.postNum;
						}
					} else {
						this.$message.error(res.msg);
					}
				})
				.catch(() => {
					this.loading = false;
					this.$message({
						message: '网络异常,请检查网络！',
						type: 'error'
					});
				});
		},
		countBasicData() {
			this.loading = true;
			this.$request({
				url: dataStatApi.countBasicData,
				data: { area: this.filterArea, date: this.filterDate },
				method: 'POST',
				format: false,
				headers: {
					contentType: 'application/json'
				}
			})
				.then(res => {
					this.loading = false;
					if (res.rCode === 0) {
						const respData = res.results;
						if (respData) {
							this.statData.enterpriseNum = respData.enterpriseNum;
							this.statData.postNum = respData.postNum;
						}
					} else {
						this.$message.error(res.msg);
					}
				})
				.catch(() => {
					this.loading = false;
					this.$message({
						message: '网络异常,请检查网络！',
						type: 'error'
					});
				});
		},
		// 获取地区数据
		async getRegion() {
			try {
				await this.$request({
					url: dataStatApi.regionData,
					params: {
						level: 3
					},
					method: 'GET'
				})
					.then(res => {
						const { results } = res;
						this.areaTree = JSON.parse(JSON.stringify(results));
						let lv = 2;
						this.addressOptions = this.handleDealTree(results, lv);
					})
					.catch(() => {
						this.$message({
							message: '网络异常,请检查网络！',
							type: 'error'
						});
					});
			} catch (error) {
				console.error('请求失败:', error);
			}
		},
		handleDealTree(nodes, lv) {
			let l = lv;
			return nodes.map(item => {
				if (Array.isArray(item.children) && item.children.length > 0 && lv > 1) {
					item.children = this.handleDealTree(item.children, --l);
				} else {
					delete item.children; // 删除空的 children 属性
				}
				return item;
			});
		},
		initPostAreaDistChart() {
			const chart = echarts.init(this.$refs.postAreaDistChartContainer);
			const option = {
				tooltip: {},
				xAxis: {
					data: this.statData.areaDistName
				},
				yAxis: {},
				series: [
					{
						type: 'bar',
						data: this.statData.areaDistNum
					}
				]
			};
			chart.setOption(option);
			// 响应式调整
			window.addEventListener('resize', () => chart.resize());
		},
		countPostAreaDistData() {
			try {
				this.$request({
					url: dataStatApi.countAreaDistData,
					data: { date: this.chartFilterDate },
					method: 'POST',
					format: false,
					headers: {
						contentType: 'application/json'
					}
				})
					.then(res => {
						this.loading = false;
						if (res.rCode === 0) {
							const respData = res.results;
							if (respData) {
								debugger;
								this.statData.areaDistName = this.getAreaName(respData.areaDistName);
								this.statData.areaDistNum = respData.areaDistNum;
							}
							console.log(this.statData.areaDistName);
							this.initPostAreaDistChart();
						} else {
							this.$message.error(res.msg);
						}
					})
					.catch(() => {
						this.$message({
							message: '网络异常,请检查网络！',
							type: 'error'
						});
					});
			} catch (error) {
				console.error('请求失败:', error);
			}
		},
		getAreaName(areaIds) {
			let areaNames = [];
			if (areaIds) {
				const cityIds = areaIds.map(item => {
					const parts = item.split(',');
					return parts.length > 1 ? parts[1] : null;
				});
				const districtIds = areaIds.map(item => {
					const parts = item.split(',');
					return parts.length > 1 ? parts[2] : null;
				});
				const tree = this.areaTree;
				const cityNames = cityIds.map(value => this.findLabelInTree(tree, value));
				const districtName = districtIds.map(value => this.findLabelInTree(tree, value));
				areaNames = cityNames.map((item, index) => `${item}\n${districtName[index]}`);
			}
			return areaNames;
		},
		findLabelInTree(tree, value) {
			for (const node of tree) {
				if (node.value === value) {
					return node.label;
				}
				if (node.children && node.children.length > 0) {
					const result = this.findLabelInTree(node.children, value);
					if (result) return result;
				}
			}
			return null;
		}
	}
};
</script>

<style scoped lang="scss">
.statDataBody {
	width: 100%;
	height: 100%;
	padding: 10px;
	overflow: auto;
}
.statCardHeader {
	display: flex;
}
.statGroupName {
	width: 150px;
}
.statGroupSearchInput {
	width: 100%;
	display: flex;
	justify-content: flex-end;
}
.statsBox {
	display: flex;
	flex-direction: row;
	justify-content: space-around;
}
.statsItemBox {
	width: 19%;
	height: 120px;
	// border: 0px solid grey;
	border-radius: 10px;
	padding: 20px;
	// margin-left: 20px;
	// margin-right: 20px;
	display: flex;
	color: rgb(255, 255, 255);
	flex-direction: row;
}
</style>
