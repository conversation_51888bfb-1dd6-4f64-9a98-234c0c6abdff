<template>
	<div class="content" style="hight: 100%">
		<es-data-table
			ref="table"
			:key="tableCount"
			:row-style="tableRowClassName"
			:full="true"
			:fit="true"
			:thead="thead"
			:toolbar="toolbar"
			:page="pageOption"
			:url="dataTableUrl"
			:param="dataTableParam"
			:border="true"
			:numbers="true"
			close
			form
			@btnClick="btnClick"
		></es-data-table>
		<!-- 查看or编辑 -->
		<es-dialog
			v-if="showViewOrUpdatePage"
			:title="formTitle"
			:visible.sync="showViewOrUpdatePage"
			width="680px"
			height="640px"
			:show-scale="false"
			:drag="false"
			append-to-body
		>
			<div v-loading="loading">
				<es-form
					v-if="showViewOrUpdatePage"
					ref="form"
					:model="formData"
					:contents="formItemList"
					collapse
					:submit="formTitle !== '查看'"
					@submit="handleFormSubmit"
					:reset="showViewOrUpdatePage"
				/>
			</div>
		</es-dialog>
	</div>
</template>

<script>
import api from '@/http/maintain/applicationApi';
import SnowflakeId from 'snowflake-id';

export default {
	data() {
		return {
			loading: false,
			buildList: [],
			pageOption: {
				layout: 'total, sizes, prev, pager, next, jumper',
				pageSize: 20,
				// hideOnSinglePage: true,
				position: 'center',
				current: 1,
				pageNum: 1
			},
			//弹出配置
			showViewOrUpdatePage: false,

			formTitle: '新增',
			formData: {},

			tableCount: 1,
			dataTableUrl: api.buildManagerJson,
			dataTableParam: {
				orderBy: 'createTime',
				asc: false
			},
			thead: [],
			toolbar: [],
			editToolbar: [
				{
					type: 'button',
					contents: [
						{
							text: '新增',
							code: 'add',
							type: 'primary'
						}
					]
				},
				{
					type: 'search',
					reset: true,
					contents: [
						{
							type: 'text',
							name: 'keyword',
							placeholder: '关键字查询',
							clearable: true
						}
					]
				}
			],
			listThead: [
				{
					title: '工号',
					align: 'center',
					field: 'userNumber',
					showOverflowTooltip: true
				},
				{
					title: '姓名',
					align: 'center',
					field: 'userName',
					showOverflowTooltip: true
				},
				{
					title: '性别',
					align: 'center',
					field: 'sex',
					showOverflowTooltip: true,
					render: (h, param) => {
						return h('p', null, param.row.sex == '0' ? '男' : '女');
					}
				},
				{
					title: '联系方式',
					align: 'center',
					field: 'phone',
					showOverflowTooltip: true
				},
				{
					title: '管理楼栋',
					align: 'center',
					field: 'buildNameDes',
					showOverflowTooltip: true
				},
				{
					title: '创建时间',
					align: 'center',
					field: 'createTime',
					showOverflowTooltip: true
				}
			],
			btnJson: {
				title: '操作',
				type: 'handle',
				width: 150,
				template: '',
				events: [
					{
						code: 'view',
						text: '查看'
					},
					{
						code: 'edit',
						text: '编辑'
					},
					{
						code: 'delete',
						text: '删除'
					}
				]
			},
			editBtn: {
				type: 'submit',
				skin: 'lay-form-btns',
				contents: [
					{
						type: 'primary',
						text: '确定',
						event: 'confirm'
					},
					{
						type: 'reset',
						text: '取消',
						event: 'cancel'
					}
				]
			},
			cancelBtn: {
				type: 'submit',
				skin: 'lay-form-btns',
				contents: [
					{
						type: 'reset',
						text: '取消',
						event: 'cancel'
					}
				]
			}
		};
	},
	computed: {
		formItemList() {
			const readonly = this.formTitle !== '新增';
			const buildReadonly = this.formTitle === '查看';
			return [
				{
					name: 'userNumber',
					label: '工号',
					placeholder: '请输入工号',
					readonly: readonly,
					col: 12
				},
				{
					name: 'userName',
					label: '姓名',
					placeholder: '请输入姓名',
					rules: {
						required: true,
						message: '请输入姓名',
						trigger: 'blur'
					},
					readonly: readonly,
					col: 12
				},
				{
					name: 'phone',
					label: '联系电话',
					placeholder: '请输入联系电话',
					rules: {
						required: true,
						trigger: 'blur',
						validator: (rule, value, callback) => {
							if (!rule.required) {
								return callback();
							}
							if (!value) {
								return callback(new Error('请输入联系电话'));
							}
							if (/^(?:(?:\+|00)86)?1[3-9]\d{9}$|^0[1-9]\d{1,2}-\d{7,8}$/.test(value)) {
								callback();
							} else {
								callback(new Error('请输入正确的电话格式'));
							}
						}
					},
					readonly: readonly,
					col: 12
				},
				{
					label: '性别',
					name: 'sex',
					placeholder: '请选择性别',
					type: 'select',
					data: [
						{ label: '男', value: 0 },
						{ label: '女', value: 1 }
					],
					'label-key': 'label',
					'value-key': 'value',
					rules: {
						required: true,
						message: '请选择性别',
						trigger: 'change'
					},
					readonly: readonly,
					col: 12
				},
				{
					name: 'idCard',
					label: '身份证号',
					placeholder: '请输入身份证号',
					rules: {
						required: true,
						message: '请输入身份证号',
						trigger: 'blur'
					},
					readonly: readonly,
					col: 12
				},
				{
					label: '管理楼栋',
					name: 'buildCodeArr',
					type: 'select',
					multiple: true,
					placeholder: '请选择管理楼栋',
					data: this.buildList,
					'label-key': 'roomName',
					'value-key': 'code',
					rules: {
						required: true,
						message: '请选择管理楼栋',
						trigger: 'change'
					},
					events: {
						change: name => {
							this.$forceUpdate();
						}
					},
					clearable: true,
					// readonly: buildReadonly,
					col: 12
				},
				{
					label: '备注',
					type: 'textarea',
					name: 'remark',
					placeholder: '请输入...',
					readonly: readonly,
					rows: 5
				}
			];
		}
	},
	created() {
		this.getBuildList();
		//初始化查询待审核列表
		this.thead = this.getListThead(this.btnJson);
		this.toolbar = this.editToolbar;
	},
	methods: {
		/**
		 * 表格行高
		 */
		tableRowClassName() {
			return {
				height: '54px !important'
			};
		},
		getBuildList() {
			this.$request({
				url: api.getBuildingParentList,
				method: 'GET',
				params: { states: 1 }
			}).then(res => {
				if (res.rCode == 0) {
					this.buildList = res.results;
				} else {
					this.$message.error(res.msg);
				}
			});
		},
		getListThead(btnJson) {
			let tempThead = [...this.listThead];
			tempThead.push(btnJson);
			return tempThead;
		},
		// 操作按钮
		btnClick(res) {
			let code = res.handle.code;
			this.formData = {};
			switch (code) {
				case 'add':
					this.formTitle = '新增';
					this.showViewOrUpdatePage = true;
					break;
				case 'view':
					this.openViewPage(res.row.id);
					break;
				case 'edit':
					this.openEditPage(res.row.id);
					break;
				case 'delete':
					this.handleDel(res.row);
					break;
			}
		},
		//打开查看
		openViewPage(id) {
			this.getManagerInfoById(id);
			this.formTitle = '查看';
			// this.showViewOrUpdatePage = true;
		},
		openEditPage(id) {
			this.getManagerInfoById(id);
			this.formTitle = '编辑';
			// this.showViewOrUpdatePage = true;
		},
		handleFormSubmit() {
			this.loading = true;
			let url = '';
			if (this.formTitle == '新增') {
				const snowflake = new SnowflakeId();
				this.formData.id = snowflake.generate();
				url = api.buildManagerSave;
			} else {
				url = api.buildManagerUpdate;
			}
			let saveData = { ...this.formData };
			if (saveData.buildCodeArr !== undefined && saveData.buildCodeArr.length > 0) {
				saveData.buildCodes = saveData.buildCodeArr.join(',');
			}
			//移除buildCodeArr
			delete saveData.buildCodeArr;
			this.$request({
				url: url,
				data: saveData,
				method: 'POST'
			}).then(res => {
				this.loading = false;
				if (res.rCode == 0) {
					this.$message.success('操作成功');
					this.formData = {};
					this.showViewOrUpdatePage = false;
					this.$refs.table.reload();
				} else {
					this.$message.error(res.msg);
				}
			});
		},
		//查询维修员详情
		getManagerInfoById(id) {
			this.loading = true;
			this.$request({ url: api.buildManagerInfo + '/' + id, method: 'GET' })
				.then(res => {
					this.loading = false;
					if (res.rCode === 0) {
						this.formData = res.results;
						if (res.results.buildCodes === null || res.results.buildCodes === '') {
							this.formData.buildCodeArr = [];
						} else {
							this.formData.buildCodeArr = res.results.buildCodes.split(',');
						}
						this.showViewOrUpdatePage = true;
					} else {
						this.$message.error(res.msg);
					}
				})
				.catch(() => {
					this.$message.error('请求失败');
					this.loading = false;
				});
		},
		handleDel(row) {
			this.$confirm('确定要删除该楼栋管理员吗？', '提示', {
				confirmButtonText: '确定',
				cancelButtonText: '取消',
				type: 'warning'
			})
				.then(async () => {
					this.$request({
						url: api.buildManagerDel,
						data: { id: row.id },
						method: 'POST'
					}).then(res => {
						if (res.rCode === 0) {
							this.$message.success('操作成功！');
							this.$refs.table.reload();
						} else {
							this.$message.error(res.msg);
						}
					});
				})
				.catch(() => {});
		}
	}
};
</script>
<style scoped></style>
