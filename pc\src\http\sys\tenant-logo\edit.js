export default {
	computed: {
		formItemList() {
			return [
				{
					name: 'name',
					label: '租户名称',
					value: '',
					col: 12,
					rules: {
						required: false,
						message: '请输入租户名称'
					}
				},
				{
					name: 'title',
					label: '标题',
					value: '',
					col: 12,
					rules: {
						required: false,
						message: '请输入标题'
					}
				},
				{
					name: 'manageLogo',
					label: '管理端LOGO',
					value: '',
					col: 12,
					rules: {
						required: false,
						message: '请输入管理端LOGO'
					}
				},
				{
					name: 'manageLeftBj',
					label: '管理端侧边背景图',
					value: '',
					col: 12,
					rules: {
						required: false,
						message: '请输入管理端侧边背景图'
					}
				},
				{
					name: 'manageLoginLogo',
					label: '管理端登录LOGO',
					value: '',
					col: 12,
					rules: {
						required: false,
						message: '请输入管理端登录LOGO'
					}
				},
				{
					name: 'manageLoginBg',
					label: '管理端登录背景图',
					value: '',
					col: 12,
					rules: {
						required: false,
						message: '请输入管理端登录背景图'
					}
				},
				{
					name: 'pc<PERSON>ogo',
					label: 'PC用户端LOGO',
					value: '',
					col: 12,
					rules: {
						required: false,
						message: '请输入PC用户端LOGO'
					}
				},
				{
					name: 'pcLoginBj',
					label: 'PC用户端登陆背景图',
					value: '',
					col: 12,
					rules: {
						required: false,
						message: '请输入PC用户端登陆背景图'
					}
				},
				{
					name: 'pcLoginLeft',
					label: 'PC登陆左侧图',
					value: '',
					col: 12,
					rules: {
						required: false,
						message: '请输入PC登陆左侧图'
					}
				},
				{
					name: 'pcTecherLogo',
					label: '职教大厅LOGO',
					value: '',
					col: 12,
					rules: {
						required: false,
						message: '请输入职教大厅LOGO'
					}
				},
				{
					name: 'pcTecherBj',
					label: 'PC用户端职教服务大厅背景图',
					value: '',
					col: 12,
					rules: {
						required: false,
						message: '请输入PC用户端职教服务大厅背景图'
					}
				},
				{
					name: 'pcTecherBj2',
					label: '职教大厅背景图二',
					value: '',
					col: 12,
					rules: {
						required: false,
						message: '请输入职教大厅背景图二'
					}
				},
			]
		}
	}
};
