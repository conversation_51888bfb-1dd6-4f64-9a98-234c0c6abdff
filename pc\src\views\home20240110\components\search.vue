<template>
	<div class="search-box">
		<el-input
			v-model.trim="inputVal"
			placeholder="请输入内容"
			class="input-with-select"
			clearable
			@clear="onSearch"
		>
			<el-button
				slot="append"
				:class="activeStyle ? 'active' : ''"
				icon="el-icon-search"
				@click="onSearch"
			></el-button>
		</el-input>
	</div>
</template>

<script>
export default {
	data() {
		return {
			inputVal: '',
			activeStyle: false
		};
	},
	methods: {
		onSearch(e) {
			this.activeStyle = true;
			setTimeout(() => {
				this.activeStyle = false;
			}, 1000);
			this.$emit('onSearch', this.inputVal);
		}
	}
};
</script>

<style lang="scss" scoped>
.search-box {
	width: 500px;
	::v-deep {
		.el-input__inner {
			border-radius: 24px 0 0 24px;
			height: 48px;
			// border: 1px solid #cedae5;
		}
		.el-input-group__append {
			border-radius: 0 24px 24px 0;
			background: #0175e8;
			width: 72px;
			border: none;

			.el-icon-search {
				font-size: 18px;
				margin-left: 5px;
				color: #fff;
			}
		}
		.active {
			animation: pulsate-fwd 0.5s ease-in-out infinite both;
		}
	}
	// 画圆动画
	@keyframes pulsate-fwd {
		0% {
			-webkit-transform: scale(1);
			transform: scale(1);
		}
		50% {
			-webkit-transform: scale(1.1);
			transform: scale(1.1);
		}
		100% {
			-webkit-transform: scale(1);
			transform: scale(1);
		}
	}
}
</style>
