<template>
	<div :id="id" style="width: 100%; height: 100%"></div>
</template>
<script>
export default {
	props: {
		id: {
			type: String,
			required: true
		},
		// title: {
		// 	type: String,
		// 	required: true
		// },
		// color: {
		// 	type: Array,
		// 	required: true
		// },
		seriesData: {
			type: Array,
			required: true
		}
	},
	data() {
		return {
			chart: null
		};
	},
	watch: {
		seriesData() {
			this.draw();
		}
	},
	mounted() {
		this.draw();
	},

	methods: {
		draw() {
			let option = {
				grid: {
					top: '13%',
					bottom: '10%' //也可设置left和right设置距离来控制图表的大小
				},
				tooltip: {
					trigger: 'axis',
					axisPointer: {
						type: 'shadow',
						label: {
							show: true
						}
					}
				},
				legend: {
					data: ['占比', '人数'],
					top: '2%',
					color: '#999',
					itemWidth: 16
				},
				xAxis: {
					data: this.seriesData[0] || [],
					axisLine: {
						show: true, //隐藏X轴轴线
						lineStyle: {
							color: 'balck'
						}
					},
					axisTick: {
						show: false //隐藏X轴刻度
					},
					axisLabel: {
						show: true,
						color: 'balck', //X轴文字颜色
						fontSize: 10,
						interval: 1
					}
				},
				yAxis: [
					{
						type: 'value',
						name: '单位：人数',
						nameTextStyle: {
							color: '#999',
							padding: [0, 20, 10, 0]
						},
						splitLine: {
							show: false
						},
						axisTick: {
							show: false
						},
						axisLine: {
							show: false,
							lineStyle: {
								color: '#999'
							}
						},
						axisLabel: {
							show: true,
							textStyle: {
								color: '#999'
							}
						}
					},
					{
						type: 'value',
						name: '单位：%',
						nameTextStyle: {
							color: '#999',
							padding: [0, 0, 10, 40]
						},
						position: 'right',
						splitLine: {
							show: false
						},
						axisTick: {
							show: false
						},
						axisLine: {
							show: false
						},
						axisLabel: {
							show: true,
							formatter: '{value} %', //右侧Y轴文字显示
							textStyle: {
								color: '#999'
							}
						}
					}
				],
				dataZoom: [
					{
						type: 'slider',
						realtime: true,
						start: 0,
						end: 40, // 初始展示40%
						height: 4,
						fillerColor: 'rgba(17, 100, 210, 0.42)', // 滚动条颜色
						borderColor: 'rgba(17, 100, 210, 0.12)',
						handleSize: 0, // 两边手柄尺寸
						showDetail: false, // 拖拽时是否展示滚动条两侧的文字
						top: '96%'

						// zoomLock:true, // 是否只平移不缩放
						// moveOnMouseMove:true, //鼠标移动能触发数据窗口平移
						// zoomOnMouseWheel :true, //鼠标移动能触发数据窗口缩放
					},
					{
						type: 'inside', // 支持内部鼠标滚动平移
						start: 0,
						end: 40,
						zoomOnMouseWheel: false, // 关闭滚轮缩放
						moveOnMouseWheel: true, // 开启滚轮平移
						moveOnMouseMove: true // 鼠标移动能触发数据窗口平移
					}
				],
				series: [
					{
						name: '占比',
						type: 'line',
						yAxisIndex: 1, //使用的 y 轴的 index，在单个图表实例中存在多个 y轴的时候有用
						// smooth: true, //平滑曲线显示
						showAllSymbol: true, //显示所有图形。
						symbol: 'emptyCircle', //标记的图形为实心圆
						symbolSize: 10, //标记的大小
						itemStyle: {
							//折线拐点标志的样式
							// color: '#EE737A'
							normal: {
								color: 'rgb(4 120 232)', // 拐点的颜色
								borderColor: '#ffffff', // 拐点边框颜色
								borderWidth: 2 // 拐点边框大小
							}
						},
						// lineStyle: {
						// 	color: '#999'
						// },
						// areaStyle: {
						// 	color: 'rgba(5,140,255, 0.2)'
						// },
						data: this.seriesData[1] || []
					},
					{
						name: '人数',
						type: 'bar',
						barWidth: 20,
						itemStyle: {
							color: new this.$echarts.graphic.LinearGradient(0, 0, 0, 1, [
								{
									offset: 0,
									color: '#0D7DE9'
								},
								{
									offset: 1,
									color: '#E5F1FD'
								}
							])
						},
						data: this.seriesData[2] || []
					}
				]
			};
			// if (this.axisData.length === 0) return;
			this.chart && this.chart.dispose();
			this.chart = this.$echarts.init(document.getElementById(this.id));
			// console.log('>>>this.chart', this.$echarts);
			this.chart.setOption(option);
			window.addEventListener('resize', () => {
				this.chart && this.chart.resize();
			});
		}
	}
};
</script>
