<template>
  <div class="content" style="height: 100%">
    <div style="width:100%;height: 100%">
      <es-data-table :row-style="tableRowClassName" v-if="true" ref="table" :key="tableCount" :full="true" :fit="true" :thead="thead" :toolbar="toolbar"
                     @btnClick="btnClick" @selection-change="handleSelectionChange" @edit="changeTable"
                     :page="page" :url="dataTableUrl" :param="dataTableParam" :border="true" :numbers="true" checkbox form>
      </es-data-table>
      <el-dialog :title="formTitle" v-if="showInfoPage" :visible.sync="showInfoPage" width="70%" append-to-body>
        <div style="height: 80vh">
          <infoPage ref="infoPage" v-on:activelyClose="closeInfoPage" :base-data="this.formData" :info-page-mode="this.infoPageMode"></infoPage>
        </div>
      </el-dialog>
    </div>
  </div>
</template>

<script>
import api from '@/http/society/societyIncomeExpendRecord/api';
import InfoPage from "@/views/society/societyIncomeExpendRecord/infoPage.vue";
import SnowflakeId from "snowflake-id";

export default {
  components: {InfoPage},
  data() {
    return {
      formData: {},
      page: {
        pageSize: 10,
        totalCount: 0,
      },
      infoPageMode: 'allOn',
      selectRowData: [],
      selectRowIds: [],
      showInfoPage: false,
      tableCount: 1,
      dialogType: "",
      dataTableUrl: api.societyIncomeExpendRecordList,
      dataTableParam: { orderBy: 'create_time', asc: false, isLeaf: 0 },
      formTitle: '',
      validityOfDateDisable: false,
      thead: [],
      listThead: [
        {
          title: '社团名称',
          align: 'left',
          field: 'societyName',
          sortable: 'custom',
          showOverflowTooltip: true,
        },
        {
          title: '收入/支出',
          width: "140px",
          align: 'left',
          field: 'incExpName',
          sortable: 'custom',
          showOverflowTooltip: true,
        },
        {
          title: '收支类型',
          width: "140px",
          align: 'left',
          field: 'typeName',
          sortable: 'custom',
          showOverflowTooltip: true,
        },
        {
          title: '收支金额',
          width: "120px",
          align: 'left',
          field: 'money',
          sortable: 'custom',
          showOverflowTooltip: true,
        },
        {
          title: '收支时间',
          width: "150px",
          align: 'center',
          field: 'inExTime',
          sortable: 'custom',
          showOverflowTooltip: true,
        },
        {
          title: '记录人',
          width: "120px",
          align: 'center',
          field: 'createUserName',
          sortable: 'custom',
          showOverflowTooltip: true,
        },
        {
          title: '记录时间',
          width: "150px",
          align: 'center',
          field: 'createTime',
          sortable: 'custom',
          showOverflowTooltip: true,
        },
      ],
      btnJson: {
        title: '操作',
        type: 'handle',
        width: 180,
        template: '',
        events: [
          {
            code: 'row',
            text: '查看'
          },
          {
            code: 'row',
            text: '编辑'
          },
          {
            code: 'row',
            text: '删除'
          },
        ]
      },
      tBSelectList: []
    };
  },
  created() {
    //初始化查询待审核列表
    this.thead = this.getListThead(this.btnJson);
  },
  methods: {
    /**
     * 表格行高
     */
    tableRowClassName() {
      return {
        "height": "54px !important"
      };
    },
    btnClick(res){
      let text = res.handle.text;
      let code = res.handle.code;

      if(code === 'row'){
        switch (text){
          case '查看':
          case '编辑': this.openInfoPage(res.row.id, text); break;
          case '删除': this.deleteRows([res.row.id]); break;
        }
      }else {
        switch (text){
          case '新增': this.openInfoPage(null, text); break;
          case '查看':
            if(this.selectRowIds.length>1){
              this.$message.warning('只能选择一个查看');
            }else if(this.selectRowIds.length<1){
              this.$message.warning('请选择一个进行查看');
            }else {this.openInfoPage(this.selectRowIds[0], text);}
            break;
          case '删除':
            if(this.selectRowIds.length<1){
              this.$message.warning('请选择一个删除');
            }else {this.deleteRows(this.selectRowIds);}
            break;
        }
      }
    },
    //打开infoPage
    openInfoPage(id, pageMode){
      if(pageMode !== '新增'){
        this.$request({
          url: api.societyIncomeExpendRecordInfo,
          params: { id: id},
          method: 'GET'
        }).then(res => {
          //处理请求结果
          this.formData = {...res.results};
          this.formData.societyId = {value: this.formData.societyId, label:this.formData.societyName};
          this.formData.typeId = {value: this.formData.typeId, label:this.formData.typeName};

          this.formTitle = pageMode;
          this.infoPageMode = pageMode;
          this.showInfoPage = true;
        });
      }else {
        this.formTitle = pageMode;
        //初始化表单数据
        const snowflake = new SnowflakeId();
        this.formData = { id: snowflake.generate() };
        this.formData.status = true;
        this.infoPageMode = pageMode;
        this.showInfoPage = true;
      }
    },
    //关闭infoPage
    closeInfoPage(reload){
      this.formData = {};
      this.showInfoPage = false;
      if(reload)
        this.$refs.table.reload();
    },
    // 数据列表多选回调
    handleSelectionChange(data) {
      let ids = [];
      data.forEach(row => {
        ids.push(row.id);
      });
      this.selectRowData = data;
      this.selectRowIds = ids;
    },
    deleteRows(ids){
      this.$confirm('确定要删除选中的数据吗？', '删除', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      })
          .then(() => {
            this.$request({
              url: api.societyIncomeExpendRecordDeleteByIds,
              data: { ids: ids.join(',') },
              method: 'POST'
            }).then(res => {
              if (res.rCode === 0) {
                this.$message.success('操作完成');
                this.$refs.table.reload();
              } else {
                this.$message.error(res.msg);
              }
            });
          })
          .catch(() => {});
    },
    getListThead(btnJson){
      let tempThead = [...this.listThead];
      tempThead.push(btnJson);
      return tempThead;
    },
    getTBSelectList(res){
      this.$request({
        url: api.societyIncomeExpendTypeSelectData,
        params: { typeName: res, type: 1, orderBy: 'createTime', asc: true},
        method: 'GET',
      }).then(result => {
        if(result.success){
          this.tBSelectList = [...result.results.records];
          this.$nextTick(()=>{
            this.$forceUpdate();
          })
        }else {
          this.$message.error(result.msg);
        }
      })
    },
    changeTable(val) {
      switch (val.name){
        case 'status': break;
      }
    },
  },
  computed: {
    toolbar(){
      return [
        {
          type: 'button',
          contents: [
            {
              text: '新增',
              code: 'toolbar',
            },
            {
              text: '删除',
              code: 'toolbar',
              type: 'danger'
            },
            {
              text: '查看',
              code: 'toolbar',
              type: 'primary'
            },
          ]
        },
        {
          type: 'search',
          reset: true,
          contents: [
            {
              type: 'text',
              name: 'keyword',
              placeholder: '社团名称',
              clearable: true,
            },
            {
              type: 'select',
              name: 'incExpType',
              placeholder: '收入/支出',
              clearable: true,
              data: [
                { value:'1', label:'收入' },
                { value:'2', label:'支出' },
              ],
            },
            {
              type: 'select',
              name: 'typeId',
              placeholder: '收支类型',
              clearable: true,
              filterable: true,
              'value-key': 'value',
              'label-key': 'label',
              'filter-method': this.getTBSelectList,
              data: this.tBSelectList,
            },
          ]
        },
      ]
    },
  }
};
</script>