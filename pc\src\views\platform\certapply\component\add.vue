<template>
	<es-form
		ref="form"
		:model="formData"
		:contents="formList"
		v-bind="$attrs"
		@submit="handleSubmit"
		@reset="handleCancel"
		@change="handleFormItemChange"
		height="100%" :genre="2" collapse
	></es-form>
</template>
<script>

import interfaceUrl from '@/http/platform/certapply.js';
import $ from 'eoss-ui/lib/utils/util';
import request from 'eoss-ui/lib/utils/http';
import SnowflakeId from "snowflake-id";

export default {
	name: 'addDialog',
	props: {
		selectInfo: {
			type: String
		},
		formInfo: {
			type: Object,
			default: () => {}
		},
		title:{
			type: String
		}
	},
	data() {
		return {
			formData: {},
			editBtn: {
                type: 'submit',
                skin: 'lay-form-btns',
                contents: [
                    {
                        type: 'primary',
                        text: '确定',
                        event: 'confirm'
                    },
                    {
                        type: 'reset',
                        text: '取消',
                        event: 'cancel'
                    },
                ]
            },
			certificationStuffList:[],//资料列表
			typeDicData:[],
			enterpriseDicData:[],
			personUrl: interfaceUrl.personList,
			watchNum: 0,//监听计数
		};
	},
	computed: {
		formList() {
			let indexFormList =  [
				{
					title: '基本信息',
					contents: [
						{
							name: 'personData',
							label: '人员姓名',
							type: 'selector',
							placeholder: '请选择人员',
							col: 6,
							//readonly: true,
							multiple: false,
							'value-type':"string",
							filterable:false,
							tree:false,
							replace: false,
							selection:this.personUrl,
							'value-key':"value",
							'label-key':"label",
							multiple:false,
							filtrate:false,
							col: 10,
							title:"选择人员",
							tabs:{ enterprise: { url: this.personUrl, name: 'enterprise', label: '人员', data: [] } },
							rules: {
								required: true,
								message: '请选择人员',
								trigger: 'blur'
							},
							confirm:"handlePersonSeletor",
						},
						{
							label: '业务类型',
							name: 'businessTypeId',
							type: 'select',
							placeholder: '请选择业务类型',
							rules: {
								required: true,
								message: '请选择业务类型',
								trigger: 'blur'
							},
							verify: 'required',
							col: 10,
							data: this.typeDicData,
							'label-key': 'name',
							'value-key': 'value',
						}
					]
				},
			];
			if(this.certificationStuffList.length>0){
				//放入认证资料信息
				indexFormList.push({
					title: '认证资料',
					contents: this.certificationStuffList
				})
			}
			
			return indexFormList;
		}
	},
	watch:{
		//监听企业的变化
		'formData.personData':{
			handler(newVal,oldVal){
				if(this.title=="编辑"&&this.watchNum==0){
					//如果是编辑，且第一次监听，则不执行
					this.watchNum++;
				}else if(newVal!=oldVal){
					this.formData.personId = newVal[0].value;
					this.getDictionary(true);
					this.watchNum++;
				}
			},
			deep: true
		}
	},
	created() {
		
		if(this.selectInfo==undefined||this.selectInfo===""){
			const snowflake = new SnowflakeId();
			let id = snowflake.generate();
			this.selectInfo = id;
			this.formData = {id:id};
		}else{
			this.$request({
				url: interfaceUrl.info+'/'+this.selectInfo,
				method: 'GET'
			}).then(res => {
				if (res.rCode == 0) {
					this.formData = res.results;
					this.getDictionary(false);
					let personData = [{
						label: this.formData.xm,
						name: this.formData.xm,
						value: this.formData.personId
					}];
					this.formData.personData = personData;
					let typeData = {
						label: this.formData.businessTypeName,
						name: this.formData.businessTypeName,
						value:this.formData.businessTypeId,
					}
					this.formData.businessTypeId = typeData;
					this.getCertificationStuffList(typeData.value);
				} else {
					this.$message.error(res.msg);
				}
			});
		}

	},
	methods: {
		handleFormItemChange(key, val) {
			if(key==="businessTypeId"){
				this.getCertificationStuffList(val);
			}
		},
		handleSubmit(data) {
			const loading = $.loading(this.$loading, '提交中');
			let url = "";
            if (this.title == '新增') {
                url = interfaceUrl.save;
            } else {
                url = interfaceUrl.update;
            }
			let method = 'POST';
			let formData = { ...data};
			formData.personId = this.formData.personData[0].value;
			let typeData = formData.businessTypeId;
			if( typeof(typeData)==="object"){
				formData.businessTypeId = this.formData.businessTypeId.value;
			}

			
			let flag = 'left'; // 定义左表格刷新还是右表格刷新
			request({
				url:url,
				method: 'POST',
				data: formData
			}).then(res => {
				loading.close();
				this.$emit('cancel');
				if (res.rCode === 0) {
					this.$emit('refresh', flag); // 刷新数据
					this.$message.success(res.msg);
				} else {
					this.$message.error(res.msg);
				}
			});
		},
		// 隐藏对话框
		handleCancel(event) {
			if (event.type === 'reset') {
				this.$emit('cancel');
			}
		},
		/**
         * 获取下拉字典
         */
         getDictionary(ischange){
             this.$request({
                 url: interfaceUrl.unApplyTypeDic+"?personId="+this.formData.personId,
                 method: 'get'
             }).then(res => {
                 if (res.rCode == 0) {
					if(ischange){
						this.formData.businessTypeId = "";
						this.certificationStuffList = [];
					}
                     this.typeDicData = res.results;
					 
                 }
             });
 
         },
		 /**
		  * 获取企业类型认证资料配置
		  * @param {*} enterpriseTypeId 
		  */
		 getCertificationStuffList(typeId){
			this.$request({
                 url: interfaceUrl.certificationStuff+'?typeId=' + typeId,
                 method: 'get'
             }).then(res => {
                 if (res.rCode == 0) {
					 let stuffList = [];
					 if(res.results.length>0){
						for(let i in res.results){
							let isRequired = false;
							if(res.results[i].isRequired==1){
								isRequired = true;
							}
							stuffList.push({
								name: 'fj',
								label: res.results[i].dataName,
								type: 'attachment',
								value: '',
								code: res.results[i].dataCode,
								preview:true,
								// onPreview: res => {
								// 	debugger;
								// 	alert("aaa");
								// },
								ownId: this.selectInfo, // 业务id
								rules: {
								   required: isRequired,
								   message: '请上传'+res.results[i].dataName,
								   trigger: 'blur'
								}
							});
						}
						
					}
                     this.certificationStuffList = stuffList;
                 }
             });
		 }
	}
};
</script>
<style  lang="scss">
@import '../../../../assets/style/style.scss';


.el-dialog__body {
	overflow: auto !important;
    .es-form .es-collapse{
        height: 100%;
    }

}
</style>