<!--
 @desc:基本信息
 @author: WH
 @date: 2023/11/14
 -->
<template>
	<div class="main">
		<div class="node-info">
			<p class="title">
				当前节点：
				<span>{{nodeValue}}</span>
			</p>
			<p class="text">填写意见</p>
			<el-input
				placeholder="填写意见"
				type="textarea"
				resize="none"
				:rows="6"
				:count="300"
				class="text-area"
				v-model="specification"
			></el-input>
			<el-select
				v-model="textValue"
				clearable
				@change="levelPramsChange"
				placeholder="--请选择常用语--"
			>
				<el-option
					v-for="item in textOption"
					:key="item.value"
					:label="item.label"
					:value="item.value"
				></el-option>
			</el-select>
		</div>
		<div class="btn-box">
			<es-button type="primary" @click="save">提交</es-button>
			<es-button @click="save">暂存</es-button>
		</div>
	</div>
</template>

<script>
import {
	getPorjectInfo,
	updatePorjectInfo,
	getFlowFormInfo,
	getArchiveInfo,
	getFinalAcceptanceInfo
} from '@/api/scientific-sys.js';

export default {
	components: {},
	inject: ['id', 'openType', 'initActiveName', 'projectClassifyStr'],
	data() {
		return {
			nodeValue:"填报",
			textValue: '',
			textOption: [],
			basicInfo: {},
			formData: {},
			row: {}
		};
	},
	computed: {
		formItemList() {
			// LENTHWAYS("lengthWays", "纵向项目",0),
			// CROSSWISE("crosswise", "横向项目",1),
			// COLLEGE("college", "院级项目",2),
			return [
				{
					name: 'remark',
					placeholder: '',
					type: 'textarea',
					col: 12
				},
				{
					name: 'projectType',
					label: '项目类型',
					type: 'select',
					sysCode: 'project_type',
					rules: {
						message: '--请选择常用语--',
						trigger: 'blur'
					},
					col: 6
				}
			];
		}
	},
	created() {},
	mounted() {},
	methods: {
		save() {}
	}
};
</script>

<style lang="scss" scoped>
@import '@ast/style/public.scss';
.main {
	height: 100%;
	width: 300px;
	overflow: auto;
	padding: 0 10px;
	background-color: rgb(248, 248, 248);
	.node-info {
		flex: 1;
		overflow: auto;
	}
	.title {
		height: 44px;
		line-height: 44px;

		background-color: #f8f8f8;
	}
	.text {
		font-size: 14px;
		height: 30px;
		line-height: 30px;
	}
	.text-area {
		margin-bottom: 10px;
	}
	.btn-box {
		@include flexBox();
		width: 100%;
		padding-bottom: 10px;
		margin-top: 20px;
	}
}
</style>
