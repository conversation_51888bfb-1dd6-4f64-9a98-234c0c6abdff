<!--  -->
<template>
	<div class="BacgroundBox">
		<div class="report-card">
			<el-button type="primary" style="margin-bottom: 20px" @click="add()">新增</el-button>
			<es-data-table
				ref="table"
				row-key="id"
				:data="tableData"
				full
				:thead="thead"
				:toolbar="toolbar"
				:url="dataTableUrl"
				:page="pageOption"
				:param="params"
				@btnClick="btnClick"
			></es-data-table>
		</div>

		<!-- 查看 -->
		<es-dialog
			title="查看"
			:visible.sync="showView"
			width="1300px"
			:drag="false"
			:close-on-click-modal="false"
			:destroy-on-close="true"
			@close="formData = {}"
		>
			<es-form
				v-if="showView"
				ref="formRef"
				:model="formData"
				:contents="formItemList"
				table
				readonly
				label-width="180"
			/>
		</es-dialog>
	</div>
</template>

<script>
import { v4 as uuidv4 } from 'uuid';

export default {
	components: {},
	data() {
		return {
			tableData: [],
			pageOption: {
				layout: 'total, sizes, prev, pager, next, jumper',
				pageSize: 10,
				position: 'center',
				current: 1,
				pageNum: 1
			},
			params: {
				orderBy: 't1.update_time',
				asc: 'false'
			},
			dataTableUrl: '/ybzy/platDisciplineInspectionReach/listJson',
			showView: false,
			formData: {}
		};
	},
	computed: {
		toolbar() {
			return [
				{
					type: 'search',
					reset: true,
					contents: [
						{
							type: 'text',
							name: 'title',
							placeholder: '请输入标题',
							col: 3
						}
					]
				}
			];
		},
		thead() {
			return [
				{
					title: '标题',
					field: 'title',
					showOverflowTooltip: true,
					align: 'center'
				},
				{
					title: '是否匿名',
					width: 90,
					field: 'anonymity'
				},
				{
					title: '举报人姓名',
					field: 'reporterName',

					align: 'center'
				},

				{
					title: '被举报人姓名',
					field: 'beReporterName',
					align: 'center'
				},
				{
					title: '被举报人单位',
					align: 'left',
					showOverflowTooltip: true,
					field: 'beReporterDepartment'
				},
				{
					title: '职务',
					field: 'beReporterJob',
					showOverflowTooltip: true,

					align: 'center'
				},

				{
					title: '操作',
					type: 'handle',
					width: 150,
					template: '',
					events: [
						{
							code: 'view',
							text: '查看'
						}
					]
				}
			];
		},
		formItemList() {
			return [
				{
					name: 'createTime',
					label: '申请时间',
					col: 6,
					placeholder: '请输入项目名称',
					rules: {
						required: true,
						message: '请输入项目名称'
					}
				},
				{
					name: 'isAnonymity',
					label: '是否匿名',
					col: 6,
					placeholder: '请输入项目编号',
					rules: {
						required: true,
						message: '请输入项目编号'
					}
				},
				{
					name: 'reporterName',
					label: '举报人姓名',
					col: 6,
					placeholder: '请输入中标供应商名称',
					rules: {
						required: true,
						message: '请输入中标供应商名称'
					}
				},
				{
					name: 'reporterIdcard',
					label: '举报人身份证号',
					col: 6,
					placeholder: '请输入合同金额',
					rules: {
						required: true,
						message: '请输入合同金额'
					}
				},
				{
					name: 'beReporterName',
					clearable: true,
					label: '被举报人姓名',
					col: 6,
					rules: {
						required: true,
						message: '请选择采购部门'
					}
				},

				{
					name: 'eReporterDepartment',
					label: '单位',
					col: 6,
					rules: {
						required: true,
						message: '请选择预验收部门'
					}
				},

				{
					name: 'beReporterJob',
					label: '职务',
					col: 6,
					rules: {
						required: true,
						message: '请选择预验收部门'
					}
				},
				{
					name: 'title',
					label: '标题',
					col: 6,
					rules: {
						required: true,
						message: '请选择预验收部门'
					}
				},
				{
					name: 'content',
					label: '主要内容',
					type: 'textarea',
					col: 12,
					rules: {
						required: true,
						message: '请选择预验收部门'
					}
				}
			];
		}
	},
	created() {},

	mounted() {},
	methods: {
		btnClick(res) {
			let row = res.row;
			let code = res.handle.code;
			switch (code) {
				case 'view':
					// 查看
					// this.showView = true;
					// this.formData = row;
					this.$router.push('/DirectTrain' + '?id=' + row.id);
					// window.open('/#/DirectTrain?id=' + row.id);
					break;
				default:
					break;
			}
		},
		add() {
			this.$router.push('/DirectTrain');
		}
	}
};
</script>
<style lang="scss" scoped>
.BacgroundBox {
	background: #d3e6fc;
	height: 100vh;
	overflow: scroll;
	padding: 20px;

	.report-card {
		margin: 0 auto;
		width: 70%;
		height: 100vh;
		background-color: #f2f2f2;
		border-radius: 8px;
		padding: 20px 60px;
		box-shadow: 0 0 5px rgba(#000, 0.1);
		margin-bottom: 20px;
		letter-spacing: 2px;
	}
}
</style>
