## 注意

##### 运行本工程之前请先修改package.jso文件中`name`和`description`的值
```
{
  "name": "project-module",    修改为GIT仓库名称或者模块名称
  "description": "eoss新框架开发模板工程", 修改为项目名称或者GIT仓库名称或者模块名称（中文）
  ...
}
```
## 选择行尾序列 LF
若出现`Delete ␍ eslint(prettier/prettier)`错误提示，请按以下方式修改
```
 设置：git config --global core.autocrlf false
 git全局配置之后，重新拉取代码。
```

## 项目开启步骤

##### 1.安装依赖

```
npm install
//或者用  yarn install ||  cnpm install

```

##### 2.启动项目

```
npm run serve
```

##### 3.项目打包

```
npm run build
```

## 安装&使用新框架

eoss-ui 文档地址： [http://admin.dev.wisesoft.net.cn/service/eoss-ui/index.html]()

##### npm i eoss-element

```
import Element from 'eoss-element';
Vue.use(Element, { size: 'small' });
```

##### npm i eoss-ui

```
import EsUi from 'eoss-ui';
Vue.use(EsUi);
```

##### 更新 eoss 依赖

```
npm uninstall eoss-ui
npm install eoss-ui
```

## 新框架样式文件使用


```
/*设置主题色*/

/*自定义换肤主题配置*/
$--color-primary: var(--theme-primary);
$--color-primary-light-1: var(--theme-primary-light-1);
$--color-primary-light-2: var(--theme-primary-light-2);
$--color-primary-light-3: var(--theme-primary-light-3);
$--color-primary-light-4: var(--theme-primary-light-4);
$--color-primary-light-5: var(--theme-primary-light-5);
$--color-primary-light-6: var(--theme-primary-light-6);
$--color-primary-light-7: var(--theme-primary-light-7);
$--color-primary-light-8: var(--theme-primary-light-8);
$--color-primary-light-9: var(--theme-primary-light-9);
$--color-primary-light-10: var(--theme-primary-light-10);

/*固定颜色主题配置*/
$--color-primary: #e94c41;

/*以下必须*/

$--el-font-path: '~eoss-element/lib/theme-chalk/fonts';
@import "~eoss-element/packages/theme-chalk/src/index";
$--es-font-path: '~eoss-ui/lib/theme-chalk/fonts';
@import "~eoss-ui/packages/theme-chalk/src/index";

/*以下必须 end*/

/*main.js 引入*/
import './assets/style/theme.scss';

```

## 项目结构说明

```
|--node_modules 依赖的node工具包目录
|--public
| |-- favicon.ico: 页签图标
| |-- index.html: 主页面
|--src
| |-- assets 静态资源, images, icons, styles等
| |-- components 公用组件
| |-- http 接口, 统一管理
| |-- router 路由, 统一管理
| |-- store vuex, 统一管理
| |-- views 视图目录
| | |-- public 公用页面模块（文件夹名称）
| | |-- |-- Error.vue 路由访问错误页面
| | |-- |-- Process.vue 业务流程页面
| | |-- system 系统管理模块（文件夹名称,此为demo文件，新开发模块用不上的可删除）
| | |-- |-- MenuManagement.vue system下子页面
| | |-- |-- PermissionManagement.vue system下子页面
| | |-- |-- UserGroupManagement.vue system下子页面
| | |-- |-- UserManagement.vue system下子页面
| |-- App.vue 项目的主组件，所有页面都是在App.vue下切换的
| |-- main.js 入口文件，初始化vue实例并使用需要的插件
|-- .gitignore: git 版本管制忽略的配置
|-- .npmrc: npm镜像地址配置；
|-- babel.config.js babel 的配置文件
|-- package.json  应用包配置文件
|-- package-lock.json 包版本控制文件
|-- README.md  应用描述文件
|-- vue.config.js 可选的配置文件
```

## XXXXXXX 项目

#### 注：不要使用 master 分支

##### web 端 使用 pc 分支，项目文件放在 pc 分支下 pc 文件夹

##### APPH5 使用 app 分支，项目文件放在 md 分支下 app 文件夹

##### 大屏 使用 big 分支，项目文件放在 big 分支下 big 文件夹

##### 平板 使用 pad 分支，项目文件放在 pad 分支下 pad 文件夹
