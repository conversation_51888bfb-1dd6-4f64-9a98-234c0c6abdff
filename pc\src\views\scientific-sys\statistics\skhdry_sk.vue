<template>
	<div v-loading="loading" class="content">
		<es-data-table
			ref="table"
			:row-style="tableRowClassName"
			:thead="thead"
			:toolbar="toolbar"
			:border="true"
			filter
			:page="pageOption"
			:url="basics.dataTableUrl"
			:numbers="true"
			:param="params"
			show-label
			close
			@search="search"
			@reset="search({})"
			@btnClick="btnClick"
		></es-data-table>
	</div>
</template>

<script>
import { host } from '@/../config/config';
import qs from 'qs';
export default {
	data() {
		return {
			basics: {
				dataTableUrl: '/ybzy/projectBaseInfo/queryPageMapSocialPerson', // 列表接口
				download: '/ybzy/projectBaseInfo/exportSocialPersons' // 导出
			},
			loading: false,
			params: {},
			selectParams: {},
			pageOption: {
				layout: 'total, prev, pager, next, jumper',
				pageSize: 20,
				// hideOnSinglePage: true,
				position: 'center',
				current: 1,
				pageNum: 1
			},
			codesObj: {} //批量数据字典
		};
	},
	computed: {
		thead() {
			return [
				{
					title: '姓名',
					field: 'name',
					align: 'center',
					showOverflowTooltip: true,
					fixed: 'left',
					width: 100
				},
				{
					title: '职工号',
					field: 'number',
					align: 'center',
					showOverflowTooltip: true,
					width: 100
				},
				{
					title: '是否统计',
					field: 'isStatistics',
					align: 'center',
					showOverflowTooltip: true,
					width: 100
				},
				{
					title: '所属机构',
					field: 'orgName',
					align: 'center',
					showOverflowTooltip: true,
					width: 100
				},
				{
					title: '性别',
					field: 'sex',
					align: 'center',
					width: 100
				},
				{
					title: '出生日期',
					field: 'birthdate',
					align: 'center',
					width: 100
				},
				{
					title: '职称',
					field: 'rank',
					align: 'center',
					width: 100
				},
				{
					title: '最后学历',
					field: 'highestEducation',
					align: 'center',
					width: 100
				},
				{
					title: '最后学位',
					field: 'highestDegree',
					align: 'center',
					width: 100
				},
				{
					title: '一级学科',
					field: 'firstLevelDiscipline',
					align: 'center',
					showOverflowTooltip: true,
					width: 120
				},
				{
					title: '二级学科',
					field: 'subDiscipline', 
					align: 'center',
					showOverflowTooltip: true,
					width: 120
				},
				{
					title: '三级学科',
					field: 'tertiaryDiscipline',
					align: 'center',
					showOverflowTooltip: true,
					width: 120
				},
				{
					title: '相关学科',
					field: 'subject',
					align: 'center',
					showOverflowTooltip: true,
					width: 120
				},
				{
					title: '政治面貌',
					field: 'politicalStatus',
					align: 'center',
					width: 100
				},
				{
					title: '研究方向',
					field: 'researchDirection',
					align: 'center',
					showOverflowTooltip: true,
					width: 150
				},
				{
					title: '英文名',
					field: 'englishName',
					align: 'center',
					showOverflowTooltip: true,
					width: 120
				},
				{
					title: '曾用名',
					field: 'formerName',
					align: 'center',
					width: 100
				},
				{
					title: '身份证',
					field: 'idCard',
					align: 'center',
					showOverflowTooltip: true,
					width: 180
				},
				{
					title: 'E-mail',
					field: 'email',
					align: 'center',
					showOverflowTooltip: true,
					width: 150
				},
				{
					title: '行政职务',
					field: 'administrativePost',
					align: 'center',
					showOverflowTooltip: true,
					width: 120
				},
				{
					title: '定职时间',
					field: 'confirmPostDate',
					align: 'center',
					width: 100
				},
				{
					title: '国籍',
					field: 'nationality',
					align: 'center',
					width: 100
				},
				{
					title: '民族',
					field: 'nation',
					align: 'center',
					width: 100
				},
				{
					title: '手机',
					field: 'phone',
					align: 'center',
					width: 120
				},
				{
					title: '办公电话',
					field: 'officePhone',
					align: 'center',
					width: 120
				},
				{
					title: '个人网站',
					field: 'personalWebsite',
					align: 'center',
					showOverflowTooltip: true,
					width: 150
				},
				{
					title: '是否为博导',
					field: 'isDoctoralAdvisor',
					align: 'center',
					width: 100
				},
				{
					title: '第一外语',
					field: 'firstLanguage',
					align: 'center',
					width: 100
				},
				{
					title: '第一外语程度',
					field: 'firstLanguageLevel',
					align: 'center',
					width: 120
				},
				{
					title: '第二外语',
					field: 'secondLanguage',
					align: 'center',
					width: 100
				},
				{
					title: '第二外语程度',
					field: 'secondLanguageLevel',
					align: 'center',
					width: 120
				},
				{
					title: '进修情况',
					field: 'furtherStudy',
					align: 'center',
					showOverflowTooltip: true,
					width: 150
				},
				{
					title: '学术兼职',
					field: 'socialWork',
					align: 'center',
					showOverflowTooltip: true,
					width: 150
				},
				{
					title: '学术特长',
					field: 'socialSpeciality',
					align: 'center',
					showOverflowTooltip: true,
					width: 150
				}
			];
		},
		toolbar() {
			return [
				{
					type: 'button',
					contents: [
						{
							text: '导出',
							exportXls: true,
							icon: 'es-icon-daochu',
							type: 'primary',
							event: () => {
								const params = { ...this.params, ...this.selectParams };
								const url = host + this.basics.download + '?' + qs.stringify(params);
								window.open(url, '_self');
							}
						}
					]
				},
				{
					type: 'search',
					reset: true,
					showLabel: true,
					contents: [
						{
							type: 'daterange',
							name: 'dates',
							label: '时间范围',
							placeholder: '请选择时间范围'
						},
						{ type: 'text', label: '关键字', name: 'keyword', placeholder: '请输入关键字查询' }
					]
				}
			];
		}
	},
	created() {
		const codes = 'member_nation,hq_person_education,hq_person_state';
		this.$utils.findSysCodeList(codes).then(res => {
			this.codesObj = res;
		});
	},
	methods: {
		//表格行高
		tableRowClassName({ row, rowIndex }) {
			let styleRes = { height: '54px !important' };
			return styleRes;
		},
		//操作按钮点击事件
		btnClick(res) {},
		search(res) {
			const paramsN = { ...res };
			if (paramsN.dates) {
				paramsN.startDate = paramsN.dates[0] || '';
				paramsN.endDate = paramsN.dates[1] || '';
				delete paramsN.dates;
			}
			this.params = {
				...{
					projectType: this.params.projectType,
					orderBy: this.params.orderBy,
					asc: this.params.asc // true 升序，false 降序
				},
				...paramsN
			};
			this.selectParams = paramsN;
		},
		refreshData() {
			this.$refs.table.reload();
		}
	}
};
</script>

<style scoped lang="scss">
.content {
	width: 100%;
	height: 100%;
}
</style>
