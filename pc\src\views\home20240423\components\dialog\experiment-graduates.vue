<template>
	<div class="">
		<div class="person-card" v-for="(item, index) in personList" :key="index">
			<p class="card-title">
				<span>{{ item.name }}</span>
				<img
					class="card-img"
					:src="require('@/assets/images/home20240423/information-icon1.png')"
					alt=""
				/>
			</p>
			<div class="card-footer">
				<p class="left">
					<span class="num">{{ item.value }}</span>
					<span class="unit">{{ item.unit }}</span>
				</p>
				<p class="right">
					<span class="desc">较上年 0%</span>
					<img
						class="desc-img"
						:src="require('@/assets/images/home20240423/down-icon.png')"
						alt=""
					/>
				</p>
			</div>
		</div>
	</div>
</template>

<script>
export default {
	components: {},
	data() {
		return {
			personList: [
				{
					name: '毕业人数',
					value: '0',
					unit: '人'
				},
				{
					name: '就业人数',
					value: '0',
					unit: '人'
				},
				{
					name: '升学人数',
					value: '0',
					unit: '人'
				},
				{
					name: '就业率',
					value: '0',
					unit: '%'
				},
				{
					name: '未就业人数',
					value: '0',
					unit: '人'
				}
			]
		};
	},
	mounted() {
		// this.createEmploymentStudentsSexECharts()
	},
	methods: {
		// createEmploymentStudentsSexECharts(data) {
		// 	if (this.studentsSexCharts) {
		// 		this.studentsSexCharts.dispose();
		// 	}
		// 	let chartDom = document.getElementById('employmentStudentsSex');
		// 	if (!chartDom) {
		// 		return;
		// 	}
		// 	this.studentsSexCharts = echarts.init(chartDom);
		// 	let options = this.studentsSexOptions;
		// 	// options.series[0].data = this.data;
		// 	this.studentsSexCharts.setOption(options);
		// },
	}
};
</script>

<style lang="scss" scoped>
.person-card {
	width: calc(20% - 6px);
	height: 130px;
	padding: 22px 26px;
	background: rgba(252, 253, 254, 0.7);
	box-shadow: 0px 0px 10px 0px rgba(5, 32, 70, 0.1);
	border-radius: 8px;
	border: 2px solid #ffffff;
	.card-title {
		font-family: MicrosoftYaHei, MicrosoftYaHei;
		font-weight: bold;
		font-size: 18px;
		color: #454545;
		line-height: 24px;
		display: flex;
		justify-content: space-between;
	}
	.card-img {
		width: 40px;
		height: 44px;
	}
	.card-footer {
		margin-top: 5px;
		display: flex;
		justify-content: space-between;
		align-items: baseline;
		.num {
			font-family: DINPro, DINPro;
			font-weight: bold;
			font-size: 36px;
			color: #0a325b;
			line-height: 46px;
		}
		.unit {
			margin-left: 11px;
			font-family: MicrosoftYaHei;
			font-size: 16px;
			color: #294d79;
			line-height: 21px;
		}
		.desc {
			font-family: MicrosoftYaHei;
			font-size: 16px;
			color: #7b96b1;
			line-height: 21px;
		}
		.desc-img{
			width: 8px;
			margin-left: 5px;
		}
	}
}
</style>