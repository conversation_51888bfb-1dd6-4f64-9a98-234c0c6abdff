<template>
	<div class="institution">
		<div class="institution-content-right">
			<div class="header">
				<el-button type="primary" size="small" @click="handleAdd">新增</el-button>
			</div>
			<el-table :data="tableData" style="width: 100%">
				<el-table-column type="index" label="序号" />
				<el-table-column prop="cateCode" label="分类编号" />
				<el-table-column prop="cateName" label="分类名称" />
				<el-table-column prop="sortNum" label="序号" />
				<el-table-column label="操作">
					<template #default="scope">
						<el-button type="text" size="small" @click="handleEdit(scope.row)">编辑</el-button>
						<el-button type="text" size="small" @click="handleDelete(scope.row)">删除</el-button>
					</template>
				</el-table-column>
			</el-table>
		</div>
		<el-dialog
			:title="title"
			:visible.sync="dialogVisible"
			width="500px"
			modal-append-to-body
			append-to-body
		>
			<el-form :model="form" ref="form" :rules="rules" label-width="90px" label-position="right">
				<el-form-item label="分类编号:" prop="cateCode">
					<el-input v-model="form.cateCode" />
				</el-form-item>
				<el-form-item label="分类名称:" prop="cateName">
					<el-input v-model="form.cateName" />
				</el-form-item>
				<el-form-item label="排序号:" prop="sortNum">
					<el-input-number v-model="form.sortNum" />
				</el-form-item>
			</el-form>
			<span slot="footer" class="dialog-footer">
				<el-button @click="dialogVisible = false">取 消</el-button>
				<el-button type="primary" @click="handleConfirm">确 定</el-button>
			</span>
		</el-dialog>
	</div>
</template>

<script>
export default {
	data() {
		return {
			dialogVisible: false,
			tableData: [],
			title: '新增部门',
			form: {
				cateCode: '',
				cateName: '',
				sortNum: ''
			},
			rules: {
				cateCode: [{ required: true, message: '请输入分类编号', trigger: 'blur' }],
				cateName: [{ required: true, message: '请输入分类名称', trigger: 'blur' }],
				sortNum: [{ required: true, message: '请输入排序号', trigger: 'blur' }]
			},
			apiPrefix: process.env.NODE_ENV === 'development' ? '' : '/ybzy-hr-api/person'
		};
	},
	watch: {
		filterText(val) {
			this.$refs.tree.filter(val);
		}
	},
	mounted() {
		this.getTableData();
	},
	methods: {
		handleAdd() {
			this.title = '新增类别';
			this.dialogVisible = true;
		},
		handleConfirm() {
			this.$refs.form.validate(valid => {
				if (valid) {
					if (this.title == '新增类别') {
						this.$requestRs({
							url: `${this.apiPrefix}/hrnCategory/save`,
							method: 'post',
							data: this.form
						}).then(res => {
							if (res.code == 200) {
								this.$message.success('新增成功');
								this.getTableData();
								this.dialogVisible = false;
							}
						});
					} else {
						this.$requestRs({
							url: `${this.apiPrefix}/hrnCategory/update`,
							method: 'post',
							data: this.form
						}).then(res => {
							if (res.code == 200) {
								this.$message.success('编辑成功');
								this.getTableData();
								this.dialogVisible = false;
							}
						});
					}
				}
			});
		},
		handleEdit(row) {
			this.title = '编辑类别';
			this.$requestRs({
				url: `${this.apiPrefix}/hrnCategory/info`,
				method: 'get',
				params: { id: row.id }
			}).then(res => {
				this.form = res.data;
			});
			this.dialogVisible = true;
		},
		handleDelete(row) {
			this.$confirm('确定要删除吗?', '提示', {
				confirmButtonText: '确定',
				cancelButtonText: '取消',
				type: 'warning'
			})
				.then(() => {
					this.$requestRs({
						url: `${this.apiPrefix}/hrnCategory/deleteById`,
						method: 'post',
						params: { id: row.id }
					}).then(res => {
						if (res.code == 200) {
							this.getTableData();
							this.$message.success('删除成功');
						}
					});
				})
				.catch(() => {});
		},
		getTableData() {
			this.$requestRs({
				url: `${this.apiPrefix}/hrnCategory/queryCategory`,
				method: 'get'
			}).then(res => {
				this.tableData = res.data;
			});
		}
	}
};
</script>

<style lang="scss" scoped>
.institution {
	padding: 20px;
	.header {
		padding-bottom: 10px;
		margin-bottom: 10px;
		border-bottom: 1px solid #e6e6e6;
		display: flex;
		justify-content: space-between;
		align-items: center;
	}
}
</style>
