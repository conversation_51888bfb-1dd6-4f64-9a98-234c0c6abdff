<template>
	<div class="institution">
		<div class="institution-content-left">
			<el-input placeholder="输入关键字进行过滤" v-model="filterText"></el-input>

			<el-tree
				class="filter-tree"
				:data="data"
				:props="defaultProps"
				:filter-node-method="filterNode"
				@node-click="handleNodeClick"
				ref="tree"
			></el-tree>
		</div>
		<div class="institution-content-right">
			<div class="header">
				<el-button type="primary" size="small" @click="handleAdd">新增</el-button>
			</div>
			<el-table
				v-loading="loading"
				:data="tableData"
				height="calc(100vh - 170px)"
				style="width: 100%"
			>
				<el-table-column type="index" label="序号" />
				<el-table-column prop="fullName" label="部门全称" />
				<el-table-column prop="shortName" label="部门简称" />
				<el-table-column prop="code" label="代码" />
				<el-table-column prop="sortNum" label="序号" />
				<el-table-column label="操作">
					<template #default="scope">
						<el-button type="text" size="small" @click="handleEdit(scope.row)">编辑</el-button>
						<el-button type="text" size="small" @click="handleDelete(scope.row.id)">删除</el-button>
					</template>
				</el-table-column>
			</el-table>
			<!-- <div class="pagination">
				<el-pagination
					background
					layout="prev, pager, next"
					:total="25"
					:page-size="20"
					:current-page="1"
				/>
			</div> -->
		</div>
		<el-dialog
			:title="title"
			:visible.sync="dialogVisible"
			width="500px"
			modal-append-to-body
			append-to-body
		>
			<el-form :model="form" ref="form" :rules="rules" label-width="90px" label-position="right">
				<el-form-item label="所属分类:" prop="cateCode">
					<el-select
						style="width: 100%"
						clearable
						@change="$forceUpdate()"
						multiple
						filterable
						v-model="form.cateCode"
						placeholder="请选择"
					>
						<el-option
							v-for="item in categoryList"
							:key="item.code"
							:label="item.name"
							:value="item.code"
						/>
					</el-select>
				</el-form-item>
				<el-form-item label="岗位选择:" prop="positionid">
					<el-select
						style="width: 100%"
						@change="$forceUpdate()"
						clearable
						filterable
						multiple
						v-model="form.positionid"
						placeholder="请选择"
					>
						<el-option
							v-for="item in positionList"
							:key="item.id"
							:label="item.positionName"
							:value="item.id"
						/>
					</el-select>
				</el-form-item>
				<el-form-item label="部门全称:" prop="fullName">
					<el-input v-model="form.fullName" />
				</el-form-item>
				<el-form-item label="部门简称:" prop="shortName">
					<el-input v-model="form.shortName" />
				</el-form-item>
				<el-form-item label="代码:" prop="code">
					<el-input v-model="form.code" />
				</el-form-item>
				<el-form-item label="排序号:" prop="sortNum">
					<el-input-number v-model="form.sortNum" />
				</el-form-item>
			</el-form>
			<span slot="footer" class="dialog-footer">
				<el-button @click="dialogVisible = false">取 消</el-button>
				<el-button type="primary" @click="handleConfirm">确 定</el-button>
			</span>
		</el-dialog>
	</div>
</template>

<script>
export default {
	data() {
		return {
			filterText: '',
			data: [],
			defaultProps: {
				children: 'child',
				label: 'name'
			},
			dialogVisible: false,
			tableData: [],
			title: '新增部门',
			form: {
				cateCode: [],
				fullName: '',
				shortName: '',
				code: '',
				sortNum: '',
				supCode: '',
				positionid: []
			},
			rules: {
				fullName: [{ required: true, message: '请输入部门全称', trigger: 'blur' }],
				shortName: [{ required: true, message: '请输入部门简称', trigger: 'blur' }],
				code: [{ required: true, message: '请输入代码', trigger: 'blur' }],
				sortNum: [{ required: true, message: '请输入排序号', trigger: 'blur' }]
			},
			loading: false,
			categoryList: [],
			positionList: [],
			apiPrefix: process.env.NODE_ENV === 'development' ? '' : '/ybzy-hr-api/person'
		};
	},
	watch: {
		filterText(val) {
			this.$refs.tree.filter(val);
		}
	},
	mounted() {
		this.getTree();
		this.getTableList('root');
		this.getCategoryList();
		this.getPositionList();
	},
	methods: {
		getPositionList() {
			this.$requestRs({ url: `${this.apiPrefix}/hrnPosition/listJson`, method: 'get' }).then(
				res => (this.positionList = res.data || [])
			);
		},
		getCategoryList() {
			this.$requestRs({
				url: `${this.apiPrefix}/hrnCategory/selectCategory`,
				method: 'get'
			}).then(res => (this.categoryList = res.data || []));
		},
		filterNode(value, data) {
			if (!value) return true;
			return data.label.indexOf(value) !== -1;
		},
		handleAdd() {
			this.title = '新增部门';
			this.dialogVisible = true;
			this.form = {
				cateCode: '',
				fullName: '',
				shortName: '',
				code: '',
				sortNum: '',
				supCode: this.form.supCode || 'root'
			};
		},
		handleConfirm() {
			this.$refs.form.validate(valid => {
				console.log(this.form, 78);
				if (valid) {
					let categoryList = (this.form.cateCode || []).map(item => {
						return {
							cateCode: item
						};
					});
					let positionList = (this.form.positionid || []).map(item => {
						return {
							id: item
						};
					});
					this.form.categoryList = categoryList;
					this.form.positionList = positionList;
					delete this.form.cateCode;
					delete this.form.positionid;
					if (this.title == '新增部门') {
						this.form.supCode = this.form.supCode ? this.form.supCode : 'root';
						this.$requestRs({
							url: `${this.apiPrefix}/hrnDepartment/saveDepartment`,
							method: 'post',
							data: this.form
						}).then(res => {
							if (res.code == 200) {
								this.$message.success('新增成功');
								this.getTableList(this.form.supCode);
								this.getTree();

								this.dialogVisible = false;
							}
						});
					} else {
						this.$requestRs({
							url: `${this.apiPrefix}/hrnDepartment/updateDepartment`,
							method: 'post',
							data: this.form
						}).then(res => {
							if (res.code == 200) {
								this.$message.success('编辑成功');
								this.getTableList(this.form.supCode);
								this.dialogVisible = false;
							}
						});
					}
				}
			});
		},
		// 获取机构树
		getTree() {
			this.$requestRs({
				url: `${this.apiPrefix}/hrnDepartment/getDepartmentTree`,
				method: 'get'
			}).then(res => {
				this.data = res.data;
			});
		},
		// 获取列表
		getTableList(supCode) {
			this.loading = true;
			this.$requestRs({
				url: `${this.apiPrefix}/hrnDepartment/getDepListBySupCode`,
				method: 'get',
				params: { supCode }
			})
				.then(res => {
					this.tableData = res.data || [];
					this.loading = false;
				})
				.catch(() => (this.loading = false));
		},
		handleNodeClick(data) {
			this.form.supCode = data.code;
			this.getTableList(this.form.supCode);
		},
		handleDelete(id) {
			this.$confirm('确定要删除吗?', '提示', {
				confirmButtonText: '确定',
				cancelButtonText: '取消',
				type: 'warning'
			})
				.then(() => {
					this.$requestRs({
						url: `${this.apiPrefix}/hrnDepartment/deleteById`,
						method: 'post',
						params: {
							id: id
						}
					}).then(res => {
						if (res.code == 200) {
							this.$message.success('删除成功');
							this.getTableList(this.form.supCode);
							this.getTree();
						}
					});
				})
				.catch(() => {});
		},
		handleEdit(row) {
			this.title = '编辑部门';
			this.$requestRs({
				url: `${this.apiPrefix}/hrnDepartment/selectDepartmentInfo`,
				method: 'get',
				params: {
					id: row.id
				}
			}).then(res => {
				this.form = res.data;
				this.form.cateCode = this.form.categoryList.map(item => item.cateCode);
				this.form.positionid = this.form.positionList.map(item => item.id);
			});
			this.dialogVisible = true;
		}
	}
};
</script>

<style lang="scss" scoped>
.institution {
	padding: 20px;
	display: flex;
	.header {
		padding-bottom: 10px;
		margin-bottom: 10px;
		border-bottom: 1px solid #e6e6e6;
		display: flex;
		justify-content: space-between;
		align-items: center;
	}
	.institution-content-left {
		width: 250px;
		flex-shrink: 0;
		padding-right: 20px;
		::v-deep .el-tree {
			height: calc(100vh - 150px);
			overflow: auto;
		}
	}
	.institution-content-right {
		width: calc(100% - 250px);
	}
	.pagination {
		margin-top: 20px;
		display: flex;
		justify-content: flex-end;
	}
}
</style>
