/*
 * @Author: 张海滔
 * @Date: 2023-06-09 10:36:28
 * @LastEditors: 张海滔
 * @LastEditTime: 2023-11-16 09:31:38
 * @FilePath: /project-ybzy/pc/src/utils/index.js
 * @Description:
 */
const Decimal = require('decimal.js');
import { host } from '../../config/config';
import request from 'eoss-ui/lib/utils/http';
import { Message } from 'eoss-element';

// 批量数据字典并缓存
export function findSysCodeList(sysAppCodes) {
	return new Promise(resolve => {
		const codeArr = sysAppCodes.split(',');
		const codesObj = JSON.parse(sessionStorage.getItem('codesObj') || '{}');
		// 已经有的就不请求
		const sysAppCodesNew = codeArr.filter(item => !codesObj[item]);
		if (sysAppCodesNew.length > 0) {
			request({
				url: '/sys/v1/mecpSys/findSysCodeList.dhtml',
				method: 'get',
				params: {
					sysAppCodes: sysAppCodesNew.join(',')
				}
			}).then(res => {
				if (res.rCode == 0) {
					const resData = res.results || {};
					sessionStorage.setItem('codesObj', JSON.stringify({ ...codesObj, ...resData }));
					resolve(resData);
				} else {
					resolve(codesObj);
				}
			});
		} else {
			resolve(codesObj);
		}
	});
}

/**
 * 判断是否具有电商权限
 * @returns {boolean} 是否有权限
 */
export function hasEcomPermission() {
	const href = window.location.href;
	const allowedDomains = ['localhost', 'ybzy.cn']; // 允许的域名列表
	return allowedDomains.some(domain => href.includes(domain)) || false;
}

function handleNum(num) {
	if (typeof num !== 'number' && typeof num !== 'string') {
		return 0;
	} else {
		return num;
	}
}
// 科学计算 +
export const computedAdd = (a, b) => {
	return Decimal(handleNum(a))
		.add(Decimal(handleNum(b)))
		.toNumber();
};
// 科学计算 -
export const computedSub = (a, b) => {
	return Decimal(handleNum(a))
		.sub(Decimal(handleNum(b)))
		.toNumber();
};
// 科学计算 X
export const computedMul = (a, b) => {
	return Decimal(handleNum(a))
		.mul(Decimal(handleNum(b)))
		.toNumber();
};
// 科学计算 /
export const computedDiv = (a, b) => {
	return Decimal(handleNum(a))
		.div(Decimal(handleNum(b)))
		.toNumber();
};
//获取url地址栏指定参数
export const getLocationParams = keyWords => {
	// 提取路由值（字符串） encodeURI解码在window.open环节打开时this.$router.resolve用encodeURI加码
	let href = decodeURI(window.location.href);
	// 从占位符开始截取路由（不包括占位符）
	let query = href.substring(href.indexOf('?') + 1);
	// 根据 & 切割字符串
	let vars = query.split('&');
	for (let i = 0; i < vars.length; i++) {
		let pair = vars[i].split('=');
		// 根据指定的参数名去筛选参数值
		if (pair[0] == keyWords) {
			return pair[1];
		}
	}
	return null;
};

/**
 * 不在main主体下打开的href地址
 *  window.open用到 取到的地址后有个斜杠 xxx/
 * 配合this.$router.resolve（）用 取到的内容是 #/
 */
export const openWindowHref = () => {
	try {
		const regex = new RegExp('main.html', 'g');
		let modifiedString = window.location.href.replace(regex, '');
		return modifiedString.split('#')[0];
	} catch (error) {
		return window.location.host;
	}
};

/**
 * 清空所有cookie
 */
export const clearAllcookie = () => {
	const cookies = document.cookie.split('; ');
	for (let i = 0; i < cookies.length; i++) {
		const cookie = cookies[i].trim(); // 添加trim()方法去除首尾空白字符
		const eqPos = cookie.indexOf('=');
		const name = eqPos > -1 ? cookie.substring(0, eqPos) : cookie;
		// 设置过期时间为过去的时间以清除cookie
		document.cookie = `${name}=; expires=Thu, 01 Jan 1970 00:00:00 UTC; path=/;`;
	}
};
// 退出登录
export const logout = () => {
	request({
		url: '/ybzy/front/platuser/loginOut',
		method: 'POST'
	}).then(res => {
		if (res.rCode == 0) {
			localStorage.clear();
			sessionStorage.clear();
			clearAllcookie();
			const origin = window.location.origin + '/';
			window.location.href = `${origin}project-ybzy/ybzy_zhxy/index.html#/login`;
		} else {
			Message({
				message: res.message || res.msg,
				type: 'warning'
			});
		}
	});
};

// 计算设计与宽比缩放页面
export const toolViewRatio = (width = 1920) => {
	// 假设设计稿宽度y
	const designWidth = width || 1920;
	const screenWidth = window.innerWidth;
	// 计算缩放比例
	const scale = screenWidth / designWidth;
	console.log(screenWidth, designWidth, '-----------', scale);
	return scale > 1 ? 1 : scale;
};

// 删除附件
export const deleteFile = (code, ownId) => {
	const params = {
		code,
		ownId
	};
	request({
		url: '/main2/mecpfileManagement/getAdjunctFileInfos',
		params,
		method: 'get'
	}).then(res => {
		if (res.rCode === 0) {
			res.results.forEach(e => {
				const params2 = {
					userName: e.userName,
					id: e.adjunctId
				};
				request({
					url: '/main2/mecpfileManagement/delAdjunct',
					params: params2,
					method: 'get'
				}).then(res2 => {
					if (res2.rCode === 0) {
						console.log('删除附件成功:' + e.originalName);
					}
				});
			});
		}
	});
};

// 下载附件
export const DownLoadFile = async (code, ownId) => {
	try {
		const params = { code, ownId };
		const res = await request({
			url: '/main2/mecpfileManagement/getAdjunctFileInfos',
			params,
			method: 'get'
		});

		if (res.rCode === 0) {
			for (const e of res.results) {
				const { adjunctId, ownId } = e;
				const link = `${host}main2/mecpfileManagement/downloadByAdjunctId?adjunctId=${adjunctId}&documentId=${ownId}`;
				const a = document.createElement('a');
				a.href = link;
				a.download = ''; // 此处可以指定下载后文件名
				document.body.appendChild(a);
				a.click();
				document.body.removeChild(a);
			}
			return res.msg;
		}
	} catch (error) {
		return error.message || '下载失败';
	}
};
