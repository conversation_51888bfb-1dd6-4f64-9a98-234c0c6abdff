<template>
	<basic-container>
		<avue-crud
			ref="crud"
			v-model="form"
			:option="option"
			:table-loading="loading"
			:data="data"
			:page="page"
			:permission="permissionList"
			:before-open="beforeOpen"
			:cell-style="cellStyle"
			@row-update="rowUpdate"
			@row-save="rowSave"
			@row-del="rowDel"
			@search-change="searchChange"
			@search-reset="searchReset"
			@selection-change="selectionChange"
			@current-change="currentChange"
			@size-change="sizeChange"
			@refresh-change="refreshChange"
			@on-load="onLoad"
		>
			<template slot="menuRight">
				<div style="display: flex; flex-direction: row">
					<avue-select
						v-model="query.status"
						size="small"
						placeholder="请选择状态"
						:dic="statusDic"
					></avue-select>
					<el-input
						v-model="query.name"
						placeholder="请输入模型名称"
						prefix-icon="el-icon-search"
						size="small"
					></el-input>
					<es-button type="primary" size="medium" @click="doSearch">搜索</es-button>
					<es-button size="medium" @click="searchReset">重置</es-button>
				</div>
			</template>
			<template slot="menu" slot-scope="scope">
				<el-button type="text" size="small" @click="toShowFields(scope.row.id, scope.row.type)">
					字段列表
				</el-button>
			</template>
		</avue-crud>
	</basic-container>
</template>

<script>
import modelUrl from '@/http/platform/model.js';
// import modelFieldUrl from '@/http/platform/modelfield.js';
// import nodeUrl from '@/http/platform/node';
const defQuery = {
	type: '',
	name: null,
	status: null
};
const defPage = {
	pageSize: 10,
	currentPage: 1,
	total: 0
};

const typeList = [
	{
		value: 'info',
		label: '文章模型'
	},
	{
		value: 'node',
		label: '目录模型'
	}
]; //状态列表
const statusList = [
	{
		value: 1,
		label: '启用'
	},
	{
		value: 0,
		label: '停用'
	}
]; //状态列表
// const defForm = {
// 	type: 'info',
// 	status: 1
// };
export default {
	components: {},
	data() {
		return {
			data: [],
			form: {},
			query: Object.assign({}, defQuery),
			loading: true,
			page: {
				pageSize: 10,
				currentPage: 1,
				total: 0
			},
			selectionList: [],
			statusDic: statusList,
			typeDic: typeList,
			option: {
				size: 'medium',
				menuWidth: 380,
				height: 'auto',
				calcHeight: 'auto',
				align: 'center',
				searchShow: true,
				searchMenuSpan: 6,
				tip: false,
				border: true,
				viewBtn: true,
				columnBtn: false,
				refreshBtn: false,
				selection: false,
				dialogClickModal: false,
				dialogMenuPosition: 'center',
				column: [
					{
						label: '模型名称',
						prop: 'name',
						span: 12,
						rules: [
							{
								required: true,
								message: '请输入模型名称',
								trigger: 'blur'
							}
						]
					},
					{
						label: '模型编码',
						prop: 'code',
						span: 12,
						rules: [
							{
								required: true,
								message: '请输入模型编码',
								trigger: 'blur'
							}
						]
					},
					// {
					// 	label: '模型类型',
					// 	prop: 'type',
					// 	type: 'radio',
					// 	value: 'info',
					// 	dicData: typeList,
					// 	rules: [
					// 		{
					// 			required: true,
					// 			message: '请选择类型',
					// 			trigger: 'blur'
					// 		}
					// 	]
					// },
					{
						label: '状态',
						prop: 'status',
						type: 'radio',
						value: 1,
						dicData: statusList,
						rules: [
							{
								required: true,
								message: '请选择状态',
								trigger: 'blur'
							}
						]
					}
				]
			}
		};
	},
	computed: {
		permissionList() {
			return {
				addBtn: true,
				viewBtn: true,
				delBtn: true,
				editBtn: true
			};
		},
		ids() {
			let ids = [];
			this.selectionList.forEach(ele => {
				ids.push(ele.id);
			});
			return ids.join(',');
		}
	},
	created() {},
	methods: {
		cellStyle({ row, column, rowIndex, columnIndex }) {
			if (column.property === 'status') {
				if (row.status === 0) {
					return 'color:red';
				}
			}
		},
		handleOpen(key, keyPath) {
			this.page = Object.assign({}, defPage);
			this.query = Object.assign({}, defQuery);
			this.query.type = key.key;
			this.onLoad();
		},
		refreshChange() {
			this.loading = true;
			this.onLoad();
		},
		rowSave(row, done, loading) {
			this.$request({
				url: modelUrl.add,
				data: row,
				method: 'POST'
			}).then(res => {
				if (res.rCode === 0) {
					done();
					this.onLoad();
					this.$message({
						type: 'success',
						message: '操作成功!'
					});
				} else {
					this.$message.error(res.msg);
				}
			});
		},
		rowUpdate(row, index, done, loading) {
			this.$request({
				url: modelUrl.update,
				data: row,
				method: 'POST'
			}).then(res => {
				if (res.rCode === 0) {
					done();
					this.onLoad();
					this.$message({
						type: 'success',
						message: '操作成功!'
					});
				} else {
					this.$message.error(res.msg);
				}
			});
		},
		rowDel(row) {
			this.$confirm('确定将选择数据删除?', {
				confirmButtonText: '确定',
				cancelButtonText: '取消',
				type: 'warning'
			}).then(() => {
				this.$request({
					url: modelUrl.remove,
					data: {
						ids: row.id
					},
					method: 'POST'
				}).then(res => {
					if (res.rCode === 0) {
						this.onLoad();
						this.$message({
							type: 'success',
							message: '操作成功!'
						});
					} else {
						this.$message.error(res.msg);
					}
				});
			});
		},
		handleDelete() {
			if (this.selectionList.length === 0) {
				this.$message.warning('请选择至少一条数据');
				return;
			}
			this.$confirm('确定将选择数据删除?', {
				confirmButtonText: '确定',
				cancelButtonText: '取消',
				type: 'warning'
			})
				.then(() => {
					return remove(this.ids);
				})
				.then(() => {
					this.onLoad();
					this.$message({
						type: 'success',
						message: '操作成功!'
					});
				});
		},
		beforeOpen(done, type) {
			if (['edit', 'view'].includes(type)) {
				this.$request({
					url: modelUrl.detail,
					params: {
						id: this.form.id
					},
					method: 'GET'
				}).then(res => {
					if (res.rCode === 0) {
						this.form = res.results.modelDetail;
					} else {
						this.$message.error(res.msg);
					}
				});
			}
			done();
		},
		doSearch() {
			this.onLoad();
		},
		searchReset() {
			this.query = Object.assign({}, defQuery);
			this.onLoad();
		},
		searchChange(params, done) {
			this.query = params;
			this.page.currentPage = 1;
			this.onLoad();
			done();
		},
		selectionChange(list) {
			this.selectionList = list;
		},
		selectionClear() {
			this.selectionList = [];
		},
		currentChange(currentPage) {
			this.page.currentPage = currentPage;
		},
		sizeChange(pageSize) {
			this.page.pageSize = pageSize;
		},
		onLoad() {
			this.loading = true;
			let params = {
				pageNum: this.page.currentPage,
				pageSize: this.page.pageSize
			};
			params = Object.assign(params, this.query);
			this.$request({
				url: modelUrl.page,
				params: params,
				method: 'GET'
			}).then(res => {
				if (res.rCode === 0) {
					const data = res.results;
					this.page.total = data.total;
					this.data = data.records;
					this.loading = false;
					this.selectionClear();
				}
			});
		},
		toShowFields(modelId, type) {
			this.$router.push({
				path: '/cmsmodelfield',
				query: { modelId: modelId, modelType: type }
			});
		}
	}
};
</script>

<style scoped="scoped">
/deep/ .el-menu-item {
	color: #ffffff;
	background-color: #000000;
	border-left: 5px #000000 solid;
}

/deep/ .el-menu-item.is-active {
	color: #ffffff;
	background-color: #24364f;
	border-left: 5px #0079fe solid;
}
</style>
