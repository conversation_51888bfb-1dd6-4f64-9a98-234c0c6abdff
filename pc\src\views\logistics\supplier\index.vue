<template>
	<div class="content">
		<es-data-table
			ref="table"
			:row-style="tableRowClassName"
			:full="true"
			:fit="true"
			:thead="thead"
			:toolbar="toolbar"
			:border="true"
			:page="pageOption"
			:url="dataTableUrl"
			:data="tableData"
			:numbers="true"
			:param="params"
			:option-data="optionData"
			close
			form
			@btnClick="btnClick"
			@sort-change="sortChange"
			@submit="hadeSubmit"
			@success="succ"
		></es-data-table>

		<!-- 新增 -->
		<es-dialog
			:title="formTitle"
			:visible.sync="showForm"
			width="40%"
			:drag="false"
			:close-on-click-modal="false"
			:destroy-on-close="true"
		>
			<es-form
				ref="form"
				:model="formData"
				:contents="formItemList"
				height="450px"
				:genre="2"
				collapse
				@change="inputChange"
				@submit="handleFormSubmit"
				@reset="showForm = false"
			/>
		</es-dialog>
		<!-- 删除 -->
		<es-dialog
			title="删除"
			:visible.sync="showDelete"
			width="20%"
			:drag="false"
			:close-on-click-modal="false"
			:middle="true"
			height="140px"
		>
			<div>确定要删除该条数据吗</div>
			<div class="btn-box">
				<div class="btn theme" @click="deleteRow">确定</div>
				<div class="btn" @click="showDelete = false">取消</div>
			</div>
		</es-dialog>
		<!-- 启用状态 -->
		<es-dialog
			:title="formData.status === '0' ? '禁用' : '启用'"
			:visible.sync="showDisable"
			width="20%"
			:drag="false"
			:close-on-click-modal="false"
			:middle="true"
			height="140px"
		>
			<div>确定要{{ formData.status === '0' ? '禁用' : '启用' }}该供应商吗？</div>
			<div class="btn-box">
				<div class="btn theme" @click="disableRow">确定</div>
				<div class="btn" @click="showDisable = false">取消</div>
			</div>
		</es-dialog>
	</div>
</template>

<script>
import interfaceUrl from '@/http/logistics/supplier.js';
import SnowflakeId from 'snowflake-id';
export default {
	data() {
		return {
			ownId: '',
			dataTableUrl: interfaceUrl.listJson,
			showForm: false,
			showDelete: false,
			showDisable: false,
			tableData: [],
			formData: {},
			formTitle: '编辑',
			editBtn: {
				type: 'submit',
				skin: 'lay-form-btns',
				contents: [
					{
						type: 'primary',
						text: '确定',
						event: 'confirm'
					},
					{
						type: 'reset',
						text: '取消',
						event: 'cancel'
					}
				]
			},
			cancelBtn: {
				type: 'submit',
				skin: 'lay-form-btns',
				contents: [
					{
						type: 'reset',
						text: '取消',
						event: 'cancel'
					}
				]
			},
			optionData: {
				status: [
					{
						value: 1,
						name: '启用'
					},
					{
						value: 0,
						name: '禁用'
					}
				]
			},
			toolbar: [
				{
					type: 'button',
					contents: [
						{
							text: '新增',
							code: 'add',
							type: 'primary'
						}
					]
				},
				{
					type: 'search',
					reset: true,
					contents: [
						{
							type: 'name', //精准查询
							//type: 'text',//精准查询
							name: 'name',
							placeholder: '请输入供应商名称'
						},
            {
              type: 'name', //精准查询
              //type: 'text',//精准查询
              name: 'code',
              placeholder: '请输入供应商编号'
            },
						{
							type: 'phone',
							name: 'phone',
							placeholder: '请输入供应商联系方式'
						}
					]
				}
			],

			thead: [
				{
					title: '名称',
					align: 'left',
					field: 'name',
					sortable: 'custom',
					showOverflowTooltip: true
				},
				{
					title: '供应商编号',
					align: 'left',
					field: 'code',
					sortable: 'custom',
					showOverflowTooltip: true
				},
				{
					title: '联系人',
					align: 'left',
					field: 'person',
					sortable: 'custom',
					showOverflowTooltip: true
				},
				{
					title: '联系电话',
					align: 'left',
					field: 'phone',
					sortable: 'custom',
					showOverflowTooltip: true
				},
				{
					title: '邮箱',
					align: 'left',
					field: 'email',
					sortable: 'custom',
					showOverflowTooltip: true
				},
				{
					title: '联系地址',
					align: 'left',
					field: 'address',
					sortable: 'custom',
					showOverflowTooltip: true
				},
				{
					title: '启用状态',
					align: 'center',
					field: 'status',
					sortable: 'custom',
					labelKey: 'name',
					valueKey: 'value'
				},
				// {
				//     title: '更新时间',
				//     width: '150px',
				//     align: 'center',
				//     field: 'updateTime',
				//     sortable: 'custom',
				// 	showOverflowTooltip: true,
				// },
				// {
				//     title: '更新人',
				//     width: '150px',
				//     align: 'center',
				//     field: 'updateUserName',
				//     sortable: 'custom',
				// 	showOverflowTooltip: true,
				// },
				{
					title: '操作',
					type: 'handle',
					width: 150,
					template: '',
					//showOverflowTooltip: false,
					events: [
						{
							code: 'edit',
							text: '编辑'
						},
						{
							code: 'view',
							text: '查看'
						},
						{
							code: 'delete',
							text: '删除'
						},
						{
							code: 'disable',
							text: '启用',
							rules: rows => {
								return rows.status === '0';
							}
						},
						{
							code: 'disable',
							text: '禁用',
							rules: rows => {
								return rows.status === '1';
							}
						}
					]
				}
			],
			pageOption: {
				layout: 'total, sizes, prev, pager, next, jumper',
				pageSize: 10,
				//// hideOnSinglePage: true,
				position: 'center',
				current: 1,
				pageNum: 1
			},
			params: {
				asc: 'false',
				orderBy: 'createTime'
			}
		};
	},
	computed: {
		formItemList() {
			return [
				{
					label: '供应商名称',
					name: 'name',
					placeholder: '请输入供应商名称',
					event: 'multipled',
					rules: {
						required: true,
						message: '请输入供应商名称',
						trigger: 'blur'
					},
					verify: 'required',
					maxlength: 85,
					col: 12
				},
				{
					label: '联系人',
					name: 'person',
					placeholder: '请输入联系人',
					event: 'multipled',
					rules: {
						required: true,
						message: '请输入联系人',
						trigger: 'blur'
					},
					maxlength: 20,
					verify: 'required',
					col: 12
				},
				{
					label: '联系电话',
					name: 'phone',
					placeholder: '请输入联系电话',
					event: 'multipled',
					rules: {
						required: true,
						message: '请输入联系电话',
						trigger: 'blur'
					},
					maxlength: 11,
					verify: 'required',
					col: 12
				},
				{
					label: '邮箱',
					name: 'email',
					placeholder: '请输入邮箱',
					event: 'multipled',
					rules: {
						required: false,
						message: '请输入邮箱',
						trigger: 'blur'
					},
					maxlength: 20,
					col: 12
				},
				{
					label: '地址',
					name: 'address',
					placeholder: '请输入地址',
					event: 'multipled',
					rules: {
						required: false,
						message: '请输入地址',
						trigger: 'blur'
					},
					maxlength: 120,
					col: 12
				},
				{
					name: 'remark',
					label: '备注',
					placeholder: '请输入备注',
					type: 'textarea',
					maxlength: 250,
					col: 12
				}
			];
		}
	},
	watch: {},
	created() {},
	mounted() {
		// this.$.ajax({
		// 	method: 'GET',
		// 	url: this.dataTableUrl
		// }).then(data => {
		// 	//console.info('打印出供应商数据：', data);
		// 	this.tableData = data.results.records;
		// 	for (let i = 0; i < this.tableData.length; i++) {
		// 		this.tableData[i].status = String(this.tableData[i].status);
		// 	}
		// 	//console.info('打印出供应商数据：', this.tableData);
		// });
	},
	methods: {
		//返回成功的数据
		succ(value) {
			//console.info('供应商返回的数据：', value);
		},
		/**
		 * 表格行高
		 */
		tableRowClassName({ row, rowIndex }) {
			let styleRes = {
				height: '54px !important'
			};
			return styleRes;
		},
		inputChange(key, value) {},
		hadeSubmit(data) {},
		/**
		 * 操作按钮点击事件
		 * @param {*} res
		 */
		btnClick(res) {
			let code = res.handle.code;
			switch (code) {
				case 'add':
					// 新增
					this.formTitle = '新增';
					this.editModule(this.formItemList);
					const snowflake = new SnowflakeId();
					let id = snowflake.generate();
					this.ownId = id;
					this.formData = {
						id: id,
						name: null,
						person: null,
						phone: null,
						email: null,
						address: null,
						remark: null,
						status: '0'
					};
					this.showForm = true;
					break;
				case 'edit':
					// 编辑
					this.formTitle = '编辑';
					this.ownId = res.row.id;
					this.editModule(this.formItemList);
					this.$request({
						url: interfaceUrl.info + '/' + res.row.id,
						method: 'GET'
					}).then(res => {
						if (res.rCode == 0) {
							this.formData = res.results;
							if (undefined !== this.formData.photoUrl && null !== this.formData.photoUrl) {
								this.formData.photoUrlTemp =
									'/main2/mecpfileManagement/previewAdjunct?adjunctId=' + this.formData.photoUrl;
							}
							this.formData.state = 1 == this.formData.state ? true : false;
							this.formData.sex = this.formData.sex + '';
							this.showForm = true;
						}
					});
					break;
				case 'view':
					this.formTitle = '查看';
					this.ownId = res.row.id;
					this.readModule(this.formItemList);
					this.$request({
						url: interfaceUrl.info + '/' + res.row.id,
						method: 'GET'
					}).then(res => {
						if (res.rCode == 0) {
							this.formData = res.results;
							if (undefined !== this.formData.photoUrl && null !== this.formData.photoUrl) {
								this.formData.photoUrlTemp =
									'/main2/mecpfileManagement/previewAdjunct?adjunctId=' + this.formData.photoUrl;
							}
							this.formData.state = 1 == this.formData.state ? true : false;
							this.formData.sex = this.formData.sex + '';
							this.showForm = true;
						}
					});
					break;
				case 'disable':
					this.disableRow(res.row);
					break;
				case 'delete':
					this.deleteId = res.row.id;
					this.showDelete = true;
					break;
				default:
					break;
			}
		},
		handleClose() {},
		handleFormItemChange() {},
		handleFormSubmit(data) {
			let formData = data;
			delete formData.extMap;
			let url = '';
			if (this.formTitle == '新增') {
				url = interfaceUrl.save;
				let Base64 = require('js-base64').Base64;
				formData['password'] = Base64.encode(formData.pwd);
			} else {
				url = interfaceUrl.update;
			}
			// formData.state = formData.state ? 1 : 0;
			// if (undefined != formData.photoUrlTemp && undefined != formData.photoUrlTemp.response) {
			//     formData.photoUrl = formData.photoUrlTemp.response.adjunctId;
			// }
			this.$request({
				url: url,
				data: formData,
				method: 'POST'
			}).then(res => {
				if (res.rCode == 0) {
					this.$message.success('操作成功');
					this.showForm = false;
					this.formData = {};
					this.$refs.table.reload();
				} else {
					this.formData.state = 1 == this.formData.state ? true : false;
					this.$message.error(res.msg);
				}
			});
		},
		deleteRow() {
			this.$request({
				url: interfaceUrl.deleteBatchIds,
				data: { ids: this.deleteId },
				method: 'POST'
			}).then(res => {
				if (res.rCode == 0) {
					this.$refs.table.reload();
					this.$message.success('删除成功');
				} else {
					this.$message.error(res.msg);
				}
				this.showDelete = false;
			});
		},
		disableRow(row) {
			//console.log('打印禁用data:', row);
			let formData = row;
			if (row.status == '0') {
				formData.status = '1';
			} else {
				formData.status = '0';
			}
			//formData.status = Number(formData.status);
			this.$request({
				url: interfaceUrl.update,
				data: formData,
				method: 'POST'
			}).then(res => {
				if (res.rCode == 0) {
					this.$message.success('操作成功');
					this.showDisable = false;
					this.formData = {};
					this.$refs.table.reload();
				} else {
					this.formData.state = 1 == this.formData.state ? true : false;
					this.$message.error(res.msg);
				}
			});
		},
		/**
		 * 排序变化事件
		 * @param {*} column
		 * @param {*} prop
		 * @param {*} order
		 */
		sortChange(column, prop, order) {
			if (column.order == 'ascending') {
				//升序
				this.params = {
					asc: 'true',
					orderBy: column.prop
				};
			} else if (column.order == 'descending') {
				//降序
				this.params = {
					asc: 'false',
					orderBy: column.prop
				};
			} else {
				//不排序
				this.params = {
					asc: 'false',
					orderBy: 'createTime'
				};
			}
			this.$refs.table.reload();
		},
		/**
		 * 编辑模式
		 */
		editModule(list) {
			for (var i in list) {
				var item = list[i];
				item.readonly = false;
			}
			list.push(this.editBtn);
		},
		/**
		 * 只读模式
		 */
		readModule(list, hideField) {
			for (var i in list) {
				var item = list[i];
				item.readonly = true;
			}
			list.push(this.cancelBtn);
		}
	}
};
</script>
<style scoped lang="scss">
@import '../../../assets/style/style.scss';

.content {
	display: flex;
	width: 100%;
	height: 100%;

	::v-deep .es-data-table {
		flex: 1;
		display: flex;
		flex-direction: column;

		.es-data-table-content {
			flex: 1;
			height: 0;
			display: flex;
			flex-direction: column;

			.el-table {
				flex: 1;
				// height: 100% !important;
			}

			.es-thead-border {
				.el-table__header {
					th {
						border-right: 1px solid #e1e1e1;
					}
				}
			}
		}
	}

	::v-deep .el-form-item__label {
		background: none;
		border: 0px solid #c2c2c2;
	}
}

.el-dialog__body {
	.btn-box {
		margin-top: 20px;
		display: flex;
		justify-content: flex-end;

		.btn {
			padding: 5px 10px;
			color: #666;
			border: 1px solid #eee;
			cursor: pointer;
			margin-right: 5px;
			border-radius: 4px;
			// &.theme {
			// 	background: $--color-primary;
			// 	color: #fff;
			// 	border-color: $--color-primary;
			// }
		}
	}
}
</style>
