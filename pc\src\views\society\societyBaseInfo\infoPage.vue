<template>
	<div class="sketch_content">
		<el-collapse v-model="activeName">
			<el-collapse-item title="社团信息" :name="1">
				<el-form ref="form" :model="formData" class="resumeTable" :rules="rules">
					<el-row>
						<el-col :span="8">
							<el-form-item label="社团名称" label-width="160px" label-position="left" prop="name">
								<el-input
									v-model="formData.name"
									placeholder="输入社团名称"
									:disabled="infoDisabled"
								></el-input>
							</el-form-item>
						</el-col>
						<el-col :span="8">
							<el-form-item
								label="社团类型"
								label-width="160px"
								label-position="left"
								prop="typeId"
							>
								<el-select
									ref="type"
									v-model="formData.typeId"
									value-key="value"
									:disabled="infoDisabled"
									clearable
									:remote-method="getSocType"
									filterable
									remote
								>
									<el-option
										v-for="item in socTypeList"
										:key="item.value"
										:label="item.label"
										:value="item.value"
									></el-option>
								</el-select>
							</el-form-item>
						</el-col>
					</el-row>
					<el-row>
						<el-col v-if="infoPageMode === '新增'" :span="8">
							<el-form-item
								label="社长所属学院"
								label-width="160px"
								label-position="left"
								prop="studentCollege"
								:hidden="pageMode !== '新增'"
							>
								<el-select
									ref="type"
									v-model="formData.studentCollege"
									value-key="value"
									:disabled="infoDisabled"
									placeholder="请选择所属学院"
									clearable
									:remote-method="getCollege"
									remote
									@change="studentCollegeChange"
								>
									<el-option
										v-for="item in collegeList"
										:key="item.value"
										:label="item.label"
										:value="item.value"
									></el-option>
								</el-select>
							</el-form-item>
						</el-col>
						<el-col :span="8">
							<el-form-item label="社长" label-width="160px" label-position="left" prop="principal">
								<el-select
									ref="type"
									v-model="formData.principal"
									value-key="value"
									placeholder="输入姓名可查询"
									:remote-method="getStudent"
									filterable
									clearable
									remote
									:disabled="
										infoDisabled &&
										(formData.studentCollege === undefined || formData.studentCollege === '')
									"
									@change="selectStudents"
									@visible-change="selectBlur"
								>
									<el-option
										v-for="item in studentSelectList"
										:key="item.value"
										:disabled="selectStudentsList.includes(item.value)"
										:label="item.label"
										:value="item.value"
									></el-option>
								</el-select>
							</el-form-item>
						</el-col>
					</el-row>
					<el-row>
						<el-col :span="8">
							<el-form-item
								label="副社长"
								label-width="160px"
								label-position="left"
								prop="principalDeputy1"
							>
								<el-select
									ref="type"
									v-model="formData.principalDeputy1"
									placeholder="输入姓名可查询"
									:remote-method="getStudent"
									filterable
									clearable
									remote
									@change="selectStudents"
									@visible-change="selectBlur"
								>
									<el-option
										v-for="item in studentSelectList"
										:key="item.value"
										:disabled="selectStudentsList.includes(item.value)"
										:label="item.label"
										:value="item.value"
									></el-option>
								</el-select>
							</el-form-item>
						</el-col>
						<el-col :span="8">
							<el-form-item
								label="副社长"
								label-width="160px"
								label-position="left"
								prop="principalDeputy2"
							>
								<el-select
									ref="type"
									v-model="formData.principalDeputy2"
									placeholder="输入姓名可查询"
									:remote-method="getStudent"
									filterable
									clearable
									remote
									@change="selectStudents"
									@visible-change="selectBlur"
								>
									<el-option
										v-for="item in studentSelectList"
										:key="item.value"
										:disabled="selectStudentsList.includes(item.value)"
										:label="item.label"
										:value="item.value"
									></el-option>
								</el-select>
							</el-form-item>
						</el-col>
						<el-col :span="8">
							<el-form-item
								label="副社长"
								label-width="160px"
								label-position="left"
								prop="principalDeputy3"
							>
								<el-select
									ref="type"
									v-model="formData.principalDeputy3"
									placeholder="输入姓名可查询"
									:remote-method="getStudent"
									filterable
									clearable
									remote
									@change="selectStudents"
									@visible-change="selectBlur"
								>
									<el-option
										v-for="item in studentSelectList"
										:key="item.value"
										:disabled="selectStudentsList.includes(item.value)"
										:label="item.label"
										:value="item.value"
									></el-option>
								</el-select>
							</el-form-item>
						</el-col>
					</el-row>
					<el-row>
						<el-col :span="8">
							<el-form-item
								label="指导老师所属学院"
								label-width="160px"
								label-position="left"
								prop="college"
							>
								<el-select
									ref="type"
									v-model="formData.college"
									value-key="value"
									:disabled="infoDisabled"
									placeholder="请选择所属学院"
									clearable
									:remote-method="getOrg"
									remote
									@change="orgChange"
								>
									<el-option
										v-for="item in orgList"
										:key="item.value"
										:label="item.label"
										:value="item.value"
									></el-option>
								</el-select>
							</el-form-item>
						</el-col>
						<el-col :span="8">
							<el-form-item
								label="指导老师"
								label-width="160px"
								label-position="left"
								prop="teacherId"
							>
								<el-select
									ref="type"
									v-model="formData.teacherId"
									value-key="value"
									:disabled="infoDisabled"
									placeholder="请选择教师"
									clearable
									:remote-method="getTeacher"
									filterable
									multiple
									multiple-limit="10"
									remote
								>
									<el-option
										v-for="item in teacherList"
										:key="item.value"
										:label="item.label"
										:value="item.value"
									></el-option>
								</el-select>
							</el-form-item>
						</el-col>
						<!-- <el-col :span="7">
							<el-form-item label="所属专业" label-width="120px" label-position="left" prop="major">
								<el-select
									ref="type"
									v-model="formData.major"
									value-key="value"
									placeholder="请选择所属专业"
									clearable
									:remote-method="getMajor"
									remote
									:disabled="
										infoDisabled || formData.college === undefined || formData.college === ''
									"
								>
									<el-option
										v-for="item in majorList"
										:key="item.value"
										:label="item.label"
										:value="item.value"
									></el-option>
								</el-select>
							</el-form-item>
						</el-col> -->
					</el-row>

					<el-row>
						<el-col :span="8">
							<el-form-item
								label="人数上限"
								label-width="160px"
								label-position="left"
								prop="memberMax"
							>
								<el-input-number v-model="formData.memberMax"></el-input-number>
							</el-form-item>
						</el-col>
						<el-col :span="8">
							<el-form-item
								label="社团状态"
								label-width="160px"
								label-position="left"
								prop="statusVO"
							>
								<el-input v-model="formData.statusVO" :disabled="true"></el-input>
							</el-form-item>
						</el-col>
						<el-col :span="16">
							<el-form-item label="社团老师申请表" label-width="160px" prop="fileList">
								<es-upload
									v-bind="fileAttrs"
									v-model="formData.fileList"
									:disabled="infoDisabled"
								></es-upload>
							</el-form-item>
						</el-col>
					</el-row>
					<el-row>
						<el-col :span="16">
							<el-form-item
								label="社团封面"
								label-width="160px"
								label-position="left"
								prop="coverUrl"
							>
								（数量一张）
								<es-upload
									v-bind="coverAttrs"
									ref="upload"
									v-model="formData.coverUrl"
									:class="{ isShowAdd: formData.coverUrl }"
									:disabled="infoDisabled"
									select-type="icon-plus"
									:on-success="uploadSuccess"
									list-type="picture-card"
									:before-upload="beforeUpload"
								></es-upload>
							</el-form-item>
						</el-col>
						<el-col :span="8">
							<el-form-item
								label="申请时间"
								label-width="160px"
								label-position="left"
								prop="createTime"
							>
								<el-date-picker
									v-model="formData.createTime"
									type="datetime"
									:disabled="true"
								></el-date-picker>
							</el-form-item>
						</el-col>
					</el-row>
					<el-row>
						<el-col :span="22">
							<el-form-item
								label="社团简介"
								label-width="90px"
								label-position="left"
								prop="introduction"
							>
								<el-input v-model="formData.introduction" type="textarea"></el-input>
							</el-form-item>
						</el-col>
					</el-row>
					<el-row>
						<el-col :span="3" style="float: right">
							<el-button type="reset" @click="infoPageClose(false)">取消</el-button>
						</el-col>
						<el-col :span="3" style="float: right">
							<el-button v-show="!infoDisabled" type="primary" @click="handleFormSubmit">
								保存
							</el-button>
						</el-col>
					</el-row>
				</el-form>
			</el-collapse-item>
			<el-collapse-item v-show="showMemberList" title="社团成员" :name="2">
				<es-data-table
					v-show="showMemberList"
					ref="table"
					:param="defaultParam"
					:thead="thead"
					:toolbar="toolbar"
					:page="page"
					:url="tableUrl"
					height="275px"
					@btnClick="btnClick"
				></es-data-table>
			</el-collapse-item>
		</el-collapse>
	</div>
</template>
<script>
import api from '@/http/society/societyBaseInfo/api';
import apiT from '@/http/society/societyBaseInfoAudit/api';
import SnowflakeId from 'snowflake-id';

export default {
	name: 'InfoPage',
	props: {
		baseData: {
			type: Object
		},
		infoPageMode: {
			type: String
		}
	},
	data() {
		return {
			page: {},
			tableUrl: api.societyBaseInfoStudentMapping,
			infoDisabled: true,
			showMemberList: true,
			formData: {},
			pageMode: 'allOn',
			activeName: [1, 2],
			socTypeList: [],
			teacherList: [],
			selectStudentsList: [],
			studentSelectList: [],
			collegeList: [],
			orgList: [],
			majorList: [],
			thead: [
				{
					title: '姓名',
					align: 'left',
					field: 'memberName',
					sortable: 'custom',
					showOverflowTooltip: true,
					render: (h, param) => {
						return h(
							'el-tag',
							{ props: { type: param.row.memberDuties === 0 ? 'danger' : '' } },
							param.row.memberName.toString() +
								(param.row.memberDuties === 0 ? '(社长)' : '').toString()
						);
					}
				},
				{
					title: '学院',
					width: '220px',
					align: 'center',
					field: 'collegeName',
					sortable: 'custom',
					showOverflowTooltip: true
				},
				{
					title: '班级',
					width: '150px',
					align: 'center',
					field: 'className',
					sortable: 'custom',
					showOverflowTooltip: true
				},
				{
					title: '学号',
					width: '150px',
					align: 'center',
					field: 'memberNo',
					sortable: 'custom',
					showOverflowTooltip: true
				},
				{
					title: '联系方式',
					width: '180px',
					align: 'center',
					field: 'telephone',
					sortable: 'custom',
					showOverflowTooltip: true
				},
				{
					title: '操作',
					type: 'handle',
					width: 90,
					template: '',
					events: [
						{
							code: 'row',
							text: '设为社长',
							rules: rows => {
								return rows.memberDuties !== 0 && this.pageMode === '编辑';
							}
						}
					]
				}
			],
			toolbar: [
				{
					type: 'search',
					reset: true,
					contents: [
						{
							type: 'text',
							name: 'keyword',
							placeholder: '姓名、班级、学院',
							clearable: true
						}
					]
				}
			],
			rules: {
				name: [{ required: true, message: '请输入社团名称', trigger: 'blur' }],
				college: [{ required: true, message: '请选择所属学院', trigger: 'blur' }],
				studentCollege: [{ required: true, message: '请选择社长所属学院', trigger: 'blur' }],
				principal: [{ required: true, message: '请选择社长', trigger: 'blur' }],
				memberMax: [{ required: true, message: '请输入人数上限', trigger: 'blur' }],
				coverUrl: [{ required: true, message: '请上传社团封面', trigger: 'change' }],
				fileList: [{ required: true, message: '请上传社团老师申请表', trigger: 'change' }],
				introduction: [{ required: true, message: '请输入社团简介', trigger: 'blur' }]
			},
			inputSeachFlag: false //社长下拉框是否有搜索条件
		};
	},
	computed: {
		defaultParam() {
			return {
				societyId: this.formData.id
			};
		},
		fileAttrs() {
			return {
				code: 'society_base_info_teacher_form',
				// code: 'society_base_info_cover',
				ownId: this.formData.id,
				preview: true,
				download: true
				// operate: true,
			};
		},
		coverAttrs() {
			return {
				code: 'society_base_info_cover',
				ownId: this.formData.id,
				preview: true,
				limit: 1,
				download: true,
				operate: true
			};
		}
	},
	watch: {},
	created() {
		this.formData = {
			...this.baseData
		};
		this.pageMode = this.infoPageMode;
		const snowflake = new SnowflakeId();
		switch (this.pageMode) {
			case '新增':
				this.formData.id = snowflake.generate();
				this.showMemberList = false;
				this.infoDisabled = false;
				break;
			case '查看':
				this.showMemberList = true;
				this.infoDisabled = true;
				break;
			case '编辑':
				console.log(this.formData.principal, '-------');
				this.showMemberList = true;
				this.infoDisabled = false;
				this.selectStudents();
				break;
		}
		// if(this.pageMode !== '查看') {
		this.getOrg();
		this.getCollege();
		this.getSocType();
		this.getStudent();
		let collegeCode = 'college' in this.formData ? this.formData.college.value : null;
		console.log(this.formData, 'collegeCode');
		if (collegeCode != null && collegeCode !== '') {
			this.getMajor(collegeCode);
			this.getTeacher(collegeCode);
		}
		// }
	},
	methods: {
		btnClick(res) {
			let mappingId = res.row.id;

			this.$request({
				url: api.societyBaseInfoSetMain,
				data: { mappingId: mappingId },
				method: 'POST'
			}).then(res => {
				if (res.rCode === 0) {
					this.$message.success('操作成功');
					this.infoPageClose(true);
				} else {
					this.$message.error(res.msg);
				}
			});
		},
		handleFormSubmit() {
			//校验
			this.$refs.form.validate(valid => {
				if (valid) {
					let apiUrl =
						this.pageMode === '编辑' ? api.societyBaseInfoUpdate : api.societyBaseInfoSave;
					let saveData = { ...this.formData };
					//处理数据
					if (saveData.id === undefined || saveData.id === '') {
						const snowflake = new SnowflakeId();
						saveData.id = snowflake.generate();
					}
					if (typeof saveData.college == 'object') saveData.collegeId = saveData.college.value;
					else saveData.collegeId = saveData.college;
					if (typeof saveData.major == 'object') saveData.major = saveData.major.value;
					if (typeof saveData.typeId == 'object') saveData.typeId = saveData.typeId.value;
					if (typeof saveData.principal == 'object') saveData.principal = saveData.principal.value;
					else saveData.principalId = saveData.principal;
					saveData.teacherId = saveData.teacherId.join(',');

					this.$delete(saveData, 'typeName');
					this.$delete(saveData, 'teacherName');
					this.$delete(saveData, 'college');
					this.$delete(saveData, 'coverUrl');
					this.$delete(saveData, 'fileList');
					this.$request({
						url: apiUrl,
						data: saveData,
						method: 'POST'
					}).then(res => {
						if (res.rCode === 0) {
							this.$message.success('操作成功');
							this.infoPageClose(true);
						} else {
							this.$message.error(res.msg);
						}
					});
				}
			});
		},
		infoPageClose(reload) {
			this.pageMode = 'allOn';
			this.infoDisabled = false;
			this.$emit('activelyClose', reload);
		},
		getSocType(res) {
			if (res !== '') {
				this.$request({
					url: api.societyTypeSelectList,
					params: { typeName: res },
					method: 'GET'
				}).then(result => {
					if (result.success) {
						this.socTypeList = result.results.records;
					} else {
						this.$message.error('操作失败');
					}
				});
			}
		},
		getStudent(res) {
			this.$request({
				url: api.studentSelectList,
				params: { yxdm: this.formData.studentCollege, name: res },
				method: 'GET'
			}).then(result => {
				if (result.success) {
					this.studentSelectList = result.results;
				} else {
					this.$message.error('操作失败');
				}
			});
			if (this.pageMode === '新增') {
				this.inputSeachFlag = res ? true : false;
				// this.$request({
				// 	url: api.studentSelectList,
				// 	params: { yxdm: this.formData.studentCollege, name: res },
				// 	method: 'GET'
				// }).then(result => {
				// 	if (result.success) {
				// 		this.studentSelectList = result.results;
				// 	} else {
				// 		this.$message.error('操作失败');
				// 	}
				// });
			} else {
				let societyId = '';
				societyId = this.formData.id;

				// this.$request({
				// 	url: api.societyStudentMappingMemberSelectList,
				// 	params: { societyId: societyId, memberName: res },
				// 	method: 'GET'
				// }).then(result => {
				// 	if (result.success) {
				// 		this.studentSelectList = result.results;
				// 	} else {
				// 		this.$message.error(result.msg);
				// 	}
				// });
			}
		},
		// getTeacher(res) {
		// 	this.$request({
		// 		url: api.teacherSelectList,
		// 		params: { keyword: res, szdw: this.formData.college.value },
		// 		method: 'GET'
		// 	}).then(result => {
		// 		if (result.success) {
		// 			this.teacherList = result.results;
		// 		} else {
		// 			this.$message.error('操作失败');
		// 		}
		// 	});
		// },
		getTeacher(res) {
			this.$request({
				url: apiT.teacherSelectList,
				params: { xydm: res },
				method: 'GET'
			}).then(result => {
				if (result.success) {
					this.teacherList = result.results;
				} else {
					this.$message.error('操作失败');
				}
			});
		},
		getCollege() {
			this.$request({
				url: api.collegeSelectList,
				method: 'POST'
			}).then(result => {
				if (result.success) {
					this.collegeList = result.results;
				} else {
					this.$message.error('操作失败');
				}
			});
		},
		getOrg() {
			this.$request({
				url: api.orgSelectList,
				method: 'GET'
			}).then(result => {
				if (result.success) {
					this.orgList = result.results;
				} else {
					this.$message.error('操作失败');
				}
			});
		},
		getMajor(res) {
			this.$request({
				url: api.majorSelectList,
				params: { collegeCode: res },
				method: 'POST'
			}).then(result => {
				if (result.success) {
					this.majorList = result.results;
				} else {
					this.$message.error('操作失败');
				}
			});
		},
		studentCollegeChange(res) {
			this.getStudent();
			this.$delete(this.formData, 'teacherId');
		},
		orgChange(res) {
			this.getMajor(res);
			this.getTeacher(res);
			// this.getTeacher();
			this.$delete(this.formData, 'teacherId');
		},
		beforeUpload() {
			if (this.formData.id === undefined || this.formData.id === '') {
				const snowflake = new SnowflakeId();
				this.formData.id = snowflake.generate();
				this.$set(this.$refs.upload.datas, 'ownId', this.formData.id);
			}
		},
		uploadSuccess(response, file, fileList) {
			this.$set(this.formData, 'cover', file.response.adjunctId);
		},
		selectStudents(student) {
			this.selectStudentsList = [];
			// this.selectStudentsList.push(this.formData.principal);
			if (typeof this.formData.principal == 'object')
				this.selectStudentsList.push(this.formData.principal.value);
			this.selectStudentsList.push(this.formData.principalDeputy1);
			this.selectStudentsList.push(this.formData.principalDeputy2);
			this.selectStudentsList.push(this.formData.principalDeputy3);
		},
		selectBlur(Event) {
			if (!Event && this.inputSeachFlag) {
				// 当下拉弹窗收回去的时候判断当前是否用过条件搜索，如果有就重新加载一下所有的姓名，以防止其他下拉框出现展示搜索结果问题
				this.getStudent();
			}
		}
	}
};
</script>
<style lang="scss" scoped>
.sketch_content {
	overflow: auto;
	height: 100%;
	border-top: 1px solid #eff1f4;
	border-bottom: 1px solid #eff1f4;
	padding: 0 30px 11px 27px;
}
.resumeTable td {
	height: 50px;
	font-weight: bold;
}

::v-deep .el-collapse-item__header.is-active {
	border-bottom: 1px solid #ebeef5;
	font-size: 18px;
	font-weight: bold;
}
::v-deep .el-collapse-item__header::before {
	content: '';
	width: 4px;
	height: 18px;
	background-color: #0076e9;
	margin-right: 2px;
}
.isShowAdd {
	::v-deep .el-upload--handle {
		display: none;
	}
}
</style>
