<template>
	<div class="guidance">
		<el-tabs v-model="activeName" @tab-click="handleClick">
			<el-tab-pane
				:label="item.text"
				:name="index.toString()"
				v-for="(item, index) in list"
				:key="index"
			></el-tab-pane>
		</el-tabs>
		<div class="content-box">
			<div
				:class="(index + 1) % 6 ? 'item ' : 'item noBorderRight'"
				v-for="(item, index) in itemContent"
				:key="index"
				@click="toOpenWindow(item)"
			>
				<div class="icon">
					<div class="picBox">
						<img :src="handelPicUrl(item.icons)" alt="" height="100%" />
					</div>
				</div>
				<div class="name">{{ item.text }}</div>
			</div>
		</div>
	</div>
</template>

<script>
import { picUrl } from '@/config/index';
export default {
	data() {
		return {
			myUsullyApp: [],
			activeName: '0',
			list: [],
			itemContent: []
		};
	},
	methods: {
		handleClick(item) {
			console.log(item);
			this.itemContent = this.list[item.name].children;
			this.$nextTick(() => {
				let dom = document.getElementsByClassName('guidance');
				let height = dom[0].clientHeight;
				window.parent.setIframeHeight(height);
				let name = 'frameStart';
				window.parent.postMessage(name, '*');
			});
		},
		toOpenWindow(item) {
			// window.open(`${picUrl}${item.url}`);
			let url = `${picUrl}${item.url}`;
			window.parent.postMessage(url, '*');
		},
		//处理图片路径
		handelPicUrl(url) {
			return `${picUrl}/static/andyui2.0/css/picIcon/img/${url}`;
		},
		//获取接口数据
		getListData() {
			this.loading = true;
			this.$request({
				url: '/sys/v1/mecpSys/getSysMenu.dhtml?userId=u6231eda47ddc4714904e686e33111615',
				method: 'GET'
			})
				.then(res => {
					this.list = res.results;
					if (res.results.length) {
						this.itemContent = res.results[0].children;
					}
					this.$nextTick(() => {
						let dom = document.getElementsByClassName('guidance');
						let height = dom[0].clientHeight + 20;
						window.parent.setIframeHeight(height);
						let name = 'frameStart';
						window.parent.postMessage(name, '*');

						console.log(height);
						console.log(dom);
					});
				})
				.catch(error => {});
		}
	},
	mounted() {
		this.getListData();
	}
};
</script>

<style lang="scss" scoped>
* {
	margin: 0;
	padding: 0;
}
.guidance {
	width: 100%;
	background: #ffffff;
	.content-box {
		display: flex;
		flex-wrap: wrap;
		background: #fff;
		width: 100%;
		.item {
			cursor: pointer;
			margin: 0 20px 20px 0;
			width: 183px;
			height: 128px;
			border: 1px solid #e8eaf0;
			border-radius: 10px;
			.icon {
				width: 100%;
				height: 80px;
				display: flex;
				justify-content: center;
				align-items: center;
				.picBox {
					// border: 1px solid red;
					border-radius: 14px;
					width: 50px;
					height: 50px;
				}
			}
			.name {
				height: 48px;
				line-height: 28px;
				font-size: 16px;
				font-weight: bold;
				color: #333333;
				text-align: center;
				white-space: nowrap;
				overflow: hidden;
				text-overflow: ellipsis;
			}
		}
	}

	.noBorderRight {
		margin-right: 0 !important;
	}
}
::v-deep .el-tabs--top {
	background: #ffffff;
}
</style>
