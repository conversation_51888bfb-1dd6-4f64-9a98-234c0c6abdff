<template>
	<div class="content">
		<es-data-table
			ref="table"
			:row-style="tableRowClassName"
			:full="true"
			:fit="true"
			:thead="thead"
			:toolbar="toolbar"
			:border="true"
			:page="pageOption"
			:url="dataTableUrl"
			:numbers="true"
			:param="params"
			:option-data="optionData"
			close
			form
			@btnClick="btnClick"
			@sort-change="sortChange"
			@submit="hadeSubmit"
			@edit="changeTable"
		></es-data-table>
		<es-dialog
			:title="formTitle"
			:visible.sync="showForm"
			width="60%"
			:close-on-click-modal="false"
			:destroy-on-close="true"
		>
			<es-form
				ref="form"
				:model="formData"
				:contents="formItemList"
				height="500px"
				:genre="2"
				collapse
				@change="inputChange"
				@submit="handleFormSubmit"
				@reset="showForm = false"
			/>
		</es-dialog>
		<es-dialog
			title="分配角色"
			:visible.sync="showRoleForm"
			width="60%"
			:close-on-click-modal="false"
			:destroy-on-close="true"
		>
			<es-form
				ref="form"
				:model="roleFormData"
				:contents="roleFormItemList"
				height="500px"
				:genre="2"
				collapse
				@change="roleInputChange"
				@submit="handleRoleFormSubmit"
				@reset="showRoleForm = false"
			/>
		</es-dialog>
		<es-dialog
			title="删除"
			:visible.sync="showDelete"
			width="20%"
			:close-on-click-modal="false"
			:middle="true"
			height="140px"
		>
			<div>确定要删除该条数据吗</div>
			<div class="btn-box">
				<div class="btn theme" @click="deleteRow">确定</div>
				<div class="btn" @click="showDelete = false">取消</div>
			</div>
		</es-dialog>
	</div>
</template>

<script>
import interfaceUrl from '@/http/platform/user.js';
import SnowflakeId from 'snowflake-id';
import { host } from '../../../../config/config';
export default {
	data() {
		return {
			ownId: '',
			deleteId: '',
			dataTableUrl: interfaceUrl.listJson,
			showForm: false,
			showRoleForm: false,
			enpList: [],
			roleList: [],
			showDelete: false,
			formData: {},
			roleFormData: {
				userId: '',
				username: '',
				phone: '',
				selectedRoleList: []
			},
			formTitle: '编辑',
			optionData: {
				state: [
					{
						value: 1,
						name: '启用'
					},
					{
						value: 0,
						name: '禁用'
					}
				]
			},
			editBtn: {
				type: 'submit',
				skin: 'lay-form-btns',
				contents: [
					{
						type: 'primary',
						text: '确定',
						event: 'confirm'
					},
					{
						type: 'reset',
						text: '取消',
						event: 'cancel'
					}
				]
			},
			cancelBtn: {
				type: 'submit',
				skin: 'lay-form-btns',
				contents: [
					{
						type: 'reset',
						text: '取消',
						event: 'cancel'
					}
				]
			},
			toolbar: [
				// {
				// 	type: 'button',
				// 	contents: [
				// 		{
				// 			text: '新增',
				// 			code: 'add',
				// 			type: 'primary'
				// 		}
				// 	]
				// },
				{
					type: 'search',
					contents: [
						{
							type: 'text',
							name: 'keyword',
							placeholder: '关键字查询'
						}
					]
				}
			],
			thead: [
				{
					title: '昵称',
					align: 'left',
					field: 'nickname',
					sortable: 'custom',
					showOverflowTooltip: true
				},
				{
					title: '用户名',
					align: 'left',
					field: 'username',
					sortable: 'custom',
					showOverflowTooltip: true
				},
				{
					title: '邮箱',
					align: 'left',
					field: 'email',
					sortable: 'custom',
					showOverflowTooltip: true
				},
				{
					title: '手机号',
					align: 'center',
					field: 'phone',
					sortable: 'custom',
					showOverflowTooltip: true
				},
				{
					title: '状态',
					field: 'state',
					align: 'center',
					type: 'switch'
				},
				{
					title: '更新时间',
					align: 'center',
					field: 'updateTime',
					sortable: 'custom',
					showOverflowTooltip: true
				},
				{
					title: '更新人',
					align: 'center',
					field: 'updateUserName',
					sortable: 'custom',
					showOverflowTooltip: true
				},
				{
					title: '操作',
					type: 'handle',
					width: 180,
					template: '',
					events: [
						{
							code: 'view',
							text: '查看'
						},
						{
							code: 'edit',
							text: '编辑'
						},
						{
							code: 'initPwd',
							text: '初始化密码'
						}
						// {
						// 	code: 'setRole',
						// 	text: '分配角色'
						// },
						// {
						// 	code: 'delete',
						// 	text: '删除'
						// }
					]
				}
			],
			pageOption: {
				layout: 'total, prev, pager, next, jumper',
				pageSize: 20,
				// hideOnSinglePage: true,
				position: 'right',
				current: 1,
				pageNum: 1
			},
			params: {
				asc: 'false',
				orderBy: 'createTime'
			}
		};
	},
	computed: {
		formItemList() {
			return [
				{
					label: '用户名',
					name: 'username',
					placeholder: '请输入用户名',
					event: 'multipled',
					rules: {
						required: true,
						message: '请输入用户名',
						trigger: 'blur'
					},
					verify: 'required',
					col: 5
				},
				{
					label: '昵称',
					name: 'nickname',
					placeholder: '请输入昵称',
					event: 'multipled',
					col: 5
				},
				{
					label: '手机号',
					name: 'phone',
					placeholder: '请输入手机号',
					event: 'multipled',
					rules: {
						required: true,
						message: '请输入手机号',
						trigger: 'blur'
					},
					verify: 'required',
					col: 5
				},
				{
					label: '邮箱',
					name: 'email',
					placeholder: '请输入邮箱',
					event: 'multipled',
					rules: {
						required: true,
						message: '请输入邮箱',
						trigger: 'blur'
					},
					verify: 'required',
					col: 5
				},
				{
					label: '登录密码',
					name: 'pwd',
					hide: false,
					placeholder: '请输入登录密码',
					event: 'multipled',
					rules: {
						required: true,
						message: '请输入登录密码',
						trigger: 'blur'
					},
					verify: 'required',
					col: 5
				},
				{
					label: '年龄',
					name: 'age',
					placeholder: '请输入年龄',
					type: 'number',
					controls: false,
					rules: {
						required: true,
						message: '请输入年龄',
						trigger: 'blur'
					},
					verify: 'required',
					col: 5
				},
				{
					label: '性别',
					name: 'sex',
					placeholder: '请选择性别',
					type: 'radio',
					rules: {
						required: true,
						message: '请选择性别',
						trigger: 'blur'
					},
					verify: 'required',
					col: 5,
					sysCode: 'plat_user_sex',
					'label-key': 'shortName',
					'value-key': 'cciValue'
				},
				{
					type: 'switch',
					label: '状态',
					verify: 'required',
					name: 'state',
					placeholder: '',
					rules: {
						required: true,
						message: '请选择状态',
						trigger: 'blur'
					},
					verify: 'required',
					col: 5,
					data: [
						{
							value: true,
							name: '启用'
						},
						{
							value: false,
							name: '禁用'
						}
					]
				},
				{
					label: '头像',
					name: 'photoUrlTemp',
					type: 'attachment',
					code: 'platform_user_head_sculpture',
					ownId: this.ownId, // 业务id
					// size: 100,
					portrait: true,
					param: {
						isShowPath: true
					},
					col: 10
				},
				{
					name: 'remark',
					label: '备注',
					placeholder: '请输入备注',
					type: 'textarea',
					col: 10
				}
			];
		},
		roleFormItemList() {
			return [
				{
					label: '用户名',
					name: 'username',
					readonly: true,
					col: 6
				},
				{
					label: '手机号',
					name: 'phone',
					col: 6
				},
				{
					type: 'select',
					label: '企业',
					placeholder: '请选择分配企业',
					name: 'enterpriseId',
					event: 'multipled',
					rules: {
						required: true,
						message: '请选择分配企业',
						trigger: 'blur'
					},
					verify: 'required',
					data: this.enpList,
					col: 12
				},
				{
					type: 'select',
					label: '角色',
					placeholder: '请选择角色',
					name: 'selectedRoleList',
					event: 'multipled',
					multiple: true,
					data: this.roleList,
					col: 12
				}
			];
		}
	},
	watch: {},
	created() {},
	mounted() {},
	methods: {
		/**
		 * 表格行高
		 */
		tableRowClassName({ row, rowIndex }) {
			let styleRes = {
				height: '54px !important'
			};
			return styleRes;
		},
		inputChange(key, value) {
			if (key == 'phone') {
			}
		},
		hadeSubmit(data) {},
		/**
		 * 操作按钮点击事件
		 * @param {*} res
		 */
		btnClick(res) {
			let code = res.handle.code;
			switch (code) {
				case 'add':
					// 新增
					this.formTitle = '新增';
					this.editModule(this.formItemList, []);
					const snowflake = new SnowflakeId();
					let id = snowflake.generate();
					this.ownId = id;
					this.formData = { id: id, state: true, photoUrlTemp: null };
					this.showForm = true;
					break;
				case 'edit':
					// 编辑
					this.formTitle = '编辑';
					this.ownId = res.row.id;
					this.editModule(this.formItemList, ['pwd']);
					this.$request({
						url: interfaceUrl.info + '/' + res.row.id,
						method: 'GET'
					}).then(res => {
						if (res.rCode == 0) {
							this.formData = res.results;
							if (undefined !== this.formData.photoUrl && null !== this.formData.photoUrl) {
								this.formData.photoUrlTemp = this.formData.photoUrl;
							}
							this.formData.state = 1 == this.formData.state ? true : false;
							this.formData.sex = this.formData.sex + '';
							this.showForm = true;
						}
					});
					break;
				case 'view':
					this.formTitle = '查看';
					this.ownId = res.row.id;
					this.readModule(this.formItemList, ['pwd']);
					this.$request({
						url: interfaceUrl.info + '/' + res.row.id,
						method: 'GET'
					}).then(res => {
						if (res.rCode == 0) {
							this.formData = res.results;
							if (undefined !== this.formData.photoUrl && null !== this.formData.photoUrl) {
								this.formData.photoUrlTemp = this.formData.photoUrl;
							}
							this.formData.state = 1 == this.formData.state ? true : false;
							this.formData.sex = this.formData.sex + '';
							this.showForm = true;
						}
					});
					break;
				case 'setRole':
					this.showRoleForm = true;
					this.roleFormData.userId = res.row.id;
					this.roleFormData.username = res.row.username;
					this.roleFormData.phone = res.row.phone;
					// 获取企业选择列表
					this.$request({
						url: interfaceUrl.enpSelectList,
						method: 'GET',
						params: {
							userId: res.row.id
						}
					}).then(res => {
						if (res.rCode == 0) {
							this.enpList = res.results;
						}
					});
					break;
				case 'initPwd':
					this.$confirm(
						'是否初始化用户“' + res.row.username + res.row.loginname + '”密码为账号?',
						'提示',
						{
							confirmButtonText: '确定',
							cancelButtonText: '取消',
							type: 'warning'
						}
					)
						.then(() => {
							this.initUserPassword(res.row.id);
						})
						.catch(() => {
							// this.$message({
							// 	type: 'info',
							// 	message: '已取消'
							// });
						});
					break;
				case 'delete':
					this.deleteId = res.row.id;
					this.showDelete = true;
					break;
				default:
					break;
			}
		},
		handleClose() {},
		handleFormItemChange() {},
		handleFormSubmit(data) {
			let formData = data;
			delete formData.extMap;
			let url = '';
			if (this.formTitle == '新增') {
				url = interfaceUrl.save;
				let Base64 = require('js-base64').Base64;
				formData['password'] = Base64.encode(formData.pwd);
			} else {
				url = interfaceUrl.update;
			}
			formData.state = formData.state ? 1 : 0;

			if (undefined != formData.photoUrlTemp) {
				formData.photoUrl =
					host +
					'ybzy/mecpfileManagement/front/previewAdjunct.json?adjunctId=' +
					formData.photoUrlTemp;
			}
			this.$request({
				url: url,
				data: formData,
				method: 'POST'
			}).then(res => {
				if (res.rCode == 0) {
					this.$message.success('操作成功');
					this.showForm = false;
					this.formData = {};
					this.$refs.table.reload();
				} else {
					this.formData.state = 1 == this.formData.state ? true : false;
					this.$message.error(res.msg);
				}
			});
		},
		initUserPassword(id) {
			this.$request({
				url: interfaceUrl.initUserPwd,
				data: { id: id },
				method: 'POST'
			}).then(res => {
				if (res.rCode == 0) {
					this.$message.success('密码初始化操作成功！');
				} else {
					this.$message.error(res.msg);
				}
			});
		},
		deleteRow() {
			this.$request({
				url: interfaceUrl.deleteBatchIds,
				data: { ids: this.deleteId },
				method: 'POST'
			}).then(res => {
				if (res.rCode == 0) {
					this.$refs.table.reload();
					this.$message.success('删除成功');
				} else {
					this.$message.error(res.msg);
				}
				this.showDelete = false;
			});
		},
		/**
		 * 排序变化事件
		 * @param {*} column
		 * @param {*} prop
		 * @param {*} order
		 */
		sortChange(column, prop, order) {
			if (column.order == 'ascending') {
				//升序
				this.params = {
					asc: 'true',
					orderBy: column.prop
				};
			} else if (column.order == 'descending') {
				//降序
				this.params = {
					asc: 'false',
					orderBy: column.prop
				};
			} else {
				//不排序
				this.params = {
					asc: 'false',
					orderBy: 'createTime'
				};
			}
			this.$refs.table.reload();
		},
		/**
		 * 编辑模式
		 */
		editModule(list, hideField) {
			for (var i in list) {
				var item = list[i];
				item.readonly = false;
				if (undefined != hideField && hideField.length > 0 && hideField.indexOf(item.name) > -1) {
					item.hide = true;
				}
			}
			list.push(this.editBtn);
		},
		/**
		 * 只读模式
		 */
		readModule(list, hideField) {
			for (var i in list) {
				var item = list[i];
				item.readonly = true;
				if (undefined != hideField && hideField.length > 0 && hideField.indexOf(item.name) > -1) {
					item.hide = true;
				}
			}
			list.push(this.cancelBtn);
		},
		changeTable(val) {
			if ('state' === val.name) {
				this.$request({
					url: interfaceUrl.updateState,
					data: {
						id: val.data.id,
						state: val.value
					},
					method: 'POST'
				}).then(res => {
					if (res.rCode == 0) {
						this.$message.success('操作成功');
					} else {
						this.$message.error(res.msg);
					}
				});
			}
		},
		roleInputChange(key, value) {
			if (key == 'enterpriseId') {
				// 获取角色选择列表
				this.$request({
					url: interfaceUrl.roleSelectList,
					method: 'GET',
					params: {
						userId: this.roleFormData.userId,
						enterpriseId: value
					}
				}).then(res => {
					if (res.rCode == 0) {
						this.roleFormData.selectedRoleList = res.results.roleList;
						this.roleList = res.results.roleSelectList;
					}
				});
			}
		},
		handleRoleFormSubmit(data) {
			this.$request({
				url: interfaceUrl.setUserRole,
				data: data,
				method: 'POST',
				format: false
			}).then(res => {
				if (res.rCode == 0) {
					this.$message.success('操作成功');
					this.showRoleForm = false;
					this.roleFormData = {};
					this.$refs.table.reload();
				} else {
					this.$message.error(res.msg);
				}
			});
		}
	}
};
</script>
<style scoped lang="scss">
@import '../../../assets/style/style.scss';

.content {
	width: 100%;
	height: 100%;
}

.el-dialog__body {
	.btn-box {
		margin-top: 20px;
		display: flex;
		justify-content: flex-end;

		.btn {
			padding: 5px 10px;
			color: #666;
			border: 1px solid #eee;
			cursor: pointer;
			margin-right: 5px;
			border-radius: 4px;
			// &.theme {
			// 	background: $--color-primary;
			// 	color: #fff;
			// 	border-color: $--color-primary;
			// }
		}
	}
}
</style>
