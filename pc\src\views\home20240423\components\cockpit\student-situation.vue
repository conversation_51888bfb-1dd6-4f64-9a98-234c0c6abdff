<template>
	<div class="situation">
		<el-tooltip effect="dark" :content="sexData.content" placement="bottom">
			<div class="desc">
				男女比例（男生
				<span class="bold">{{ sexData.nanxsrszb || '0' }}%</span>
				/ 女生
				<span class="bold">{{ sexData.nvxsrszb || '0' }}%</span>
				）
			</div>
		</el-tooltip>
		<!-- 民族 -->
		<div id="nationECharts"></div>
		<!-- 生源 -->
		<div id="sourceECharts"></div>
	</div>
</template>

<script>
import * as echarts from 'echarts';
import { xodbApi2 } from '@/api/xodb';
const memberNationEChartsOptions = {
	title: {
		text: '民族',
		left: '32%',
		top: '45%',
		textStyle: {
			fontWeight: 'bold',
			fontSize: 16,
			color: '#053B6D'
		}
	},
	tooltip: {
		trigger: 'item',
		formatter: '{a} <br/>{b} : {c} ({d}%)'
	},
	graphic: {
		//图形中间图片
		elements: [
			{
				type: 'image',
				style: {
					image: require('@/assets/images/home20240423/nation-echart.png'), //你的图片地址
					width: 30,
					height: 28
				},
				left: '12%',
				top: 'center'
			}
		]
	},
	legend: [
		{
			orient: 'vertical',
			top: '15%',
			right: '30%',
			itemWidth: 10, // 标志图形的长度
			itemHeight: 8, // 标志图形的宽度
			type: 'scroll',

			textStyle: {
				fontSize: 12, // 字体大小
				color: '#454545', // 字体颜色
				rich: {
					a: {
						fontSize: 16,
						fontWeight: 'bold',
						color: '#0A325B',
						display: 'block',
						marginBottom: 3
					}
				}
			},
			itemGap: 14,
			data: []
		},
		{
			orient: 'vertical',
			right: '0%',
			top: '15%',
			type: 'scroll',
			itemWidth: 10, // 标志图形的长度
			itemHeight: 8, // 标志图形的宽度
			formatter: function (name) {
				// 添加
				let total = 0;
				let target;
				// for (let i = 0; i < data.length; i++) {
				// 	total += data[i].value;
				// 	if (data[i].name === name) {
				// 		target = data[i].value;
				// 	}
				// }
				var arr = ['{a|' + (0.34 * 100).toFixed(2) + '%' + '}', '\n', name];
				return arr.join('  ');
			},
			textStyle: {
				fontSize: 12, // 字体大小
				color: '#454545', // 字体颜色
				rich: {
					a: {
						fontSize: 16,
						fontWeight: 'bold',
						color: '#0A325B',
						display: 'block',
						marginBottom: 3
					}
				}
			},
			itemGap: 14,
			data: []
		}
	],
	series: [
		{
			name: '民族',
			type: 'pie',
			label: {
				show: false,
				formatter: '{b}\n{c}人',
				position: 'center',
				shadowColor: '#fff'
			},
			emphasis: {
				label: {
					show: true
				}
			},
			radius: [30, 50],
			center: ['15%', '50%'],
			itemStyle: {
				borderRadius: 2
			},
			data: []
		},
		{
			name: '外边框',
			type: 'pie',
			// clockWise: false, //顺时加载
			hoverAnimation: false, //鼠标移入变大
			radius: [60, 60],
			center: ['15%', '50%'],
			label: {
				normal: {
					show: false
				}
			},
			tooltip: {
				show: false
			},
			data: [
				{
					value: 9,
					name: '',
					itemStyle: {
						normal: {
							borderWidth: 3,
							borderColor: '#fff'
						}
					}
				}
			]
		}
	],
	color: ['#FFD56D', '#FFFA4E', '#50E4F4', '#FF82E5', '#7AEB81']
};
const memberSourceEChartsOptions = {
	title: {
		text: '生源',
		left: '32%',
		top: '45%',
		textStyle: {
			fontWeight: 'bold',
			fontSize: 16,
			color: '#053B6D'
		}
	},
	tooltip: {
		trigger: 'item',
		formatter: '{a} <br/>{b} : {c} ({d}%)'
	},
	graphic: {
		//图形中间图片
		elements: [
			{
				type: 'image',
				style: {
					image: require('@/assets/images/home20240423/source-echart.png'), //你的图片地址
					width: 30,
					height: 28
				},
				left: '12%',
				top: 'center'
			}
		]
	},
	legend: [
		{
			orient: 'vertical',
			top: '15%',
			right: '20%',
			itemWidth: 10, // 标志图形的长度
			itemHeight: 8, // 标志图形的宽度
			type: 'scroll',
			formatter: function (name) {
				return echarts.format.truncateText(name, 1, '14px Microsoft Yahei', '…');
			},
			textStyle: {
				fontSize: 12, // 字体大小
				color: '#454545', // 字体颜色
				rich: {
					a: {
						fontSize: 16,
						fontWeight: 'bold',
						color: '#0A325B',
						display: 'block',
						marginBottom: 3
					}
				}
			},
			itemGap: 14,
			data: []
		},
		{
			orient: 'vertical',
			right: '0%',
			top: '15%',
			itemWidth: 10, // 标志图形的长度
			itemHeight: 8, // 标志图形的宽度
			type: 'scroll',
			tooltip: {
				show: true
			},
			textStyle: {
				fontSize: 12, // 字体大小
				color: '#454545', // 字体颜色
				rich: {
					a: {
						fontSize: 16,
						fontWeight: 'bold',
						color: '#0A325B',
						display: 'block',
						marginBottom: 3
					}
				}
			},
			itemGap: 14,
			data: []
		}
	],
	series: [
		{
			name: '生源',
			type: 'pie',
			label: {
				show: false,
				formatter: '{b}\n{c}人',
				position: 'center',
				// color:"#000",
				shadowColor: '#fff'
			},

			emphasis: {
				label: {
					show: true
				}
			},
			radius: [30, 50],
			center: ['15%', '50%'],
			itemStyle: {
				borderRadius: 2
			},
			data: []
		},
		{
			name: '外边框',
			type: 'pie',
			// clockWise: false, //顺时加载
			hoverAnimation: false, //鼠标移入变大
			radius: [60, 60],
			center: ['15%', '50%'],
			label: {
				normal: {
					show: false
				}
			},
			tooltip: {
				show: false
			},
			data: [
				{
					value: 9,
					name: '',
					itemStyle: {
						normal: {
							borderWidth: 3,
							borderColor: '#fff'
						}
					}
				}
			]
		}
	],
	color: ['#ADDCF8', '#F979AA', '#FF9F59', '#5B54F7', '#AEDF27']
};
export default {
	name: '',
	components: {},
	props: {},
	data() {
		return {
			sourcCharts: null,
			nationCharts: null,
			nationData: [],
			sourceData: [],
			sexData: { nvxsrszb: '', nanxsrszb: '', content: '' } //学生性别数据
		};
	},
	computed: {
		loginUserInfo() {
			return JSON.parse(localStorage.getItem('loginUserInfo')) || {};
		}
	},
	created() {
		// 学生性别分布
		this.getDataN('ads_xs_xsxbqk_query', 'sexData');
		this.getXsmzfbList();
		this.getXssyfbList();
	},
	methods: {
		async getDataN(url, listName) {
			const LeaderRole = localStorage.getItem('LeaderRole');

			try {
				const {
					data: { list }
				} = await xodbApi2.post({
					url,
					params: {
						// xyjgbh: '1'
						// xyjgbh: '203'，
						xyjgbh: LeaderRole === 'xLeader' ? '1' : this.loginUserInfo.orgCode
					}
				});
				switch (listName) {
					case 'sexData':
						this[listName].nvxsrszb = (list[0]?.xbrszb || 0) * 100;
						this[listName].nanxsrszb = (list[1]?.xbrszb || 0) * 100;
						this[listName].content = `男生${list[1]?.xbrs}人 女生${list[0]?.xbrs}人`;
						break;
				}
				console.log(this[listName], 'this[listName]');
			} catch (error) {
				console.error(`${listName}:处理数据失败${listName}:`, error);
			}
		},
		createNationECharts(data, legend) {
			if (this.nationCharts) {
				this.nationCharts.dispose();
			}
			let chartDom = document.getElementById('nationECharts');
			if (!chartDom) {
				return;
			}
			this.nationCharts = echarts.init(chartDom);
			let options = memberNationEChartsOptions;
			options.series[0].data = data;
			// 区别legend
			let legendLen = Math.ceil(legend.length / 2);
			options.legend[0].data = legend.splice(0, legendLen);
			options.legend[1].data = legend;
			options.legend[0].formatter = name => {
				let target;
				for (let i = 0; i < this.nationData.length; i++) {
					if (this.nationData[i].name === name) {
						target = this.nationData[i].mzrszb;
					}
				}
				var arr = ['{a|' + (target * 100).toFixed(2) + '%\n' + '}', name];
				return arr.join('  ');
			};
			options.legend[1].formatter = name => {
				let target;
				for (let i = 0; i < this.nationData.length; i++) {
					if (this.nationData[i].name === name) {
						target = this.nationData[i].mzrszb;
					}
				}
				var arr = ['{a|' + (target * 100).toFixed(2) + '%\n' + '}', name];
				return arr.join('  ');
			};
			this.nationCharts.setOption(options);
		},
		createSourceECharts(data, legend) {
			if (this.sourcCharts) {
				this.sourcCharts.dispose();
			}
			let chartDom = document.getElementById('sourceECharts');
			if (!chartDom) {
				return;
			}
			this.sourcCharts = echarts.init(chartDom);
			let options = memberSourceEChartsOptions;
			options.series[0].data = data;
			// 区别legend
			let legendLen = Math.ceil(legend.length / 2);
			options.legend[0].data = legend.splice(0, legendLen);
			options.legend[1].data = legend;
			options.legend[0].formatter = name => {
				let target;
				let total = 0;
				for (let i = 0; i < this.sourceData.length; i++) {
					total += this.sourceData[i].xzrs;
					if (this.sourceData[i].name === name) {
						target = this.sourceData[i].xzrs;
					}
				}
				console.log(name, target, total, '------target / total');
				var arr = ['{a|' + ((target / total) * 100).toFixed(2) + '%\n' + '}', name];
				return arr.join('  ');
			};
			options.legend[1].formatter = name => {
				let target;
				let total = 0;
				for (let i = 0; i < this.sourceData.length; i++) {
					total += this.sourceData[i].xzrs;
					if (this.sourceData[i].name === name) {
						target = this.sourceData[i].xzrs;
					}
				}
				var arr = ['{a|' + ((target / total) * 100).toFixed(2) + '%\n' + '}', name];
				return arr.join('  ');
			};
			this.sourcCharts.setOption(options);
		},
		// 学生民族分布
		async getXsmzfbList() {
			const LeaderRole = localStorage.getItem('LeaderRole');

			const {
				data: { list }
			} = await xodbApi2.post({
				url: 'ads_xs_xsmzqk_query',
				params: {
					xyjgbh: LeaderRole === 'xLeader' ? '1' : this.loginUserInfo.orgCode
				}
			});
			let nationLegend = [];
			this.nationData = [];
			list.reduce((acc, cur) => {
				if (cur.mzrs > 0) {
					acc.push(cur.mzmc);
					this.nationData.push({
						...cur,
						name: cur.mzmc,
						value: cur.mzrs
					});
					return acc;
				}
			}, nationLegend);
			this.$nextTick(() => {
				this.createNationECharts(this.nationData, nationLegend);
			});
		},
		// 学生生源分布
		async getXssyfbList() {
			const LeaderRole = localStorage.getItem('LeaderRole');
			const {
				data: { list }
			} = await xodbApi2.post({
				url: 'ads_xs_xssyqk_query',
				params: {
					// xyjgbh: '1'
					// xyjgbh: '203'，
					xyjgbh: LeaderRole === 'xLeader' ? '1' : this.loginUserInfo.orgCode
				}
			});

			let nationLegend = [];
			this.sourceData = list.reduce((result, current) => {
				let existingItem = result.find(item => item.xz === current.xz);
				if (existingItem) {
					existingItem.value += current.xzrs;
				} else {
					result.push({ ...current, name: current.xz, value: current.xzrs });
					nationLegend.push(current.xz);
				}
				return result;
			}, []);
			this.$nextTick(() => {
				this.createSourceECharts(this.sourceData, nationLegend);
			});
		}
	}
};
</script>

<style lang="scss" scoped>
.situation {
	width: 100%;
	height: 100%;
	position: relative;
	.desc {
		position: absolute;
		right: 0;
		top: -20px;
		font-family: MicrosoftYaHei;
		font-size: 14px;
		color: #454545;
		line-height: 19px;
		.bold {
			font-weight: bold;
			color: #0a325b;
		}
	}
}
#nationECharts,
#sourceECharts {
	width: 100%;
	height: 50%;
	position: relative;
	&::after {
		content: '';
		height: 112px;
		position: absolute;
		border-left: 1px dashed #a9bed5;
		top: 20%;
		left: 45%;
	}
}
#sourceECharts {
	border-top: 1px dashed #b2c6dc;
}
</style>
