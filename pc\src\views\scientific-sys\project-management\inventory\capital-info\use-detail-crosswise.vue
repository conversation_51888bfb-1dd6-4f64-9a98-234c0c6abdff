<!--
 @desc:资金信息 使用明细  横向项目专用
 @author: WH
 @date: 2023/11/13
 -->
<template>
	<main>
		<title-card title="经费使用明细">
			<div class="title-btn">
				<p>变更项目经费明细后，请确认保存</p>
				<es-button type="primary" round @click="submit" v-if="!readonly">保存</es-button>
			</div>
		</title-card>
		<div class="is-form-box">
			<el-form ref="elform" :model="formData" label-width="120px" class="is-form">
				<el-form-item label="项目经费明细">
					<ul class="table-box">
						<li class="row is-head">
							<div>经费支出类型</div>
							<div>金额（元）</div>
							<div v-if="!readonly">
								<es-button
									type="primary"
									size="mini"
									icon="es-icon-jiahao"
									circle
									@click="addTableRow"
								/>
							</div>
						</li>
						<li v-for="(item, index) in formData" :key="index" class="row is-body">
							<div>
								<el-input placeholder="请输入名称" v-model="item.title" clearable></el-input>
							</div>
							<div>
								<el-input
									placeholder="请输入金额"
									type="number"
									v-model="item.amount"
								></el-input>
							</div>
							<div v-if="!readonly">
								<es-button
									type="danger"
									size="mini"
									icon="es-icon-jianhao"
									circle
									@click="delTableRow(index)"
								/>
							</div>
						</li>
					</ul>
				</el-form-item>
				<el-form-item label="报销凭证">
					<es-upload v-bind="attrs" :disabled="readonly"></es-upload>
				</el-form-item>
			</el-form>
			<!-- <div class="btn-box">
				<el-button size="medium" @click="submit">确认</el-button>
				<el-button type="primary" size="medium" @click="reset">取消</el-button>
			</div> -->
		</div>
	</main>
</template>

<script>
import { getCSCapitalUseList, saveCSCapitalUseByid } from '@/api/scientific-sys.js';
import TitleCard from '@cpt/scientific-sys/title-card.vue';
import { v4 as uuidv4 } from 'uuid';
export default {
	name: 'approvalList',
	components: { TitleCard },
	inject: ['id', 'openType', 'initActiveName'],
	data() {
		return {
			// 弹窗
			isOnload: false,
			dataChange: 0, //是否改变过数据 父级刷新列表用
			readonly: false,
			attrs: {
				code: 'transationform_editfile',
				ownId: uuidv4(),
				// 业务id
				download: true,
				dragSort: true //是否允许附件列表进行拖拽排序
			},
			formData: [
				{
					title: '材料费',
					amount: ''
				},
				{
					title: '专家咨询费',
					amount: ''
				},
				{
					title: '劳务费',
					amount: ''
				}
			]
		};
	},
	created() {
		//初次打开名字是CapitalInfo 才能够编辑经费信息和经费使用明细
		if (this.openType == 'look' || this.initActiveName == 'BasicInfo') {
			this.readonly = true;
		}
		this.getList();
	},
	beforeDestroy() {
		if (this.dataChange != 0) {
			this.$bus.$emit('closeDialog', true);
		}
	},
	methods: {
		addTableRow() {
			this.formData.push({
				title: '',
				amount: ''
			});
		},
		delTableRow(index) {
			this.$delete(this.formData, index);
		},

		del() {
			this.$confirm('是否删除?', '提示', {
				confirmButtonText: '确定',
				cancelButtonText: '取消',
				type: 'warning'
			})
				.then(() => {})
				.catch(() => {
					this.$message({
						type: 'info',
						message: '已取消'
					});
				});
		},
		async getList() {
			const loading = this.load('加载中...');
			try {
				let { rCode, msg, results } = await this.$.ajax({
					url: getCSCapitalUseList,
					method: 'get',
					params: { projectId: this.id }
				});
				if (rCode == 0) {
					if (results?.records?.length > 0) {
						this.formData = [...results.records];
						this.attrs.ownId = results.records[0].adjunctId;
					}
				} else {
					this.$message.error(msg);
				}
				this.isOnload = true;
			} catch (error) {
				console.log('>>>error', error);
			} finally {
				loading.close();
			}
		},
		async submit() {
			const loading = this.load('提交中...');
			try {
				// let formData = { ...this.formData };
				// delete formData.adjunctName;
				let formData = {
					adjunctId: this.attrs.ownId, //报销凭证
					list: [...this.formData],
					projectId: this.id //projectId==最外层列表id 项目id
				};
				let { rCode, msg, results } = await this.$.ajax({
					//有id代表修改 无代表新增
					url: saveCSCapitalUseByid,
					// url: formData.id ? updateCapitalUseByid : saveCSCapitalUseByid,
					method: 'post',
					format: false,
					data: formData
				});
				if (rCode == 0) {
					this.dataChange += 1;
					this.$message.success(msg);
				} else {
					this.$message.error(msg);
				}
			} catch (error) {
				console.log('>>>error', error);
			} finally {
				loading.close();
			}
		},
		reset() {
			this.formData = [
				{
					title: '材料费',
					amount: ''
				},
				{
					title: '专家咨询费',
					amount: ''
				},
				{
					title: '劳务费',
					amount: ''
				}
			];
			this.visible = false;
		},
		// 页面加载
		load(text) {
			return this.$.loading(this.$loading, text);
		}
	}
};
</script>
<style lang="scss" scoped>
@import '@ast/style/public.scss';
$--border-color: #d1d1d1;
main {
	width: 100%;
	height: 100%;
	overflow: auto;
	.mini-title {
		position: sticky;
		top: 0;
		background: #fff;
		z-index: 99;
		margin-top: 0;
	}
	.title-btn {
		@include flexBox();
		margin-right: 16px;
		p {
			color: #f5222d;
		}
	}
	.is-form-box {
		width: 100%;
		height: 50%;
		.table-box {
			// max-height: 200px;
			// overflow: auto;
			.row {
				@include flexBox();
				width: 100%;
				height: 60px;
				border: 1px solid $--border-color;
				& > div {
					height: 100%;
					// line-height: 50px;
					// text-align: center;
					padding: 0 10px;
					@include flexBox();

					&:nth-of-type(1) {
						border-right: 1px solid $--border-color;
						width: 180px;
						padding: 0 10px;
					}
					&:nth-of-type(2) {
						@include flexBox(flex-start);
						border-right: 1px solid $--border-color;
						flex: 1;
					}
					&:nth-of-type(3) {
						width: 100px;
					}
				}
			}
			.is-head {
				height: 50px;
				font-weight: 550;
			}
		}
	}
}
</style>
