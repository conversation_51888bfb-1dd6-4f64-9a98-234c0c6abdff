<template>
	<div class="main">
		<div class="query-container">
			<el-form :inline="true" :model="page" class="form-inline">
				<el-form-item>
					<el-input v-model="page.keyword" placeholder="请输入关键词" clearable></el-input>
				</el-form-item>
				<el-form-item>
					<el-button type="primary" @click="handleSearch">搜索</el-button>
					<el-button @click="resetSearch">重置</el-button>
					<el-button type="success" @click="handleAdd">新增</el-button>
				</el-form-item>
			</el-form>
		</div>

		<div class="table-container" v-loading="tableLoading">
			<el-table :data="tableData" border style="width: 100%">
				<el-table-column
					prop="userName"
					label="用户名称"
					align="center"
					width="120"
				></el-table-column>

				<el-table-column prop="orgName" label="所在机构名称" align="center"></el-table-column>
				<el-table-column
					prop="lendType"
					label="出借类型"
					align="center"
					width="100"
				></el-table-column>

				<el-table-column prop="lendOrgName" label="出借机构名称" align="center"></el-table-column>
				<el-table-column
					prop="lendPlace"
					label="借调说明"
					align="center"
					width="200"
					:show-overflow-tooltip="true"
				></el-table-column>
				<el-table-column
					prop="lendStartTime"
					label="借调开始时间"
					align="center"
					width="160"
				></el-table-column>
				<el-table-column
					prop="lendEndTime"
					label="借调结束时间"
					align="center"
					width="160"
				></el-table-column>

				<el-table-column label="操作" width="200" align="center" fixed="right">
					<template slot-scope="scope">
						<!-- <el-button type="text" size="small" @click="handleDetail(scope.row)">查看</el-button> -->
						<el-button
							type="text"
							size="small"
							style="color: #409eff"
							@click="handleEdit(scope.row)"
						>
							编辑
						</el-button>
						<el-button
							type="text"
							size="small"
							style="color: #f56c6c"
							@click="handleDelete(scope.row)"
						>
							删除
						</el-button>
					</template>
				</el-table-column>
			</el-table>

			<div class="pagination-container">
				<el-pagination
					@size-change="handleSizeChange"
					@current-change="handleCurrentChange"
					:current-page="page.pageNum"
					:page-sizes="[10, 20, 50, 100]"
					:page-size="page.pageSize"
					layout="total, sizes, prev, pager, next, jumper"
					:total="total"
				></el-pagination>
			</div>
		</div>

		<!-- 新增/编辑弹窗 -->
		<el-dialog :title="dialogTitle" :visible.sync="dialogVisible" width="600px" append-to-body>
			<el-form :model="formData" :rules="rules" ref="secondmentForm" label-width="120px">
				<el-form-item label="用户名称" prop="userName">
					<el-input
						v-model="formData.userName"
						placeholder="请选择用户"
						style="width: 100%"
						@focus="openUserOrgSelector"
					>
						<el-button slot="append" icon="el-icon-search" @click="openUserOrgSelector"></el-button>
					</el-input>
				</el-form-item>
				<el-form-item label="用户ID" prop="userId" v-show="false">
					<el-input v-model="formData.userId"></el-input>
				</el-form-item>

				<el-form-item label="所在机构名称" prop="orgName">
					<el-input
						v-model="formData.orgName"
						placeholder="请选择所在机构"
						readonly
						disabled
						style="width: 100%"
					></el-input>
				</el-form-item>
				<el-form-item label="所在机构编号" prop="orgId" v-show="false">
					<el-input v-model="formData.orgId"></el-input>
				</el-form-item>
				<el-form-item label="出借类型" prop="lendType">
					<el-radio-group v-model="formData.lendType" @change="handleLendTypeChange">
						<el-radio label="内借">内借</el-radio>
						<el-radio label="外借">外借</el-radio>
					</el-radio-group>
				</el-form-item>
				<el-form-item label="出借机构名称" prop="lendOrgName" v-if="formData.lendType === '内借'">
					<el-cascader
						v-model="selectedOrgIds.lendOrg"
						:options="orgTreeData"
						:props="cascaderProps"
						clearable
						filterable
						placeholder="请选择出借机构"
						style="width: 100%"
						@change="handleOrgChange('lendOrg')"
						:loading="orgTreeLoading"
					></el-cascader>
				</el-form-item>
				<el-form-item label="出借机构编号" prop="lendOrgId" v-show="false">
					<el-input v-model="formData.lendOrgId"></el-input>
				</el-form-item>
				<el-form-item label="借调说明" prop="lendPlace">
					<el-input
						type="textarea"
						:rows="3"
						v-model="formData.lendPlace"
						placeholder="请输入借调说明（外借必填）"
					></el-input>
				</el-form-item>
				<el-form-item label="借调开始时间" prop="lendStartTime">
					<el-date-picker
						v-model="formData.lendStartTime"
						type="datetime"
						placeholder="选择开始时间"
						value-format="yyyy-MM-dd HH:mm:ss"
						style="width: 100%"
					></el-date-picker>
				</el-form-item>
				<el-form-item label="借调结束时间" prop="lendEndTime">
					<el-date-picker
						v-model="formData.lendEndTime"
						type="datetime"
						placeholder="选择结束时间"
						value-format="yyyy-MM-dd HH:mm:ss"
						style="width: 100%"
					></el-date-picker>
				</el-form-item>
			</el-form>
			<div slot="footer" class="dialog-footer">
				<el-button @click="dialogVisible = false">取 消</el-button>
				<el-button type="primary" @click="submitForm">确 定</el-button>
			</div>
		</el-dialog>

		<!-- 用户机构选择弹窗 -->
		<el-dialog title="选择用户" :visible.sync="userOrgDialogVisible" width="900px" append-to-body>
			<div class="user-org-container">
				<div class="org-tree-container">
					<div class="panel-title">机构列表</div>
					<div v-loading="orgTreeLoading" class="tree-wrapper">
						<el-input
							placeholder="搜索机构"
							v-model="orgSearchKeyword"
							prefix-icon="el-icon-search"
							clearable
						></el-input>
						<el-tree
							ref="orgTree"
							:data="orgTreeData"
							:props="{
								label: 'shortName',
								children: 'children'
							}"
							node-key="id"
							highlight-current
							default-expand-all
							@node-click="handleOrgTreeNodeClick"
							:filter-node-method="filterNode"
						></el-tree>
					</div>
				</div>
				<div class="user-list-container">
					<div class="panel-title">用户列表</div>
					<div class="user-search">
						<el-input
							placeholder="搜索用户"
							v-model="userSearchKeyword"
							prefix-icon="el-icon-search"
							clearable
							@clear="getUserListByOrg"
							@keyup.enter.native="getUserListByOrg"
						>
							<el-button slot="append" icon="el-icon-search" @click="getUserListByOrg"></el-button>
						</el-input>
					</div>
					<div v-loading="userLoading" class="user-table-wrapper">
						<el-table
							:data="userList"
							height="400"
							border
							@row-click="handleUserSelect"
							highlight-current-row
						>
							<el-table-column prop="userName" label="用户名称"></el-table-column>
							<el-table-column prop="loginName" label="教工号"></el-table-column>
							<el-table-column prop="orgName" label="所在机构"></el-table-column>
						</el-table>
						<div class="pagination-container">
							<el-pagination
								@size-change="handleUserSizeChange"
								@current-change="handleUserCurrentChange"
								:current-page="userPage.pageNum"
								:page-sizes="[10, 20, 50]"
								:page-size="userPage.pageSize"
								layout="total, sizes, prev, pager, next"
								:total="userTotal"
							></el-pagination>
						</div>
					</div>
				</div>
			</div>
			<div slot="footer" class="dialog-footer">
				<el-button @click="userOrgDialogVisible = false">取 消</el-button>
				<el-button type="primary" @click="confirmUserSelect">确 定</el-button>
			</div>
		</el-dialog>
	</div>
</template>

<script>
export default {
	data() {
		return {
			tableData: [],
			tableLoading: false,
			total: 0,
			page: {
				keyword: '',
				pageNum: 1,
				pageSize: 10
			},
			// 弹窗相关数据
			dialogVisible: false,
			dialogTitle: '新增借调信息',
			formData: {
				id: '',
				userId: '',
				userName: '',
				orgId: '',
				orgName: '',
				lendType: '内借',
				lendOrgId: '',
				lendOrgName: '',
				lendPlace: '',
				lendStartTime: '',
				lendEndTime: ''
			},
			isEdit: false,
			rules: {
				userName: [{ required: true, message: '请选择用户', trigger: 'change' }],
				orgName: [{ required: true, message: '请选择所在机构', trigger: 'change' }],
				lendType: [{ required: true, message: '请选择出借类型', trigger: 'change' }],
				lendOrgName: [
					{
						required: true,
						message: '请选择出借机构',
						trigger: 'change',
						validator: (rule, value, callback) => {
							if (this.formData.lendType === '内借' && !value) {
								callback(new Error('请选择出借机构'));
							} else {
								callback();
							}
						}
					}
				],
				lendPlace: [
					{
						validator: (rule, value, callback) => {
							if (this.formData.lendType === '外借' && !value) {
								callback(new Error('外借时借调说明必填'));
							} else {
								callback();
							}
						},
						trigger: 'blur'
					}
				],
				lendStartTime: [{ required: true, message: '请选择借调开始时间', trigger: 'change' }],
				lendEndTime: [
					{ required: true, message: '请选择借调结束时间', trigger: 'change' },
					{
						validator: (rule, value, callback) => {
							if (
								this.formData.lendStartTime &&
								value &&
								new Date(value) <= new Date(this.formData.lendStartTime)
							) {
								callback(new Error('结束时间必须大于开始时间'));
							} else {
								callback();
							}
						},
						trigger: 'change'
					}
				]
			},
			// 机构选择相关数据
			orgTreeData: [],
			orgTreeLoading: false,
			cascaderProps: {
				checkStrictly: true, // 可选择任意一级
				value: 'id',
				label: 'shortName', // 使用shortName作为显示标签
				children: 'children',
				emitPath: false // 只返回选中节点的值
			},
			selectedOrgIds: {
				org: null,
				lendOrg: null
			},

			// 用户选择相关数据
			userList: [],
			userLoading: false,

			// 用户机构选择弹窗相关数据
			userOrgDialogVisible: false,
			orgSearchKeyword: '',
			userSearchKeyword: '',
			userPage: {
				pageNum: 1,
				pageSize: 10,
				orgId: null
			},
			userTotal: 0,
			selectedUser: null,
			selectedOrg: null
		};
	},
	watch: {
		orgTreeData(val) {
			this.selectedOrgIds.org = null;
			this.selectedOrgIds.lendOrg = null;
		},
		orgSearchKeyword(val) {
			this.$refs.orgTree.filter(val);
		}
	},
	mounted() {
		this.getSecondmentInfo();
		this.getOrgTree(); // 页面加载时获取机构树数据
	},
	methods: {
		handleLendTypeChange(val) {
			if (val == '外借') {
				this.formData.lendOrgId = '';
				this.formData.lendOrgName = '';
			}
		},
		// 借调信息分页接口
		getSecondmentInfo() {
			this.tableLoading = true;
			this.$request({
				url: '/ybzy/attendanceRecordLend/listJson',
				method: 'get',
				params: this.page
			})
				.then(res => {
					this.tableData = res.results.records;
					this.total = res.results.total;
					this.tableLoading = false;
				})
				.catch(() => {
					this.tableLoading = false;
				});
		},

		// 搜索
		handleSearch() {
			this.page.pageNum = 1;
			this.getSecondmentInfo();
		},

		// 重置搜索
		resetSearch() {
			this.page.keyword = '';
			this.page.pageNum = 1;
			this.getSecondmentInfo();
		},

		// 改变每页条数
		handleSizeChange(val) {
			this.page.pageSize = val;
			this.getSecondmentInfo();
		},

		// 改变页码
		handleCurrentChange(val) {
			this.page.pageNum = val;
			this.getSecondmentInfo();
		},

		// 查看详情
		handleDetail(row) {
			// 查看详情功能，根据需要实现
			console.log('查看详情', row);
		},

		// 获取用户列表(初始化时调用)
		getUserList() {
			// 这个方法保留用于兼容现有代码
			// 实际用户列表获取由getUserListByOrg方法处理
		},

		// 打开用户机构选择弹窗
		openUserOrgSelector() {
			this.userOrgDialogVisible = true;
			this.orgSearchKeyword = '';
			this.userSearchKeyword = '';
			this.selectedUser = null;
			this.userPage.pageNum = 1;
			this.userPage.orgId = null;

			// 如果已经有选择的用户，尝试在树中定位
			if (this.formData.orgId) {
				this.$nextTick(() => {
					// 尝试在树中找到并高亮当前选中的机构
					if (this.$refs.orgTree) {
						this.$refs.orgTree.setCurrentKey(this.formData.orgId);
					}
				});
			}

			// 加载用户列表
			this.getUserListByOrg();
		},

		// 处理机构树节点点击
		handleOrgTreeNodeClick(data) {
			this.selectedOrg = data;
			this.userPage.orgId = data.id;
			this.userPage.pageNum = 1;
			this.getUserListByOrg();
		},
		filterNode(value, data) {
			if (!value) return true;
			return data.shortName.indexOf(value) !== -1;
		},
		// 根据机构获取用户列表
		getUserListByOrg() {
			this.userLoading = true;
			this.$request({
				url: '/ybzy/attendanceRecordLend/userPage',
				method: 'get',
				params: {
					keyword: this.userSearchKeyword,
					pageNum: this.userPage.pageNum,
					pageSize: this.userPage.pageSize,
					orgId: this.userPage.orgId
				}
			})
				.then(res => {
					this.userList = res.results.records || [];
					this.userTotal = res.results.total || 0;
				})
				.finally(() => {
					this.userLoading = false;
				});
		},

		// 处理用户分页大小变化
		handleUserSizeChange(val) {
			this.userPage.pageSize = val;
			this.getUserListByOrg();
		},

		// 处理用户分页页码变化
		handleUserCurrentChange(val) {
			this.userPage.pageNum = val;
			this.getUserListByOrg();
		},

		// 处理用户选择
		handleUserSelect(row) {
			this.selectedUser = row;
		},

		// 确认用户选择
		confirmUserSelect() {
			if (this.selectedUser) {
				this.formData.userId = this.selectedUser.userId;
				this.formData.userName = this.selectedUser.userName;
				this.formData.orgId = this.selectedUser.orgId;
				this.formData.orgName = this.selectedUser.orgName;
				this.userOrgDialogVisible = false;
			} else {
				this.$message.warning('请选择一个用户');
			}
		},

		// 获取机构树
		getOrgTree() {
			this.orgTreeLoading = true;
			this.$request({
				url: '/ybzy/attendanceRecordLend/orgTree',
				method: 'get'
			})
				.then(res => {
					this.orgTreeData = res.results || [];
				})
				.catch(() => {
					this.$message.error('获取机构树失败');
				})
				.finally(() => {
					this.orgTreeLoading = false;
				});
		},

		// 处理机构选择变化
		handleOrgChange(type) {
			// 获取选中的机构ID
			const orgId = this.selectedOrgIds[type];
			if (!orgId) {
				// 如果清空了选择
				if (type === 'org') {
					this.formData.orgId = '';
					this.formData.orgName = '';
				} else if (type === 'lendOrg') {
					this.formData.lendOrgId = '';
					this.formData.lendOrgName = '';
				}
				return;
			}

			// 递归查找选中的节点
			const findNode = (nodes, id) => {
				for (const node of nodes) {
					if (node.id === id) {
						return node;
					}
					if (node.children && node.children.length) {
						const found = findNode(node.children, id);
						if (found) return found;
					}
				}
				return null;
			};

			const selectedNode = findNode(this.orgTreeData, orgId);
			if (selectedNode) {
				if (type === 'org') {
					this.formData.orgId = selectedNode.id;
					this.formData.orgName = selectedNode.fullName || selectedNode.shortName; // 优先使用fullName
				} else if (type === 'lendOrg') {
					this.formData.lendOrgId = selectedNode.id;
					this.formData.lendOrgName = selectedNode.fullName || selectedNode.shortName; // 优先使用fullName
				}
			}
		},

		// 重置表单数据
		resetFormData() {
			this.formData = {
				id: '',
				userId: '',
				userName: '',
				orgId: '',
				orgName: '',
				lendType: '内借',
				lendOrgId: '',
				lendOrgName: '',
				lendPlace: '',
				lendStartTime: '',
				lendEndTime: ''
			};
			this.selectedOrgIds = {
				org: null,
				lendOrg: null
			};
			this.selectedUser = null;
			if (this.$refs.secondmentForm) {
				this.$refs.secondmentForm.clearValidate();
			}
		},

		// 提交表单
		submitForm() {
			this.$refs.secondmentForm.validate(valid => {
				if (valid) {
					const loadingInstance = this.$loading({
						lock: true,
						text: this.isEdit ? '正在提交修改...' : '正在保存...',
						spinner: 'el-icon-loading',
						background: 'rgba(0, 0, 0, 0.7)'
					});

					// 根据是新增还是编辑调用不同的接口
					const url = this.isEdit
						? '/ybzy/attendanceRecordLend/update'
						: '/ybzy/attendanceRecordLend/save';

					this.$request({
						url: url,
						method: 'post',
						data: this.formData
					})
						.then(res => {
							this.dialogVisible = false;
							this.getSecondmentInfo();
							this.$message({
								message: res.msg,
								type: res.success ? 'success' : 'error'
							});
						})

						.finally(() => {
							loadingInstance.close();
						});
				}
			});
		},

		// 新增借调信息
		handleAdd() {
			this.dialogTitle = '新增借调信息';
			this.isEdit = false;
			this.resetFormData();
			this.getUserList(); // 初始化加载用户列表
			this.dialogVisible = true;

			// 确保DOM更新后再清除验证
			this.$nextTick(() => {
				if (this.$refs.secondmentForm) {
					this.$refs.secondmentForm.clearValidate();
				}
			});
		},

		// 编辑借调信息
		handleEdit(row) {
			this.dialogTitle = '编辑借调信息';
			this.isEdit = true;
			this.resetFormData();
			this.getUserList(); // 初始化加载用户列表

			// 填充表单数据
			this.formData = {
				id: row.id,
				userId: row.userId,
				userName: row.userName,
				orgId: row.orgId,
				orgName: row.orgName,
				lendType: row.lendType,
				lendOrgId: row.lendOrgId,
				lendOrgName: row.lendOrgName,
				lendPlace: row.lendPlace,
				lendStartTime: row.lendStartTime,
				lendEndTime: row.lendEndTime
			};

			// 设置级联选择器的值
			this.selectedOrgIds.org = row.orgId;
			if (row.lendType === '内借' && row.lendOrgId) {
				this.selectedOrgIds.lendOrg = row.lendOrgId;
			}

			this.dialogVisible = true;

			// 确保DOM更新后再清除验证
			this.$nextTick(() => {
				if (this.$refs.secondmentForm) {
					this.$refs.secondmentForm.clearValidate();
				}
			});
		},

		// 处理删除
		handleDelete(row) {
			this.$confirm('确认删除该借调信息吗？', '提示', {
				confirmButtonText: '确定',
				cancelButtonText: '取消',
				type: 'warning'
			})
				.then(() => {
					const loadingInstance = this.$loading({
						lock: true,
						text: '正在删除...',
						spinner: 'el-icon-loading',
						background: 'rgba(0, 0, 0, 0.7)'
					});

					this.$request({
						url: '/ybzy/attendanceRecordLend/del',
						method: 'post',
						data: { id: row.id }
					})
						.then(res => {
							this.$message.success('删除成功');
							this.getSecondmentInfo();
						})
						.catch(() => {
							this.$message.error('删除失败');
						})
						.finally(() => {
							loadingInstance.close();
						});
				})
				.catch(() => {
					// 取消删除操作
				});
		}
	}
};
</script>

<style lang="scss" scoped>
@import '@ast/style/public.scss';
.main {
	height: 100%;
	width: 100%;
	background: #f0f2f5;
	padding: 20px;
	box-sizing: border-box;
	overflow-y: auto;
}

.query-container {
	background: #fff;
	padding: 20px;
	border-radius: 4px;
	margin-bottom: 20px;
}

.table-container {
	background: #fff;
	padding: 20px;
	border-radius: 4px;
}

.pagination-container {
	margin-top: 20px;
	text-align: right;
}

.user-org-container {
	display: flex;
	height: 500px;
}

.org-tree-container {
	width: 300px;
	border-right: 1px solid #e0e0e0;
	padding-right: 10px;
	overflow: hidden;
	display: flex;
	flex-direction: column;
}

.user-list-container {
	flex: 1;
	padding-left: 10px;
	overflow: hidden;
	display: flex;
	flex-direction: column;
}

.panel-title {
	font-size: 16px;
	font-weight: bold;
	margin-bottom: 10px;
	padding-bottom: 10px;
	border-bottom: 1px solid #e0e0e0;
}

.tree-wrapper {
	overflow: auto;
	flex: 1;
}

.user-search {
	margin-bottom: 10px;
}

.user-table-wrapper {
	flex: 1;
	display: flex;
	flex-direction: column;
	overflow: hidden;
}

.user-table-wrapper .el-table {
	flex: 1;
}
</style>
