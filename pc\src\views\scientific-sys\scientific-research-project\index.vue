<!-- eslint-disable no-duplicate-case -->
<template>
	<div class="content">
		<CardList
			v-if="isCard"
			ref="refTable"
			:url="wd.basics.dataTableUrl"
			:toolbar="wd.toolbar"
			:thead="wd.thead"
			:param="params"
			@btnClick="btnClick"
		/>
		<es-data-table
			v-else
			ref="refTable"
			show-label
			:row-style="tableRowClassName"
			:thead="wd.thead"
			:toolbar="wd.toolbar"
			:page="true"
			:url="wd.basics.dataTableUrl"
			:numbers="true"
			:param="params"
			:option-data="optionData"
			style="width: 100%"
			close
			stripe
			:border="true"
			form
			@btnClick="btnClick"
			@sort-change="sortChange"
			@submit="hadeSubmit"
			@edit="changeTable"
		></es-data-table>

		<es-dialog
			:title="formTitle"
			:visible.sync="visibleBasicInfo"
			:show-scale="false"
			size="full"
			height="100%"
		>
			<BasicInfo
				v-if="visibleBasicInfo"
				:id="formId"
				:key="visibleBasicInfo"
				:contents-key="contentsKey"
				:title="formTitle"
				@visible="
					e => {
						visibleBasicInfo = e;
						$refs.refTable.reload();
					}
				"
			/>
		</es-dialog>
	</div>
</template>

<script>
import BasicInfo from '../components/project-info/basic-info';
import CardList from '../components/card-list.vue';
import { mixinList } from './mixinList';
export default {
	components: { BasicInfo, CardList },
	mixins: [mixinList],
	props: {
		// 是否展示卡片列表
		isCard: {
			type: Boolean,
			default: false
		}
	},
	data() {
		return {
			visibleBasicInfo: false,
			deleteId: '',
			codesObj: {},
			showForm: false,
			enpList: [],
			roleList: [],
			formReadonly: true,
			roleFormData: {
				userId: '',
				username: '',
				phone: '',
				selectedRoleList: []
			},
			formId: '',
			formTitle: '查看',
			optionData: {
				state: [
					{
						value: 1,
						name: '启用'
					},
					{
						value: 0,
						name: '禁用'
					}
				]
			},
			editBtn: {
				type: 'submit',
				skin: 'lay-form-btns',
				contents: [
					{
						type: 'primary',
						text: '确定',
						event: 'confirm'
					},
					{
						type: 'reset',
						text: '取消',
						event: 'cancel'
					}
				]
			},
			cancelBtn: {
				type: 'submit',
				skin: 'lay-form-btns',
				contents: [
					{
						type: 'reset',
						text: '取消',
						event: 'cancel'
					}
				]
			},
			pageOption: {
				layout: 'total, prev, pager, next, jumper',
				pageSize: 20,
				// hideOnSinglePage: true,
				position: 'right',
				current: 1,
				pageNum: 1
			},
			params: {
				orderBy: 'createTime',
				asc: 'false' // true 升序，false 降序
			},
			submitFilterParams: {}
		};
	},
	watch: {
		// 监听visibleBasicInfo为true时新列表
		// visibleBasicInfo: {
		// 	handler(val) {
		// 		if (!val && this.formTitle !== '查看') {
		// 			this.$refs.refTable.reload();
		// 		}
		// 	}
		// }
	},
	created() {
		this.getSysCode();
	},
	methods: {
		// 批量请求数据字典
		getSysCode() {
			// 工作量 project_workload
			// 研究类别 project_study_type
			// 活动类型 project_activity_type
			// 项目来源 project_source
			// 组织形式 project_organization_form
			// 合作形式 project_cooperation_form
			// 横向项目-合作类型 project_cooperation_type
			// 纵向项目-合同类型 project_contract_type
			// 项目类型 project_type
			// 申报说明 project_declaration_instructions
			// 项目-预期成果形势|最终成果形势 project_results_form

			// 项目级别 纵向-project_level_lengthways、院级-project_level_college

			//人员类型 project_member_type
			//证件类型 member_idcard_type
			//承担类型 member_assume_type
			//职称 member_rank
			//国籍 member_nationality
			//民族 member_nation
			//政治面貌 member_political_status
			//职务类别 member_position_type
			//岗位类型 member_post_type
			//最后学历 member_highest_education
			//最后学位 member_highest_degree
			// const codes = 'project_type,project_cooperation_type';
			const codes =
				'project_results_form,project_level_lengthways,project_level_college,project_workload,project_study_type,project_activity_type,project_source,project_organization_form,project_cooperation_form,project_cooperation_type,project_contract_type,project_type,project_declaration_instructions,member_assume_type,member_rank,project_member_type,member_idcard_type,member_nationality,member_nation,member_political_status,member_position_type,member_post_type,member_highest_education,member_highest_degree';
			this.$utils.findSysCodeList(codes).then(res => {
				this.codesObj = { ...this.codesObj, ...res };
			});
		},
		/**
		 * 表格行高
		 */
		tableRowClassName({ row, rowIndex }) {
			let styleRes = {
				height: '54px !important'
			};
			return styleRes;
		},
		// 高级搜索确认
		hadeSubmit(e) {
			this.submitFilterParams = e.data;
		},
		/**
		 * 操作按钮点击事件
		 * @param {*} res
		 */
		async btnClick(res) {
			let text = res.handle.text;
			this.formId = res.row?.id || '';


			switch (text) {
				case '查看':
					this.formTitle = '查看';
					this.visibleBasicInfo = true;
			console.log(12312312312312312312312312312312);

					
					break;
				case '编辑':
					this.formTitle = '编辑';
					this.visibleBasicInfo = true;
					break;
				case '新增':
					this.formId = this.$uuidv4();
					this.formTitle = '新增';
					this.visibleBasicInfo = true;
					break;
				case '撤回':
					this.$confirm(`您确定要撤回数据吗？`, '提示', {
						confirmButtonText: '确定',
						cancelButtonText: '取消',
						type: 'warning'
					})
						.then(() => {
							const loading = this.$loading();
							this.$request({
								url: this.wd.basics.revoke,
								data: {
									id: this.formId
								},
								method: 'POST'
							}).then(res => {
								loading.close();
								if (res.rCode == 0) {
									this.$message.success('撤回成功');
									this.$refs.refTable.reload();
								}
							});
						})
						.catch(err => {});
					break;
				case '删除':
					{
						this.$confirm(`您确定要删除数据吗？`, '提示', {
							confirmButtonText: '确定',
							cancelButtonText: '取消',
							type: 'warning'
						})
							.then(() => {
								const loading = this.$loading();
								this.$request({
									url: this.wd.basics.delete,
									data: {
										id: this.formId
									},
									method: 'POST'
								}).then(res => {
									loading.close();
									if (res.rCode == 0) {
										// 删除详情里面的所有附件
										this.$request({
											url: this.wd.basics.info,
											method: 'get',
											params: { id: this.formId }
										}).then(res2 => {
											if (res2.rCode == 0) {
												res2.results.students.map(item => {
													this.$utils.deleteFile('transationform_editfile', item.id);
												});
											}
										});
										this.$utils.deleteFile('transationform_editfile', this.formId);

										this.$message.success(res.msg);
										this.$refs.refTable.reload();
									} else {
										this.$message.error(res.msg);
									}
								});
							})
							.catch(err => {});
					}
					break;
				case '导出':
					this.exportFile();
					break;
				default:
					break;
			}
		},
		//导出函数
		exportFile() {
			let isDev = process.env.NODE_ENV === 'development' ? '/api' : '';
			const paramAll = { ...this.params, ...this.submitFilterParams };
			let url = `${isDev}/ybzy/projectSrAchievement/export?${this.objToUrlParams(paramAll)}`;
			window.open(url);
		},
		objToUrlParams(obj) {
			return Object.keys(obj)
				.map(key => encodeURIComponent(key) + '=' + encodeURIComponent(obj[key]))
				.join('&');
		},
		handleClose() {},
		handleFormItemChange() {},

		/**
		 * 排序变化事件
		 * @param {*} column
		 * @param {*} prop
		 * @param {*} order
		 */
		sortChange(column, prop, order) {
			if (column.order == 'ascending') {
				//升序
				this.params = {
					asc: 'true',
					orderBy: column.prop
				};
			} else if (column.order == 'descending') {
				//降序
				this.params = {
					asc: 'false',
					orderBy: column.prop
				};
			} else {
				//不排序
				this.params = {
					asc: 'false',
					orderBy: 'createTime'
				};
			}
			this.$refs.refTable.reload();
		},
		/**
		 * 编辑模式
		 */
		editModule(list, hideField) {
			for (var i in list) {
				var item = list[i];
				item.readonly = false;
				if (undefined != hideField && hideField.length > 0 && hideField.indexOf(item.name) > -1) {
					item.hide = true;
				}
			}
			list.push(this.editBtn);
		},

		changeTable(val) {
			if ('state' === val.name) {
				this.$request({
					url: this.wd.basics.updateState,
					data: {
						id: val.data.id,
						state: val.value
					},
					method: 'POST'
				}).then(res => {
					if (res.rCode == 0) {
						this.$message.success('操作成功');
					} else {
						this.$message.error(res.msg);
					}
				});
			}
		},
		reset() {
			this.visible = false;
		}
	}
};
</script>
<style scoped lang="scss">
.content {
	width: 100%;
	height: 100%;
}
</style>
