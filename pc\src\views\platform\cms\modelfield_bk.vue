<template>
	<div class="content">
		<es-data-table
			ref="table"
			:key="tableCount"
			:row-style="tableRowClassName"
			:full="true"
			:fit="true"
			:thead="thead"
			:toolbar="toolbar"
			:border="true"
			:page="pageOption"
			:url="dataTableUrl"
			:numbers="true"
			:param="params"
			:option-data="optionData"
			close
			form
			@btnClick="btnClick"
			@sort-change="sortChange"
			@submit="hadeSubmit"
		></es-data-table>
		<es-dialog
			:title="formTitle"
			:visible.sync="showForm"
			width="60%"
			:close-on-click-modal="false"
			:destroy-on-close="true"
		>
			<es-form
				ref="form"
				:model="formData"
				:contents="formItemList"
				height="500px"
				:genre="2"
				collapse
				@change="inputChange"
				@submit="handleFormSubmit"
				@reset="showForm = false"
			/>
		</es-dialog>
		<es-dialog
			title="删除"
			:visible.sync="showDelete"
			width="20%"
			:close-on-click-modal="false"
			:middle="true"
			height="140px"
		>
			<div>确定要删除该条数据吗</div>
			<div class="btn-box">
				<div class="btn theme" @click="deleteRow">确定</div>
				<div class="btn" @click="showDelete = false">取消</div>
			</div>
		</es-dialog>
	</div>
</template>

<script>
import modelFieldUrl from '@/http/platform/modelfield.js';
import componentList from './component';
const statusDic = [
	{
		value: 1,
		name: '启用'
	},
	{
		value: 0,
		name: '禁用'
	}
];
const typeDic = [
	{
		value: 'info',
		name: '文章模型'
	},
	{
		value: 'node',
		name: '目录模型'
	}
];
export default {
	data() {
		var validateSortIndex = (rule, value, callback) => {
			let isletter2 = /^[1-9]\d*|0$/.test(value);
			if (!isletter2) {
				callback(new Error('排序数字只能为非负整数'));
			} else {
				callback();
			}
		};
		return {
			dataTableUrl: modelFieldUrl.page,
			treeData: [],
			fixedId: '',
			showForm: false,
			showDelete: false,
			formData: {},
			formTitle: '编辑',
			formItemList: [
				{
					label: '字段名称',
					name: 'name',
					placeholder: '字段名称',
					rules: {
						required: true,
						message: '请输入字段名称',
						trigger: 'blur'
					},
					col: 5
				},
				{
					label: '字段编码',
					name: 'code',
					placeholder: '请输入字段编码',
					event: 'input',
					rules: {
						required: true,
						message: '请输入模型编码',
						trigger: 'blur'
					},
					col: 5
				},
				{
					type: 'select',
					label: '类型',
					name: 'inputType',
					data: componentList,
					rules: {
						required: true,
						message: '请选择类型',
						trigger: 'blur'
					},
					col: 5
				},
				{
					type: 'select',
					label: '内置类型',
					name: 'sysType',
					'label-key': 'shortName',
					'value-key': 'cciValue',
					sysCode: 'cms_model_sys_type',
					rules: {
						required: true,
						message: '请选择内置类型',
						trigger: 'blur'
					},
					col: 5
				},
				{
					label: '输入提示',
					name: 'tips',
					rules: [
						{
							required: false,
							message: '请输入输入提示',
							trigger: 'blur'
						}
					],
					col: 10
				},
				{
					label: '验证表达式',
					name: 'validates',
					rules: [
						{
							required: false,
							message: '请输入验证表达式',
							trigger: 'blur'
						}
					],
					col: 10
				},
				{
					label: '默认数值',
					name: 'defaultValue',
					rules: [
						{
							required: false,
							message: '请输入默认值',
							trigger: 'blur'
						}
					],
					col: 10
				},
				{
					label: '是否必填',
					name: 'requireSet',
					type: 'radio',
					data: [
						{ label: '是', value: true },
						{ label: '否', value: false }
					],
					rules: [
						{
							required: true,
							message: '请选择是否必填',
							trigger: 'blur'
						}
					],
					col: 5
				},
				{
					label: '是否多值',
					name: 'multiple',
					type: 'radio',
					data: [
						{ label: '是', value: true },
						{ label: '否', value: false }
					],
					rules: [
						{
							required: true,
							message: '请输入是否多值',
							trigger: 'blur'
						}
					],
					col: 5
				},
				{
					label: '排列样式',
					name: 'colspan',
					type: 'radio',
					'label-key': 'shortName',
					'value-key': 'cciValue',
					sysCode: 'cms_model_span_type',
					rules: [
						{
							required: false,
							message: '请选择排列样式',
							trigger: 'blur'
						}
					],
					col: 5
				},
				{
					label: '排序',
					name: 'sortIndex',
					type: 'number',
					minRows: 1,
					rules: [
						{
							required: true,
							validator: validateSortIndex,
							trigger: 'blur'
						}
					],
					col: 5
				},
				{
					label: '状态',
					name: 'status',
					type: 'radio',
					'label-key': 'shortName',
					'value-key': 'cciValue',
					sysCode: 'cms_enable_status',
					rules: [
						{
							required: true,
							message: '请选择状态',
							trigger: 'blur'
						}
					],
					col: 5
				}
			],
			editBtn: {
				type: 'submit',
				skin: 'lay-form-btns',
				contents: [
					{
						type: 'primary',
						text: '确定',
						event: 'confirm'
					},
					{
						type: 'reset',
						text: '取消',
						event: 'cancel'
					}
				]
			},
			cancelBtn: {
				type: 'submit',
				skin: 'lay-form-btns',
				contents: [
					{
						type: 'reset',
						text: '取消',
						event: 'cancel'
					}
				]
			},
			tableCount: 1,
			search: {},
			toolbar: [
				{
					type: 'button',
					contents: [
						{
							text: '返回',
							code: 'cancel',
							type: 'primary'
						},
						{
							text: '新增',
							code: 'add',
							type: 'primary'
						}
					]
				}
			],
			thead: [
				{
					title: '字段名称',
					align: 'center',
					field: 'name'
				},
				{
					title: '字段编码',
					align: 'center',
					field: 'code'
				},
				{
					title: '类型',
					align: 'center',
					field: 'inputType'
				},
				{
					title: '操作',
					type: 'handle',
					width: 180,
					template: '',
					events: [
						{
							code: 'edit',
							text: '编辑'
						},
						{
							code: 'view',
							text: '查看'
						},
						{
							code: 'delete',
							text: '删除'
						}
					]
				}
			],
			optionData: {},
			pageOption: {
				layout: 'total, prev, pager, next, jumper',
				pageSize: 10,
				// hideOnSinglePage: true,
				position: 'right',
				current: 1,
				pageNum: 1
			},
			params: {
				modelId: this.$route.query.modelId
			},
			modelId: null,
			modelType: null
		};
	},
	computed: {},
	watch: {
		dataTableUrl() {
			this.tableCount++;
		}
	},
	created() {
		this.modelId = this.$route.query.modelId;
		this.modelType = this.$route.query.modelType;
	},
	mounted() {},
	methods: {
		/**
		 * 表格行高
		 */
		tableRowClassName({ row, rowIndex }) {
			let styleRes = {
				height: '54px !important'
			};
			return styleRes;
		},
		inputChange(key, value) {},
		hadeSubmit(data) {},
		/**
		 * 操作按钮点击事件
		 * @param {*} res
		 */
		btnClick(res) {
			let code = res.handle.code;
			switch (code) {
				case 'cancel':
					// 返回
					this.$router.back();
					break;
				case 'add':
					// 新增
					this.formTitle = '新增';
					this.editModule(this.formItemList);
					this.formData = {};
					this.showForm = true;
					break;
				case 'edit':
					// 编辑
					this.formTitle = '编辑';
					this.fixedId = res.row.id;
					this.editModule(this.formItemList);
					this.$request({
						url: modelFieldUrl.detail + '?id=' + res.row.id,
						method: 'GET'
					}).then(res => {
						if (res.rCode == 0) {
							this.formData = res.results;
							this.showForm = true;
						}
					});
					break;
				case 'view':
					this.readModule(this.formItemList);
					this.formTitle = '查看';
					this.$request({
						url: modelFieldUrl.detail + '?id=' + res.row.id,
						method: 'GET'
					}).then(res => {
						if (res.rCode == 0) {
							this.formData = res.results;
							this.showForm = true;
						}
					});
					break;
				case 'delete':
					this.deleteId = res.row.id;
					this.showDelete = true;
					break;
				default:
					break;
			}
		},
		handleClose() {},
		handleFormItemChange() {},
		handleFormSubmit(data) {
			let formData = data;
			console.log(formData);
			delete formData.extMap;
			let url = '';
			if (this.formTitle == '新增') {
				// 新增
				formData.modelId = this.modelId; // 捐赠者
				url = modelFieldUrl.add;
			} else {
				url = modelFieldUrl.update;
				formData.id = this.fixedId;
			}
			this.$request({
				url: url,
				data: formData,
				method: 'POST'
			}).then(res => {
				if (res.rCode == 0) {
					this.$message.success('操作成功');
					this.showForm = false;
					this.tableCount++;
					this.formData = {};
				} else {
					this.$message.error(res.msg);
				}
			});
		},
		deleteRow() {
			this.$request({
				url: modelUrl.remove,
				data: { ids: this.deleteId },
				method: 'POST'
			}).then(res => {
				if (res.rCode == 0) {
					this.$message.success('删除成功');
				} else {
					this.$message.error(res.msg);
				}
				this.showDelete = false;
				this.tableCount++;
			});
		},
		/**
		 * 排序变化事件
		 * @param {*} column
		 * @param {*} prop
		 * @param {*} order
		 */
		sortChange(column, prop, order) {
			if (column.order == 'ascending') {
				//升序
				this.params = {
					asc: 'true',
					orderBy: column.prop
				};
			} else if (column.order == 'descending') {
				//降序
				this.params = {
					asc: 'false',
					orderBy: column.prop
				};
			} else {
				//不排序
				this.params = {
					asc: 'false',
					orderBy: 'createTime'
				};
			}
			this.$refs.table.reload();
		},
		/**
		 * 编辑模式
		 */
		editModule(list) {
			for (var i in list) {
				var item = list[i];
				item.readonly = false;
			}
			list.push(this.editBtn);
		},
		/**
		 * 只读模式
		 */
		readModule(list) {
			for (var i in list) {
				var item = list[i];
				item.readonly = true;
			}
			list.push(this.cancelBtn);
		}
	}
};
</script>
<style scoped lang="scss">
@import '../../../assets/style/style.scss';

.content {
	display: flex;
	width: 100%;
	height: 100%;

	::v-deep .es-data-table {
		flex: 1;
		display: flex;
		flex-direction: column;

		.es-data-table-content {
			flex: 1;
			height: 0;
			display: flex;
			flex-direction: column;

			.el-table {
				flex: 1;
				// height: 100% !important;
			}

			.es-thead-border {
				.el-table__header {
					th {
						border-right: 1px solid #e1e1e1;
					}
				}
			}
		}
	}

	::v-deep .el-form-item__label {
		background: none;
		border: 0px solid #c2c2c2;
	}
}

.el-dialog__body {
	.btn-box {
		margin-top: 20px;
		display: flex;
		justify-content: flex-end;

		.btn {
			padding: 5px 10px;
			color: #666;
			border: 1px solid #eee;
			cursor: pointer;
			margin-right: 5px;
			border-radius: 4px;
			// &.theme {
			// 	background: $--color-primary;
			// 	color: #fff;
			// 	border-color: $--color-primary;
			// }
		}
	}
}
</style>
