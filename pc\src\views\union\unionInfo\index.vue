<template>
	<div class="content">
		<es-tree-group
			:tree="tree"
			@node-click="handleChange"
			:syncKeys="{id:'id', name:'name'}"
		></es-tree-group>
		<es-data-table
			ref="table"
			:row-style="tableRowClassName"
			:full="true"
			:fit="true"
			:thead="thead"
			:toolbar="toolbar"
			:border="true"
			:page="pageOption"
			:url="dataTableUrl"
			:numbers="true"
			:param="params"
			close
			form
			@btnClick="btnClick"
			@sort-change="sortChange"
			@submit="handleSubmit"
		></es-data-table>
		<es-dialog
			:title="formTitle"
			:visible.sync="showForm"
			width="60%"
			:close-on-click-modal="false"
			:destroy-on-close="true"
		>
      <es-form
        ref="form"
        :model="formData"
        :contents="formItemList"
        height="500px"
        :genre="2"
        collapse
        :readonly="readonly"
        @change="inputChange"
        @submit="handleFormSubmit"
        @reset="showForm = false"
      />
		</es-dialog>
		<es-dialog
			title="删除"
			:visible.sync="showDelete"
			width="20%"
			:close-on-click-modal="false"
			:middle="true"
			height="140px"
		>
			<div>确定要删除该条数据吗</div>
			<div class="btn-box">
				<div class="btn theme" @click="deleteRow">确定</div>
				<div class="btn" @click="showDelete = false">取消</div>
			</div>
		</es-dialog>
	</div>
</template>


<script>
import interfaceUrl from '@/http/union/unionInfo/api.js';
import SnowflakeId from 'snowflake-id';
// import { host } from '../../../../config/config';
export default {
	data() {
		return {
      // 是否为根节点
      isRoot: false,
      tree: {
        defaultExpandAll:true,
        showSearch: false,
        data: [],
        defaultProps: {
          children: 'children',
          label: 'name'
        }
      },
      parentTree: {
        defaultExpandAll:true,
        showSearch: false,
        data: [],
        defaultProps: {
          children: 'children',
          label: 'name'
        }
      },
			unionSelect: {
				label: '请选择上级工会',
				id: 'union-select'
			},
			unionSelectData: [],
      teacherSelectList: [],
			deleteId: '',
			dataTableUrl: interfaceUrl.cUserListJson,
			showForm: false,
			showRoleForm: false,
			enpList: [],
			roleList: [],
      collegeSelectData: [],
			showDelete: false,
			formData: {},
			clickNode: '',
      readonly: false,
			roleFormData: {
				userId: '',
				username: '',
				phone: '',
				selectedRoleList: [],
			},
			formTitle: '编辑',
			editBtn: {
				type: 'submit',
				skin: 'lay-form-btns',
				contents: [
					{
						type: 'primary',
						text: '确定',
						event: 'confirm'
					},
					{
						type: 'reset',
						text: '取消',
						event: 'cancel'
					}
				]
			},
			cancelBtn: {
				type: 'submit',
				skin: 'lay-form-btns',
				contents: [
					{
						type: 'reset',
						text: '取消',
						event: 'cancel'
					}
				]
			},
			toolbar: [
				// {
				// 	type: 'button',
				// 	contents: [
				// 		{
				// 			text: '新增',
				// 			code: 'add',
				// 			type: 'primary'
				// 		}
				// 	]
				// },
				{
					type: 'search',
					contents: [
						{
							type: 'text',
							name: 'keyword',
							placeholder: '关键字查询'
						}
					]
				}
			],
			thead: [
				{
					title: '工会名称',
					align: 'left',
					field: 'unionName',
					sortable: 'custom',
					showOverflowTooltip: true,
				},
				{
					title: '上级工会',
					align: 'left',
					field: 'parentName',
					sortable: 'custom',
					showOverflowTooltip: true,
				},
				{
					title: '工会代码',
					align: 'center',
					field: 'unionCode',
					sortable: 'custom',
					showOverflowTooltip: true,
				},
				{
					title: '工会主席',
					align: 'center',
					field: 'chairmanName',
					sortable: 'custom',
					showOverflowTooltip: true,
				},
				{
					title: '工会副主席',
					align: 'center',
					field: 'viceChairmanName',
					sortable: 'custom',
					showOverflowTooltip: true,
				},
				{
					title: '操作',
					type: 'handle',
					width: 180,
					template: '',
					events: [
						{
							code: 'edit',
							text: '编辑'
						},
						{
							code: 'view',
							text: '查看'
						},
						{
							code: 'delete',
							text: '删除'
						}
					]
				}
			],
			pageOption: {
				layout: 'total, prev, pager, next, jumper',
				pageSize: 10,
				// hideOnSinglePage: true,
				position: 'right',
				current: 1,
				pageNum: 1
			},
			params: {
				asc: 'true',
				orderBy: 'sortNum'
			}
		};
	},
	computed: {
		formItemList() {
			return [
				{
					label: '工会名称',
					name: 'unionName',
					placeholder: '请输入工会名称',
					event: 'multipled',
					rules: {
						required: true,
						message: '请输入工会名称',
						trigger: 'blur'
					},
					verify: 'required',
					col: 12
				},
				{
					label: '上级工会',
					name: 'parentId',
					placeholder: '请选择上级工会',
					type: 'select',
					tree: true,
					valueKey: 'id',
					labelKey: 'name',
					events: { change: this.unionSelectChange },
					rules: {
						required: true,
						message: '请选择上级工会',
						trigger: 'blur'
					},
					data: this.unionSelectData,
					valueType: 'string',
					verify: 'required',
          hide: this.isRoot,
					col: 6,
				},
        {
          label: '所属学院',
          name: 'collegeCode',
          placeholder: '请选择所属学院',
          type: 'select',
          tree: true,
          valueKey: 'value',
          labelKey: 'name',
          event: 'multipled',
          rules: {
            required: true,
            message: '请选择所属学院',
            trigger: 'blur'
          },
          data: this.collegeSelectData,
          valueType: 'string',
          verify: 'required',
          hide: this.isRoot,
          col: 6,
        },
				{
					label: '工会代码',
					name: 'unionCode',
					placeholder: '请输入工会代码',
					event: 'multipled',
					rules: {
						required: true,
						message: '请输入工会代码',
						trigger: 'blur'
					},
					verify: 'required',
					col: 6
				},
				{
					label: '工会主席',
					name: 'chairmanName',
          type: 'select',
          'value-type': 'object',
          clearable: true,
					placeholder: '请输入工会主席',
					event: 'multipled',
          data: this.teacherSelectList,
					rules: {
						required: true,
						message: '请输入工会主席',
						trigger: 'blur'
					},
					verify: 'required',
					col: 6
				},
				{
					label: '工会主席联系方式',
					name: 'chairmanPhone',
					placeholder: '请输入工会主席联系方式',
					event: 'multipled',
					rules: [
						{
							required: true,
							message: '请输入工会主席联系方式',
							trigger: 'blur'
						},
						{ pattern: /^1[3456789]\d{9}$/, message: '手机号码格式不正确', trigger: 'blur' },
					],
					verify: 'required',
					col: 6
				},
				{
					label: '工会副主席',
					name: 'viceChairmanName',
          type: 'select',
          'value-key': 'value',
          'label-key': 'label',
          'value-type': 'object',
          clearable: true,
					placeholder: '请输入工会副主席',
					event: 'multipled',
					rules: {
						required: true,
						message: '请输入工会副主席',
						trigger: 'blur'
					},
          data: this.teacherSelectList,
					verify: 'required',
					col: 6
				},
				{
					label: '工会副主席联系方式',
					name: 'viceChairmanPhone',
					placeholder: '请输入工会副主席联系方式',
					event: 'multipled',
					rules: [
						{
							required: true,
							message: '请输入工会副主席联系方式',
							trigger: 'blur'
						},
						{ pattern: /^1[3456789]\d{9}$/, message: '手机号码格式不正确', trigger: 'blur' },
					],
					verify: 'required',
					col: 6
				},
				{
					label: '排序',
					name: 'sortNum',
					placeholder: '请输入排序号',
					col: 6
				},
			];
		},
	},
	watch: {
    formData: {
      handler(){
        this.initTeacherSelectList();
      }
    }
  },
	created() {
		this.initTree();
    this.initCollegeTree();
	},
	mounted() {},
	methods: {
		/**
		 * 表格行高
		 */
		tableRowClassName({ row, rowIndex }) {
			let styleRes = {
				height: '54px !important'
			};
			return styleRes;
		},
		inputChange(key, value) {
			if (key == 'phone') {
			}
		},
		handleSubmit(data) {},

		/**
		 * 树点击事件
		 */
		handleChange(tree, data) {
			this.clickNode = data.id;
      this.$set(this.params, 'rootId', data.id);
		},
		handleNodeClick(data) {
			console.log(data);
		},

		
		/**
		 * 树点击事件
		 */
		 unionSelectChange(tree, data) {
			console.log(data);
		},

		/**
		 * 操作按钮点击事件
		 * @param {*} res
		 */
		btnClick(res) {
			let code = res.handle.code;
			switch (code) {
				case 'add':
					// 新增
					this.formTitle = '新增';
					this.editModule(this.formItemList, []);
					const snowflake = new SnowflakeId();
					let id = snowflake.generate();
					this.formData = { id: id, parentId: this.clickNode };
					this.showForm = true;
					break;
				case 'edit':
					// 编辑
					this.formTitle = '编辑';
					this.editModule(this.formItemList, ['pwd']);
          this.handleOpenDialog(res);
					break;
				case 'view':
					this.formTitle = '查看';
					this.readModule(this.formItemList, ['pwd']);
          this.handleOpenDialog(res);
					break;
				case 'setRole':
					this.showRoleForm = true;
					this.roleFormData.userId = res.row.id;
					this.roleFormData.username = res.row.username;
					this.roleFormData.phone = res.row.phone;
					// 获取企业选择列表
					this.$request({
						url: interfaceUrl.enpSelectList,
						method: 'GET',
						params: {
							userId: res.row.id,
						}
					}).then(res => {
						if (res.rCode == 0) {
							this.enpList = res.results;
						}
					});
					break;
				case 'delete':
					this.deleteId = res.row.id;
					this.showDelete = true;
					break;
				default:
					break;
			}
		},
		handleClose() {},
		handleFormItemChange() {},
    handleOpenDialog(res){
      this.$request({
        url: interfaceUrl.info + '/' + res.row.id,
        method: 'GET'
      }).then(res => {
        if (res.rCode === 0) {
          // 处理返回数据
          this.formData = res.results;
          // 根节点隐藏上级工会和所属学院
          this.isRoot = this.formData.id === 'root'

          this.showForm = true;
        }
      });
    },
		handleFormSubmit(data) {
      // 处理请求数据
			let formData = data;
      if(formData.chairmanName !== undefined && typeof formData.chairmanName == 'object') {
        formData.chairmanId = formData.chairmanName.value;
        formData.chairmanName = formData.chairmanName.label;
      }
      if(formData.viceChairmanName !== undefined && typeof formData.viceChairmanName == 'object') {
        formData.viceChairmanId = formData.viceChairmanName.value;
        formData.viceChairmanName = formData.viceChairmanName.label;
      }

			delete formData.extMap;
			let url = '';
			if (this.formTitle == '新增') {
				url = interfaceUrl.save;
				let Base64 = require('js-base64').Base64;
				formData['password'] = Base64.encode(formData.pwd);
			} else {
				url = interfaceUrl.update;
			}
			this.$request({
				url: url,
				data: formData,
				method: 'POST'
			}).then(res => {
				if (res.rCode == 0) {
					this.$message.success('操作成功');
					this.showForm = false;
					this.formData = {};
					this.$refs.table.reload();
					this.initTree();
				} else {
					this.formData.state = 1 == this.formData.state ? true : false;
					this.$message.error(res.msg);
				}
			});
		},
		deleteRow() {
			this.$request({
				url: interfaceUrl.deleteBatchIds,
				data: { ids: this.deleteId },
				method: 'POST'
			}).then(res => {
				if (res.rCode == 0) {
					this.$refs.table.reload();
					this.$message.success('删除成功');
				} else {
					this.$message.error(res.msg);
				}
				this.showDelete = false;
				this.initTree();
			});
		},
		/**
		 * 排序变化事件
		 * @param {*} column
		 * @param {*} prop
		 * @param {*} order
		 */
		sortChange(column, prop, order) {
			if (column.order == 'ascending') {
				//升序
				this.params = {
					asc: 'true',
					orderBy: column.prop
				};
			} else if (column.order == 'descending') {
				//降序
				this.params = {
					asc: 'false',
					orderBy: column.prop
				};
			} else {
				//不排序
				this.params = {
					asc: 'false',
					orderBy: 'createTime'
				};
			}
			this.$refs.table.reload();
		},
		/**
		 * 编辑模式
		 */
		editModule(list, hideField) {
      this.readonly = false;
			for (var i in list) {
				var item = list[i];
				if (undefined != hideField && hideField.length > 0 && hideField.indexOf(item.name) > -1) {
					item.hide = true;
				}
			}
		},
		/**
		 * 只读模式
		 */
		readModule(list, hideField) {
      this.readonly = true;
			for (var i in list) {
				var item = list[i];
				if (undefined != hideField && hideField.length > 0 && hideField.indexOf(item.name) > -1) {
					item.hide = true;
				}
			}
		},

		// 初始化左侧树数据
		initTree() {
			this.$request({
				url: interfaceUrl.cUserTreeList,
				method: 'POST'
			}).then(res => {
				if (res.rCode == 0) {
          this.tree.data = res.results.baseTree;
					this.unionSelectData = res.results.parentTree;
				}
			});
		},

    // 初始化学院选择树数据
    initCollegeTree() {
      this.$request({
        url: interfaceUrl.collegeTree,
        method: 'POST'
      }).then(res => {
        if (res.rCode == 0) {
          this.collegeSelectData = res.results;
        }
      });
    },

    //
    initTeacherSelectList(res) {
      let collegeCode = this.formData.collegeCode;
      this.$request({
        url: interfaceUrl.teacherSelectList,
        params: { xydm: collegeCode === 'root'? null:collegeCode }
      }).then(res => {
        // 处理自定义的数据格式化, extFiled 额外属性名， key、val 数据映射

        this.teacherSelectList = res.results;
        console.log(this.teacherSelectList);
      });
    }
	}
};
</script>
<style scoped lang="scss">
@import '../../../assets/style/style.scss';

.content {
	display: flex;
	width: 100%;
	height: 100%;

	::v-deep .es-data-table {
		flex: 1;
		display: flex;
		flex-direction: column;

		.es-data-table-content {
			flex: 1;
			height: 0;
			display: flex;
			flex-direction: column;

			.el-table {
				flex: 1;
				// height: 100% !important;
			}

			.es-thead-border {
				.el-table__header {
					th {
						border-right: 1px solid #e1e1e1;
					}
				}
			}
		}
	}

	::v-deep .el-form-item__label {
		background: none;
		border: 0px solid #c2c2c2;
	}
}

.el-dialog__body {
	.btn-box {
		margin-top: 20px;
		display: flex;
		justify-content: flex-end;

		.btn {
			padding: 5px 10px;
			color: #666;
			border: 1px solid #eee;
			cursor: pointer;
			margin-right: 5px;
			border-radius: 4px;
			// &.theme {
			// 	background: $--color-primary;
			// 	color: #fff;
			// 	border-color: $--color-primary;
			// }
		}
	}
}
</style>
