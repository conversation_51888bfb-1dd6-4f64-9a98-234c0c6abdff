<template>
	<div class="sketch_content">
		<el-form ref="form" :model="formData" class="resumeTable" :rules="rules">
			<el-row>
				<el-col :span="22">
					<el-form-item label="分类名称" label-width="90px" label-position="left" prop="name">
						<el-input v-model="formData.name" :disabled="infoDisabled"></el-input>
					</el-form-item>
				</el-col>
			</el-row>
			<el-row>
				<el-col :span="22">
					<el-form-item label="创业/就业" label-width="90px" label-position="left" prop="name">
						<el-select v-model="formData.type" :disabled="infoDisabled">
							<el-option value="0" label="创业"></el-option>
							<el-option value="1" label="就业"></el-option>
						</el-select>
					</el-form-item>
				</el-col>
			</el-row>
			<el-row>
				<el-col :span="22">
					<el-form-item label="简介" label-width="90px" label-position="left" prop="introduction">
						<el-input
							v-model="formData.introduction"
							:disabled="infoDisabled"
							:autosize="{ maxRows: 6, minRows: 6 }"
							type="textarea"
						></el-input>
					</el-form-item>
				</el-col>
			</el-row>
			<el-row>
				<el-col :span="11">
					<el-form-item label="图标" label-width="90px" label-position="left" prop="icon">
						<es-upload
							v-bind="iconAttrs"
							:on-success="uploadIconSuccess"
							:disabled="infoDisabled"
						></es-upload>
					</el-form-item>
				</el-col>
				<el-col :span="11">
					<el-form-item label="封面" label-width="90px" label-position="left" prop="cover">
						<es-upload
							v-bind="coverAttrs"
							:on-success="uploadCoverSuccess"
							:disabled="infoDisabled"
						></es-upload>
					</el-form-item>
				</el-col>
			</el-row>
			<el-row>
				<el-col :span="6">
					<el-form-item label="状态" label-width="90px" label-position="left" prop="status">
						<el-switch
							v-model="formData.status"
							class="switchStyle"
							:disabled="infoDisabled"
							active-text="启用"
							inactive-text="禁用"
						></el-switch>
					</el-form-item>
				</el-col>
			</el-row>
			<el-row><br /></el-row>
			<el-row>
				<el-col :span="3" style="float: right">
					<el-button type="reset" @click="infoPageClose(false)">取消</el-button>
				</el-col>
				<el-col v-show="!infoDisabled" :span="3" style="float: right">
					<el-button type="primary" @click="handleFormSubmit">保存</el-button>
				</el-col>
				<el-col v-show="auditBtnVisible" :span="3" style="float: right">
					<el-button type="danger" @click="handleFormAudit('驳回')">驳回</el-button>
				</el-col>
				<el-col v-show="auditBtnVisible" :span="4" style="float: right">
					<el-button type="primary" @click="handleFormAudit('审核通过')">审核通过</el-button>
				</el-col>
			</el-row>
		</el-form>
	</div>
</template>
<script>
import api from '@/http/job/jobServiceType/api';

export default {
	name: 'InfoPage',
	props: {
		baseData: {
			type: Object
		},
		infoPageMode: {
			type: String
		}
	},
	data() {
		return {
			infoDisabled: false,
			auditDisabled: false,
			auditVisible: true,
			auditBtnVisible: false,
			formData: {},
			pageMode: 'allOn',
			startupServices: [],
			rules: {
				typeId: { required: true, message: '请选择服务类型', trigger: 'blur' }
			}
		};
	},
	computed: {
		coverAttrs() {
			return {
				ownId: this.formData.id,
				portrait: true,
				code: 'job_co_startup_cover',
				preview: true,
				download: true,
				operate: true
			};
		},
		iconAttrs() {
			return {
				ownId: this.formData.id,
				portrait: true,
				code: 'job_co_startup_icon',
				preview: true,
				download: true
			};
		}
	},
	watch: {},
	created() {
		this.formData = { ...this.baseData };
		this.pageMode = this.infoPageMode;
		switch (this.pageMode) {
			case '审核':
				this.infoDisabled = true;
				this.auditDisabled = false;
				this.auditVisible = true;
				this.auditBtnVisible = true;
				break;
			case 'allOn':
			case '新增':
			case '编辑':
				this.infoDisabled = false;
				this.auditDisabled = true;
				this.auditVisible = false;
				this.auditBtnVisible = false;
				break;
			case '查看':
				this.infoDisabled = true;
				this.auditDisabled = true;
				this.auditVisible = true;
				this.auditBtnVisible = false;
				break;
		}
	},
	methods: {
		uploadIconSuccess(response, file, fileList) {
			this.$set(this.formData, 'icon', file.response.adjunctId);
			debugger;
		},
		uploadCoverSuccess(response, file, fileList) {
			this.$set(this.formData, 'cover', file.response.adjunctId);
		},
		handleFormSubmit() {
			this.$refs.form.validate(valid => {
				if (valid) {
					//处理请求数据
					let saveData = { ...this.formData };
					debugger;
					saveData.status = saveData.status === true ? 1 : 0;
					if (typeof saveData.typeId == 'object') saveData.typeId = saveData.typeId.value;
					// if (typeof saveData.iconUrl == 'object')
					// 	saveData.icon = saveData.iconUrl.response.adjunctId;
					// this.$delete(saveData, 'iconUrl');
					// if (typeof saveData.coverUrl == 'object')
					// 	saveData.cover = saveData.coverUrl.response.adjunctId;
					// this.$delete(saveData, 'coverUrl');

					this.$request({
						url: this.pageMode === '新增' ? api.jobServiceTypeSave : api.jobServiceTypeUpdate,
						data: saveData,
						method: 'POST'
					}).then(res => {
						if (res.success) {
							this.$message.success('操作成功');
							this.infoPageClose(true);
						} else {
							this.$message.error(res.msg);
						}
					});
				}
			});
		},
		handleFormAudit(res) {
			//校验
			this.$refs['form'].validate(valid => {
				//开启校验
				if (valid) {
					// 如果校验通过，请求接口
					let auditFormData = {};
					auditFormData.id = this.formData.id;
					auditFormData.auditStatus = -1;
					auditFormData.auditOpinion = this.formData.auditOpinion;
					if (typeof this.formData.typeId == 'object')
						auditFormData.typeId = this.formData.typeId.value;
					else auditFormData.typeId = this.formData.typeId;
					let btnType = res;

					this.$confirm('是否确认' + btnType + '？', '审核', {
						confirmButtonText: '确定',
						cancelButtonText: '取消',
						type: 'warning'
					})
						.then(() => {
							switch (btnType) {
								case '审核通过':
									auditFormData.auditStatus = 1;
									break;
								case '驳回':
									auditFormData.auditStatus = 2;
									break;
							}
							if (auditFormData.auditStatus !== -1) {
								this.$request({
									url: api.jobServiceTypeAudit,
									data: auditFormData,
									method: 'POST'
								}).then(response => {
									if (response.success) {
										this.$message.success('审核成功');
										this.infoPageClose(true);
									} else {
										this.$message.error(response.msg);
									}
								});
							}
						})
						.catch(() => {});
				} else {
					return false;
				} //校验不通过
			});
		},
		infoPageClose(reload) {
			this.pageMode = 'allOn';
			this.infoDisabled = false;
			this.$emit('activelyClose', reload);
		}
	}
};
</script>
<style scoped>
.sketch_content {
	overflow: auto;
	height: auto;
	border-top: 1px solid #eff1f4;
	border-bottom: 1px solid #eff1f4;
	padding: 0 30px 11px 27px;
}
.resumeTable td {
	height: 50px;
	font-weight: bold;
}
::v-deep .switchStyle .el-switch__label {
	position: absolute;
	display: none;
	color: #fff;
}
::v-deep .el-switch__core {
	background-color: rgba(166, 166, 166, 1);
}
::v-deep .switchStyle .el-switch__label--left {
	z-index: 9;
	left: 20px;
}
::v-deep .switchStyle .el-switch__label.is-active {
	display: block;
}
::v-deep .switchStyle.el-switch .el-switch__core,
.el-switch .el-switch__label {
	width: 60px !important;
}
</style>
