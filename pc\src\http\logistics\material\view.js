export default {
	computed: {
		viewItemList() {
			return [
				{
					label: '原料名称',
					name: 'name',
					placeholder: '请输入原料名称',
					event: 'multipled',
					rules: {
						required: true,
						message: '请输入原料名称',
						trigger: 'blur'
					},
					verify: 'required',
					col: 6
				},
				{
					label: '原料分类',
					type: 'select',
					name: 'categoryName',
					placeholder: '请选择原料分类',
					event: 'multipled',
					rules: {
						required: true,
						message: '请选择原料分类',
						trigger: 'blur'
					},
					data: this.typeList,
					'value-key': 'id',
					'label-key': 'name',
					verify: 'required',
					col: 6
				},
				{
					label: '原料编号',
					name: 'code',
					placeholder: '请输入原料编号',
					event: 'multipled',
					rules: {
						required: true,
						message: '请输入原料编号',
						trigger: 'blur'
					},
					verify: 'required',
					col: 6
				},
				{
					label: '库存单位',
					name: 'unit',
					placeholder: '请输入库存单位',
					event: 'multipled',
					rules: {
						required: true,
						message: '请输入库存单位',
						trigger: 'blur'
					},
					verify: 'required',
					col: 6,
					type: 'select',
					sysCode: 'hq_material_unit'
				},
				{
					label: '品牌',
					name: 'brand',
					placeholder: '请输入品牌',
					event: 'multipled',
					rules: {
						required: true,
						message: '请输入品牌',
						trigger: 'blur'
					},
					verify: 'required',
					col: 6
				},
				{
					label: '规格',
					name: 'specification',
					placeholder: '请输入规格',
					event: 'multipled',
					rules: {
						required: true,
						message: '请输入规格',
						trigger: 'blur'
					},
					verify: 'required',
					col: 6
				},
				{
					label: '能量',
					name: 'energy',
					placeholder: '请输入能量',
					event: 'multipled',
					rules: {
						required: false,
						message: '请输入能量',
						trigger: 'blur'
					},
					verify: 'required',
					col: 6
				},
				{
					label: '蛋白质',
					name: 'protein',
					placeholder: '请输入蛋白质',
					event: 'multipled',
					rules: {
						required: false,
						message: '请输入蛋白质',
						trigger: 'blur'
					},
					verify: 'required',
					col: 6
				},
				{
					label: '脂肪',
					name: 'fat',
					placeholder: '请输入脂肪',
					event: 'multipled',
					rules: {
						required: false,
						message: '请输入脂肪',
						trigger: 'blur'
					},
					verify: 'required',
					col: 6
				},
				{
					label: '碳水化合物',
					name: 'carbohydrate',
					placeholder: '请输入碳水化合物',
					event: 'multipled',
					rules: {
						required: false,
						message: '请输入碳水化合物',
						trigger: 'blur'
					},
					verify: 'required',
					col: 6
				},
				{
					label: '钠',
					name: 'sodium',
					placeholder: '请输入钠',
					event: 'multipled',
					rules: {
						required: false,
						message: '请输入钠',
						trigger: 'blur'
					},
					verify: 'required',
					col: 6
				}
			];
		}
	}
};
