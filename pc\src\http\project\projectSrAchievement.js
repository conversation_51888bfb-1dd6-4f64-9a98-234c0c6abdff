/**
 * 用户相关接口
 */
const interfaceUrl = {
	listJson: '/ybzy/projectSrAchievement/commonListJson', // 列表
	info: '/ybzy/projectSrAchievement/info', // 获取
	listJsonKyccRecords: '/ybzy/projectkycgsubmitrecords/listJsonRecords', // 科研成果未提交记录列表
	listJsonSubmitRecords: '/ybzy/projectkycgsubmitrecords/listJson', // 科研成果已提交记录列表
	submitr: '/ybzy/projectkycgsubmitrecords/save', // 科研成果批量提交到科技处
	getrecordById: '/ybzy/projectkycgsubmitrecords', // 获取
	updateRecordById: '/ybzy/projectkycgsubmitrecords/update', // 更新科研成果提交记录
	deleteRecordById: '/ybzy/projectkycgsubmitrecords/deleteById', // 删除科研成果提交记录
	listJsonDetails: '/ybzy/projectkycgsubmitrecordsdetail/submitDetailListJson', // 已提交的记录明细
};
export default interfaceUrl;

