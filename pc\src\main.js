import 'babel-polyfill';
import 'classlist-polyfill';
import * as echarts from 'echarts';
// import elementUi from 'element-ui';
// import 'element-ui/lib/theme-chalk/index.css';
import Element from 'eoss-element';
// import EsProcess from 'eoss-process';
import EsUi from 'eoss-ui';
import request from 'eoss-ui/lib/utils/http';
import request2 from '@/utils/request.js';
import requestRs from '@/utils/request-rs.js';
import Vue from 'vue';
import App from './App.vue';
import './assets/style/theme.scss';
import router from './router';
import store from './store';

import axios from 'axios';
import WujieVue from 'wujie-vue2';
import AVUE from '../public/avue/2.8.2/avue.min';
import basicContainer from './views/platform/cms/basic-container/main';
import BaiduMap from 'vue-baidu-map';
import Wangeditor from '@/components/wangeditor/wangeditor';
import DictRender from '@/components/dict-render';
// import ScwlComponent from 'scwl-component';
// import 'scwl-component/lib/theme-chalk/index.css'; // 业务组件样式
import Empty from '@/components/empty.vue';

import util from 'eoss-ui/src/utils/util';
import * as utils from '@/utils/index.js';
import vCalendar from 'v-calendar';
import { host } from '../config/config';
import { v4 as uuidv4 } from 'uuid';
Vue.use(vCalendar);
Vue.prototype.$ = util;
Vue.prototype.$utils = utils;
Vue.prototype.$host = host; // 当前环境
Vue.prototype.$uuidv4 = uuidv4; // 随机获取id
// Vue.use(elementUi);
Vue.use(WujieVue);
Element.Dialog.props.closeOnClickModal.default = false; // 禁止点击遮罩关闭
Vue.use(Element);
Vue.use(EsUi);
// Vue.use(EsProcess);
Vue.component('BasicContainer', basicContainer);
Vue.component('Wangeditor', Wangeditor);
Vue.component('DictRender', DictRender);
Vue.component('Empty', Empty);
// Vue.use(ScwlComponent);

window.axios = axios;
Vue.use(AVUE, { axios });
Vue.use(BaiduMap, {
	ak: 'igSaXGgHOGCAl1zFiVu0QXZ0FVthiL2z'
});
//封装的请求函数绑定到vue
Vue.prototype.$bus = new Vue();
Vue.prototype.$request = request;
Vue.prototype.$request2 = request2;
Vue.prototype.$requestRs = requestRs;
Vue.prototype.$echarts = echarts;
if (window.__POWERED_BY_WUJIE__) {
	let instance;
	window.__WUJIE_MOUNT = () => {
		instance = new Vue({ router, store, render: h => h(App) }).$mount('#app');
	};
	window.__WUJIE_UNMOUNT = () => {
		instance.$destroy();
	};
} else {
	new Vue({ router, store, render: h => h(App) }).$mount('#app');
}

Vue.use({
	install(Vue) {
		Vue.prototype.CommonFunc = {
			reader(data, val) {
				let title = val;
				if (data) {
					for (var i = 0; i < data.length; i++) {
						var item = data[i];
						if (item.value == val) {
							title = item.label;
							return title;
						}
					}
				}
				return title;
			}
		};
	}
});
