<template>
  <div class="content">
    <es-data-table
      ref="table"
      :full="true"
      :fit="true"
      :thead="thead"
      :toolbar="toolbar"
      :border="true"
      :page="pageOption"
      :url="dataTableUrl"
      :numbers="true"
      :param="params"
      close
      form
      @btnClick="btnClick"
    ></es-data-table>

    <!-- 回复 -->
    <es-dialog
      :title="formTitle"
      :visible.sync="showForm"
      width="670px"
      height="400px"
      :drag="false"
      :close-on-click-modal="false"
      :destroy-on-close="true"
    >
      <es-form
        v-if="showForm"
        ref="form"
        :model="formData"
        :contents="formItemList"
        height="290px"
        :genre="2"
        collapse
        @submit="handleFormSubmit"
        :submit="formTitle === '回复' ? true : false"
        @reset="showForm = false"
      />
    </es-dialog>

    <!-- 查看提交列表 -->
    <es-dialog
      title="查看明细"
      :visible.sync="showView"
      width="1100px"
      height="630px"
      :drag="false"
      :close-on-click-modal="false"
      :destroy-on-close="true"
    >
      <es-data-table
        v-if="showView"
        :toolbar="details.toolbar"
        :thead="details.thead"
        :data="details.data"
        :border="true"
        @btnClick="btnClick"
      ></es-data-table>
    </es-dialog>

    <es-dialog
      title="查看"
      :visible.sync="visibleBasicInfo"
      :show-scale="false"
      size="full"
      height="100%"
    >
      <BasicInfo
        v-if="visibleBasicInfo"
        :id="formId"
        :contents-key="contentsKey"
        title="查看"
        @visible="visibleBasicInfo = false"
      />
    </es-dialog>
  </div>
</template>

<script>
import baseReplyApi from "@/http/achievement/baseReply";
import BasicInfo from '@/views/scientific-sys/components/achievement-info/basic-info';

export default {
  name: "scientificPayoffsReply",
	components: { BasicInfo },
  computed: {
    submitF() {
      if (this.formTitle === '回复') {
        return this.handleFormSubmit()
      } else {
        return false
      }
    },
    formItemList() {
      if (this.formTitle === '查看回复') {
        return [
          {
            label: '回复人',
            name: 'replyUserName',
            placeholder: '无',
            readonly: true,
            col: 12
          },
          {
            label: '回复时间',
            name: 'replyTime',
            placeholder: '无',
            readonly: true,
            col: 12
          },
          {
            type: 'textarea',
            autosize: { minRows: 2, maxRows: 10 },
            label: '回复内容',
            name: 'replyRemark',
            placeholder: '无',
            readonly: true,
            col: 12
          }
        ]
      } else {
        return [
          {
            type: 'textarea',
            autosize: { minRows: 2, maxRows: 10 },
            label: '回复内容',
            name: 'replyRemark',
            placeholder: '请输入回复内容',
            rules: {
              required: true,
              message: '请输入回复内容',
              trigger: 'blur'
            },
            col: 12
          }
        ]
      }
    }
  },
  data(){
    return {
      dataTableUrl: baseReplyApi.listJson,
      toolbar: [
        {
          type: 'search',
          reset: true,
          contents: [
            {
              type: 'text',
              label: '单位名称',
              name: 'orgName',
              placeholder: '请输入单位名称',
              col: 3
            },
            {
              type: 'select',
              label: '回复状态',
              name: 'status',
              placeholder: '请选择',
              clearable: true,
              sysCode: 'pa_base_reply_status',
              'value-key': 'cciValue',
              'label-key': 'shortName',
              col: 3
            }
          ]
        }
      ],
      thead: [
        {
          title: '单位名称',
          field: 'orgName',
          align: 'center'
        },
        {
          title: '操作人',
          field: 'createUserName',
          align: 'center'
        },
        {
          title: '操作时间',
          field: 'createTime',
          align: 'center',
        },
        {
          title: '明细数量',
          field: 'achievementIds',
          align: 'center',
          render: (h, { row }) => {
            return h('span', null, row.achievementIds ? row.achievementIds.split(',').length : '0');
          }
        },
        {
          title: '回复状态',
          field: 'statusTxt',
          align: 'center',
        },
        {
          title: '回复人',
          field: 'replyUserName',
          align: 'center',
        },
        {
          title: '回复时间',
          field: 'replyTime',
          align: 'center',
        },
        {
          title: '操作',
          type: 'handle',
          width: 140,
          template: '',
          events: [
            {
              code: 'view',
              text: '查看明细'
            },
            {
              code: 'revert',
              text: '回复',
              rules: rows => {
                return rows.status === '1';
              }
            },
            {
              code: 'revertDetail',
              text: '查看回复',
              rules: rows => {
                return rows.status === '2';
              }
            }
          ]
        }
      ],
      pageOption: {
        layout: 'total, sizes, prev, pager, next, jumper',
        pageSize: 10,
        position: 'center',
        current: 1,
        pageNum: 1
      },
      params: {
        orderBy: 't1.create_time',
        asc: 'false'
      },
      formTitle: '',
      formData: {},
      showForm: false,
      showView: false,
      details: {
        toolbar: [
          {
            type: 'button',
            contents: [
              {
              text: '导出',
              code: 'itemExport',
              type: 'primary'
              }
            ]
          }
        ],
        thead: [
          {
            title: '标题',
            field: 'name',
            align: 'center',
          },
          {
            title: '成果类型',
            field: 'typeTxt',
            align: 'center',
          },
          {
            title: '申报人',
            field: 'createUserName',
            align: 'center',
          },
          {
            title: '申报时间',
            field: 'createTime',
            align: 'center',
          },
          {
            title: '操作',
            type: 'handle',
            width: 100,
            template: '',
            events: [
              {
                code: 'viewInfo',
                text: '查看详情'
              },

            ]
          }
        ],
        data: []
      },
      visibleBasicInfo: false,
      formId: '',
      contentsKey: '',
      exportId: ''
    }
  },
  methods: {
    btnClick(res){
      let code = res.handle.code;
      switch (code) {
        case 'revert':
          this.formTitle = '回复';
          this.formData = Object.assign({}, {
            id: res.row.id,
            replyRemark: undefined
          });
          this.showForm = true;
          break;
        case 'revertDetail':
          this.formTitle = '查看回复';
          this.formData = Object.assign({}, {
            replyUserName: res.row.replyUserName,
            replyRemark: res.row.replyRemark,
            replyTime: res.row.replyTime
          });
          this.showForm = true;
          break;
        case 'view':
          this.getViewById(res.row.id);
          break;
        case 'viewInfo':
          if (res.row.type === 'academicPaper') {
            this.contentsKey = 'academicPaper'
          } else if (res.row.type === 'academicPatent') {
            this.contentsKey = 'academicPatent'
          } else if (res.row.type === 'academicWriting') {
            this.contentsKey = 'academicBook'
          } else if (res.row.type === 'awards') {
            this.contentsKey = 'awardAchievement'
          } else if (res.row.type === 'platformTeam') {
            this.contentsKey = 'platformTeam'
          } else if (res.row.type === 'scienceAchievement') {
            this.contentsKey = 'scientificConversionAchievement'
          } else if (res.row.type === 'softwareCopyright') {
            this.contentsKey = 'softwareAchievement'
          } else if (res.row.type === 'technicalProducts') {
            this.contentsKey = 'technicalProduct'
          } else if (res.row.type === 'technicalStandard') {
            this.contentsKey = 'technicalStandard'
          } else {
            this.contentsKey = ''
          }
          if (!this.contentsKey) {
            return;
          }
          this.formId = res.row.id;
          this.visibleBasicInfo = true;
          break;
        case 'itemExport':
          this.itemExportFile();
        break;
        default:
          break;
      }
    },
    //导出函数
    itemExportFile(id) {
      if (!this.exportId) {
        return;
      }
      let isDev = process.env.NODE_ENV === 'development' ? '/api' : '';
      let url = `${isDev}${baseReplyApi.exportItemData}?exportId=${this.exportId}`;
      window.open(url);
    },
    /**
     * 查看弹窗
     */
    getViewById(id){
      this.exportId = id;
      this.$request({
        url: baseReplyApi.info,
        method: 'get',
        params: { id }
      }).then(({ results }) => {
        this.details.data = results?.baseInfoVOList || [];
        this.showView = true;
      });
    },
    /**
     * form提交 回复内容提交后台
     */
    handleFormSubmit(){
      const loading = this.$loading();
      this.$request({
        url: baseReplyApi.deal,
        data: this.formData,
        method: 'POST',
        format: false
      }).then(res => {
        loading.close();
        this.showForm = false;
        if (res.rCode == 0) {
          this.$message.success('回复成功');
          this.$refs.table.reload();
        } else {
          this.$message.error(res.msg);
        }
      });
    }
  }
}
</script>

<style scoped lang="scss">
@import '../../../assets/style/style.scss';
.el-row,
.el-col,
.table-box {
  height: 100%;
  width: 100%;
}
.content {
  width: 100%;
  height: 100%;
}

.el-dialog__body {
  .btn-box {
    margin-top: 20px;
    display: flex;
    justify-content: flex-end;

    .btn {
      padding: 5px 10px;
      color: #666;
      border: 1px solid #eee;
      cursor: pointer;
      margin-right: 5px;
      border-radius: 4px;
      // &.theme {
      // 	background: $--color-primary;
      // 	color: #fff;
      // 	border-color: $--color-primary;
      // }
    }
  }
}
.el-dialog__body {
  .content-warning-Btn {
    float: right;
    margin: 10px 0px;
  }
  .btn-box {
    margin-top: 20px;
    display: flex;
    justify-content: flex-end;

    .btn {
      padding: 5px 10px;
      color: #666;
      border: 1px solid #eee;
      cursor: pointer;
      margin-right: 5px;
      border-radius: 4px;
    }
  }
}
</style>