<template>
	<div class="content">
		<div style="width: 100%">
			<el-menu
				:default-active="activeMenus"
				mode="horizontal"
				class="es-menu"
				@select="handleSelect"
			>
				<el-menu-item v-for="item in menus" :key="item.key" :index="item.key">
					{{ item.label }}
				</el-menu-item>
			</el-menu>
			<es-data-table
				v-if="activeMenus === '0'"
				ref="table"
				:key="tableCount"
				:row-style="tableRowClassName"
				:full="true"
				:fit="true"
				:thead="thead"
				:toolbar="toolbar"
				:border="true"
				:page="pageOption"
				:url="dataTableUrl"
				:numbers="true"
				:param="params"
				close
				@btnClick="btnClick"
				@sort-change="sortChange"
			></es-data-table>
			<es-data-table
				v-if="activeMenus === '1'"
				ref="table"
				:key="tableCount"
				:row-style="tableRowClassName"
				:full="true"
				:fit="true"
				:thead="thead"
				:toolbar="toolbar"
				:border="true"
				:page="pageOption"
				:url="dataTableUrl"
				:numbers="true"
				:param="params"
				close
				@btnClick="btnClick"
				@sort-change="sortChange"
			></es-data-table>
			<es-data-table
				v-if="activeMenus === '2'"
				ref="table"
				:key="tableCount"
				:row-style="tableRowClassName"
				:full="true"
				:fit="true"
				:thead="thead"
				:toolbar="toolbar"
				:border="true"
				:page="pageOption"
				:url="dataTableUrl"
				:numbers="true"
				:param="params"
				close
				@btnClick="btnClick"
				@sort-change="sortChange"
			></es-data-table>
			<es-dialog
				v-if="showForm"
				:title="formTitle"
				:visible.sync="showForm"
				width="80%"
				height="80%"
				:close-on-click-modal="false"
				:destroy-on-close="true"
			>
				<es-form
					ref="form"
					:model="formData"
					:contents="formItemList"
					height="100%"
					:genre="2"
					collapse
					@change="inputChange"
					@submit="handleFormSubmit"
					@reset="showForm = false"
				/>
			</es-dialog>
			<es-dialog
				title="删除"
				:visible.sync="showDelete"
				width="20%"
				:close-on-click-modal="false"
				:middle="true"
				height="140px"
			>
				<div>确定要删除该条数据吗</div>
				<div class="btn-box">
					<div class="btn theme" @click="deleteRow">确定</div>
					<div class="btn" @click="showDelete = false">取消</div>
				</div>
			</es-dialog>
		</div>
	</div>
</template>

<script>
import interfaceUrl from '@/http/alumna/album.js';
import { fileAccess } from '@/../config/config';
import SnowflakeId from 'snowflake-id';

export default {
	data() {
		return {
			menus: [
				{ key: '0', label: '待审核' },
				{ key: '2', label: '审核未通过' },
				{ key: '1', label: '审核通过' }
			],
			activeMenus: '0',
			dataTableUrl: interfaceUrl.listJson,
			tableCount: 1,
			showForm: false,
			showDelete: false,
			ownId: '', //数据行Id
			params: {
				orderBy: 'createTime',
				asc: 'false',
				isAudit: 0
			},
			editBtn: {
				type: 'submit',
				skin: 'lay-form-btns',
				contents: [
					{ type: 'primary', text: '确定', event: 'confirm' },
					{ type: 'reset', text: '取消', event: 'cancel' }
				]
			},
			cancelBtn: {
				type: 'submit',
				skin: 'lay-form-btns',
				contents: [{ type: 'reset', text: '取消', event: 'cancel' }]
			},
			toolbar: [
				{
					type: 'search',
					contents: [{ type: 'text', name: 'keyword', placeholder: '关键字查询' }]
				}
			],
			thead: [],
			listThead: [
				{
					title: '相册名称',
					align: 'left',
					field: 'albumName',
					showOverflowTooltip: true
				},
				{
					title: '所属社区',
					align: 'left',
					field: 'communityName'
				},
				{
					title: '申请人',
					align: 'left',
					field: 'createUserName'
				},
				{
					title: '申请时间',
					align: 'left',
					field: 'createTime'
				},
				{
					title: '审核时间',
					align: 'left',
					field: 'auditTime'
				}
			],
			auditListBtn: {
				title: '操作',
				type: 'handle',
				width: 180,
				template: '',
				events: [
					{ code: 'audit', text: '审核' },
					{ code: 'view', text: '查看' }
				]
			},
			viewListBtn: {
				title: '操作',
				type: 'handle',
				width: 180,
				template: '',
				events: [{ code: 'view', text: '查看' }]
			},
			pageOption: {
				layout: 'total, prev, pager, next, jumper',
				pageSize: 10,
				// hideOnSinglePage: true,
				position: 'right',
				current: 1,
				pageNum: 1
			},
			formData: {},
			formTitle: '编辑',
			formItemList: [
				{
					label: '相册名称',
					name: 'albumName',
					placeholder: '请输入相册名称',
					readonly: false,
					col: 8
				},
				{
					label: '相册封面图',
					name: 'coverImgTemp',
					type: 'attachment',
					fileList: [],
					code: 'alumna_community_album_cover',
					ownId: this.ownId,
					// size: 100,
					portrait: true,
					readonly: false,
					param: {
						isShowPath: true
					},
					col: 12
				},
				{
					label: '相册介绍',
					name: 'introduce',
					placeholder: '请填写社区介绍',
					type: 'textarea',
					readonly: false,
					col: 12
				},
				{
					label: '审核状态',
					name: 'isAudit',
					type: 'radio',
					data: [
						{ value: 1, name: '审核通过' },
						{ value: 2, name: '审核不通过' }
					],
					verify: 'required',
					rules: {
						required: true,
						message: '请确定是否审核通过',
						trigger: 'blur'
					},
					readonly: false,
					col: 4
				},
				{
					label: '审核意见',
					name: 'auditContent',
					placeholder: '请填写审核意见',
					type: 'textarea',
					readonly: false,
					col: 12
				}
			]
		};
	},
	watch: {
		dataTableUrl() {
			this.tableCount++;
		}
	},
	created() {
		this.thead = this.getListThead(this.auditListBtn);
	},
	methods: {
		//表格行高
		tableRowClassName({ row, rowIndex }) {
			let styleRes = { height: '54px !important' };
			return styleRes;
		},
		inputChange(key, value) {},
		//操作按钮点击事件
		btnClick(res) {
			let code = res.handle.code;
			switch (code) {
				case 'add':
					this.formTitle = '新增';
					this.editModule(this.formItemList);
					const snowflake = new SnowflakeId();
					this.formData = { id: snowflake.generate(), status: true };
					this.showForm = true;
					break;
				case 'edit':
					this.formTitle = '编辑';
					this.ownId = res.row.id;
					this.editModule(this.formItemList);
					this.$request({ url: interfaceUrl.info + '/' + res.row.id, method: 'GET' }).then(res => {
						if (res.rCode === 0) {
							this.formData = res.results;
							this.showForm = true;
						}
					});
					break;
				case 'view':
					this.formTitle = '查看';
					this.ownId = res.row.id;
					this.readModule(this.formItemList);
					this.$request({ url: interfaceUrl.info + '/' + res.row.id, method: 'GET' }).then(res => {
						if (res.rCode === 0) {
							this.formData = res.results;
							this.formData.coverImgTemp = fileAccess + res.results.coverImg;
							this.showForm = true;
						}
					});
					break;
				case 'delete':
					this.deleteId = res.row.id;
					this.showDelete = true;
					break;
				case 'audit':
					this.formTitle = '审核';
					this.ownId = res.row.id;
					this.auditModule(this.formItemList, ['isAudit', 'auditContent']);
					this.$request({ url: interfaceUrl.info + '/' + res.row.id, method: 'GET' }).then(res => {
						if (res.rCode === 0) {
							this.formData = res.results;
							this.formData.coverImgTemp = fileAccess + res.results.coverImg;
							this.formData.isAudit = null;
							this.showForm = true;
						}
					});
					break;
				default:
					break;
			}
		},
		//表单提交
		handleFormSubmit(data) {
			this.$refs.form.validate(valid => {
				if (!valid) {
					return;
				}
				let formData = data;
				for (let key in formData) {
					if (key.indexOf('_') >= 0) {
						let indexName = key.replace('_', '.');
						formData[indexName] = formData[key];
					}
				}
				let url = '';
				if (this.formTitle === '新增') {
					url = interfaceUrl.save;
				} else if (this.formTitle === '审核') {
					url = interfaceUrl.info + '/' + this.ownId + '/audit';
					formData = {
						isAudit: formData.isAudit,
						auditContent: formData.auditContent
					};
				} else {
					url = interfaceUrl.update;
				}

				this.$request({ url: url, data: formData, method: 'POST' }).then(res => {
					if (res.rCode !== 0) {
						this.$message.error(res.msg);
						return;
					}
					this.$message.success('操作成功');
					this.showForm = false;
					this.formData = {};
					this.$refs.table.reload();
				});
			});
		},
		//删除行
		deleteRow() {
			this.$request({
				url: interfaceUrl.deleteBatchIds,
				data: { ids: this.deleteId },
				method: 'POST'
			}).then(res => {
				if (res.rCode === 0) {
					this.$refs.table.reload();
					this.$message.success('删除成功');
				} else {
					this.$message.error(res.msg);
				}
				this.showDelete = false;
			});
		},
		//排序变化事件
		sortChange(column, prop, order) {
			let asc = this.params.asc;
			let orderBy = this.params.orderBy;
			if (column.order === 'ascending') {
				//升序
				asc = 'true';
				orderBy = column.prop;
			} else if (column.order === 'descending') {
				//降序
				asc = 'false';
				orderBy = column.prop;
			} else {
				//不排序
				asc = 'false';
				orderBy = column.prop;
			}
			this.params.asc = asc;
			this.params.orderBy = orderBy;
			this.$refs.table.reload();
		},
		//编辑模式
		editModule(list) {
			for (var i in list) {
				var item = list[i];
				item.readonly = false;
			}
			list.push(this.editBtn);
		},
		//只读模式
		readModule(list) {
			for (var i in list) {
				var item = list[i];
				item.readonly = true;
			}
			list.push(this.cancelBtn);
		},
		//审核模式
		auditModule(list, editCols) {
			for (var i in list) {
				var item = list[i];
				item.readonly = !(
					editCols !== undefined &&
					editCols.length > 0 &&
					editCols.indexOf(item.name) > -1
				);
			}
			list.push(this.editBtn);
		},
		//页签切换
		handleSelect(res) {
			this.activeMenus = res;
			this.thead = [];
			if ('0' === res) {
				this.params.isAudit = 0;
				this.thead = this.getListThead(this.auditListBtn);
			} else if ('1' === res) {
				this.params.isAudit = 1;
				this.thead = this.getListThead(this.viewListBtn);
			} else if ('2' === res) {
				this.params.isAudit = 2;
				this.thead = this.getListThead(this.viewListBtn);
			}
			this.tableCount++;
		},
		getListThead(btnJson) {
			let tempThead = Object.assign([], this.listThead);
			if (tempThead.length > 7) {
				tempThead.pop();
			}
			tempThead.push(btnJson);
			return tempThead;
		},
		refreshData() {
			this.$refs.table.reload();
		}
	}
};
</script>

<style scoped lang="scss">
@import '../../../assets/style/style.scss';
.content {
	display: flex;
	width: 100%;
	height: 100%;

	::v-deep .es-data-table {
		flex: 1;
		display: flex;
		flex-direction: column;
		height: calc(100% - 58px);

		.es-data-table-content {
			flex: 1;
			height: 0;
			display: flex;
			flex-direction: column;

			.el-table {
				flex: 1;
				// height: 100% !important;
			}

			.es-thead-border {
				.el-table__header {
					th {
						border-right: 1px solid #e1e1e1;
					}
				}
			}
		}
	}

	::v-deep .el-form-item__label {
		background: none;
		border: 0px solid #c2c2c2;
	}
}

.el-dialog__body {
	.btn-box {
		margin-top: 20px;
		display: flex;
		justify-content: flex-end;

		.btn {
			padding: 5px 10px;
			color: #666;
			border: 1px solid #eee;
			cursor: pointer;
			margin-right: 5px;
			border-radius: 4px;
			// &.theme {
			// 	background: $--color-primary;
			// 	color: #fff;
			// 	border-color: $--color-primary;
			// }
		}
	}
}
</style>
