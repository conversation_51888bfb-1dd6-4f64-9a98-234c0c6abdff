export const mixinInfo = {
	props: {
		// 动态化参数
		contentsKey: {
			type: String,
			// required: true
			default: ''
		}
	},
	computed: {
		wd() {
			const formReadonly = this.formReadonly;
			const projectAdjunctId = this.formData.projectAdjunctId; // 项目附件id
			// this.formData.expectedResultsForm = this.formData.expectedResultsForm
			// 	? this.formData.expectedResultsForm.split(',')
			// 	: this.formData.expectedResultsForm;
			// console.log(this.formData.expectedResultsForm, 7897);

			const formData = this.formData;

			const projectType = formData.projectType; // 0-自然 1-社科
			let obj = {
				basics: {}, // 基础配置
				contents: [] // 表单配置
			};
			switch (this.contentsKey) {
				case 'crosswiseProject': // 横向项目
					{
						obj = {
							// 基础配置
							basics: {
								info: '/ybzy/projectBaseInfo/projectApplyInfo', // 详情接口
								save: '/ybzy/projectBaseInfo/save', // 新增
								edit: '/ybzy/projectBaseInfo/update', // 修改
								flowTypeCode: 'project_crosswise', // 流程code
								defaultProcessKey: 'gylc' // 默认关联流程 key
							},
							// 表单配置
							contents: [
								{
									name: 'projectNumber',
									placeholder: '请输入',
									label: '项目编号',
									readonly: true,
									hide: this.title !== '查看',
									col: 6
								},
								{
									name: 'projectType',
									label: '项目类型',
									type: 'select',
									data: this.codesObj.project_type,
									'value-key': 'cciValue',
									'label-key': 'shortName',
									rules: {
										required: !formReadonly,
										message: '请选择',
										trigger: 'blur'
									},
									col: 6
								},
								{
									name: 'projectName',
									placeholder: '请输入',
									label: '项目名称',
									rules: {
										required: !formReadonly,
										message: '请输入',
										trigger: 'blur'
									},
									col: 6
								},

								{
									name: 'projectSource',
									label: '项目来源',
									type: 'select',
									data: this.codesObj.project_source,
									'value-key': 'cciValue',
									'label-key': 'shortName',
									hide: projectType !== '2',
									rules: {
										required: !formReadonly,
										message: '请选择',
										trigger: 'blur'
									},
									col: 6
								},
								{
									name: 'approveDate',
									placeholder: '请选择',
									col: 6,
									label: projectType === '1' ? '项目批准（合同签订）时间' : '批准日期',
									rules: {
										required: !formReadonly,
										message: '请选择',
										trigger: 'blur'
									},
									type: 'date'
								},
								{
									name: 'principal',
									placeholder: '请输入',
									col: 6,
									label: '负责人名称',
									hide: projectType !== '2',
									rules: {
										required: !formReadonly,
										message: '请输入',
										trigger: 'blur'
									}
								},
								{
									name: 'workload',
									placeholder: '请输入',
									col: 6,
									label: '工作量',
									type: 'select',
									data: this.codesObj.project_workload,
									'value-key': 'cciValue',
									'label-key': 'shortName',
									filterable: true,
									hide: projectType !== '2',
									rules: {
										required: !formReadonly,
										message: '请输入',
										trigger: 'blur'
									}
								},
								{
									name: 'firstLevelDiscipline',
									placeholder: '请选择',
									col: 6,
									label: '一级学科',
									type: 'select',
									hide: projectType !== '2',
									data: this.codesObj.selectList1,
									filterable: true,
									'value-key': 'value',
									'label-key': 'label',
									rules: {
										required: !formReadonly,
										message: '请输入',
										trigger: 'blur'
									}
								},
								{
									name: 'studyType',
									label: '研究类别',
									type: 'select',
									data: this.codesObj.project_study_type,
									'value-key': 'cciValue',
									'label-key': 'shortName',
									filterable: true,
									hide: projectType !== '2',
									rules: {
										required: !formReadonly,
										message: '请选择',
										trigger: 'blur'
									},
									col: 6
								},
								// {
								// 	name: 'signContractDate',
								// 	placeholder: '请选择',
								// 	col: 6,
								// 	label: '合同签订日期',
								// 	rules: {
								// 		required: !formReadonly,
								// 		message: '请选择',
								// 		trigger: 'blur'
								// 	},
								// 	type: 'date'
								// },
								{
									name: 'studyStartDate',
									placeholder: '请选择',
									col: 6,
									label: '研究期限开始时间',
									type: 'date',
									format: 'yyyy-MM-dd',
									rules: {
										required: !formReadonly,
										message: '请选择',
										trigger: 'blur'
									},
									unlinkPanels: true
								},
								{
									name: 'studyEndDate',
									placeholder: '请选择',
									col: 6,
									label: '研究期限结束时间',
									type: 'date',
									rules: {
										required: !formReadonly,
										message: '请选择',
										trigger: 'blur'
									},
									unlinkPanels: true
								},

								// {
								// 	name: 'doctorPerYear',
								// 	placeholder: '请输入',
								// 	col: 6,
								// 	type: 'number',
								// 	min: 0,
								// 	label: '博士生折合人时 (人年)',
								// 	hide: projectType !== '1',
								// 	rules: {
								// 		required: !formReadonly,
								// 		message: '请输入',
								// 		trigger: 'blur'
								// 	}
								// },
								{
									name: 'subjectType',
									placeholder: '请选择',
									col: 6,
									label: '学科分类',
									type: 'select',
									data: this.codesObj.selectList1,
									filterable: true,
									'value-key': 'value',
									'label-key': 'label',
									hide: projectType !== '1',
									rules: {
										required: !formReadonly,
										message: '请选择',
										trigger: 'blur'
									}
								},
								// {
								// 	name: 'activityType',
								// 	placeholder: '请选择',
								// 	col: 6,
								// 	label: '活动类型',
								// 	hide: projectType !== '1',
								// 	type: 'select',
								// 	data: this.codesObj.project_activity_type,
								// 	'value-key': 'cciValue',
								// 	'label-key': 'shortName',
								// 	filterable: true
								// 	// rules: {
								// 	// 	required: !formReadonly,
								// 	// 	message: '请选择',
								// 	// 	trigger: 'blur'
								// 	// },
								// },
								{
									name: 'projectSource',
									placeholder: '请选择',
									col: 6,
									label: '项目来源',
									type: 'select',
									data: this.codesObj.project_source,
									'value-key': 'cciValue',
									'label-key': 'shortName',
									filterable: true,
									hide: projectType !== '1',
									rules: {
										required: !formReadonly,
										message: '请选择',
										trigger: 'blur'
									}
								},
								{
									name: 'orgForm',
									placeholder: '请选择',
									col: 6,
									label: '组织形式',
									type: 'select',
									data: this.codesObj.project_organization_form,
									'value-key': 'cciValue',
									'label-key': 'shortName',
									filterable: true,
									hide: projectType !== '1',
									rules: {
										required: !formReadonly,
										message: '请选择',
										trigger: 'blur'
									},
									events: {
										change: (val, val1) => {
											if (val1 == 1) {
												this.$message.warning(
													'"牵头单位"指项目承担单位直接与项目下达单位签订合同或计划任务书的单位'
												);
											}
										}
									}
								},

								{
									name: formReadonly ? 'ecoIndustryTypeTxt' : 'ecoIndustryType',
									placeholder: '请选择',
									col: 6,
									label: '服务的国民经济行业',
									type: 'cascader',
									data: this.codesObj.ecoIndustryType,
									'value-key': 'value',
									'label-key': 'label',
									filterable: true,
									clearable: true,
									props: { checkStrictly: true },
									rules: {
										required: !formReadonly,
										message: '请选择',
										trigger: 'blur'
									}
								},
								{
									name: formReadonly ? 'ecoGoalTypeTxt' : 'ecoGoalType',
									placeholder: '请选择',
									col: 6,
									label: projectType === '1' ? '项目的社会经济目标' : '社会经济目标',
									type: 'cascader',
									data: this.codesObj.ecoGoalType,
									'value-key': 'value',
									'label-key': 'label',
									filterable: true,
									clearable: true,
									props: { checkStrictly: true },
									rules: {
										required: !formReadonly,
										message: '请选择',
										trigger: 'blur'
									}
								},
								// {
								// 	name: 'projectState',
								// 	placeholder: '请选择',
								// 	col: 6,
								// 	label: '项目状态',
								// 	hide: projectType !== '2',
								// 	type: 'select',
								// 	'value-key': 'cciValue',
								// 	'label-key': 'shortName',
								// 	filterable: true,
								// 	data: [
								// 		{
								// 			cciValue: '0',
								// 			shortName: '进行'
								// 		},
								// 		{
								// 			cciValue: '1',
								// 			shortName: '完成'
								// 		}
								// 	],
								// 	rules: {
								// 		required: !formReadonly,
								// 		message: '请选择',
								// 		trigger: 'blur'
								// 	}
								// },
								{
									name: 'planEndDate',
									placeholder: '请选择',
									col: 6,
									label: '计划结束日期',
									hide: projectType !== '2',
									type: 'date',
									rules: {
										required: !formReadonly,
										message: '请选择',
										trigger: 'blur'
									},
									unlinkPanels: true
								},
								{
									name: 'actualFinishDate',
									placeholder: '请选择',
									col: 6,
									label: '实际完成日期',
									hide: projectType !== '2',
									type: 'date',
									rules: {
										required: !formReadonly,
										message: '请选择',
										trigger: 'blur'
									},
									unlinkPanels: true
								},
								{
									name: 'expectedResultsForm',
									placeholder: '请输入',
									col: 6,
									label: '预期成果形式',
									type: 'select',
									multiple: true,
									data: this.codesObj.project_results_form,
									'value-key': 'cciValue',
									'label-key': 'shortName',
									hide: projectType !== '2',
									rules: {
										required: !formReadonly,
										message: '请输入',
										trigger: 'blur'
									}
								},
								// {
								// 	name: 'finalResultsForm',
								// 	placeholder: '请输入',
								// 	col: 6,
								// 	label: '最终成果形式',
								// 	type: 'select',
								// 	data: this.codesObj.project_results_form,
								// 	'value-key': 'cciValue',
								// 	'label-key': 'shortName',
								// 	hide: projectType !== '2',
								// 	rules: {
								// 		required: !formReadonly,
								// 		message: '请输入',
								// 		trigger: 'blur'
								// 	}
								// },
								{
									name: 'cooForm',
									placeholder: '请输入',
									col: 6,
									label: '合作形式',
									type: 'select',
									data: this.codesObj.project_cooperation_form,
									'value-key': 'cciValue',
									'label-key': 'shortName',
									rules: {
										required: !formReadonly,
										message: '请输入',
										trigger: 'blur'
									}
								},
								// {
								// 	name: 'cooperatingOrg',
								// 	placeholder: '请输入',
								// 	col: 6,
								// 	label: '合作单位',
								// 	hide: projectType !== '2',
								// 	rules: {
								// 		required: !formReadonly,
								// 		message: '请输入',
								// 		trigger: 'blur'
								// 	}
								// },
								{
									name: 'cooperationType',
									placeholder: '请选择',
									col: 6,
									label: '合作类型',
									type: 'select',
									data: this.codesObj.project_cooperation_type,
									'value-key': 'cciValue',
									'label-key': 'shortName',
									filterable: true,
									rules: {
										required: !formReadonly,
										message: '请选择',
										trigger: 'blur'
									}
								},
								{
									name: 'contractType',
									placeholder: '请选择',
									col: 6,
									label: '合同类型',
									type: 'select',
									data: this.codesObj.project_contract_type,
									'value-key': 'cciValue',
									'label-key': 'shortName',
									filterable: true,
									rules: {
										required: !formReadonly,
										message: '请选择',
										trigger: 'blur'
									}
								},
								// {
								// 	name: 'isSign',
								// 	placeholder: '请选择',
								// 	col: 6,
								// 	label: '国际合作项目标识和企事业单位委托省内外标识',
								// 	type: 'select',
								// 	'value-key': 'cciValue',
								// 	'label-key': 'shortName',
								// 	filterable: true,
								// 	data: [
								// 		{
								// 			cciValue: '0',
								// 			shortName: '否'
								// 		},
								// 		{
								// 			cciValue: '1',
								// 			shortName: '是'
								// 		}
								// 	],
								// 	hide: projectType !== '1',
								// 	rules: {
								// 		required: !formReadonly,
								// 		message: '请选择',
								// 		trigger: 'blur'
								// 	}
								// },

								// {
								// 	name: 'fj1',
								// 	label: '横向审批流程单（二级学院签章）',
								// 	type: 'attachment',
								// 	value: '',
								// 	col: 12,
								// 	preview: true,
								// 	code: 'pro_crosswise_audit_att',
								// 	ownId: projectAdjunctId, // 业务id
								// 	rules: {
								// 		required: !formReadonly,
								// 		message: '请上传',
								// 		trigger: 'blur'
								// 	}
								// },
								{
									name: 'fj2',
									label: '登记备案表、正式合同、到账证明表等支撑材料',
									type: 'attachment',
									value: '',
									col: 12,
									preview: true,
									code: 'pro_crosswise_contract_att',
									ownId: projectAdjunctId, // 业务id
									rules: {
										required: !formReadonly,
										message: '请上传',
										trigger: 'blur'
									}
								},

								{
									name: 'responsibleContent',
									placeholder: '',
									label: '研究内容',
									type: 'textarea',
									rules: {
										required: !formReadonly,
										message: '请输入',
										trigger: 'blur'
									},
									col: 12
								},
								{
									name: 'achievement',
									placeholder: '',
									label: '成果实现',
									rules: {
										required: !formReadonly,
										message: '请输入',
										trigger: 'blur'
									},
									col: 12
								},
								{
									name: 'signUnit',
									placeholder: '',
									label: '签订单位',
									rules: {
										required: !formReadonly,
										message: '请输入',
										trigger: 'blur'
									},
									col: 12
								},
								{
									name: 'appropriationBudget',
									placeholder: '',
									label: '经费预算',
									rules: {
										required: !formReadonly,
										message: '请输入',
										trigger: 'blur'
									},
									col: 12
								},
								// {
								// 	name: 'declarationInstructions',
								// 	label: '申报说明',
								// 	type: 'radio',
								// 	rules: {
								// 		required: !formReadonly,
								// 		message: '请选择',
								// 		trigger: 'blur'
								// 	},
								// 	col: 12,
								// 	data: this.codesObj.project_declaration_instructions,
								// 	'value-key': 'cciValue',
								// 	'label-key': 'shortName'
								// },
								{
									name: 'declareTime',
									label: '申报时间',
									value: '',
									type: 'date',
									rules: {
										required: !formReadonly,
										message: '请选择',
										trigger: 'blur'
									},
									col: 12
								},
								{
									name: 'isBelongYbzy',
									label: '第一归属单位是否为宜宾职业技术学院',
									type: 'select',
									data: [
										{ cciValue: '0', shortName: '否' },
										{ cciValue: '1', shortName: '是' }
									],
									'value-key': 'cciValue',
									'label-key': 'shortName',
									rules: {
										required: !formReadonly,
										message: '请选择',
										trigger: 'blur'
									},
									col: 12
								},
								{
									name: 'cooperatingOrg',
									label: '合作单位',
									hide: this.formData.isBelongYbzy != '0',
									rules: {
										required: !formReadonly,
										message: '请选择',
										trigger: 'blur'
									},
									col: 12
								}
							]
						};
					}
					break;
				case 'verticalProject': // 纵向项目
				
					{
						obj = {
							// 基础配置
							basics: {
								info: '/ybzy/projectBaseInfo/projectApplyInfo', // 详情接口
								save: '/ybzy/projectBaseInfo/save', // 新增
								edit: '/ybzy/projectBaseInfo/update', // 修改
								flowTypeCode: 'project_lengthways', // 流程code
								defaultProcessKey: 'gylc' // 默认关联流程 key
							},
							// 表单配置
							contents: [
								{
									name: 'projectNumber',
									placeholder: '请输入',
									label: '项目编号',
									readonly: true,
									hide: this.title !== '查看',
									col: 6
								},
								{
									name: 'projectType',
									label: '项目类型',
									type: 'select',
									data: this.codesObj.project_type,
									'value-key': 'cciValue',
									'label-key': 'shortName',
									rules: {
										required: !formReadonly,
										message: '请选择',
										trigger: 'blur'
									},
									col: 6
								},
								{
									name: 'projectName',
									placeholder: '请输入',
									label: '项目名称',
									rules: {
										required: !formReadonly,
										message: '请输入',
										trigger: 'blur'
									},
									col: 6
								},

								{
									name: 'projectLevel',
									label: '项目级别',
									// 纵向-project_level_lengthways、院级-project_level_college）
									type: 'select',
									data: this.codesObj.project_level_lengthways,
									'value-key': 'cciValue',
									'label-key': 'shortName',
									rules: {
										required: !formReadonly,
										message: '请选择',
										trigger: 'blur'
									},
									col: 6
								},
								{
									name: 'belowOrgName',
									label: '下达单位',
									rules: {
										required: !formReadonly,
										message: '请输入',
										trigger: 'blur'
									},
									col: 6
								},
								{
									name: 'studyStartDate',
									placeholder: '请选择',
									col: 6,
									label: '研究期限开始时间',
									type: 'date',
									format: 'yyyy-MM-dd',
									rules: {
										required: !formReadonly,
										message: '请选择',
										trigger: 'blur'
									},
									unlinkPanels: true
								},
								{
									name: 'studyEndDate',
									placeholder: '请选择',
									col: 6,
									label: '研究期限结束时间',
									type: 'date',
									rules: {
										required: !formReadonly,
										message: '请选择',
										trigger: 'blur'
									},
									unlinkPanels: true
								},
								{
									name: 'projectSource',
									placeholder: '请选择',
									col: 6,
									label: '项目来源',
									type: 'select',
									data: this.codesObj.project_source,
									'value-key': 'cciValue',
									'label-key': 'shortName',
									filterable: true,
									rules: {
										required: !formReadonly,
										message: '请选择',
										trigger: 'blur'
									}
								},

								// {
								// 	name: 'approveDate',
								// 	placeholder: '请选择',
								// 	col: 6,
								// 	label: projectType === '1' ? '项目批准（合同签定）时间' : '批准日期',
								// 	rules: {
								// 		required: !formReadonly,
								// 		message: '请选择',
								// 		trigger: 'blur'
								// 	},
								// 	type: 'date'
								// },
								{
									name: 'principal',
									placeholder: '请输入',
									col: 6,
									label: '负责人名称',
									hide: projectType !== '2',
									rules: {
										required: !formReadonly,
										message: '请输入',
										trigger: 'blur'
									}
								},
								{
									name: 'workload',
									placeholder: '请输入',
									col: 6,
									label: '工作量',
									type: 'select',
									data: this.codesObj.project_workload,
									'value-key': 'cciValue',
									'label-key': 'shortName',
									filterable: true,
									hide: projectType !== '2',
									rules: {
										required: !formReadonly,
										message: '请输入',
										trigger: 'blur'
									}
								},
								{
									name: 'firstLevelDiscipline',
									placeholder: '请选择',
									col: 6,
									label: '一级学科',
									type: 'select',
									hide: projectType !== '2',
									data: this.codesObj.selectList1,
									filterable: true,
									'value-key': 'value',
									'label-key': 'label',
									rules: {
										required: !formReadonly,
										message: '请输入',
										trigger: 'blur'
									}
								},
								{
									name: 'studyType',
									label: '研究类别',
									type: 'select',
									data: this.codesObj.project_study_type,
									'value-key': 'cciValue',
									'label-key': 'shortName',
									filterable: true,
									hide: projectType !== '2',
									rules: {
										required: !formReadonly,
										message: '请选择',
										trigger: 'blur'
									},
									col: 6
								},

								// {
								// 	name: 'signContractDate',
								// 	placeholder: '请选择',
								// 	col: 6,
								// 	label: '合同签订日期',
								// 	rules: {
								// 		required: !formReadonly,
								// 		message: '请选择',
								// 		trigger: 'blur'
								// 	},
								// 	type: 'date'
								// },

								// {
								// 	name: 'doctorPerYear',
								// 	placeholder: '请输入',
								// 	col: 6,
								// 	type: 'number',
								// 	min: 0,
								// 	label: '博士生折合人时 (人年)',
								// 	hide: projectType !== '1',
								// 	rules: {
								// 		required: !formReadonly,
								// 		message: '请输入',
								// 		trigger: 'blur'
								// 	}
								// },
								{
									name: 'subjectType',
									placeholder: '请选择',
									col: 6,
									label: '学科分类',
									type: 'select',
									data: this.codesObj.selectList1,
									filterable: true,
									'value-key': 'value',
									'label-key': 'label',
									hide: projectType !== '1',
									rules: {
										required: !formReadonly,
										message: '请选择',
										trigger: 'blur'
									}
								},
								// {
								// 	name: 'activityType',
								// 	placeholder: '请选择',
								// 	col: 6,
								// 	label: '活动类型',
								// 	hide: projectType !== '1',
								// 	type: 'select',
								// 	data: this.codesObj.project_activity_type,
								// 	'value-key': 'cciValue',
								// 	'label-key': 'shortName',
								// 	filterable: true
								// 	// rules: {
								// 	// 	required: !formReadonly,
								// 	// 	message: '请选择',
								// 	// 	trigger: 'blur'
								// 	// },
								// },

								{
									name: 'orgForm',
									placeholder: '请选择',
									col: 6,
									label: '组织形式',
									type: 'select',
									data: this.codesObj.project_organization_form,
									'value-key': 'cciValue',
									'label-key': 'shortName',
									filterable: true,
									hide: projectType !== '1',
									rules: {
										required: !formReadonly,
										message: '请选择',
										trigger: 'blur'
									},
									events: {
										change: (val, val1) => {
											if (val1 == 1) {
												this.$message.warning(
													'"牵头单位"指项目承担单位直接与项目下达单位签订合同或计划任务书的单位'
												);
											}
										}
									}
								},

								{
									name: formReadonly ? 'ecoIndustryTypeTxt' : 'ecoIndustryType',
									placeholder: '请选择',
									col: 6,
									label: '服务的国民经济行业',
									type: 'cascader',
									data: this.codesObj.ecoIndustryType,
									'value-key': 'value',
									'label-key': 'label',
									filterable: true,
									clearable: true,
									props: { checkStrictly: true },
									rules: {
										required: !formReadonly,
										message: '请选择',
										trigger: 'blur'
									}
								},
								{
									name: formReadonly ? 'ecoGoalTypeTxt' : 'ecoGoalType',
									placeholder: '请选择',
									col: 6,
									label: projectType === '1' ? '项目的社会经济目标' : '社会经济目标',
									type: 'cascader',
									data: this.codesObj.ecoGoalType,
									'value-key': 'value',
									'label-key': 'label',
									filterable: true,
									clearable: true,
									props: { checkStrictly: true },
									rules: {
										required: !formReadonly,
										message: '请选择',
										trigger: 'blur'
									}
								},
								// {
								// 	name: 'projectState',
								// 	placeholder: '请选择',
								// 	col: 6,
								// 	label: '项目状态',
								// 	hide: projectType !== '2',
								// 	type: 'select',
								// 	'value-key': 'cciValue',
								// 	'label-key': 'shortName',
								// 	filterable: true,
								// 	data: [
								// 		{
								// 			cciValue: '0',
								// 			shortName: '进行'
								// 		},
								// 		{
								// 			cciValue: '1',
								// 			shortName: '完成'
								// 		}
								// 	],
								// 	rules: {
								// 		required: !formReadonly,
								// 		message: '请选择',
								// 		trigger: 'blur'
								// 	}
								// },
								{
									name: 'cooForm',
									placeholder: '请输入',
									col: 6,
									label: '合作形式',
									type: 'select',
									data: this.codesObj.project_cooperation_form,
									'value-key': 'cciValue',
									'label-key': 'shortName',
									rules: {
										required: !formReadonly,
										message: '请输入',
										trigger: 'blur'
									}
								},
								// {
								// 	name: 'planEndDate',
								// 	placeholder: '请选择',
								// 	col: 6,
								// 	label: '计划结束日期',
								// 	hide: projectType !== '2',
								// 	type: 'date',
								// 	rules: {
								// 		required: !formReadonly,
								// 		message: '请选择',
								// 		trigger: 'blur'
								// 	},
								// 	unlinkPanels: true
								// },
								// {
								// 	name: 'actualFinishDate',
								// 	placeholder: '请选择',
								// 	col: 6,
								// 	label: '实际完成日期',
								// 	hide: projectType !== '2',
								// 	type: 'date',
								// 	rules: {
								// 		required: !formReadonly,
								// 		message: '请选择',
								// 		trigger: 'blur'
								// 	},
								// 	unlinkPanels: true
								// },
								{
									name: 'expectedResultsForm',
									placeholder: '请输入',
									col: 6,
									label: '预期成果形式',
									type: 'select',
									multiple: true,
									data: this.codesObj.project_results_form,
									'value-key': 'cciValue',
									'label-key': 'shortName',
									hide: projectType !== '2',
									rules: {
										required: !formReadonly,
										message: '请输入',
										trigger: 'blur'
									}
								},
								// {
								// 	name: 'finalResultsForm',
								// 	placeholder: '请输入',
								// 	col: 6,
								// 	label: '最终成果形式',
								// 	type: 'select',
								// 	data: this.codesObj.project_results_form,
								// 	'value-key': 'cciValue',
								// 	'label-key': 'shortName',
								// 	hide: projectType !== '2',
								// 	rules: {
								// 		required: !formReadonly,
								// 		message: '请输入',
								// 		trigger: 'blur'
								// 	}
								// },
								// {
								// 	name: 'isSign',
								// 	placeholder: '请选择',
								// 	col: 6,
								// 	label: '国际合作项目标识和企事业单位委托省内外标识',
								// 	type: 'select',
								// 	'value-key': 'cciValue',
								// 	'label-key': 'shortName',
								// 	filterable: true,
								// 	data: [
								// 		{
								// 			cciValue: '0',
								// 			shortName: '否'
								// 		},
								// 		{
								// 			cciValue: '1',
								// 			shortName: '是'
								// 		}
								// 	],
								// 	hide: projectType !== '1',
								// 	rules: {
								// 		required: !formReadonly,
								// 		message: '请选择',
								// 		trigger: 'blur'
								// 	}
								// },
								{
									name: 'declareTaskSource',
									placeholder: '请输入',
									type: 'textarea',

									label: '申报课题来源（网址）',
									hide: projectType !== '1',
									rules: {
										required: !formReadonly,
										message: '请输入',
										trigger: 'blur'
									},
									col: 12
								},
								// {
								// 	name: 'researchContents',
								// 	placeholder: '请输入',
								// 	label: '研究内容(体现价值、特色、创新点、可行性)',
								// 	type: 'textarea',
								// 	maxlength: '255',
								// 	'show-word-limit': true,
								// 	hide: projectType !== '1',
								// 	rules: {
								// 		required: !formReadonly,
								// 		message: '请输入',
								// 		trigger: 'blur'
								// 	},
								// 	col: 12
								// },
								{
									name: 'fj1',
									label: '申报书、活页、汇总表等材料',
									type: 'attachment',
									value: '',
									col: 12,
									preview: true,
									code: 'pro_declaration_att',
									ownId: projectAdjunctId, // 业务id
									rules: {
										required: !formReadonly,
										message: '请上传',
										trigger: 'blur'
									},
									// hide: projectType !== '1'
								},
								// {
								// 	name: 'fj2',
								// 	label: '纵向审批流程单（二级学院签章）',
								// 	type: 'attachment',
								// 	value: '',
								// 	col: 12,
								// 	preview: true,
								// 	code: 'pro_lengthways_audit_att',
								// 	ownId: projectAdjunctId, // 业务id
								// 	rules: {
								// 		required: !formReadonly,
								// 		message: '请上传',
								// 		trigger: 'blur'
								// 	},
								// 	hide: projectType !== '1'
								// },
								{
									name: 'responsibleContent',
									placeholder: '请输入',
									label: '研究内容(体现价值、特色、创新点、可行性)',
									type: 'textarea',
									maxlength: '255',
									'show-word-limit': true,
									rules: {
										required: !formReadonly,
										message: '请输入',
										trigger: 'blur'
									},
									col: 12
								},
								{
									name: 'remark',
									placeholder: '',
									label: '备注',
									type: 'textarea',
									maxlength: '255',
									'show-word-limit': true,
									col: 12
								},
								// {
								// 	name: 'responsibleContent',
								// 	placeholder: '',
								// 	label: '研究内容',
								// 	type: 'textarea',
								// 	rules: {
								// 		required: !formReadonly,
								// 		message: '请输入',
								// 		trigger: 'blur'
								// 	},
								// 	col: 12
								// },
								// {
								// 	name: 'declarationInstructions',
								// 	label: '申报说明',
								// 	type: 'radio',
								// 	rules: {
								// 		required: !formReadonly,
								// 		message: '请选择',
								// 		trigger: 'blur'
								// 	},
								// 	col: 12,
								// 	data: this.codesObj.project_declaration_instructions,
								// 	'value-key': 'cciValue',
								// 	'label-key': 'shortName'
								// },
								{
									name: 'declareTime',
									label: '申报时间',
									value: '',
									type: 'date',
									rules: {
										required: !formReadonly,
										message: '请选择',
										trigger: 'blur'
									},
									col: 12
								},
								{
									name: 'isBelongYbzy',
									label: '第一归属单位是否为宜宾职业技术学院',
									type: 'select',
									data: [
										{ cciValue: '0', shortName: '否' },
										{ cciValue: '1', shortName: '是' }
									],
									'value-key': 'cciValue',
									'label-key': 'shortName',
									rules: {
										required: !formReadonly,
										message: '请选择',
										trigger: 'blur'
									},
									col: 12
								},
								{
									name: 'cooperatingOrg',
									label: '合作单位',
									hide: this.formData.isBelongYbzy != '0',
									rules: {
										required: !formReadonly,
										message: '请选择',
										trigger: 'blur'
									},
									col: 12
								}
							]
						};
					}
					break;
				case 'instituteProject': // 院级项目
					{
						obj = {
							// 基础配置
							basics: {
								info: '/ybzy/projectBaseInfo/projectApplyInfo', // 详情接口
								save: '/ybzy/projectBaseInfo/save', // 新增
								edit: '/ybzy/projectBaseInfo/update', // 修改
								flowTypeCode: 'project_college', // 流程code
								defaultProcessKey: 'gylc' // 默认关联流程 key
							},
							// 表单配置
							contents: [
								{
									name: 'projectNumber',
									placeholder: '请输入',
									label: '项目编号',
									readonly: true,
									hide: this.title !== '查看',
									col: 6
								},
								{
									name: 'projectType',
									label: '项目类型',
									type: 'select',
									data: this.codesObj.project_type,
									'value-key': 'cciValue',
									'label-key': 'shortName',
									rules: {
										required: !formReadonly,
										message: '请选择',
										trigger: 'blur'
									},
									col: 6
								},
								{
									name: 'projectName',
									placeholder: '请输入',
									label: '项目名称',
									rules: {
										required: !formReadonly,
										message: '请输入',
										trigger: 'blur'
									},
									col: 6
								},

								{
									name: 'projectLevel',
									label: '项目级别',
									// 纵向-project_level_lengthways、院级-project_level_college）
									type: 'select',
									data: this.codesObj.project_level_college,
									'value-key': 'cciValue',
									'label-key': 'shortName',
									rules: {
										required: !formReadonly,
										message: '请选择',
										trigger: 'blur'
									},
									col: 6
								},
								{
									name: 'studyStartDate',
									placeholder: '请选择',
									col: 6,
									label: '研究期限开始时间',
									type: 'date',
									format: 'yyyy-MM-dd',
									rules: {
										required: !formReadonly,
										message: '请选择',
										trigger: 'blur'
									},
									unlinkPanels: true
								},
								{
									name: 'studyEndDate',
									placeholder: '请选择',
									col: 6,
									label: '研究期限结束时间',
									type: 'date',
									rules: {
										required: !formReadonly,
										message: '请选择',
										trigger: 'blur'
									},
									unlinkPanels: true
								},
								{
									name: 'projectSource',
									placeholder: '请选择',
									col: 6,
									label: '项目来源',
									type: 'select',
									data: this.codesObj.project_source_college,
									'value-key': 'cciValue',
									'label-key': 'shortName',
									filterable: true,
									rules: {
										required: !formReadonly,
										message: '请选择',
										trigger: 'blur'
									}
								},

								// {
								// 	name: 'approveDate',
								// 	placeholder: '请选择',
								// 	col: 6,
								// 	label: projectType === '1' ? '项目批准（合同签定）时间' : '批准日期',
								// 	rules: {
								// 		required: !formReadonly,
								// 		message: '请选择',
								// 		trigger: 'blur'
								// 	},
								// 	type: 'date'
								// },
								{
									name: 'principal',
									placeholder: '请输入',
									col: 6,
									label: '负责人名称',
									hide: projectType !== '2',
									rules: {
										required: !formReadonly,
										message: '请输入',
										trigger: 'blur'
									}
								},
								{
									name: 'workload',
									placeholder: '请输入',
									col: 6,
									label: '工作量',
									type: 'select',
									data: this.codesObj.project_workload,
									'value-key': 'cciValue',
									'label-key': 'shortName',
									filterable: true,
									hide: projectType !== '2',
									rules: {
										required: !formReadonly,
										message: '请输入',
										trigger: 'blur'
									}
								},
								{
									name: 'firstLevelDiscipline',
									placeholder: '请选择',
									col: 6,
									label: '一级学科',
									type: 'select',
									hide: projectType !== '2',
									data: this.codesObj.selectList1,
									filterable: true,
									'value-key': 'value',
									'label-key': 'label',
									rules: {
										required: !formReadonly,
										message: '请输入',
										trigger: 'blur'
									}
								},
								{
									name: 'studyType',
									label: '研究类别',
									type: 'select',
									data: this.codesObj.project_study_type,
									'value-key': 'cciValue',
									'label-key': 'shortName',
									filterable: true,
									hide: projectType !== '2',
									rules: {
										required: !formReadonly,
										message: '请选择',
										trigger: 'blur'
									},
									col: 6
								},
								// {
								// 	name: 'doctorPerYear',
								// 	placeholder: '请输入',
								// 	col: 6,
								// 	type: 'number',
								// 	min: 0,
								// 	label: '博士生折合人时 (人年)',
								// 	hide: projectType !== '1',
								// 	rules: {
								// 		required: !formReadonly,
								// 		message: '请输入',
								// 		trigger: 'blur'
								// 	}
								// },
								{
									name: 'subjectType',
									placeholder: '请选择',
									col: 6,
									label: '学科分类',
									type: 'select',
									data: this.codesObj.selectList1,
									filterable: true,
									'value-key': 'value',
									'label-key': 'label',
									hide: projectType !== '1',
									rules: {
										required: !formReadonly,
										message: '请选择',
										trigger: 'blur'
									}
								},
								// {
								// 	name: 'activityType',
								// 	placeholder: '请选择',
								// 	col: 6,
								// 	label: '活动类型',
								// 	hide: projectType !== '1',
								// 	type: 'select',
								// 	data: this.codesObj.project_activity_type,
								// 	'value-key': 'cciValue',
								// 	'label-key': 'shortName',
								// 	filterable: true
								// },

								{
									name: 'orgForm',
									placeholder: '请选择',
									col: 6,
									label: '组织形式',
									type: 'select',
									data: this.codesObj.project_organization_form,
									'value-key': 'cciValue',
									'label-key': 'shortName',
									filterable: true,
									hide: projectType !== '1',
									rules: {
										required: !formReadonly,
										message: '请选择',
										trigger: 'blur'
									},
									events: {
										change: (val, val1) => {
											if (val1 == 1) {
												this.$message.warning(
													'"牵头单位"指项目承担单位直接与项目下达单位签订合同或计划任务书的单位'
												);
											}
										}
									}
								},
								{
									name: 'cooForm',
									placeholder: '请输入',
									col: 6,
									label: '合作形式',
									type: 'select',
									data: this.codesObj.project_cooperation_form,
									'value-key': 'cciValue',
									'label-key': 'shortName',
									rules: {
										required: !formReadonly,
										message: '请输入',
										trigger: 'blur'
									}
								},

								{
									name: formReadonly ? 'ecoIndustryTypeTxt' : 'ecoIndustryType',
									placeholder: '请选择',
									col: 6,
									label: '服务的国民经济行业',
									type: 'cascader',
									data: this.codesObj.ecoIndustryType,
									'value-key': 'value',
									'label-key': 'label',
									filterable: true,
									clearable: true,
									props: { checkStrictly: true },
									rules: {
										required: !formReadonly,
										message: '请选择',
										trigger: 'blur'
									}
								},
								{
									name: formReadonly ? 'ecoGoalTypeTxt' : 'ecoGoalType',
									placeholder: '请选择',
									col: 6,
									label: projectType === '1' ? '项目的社会经济目标' : '社会经济目标',
									type: 'cascader',
									data: this.codesObj.ecoGoalType,
									'value-key': 'value',
									'label-key': 'label',
									filterable: true,
									clearable: true,
									props: { checkStrictly: true },
									rules: {
										required: !formReadonly,
										message: '请选择',
										trigger: 'blur'
									}
								},
								// {
								// 	name: 'projectState',
								// 	placeholder: '请选择',
								// 	col: 6,
								// 	label: '项目状态',
								// 	hide: projectType !== '2',
								// 	type: 'select',
								// 	'value-key': 'cciValue',
								// 	'label-key': 'shortName',
								// 	filterable: true,
								// 	data: [
								// 		{
								// 			cciValue: '0',
								// 			shortName: '进行'
								// 		},
								// 		{
								// 			cciValue: '1',
								// 			shortName: '完成'
								// 		}
								// 	],
								// 	rules: {
								// 		required: !formReadonly,
								// 		message: '请选择',
								// 		trigger: 'blur'
								// 	}
								// },

								{
									name: 'planEndDate',
									placeholder: '请选择',
									col: 6,
									label: '计划结束日期',
									hide: projectType !== '2',
									type: 'date',
									rules: {
										required: !formReadonly,
										message: '请选择',
										trigger: 'blur'
									},
									unlinkPanels: true
								},
								{
									name: 'actualFinishDate',
									placeholder: '请选择',
									col: 6,
									label: '实际完成日期',
									hide: projectType !== '2',
									type: 'date',
									rules: {
										required: !formReadonly,
										message: '请选择',
										trigger: 'blur'
									},
									unlinkPanels: true
								},
								{
									name: 'expectedResultsForm',
									placeholder: '请输入',
									col: 6,
									label: '预期成果形式',
									type: 'select',
									multiple: true,
									data: this.codesObj.project_results_form,
									'value-key': 'cciValue',
									'label-key': 'shortName',
									hide: projectType !== '2',
									rules: {
										required: !formReadonly,
										message: '请输入',
										trigger: 'blur'
									}
								},
								// {
								// 	name: 'finalResultsForm',
								// 	placeholder: '请输入',
								// 	col: 6,
								// 	label: '最终成果形式',
								// 	type: 'select',
								// 	data: this.codesObj.project_results_form,
								// 	'value-key': 'cciValue',
								// 	'label-key': 'shortName',
								// 	hide: projectType !== '2',
								// 	rules: {
								// 		required: !formReadonly,
								// 		message: '请输入',
								// 		trigger: 'blur'
								// 	}
								// },
								// {
								// 	name: 'isSign',
								// 	placeholder: '请选择',
								// 	col: 6,
								// 	label: '国际合作项目标识和企事业单位委托省内外标识',
								// 	type: 'select',
								// 	'value-key': 'cciValue',
								// 	'label-key': 'shortName',
								// 	filterable: true,
								// 	data: [
								// 		{
								// 			cciValue: '0',
								// 			shortName: '否'
								// 		},
								// 		{
								// 			cciValue: '1',
								// 			shortName: '是'
								// 		}
								// 	],
								// 	hide: projectType !== '1',
								// 	rules: {
								// 		required: !formReadonly,
								// 		message: '请选择',
								// 		trigger: 'blur'
								// 	}
								// },

								{
									name: 'targetTask',
									placeholder: '',
									label: '目标任务',
									type: 'textarea',
									maxlength: '255',
									'show-word-limit': true,
									rules: {
										required: !formReadonly,
										message: '请输入',
										trigger: 'blur'
									},
									col: 12
								},
								// {
								// 	name: 'fj1',
								// 	label: '申报书',
								// 	type: 'attachment',
								// 	value: '',
								// 	col: 12,
								// 	preview: true,
								// 	code: 'pro_declaration_att',
								// 	ownId: projectAdjunctId, // 业务id
								// 	rules: {
								// 		required: !formReadonly,
								// 		message: '请上传',
								// 		trigger: 'blur'
								// 	}
								// },
								{
									name: 'fj2',
									label: '申报材料及附件',
									type: 'attachment',
									value: '',
									col: 12,
									preview: true,
									code: 'pro_college_declarationfile_att',
									ownId: projectAdjunctId, // 业务id
									rules: {
										required: !formReadonly,
										message: '请上传',
										trigger: 'blur'
									}
								},
								{
									name: 'remark',
									placeholder: '',
									label: '备注',
									type: 'textarea',
									maxlength: '255',
									'show-word-limit': true,
									col: 12
								},
								{
									name: 'responsibleContent',
									placeholder: '',
									type: 'textarea',
									label: '研究内容',
									rules: {
										required: !formReadonly,
										message: '请输入',
										trigger: 'blur'
									},
									col: 12
								},
								// {
								// 	name: 'declarationInstructions',
								// 	label: '申报说明',
								// 	type: 'radio',
								// 	rules: {
								// 		required: !formReadonly,
								// 		message: '请选择',
								// 		trigger: 'blur'
								// 	},
								// 	col: 12,
								// 	data: this.codesObj.project_declaration_instructions,
								// 	'value-key': 'cciValue',
								// 	'label-key': 'shortName'
								// },
								{
									name: 'declareTime',
									label: '申报时间',
									value: '',
									type: 'date',
									rules: {
										required: !formReadonly,
										message: '请选择',
										trigger: 'blur'
									},
									col: 12
								},
								{
									name: 'isBelongYbzy',
									label: '第一归属单位是否为宜宾职业技术学院',
									type: 'select',
									data: [
										{ cciValue: '0', shortName: '否' },
										{ cciValue: '1', shortName: '是' }
									],
									'value-key': 'cciValue',
									'label-key': 'shortName',
									rules: {
										required: !formReadonly,
										message: '请选择',
										trigger: 'blur'
									},
									col: 12
								},
								{
									name: 'cooperatingOrg',
									label: '合作单位',
									hide: this.formData.isBelongYbzy != '0',
									rules: {
										required: !formReadonly,
										message: '请选择',
										trigger: 'blur'
									},
									col: 12
								}
							]
						};
					}
					break;
				default:
					break;
			}
			obj.basics = { ...obj.basics, ...this.basics };
			return obj;
		}
	}
};
