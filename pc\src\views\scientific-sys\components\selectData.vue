<template>
	<!-- <div style="width: 100%; height: 90%"> -->
	<es-data-table
		form
		:fit="true"
		:thead="thead"
		:url="tableUrl"
		:param="param"
		:toolbar="toolbar"
		size="mini"
		close
		numbers
		page
		@btn-click="btnClick"
	></es-data-table>
	<!-- </div> -->
</template>

<script>
export default {
	props: {
		id: {
			type: String,
			default: ''
		}
	},
	data() {
		return {
			tableUrl: '/ybzy/projectBaseInfo/ableChangePage',
			param: {},
			visibleChange: false,
			tableData: [
				// {
				// 	projectCode: '123',
				// 	projectName: '项目名称',
				// 	xmzt: '状态'
				// }
			],
			toolbar: [
				{
					type: 'search',
					reset: true,
					contents: [
						{
							type: 'text',
							label: '关键字',
							name: 'keyword',
							placeholder: '请输入关键词'
						}
					]
				}
			],
			thead: [
				{
					title: '项目编号',
					field: 'projectNumber',
					width: 146,
					showOverflowTooltip: true,
					align: 'center'
				},
				{
					title: '项目名称',
					field: 'projectName',
					showOverflowTooltip: true,
					align: 'center'
				},

				{
					title: '项目状态',
					field: 'stateTxt',
					align: 'center',
					width: 120,
					render: (h, params) => {
						let stateTxt = params.row.stateTxt;
						let state = params.row.state;
						return h(
							'el-tag',
							{
								props: {
									size: 'small',
									type: this.projecstate(state)
								}
							},
							stateTxt
						);
					}
				},
				{
					title: '操作',
					type: 'handle',
					fixed: 'right',
					width: '80',
					align: 'center',
					events: [
						{
							text: '选择',
							rules: rows => {
								return rows.state === '1';
							}
						}
					]
				}
			]
		};
	},
	computed: {},
	mounted() {},
	created() {},
	methods: {
		// 获取项目审核状态
		projecstate(state) {
			let stateCurrent = '';
			// 项目状态（字典编码：project_status）0：待立项； 1：已立项； 2：立项不通过； 3：变更审核中； 4：结题审核中； 5：已结题； 6：已归档； 7：终止；
			switch (state) {
				case '0':
					stateCurrent = 'warning';
					break;
				case '3':
				case '4':
					stateCurrent = 'primary';
					break;
				case '1':
				case '5':
				case '6':
					stateCurrent = 'success';
					break;
				// case '2':
				// 	stateCurrent = 'info';
				// 	break;
				case '2':
				case '7':
					stateCurrent = 'danger';
					break;
				default:
					break;
			}
			return stateCurrent;
		},
		btnClick({ handle, row }) {
			switch (handle.text) {
				case '选择':
					this.$emit('cloeseChoose', row);
					// this.visibleChange = true;
					break;

				default:
					break;
			}
		}
	}
};
</script>
