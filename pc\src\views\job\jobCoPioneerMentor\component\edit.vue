<template>
	<es-form
		class="esFrom"
		ref="form"
		:model="formData"
		:contents="formList"
		:submit="type === '新增' || type === '修改'"
		@submit="handleSubmit"
	></es-form>
</template>
<script>
import httpApi from '@/http/job/jobCoPioneerMentor/api.js';
import request from 'eoss-ui/lib/utils/http';
import $ from 'eoss-ui/lib/utils/util';
export default {
	name: 'auditDialog',
	props: {
		selectInfo: {
			type: String
		},
		type: {
			type: String,
			required: true
		}
	},
	data() {
		return {
			formData: {
				subordinateCompany: '',
				subordinateMentorJob: '',
				company: '',
				degree: '',
				degreeExperience: '',
				jobExperience: '',
				major: '',
				mentorJob: '',
				name: '',
				projectIntroduction: '',
				projectName: '',
				tag: '',
				university: '',
				university1: '',
				xxx1: []
			},
			educationFormList: [],
			wordFormList: [],
			projectFormList: [],
			tagFormList: [],
			interfaceList: []
		};
	},
	computed: {
		formList() {
			let indexFormList = [];
			if (this.type == '新增') {
				indexFormList = [
					{
						title: '导师基本信息',
						contents: [
							{
								label: '导师头像',
								name: 'mentorHead',
								type: 'attachment',
								verify: 'required',
								code: 'job_mentor_head',
								ownId: 'png_1', // 业务id
								// size: 100,
								portrait: true,
								param: {
									isShowPath: true
								},
								col: 12
							},
							{
								label: '导师姓名',
								name: 'name',
								type: 'text',
								//readonly:true,
								verify: 'required',
								col: 6,
								rules: {
									required: true,
									message: '请填写导师姓名',
									trigger: 'change'
								}
							},

							{
								label: '所属职务',
								name: 'subordinateMentorJob',
								type: 'text',
								//readonly:true,
								verify: 'required',
								col: 6,
								rules: {
									required: true,
									message: '请填写所属职务',
									trigger: 'change'
								}
							},
							{
								label: '所属公司',
								name: 'subordinateCompany',
								type: 'text',
								//readonly:true,
								verify: 'required',
								col: 12,
								rules: {
									required: true,
									message: '请填写所属公司',
									trigger: 'change'
								}
							}
						]
					}
				];
				let educationFormList = {
					title: [
						{
							type: 'text',
							label: '教育背景:'
						},
						{
							icon: 'es-icon-jiahao',
							event: (res, index) => {
								let number = (res.contents.length + 1) / 6;
								let arr = [
									{
										label: '',
										name: `zhanwei1${number}`,
										type: 'text',
										col: 12,
										filtration: '1',
										index: number + 1,
										sendName: ''
									},
									{
										label: '学校',
										name: `university${number}`,
										type: 'text',
										//readonly:true,
										verify: 'required',
										col: 6,
										rules: {
											required: true,
											message: '请填写学校',
											trigger: 'change'
										},
										index: number + 1,
										sendName: 'university'
									},
									{
										label: '专业',
										name: `major${number}`,
										type: 'text',
										//readonly:true,
										verify: 'required',
										col: 6,
										rules: {
											required: true,
											message: '请填写专业',
											trigger: 'change'
										},
										index: number + 1,
										sendName: 'major'
									},
									{
										name: `stydyTime${number}`,
										placeholder: '请选择学习时间',
										disabled: this.reissue,
										col: 6,
										label: '学习时间',
										type: 'daterange',
										unlinkPanels: true,
										value: '',
										rules: {
											required: true,
											message: '请选择开始学习时间',
											trigger: 'change'
										},
										index: number + 1,
										sendName: 'stydyTime'
									},
									{
										label: '学历',
										name: `degree${number}`,
										type: 'text',
										//readonly:true,
										verify: 'required',
										col: 6,
										rules: {
											required: true,
											message: '请填写学历',
											trigger: 'change'
										},
										index: number + 1,
										sendName: 'degree'
									},
									{
										label: '学习经历',
										name: `degreeExperience${number}`,
										type: 'textarea',
										//readonly:true,
										rows: 5,
										col: 12,
										rules: {
											required: true,
											message: '请填写学习经历',
											trigger: 'change'
										},
										index: number + 1,
										sendName: 'degreeExperience'
									}
								];
								res.contents.push(...arr);
							}
						},
						{
							icon: 'es-icon-jianhao',
							event: (res, index) => {
								if (res.contents.length === 5) {
									return;
								}
								res.contents.splice(-6, 6);
							}
						}
					],
					//*//
					contents: [
						{
							label: '学校',
							name: 'university',
							type: 'text',
							//readonly:true,
							verify: 'required',
							col: 6,
							rules: {
								required: true,
								message: '请填写学校',
								trigger: 'change'
							},
							index: 1
						},
						{
							label: '专业',
							name: 'major',
							type: 'text',
							//readonly:true,
							verify: 'required',
							col: 6,
							rules: {
								required: true,
								message: '请填写专业',
								trigger: 'change'
							},
							index: 1
						},
						{
							name: `stydyTime`,
							placeholder: '请选择学习时间',
							disabled: this.reissue,
							col: 6,
							label: '学习时间',
							type: 'daterange',
							unlinkPanels: true,
							value: '',
							rules: {
								required: true,
								message: '请选择学习时间',
								trigger: 'change'
							},
							index: 1
						},
						{
							label: '学历',
							name: 'degree',
							type: 'text',
							//readonly:true,
							verify: 'required',
							col: 6,
							rules: {
								required: true,
								message: '请填写学历',
								trigger: 'change'
							},
							index: 1
						},
						{
							label: '学习经历',
							name: 'degreeExperience',
							type: 'textarea',
							//readonly:true,
							rows: 5,
							col: 12,
							rules: {
								required: true,
								message: '请填写学习经历',
								trigger: 'change'
							},
							index: 1
						}
					]
				};
				let wordFormList = {
					title: [
						{
							type: 'text',
							label: '工作经历:'
						},
						{
							icon: 'es-icon-jiahao',
							event: (res, index) => {
								let number = (res.contents.length + 1) / 5;
								let arr = [
									{
										label: '',
										name: `zhanwei2${number}`,
										type: 'text',
										col: 12,
										filtration: '1',
										index: number + 1,
										sendName: ''
									},
									{
										label: '公司',
										name: `company${number}`,
										type: 'text',
										//readonly:true,
										verify: 'required',
										col: 6,
										rules: {
											required: true,
											message: '请填写公司',
											trigger: 'change'
										},
										index: number + 1,
										sendName: 'company'
									},
									{
										label: '职务',
										name: `mentorJob${number}`,
										type: 'text',
										//readonly:true,
										verify: 'required',
										col: 6,
										rules: {
											required: true,
											message: '请填写职务',
											trigger: 'change'
										},
										index: number + 1,
										sendName: 'mentorJob'
									},
									{
										name: `workTime${number}`,
										placeholder: '请选择工作时间',
										disabled: this.reissue,
										col: 6,
										label: '工作时间',
										type: 'daterange',
										unlinkPanels: true,
										value: '',
										rules: {
											required: true,
											message: '请选择工作时间',
											trigger: 'change'
										},
										index: number + 1,
										sendName: 'workTime'
									},
									{
										label: '工作经历',
										name: `jobExperience${number}`,
										type: 'textarea',
										//readonly:true,
										rows: 5,
										col: 12,
										rules: {
											required: true,
											message: '请填写工作经历',
											trigger: 'change'
										},
										index: number + 1,
										sendName: 'jobExperience'
									}
								];
								res.contents.push(...arr);
							}
						},
						{
							icon: 'es-icon-jianhao',
							event: (res, index) => {
								if (res.contents.length === 4) {
									return;
								}
								res.contents.splice(-5, 5);
							}
						}
					],
					contents: [
						{
							label: '公司',
							name: 'company',
							type: 'text',
							//readonly:true,
							verify: 'required',
							col: 6,
							rules: {
								required: true,
								message: '请填写公司',
								trigger: 'change'
							},
							index: 1
						},
						{
							label: '职务',
							name: 'mentorJob',
							type: 'text',
							//readonly:true,
							verify: 'required',
							col: 6,
							rules: {
								required: true,
								message: '请填写职务',
								trigger: 'change'
							},
							index: 1
						},
						{
							name: `workTime`,
							placeholder: '请选择工作时间',
							disabled: this.reissue,
							col: 6,
							label: '工作时间',
							type: 'daterange',
							unlinkPanels: true,
							value: '',
							rules: {
								required: true,
								message: '请选择工作时间',
								trigger: 'change'
							},
							index: 1
						},
						{
							label: '工作经历',
							name: 'jobExperience',
							type: 'textarea',
							//readonly:true,
							rows: 5,
							col: 12,
							rules: {
								required: true,
								message: '请填写工作经历',
								trigger: 'change'
							},
							index: 1
						}
					]
				};
				let projectFormList = {
					title: [
						{
							type: 'text',
							label: '参与项目:'
						},
						{
							icon: 'es-icon-jiahao',
							event: (res, index) => {
								let number = (res.contents.length + 1) / 3;
								let arr = [
									{
										label: '',
										name: `zhanwei3${number}`,
										type: 'text',
										col: 12,
										filtration: '1',
										index: number + 1,
										sendName: ''
									},
									{
										label: '项目名称',
										name: `projectName${number}`,
										type: 'text',
										verify: 'required',
										col: 12,
										rules: {
											required: true,
											message: '请填写项目名称',
											trigger: 'change'
										},
										index: number + 1,
										sendName: 'projectName'
										//readonly:true,
									},
									{
										label: '项目介绍',
										name: `projectIntroduction${number}`,
										type: 'textarea',
										//readonly:true,
										rows: 5,
										col: 12,
										rules: {
											required: true,
											message: '请填写项目介绍',
											trigger: 'change'
										},
										index: number + 1,
										sendName: 'projectIntroduction'
									}
								];
								res.contents.push(...arr);
							}
						},
						{
							icon: 'es-icon-jianhao',
							event: (res, index) => {
								if (res.contents.length === 2) {
									return;
								}
								res.contents.splice(-3, 3);
							}
						}
					],
					contents: [
						{
							label: '项目名称',
							name: 'projectName',
							type: 'text',
							verify: 'required',
							col: 12,
							rules: {
								required: true,
								message: '请填写项目名称',
								trigger: 'change'
							},
							index: 1
							//readonly:true,
						},
						{
							label: '项目介绍',
							name: 'projectIntroduction',
							type: 'textarea',
							//readonly:true,
							rows: 5,
							col: 12,
							rules: {
								required: true,
								message: '请填写项目介绍',
								trigger: 'change'
							},
							index: 1
						}
					]
				};
				let tagFormList = {
					title: [
						{
							type: 'text',
							label: '人物标签:'
						},
						{
							icon: 'es-icon-jiahao',
							event: (res, index) => {
								let number = (res.contents.length + 1) / 2;
								let arr = [
									{
										label: '',
										name: `zhanwei4${number}`,
										type: 'text',
										col: 12,
										filtration: '1',
										index: number + 1,
										sendName: ''
									},
									{
										label: `标签${number + 1}`,
										name: `tag${number}`,
										type: 'text',
										verify: 'required',
										col: 12,
										index: number + 1,
										sendName: 'tag'
										//readonly:true,
									}
								];
								res.contents.push(...arr);
							}
						},
						{
							icon: 'es-icon-jianhao',
							event: (res, index) => {
								if (res.contents.length === 1) {
									return;
								}
								res.contents.splice(-2, 2);
							}
						}
					],
					contents: [
						{
							label: '标签',
							name: 'tag',
							type: 'text',
							verify: 'required',
							col: 12,
							index: 1
							//readonly:true,
						}
					]
				};
				indexFormList.push(educationFormList);
				indexFormList.push(wordFormList);
				indexFormList.push(projectFormList);
				indexFormList.push(tagFormList);
				this.educationFormList = educationFormList.contents;
				this.wordFormList = wordFormList.contents;
				this.projectFormList = projectFormList.contents;
				this.tagFormList = tagFormList.contents;
			} else {
				indexFormList = this.interfaceList;
			}
			return indexFormList;
		}
	},
	created() {
		let formDataInterface = {};
		if (this.type == '新增') {
		} else {
			const loadingText = this.$loading({
				target: '.esFrom',
				lock: true,
				text: '获取中',
				spinner: 'el-icon-loading',
				background: 'rgba(0, 0, 0, 0.0)'
			});
			let id = this.selectInfo;
			this.$request({
				url: httpApi.info + '/' + id,
				method: 'GET'
			}).then(res => {
				loadingText.close();
				let obj = {};
				obj = res.results;
				//--------
				let objForList = {
					title: '导师基本信息',
					contents: [
						{
							label: '导师头像',
							name: 'mentorHead',
							type: 'attachment',
							verify: 'required',
							code: 'job_mentor_head',
							ownId: 'png_1', // 业务id
							// size: 100,
							portrait: true,
							param: {
								isShowPath: true
							},
							col: 12
						},
						{
							label: '导师姓名',
							name: 'name',
							type: 'text',
							//readonly:true,
							verify: 'required',
							col: 6,
							rules: {
								required: true,
								message: '请填写导师姓名',
								trigger: 'change'
							}
						},

						{
							label: '所属职务',
							name: 'subordinateMentorJob',
							type: 'text',
							//readonly:true,
							verify: 'required',
							col: 6,
							rules: {
								required: true,
								message: '请填写所属职务',
								trigger: 'change'
							}
						},
						{
							label: '所属公司',
							name: 'subordinateCompany',
							type: 'text',
							//readonly:true,
							verify: 'required',
							col: 12,
							rules: {
								required: true,
								message: '请填写所属公司',
								trigger: 'change'
							}
						}
					]
				};
				this.interfaceList.push(objForList);
				//教育背景
				let educationFormList = {};
				(educationFormList.title = [
					{
						type: 'text',
						label: '教育背景:'
					},
					{
						icon: 'es-icon-jiahao',
						event: (res, index) => {
							let number = (res.contents.length + 1) / 6;
							let arr = [
								{
									label: '',
									name: `zhanwei1${number}`,
									type: 'text',
									col: 12,
									filtration: '1',
									index: number + 1,
									sendName: ''
								},
								{
									label: '学校',
									name: `university${number}`,
									type: 'text',
									//readonly:true,
									verify: 'required',
									col: 6,
									rules: {
										required: true,
										message: '请填写学校',
										trigger: 'change'
									},
									index: number + 1,
									sendName: 'university'
								},
								{
									label: '专业',
									name: `major${number}`,
									type: 'text',
									//readonly:true,
									verify: 'required',
									col: 6,
									rules: {
										required: true,
										message: '请填写专业',
										trigger: 'change'
									},
									index: number + 1,
									sendName: 'major'
								},
								{
									name: `stydyTime${number}`,
									placeholder: '请选择学习时间',
									disabled: this.reissue,
									col: 6,
									label: '学习时间',
									type: 'daterange',
									unlinkPanels: true,
									value: '',
									rules: {
										required: true,
										message: '请选择开始学习时间',
										trigger: 'change'
									},
									index: number + 1,
									sendName: 'stydyTime'
								},
								{
									label: '学历',
									name: `degree${number}`,
									type: 'text',
									//readonly:true,
									verify: 'required',
									col: 6,
									rules: {
										required: true,
										message: '请填写学历',
										trigger: 'change'
									},
									index: number + 1,
									sendName: 'degree'
								},
								{
									label: '学习经历',
									name: `degreeExperience${number}`,
									type: 'textarea',
									//readonly:true,
									rows: 5,
									col: 12,
									rules: {
										required: true,
										message: '请填写学习经历',
										trigger: 'change'
									},
									index: number + 1,
									sendName: 'degreeExperience'
								}
							];
							res.contents.push(...arr);
						}
					},
					{
						icon: 'es-icon-jianhao',
						event: (res, index) => {
							if (res.contents.length === 5) {
								return;
							}
							res.contents.splice(-6, 6);
						}
					}
				]),
					(educationFormList.contents = []);
				obj.educationFormList.forEach((item, index) => {
					let code = '';
					if (index != 0) {
						code = index;
					}
					let arr = [];
					if (index == 0) {
						arr = [
							{
								label: '学校',
								name: `university${code}`,
								type: 'text',
								//readonly:true,
								verify: 'required',
								col: 6,
								rules: {
									required: true,
									message: '请填写学校',
									trigger: 'change'
								},
								index: index + 1
							},
							{
								label: '专业',
								name: `major${code}`,
								type: 'text',
								//readonly:true,
								verify: 'required',
								col: 6,
								rules: {
									required: true,
									message: '请填写专业',
									trigger: 'change'
								},
								index: index + 1
							},
							{
								name: `stydyTime${code}`,
								placeholder: '请选择学习时间',
								disabled: this.reissue,
								col: 6,
								label: '学习时间',
								type: 'daterange',
								unlinkPanels: true,
								value: '',
								rules: {
									required: true,
									message: '请选择学习时间',
									trigger: 'change'
								},
								index: index + 1
							},
							{
								label: '学历',
								name: `degree${code}`,
								type: 'text',
								//readonly:true,
								verify: 'required',
								col: 6,
								rules: {
									required: true,
									message: '请填写学历',
									trigger: 'change'
								},
								index: index + 1
							},
							{
								label: '学习经历',
								name: `degreeExperience${code}`,
								type: 'textarea',
								//readonly:true,
								rows: 5,
								col: 12,
								rules: {
									required: true,
									message: '请填写学习经历',
									trigger: 'change'
								},
								index: index + 1
							}
						];
					} else {
						arr = [
							{
								label: '',
								name: `jiaoyu${index}`,
								type: 'text',
								col: 12,
								filtration: '1',
								index: index + 1,
								sendName: ''
							},
							{
								label: '学校',
								name: `university${code}`,
								type: 'text',
								//readonly:true,
								verify: 'required',
								col: 6,
								rules: {
									required: true,
									message: '请填写学校',
									trigger: 'change'
								},
								index: index + 1,
								sendName: 'university'
							},
							{
								label: '专业',
								name: `major${code}`,
								type: 'text',
								//readonly:true,
								verify: 'required',
								col: 6,
								rules: {
									required: true,
									message: '请填写专业',
									trigger: 'change'
								},
								index: index + 1,
								sendName: 'major'
							},
							{
								name: `stydyTime${code}`,
								placeholder: '请选择学习时间',
								disabled: this.reissue,
								col: 6,
								label: '学习时间',
								type: 'daterange',
								unlinkPanels: true,
								value: '',
								rules: {
									required: true,
									message: '请选择学习时间',
									trigger: 'change'
								},
								index: index + 1,
								sendName: 'stydyTime'
							},
							{
								label: '学历',
								name: `degree${code}`,
								type: 'text',
								//readonly:true,
								verify: 'required',
								col: 6,
								rules: {
									required: true,
									message: '请填写学历',
									trigger: 'change'
								},
								index: index + 1,
								sendName: 'degree'
							},
							{
								label: '学习经历',
								name: `degreeExperience${code}`,
								type: 'textarea',
								//readonly:true,
								rows: 5,
								col: 12,
								rules: {
									required: true,
									message: '请填写学习经历',
									trigger: 'change'
								},
								index: index + 1,
								sendName: 'degreeExperience'
							}
						];
					}
					educationFormList.contents.push(...arr);
				});
				this.interfaceList.push(educationFormList);
				//工作经历
				let wordFormList = {};
				(wordFormList.title = [
					{
						type: 'text',
						label: '工作经历:'
					},
					{
						icon: 'es-icon-jiahao',
						event: (res, index) => {
							let number = (res.contents.length + 1) / 5;
							let arr = [
								{
									label: '',
									name: `zhanwei2${number}`,
									type: 'text',
									col: 12,
									filtration: '1',
									index: number + 1,
									sendName: ''
								},
								{
									label: '公司',
									name: `company${number}`,
									type: 'text',
									//readonly:true,
									verify: 'required',
									col: 6,
									rules: {
										required: true,
										message: '请填写公司',
										trigger: 'change'
									},
									index: number + 1,
									sendName: 'company'
								},
								{
									label: '职务',
									name: `mentorJob${number}`,
									type: 'text',
									//readonly:true,
									verify: 'required',
									col: 6,
									rules: {
										required: true,
										message: '请填写职务',
										trigger: 'change'
									},
									index: number + 1,
									sendName: 'mentorJob'
								},
								{
									name: `workTime${number}`,
									placeholder: '请选择工作时间',
									disabled: this.reissue,
									col: 6,
									label: '工作时间',
									type: 'daterange',
									unlinkPanels: true,
									value: '',
									rules: {
										required: true,
										message: '请选择工作时间',
										trigger: 'change'
									},
									index: number + 1,
									sendName: 'workTime'
								},
								{
									label: '工作经历',
									name: `jobExperience${number}`,
									type: 'textarea',
									//readonly:true,
									rows: 5,
									col: 12,
									rules: {
										required: true,
										message: '请填写工作经历',
										trigger: 'change'
									},
									index: number + 1,
									sendName: 'jobExperience'
								}
							];
							res.contents.push(...arr);
						}
					},
					{
						icon: 'es-icon-jianhao',
						event: (res, index) => {
							if (res.contents.length === 4) {
								return;
							}
							res.contents.splice(-5, 5);
						}
					}
				]),
					(wordFormList.contents = []);
				obj.wordFormList.forEach((item, index) => {
					let code = '';
					if (index != 0) {
						code = index;
					}
					let arr = [];
					if (index == 0) {
						arr = [
							{
								label: '公司',
								name: `company${code}`,
								type: 'text',
								//readonly:true,
								verify: 'required',
								col: 6,
								rules: {
									required: true,
									message: '请填写公司',
									trigger: 'change'
								},
								index: index + 1
							},
							{
								label: '职务',
								name: `mentorJob${code}`,
								type: 'text',
								//readonly:true,
								verify: 'required',
								col: 6,
								rules: {
									required: true,
									message: '请填写职务',
									trigger: 'change'
								},
								index: index + 1
							},
							{
								name: `workTime${code}`,
								placeholder: '请选择工作时间',
								disabled: this.reissue,
								col: 6,
								label: '工作时间',
								type: 'daterange',
								unlinkPanels: true,
								value: '',
								rules: {
									required: true,
									message: '请选择工作时间',
									trigger: 'change'
								},
								index: index + 1
							},
							{
								label: '工作经历',
								name: `jobExperience${code}`,
								type: 'textarea',
								//readonly:true,
								verify: 'required',
								rows: 5,
								col: 12,
								rules: {
									required: true,
									message: '请填写工作经历',
									trigger: 'change'
								},
								index: index + 1
							}
						];
					} else {
						arr = [
							{
								label: '',
								name: `gongzuojj${index}`,
								type: 'text',
								col: 12,
								filtration: '1',
								index: index + 1,
								sendName: ''
							},
							{
								label: '公司',
								name: `company${code}`,
								type: 'text',
								//readonly:true,
								verify: 'required',
								col: 6,
								rules: {
									required: true,
									message: '请填写公司',
									trigger: 'change'
								},
								index: index + 1,
								sendName: 'company'
							},
							{
								label: '职务',
								name: `mentorJob${code}`,
								type: 'text',
								//readonly:true,
								verify: 'required',
								col: 6,
								rules: {
									required: true,
									message: '请填写职务',
									trigger: 'change'
								},
								index: index + 1,
								sendName: 'mentorJob'
							},
							{
								name: `workTime${code}`,
								placeholder: '请选择工作时间',
								disabled: this.reissue,
								col: 6,
								label: '工作时间',
								type: 'daterange',
								unlinkPanels: true,
								value: '',
								rules: {
									required: true,
									message: '请选择工作时间',
									trigger: 'change'
								},
								index: index + 1,
								sendName: 'workTime'
							},
							{
								label: '工作经历',
								name: `jobExperience${code}`,
								type: 'textarea',
								//readonly:true,
								verify: 'required',
								rows: 5,
								col: 12,
								rules: {
									required: true,
									message: '请填写工作经历',
									trigger: 'change'
								},
								index: index + 1,
								sendName: 'jobExperience'
							}
						];
					}
					wordFormList.contents.push(...arr);
				});
				this.interfaceList.push(wordFormList);
				//参与项目
				let projectFormList = {};
				(projectFormList.title = [
					{
						type: 'text',
						label: '参与项目:'
					},
					{
						icon: 'es-icon-jiahao',
						event: (res, index) => {
							let number = (res.contents.length + 1) / 3;
							let arr = [
								{
									label: '',
									name: `zhanwei3${number}`,
									type: 'text',
									col: 12,
									filtration: '1',
									index: number + 1,
									sendName: ''
								},
								{
									label: '项目名称',
									name: `projectName${number}`,
									type: 'text',
									verify: 'required',
									col: 12,
									rules: {
										required: true,
										message: '请填写项目名称',
										trigger: 'change'
									},
									index: number + 1,
									sendName: 'projectName'
									//readonly:true,
								},
								{
									label: '项目介绍',
									name: `projectIntroduction${number}`,
									type: 'textarea',
									//readonly:true,
									rows: 5,
									col: 12,
									rules: {
										required: true,
										message: '请填写项目介绍',
										trigger: 'change'
									},
									index: number + 1,
									sendName: 'projectIntroduction'
								}
							];
							res.contents.push(...arr);
						}
					},
					{
						icon: 'es-icon-jianhao',
						event: (res, index) => {
							if (res.contents.length === 2) {
								return;
							}
							res.contents.splice(-3, 3);
						}
					}
				]),
					(projectFormList.contents = []);
				obj.projectFormList.forEach((item, index) => {
					let code = '';
					if (index != 0) {
						code = index;
					}
					let arr = [];
					if (index == 0) {
						arr = [
							{
								label: '项目名称',
								name: `projectName${code}`,
								type: 'text',
								//readonly:true,
								verify: 'required',
								col: 12,
								rules: {
									required: true,
									message: '请填写项目名称',
									trigger: 'change'
								},
								index: index + 1
							},
							{
								label: '项目介绍',
								name: `projectIntroduction${code}`,
								type: 'text',
								//readonly:true,
								verify: 'required',
								rows: 5,
								col: 12,
								rules: {
									required: true,
									message: '请填写项目介绍',
									trigger: 'change'
								},
								index: index + 1
							}
						];
					} else {
						arr = [
							{
								label: '',
								name: `canyuxiangmu${index}`,
								type: 'text',
								col: 12,
								filtration: '1',
								index: index + 1,
								sendName: ''
							},
							{
								label: '项目名称',
								name: `projectName${code}`,
								type: 'text',
								//readonly:true,
								verify: 'required',
								col: 12,
								rules: {
									required: true,
									message: '请填写项目名称',
									trigger: 'change'
								},
								index: index + 1,
								sendName: 'projectName'
							},
							{
								label: '项目介绍',
								name: `projectIntroduction${code}`,
								type: 'text',
								//readonly:true,
								verify: 'required',
								rows: 5,
								col: 12,
								rules: {
									required: true,
									message: '请填写项目介绍',
									trigger: 'change'
								},
								index: index + 1,
								sendName: 'projectIntroduction'
							}
						];
					}
					projectFormList.contents.push(...arr);
				});
				this.interfaceList.push(projectFormList);
				//标签
				let tagFormList = {};
				(tagFormList.title = [
					{
						type: 'text',
						label: '人物标签:'
					},
					{
						icon: 'es-icon-jiahao',
						event: (res, index) => {
							let number = (res.contents.length + 1) / 2;
							let arr = [
								{
									label: '',
									name: `zhanwei4${number}`,
									type: 'text',
									col: 12,
									filtration: '1',
									index: number + 1,
									sendName: ''
								},
								{
									label: `标签${number + 1}`,
									name: `tag${number}`,
									type: 'text',
									verify: 'required',
									col: 12,
									index: number + 1,
									sendName: 'tag'
									//readonly:true,
								}
							];
							res.contents.push(...arr);
						}
					},
					{
						icon: 'es-icon-jianhao',
						event: (res, index) => {
							if (res.contents.length === 1) {
								return;
							}
							res.contents.splice(-2, 2);
						}
					}
				]),
					(tagFormList.contents = []);
				obj.tagFormList.forEach((item, index) => {
					let code = '';
					if (index != 0) {
						code = index;
					}
					let arr = [];
					if (index == 0) {
						arr = [
							{
								label: '标签',
								name: `tag${code}`,
								type: 'text',
								//readonly:true,
								verify: 'required',
								col: 12,
								rules: {
									required: true,
									message: '请填写标签',
									trigger: 'change'
								},
								index: index + 1
							}
						];
					} else {
						arr = [
							{
								label: '',
								name: `biaoqian${index}`,
								type: 'text',
								col: 12,
								filtration: '1',
								index: index + 1,
								sendName: ''
							},
							{
								label: '标签',
								name: `tag${code}`,
								type: 'text',
								//readonly:true,
								verify: 'required',
								col: 12,
								rules: {
									required: true,
									message: '请填写标签',
									trigger: 'change'
								},
								index: index + 1,
								sendName: 'tag'
							}
						];
					}
					tagFormList.contents.push(...arr);
				});
				this.interfaceList.push(tagFormList);
				this.educationFormList = educationFormList.contents;
				this.wordFormList = wordFormList.contents;
				this.projectFormList = projectFormList.contents;
				this.tagFormList = tagFormList.contents;
				//formData
				for (let key in obj) {
					if (Array.isArray(obj[key])) {
						obj[key].forEach((item, index) => {
							let code = '';
							if (index != 0) {
								code = index;
							}
							for (let k in item) {
								formDataInterface[k + code] = item[k];
							}
						});
					} else {
						for (let i in obj[key]) {
							formDataInterface[i] = obj[key][i];
						}
					}
				}
				this.formData = formDataInterface;
				// this.interfaceList
				// this.formList = this.interfaceList
			});
			// let obj = {
			// 	basicInfo: {
			// 		name: '1212',
			// 		subordinateMentorJob: '222',
			// 		subordinateCompany: '222'
			// 	},
			// 	educationFormList: [
			// 		{
			// 			university: '1212',
			// 			major: '1212',
			// 			stydyTime: ['2023-08-07', '2023-09-03'],
			// 			degree: '1312',
			// 			degreeExperience: '131313'
			// 		},
			// 		{
			// 			university: '1313',
			// 			major: '13',
			// 			stydyTime: ['2023-08-15', '2023-09-19'],
			// 			degree: '1313',
			// 			degreeExperience: '1313'
			// 		}
			// 	],
			// 	wordFormList: [
			// 		{
			// 			company: '1212',
			// 			mentorJob: '131',
			// 			workTime: ['2023-08-08', '2023-09-06'],
			// 			jobExperience: '1313'
			// 		}
			// 	],
			// 	projectFormList: [
			// 		{
			// 			projectName: '13',
			// 			projectIntroduction: '1313'
			// 		}
			// 	],
			// 	tagFormList: [
			// 		{
			// 			tag: '1313'
			// 		}
			// 	]
			// };
		}
	},
	methods: {
		handleSubmit(data, obj) {
			console.log(this.formData, 'this.formData');
			let SendObj = {
				basicInfo: {},
				educationFormList: [],
				wordFormList: [],
				projectFormList: [],
				tagFormList: []
			};
			if (this.type == '修改') {
				SendObj.basicInfo.id = this.selectInfo;
			}
			SendObj.basicInfo.name = this.formData.name;
			SendObj.basicInfo.subordinateMentorJob = this.formData.subordinateMentorJob;
			SendObj.basicInfo.subordinateCompany = this.formData.subordinateCompany;
			let changeIndex = 1;
			let sendObjItem = {};
			this.educationFormList.forEach((item, index) => {
				if (item.filtration == '1') {
					return;
				}
				if (item.index == changeIndex) {
					if (item.index != 1) {
						sendObjItem[item.sendName] = this.formData[item.name];
					} else {
						sendObjItem[item.name] = this.formData[item.name];
					}
				} else {
					SendObj.educationFormList.push(sendObjItem);
					sendObjItem = {};
					changeIndex = item.index;
					sendObjItem[item.sendName] = this.formData[item.name];
				}
				if (index == this.educationFormList.length - 1) {
					SendObj.educationFormList.push(sendObjItem);
				}
			});
			sendObjItem = {};
			changeIndex = 1;
			this.wordFormList.forEach((item, index) => {
				if (item.filtration == '1') {
					return;
				}
				if (item.index == changeIndex) {
					if (item.index != 1) {
						sendObjItem[item.sendName] = this.formData[item.name];
					} else {
						sendObjItem[item.name] = this.formData[item.name];
					}
				} else {
					SendObj.wordFormList.push(sendObjItem);
					sendObjItem = {};
					changeIndex = item.index;
					sendObjItem[item.sendName] = this.formData[item.name];
				}
				if (index == this.wordFormList.length - 1) {
					SendObj.wordFormList.push(sendObjItem);
				}
			});
			sendObjItem = {};
			changeIndex = 1;
			this.projectFormList.forEach((item, index) => {
				if (item.filtration == '1') {
					return;
				}
				if (item.index == changeIndex) {
					if (item.index != 1) {
						sendObjItem[item.sendName] = this.formData[item.name];
					} else {
						sendObjItem[item.name] = this.formData[item.name];
					}
				} else {
					SendObj.projectFormList.push(sendObjItem);
					sendObjItem = {};
					changeIndex = item.index;
					sendObjItem[item.sendName] = this.formData[item.name];
				}
				if (index == this.projectFormList.length - 1) {
					SendObj.projectFormList.push(sendObjItem);
				}
			});
			sendObjItem = {};
			changeIndex = 1;
			this.tagFormList.forEach((item, index) => {
				if (item.filtration == '1') {
					return;
				}
				if (item.index == changeIndex) {
					if (item.index != 1) {
						sendObjItem[item.sendName] = this.formData[item.name];
					} else {
						sendObjItem[item.name] = this.formData[item.name];
					}
				} else {
					SendObj.tagFormList.push(sendObjItem);
					sendObjItem = {};
					changeIndex = item.index;
					sendObjItem[item.sendName] = this.formData[item.name];
				}
				if (index == this.tagFormList.length - 1) {
					SendObj.tagFormList.push(sendObjItem);
				}
			});
			console.log(SendObj, 'SendObj');
			let dataParams = JSON.stringify(SendObj);
			if (this.type === '新增') {
				const loading = $.loading(this.$loading, '提交中');
				this.$request({
					url: httpApi.jobCoPioneerMentorSave,
					data: { params: dataParams },
					method: 'POST'
				}).then(res => {
					loading.close();
					this.$message.success('操作成功');
					this.$emit('closeEdit');
				});
			} else if (this.type === '修改') {
				const loading = $.loading(this.$loading, '提交中');
				this.$request({
					url: httpApi.jobCoPioneerMentorUpdate,
					data: { params: dataParams },
					method: 'POST'
				}).then(res => {
					loading.close();
					this.$message.success('操作成功');
					this.$emit('closeEdit');
				});
			}
		}
	}
};
</script>
<style lang="scss" scoped>
::v-deep .el-scrollbar__view :nth-child(2).el-collapse-item.es-collapse-item.is-active {
	.el-collapse-item__wrap {
		.el-collapse-item__content :nth-child(6) {
			height: 2px;
			// padding: 0 0 20px 0;
			border-top: 1px solid #d9d9d9;
			.el-form-item__content {
				display: none;
			}
		}
		.el-collapse-item__content :nth-child(12) {
			height: 2px;
			// padding: 0 0 20px 0;
			border-top: 1px solid #d9d9d9;
			.el-form-item__content {
				display: none;
			}
		}
		.el-collapse-item__content :nth-child(18) {
			height: 2px;
			// padding: 0 0 20px 0;
			border-top: 1px solid #d9d9d9;
			.el-form-item__content {
				display: none;
			}
		}
		.el-collapse-item__content :nth-child(24) {
			height: 2px;
			// padding: 0 0 20px 0;
			border-top: 1px solid #d9d9d9;
			.el-form-item__content {
				display: none;
			}
		}
	}
}
::v-deep .el-scrollbar__view :nth-child(3).el-collapse-item.es-collapse-item.is-active {
	.el-collapse-item__wrap {
		.el-collapse-item__content :nth-child(5) {
			height: 2px;
			// padding: 0 0 20px 0;
			border-top: 1px solid #d9d9d9;
			.el-form-item__content {
				display: none;
			}
		}
		.el-collapse-item__content :nth-child(10) {
			height: 2px;
			// padding: 0 0 20px 0;
			border-top: 1px solid #d9d9d9;
			.el-form-item__content {
				display: none;
			}
		}
		.el-collapse-item__content :nth-child(15) {
			height: 2px;
			// padding: 0 0 20px 0;
			border-top: 1px solid #d9d9d9;
			.el-form-item__content {
				display: none;
			}
		}
		.el-collapse-item__content :nth-child(20) {
			height: 2px;
			// padding: 0 0 20px 0;
			border-top: 1px solid #d9d9d9;
			.el-form-item__content {
				display: none;
			}
		}
	}
}
::v-deep .el-scrollbar__view :nth-child(4).el-collapse-item.es-collapse-item.is-active {
	.el-collapse-item__wrap {
		.el-collapse-item__content :nth-child(3) {
			height: 2px;
			// padding: 0 0 20px 0;
			border-top: 1px solid #d9d9d9;
			.el-form-item__content {
				display: none;
			}
		}
		.el-collapse-item__content :nth-child(6) {
			height: 2px;
			// padding: 0 0 20px 0;
			border-top: 1px solid #d9d9d9;
			.el-form-item__content {
				display: none;
			}
		}
		.el-collapse-item__content :nth-child(9) {
			height: 2px;
			// padding: 0 0 20px 0;
			border-top: 1px solid #d9d9d9;
			.el-form-item__content {
				display: none;
			}
		}
		.el-collapse-item__content :nth-child(12) {
			height: 2px;
			// padding: 0 0 20px 0;
			border-top: 1px solid #d9d9d9;
			.el-form-item__content {
				display: none;
			}
		}
	}
}
::v-deep .el-scrollbar__view :nth-child(5).el-collapse-item.es-collapse-item.is-active {
	.el-collapse-item__wrap {
		.el-collapse-item__content :nth-child(2) {
			height: 2px;
			.el-form-item__content {
				display: none;
			}
		}
		.el-collapse-item__content :nth-child(4) {
			height: 2px;
			.el-form-item__content {
				display: none;
			}
		}
		.el-collapse-item__content :nth-child(6) {
			height: 2px;
			.el-form-item__content {
				display: none;
			}
		}
		.el-collapse-item__content :nth-child(8) {
			height: 2px;
			.el-form-item__content {
				display: none;
			}
		}
	}
}
</style>
