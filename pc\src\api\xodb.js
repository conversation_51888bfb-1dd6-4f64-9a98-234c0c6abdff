/**
 * @Author: <PERSON><PERSON> <EMAIL>
 * @Date: 2023-05-09 10:59:45
 * @LastEditors: <PERSON><PERSON> <EMAIL>
 * @LastEditTime: 2023-11-20 16:29:24
 * @FilePath: /ybzy-screen/src/api/xodb.js
 * @Description: xodbAPI
 */

import qs from 'qs';
import request from '@/utils/request-xodb';
import request2 from '@/utils/request-xodb2';
import store from '@/store';

export const xodbApi = {
	get(data) {
		const xodbToken = store.getters.xodbToken;
		const xodbParams = {
			...data,
			token: xodbToken || 'P7J1YCAmhOvXGEHGPATShXGQ/kVS4hb06k/vmLlCGMzTYuVqJB15AWJ8UQxkdxBO'
		};
		return request({
			url: '/xodbapi/wiseSafe/dataQuery/selectUserSelfData',
			method: 'post',
			data: qs.stringify(xodbParams)
		});
	}
};
export const xodbApi2 = {
	get(url, data) {
		// const xodbToken = store.getters.xodbToken;
		const xodbParams = {
			...data
			// userCode: 'ybzysjdp',
			// userToken: 'LEXF4eV+4YJe3TuDRnTszBJ6LIFtmazd/CYELx1qnVNBmYxJfhTCcBTcpsXbL5bI'
		};
		return request2({
			url: url,
			method: 'post',
			data: qs.stringify(xodbParams)
		});
	},
	post(data) {
		return request2({
			// url: `/dist/api/xodb-datashare/dataShare/${data.url}/1/${data.pageSize || 10}`,
			url: `/wds/api/xodb-datashare/dataShare/serviceApi/data/${data.url}/1/${data.pageSize || 10}`,
			method: 'post',
			type: 'JSON',
			data: data.params || {}
		});
	}
};
// 用户登录
export const xodbAuthorization = {
	get(params) {
		return request({
			url: '/xodbapi/authServer/login/authorize',
			method: 'get',
			params
		});
	}
};
