<!--
 @desc: 项目详情 办理中心用入参通过url地址栏获取
 @author: WH
 @date: 2023/11/23
 -->
<template>
	<es-tabs v-model="activeName" @tab-click="handleClick">
		<el-tab-pane label="基本信息" name="BasicInfo">
			<basic-info v-if="activeName == 'BasicInfo'" />
		</el-tab-pane>
		<el-tab-pane label="资金信息" name="CapitalInfo">
			<capital-info v-if="activeName == 'CapitalInfo'" />
		</el-tab-pane>
		<el-tab-pane label="变更信息" name="ChangeInfo">
			<change-info />
		</el-tab-pane>
	</es-tabs>
</template>
<script>
import BasicInfo from './basic-info/index.vue';
import CapitalInfo from './capital-info/index.vue';
import ChangeInfo from './change-info/index.vue';

export default {
	name: 'scientificDeailUrl',
	components: {
		BasicInfo,
		CapitalInfo,
		ChangeInfo
	},
	provide() {
		return {
			id: this.$.getParams().projectId,
			openType: 'look',
			initActiveName: this.activeName
		};
	},
	data() {
		return {
			activeName: 'BasicInfo',
		};
	},
	methods: {
		reload(reload) {
			this.$emit('reload', reload);
		},
		handleClick(tab, event) {
			console.log(tab, event);
		}
	}
};
</script>
