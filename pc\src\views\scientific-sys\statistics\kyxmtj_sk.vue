<template>
	<div v-loading="loading" class="content">
		<es-data-table
			ref="table"
			:row-style="tableRowClassName"
			:thead="thead"
			:toolbar="toolbar"
			:border="true"
			:page="pageOption"
			:url="basics.dataTableUrl"
			filter
			:numbers="true"
			show-label
			:param="params"
			close
			@search="search"
			@reset="search({})"
			@btnClick="btnClick"
		></es-data-table>
	</div>
</template>

<script>
import { host } from '@/../config/config';
import qs from 'qs';
export default {
	data() {
		return {
			basics: {
				dataTableUrl: '/ybzy/projectBaseInfo/queryPageMap', // 列表接口on/deleteById', // 删除 removeById逻辑删除 deleteById真删
				download: '/ybzy/projectBaseInfo/exportStatistics' // 导出
			},
			loading: false,
			params: {
				projectType: 'social',
				orderBy: 'createTime',
				asc: 'false' // true 升序，false 降序
			},
			pageOption: {
				layout: 'total, prev, pager, next, jumper',
				pageSize: 20,
				// // hideOnSinglePage: true,
				position: 'center',
				current: 1,
				pageNum: 1
			},
			codesObj: {} //批量数据字典
		};
	},
	computed: {
		thead() {
			return [
				{
					title: '项目名称',
					align: 'center',
					showOverflowTooltip: true,
					width: 180,
					fixed: 'left',
					field: 'projectName'
				},
				{
					title: '项目批准号',
					align: 'center',
					showOverflowTooltip: true,
					width: 110,
					field: 'ratificationNumber'
				},
				{
					title: '负责人',
					align: 'center',
					showOverflowTooltip: true,
					width: 80,
					field: 'principal'
				},
				{
					title: '所属机构',
					align: 'center',
					showOverflowTooltip: true,
					width: 110,
					field: 'orgName'
				},
				{
					title: '批准日期',
					align: 'center',
					showOverflowTooltip: true,
					width: 110,
					field: 'approveDate'
				},
				{
					title: '批准经费',
					align: 'center',
					showOverflowTooltip: true,
					width: 100,
					field: 'appropriationApproval'
				},
				{
					title: '只统计后续经费',
					align: 'center',
					showOverflowTooltip: true,
					width: 100,
					field: 'isFollowUpFunds'
				},
				{
					title: '项目来源',
					align: 'center',
					showOverflowTooltip: true,
					width: 160,
					field: 'projectSource'
				},
				{
					title: '一级学科',
					align: 'center',
					showOverflowTooltip: true,
					width: 110,
					field: 'firstLevelDiscipline'
				},
				{
					title: '研究类别',
					align: 'center',
					showOverflowTooltip: true,
					width: 90,
					field: 'studyType'
				},
				{
					title: '合作形式',
					align: 'center',
					showOverflowTooltip: true,
					width: 100,
					field: 'cooform'
				},
				{
					title: '服务的国民经济行业',
					align: 'center',
					showOverflowTooltip: true,
					width: 110,
					field: 'ecoIndustryType'
				},
				{
					title: '社会经济目标',
					align: 'center',
					width: 110,
					showOverflowTooltip: true,
					field: 'ecoGoalType'
				},
				{
					title: '项目状态',
					align: 'center',
					showOverflowTooltip: true,
					width: 80,
					fixed: 'right',
					field: 'state'
				},
				{
					title: '项目编号',
					align: 'center',
					showOverflowTooltip: true,
					width: 110,
					field: 'projectNumber'
				},
				{
					title: '计划结束日期',
					align: 'center',
					showOverflowTooltip: true,
					width: 110,
					field: 'planEndDate'
				},
				{
					title: '实际完成日期',
					align: 'center',
					showOverflowTooltip: true,
					width: 110,
					field: 'actualFinishDate'
				},
				{
					title: '预期成果形式',
					align: 'center',
					showOverflowTooltip: true,
					width: 130,
					field: 'expectedResultsForm'
				},
				{
					title: '最终成果形式',
					align: 'center',
					showOverflowTooltip: true,
					width: 130,
					field: 'finalForm'
				}
			];
		},
		toolbar() {
			return [
				{
					type: 'button',
					contents: [
						{
							text: '导出',
							exportXls: true,
							icon: 'es-icon-daochu',
							type: 'primary',
							event: () => {
								const params = { ...this.params, ...this.selectParams };
								const url = host + this.basics.download + '?' + qs.stringify(params);
								window.open(url, '_self');
							}
						}
					]
				},
				{
					type: 'search',
					reset: true,
					contents: [
						{
							type: 'daterange',
							name: 'dates',
							label: '时间范围',
							placeholder: '请选择时间范围'
						},
						{ type: 'text', label: '关键字', name: 'keyword', placeholder: '请输入关键字查询' }
					]
				}
			];
		}
	},
	created() {
		const codes = 'member_nation,hq_person_education,hq_person_state';
		this.$utils.findSysCodeList(codes).then(res => {
			this.codesObj = res;
		});
	},
	methods: {
		//表格行高
		tableRowClassName({ row, rowIndex }) {
			let styleRes = { height: '54px !important' };
			return styleRes;
		},
		//操作按钮点击事件
		btnClick(res) {},
		search(res) {
			const paramsN = { ...res };
			if (paramsN.dates) {
				paramsN.startDate = paramsN.dates[0] || '';
				paramsN.endDate = paramsN.dates[1] || '';
				delete paramsN.dates;
			}
			this.params = {
				...{
					projectType: this.params.projectType,
					orderBy: this.params.orderBy,
					asc: this.params.asc // true 升序，false 降序
				},
				...paramsN
			};
			this.selectParams = paramsN;
		},
		refreshData() {
			this.$refs.table.reload();
		}
	}
};
</script>

<style scoped lang="scss">
.content {
	width: 100%;
	height: 100%;
}
</style>
