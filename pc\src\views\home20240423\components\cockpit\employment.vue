<template>
	<div class="employment">
		<div class="floor1" @click="onPopup('毕业就业')">
			<div class="describe">
				<div class="describe-val">{{ practiceData.countAll }}</div>
				<div class="describe-name">本学年实习人数</div>
			</div>
			<div v-for="(item, i) of practiceData.list" :key="i" class="info">
				<div class="info-t">
					{{ item.count }}
				</div>
				<div class="info-b">{{ item.name }}</div>
			</div>
		</div>
		<div class="floor2" @click="onPopup('毕业就业')">
			<div class="describe">
				<div class="describe-val">{{ graduateData.countAll }}</div>
				<div class="describe-name">本学年毕业人数</div>
			</div>
			<div v-for="(item, i) of graduateData.list" :key="i" class="info">
				<div class="info-t">
					{{ item.count }}
				</div>
				<div class="info-b">{{ item.name }}</div>
			</div>
		</div>

		<div class="title">学生就业行业分布top</div>
		<div id="myEchart" class="floor3"></div>

		<div class="title">学生实习行业分布top</div>
		<div class="floor4" @click="onPopup('实习')">
			<div v-for="(item, i) of internList" :key="i" class="intern-item">
				<el-tooltip effect="dark" :content="item.name" placement="top-start">
					<div class="intern-item-t">{{ item.name }}</div>
				</el-tooltip>
				<div class="intern-item-c">
					{{ item.count }}
					<span class="unit">人</span>
				</div>
				<div class="intern-item-b">
					<img src="~@/assets/images/home20240423/employment-b.png" alt="" srcset="" />
				</div>
			</div>
		</div>
		<PopEmployment ref="refPopup" :title="title" />
	</div>
</template>

<script>
import PopEmployment from './pop-employment.vue';
import { xodbApi, xodbApi2 } from '@/api/xodb';
export default {
	name: 'Teacher',
	components: { PopEmployment },
	props: {},
	data() {
		return {
			title: '毕业就业', // 毕业就业 实现
			//本学年实习人数
			practiceData: {
				countAll: 0,
				key: 'bxnsxrs',
				list: [
					{
						name: '实习中',
						key: 'sxzzb',
						count: '-'
					},
					{
						name: '实习结束',
						key: 'sxjszb',
						count: '-'
					},
					{
						name: '未实习',
						key: 'wsxzb',
						count: '-'
					}
				]
			},
			//本学年毕业人数
			graduateData: {
				countAll: 0,
				key: 'bxnbyrs',
				list: [
					{
						name: '毕业就业',
						key: 'byjybl',
						count: '-'
					},
					{
						name: '升学',
						key: 'sxbl',
						count: '-'
					},
					{
						name: '未就业',
						key: 'wjybl',
						count: '-'
					}
				]
			},
			//学生实习行业分布top
			internList: [
				{
					name: '-',
					key: '',
					count: '-'
				},
				{
					name: '-',
					key: '',
					count: '-'
				},
				{
					name: '-',
					key: '',
					count: '-'
				},
				{
					name: '-',
					key: '',
					count: '-'
				}
			],
			myChart: null
		};
	},
	computed: {
		loginUserInfo() {
			return JSON.parse(localStorage.getItem('loginUserInfo')) || {};
		}
	},
	created() {},
	mounted() {
		window.addEventListener('resize', this.onResize);
		this.getDataN('ads_xs_xssxqk_query', 'practiceData'); // 实习人数
		this.getDataN('ads_xs_xsbyqxqk_query', 'graduateData'); // 毕业人数
		this.getDataN('ads_xs_xybyrs_query', 'graduateDataAll'); // 本学年毕业人数
		this.getDataN('ads_xs_xybysjyhyfb_query', 'topData'); // 就业榜

		// this.queryIntern(); // 实习榜
	},
	beforeDestroy() {
		window.removeEventListener('resize', this.onResize);
		if (this.myChart) {
			this.myChart.dispose();
		}
	},
	methods: {
		onPopup(title) {
			this.title = title;
			this.$refs.refPopup.dialogVisible = true;
		},

		async getDataN(url, listName) {
			const LeaderRole = localStorage.getItem('LeaderRole');
			try {
				const {
					data: { list }
				} = await xodbApi2.post({
					url,
					params: {
						xyjgbh: LeaderRole === 'xLeader' ? '1' : this.loginUserInfo.orgCode
					}
				});
				let data = list[0] || {};
				switch (listName) {
					case 'practiceData':
						this[listName] = {
							...this[listName],
							countAll: data[this[listName].key],
							list: this[listName].list.map(item => {
								return {
									...item,
									count: (data[item.key] || 0) * 100 + '%'
								};
							})
						};
						break;
					case 'graduateData':
						this[listName] = {
							...this[listName],
							list: this[listName].list.map(item => {
								return {
									...item,
									count: (data[item.key] || 0) * 100 + '%'
								};
							})
						};
						break;
					case 'graduateDataAll':
						this.graduateData.countAll = data[this.graduateData.key];
						break;
					case 'topData':
						{
							// const internList = list ? JSON.parse(JSON.stringify(list)).splice(0, 4) : [];
							// this.internList = internList.map(item => {
							// 	return {
							// 		name: item.dwhymc,
							// 		count: item.jyhyrs || 0
							// 	};
							// });

							const xData = [];
							const yData = [];
							for (let i = 0; i < 5; i++) {
								const item = list[i];
								xData.push(item.dwhymc);
								yData.push(item.jyhyrs || 0);
							}
							this.toolChart(xData, yData);
						}
						break;
				}
			} catch (error) {
				console.error(`毕业就业:${listName}:处理数据失败${listName}:`, error);
			}
		},
		// 学生实习行业分布top
		// async queryIntern() {
		// 	const {
		// 		dataResult: { dataList }
		// 	} = await xodbApi.get({
		// 		url: 'warehouseConfig_ybzy_dw_dwb_jw_xssxhyfb_query'
		// 	});
		// 	this.internList = dataList.map(item => {
		// 		return {
		// 			name: item.sxhy,
		// 			count: item.sxhyrs
		// 		};
		// 	});
		// },
		toolChart(xData = [], yData = []) {
			let chartDom = document.getElementById('myEchart');
			if (!chartDom) return;
			if (this.myChart) {
				this.myChart.clear();
			} else {
				this.myChart = this.$echarts.init(chartDom);
			}
			// xData = ['信息技术', '金融服务', '医疗卫生', '教育培训', '制造业'];
			// yData = [843.21, 777.98, 712.54, 699.12, 654.21];

			const colorList = ['#D16DF8', '#FBFF97', '#368FF0', '#83D978', '#1AC488', '#596FF8'];
			const option = {
				grid: {
					left: '0',
					right: '0',
					bottom: '-20',
					top: '0',
					containLabel: true
				},
				tooltip: {
					trigger: 'axis',
					// backgroundColor: 'rgba(73,81,92,.95)', //背景颜色
					borderWidth: '0', //边框为0
					textStyle: {
						color: '#333' //字体颜色
					},
					axisPointer: {
						type: 'none'
					}
				},
				xAxis: {
					show: true,
					type: 'value',
					splitLine: {
						show: true,
						lineStyle: {
							color: '#fff',
							type: 'dashed',
							width: 1
						}
					}
				},
				yAxis: [
					{
						type: 'category',
						name: '地区名称',
						inverse: true,
						//name的样式设计
						nameTextStyle: {
							align: 'left',
							padding: [-602, 0, 0, -44], //地区名称的位置
							color: '#333',
							fontSize: '12'
						},
						axisLabel: {
							show: true,
							textStyle: {
								color: '#454545'
							}
						},
						splitLine: {
							show: false
						},
						axisTick: {
							show: false
						},
						axisLine: {
							show: false
						},
						data: xData
					}
				],
				series: [
					{
						name: '人数',
						type: 'bar',
						zlevel: 1,
						barWidth: 6, // 或者 '60%'
						itemStyle: {
							normal: {
								// barBorderRadius: [0, 20, 20, 0],
								// color: new echarts.graphic.LinearGradient(0, 0, 1, 0, [{
								//     offset: 0,
								//     color: 'rgb(57,89,255,1)'
								// }, {
								//     offset: 1,
								//     color: 'rgb(46,200,207,1)'
								// }]),
								color: params => {
									return colorList[params.dataIndex];
								}
							}
						},
						data: yData
					}
				]
			};

			option && this.myChart.setOption(option);
		},
		onResize() {
			if (this.myChart) {
				this.myChart.resize();
			}
		}
	}
};
</script>

<style lang="scss" scoped>
.employment {
	display: flex;
	flex-direction: column;
	align-items: center;
	font-family: MicrosoftYaHei, MicrosoftYaHei;
	padding: 0 6px;
	.title {
		width: 100%;
		font-weight: bold;
		font-size: 14px;
		color: #0a325b;
		line-height: 19px;
	}

	.floor1,
	.floor2 {
		cursor: pointer;
		margin-top: 12px;
		background: rgba(255, 255, 255, 0.25);
		border-radius: 4px;
		border: 1px solid #ffffff;
		display: flex;
		align-items: center;
		justify-content: space-between;
		padding: 0 21px 0 28px;
		width: 430px;
		height: 72px;

		.describe {
			min-width: 128px;
			line-height: 30px;
			width: 56px;
			font-weight: bold;
			font-size: 14px;
			color: #454545;
			// border-right: 1px dotted #a9bed5;
			position: relative;
			&::after {
				position: absolute;
				content: '';
				display: inline-block;
				width: 1px;
				height: 30px;
				background: #a9bed5;
				margin-left: 10px;
				vertical-align: middle;
				top: calc(50% - 15px);
				bottom: 0;
				right: 0;
			}

			.describe-val {
				font-family: DINPro, DINPro;
				font-weight: bold;
				font-size: 22px;
				color: #0275e8;
				line-height: 28px;
				text-align: left;
				font-style: normal;
				margin-bottom: 4px;
			}
			.describe-name {
				width: 98px;
				height: 19px;
				font-size: 14px;
				color: #454545;
				line-height: 19px;
				text-align: left;
				font-style: normal;
			}
		}

		.info {
			.info-t {
				font-weight: bold;
				font-size: 16px;
				color: #0a325b;
				line-height: 21px;
				margin-bottom: 7px;
			}

			.info-b {
				font-size: 14px;
				color: #454545;
				line-height: 19px;
			}
		}
	}
	.floor2 {
		margin-top: 8px;
		margin-bottom: 12px;
	}
	.floor3 {
		width: 430px;
		// height: 173px;
		height: 103px;
		margin-bottom: 7px;
		margin-top: 6px;
	}
	.floor4 {
		cursor: pointer;
		width: 100%;
		display: flex;
		justify-content: space-between;
		margin-top: 10px;
		.intern-item {
			display: flex;
			flex-direction: column;
			align-items: center;
			.intern-item-t {
				width: 94px;
				height: 24px;
				background: rgba(255, 255, 255, 0.25);
				border-radius: 3px;
				border: 1px solid #ffffff;
				font-size: 12px;
				color: #454545;
				line-height: 22px;
				text-align: center;
				margin-bottom: 5px;
				overflow: hidden;
				text-overflow: ellipsis;
				white-space: nowrap;
			}

			.intern-item-c {
				height: 26px;
				font-family: DINPro, DINPro;
				font-weight: bold;
				font-size: 20px;
				color: #0a325b;
				line-height: 26px;
				margin-bottom: 4px;
				.unit {
					width: 14px;
					height: 19px;
					font-weight: normal;
					font-family: MicrosoftYaHei;
					font-size: 14px;
					color: #294d79;
					line-height: 19px;
				}
			}

			.intern-item-b {
				widows: 70px;
				height: 40px;
				img {
					width: 100%;
					height: 100%;
				}
			}
		}
	}
}
</style>
