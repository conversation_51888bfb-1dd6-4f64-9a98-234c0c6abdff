<!--
 @desc:成果转换弹出框
 @author: MRH
 @date: 2023/11/23

parmas:
	archievement_id: 成果id String
	showAchievementForm: 弹出框是否展示 false true
	achievementTitle: 弹出框title String
methods:
	changeShow: 改变showAchievementForm
 -->
<template>
	<es-dialog
		:title="achievementTitle"
		width="60%"
		:visible="showAchievementForm"
		@closed="changeShow"
		:drag="false"
	>
		<es-form
			v-if="show"
			ref="form"
			:model="addFormData"
			:contents="formItemList"
			height="500px"
			:readonly="formReadonly"
			collapse
			@submit="handleAddFormSubmit"
			@reset="changeShow"
		/>
	</es-dialog>
</template>

<script>
import { getAchievementList, createAchievement } from '@/api/scientific-sys.js';
export default {
	props: {
		showAchievementForm: {
			type: Boolean,
			required: true
		},
		archievement_id: {
			type: String,
			required: true
		},
		achievementTitle: {
			type: String,
			required: true
		}
	},
	data() {
		return {
			show: this.showAchievementForm,
			formReadonly: false,
			// loading: '',
			formItemList: [
				{
					label: '成果编码',
					name: 'achievementCode',
					placeholder: '请输入成果编码',
					event: 'multipled',
					disabled: true,
					col: 6
				},
				{
					label: '成果名称',
					name: 'achievementName',
					disabled: true,
					col: 6
				},
				{
					label: '教师姓名',
					name: 'teacherName',
					placeholder: '请输入教师姓名',
					event: 'multipled',
					col: 6
				},
				{
					label: '身份证号',
					name: 'teacherIdCard',
					placeholder: '请输入身份证号',
					event: 'multipled',
					col: 6,
					rules: {
						required: true,
						trigger: 'blur',
						validator: (rule, value, callback) => {
							if (!value) {
								return callback(new Error('身份证号不能为空'));
							}
							if (
								/^[1-9]\d{5}(18|19|20)?\d{2}(0[1-9]|1[0-2])(0[1-9]|[1-2]\d|3[0-1])\d{3}([0-9]|X)$/.test(
									value
								)
							) {
								callback();
							} else {
								callback(new Error('请输入正确的身份证号格式'));
							}
						}
					}
				},
				{
					label: '联系电话',
					name: 'teacherPhone',
					hide: false,
					placeholder: '请输入联系电话',
					event: 'multipled',
					col: 6,
					rules: {
						required: true,
						trigger: 'blur',
						validator: (rule, value, callback) => {
							if (!rule.required) {
								return callback();
							}
							if (!value) {
								return callback(new Error('联系方式不能为空'));
							}
							if (/^(?:(?:\+|00)86)?1[3-9]\d{9}$|^0[1-9]\d{1,2}-\d{7,8}$/.test(value)) {
								callback();
							} else {
								callback(new Error('请输入正确的电话格式'));
							}
						}
					}
				},
				{
					label: '专利号',
					name: 'patentNumber',
					placeholder: '请输入专利号',
					controls: false,
					col: 6
				},
				{
					label: '合作企业名称',
					name: 'cooperativeEnterprise',
					placeholder: '请选择合作企业名称',
					col: 6
				},
				{
					label: '专利金额（元）',
					name: 'patentAmount',
					placeholder: '请输入专利金额',
					controls: false,
					value: '',
					col: 6
				}
			],
			addFormData: {}
		};
	},
	mounted() {
		this.handleMessage();
		this.getViewTransition();
	},
	methods: {
		async handleMessage() {
			if (this.achievementTitle == '查看成果') {
				this.formReadonly = true;
			}
			if (this.achievementTitle == '成果转换') {
				this.formReadonly = false;
			}
		},
		// 新增成果转换
		async handleAddFormSubmit() {
			const loading = this.$.loading(this.$loading, '新增中');
			// this.loading = this.$.loading(this.$loading, '新增中');
			try {
				let addFormData = { ...this.addFormData };
				delete addFormData.adjunctName;
				let { rCode, msg } = await this.$.ajax({
					url: createAchievement,
					method: 'post',
					format: true,
					data: { ...addFormData, id: this.archievement_id } //id==最外层列表id 成果id achievementName==成果名称
				});
				if (rCode == 0) {
					this.$message.success(msg);
					this.changeShow();
					this.reset();
				} else {
					this.$message.error(msg);
				}
			} catch (error) {
				console.log('>>>error', error);
			} finally {
				loading.close();
			}
		},
		// 查询成果接口
		async getViewTransition() {
			const loading = this.$.loading(this.$loading, '查询中');
			try {
				let { results, rCode, msg } = await this.$.ajax({
					url: getAchievementList,
					method: 'post',
					data: { id: this.archievement_id }
				});
				if (rCode == 0) {
					this.addFormData = { ...results };
				} else if (rCode == 2) {
					this.addFormData = { ...results };
				} else {
					this.$message.error(msg);
				}
			} finally {
				loading.close();
			}
		},
		changeShow() {
			this.$emit('changeShow', this.showAchievementForm);
		}
	}
};
</script>
