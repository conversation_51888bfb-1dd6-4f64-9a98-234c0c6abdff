/*  接口地址定义
    方式1：
        //获取xxx信息
        export const getInfoStatistical = '/hr/shycInfo/getInfoStatistical.dhtml';
    调用方式1：
        import {getInfoStatistical} from '@/http/system.js';
        console.log(getInfoStatistical)

    方式2：
        //系统管理模块
        const system = {
            //用户、用户组树
            getDeptTree: '/bootdemo/simplesysDept/getDeptTree.djson',
        }
        export default system;
    调用方式2:
        import system from '@/http/system.js';
        console.log(system.getDeptTree)
        
*/

//接口地址
const api = {
	// 双选会
	listJsonSelection: '/ybzy/jobDualSelectInfo/listJson', // 邀请函列表
	treeJson: '/ybzy/front/specificOrg/getColSchSelectList', // 选择企业树
	enterprisePageList: '/ybzy/jobDualSelectInfo/enterprisePageList2', // 选择企业列表
	checkRole: '/ybzy/jobDualSelectInfo/checkRole', // 检查角色
	saveSelection: '/ybzy/jobDualSelectInfo/save', // 新建邀请函
	updateSelection: '/ybzy/jobDualSelectInfo/update', // 修改邀请函
	infoSelection: '/ybzy/jobDualSelectInfo/info', // 邀请函详情
	recallSelection: '/ybzy/jobDualSelectInfo/recall', // 邀请函撤回
	deleteById: '/ybzy/jobDualSelectInfo/deleteById', // 邀请函删除

	listJsonJobReply: '/ybzy/jobDualSelectAudit/listJson', // 审核回复列表接口
	infoReply: '/ybzy/front/jobDualSelect/info', // 回复详情
	postListByMappingId: '/ybzy/front/jobDualSelect/selectPostList', // 企业回复中职位列表
	auditJobReply: '/ybzy/jobDualSelectAudit/audit', // 审核回复
	getAuditList: '/ybzy/jobDualSelectAudit/getAuditList/' //根据邀请id查询审核列表，格式：/getAuditList/1770261899432275969
};
export default api;
