<template>
	<div class="content">
		<es-data-table
			ref="table"
			:row-style="tableRowClassName"
			:full="true"
			:fit="true"
			:thead="thead.storeRoom"
			:toolbar="toolbar.storeRoom"
			:border="true"
			:page="pageOption"
			:url="dataTableUrl"
			:numbers="true"
			:param="params"
			close
			form
			@btnClick="btnClick"
			@sort-change="sortChange"
			@submit="hadeSubmit"
		></es-data-table>

		<!-- 新增库房 -->
		<es-dialog
			:title="formTitle"
			:visible.sync="showForm"
			width="670px"
			height="400px"
			:drag="false"
			:close-on-click-modal="false"
			:destroy-on-close="true"
		>
			<es-form
				v-if="showForm"
				ref="form"
				:model="formData"
				:contents="formItemList"
				height="300px"
				:genre="2"
				collapse
				@change="inputChange"
				@submit="handleFormSubmit"
				@reset="showForm = false"
			/>
		</es-dialog>

		<!-- 分类预警设置 根据库房-->
		<es-dialog
			:title="formTitle"
			:visible.sync="showWarningType"
			width="1100px"
			height="630px"
			:drag="false"
			:close-on-click-modal="false"
			:destroy-on-close="true"
		>
			<es-toolbar
				:contents="materialType"
				:search-value="storeRoomSelectBar.value"
				@change="onSelectionTypeChange"
				@search="searchWarningType"
			></es-toolbar>
			<es-data-table
				v-if="showWarningType"
				ref="warningTable"
				height="430px"
				:full="true"
				:fit="true"
				:thead="thead.materialType"
				:border="true"
				:page="pageOption"
				:data="setWarningType"
				:numbers="true"
				:cell-class-name="cellClassName"
				:param="warningParams"
				close
				form
				@btnClick="selectWarning"
				@sort-change="sortChange"
				@success="succ"
				@change="dataWarningTypeChange"
				@cell-click="warningTypeClick"
			></es-data-table>
			<div class="content-warning-Btn">
				<el-button size="medium" type="primary" @click="saveWarningType">保存</el-button>
				<el-button size="medium" @click="saveWarningCancel">取消</el-button>
			</div>
		</es-dialog>
		<!-- 预警设置  根据原料类型-->
		<es-dialog
			:title="formTitle"
			:visible.sync="showWarning"
			width="1100px"
			height="630px"
			:drag="false"
			:close-on-click-modal="false"
			:destroy-on-close="true"
		>
			<es-toolbar
				:contents="material"
				:search-value="storeRoomSelectBar.value"
				@change="onSelectionChange"
				@search="searchWarning"
			></es-toolbar>
			<es-data-table
				v-if="showWarning"
				ref="warningTable"
				height="430px"
				:full="true"
				:fit="true"
				:thead="thead.material"
				:border="true"
				:page="pageOption"
				:data="setWarning"
				:numbers="true"
				:cell-class-name="cellClassName"
				:param="warningParams"
				close
				form
				@btnClick="selectWarning"
				@sort-change="sortChange"
				@success="succ"
				@change="dataWarningChange"
				@cell-click="warningClick"
			></es-data-table>
			<div class="content-warning-Btn">
				<el-button size="medium" type="primary" @click="saveWarning">保存</el-button>
				<el-button size="medium" @click="saveWarningCancel">取消</el-button>
			</div>
		</es-dialog>
		<!-- 启用状态 -->
		<es-dialog
			:title="formData.status == '0' ? '启用' : '禁用'"
			:visible.sync="showDisable"
			width="20%"
			:close-on-click-modal="false"
			:middle="true"
			:drag="false"
			height="140px"
		>
			<div>确定要{{ formData.status == '0' ? '启用' : '禁用' }}该库房吗</div>
			<div class="btn-box">
				<div class="btn theme" @click="disableRow">确定</div>
				<div class="btn" @click="showDisable = false">取消</div>
			</div>
		</es-dialog>
	</div>
</template>

<script>
import interfaceUrl from '@/http/logistics/hqStoeroom.js';
import operatePersonApi from '@/http/logistics/operatePerson.js';
import SnowflakeId from 'snowflake-id';
export default {
	data() {
		return {
			ownId: '',
			dataTableUrl: interfaceUrl.listJson, //库房列表
			dataWarningTypeUrl: interfaceUrl.categoryList, //分类预警列表
			dataWarningUrl: interfaceUrl.infoWarning, //原料预警列表
			showForm: false,
			//showDelete: false,
			showDisable: false,
			showWarningType: false, //预警弹窗-原料类型
			showWarning: false, //预警弹窗-原料
			storeroomIdType: null, //库房id-原料类型
			storeroomId: null, //库房id-原料
			keyword: '',

			parentRoomList: [],
			manageList: [],

			warningTypeIndex: 0,
			warningIndex: 0,
			formData: {},
			roomTableData: [],
			formTitle: '编辑',
			//
			storeRoomSelectBar: [], //预警下拉
			setWarningType: [], // 预警页面数据
			setWarning: [], // 预警页面数据
			//
			editBtn: {
				type: 'submit',
				skin: 'lay-form-btns',
				contents: [
					{
						type: 'primary',
						text: '确定',
						event: 'confirm'
					},
					{
						type: 'reset',
						text: '取消',
						event: 'cancel'
					}
				]
			},
			cancelBtn: {
				type: 'submit',
				skin: 'lay-form-btns',
				contents: [
					{
						type: 'reset',
						text: '取消',
						event: 'cancel'
					}
				]
			},
			toolbar: {
				storeRoom: [
					{
						type: 'button',
						contents: [
							{
								text: '新增',
								code: 'add',
								type: 'primary'
							},
							{
								text: '分类预警设置',
								code: 'warningType',
								type: 'primary'
							},
							{
								text: '原料预警设置',
								code: 'warning',
								type: 'primary'
							}
						]
					},
					{
						type: 'search',
						reset: true,
						contents: [
							{
								type: 'name', //精准查询
								//type: 'text',//模糊查询
								name: 'name',
								placeholder: '请输入库房名称'
							},
							{
								type: 'address',
								name: 'address',
								placeholder: '请输入库房地址'
							}
						]
					}
				]
			},
			thead: {
				storeRoom: [
					{
						title: '库房名称',
						align: 'left',
						field: 'name',
						showOverflowTooltip: true
					},
					{
						title: '所属库房',
						align: 'left',
						field: 'parentRoomName',
						showOverflowTooltip: true
					},
					{
						title: '库房地址',
						align: 'left',
						field: 'address',
						showOverflowTooltip: true
					},
					{
						title: '管理员',
						align: 'center',
						field: 'managerName',
						showOverflowTooltip: true
					},
					{
						title: '创建人',
						align: 'center',
						field: 'createUserName',
						showOverflowTooltip: true
					},
					{
						title: '创建时间',
						align: 'center',
						field: 'createTime',
						showOverflowTooltip: true
					},
					{
						title: '操作',
						type: 'handle',
						width: 180,
						template: '',
						events: [
							{
								code: 'edit',
								text: '编辑'
							},
							{
								code: 'view',
								text: '查看'
							},
							{
								code: 'disable',
								text: '启用',
								rules: rows => {
									return rows.status == '0';
								}
							},
							{
								code: 'disable',
								text: '禁用',
								rules: rows => {
									return rows.status == '1';
								}
							}
						]
					}
				],
				materialType: [
					{
						title: '分类名称',
						field: 'name'
					},
					{
						title: '预警值',
						field: 'warnPoint',
						type: 'text'
					}
				],
				material: [
					{
						title: '原料名称',
						field: 'materialName'
					},
					{
						title: '预警值',
						field: 'warnPoint',
						type: 'text'
					}
				]
			},
			pageOption: {
				layout: 'total, sizes, prev, pager, next, jumper',
				pageSize: 20,
				//// hideOnSinglePage: true,
				position: 'center',
				current: 1,
				pageNum: 1
			},
			params: { orderBy: 'create_time', asc: false },
			warningParams: {
				asc: true,
				orderBy: 'id'
			},
			formItemList: [
				{
					label: '库房类型',
					name: 'kfType',
					type: 'radio',
					value: '子级库房',
					col: 6,
					data: ['父级库房', '子级库房'],
					event: 'multipled',
					rules: { required: true, message: '请选库房类型', trigger: 'blur' }
				},
				{
					label: '库房名称',
					name: 'name',
					placeholder: '请输入库房名称',
					event: 'multipled',
					rules: {
						required: true,
						message: '请输入库房名称',
						trigger: 'blur'
					},
					maxlength: 85,
					verify: 'required',
					col: 12
				},
				{
					type: 'select',
					name: 'parentId',
					label: '所属库房',
					placeholder: '请选择所属库房',
					url: interfaceUrl.getRoomList + '?parentId=root&status=1',
					'label-key': 'name',
					'value-key': 'id',
					rules: {
						required: true,
						message: '请选择所属库房',
						trigger: 'change'
					},
					col: 12
				},
				{
					label: '库房地址',
					name: 'address',
					placeholder: '请输入库房地址',
					event: 'multipled',
					rules: {
						required: true,
						message: '请输入库房地址',
						trigger: 'blur'
					},
					maxlength: 85,
					col: 12
				},
				{
					type: 'select',
					name: 'manager',
					label: '管理员',
					placeholder: '请选择管理员',
					url: operatePersonApi.getSelectList + '?type=3&status=1',
					'label-key': 'label',
					'value-key': 'value',
					rules: {
						required: true,
						message: '请选择管理员',
						trigger: 'change'
					},
					col: 12
				}
			]
		};
	},
	computed: {
		//分类预警设置工具栏
		materialType() {
			return [
				{
					type: 'search',
					reset: true,
					contents: [
						{
							type: 'select',
							label: '下拉选择',
							placeholder: '请选择库房',
							name: 'storeroomId',
							event: 'multipled',
							data: this.storeRoomSelectBar,
							verify: 'required',
							col: 6
						},
						{
							type: 'type',
							placeholder: '请输入类型名称',
							name: 'keyword',
							col: 6
						}
					]
				}
			];
		},
		//原料预警设置工具栏
		material() {
			return [
				{
					type: 'search',
					reset: true,
					contents: [
						{
							type: 'select',
							label: '下拉选择',
							placeholder: '请选择库房',
							name: 'storeroomId',
							event: 'multipled',
							data: this.storeRoomSelectBar,
							verify: 'required',
							col: 6
						},
						{
							type: 'type',
							placeholder: '请输入原料名称',
							name: 'keyword',
							col: 6
						}
					]
				}
			];
		}
	},
	watch: {
		'formData.kfType'(obj) {
			let list = this.formItemList;
			if ('子级库房' == obj) {
				list[2].hide = false;
			} else {
				list[2].hide = true;
			}
		}
	},
	created() {
		this.getSelectParentRoomList();
		// this.getSelectManageList();
	},
	mounted() {},
	methods: {
		//打印请求成功的回调
		succ(value) {
			value.success ? (this.setWarning = value.results.records) : '';
			this.setWarning.forEach(el => {
				el.storeroomId == null ? el.storeroomId == '' : '';
			});
		},
		//获取父级库房下拉列表
		getSelectParentRoomList() {
			this.$request({
				url: interfaceUrl.getRoomList,
				// params: { parentId: 'root', status: 1 },
				params: { status: 1 },
				method: 'GET'
			}).then(res => {
				if (res.rCode === 0) {
					this.parentRoomList = [];
					this.storeRoomSelectBar = [];
					res.results.forEach(el => {
						this.storeRoomSelectBar.push({ name: el.name, value: el.id });
						if (el.parentId == 'root') {
							this.parentRoomList.push(el);
						}
					});
				}
			});
		},
		//获取库房管理员下拉列表
		getSelectManageList() {
			this.$request({
				url: operatePersonApi.getSelectList,
				params: { type: 3, status: 1 },
				method: 'GET'
			}).then(res => {
				if (res.rCode === 0) {
					this.manageList = res.results;
				}
			});
		},
		onSelectionTypeChange(key, value, data) {
			// console.log(key, value, data);
			// if (key == 'keyword') {
			//   this.keyword = value;
			// } else {
			//   this.storeroomIdType = value;
			// }
		},
		//
		onSelectionChange(key, value, data) {
			// console.log(key, value, data);
			// if (key == 'keyword') {
			// 	this.keyword = value;
			// } else {
			// 	this.storeroomId = value;
			// }
		},
		searchWarningType(param) {
			if (!param.storeroomId) {
				this.$message.error('必须选择库房才能操作！');
				return false;
			} else if (param.keyword) {
				this.keyword = param.keyword;
			}
			this.storeroomIdType = param.storeroomId;
			this.$.ajax({
				method: 'GET',
				url: this.dataWarningTypeUrl,
				params: { storeroomId: this.storeroomIdType, keyword: this.keyword }
			}).then(data => {
				this.setWarningType = data.results.records;
			});
		},
		searchWarning(param) {
			if (!param.storeroomId) {
				this.$message.error('必须选择库房才能操作！');
				return false;
			} else if (param.keyword) {
				this.keyword = param.keyword;
			}
			this.storeroomId = param.storeroomId;
			this.$.ajax({
				method: 'GET',
				url: this.dataWarningUrl,
				params: { storeroomId: this.storeroomId, keyword: this.keyword }
			}).then(data => {
				this.setWarning = data.results.records;
			});
		},
		//
		cellClassName({ row, column, rowIndex, columnIndex }) {
			row.index = rowIndex;
			column.index = columnIndex;
		},
		//  DoThis ： 通过表格数据改变时，通过 dataWarningChange 方法利用下标修改存储改变数据的属性(boj)
		/**
		 * 表格行点击
		 */
		warningTypeClick(row) {
			this.warningTypeIndex = row.index;
		},
		/**
		 * 表格行点击
		 */
		warningClick(row) {
			this.warningIndex = row.index;
		},
		/**
		 * 表格数据改变
		 */
		dataWarningTypeChange(data) {
			this.setWarningType[this.warningTypeIndex].warnPoint = Number(data.value); //加入发生改变的数据项[{}]
		},
		/**
		 * 表格数据改变
		 */
		dataWarningChange(data) {
			this.setWarning[this.warningIndex].warnPoint = Number(data.value); //加入发生改变的数据项[{}]
		},
		/**
		 * 提交分类预警界面的数据
		 */
		saveWarningType() {
			var params = [];
			this.setWarningType.forEach(el => {
				params.push({
					categoryId: el.id,
					storeroomId: this.storeroomIdType,
					warnPoint: Number(el.warnPoint)
				});
			});
			this.$request({
				headers: {
					contentType: 'application/json'
				},
				url: interfaceUrl.updateWarningType,
				method: 'POST',
				data: params,
				format: false
			}).then(res => {
				if (res.rCode == 0) {
					this.$message.success('操作成功');
					this.showWarning = false;
					this.showWarningType = false;
					this.setWarning = [];
				} else {
					this.$message.error(res.msg);
				}
			});
		},
		/**
		 * 提交原料预警界面的数据
		 */
		saveWarning() {
			var params = [];
			this.setWarning.forEach(el => {
				params.push({
					materialId: el.materialId,
					storeroomId: this.storeroomId,
					warnPoint: Number(el.warnPoint)
				});
			});
			this.$request({
				headers: {
					contentType: 'application/json'
				},
				url: interfaceUrl.updateWarning,
				method: 'POST',
				data: params,
				format: false
			}).then(res => {
				if (res.rCode == 0) {
					this.$message.success('操作成功');
					this.showWarning = false;
					this.showWarningType = false;
					this.setWarning = [];
				} else {
					this.$message.error(res.msg);
				}
			});
		},
		/**
		 * 表格行高
		 */
		tableRowClassName({ row, rowIndex }) {
			let styleRes = {
				height: '54px !important'
			};
			return styleRes;
		},
		inputChange(key, value) {
			if (key === 'parentId') {
				if (value && value !== 'root') {
					// this.formItemList[2].readonly = true;
					// this.formData.address = this.parentRoomList.find(item => item.id == value).address;
					// this.$refs.form.validateField('address');
				} else {
					// this.formItemList[2].readonly = false;
					this.formData.address = null;
				}
			}
		},
		hadeSubmit(data) {},
		//
		selectWarning(data) {
			console.log('筛选条件--', data);
		},
		saveWarningCancel() {
			this.showWarning = this.showWarningType = false;
		},
		/**
		 * 操作按钮点击事件
		 * @param {*} res
		 */
		async btnClick(res) {
			let code = res.handle.code;
			switch (code) {
				case 'add':
					// 新增
					this.formTitle = '新增库房';
					this.editModule(this.formItemList, code, [], []);
					const snowflake = new SnowflakeId();
					let id = snowflake.generate();
					this.ownId = id;
					this.formData = {
						id: id,
						kfType: '子级库房',
						manager: null,
						name: null,
						parentId: null,
						address: null,
						status: '1'
					};
					this.showForm = true;
					break;
				case 'edit':
				case 'view':
					this.ownId = res.row.id;
					let readonlyField = [];
					let hideField = [];
					let { rCode, msg, results } = await this.$.ajax({
						url: interfaceUrl.info + '/' + res.row.id,
						method: 'GET'
					});
					if (rCode == 0) {
						this.formData = results;
						if (this.formData) {
							if (this.formData.parentId == 'root') {
								this.$set(this.formData, 'kfType', '父级库房');
								hideField.push('parentId');
							} else {
								this.$set(this.formData, 'kfType', '子级库房');
							}
						}
						if (code == 'edit') {
							this.formTitle = '编辑';
							this.editModule(this.formItemList, code, readonlyField, hideField);
						} else {
							this.formTitle = '查看';
							this.readModule(this.formItemList, hideField);
						}
						this.showForm = true;
					} else {
						this.$message.error(msg);
					}
					break;
				case 'disable':
					// this.disableRow(res.row);
					this.formData = res.row;
					this.showDisable = true;
					break;
				case 'warningType':
					this.formTitle = '分类预警设置';
					// this.getStoreRoom(); //查询所有库房接口
					this.showWarningType = true;
					break;
				case 'warning':
					this.formTitle = '原料预警设置';
					// this.getStoreRoom(); //查询所有库房接口
					this.showWarning = true;
					break;
				default:
					break;
			}
		},
		handleClose() {},
		handleFormItemChange() {},
		handleFormSubmit(data) {
			let formData = data;
			let url = '';
			if (formData.kfType == '父级库房') {
				formData.parentId = null;
			}
			if (this.formTitle == '新增库房') {
				url = interfaceUrl.save;
			} else {
				url = interfaceUrl.update;
			}
			this.$request({
				url: url,
				data: formData,
				method: 'POST'
			}).then(res => {
				if (res.rCode == 0) {
					this.$message.success('操作成功');
					this.showForm = false;
					this.formData = {};
					this.$refs.table.reload();
					this.getSelectParentRoomList();
				} else {
					this.$message.error(res.msg);
				}
			});
		},
		disableRow() {
			let url = interfaceUrl.update;
			this.formData.status == '0' ? (this.formData.status = '1') : (this.formData.status = '0');
			this.$request({
				url: url,
				data: this.formData,
				method: 'POST'
			}).then(res => {
				if (res.rCode == 0) {
					this.$message.success('操作成功');
					this.showDisable = false;
					this.formData = {};
					this.$refs.table.reload();
					this.getSelectParentRoomList();
				} else {
					this.showDisable = false;
					this.$message.error(res.msg);
				}
			});
		},
		/**
		 * 排序变化事件
		 * @param {*} column
		 * @param {*} prop
		 * @param {*} order
		 */
		sortChange(column, prop, order) {
			if (column.order == 'ascending') {
				//升序
				this.params = {
					asc: 'true',
					orderBy: column.prop
				};
			} else if (column.order == 'descending') {
				//降序
				this.params = {
					asc: 'false',
					orderBy: column.prop
				};
			} else {
				//不排序
				this.params = {
					asc: 'false',
					orderBy: 'createTime'
				};
			}
			this.$refs.table.reload();
		},
		/**
		 * 编辑模式
		 */
		editModule(list, code, readonlyField, hideField) {
			for (var i in list) {
				var item = list[i];
				if (
					undefined != readonlyField &&
					readonlyField.length > 0 &&
					readonlyField.indexOf(item.name) > -1
				) {
					item.readonly = true;
				} else {
					item.readonly = false;
				}
				if (undefined != hideField && hideField.length > 0 && hideField.indexOf(item.name) > -1) {
					item.hide = true;
				} else {
					item.hide = false;
				}
				if (item.name == 'parentId') {
					if (code == 'add') {
						item.clearable = true;
					} else {
						item.clearable = false;
					}
				}
			}
			list.push(this.editBtn);
		},
		/**
		 * 只读模式
		 */
		readModule(list, hideField) {
			for (var i in list) {
				var item = list[i];
				item.readonly = true;
				if (undefined != hideField && hideField.length > 0 && hideField.indexOf(item.name) > -1) {
					item.hide = true;
				} else {
					item.hide = false;
				}
			}
			list.push(this.cancelBtn);
		},
		/**
		 * 获取库房名称
		 */
		getStoreRoom() {
			let url = interfaceUrl.listAll;
			this.$request({
				url: url,
				method: 'GET'
			}).then(res => {
				if (res.rCode == 0) {
					let ArrList = [];
					this.storeRoomSelectBar = [];
					res.results.forEach(el => {
						ArrList.push({ name: el.name, value: el.id });
					});
					this.storeRoomSelectBar = ArrList;
				} else {
					this.$message.error(res.msg);
				}
			});
		}
	}
};
</script>
<style scoped lang="scss">
@import '../../../assets/style/style.scss';

.content {
	display: flex;
	width: 100%;
	height: 100%;

	::v-deep .es-data-table {
		flex: 1;
		display: flex;
		flex-direction: column;

		.es-data-table-content {
			flex: 1;
			height: 0;
			display: flex;
			flex-direction: column;

			.el-table {
				flex: 1;
			}

			.es-thead-border {
				.el-table__header {
					th {
						border-right: 1px solid #e1e1e1;
					}
				}
			}
		}
	}

	::v-deep .el-form-item__label {
		background: none;
		border: 0px solid #c2c2c2;
	}
}

.el-dialog__body {
	.content-warning-Btn {
		float: right;
		margin: 10px 0px;
	}
	.btn-box {
		margin-top: 20px;
		display: flex;
		justify-content: flex-end;

		.btn {
			padding: 5px 10px;
			color: #666;
			border: 1px solid #eee;
			cursor: pointer;
			margin-right: 5px;
			border-radius: 4px;
		}
	}
}
</style>
