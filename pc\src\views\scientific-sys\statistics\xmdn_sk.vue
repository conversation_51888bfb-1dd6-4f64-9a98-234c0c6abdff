<template>
	<div v-loading="loading" class="content">
		<es-data-table
			ref="table"
			:row-style="tableRowClassName"
			:thead="thead"
			:toolbar="toolbar"
			:border="true"
			filter
			:page="pageOption"
			:url="basics.dataTableUrl"
			:param="params"
			show-label
			close
			@search="search"
			@reset="search({})"
			@btnClick="btnClick"
		></es-data-table>
	</div>
</template>

<script>
import { host } from '@/../config/config';
import qs from 'qs';
export default {
	data() {
		return {
			basics: {
				dataTableUrl: '/ybzy/projectBaseInfo/queryPageMapSocialWorkload', // 列表接口
				download: '/ybzy/projectBaseInfo/exportSocialWorkload' // 导出
			},
			loading: false,
			params: {},
			selectParams: {},
			pageOption: {
				layout: 'total, prev, pager, next, jumper',
				pageSize: 20,
				// hideOnSinglePage: true,
				position: 'center',
				current: 1,
				pageNum: 1
			},
			codesObj: {} //批量数据字典
		};
	},
	computed: {
		thead() {
			return [
				{
					title: '项目名称',
					field: 'projectName',
					align: 'center',
					showOverflowTooltip: true,
					fixed: 'left'
				},
				{
					title: '负责人姓名',
					field: 'principal',
					align: 'center',
					showOverflowTooltip: true,
					width: 100
				},
				{
					title: '批准日期',
					field: 'approveDate',
					align: 'center',
					width: 100
				},
				{
					title: '项目来源',
					field: 'projectSource',
					align: 'center',
					showOverflowTooltip: true,
					width: 160
				},
				{
					title: '研究类别',
					field: 'studyType',
					align: 'center',
					showOverflowTooltip: true,
					width: 120
				},
				{
					title: '一级学科',
					field: 'firstLevelDiscipline',
					align: 'center',
					showOverflowTooltip: true,
					width: 120
				},
				{
					title: '人员类型',
					field: 'personType',
					align: 'center',
					width: 120
				},
				{
					title: '人员名称',
					field: 'personName',
					align: 'center',
					showOverflowTooltip: true,
					width: 120
				},
				{
					title: '职称',
					field: 'rank',
					align: 'center',
					width: 120
				},
				{
					title: '性别',
					field: 'sex',
					align: 'center',
					width: 80
				},
				{
					title: '学历学位',
					field: 'highestEducation',
					align: 'center',
					width: 120
				},
				{
					title: '研究生类型',
					field: 'graduateStudentType',
					align: 'center',
					width: 100
				},
				{
					title: '工作量',
					field: 'workload',
					align: 'center',
					width: 100
				}
			];
		},
		toolbar() {
			return [
				{
					type: 'button',
					contents: [
						{
							text: '导出',
							exportXls: true,
							icon: 'es-icon-daochu',
							type: 'primary',
							event: () => {
								const params = { ...this.params, ...this.selectParams };
								const url = host + this.basics.download + '?' + qs.stringify(params);
								window.open(url, '_self');
							}
						}
					]
				},
				{
					type: 'search',
					reset: true,
					showLabel: true,
					contents: [
						{
							type: 'daterange',
							name: 'dates',
							label: '时间范围',
							placeholder: '请选择时间范围'
						},
						{ type: 'text', label: '关键字', name: 'keyword', placeholder: '请输入关键字查询' }
					]
				}
			];
		}
	},
	created() {
		const codes = 'member_nation,hq_person_education,hq_person_state';
		this.$utils.findSysCodeList(codes).then(res => {
			this.codesObj = res;
		});
	},
	methods: {
		//表格行高
		tableRowClassName({ row, rowIndex }) {
			let styleRes = { height: '54px !important' };
			return styleRes;
		},
		//操作按钮点击事件
		btnClick(res) {},
		search(res) {
			const paramsN = { ...res };
			if (paramsN.dates) {
				paramsN.startDate = paramsN.dates[0] || '';
				paramsN.endDate = paramsN.dates[1] || '';
				delete paramsN.dates;
			}
			this.params = {
				...{
					projectType: this.params.projectType,
					orderBy: this.params.orderBy,
					asc: this.params.asc // true 升序，false 降序
				},
				...paramsN
			};
			this.selectParams = paramsN;
		},
		refreshData() {
			this.$refs.table.reload();
		}
	}
};
</script>

<style scoped lang="scss">
.content {
	width: 100%;
	height: 100%;
}
</style>
