<!--
 @desc:基本信息
 @author: WH
 @date: 2023/11/14
 -->
<template>
	<div class="main">
		<div class="basic-info">
			<title-card title="基础信息"></title-card>
			<es-form
				ref="isform"
				label-width="280"
				:model="formData"
				:contents="formItemList"
				table
				:submit="false"
				:readonly="formReadonly"
			></es-form>
		</div>
		<div class="menber-info">
			<title-card title="成员信息"></title-card>
			<div class="is-table">
				<es-data-table
					form
					:readonly="formReadonly"
					style="width: 100%"
					:thead="theadTeacher"
					:data="teacherList"
					@btnClick="btnClick"
				></es-data-table>
			</div>
			<div class="is-table">
				<es-data-table
					form
					:readonly="formReadonly"
					style="width: 100%"
					:thead="theadStudent"
					:data="studentList"
					@btnClick="btnClick"
				></es-data-table>
			</div>
		</div>
		<!-- <div v-if="additionName" class="addition-info">
			<title-card :title="additionName"></title-card>
			<ul v-if="additionData">
				<li v-for="(item, index) in tableItme" :key="index">
					<span class="title">{{ item.name }}</span>
					<span class="value">{{ additionData[item.label] }}</span>
				</li>
			</ul>
			<p v-else class="no-data">暂无数据......</p>
		</div> -->
		<div v-if="!formReadonly" class="btn-box">
			<es-button type="primary" size="small" @click="save">保存</es-button>
		</div>
		<es-dialog title="修改" :visible.sync="visible">
			<es-form
				v-if="visible"
				ref="tableForm"
				:model="row"
				:contents="rowItemList"
				@submit="submit"
				@reset="reset"
			></es-form>
		</es-dialog>
	</div>
</template>

<script>
import TitleCard from '@cpt/scientific-sys/title-card.vue';
import {
	getPorjectInfo,
	updatePorjectInfo,
	getFlowFormInfo,
	getArchiveInfo,
	getFinalAcceptanceInfo
} from '@/api/scientific-sys.js';
import SnowflakeId from 'snowflake-id';

export default {
	components: { TitleCard },
	inject: ['id', 'openType', 'initActiveName', 'projectClassifyStr'],
	data() {
		return {
			// tableUrl: ,
			visible: false,
			additionData: {}, //中期检查 归档  项目变更 需要扩展显示内容
			additionName: '', //中期检查 归档  项目变更 需要扩展显示内容
			tableItme: [], //中期检查 归档  项目变更 需要扩展显示内容
			param: {
				type: 0 //0待审核 1已审核
			},
			basicInfo: {},
			theadTeacher: [
				{
					label: '教师信息',
					childHead: [
						{
							label: '人员类型',
							field: 'memberType',
							type: 'select',
							sysCode: 'project_member_type',
							filterable: true
						},
						{
							label: '姓名',
							field: 'memberName',
							type: 'text',
							filterable: true
						},
						{
							label: '教工号',
							field: 'memberNum',
							type: 'text',
							filterable: true
						},
						{
							label: '部门/学院',
							field: 'orgName',
							type: 'text',
							filterable: true
						},
						{
							label: '职称',
							field: 'professional',
							type: 'text',
							filterable: true
						},
						{
							label: '联系电话',
							field: 'memberPhone',
							type: 'text',
							filterable: true
						},
						{
							label: '承担类型',
							field: 'assumeType',
							type: 'select',
							sysCode: 'project_assume_type',
							filterable: true
						}
					]
				}
			],
			theadStudent: [
				{
					label: '学生信息',
					childHead: [
						// {
						// 	label: '序号',
						// 	type: 'index',
						// 	align: 'center',
						// 	width: 70,
						// 	render: (h, params) => {
						// 		return h('span', {}, params.row.index);
						// 	}
						// },
						{
							label: '人员类型',
							field: 'memberType',
							type: 'select',
							sysCode: 'project_member_type',
							filterable: true
						},
						{
							label: '姓名',
							field: 'memberName',
							type: 'text',
							filterable: true
						},
						{
							label: '学工号',
							field: 'memberNum',
							type: 'text',
							filterable: true
						},
						{
							label: '部门/学院',
							field: 'orgName',
							type: 'text',
							filterable: true
						},
						{
							label: '联系电话',
							field: 'memberPhone',
							type: 'text',
							filterable: true
						},
						{
							label: '承担类型',
							field: 'assumeTypeTxt',
							readonly: true,
							filterable: true
						},
						{
							label: '证明材料（学生证）',
							minWidth: '200px',
							filterable: true,
							render: (h, params) => {
								return h('es-upload', {
									class: 'upload-box',
									attrs: {
										preview: true,
										code: 'transationform_editfile',
										ownId: params.row.studentAdjunctId, // 业务id
										readonly: this.formReadonly,
										showInfo: []
									}
								});
							}
						}
					]
				}
			],
			rowData: [],
			studentList: [],
			teacherList: [],
			formData: {
				table2: [
					{ type: '1', name: 'fff' },
					{ type: '1', name: 'fff' }
				]
			},
			formReadonly: false,
			rowItemList: [
				{
					name: 'memberType',
					label: '人员类型',
					type: 'select',
					sysCode: 'project_member_type',
					placeholder: '请选择人员类型',
					rules: {
						required: true,
						message: '请选择人员类型',
						trigger: 'change'
					}
				},
				{
					name: 'memberName',
					placeholder: '请输入',
					label: '姓名',
					rules: {
						required: true,
						message: '请输入姓名',
						trigger: 'blur'
					}
				},
				{
					name: 'orgName',
					label: '所在部门/学院',
					// type: 'select',
					// sysCode: 'project_type',
					placeholder: '请输入',
					rules: {
						required: true,
						message: '请输入所在部门/学院',
						trigger: 'blur'
					}
				},
				{
					name: 'professional',
					placeholder: '请输入',
					label: '职称',
					rules: {
						required: true,
						message: '请输入职称',
						trigger: 'blur'
					}
				},
				{
					name: 'memberPhone',
					placeholder: '请输入',
					label: '联系电话',
					rules: {
						required: true,
						trigger: 'blur',
						validator: (rule, value, callback) => {
							if (!rule.required) {
								return callback();
							}
							if (!value) {
								return callback(new Error('联系方式不能为空'));
							}
							if (/^(?:(?:\+|00)86)?1[3-9]\d{9}$|^0[1-9]\d{1,2}-\d{7,8}$/.test(value)) {
								callback();
							} else {
								callback(new Error('请输入正确的电话格式'));
							}
						}
					}
				},

				{
					name: 'assumeType',
					label: '承担责任',
					type: 'select',
					sysCode: 'project_assume_type',
					placeholder: '请选择承担责任',
					rules: {
						required: true,
						message: '请选择承担责任',
						trigger: 'change'
					}
				}
			],
			row: {}
		};
	},
	computed: {
		formItemList() {
			// LENTHWAYS("lengthWays", "纵向项目",0),
			// CROSSWISE("crosswise", "横向项目",1),
			// COLLEGE("college", "院级项目",2),
			const projectClassify = this.formData.projectClassify || Number(this.projectClassifyStr);
			const snowflake = new SnowflakeId();
			this.formData.declarationAdjunctId ||
				(this.formData.declarationAdjunctId = snowflake.generate());
			this.formData.materialsAdjunctId || (this.formData.materialsAdjunctId = snowflake.generate());
			this.formData.contractListAdjunctId ||
				(this.formData.contractListAdjunctId = snowflake.generate());
			this.formData.contractAdjunctId || (this.formData.contractAdjunctId = snowflake.generate());

			return [
				{
					name: 'projectNumber',
					placeholder: '请输入',
					label: '项目编号',
					readonly: true,
					hide: this.openType == 'add',
					col: 6
				},
				{
					name: 'projectName',
					placeholder: '请输入',
					label: '项目名称',
					rules: {
						required: !this.formReadonly,
						message: '请输入',
						trigger: 'blur'
					},
					col: 6
				},
				{
					name: 'projectType',
					label: '项目类型',
					type: 'select',
					sysCode: 'project_type',
					hide: projectClassify === 1,
					rules: {
						required: !this.formReadonly,
						message: '请选择',
						trigger: 'blur'
					},
					col: 6
				},
				{
					name: 'projectLevel',
					label: '项目级别',
					type: 'select',
					sysCode: projectClassify === 0 ? 'project_level_other' : 'project_level_college',
					hide: projectClassify === 1,
					rules: {
						required: !this.formReadonly,
						message: '请选择',
						trigger: 'blur'
					},
					col: 6
				},
				{
					name: 'belowOrgName',
					label: '下达单位',
					hide: projectClassify !== 0,
					rules: {
						required: !this.formReadonly,
						message: '请输入',
						trigger: 'blur'
					},
					col: 6
				},
				{
					name: 'contractSignDate',
					placeholder: '请选择',
					col: 6,
					label: '合同签订日期',
					hide: projectClassify !== 1,
					rules: {
						required: !this.formReadonly,
						message: '请选择',
						trigger: 'blur'
					},
					type: 'date'
				},
				{
					name: 'studyDeadlineStart',
					placeholder: '请选择',
					col: 6,
					label: '研究期限开始时间',
					type: 'date',
					format: 'yyyy-MM-dd',
					rules: {
						required: !this.formReadonly,
						message: '请选择',
						trigger: 'blur'
					},
					unlinkPanels: true
				},
				{
					name: 'studyDeadlineEnd',
					placeholder: '请选择',
					col: 6,
					label: '研究期限结束时间',
					type: 'date',
					rules: {
						required: !this.formReadonly,
						message: '请选择',
						trigger: 'blur'
					},
					unlinkPanels: true
				},

				{
					name: 'cooperatingOrganization',
					placeholder: '请输入',
					col: 6,
					label: '合作单位',
					rules: {
						required: !this.formReadonly,
						message: '请输入',
						trigger: 'blur'
					},
					hide: projectClassify !== 1
				},
				{
					name: 'cooperationType',
					placeholder: '请选择',
					col: 6,
					label: '合作类型',
					hide: projectClassify !== 1,
					type: 'select',
					rules: {
						required: !this.formReadonly,
						message: '请选择',
						trigger: 'blur'
					},
					sysCode: 'project_cooperation_type'
				},
				{
					name: 'contractType',
					placeholder: '请选择',
					col: 6,
					label: '合同类型',
					hide: projectClassify !== 1,
					type: 'select',
					rules: {
						required: !this.formReadonly,
						message: '请选择',
						trigger: 'blur'
					},
					sysCode: 'project_contract_type'
				},

				{
					name: 'declareTaskSource',
					placeholder: '请输入',
					label: '申报课题来源（网址）',
					hide: projectClassify !== 0,
					rules: {
						required: !this.formReadonly,
						message: '请输入',
						trigger: 'blur'
					},
					col: 12
				},
				{
					name: 'researchContents',
					placeholder: '请输入备注',
					label: '研究内容',
					type: 'textarea',
					hide: projectClassify !== 0,
					rules: {
						required: !this.formReadonly,
						message: '请输入',
						trigger: 'blur'
					},
					col: 12
				},
				{
					name: 'fj0',
					label: '申报书、活页、汇总表等材料',
					type: 'attachment',
					value: '',
					col: 12,
					preview: true,
					code: 'transationform_editfile',
					ownId: this.formData.declarationAdjunctId, // 业务id
					rules: {
						required: !this.formReadonly,
						message: '请上传',
						trigger: 'blur'
					},
					hide: projectClassify !== 0
				},
				{
					name: 'fj1',
					label: '纵向审批流程单（二级学院签章）',
					type: 'attachment',
					value: '',
					col: 12,
					preview: true,
					//ownId:this.ownId,
					code: 'transationform_editfile',
					ownId: this.formData.materialsAdjunctId, // 业务id
					rules: {
						required: !this.formReadonly,
						message: '请上传',
						trigger: 'blur'
					},
					hide: projectClassify !== 0
				},

				{
					name: 'fj1',
					label: '横向审批流程单（二级学院签章）',
					type: 'attachment',
					value: '',
					col: 12,
					preview: true,
					//ownId:this.ownId,
					code: 'transationform_editfile',
					ownId: this.formData.contractListAdjunctId, // 业务id
					rules: {
						required: !this.formReadonly,
						message: '请上传',
						trigger: 'blur'
					},
					hide: projectClassify !== 1
				},
				{
					name: 'fj2',
					label: '正式合同',
					type: 'attachment',
					value: '',
					col: 12,
					preview: true,
					//ownId:this.ownId,
					code: 'transationform_editfile',
					ownId: this.formData.contractAdjunctId, // 业务id
					rules: {
						required: !this.formReadonly,
						message: '请上传',
						trigger: 'blur'
					},
					hide: projectClassify !== 1
				},
				{
					name: 'targetTask',
					placeholder: '',
					label: '目标任务',
					hide: projectClassify !== 2,
					rules: {
						required: !this.formReadonly,
						message: '请输入',
						trigger: 'blur'
					},
					col: 12
				},
				{
					name: 'fj3',
					label: '申报书',
					type: 'attachment',
					value: '',
					col: 12,
					preview: true,
					//ownId:this.ownId,
					code: 'transationform_editfile',
					ownId: this.formData.declarationAdjunctId, // 业务id
					rules: {
						required: !this.formReadonly,
						message: '请上传',
						trigger: 'blur'
					},
					hide: projectClassify !== 2
				},
				{
					name: 'fj4',
					label: '申报材料及附件',
					type: 'attachment',
					value: '',
					col: 12,
					preview: true,
					//ownId:this.ownId,
					code: 'transationform_editfile',
					ownId: this.formData.materialsAdjunctId, // 业务id
					rules: {
						required: !this.formReadonly,
						message: '请上传',
						trigger: 'blur'
					},
					hide: projectClassify !== 2
				},
				{
					name: 'remark',
					placeholder: '',
					label: '备注',
					type: 'textarea',
					hide: projectClassify === 1,
					col: 12
				},
				{
					name: 'responsibleContent',
					placeholder: '',
					label: '负责内容详细介绍',
					rules: {
						required: !this.formReadonly,
						message: '请输入',
						trigger: 'blur'
					},
					col: 12
				},
				{
					name: 'declarationInstructions',
					label: '申报说明',
					type: 'radio',
					rules: {
						required: !this.formReadonly,
						message: '请输入',
						trigger: 'blur'
					},
					col: 12,
					sysCode: 'project_declaration_instructions'
				},
				{
					name: 'declareTime',
					label: '申报时间',
					value: '',
					type: 'date',
					rules: {
						required: !this.formReadonly,
						message: '请选择',
						trigger: 'blur'
					},
					col: 12
				}
			];
		}
	},
	created() {
		const routerName = this.$route.name;
		const URL_KEY = {
			interimCheck: {
				url: getFlowFormInfo,
				title: '中检信息',
				tableItme: [
					{ name: '中检说明', label: 'inspectionContent' },
					{ name: '中检时间', label: 'inspectionTime' }
				]
			},
			FinalAcceptance: {
				url: getFinalAcceptanceInfo,
				title: '结项信息',
				tableItme: [
					{ name: '结项说明', label: 'specification' },
					{ name: '结项时间', label: 'createTime' }
				]
			},
			archive: {
				url: getArchiveInfo,
				title: '归档信息',
				tableItme: [
					{ name: '归档说明', label: 'fileContent' },
					{ name: '归档时间', label: 'fileTime' }
				]
			}
		};

		//初次打开名字是BasicInfo 才能够编辑基本信息
		if (this.openType == 'look' || this.initActiveName == 'CapitalInfo') {
			this.formReadonly = true;
		}

		//基本信息扩展内容
		if (URL_KEY[routerName]) {
			this.additionName = URL_KEY[routerName].title;
			this.tableItme = URL_KEY[routerName].tableItme;
			this.additionRequst(URL_KEY[routerName].url);
		}
	},
	mounted() {
		if (this.openType !== 'add') {
			// 非新增情况下需要请求详情数据
			this.getPorjectInfo();
		}
	},
	methods: {
		//表格修改弹窗操作————————————————————————————>>>
		submit(data) {
			let idx = this.tableData.findIndex(item => (item.id = this.row.id));
			this.$set(this.tableData, [idx], {
				...this.tableData[idx],
				...data
			});
			this.reset();
		},
		reset() {
			this.row = {};
			this.visible = false;
		},
		//————————————————————————————————————————————<<<

		btnClick({ handle, row }) {
			// console.log(">>>", this.$router.resolve({ name: "allotSubmitList" }));
			let { text, btnType } = handle;
			this.visible = true;
			this.row = { ...row, memberType: String(row.memberType), assumeType: String(row.assumeType) };
		},
		//修改保存————————————————————————————>>>
		save() {
			this.$refs.isform.validate(valid => {
				if (valid) {
					this.updatePorjectInfo();
				} else {
					return false;
				}
			});
		},
		async updatePorjectInfo() {
			try {
				const loading = this.load('提交中...');
				let formData = { ...this.formData };

				delete formData.declarationAdjunctFile;
				delete formData.materialsAdjunctFile;
				delete formData.contractAdjunctFile;
				let { rCode, msg, results } = await this.$.ajax({
					url: updatePorjectInfo,
					method: 'post',
					format: false,

					data: {
						baseInfoMap: { ...formData, saveType: 'update' },
						memberInfoList: [...this.studentList, ...this.teacherList]
					}
				});
				loading.close();
				if (rCode == 0) {
					this.$message.success(msg);
					this.$bus.$emit('closeDialog', false);
				} else {
					this.$message.error(msg);
				}
			} catch (error) {
				console.log('>>>error', error);
			}
		},
		// 页面加载
		load(text) {
			return this.$.loading(this.$loading, text);
		},
		async getPorjectInfo() {
			const loading = this.load('加载中...');
			try {
				let { rCode, msg, results } = await this.$.ajax({
					url: getPorjectInfo,
					method: 'get',
					params: { id: this.id }
				});
				if (rCode == 0) {
					let obj = results.baseInfoMap;
					obj = Object.keys(obj).length ? obj : {};

					//申报书附件id
					// this.formItemList[this.formItemList.length - 3].ownId = obj.declarationAdjunctId;
					// //申报材料附件id
					// this.formItemList[this.formItemList.length - 2].ownId = obj.materialsAdjunctId;
					const memberInfoList = results.memberInfoList || [];

					memberInfoList.forEach(e => {
						e.assumeType = String(e.assumeType);
						e.memberType = String(e.memberType);
						if (e.assumeType === '3') {
							this.studentList.push(e);
						} else {
							this.teacherList.push(e);
						}
					});
					this.setTableData(obj);
				} else {
					this.$message.error(msg);
				}
			} finally {
				loading.close();
			}
		},

		async additionRequst(url) {
			const loading = this.load('加载中...');
			try {
				let { rCode, msg, results } = await this.$.ajax({
					url,
					method: 'get',
					params: { projectId: this.id }
				});
				if (rCode == 0) {
					this.additionData = results;
				} else {
					this.$message.error(msg);
				}
			} finally {
				loading.close();
			}
		},
		setTableData(obj) {
			obj.cooperationType = String(obj.cooperationType);
			obj.studyDeadlineStart = obj.studyDeadlineStart ? obj.studyDeadlineStart.split(' ')[0] : '';
			obj.studyDeadlineEnd = obj.studyDeadlineEnd ? obj.studyDeadlineEnd.split(' ')[0] : '';
			obj.studyDeadlineStart = obj.studyDeadlineStart ? obj.studyDeadlineStart.split(' ')[0] : '';
			obj.declareTime = obj.declareTime ? obj.declareTime.split(' ')[0] : '';

			obj.projectClassify = obj.projectClassify || 0;
			//项目类型 公共
			obj.projectType = String(obj.projectType);
			//申报说明	公共
			obj.declarationInstructions = String(obj.declarationInstructions);
			if (obj.projectClassify == 0) {
				//项目级别
				obj.projectLevel = String(obj.projectLevel);
			} else if (obj.projectClassify == 1) {
				//合同类型
				obj.contractType = String(obj.contractType);
			} else if (obj.projectClassify == 2) {
				//项目级别
				obj.projectLevel = String(obj.projectLevel);
			}
			this.formData = obj;
		}
	}
};
</script>

<style lang="scss" scoped>
@import '@ast/style/public.scss';
.main {
	// @include flexBox();
	// flex-direction: column;
	height: 100%;
	overflow: auto;
	padding: 0 8px;
	width: 100%;
	flex: 1;
	.basic-info {	
		flex: 1;
		// height: 400px;
		// width: 100%;
		overflow: auto;
	}
	.menber-info {
		margin-bottom: 30px;
		width: 100%;
		.is-table {
			width: 100%;
			// height: calc(100% - 80px);
			margin-bottom: 15px;
		}
	}
	.addition-info {
		width: 100%;
		ul {
			li {
				@include flexBox(flex-start);
				width: 100%;
				border: 1px solid #d0d0d0;
				border-bottom: none;
				&:nth-last-child(1) {
					border-bottom: 1px solid #d0d0d0;
				}
				.title {
					padding: 10px 16px;

					width: 200px;
					background: #f5f5f5;
					border-right: 1px solid #d0d0d0;
					color: #747474;
					font-weight: 550;
					text-align: right;
				}
				.value {
					flex: 1;
					padding: 10px 16px;
					color: #747474;
				}
			}
		}
		.no-data {
			text-align: center;
			padding: 10px;
		}
	}
	.btn-box {
		@include flexBox();
		width: 100%;
		padding-bottom: 10px;
	}
}
::v-deep .upload-box {
	width: 100%;
	.el-button--medium {
		padding: 2px 6px;
		font-size: 12px;
	}
	.el-upload-list {
		// margin-top: -15px !important;
		.el-upload-list__item-name {
			width: auto;
			top: 0px;
			text-align: center;
			height: 37.7px;
			line-height: 39.7px;
		}
	}
}
::v-deep .es-form-content {
	padding: 0 !important;
}
::v-deep .es-table-form-label {
	text-align: right;
	color: #747474;
	font-weight: 550;
}
::v-deep .cell {
	color: #747474;
}
</style>
