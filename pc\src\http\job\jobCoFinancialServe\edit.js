import httpApi from '@/http/job/jobCoFinance/api.js';
export default {
	data() {
		return{
			editPageMode: 'edit',
			selectDataList: [],
		}
	},
	computed: {
		formItemList() {
			return [
				{
					type: 'select',
					name: 'orgIdVO',
					label: '金融机构',
					filterable: true,
					isNoParamRequest: false,
					'value-key': 'value',
					'label-key': 'label',
					'filter-method': this.selectData,
					clearable: true,
					col: 12,
					disabled: 'add' !== this.editPageMode,
					rules: {
						required: true,
						message: '金融机构'
					},
					data: this.selectDataList
				},
				{
					name: 'applicableEntity',
					label: '适用主体',
					value: '',
					col: 12,
					disabled: 'add' !== this.editPageMode,
					type: 'radio',
					rules: {
						required: true,
						message: '请选择适用主体'
					},
					data: [
						{value: 0, label: '企业(含个体工商户)'},
						{value: 1, label: '个人'}
					]
				},
				{
					name: 'productName',
					label: '产品名称',
					value: '',
					col: 6,
					disabled: 'add' !== this.editPageMode,
					rules: {
						required: true,
						message: '请输入产品名称'
					}
				},
				{
					name: 'productType',
					label: '产品类别',
					value: '',
					col: 6,
					disabled: 'add' !== this.editPageMode,
					rules: {
						required: true,
						message: '请输入产品类别'
					}
				},
				{
					name: 'annualRateStart',
					label: '参考年利率(%)',
					value: '',
					col: 6,
					rules: {
						required: true,
						message: '请输入参考年利率'
					},
					verify: 'required',
					hide: 'add' !== this.editPageMode,
					inline: {
						name: 'annualRateEnd',
						rules: {
							required: true,
							message: '请输入参考年利率'
						},
					},
				},
				{
					name: 'financingAmountStart',
					label: '融资额度(万)',
					value: '',
					col: 6,
					hide: 'add' !== this.editPageMode,
					rules: {
						required: true,
						message: '请输入融资额度'
					},
					inline: {
						name: 'financingAmountEnd',
						rules: {
							required: true,
							message: '请输入融资额度'
						},
					},
				},
				{
					name: 'financingPeriodStart',
					label: '融资期限(月)',
					value: '',
					col: 6,
					hide: 'add' !== this.editPageMode,
					rules: {
						required: true,
						message: '请输入融资期限'
					},
					inline: {
						name: 'financingPeriodEnd',
					},
				},
				{
					name: 'annualRateVO',
					label: '参考年利率(%)',
					value: '',
					col: 4,
					disabled: 'add' !== this.editPageMode,
					hide: 'add' === this.editPageMode,
				},
				{
					name: 'financingAmountVO',
					label: '融资额度(万)',
					value: '',
					col: 4,
					disabled: 'add' !== this.editPageMode,
					hide: 'add' === this.editPageMode,
				},
				{
					name: 'financingPeriodVO',
					label: '融资期限(月)',
					value: '',
					col: 4,
					disabled: 'add' !== this.editPageMode,
					hide: 'add' === this.editPageMode,
				},
				{
					name: 'applicableCustomers',
					label: '适用客户',
					value: '',
					col: 12,
					disabled: 'add' !== this.editPageMode,
					rules: {
						required: true,
						message: '请输入适用客户'
					}
				},
				{
					name: 'introduction',
					label: '产品介绍',
					value: '',
					col: 12,
					disabled: 'add' !== this.editPageMode,
					type: 'textarea',
					rules: {
						required: true,
						message: '请输入产品介绍'
					}
				},
				{
					name: 'status',
					label: '启用状态',
					value: '',
					col: 3,
					disabled: !('edit' === this.editPageMode || 'add' === this.editPageMode),
					type: 'switch',
					rules: {
						required: true,
						message: '请输入启用状态(0.停用、1.启用)'
					},
					data: [
						{value: 0, text: '停用'},
						{value: 1, text: '启用'},
					]
				},
				{
					name: 'auditStatus',
					label: '审核状态',
					value: '',
					col: 6,
					disabled: !('edit' === this.editPageMode),
					type : 'radio',
					rules: {
						required: true,
						message: '请输入审核状态(0.待审核、1.已通过、2.已驳回)'
					},
					data: [
						{ value: 0, label: '待审核', checked: true},
						{ value: 1, label: '已通过'},
						{ value: 2, label: '已驳回'},
					]
				},
				{
					name: 'auditOpinion',
					label: '审核意见',
					value: '',
					col: 12,
					disabled: !('edit' === this.editPageMode || 'audit' === this.editPageMode),
					type: 'textarea',
					rules: {
						required: 'audit' === this.editPageMode,
						message: '请输入审核意见'
					}
				},
				{
					type: 'button',
					contents: [
						{
							type: 'primary',
							text: '保存',
							event: 'submit',
							hide: !('edit' === this.editPageMode || 'add' === this.editPageMode),
						},
						{
							type: 'reset',
							text: '取消',
						},
						{
							type: 'primary',
							text: '审核通过',
							event: 'click',
							hide: 'audit' !== this.editPageMode,
						},
						{
							type: 'danger',
							text: '驳回',
							event: 'click',
							hide: 'audit' !== this.editPageMode,
						},
					]
				}
			]
		}
	},
	methods: {
		selectData(res) {
			this.$request({
				url: httpApi.jobCoFinanceSelectData,
				params: { orgName: res }
			}).then(res => {
				// 处理自定义的数据格式化, extFiled 额外属性名， key、val 数据映射
				// this.viewData = this.formatExtraData(res.results[*], 'extFiled', 'key', 'val');
				this.selectDataList = res.results;
			});
		}
	},
};
