/*  接口地址定义
    方式1：
        //获取xxx信息
        export const getInfoStatistical = '/hr/shycInfo/getInfoStatistical.dhtml';
    调用方式1：
        import {getInfoStatistical} from '@/http/system.js';
        console.log(getInfoStatistical)

    方式2：
        //系统管理模块
        const system = {
            //用户、用户组树
            getDeptTree: '/bootdemo/simplesysDept/getDeptTree.djson',
        }
        export default system;
    调用方式2:
        import system from '@/http/system.js';
        console.log(system.getDeptTree)
        
*/

//接口地址
const api = {
	// 岗位转岗
	hrnPersonPostTransferList: '/ybzy/hrnPersonPostTransfer/listJson',
	hrnPersonPostTransferInfo: '/ybzy/hrnPersonPostTransfer/info',
	hrnPersonPostTransferSave: '/ybzy/hrnPersonPostTransfer/save',
	hrnPersonPostTransferUpdate: '/ybzy/hrnPersonPostTransfer/update',
	hrnPersonPostTransferDeleteById: '/ybzy/hrnPersonPostTransfer/deleteById',
	hrnPersonPostTransferTree: '/ybzy/hrnPersonPostTransfer/Tree'
};
export default api;
