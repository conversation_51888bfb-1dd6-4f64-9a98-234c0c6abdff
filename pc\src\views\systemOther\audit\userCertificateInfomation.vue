<template>
	<div class="main_container">
		<div class="headContainer">
			<el-button type="primary" style="margin-bottom: 10px" size="small" @click="handleAdd">
				新增
			</el-button>
			<el-input style="width: 200px" placeholder="请输入关键字搜索" v-model="search"></el-input>
		</div>
		<es-data-table :data="tableData" :key="tableKey" :border="true">
			<template slot="append">
				<el-table-column label="证书号" prop="id"></el-table-column>
				<el-table-column label="颁发者" prop="user"></el-table-column>
				<el-table-column label="申请者" prop="applyUser"></el-table-column>
				<el-table-column label="证书类型" prop="type"></el-table-column>
				<el-table-column label="有效期范围" prop="range"></el-table-column>
				<el-table-column label="状态" prop="status"></el-table-column>
				<el-table-column label="操作用户" prop="handleBy"></el-table-column>
				<el-table-column label="操作时间" prop="handleTime"></el-table-column>
				<el-table-column label="操作类型" prop="handleType"></el-table-column>
				<el-table-column label="操作">
					<template slot-scope="scope">
						<el-button @click="handleCheck(scope.row)" type="text">查看</el-button>
						<el-button @click="hanldeEdit(scope.row)" type="text">编辑</el-button>
						<el-button @click.native.prevent="deleteRow(scope.$index, tableData)" type="text">
							删除
						</el-button>
					</template>
				</el-table-column>
			</template>
		</es-data-table>
		<el-dialog :title="title" :visible.sync="dialogFormVisible" append-to-body>
			<el-form
				:model="form"
				label-width="120px"
				:rules="rules"
				ref="formRef"
				:disabled="isDisabled"
			>
				<el-form-item label="证书号" prop="id">
					<el-input v-model="form.id"></el-input>
				</el-form-item>
				<el-form-item label="颁发者" prop="user">
					<el-input v-model="form.user"></el-input>
				</el-form-item>
				<el-form-item label="申请者" prop="applyUser">
					<el-input v-model="form.applyUser"></el-input>
				</el-form-item>
				<el-form-item label="证书类型" prop="type">
					<!-- <el-input v-model="form.type"></el-input> -->
					<el-select v-model="form.type">
						<el-option label="SSL/TLS证书" value="SSL/TLS证书"></el-option>
						<el-option label="代码签名证书" value="代码签名证书"></el-option>
					</el-select>
				</el-form-item>
				<!-- <el-form-item label="申请者" prop="applyUser">
					<el-input v-model="form.applyUser"></el-input>
				</el-form-item> -->
				<el-form-item label="请选择时间" prop="range">
					<el-date-picker
						v-model="form.range"
						type="daterange"
						range-separator="至"
						format="yyyy-MM-dd"
						value-format="yyyy-MM-dd"
						start-placeholder="开始日期"
						end-placeholder="结束日期"
						@change="dateChange"
					></el-date-picker>
				</el-form-item>
				<el-form-item label="状态" prop="status">
					<el-select v-model="form.status">
						<el-option label="有效" value="有效"></el-option>
						<el-option label="无效" value="无效"></el-option>
					</el-select>
				</el-form-item>
				<el-form-item label="操作类型" prop="handleType">
					<el-select v-model="form.handleType">
						<el-option label="申请" value="申请"></el-option>
						<el-option label="续约" value="续约"></el-option>
						<el-option label="吊销" value="吊销"></el-option>
					</el-select>
				</el-form-item>
				<el-form-item label="操作时间" prop="range">
					<el-date-picker
						v-model="form.handleTime"
						type="datetime"
						format="yyyy-MM-dd"
						value-format="yyyy-MM-dd"
					></el-date-picker>
				</el-form-item>
			</el-form>
			<div slot="footer" class="dialog-footer">
				<el-button @click="dialogFormVisible = false">取 消</el-button>
				<el-button type="primary" @click="handleSure()">确 定</el-button>
			</div>
		</el-dialog>
	</div>
</template>

<script>
export default {
	data() {
		return {
			tableData: [
				{
					id: '12345678',
					user: '测试',
					applyUser: '测试用户1',
					type: 'SSL/TLS证书',
					range: '2020-01-01至2024-12-01',
					status: '有效',
					handleBy: '管理员',
					handleTime: '2020-01-01',
					handleType: '申请'
				}
			],
			form: {},
			dialogFormVisible: false,
			title: '新增',
			rules: {
				id: [{ required: true, message: '请填写', trigger: 'blue' }],
				user: [{ required: true, message: '请填写', trigger: 'blue' }],
				applyUser: [{ required: true, message: '请填写', trigger: 'blue' }],
				type: [{ required: true, message: '请填写', trigger: 'blue' }],
				range: [{ required: true, message: '请填写', trigger: 'blue' }],
				status: [{ required: true, message: '请填写', trigger: 'blue' }],
				handleBy: [{ required: true, message: '请填写', trigger: 'blue' }],
				handleTime: [{ required: true, message: '请填写', trigger: 'blue' }],
				handleType: [{ required: true, message: '请填写', trigger: 'blue' }]
			},
			tableKey: 0,
			isDisabled: false,
			search: ''
		};
	},
	computed: {
		filteredTableData() {
			if (!this.search) {
				return this.tableData; // 如果没有搜索词，则返回所有数据
			}
			return this.tableData.filter(item => {
				// 假设你想在多个字段上搜索，比如 id, user, type 等
				return (
					item.id.toLowerCase().includes(this.search.toLowerCase()) ||
					item.user.toLowerCase().includes(this.search.toLowerCase()) ||
					item.type.toLowerCase().includes(this.search.toLowerCase())
					// 可以继续添加其他字段
				);
			});
		}
	},
	methods: {
		deleteRow(index, rows) {
			rows.splice(index, 1);
			this.$message.success('操作成功');
		},
		hanldeEdit(row) {
			this.dialogFormVisible = true;
			this.isDisabled = false;
			this.form = row;
			this.title = '查看/修改';
		},
		dateChange(val) {
			console.log(val);
		},
		handleAdd() {
			this.dialogFormVisible = true;
			this.isDisabled = false;
			this.title = '新增';
		},
		handleSure() {
			this.$refs.formRef.validate(valid => {
				if (valid) {
					if (this.title == '新增') {
						this.form.range = this.form.range[0] + '至' + this.form.range[1];
						this.form.handleBy = '管理员';
						this.tableData.push(this.form);
						this.tableKey = Math.random();
						console.log(this.tableData, 789);
						this.dialogFormVisible = false;
						this.form = {};
						this.$message.success('新增成功');
					} else {
						this.$message.success('编辑成功');
						this.dialogFormVisible = false;
					}
				}
			});
			// if (!this.dateRange) {
			// 	this.$message.warning('请选择日期');
			// 	return;
			// }
		},
		handleCheck(row) {
			this.dialogFormVisible = true;
			this.isDisabled = true;
			this.form = row;
		}
	}
};
</script>

<style lang="scss" scoped>
.main_container {
	padding: 35px 20px;
}
.headContainer {
	display: flex;
	align-items: center;
	justify-content: space-between;
	margin-bottom: 10px;
}
</style>
