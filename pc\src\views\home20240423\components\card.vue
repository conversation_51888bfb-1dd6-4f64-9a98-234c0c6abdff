<template>
	<div class="card" :style="myStyle">
		<div class="card-box">
			<span v-if="title" class="card-box-title">{{ title }}</span>
			<!-- 插槽 -->
			<slot class="card-box-content"></slot>
		</div>
	</div>
</template>

<script>
export default {
	props: {
		myStyle: {
			type: Object,
			default: () => {
				return {
					width: '470px',
					height: '415px'
				};
			}
		},
		title: {
			type: String,
			default: ''
		}
	},
	data() {
		return {};
	},
	created() {},
	methods: {}
};
</script>

<style lang="scss" scoped>
.card {
	width: 470px;
	height: 415px;
	background: linear-gradient(180deg, rgba(239, 255, 255, 0.6) 0%, rgba(229, 250, 255, 0) 100%);
	box-shadow: 0px 2px 10px 0px rgba(70, 145, 217, 0.2);
	border-radius: 8px;
	backdrop-filter: blur(3px);
	overflow: hidden;
	.card-box {
		width: 100%;
		height: 100%;
		border: 2px solid;
		border-image: linear-gradient(180deg, rgba(255, 255, 255, 1), rgba(255, 255, 255, 0)) 2 2;
		backdrop-filter: blur(3px);
		padding: 14px 12px;

		position: relative;
		&::after {
			content: '';
			position: absolute;
			top: -2px;
			left: -2px;
			bottom: 0;
			right: -2px;
			height: 10%;
			border: 2px solid #fff;
			border-radius: 8px 8px 0 0;
			border-bottom: none;
			z-index: -1;
		}
	}
	.card-box-title {
		height: 24px;
		font-family: MicrosoftYaHei, MicrosoftYaHei;
		font-weight: bold;
		font-size: 18px;
		color: #053b6d;
		line-height: 24px;
		text-align: left;
		font-style: normal;
	}
	.card-box-content {
		width: 100%;
		height: 100%;
	}
}
</style>
