.amend-standing {
	padding: 20px;
	height: calc(100% - 23px);
	display: flex;
	justify-content: space-between;

	.tree {
		width: 250px;
		height: 100%;
		border:1px solid #eee;
	}

	.content {
		width: calc(100% - 260px);
	}
}

// 	.tree {
// 		width: 250px;
// 		height: 100%;
// 	}
// 	// .content {
// 	// 	width: calc(100% - 260px);
// 	// 	// display: flex;
// 	// 	height: 100%;
// 	// 	.top {
// 	// 		font-size: 13px;
// 	// 		text-align: right;
// 	// 		width: 100%;
// 	// 		.top-right {
// 	// 			width: 250px;
// 	// 			.el-input-group__append {
// 	// 				// background: var(--btnColor) !important;
// 	// 				color: #fff;
// 	// 			}
// 	// 		}
// 	// 	}
// 	// 	.write-check,
// 	// 	.es-data-table {
// 	// 		margin-top: 10px;
// 	// 		height: 100%;
// 	// 	}
// 	// }
// 	.sum {
// 		margin: 0px 10px;
// 	}
// }