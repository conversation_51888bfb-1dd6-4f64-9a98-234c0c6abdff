@import "./base.less";

//主题色
@themeColor:#e94c41;
@themeColor2:#2EB7F5;
@themeColor3: #87D068;
@themeColor4: #FFAA00;
//前景色、字体颜色
@forecolor:#333;
//字体颜色
@fontColor:#333;
//body背景色
@bodyColor:#f8f8f8;
//背景色
@backgroundColor:#fff;
//边框颜色
@borderColor:#dddddd;
//超链接颜色
@linkColor:#28a9e4;
//placeholder(输入框提示)颜色
@placeholderColor:#b3b3b3;
//输入框获取焦点字体颜色
@focusColor:#333;
//输入框获取焦点背景颜色
@focusBgColor:#fff;
//输入框获取焦点边框颜色
@focusBoderColor:#d2d2d2;
//下拉选择选中颜色
@selectColor:#5FB878;
//滚动条前景色
@scrollColor:#A8A8A8;
//滚动条背景色
@scrollBarColor:rgba(0,0,0,.1);
//头部背景
@headerColor:#C0220F;
//侧边导航栏背景色
@sideBgColor:#32110c;
//侧边导航栏hover背景色
@sideHoverBgColor:rgba(255, 255, 255, .1);
//侧边导航栏hover色
@sideHoverColor:#e94c41;
//侧边导航栏选中背景颜色
@sideActiveBgColor:rgba(255, 255, 255, .1);
//侧边导航栏选中颜色
@sideActiveColor:#fff;
//菜单hover前景色
@menuColor : #e94c41;
//菜单hover背景色
@menuBgColor: #fdebea;
//菜单hover边框色
@menuBorderColor: #f5aba6;
//下载颜色
@downColor:#999;
//下载颜色
@timeColor:#999;
@headline:#B75143;
@tableheadBgColor: #f3f3f3;
@tableheadColor:#333;

body{
	background-color: @bodyColor;
}
.layui-scrollbar-horizontal,.layui-scrollbar-vertical{
	background-color: @scrollBarColor;
	.layui-scrollbar-thumb{
		background-color: @scrollColor;
	}
}
.layui-layout-admin{
	.lay-header{
		background-color: @headerColor;
		color: contrast(@headerColor);
		& when (@headerColor = #fff){
			box-shadow: 0px 3px 15px 0px rgba(128, 151, 163, .28);
		}
		.lay-tools-user::after{
			background-color: contrast(@headerColor);
		}
		.layui-nav-tree{
			.layui-nav-item{
				&.layui-this,
				&:hover {
					background-color: transparent;
					a{
						background-color: transparent;
					}
				}
				a{
					color: contrast(@headerColor);
				}
			}
		} 
		.lay-tools-online{
			.lay-online-num{
				color: #ff0000;
				& when not (@headerColor = #fff) {
					color: rgb(255, 234, 0);
				}
			}
		}
		.lay-user-info{
			.lay-user-job{
				.layui-edge {
					border-top-color:contrast(@headerColor);
				}
				.layui-select-input{
					&.lay-focus{
						color: contrast(@headerColor);
					}
				}
			}
		}
		.lay-tools-system{
			a{
				color: contrast(@headerColor);
				&:hover{
					background-color:desaturate(@headerColor, 15%);
				}
			}
		}
	}
	.lay-side{
		background-color: @sideBgColor;
		.lay-sidenav{
			 .layui-nav-item{
				a{
					color: contrast(@sideBgColor);
					&:hover{
						background-color:@sideHoverBgColor;
						color:@sideHoverColor!important;
					}
				}
				&.layui-this{
					background-color: transparent;
					a{
						color: @sideActiveColor;
						background-color: @sideActiveBgColor;
						&::before{
							background-color: @sideBgColor;
						}
					}
				}
			}
		}
		.lay-sidenav-box{
			.lay-scrollbar-thumb{
				background-color: @themeColor;
			}
		}
		.lay-menubar-box{
			.lay-menu-title{
				color: contrast(@themeColor);
				background-color:@themeColor;
			}
			.lay-menu-item{
				&:hover{
					background-color: transparent;
				}
				.lay-menu-body-title{
					color: @fontColor;
					border-bottom-color: @borderColor;
					&:hover{
						color: @menuColor;
						border-color: @menuBorderColor;
						background-color: @menuBgColor;
					}
				}
				&.layui-menu-item-down,&.layui-menu-item-checked{
					border-color: @borderColor;
					background-color: transparent!important;
					&>.layui-menu-body-title{
						color: @menuColor;
						&::before{ 
							background-color: @menuColor;
						}
					}
				}
				.lay-menu-child{
					.layui-menu-item-down{
						border-color: @borderColor;
						&>.layui-menu-body-title{
							color: @menuColor;
							&::before{ 
								background-color: @menuColor;
							}
						}
					}
				}
			}
		}
	}
}
.lay-form-box,.lay-title,.lay-caption,.lay-table,.lay-page-full-white{
	background-color: @backgroundColor;
}
.layui-btn-submit{
	background-color:@themeColor
}
.layui-btn-add{
	background-color:@themeColor
}
.layui-btn-second {
	background-color: @themeColor4;
}

.layui-btn-cancel {
	background-color: #eeeeee;
	border-color    : @borderColor;
	color           : #333333;

	&:hover {
		color       : #333333;
		border-color: @borderColor;
	}
}

.layui-btn-primary {
	background-color: @backgroundColor;
	border-color: @borderColor;
	color: #333;
	&:hover {
		color: #333;
		border-color: @borderColor;
	}
}
.layui-btn-group{
	.layui-btn-primary{
		.layui-btn-primary;
		border-left: solid 1px @borderColor;
	}
}

button[disabled] {
	background  : #eeeeee !important;
	border-color: @borderColor;
	color       : #666666 !important;
	cursor      : not-allowed;
}

.layui-tab {
	.lay-toolbar{
		background: @backgroundColor;
		border-bottom: solid 1px @borderColor;
	}
	.layui-tab-title {
		background-color: @backgroundColor;
		border-color    : @borderColor;

		li {
			color: #a6a6a6;

			&:hover {
				color: @themeColor;
			}

			&.layui-this {
				color: @themeColor;

				&::after {
					background-color: @themeColor;
				}
			}

			.layui-badge {
				background-color: @themeColor;
			}
		}
	}

	&.tab-card {
		.layui-tab-title {
			display: flex;

			li {
				flex         : 1;
				border-radius: 10px 10px 0px 0px;
				border       : solid 1px #EEEEEE;
				border-bottom: none;

				&.layui-this {
					color     : white;
					background: @themeColor;
					border    : none;

					&::after {
						background-color: transparent;
					}
				}
			}
		}
	}

	&.tab-round {
		.layui-tab-title {
			padding: 7px;

			li {
				border-radius: 20px;
				height       : 30px;
				line-height  : 30px;

				&.layui-this {
					color     : white;
					background: @themeColor;

					&::after {
						background-color: transparent;
					}
				}
			}
		}
	}
}
.lay-headline{
	background-color: @backgroundColor;
	&::before {
		background: @headline;
	}
}
.no-value{
	color: @placeholderColor;
}
.layui-input,.layui-textarea{
	border-color: @borderColor;
	&::-webkit-input-placeholder {
		color: @placeholderColor;
	}
	&:-moz-placeholder {
		color: @placeholderColor;
	}
	&:-ms-input-placeholder {
		color: @placeholderColor;
	}
	/* &:not(.layui-input[readonly],.layui-form-danger,.layui-textarea[readonly]){
		border-color: @borderColor;
		&:focus{
			color: @focusColor;
			background-color:@focusBgColor;
			border-color: @focusBoderColor!important;
			&::-webkit-input-placeholder {
				color: @focusColor;
			}
			&:-moz-placeholder {
				color: @focusColor;
			}
			&:-ms-input-placeholder {
				color: @focusColor;
			}
		}
	} */
	&.layui-disabled{
		color: @fontColor!important;
	}
}
.lay-focus{
	color: @focusColor;
	background-color:@focusBgColor;
	border-color:@focusBoderColor;
	&+.layui-edge{
		border-top-color: @focusColor;
	}
}
.layui-form-select{
	// border: solid 1px #d1d1d1 !important;
	// box-sizing: border-box;
	.layui-hide-input{
		border-color: @borderColor;
	}
	dl{
		//color: @focusColor;
		//background-color:@focusBgColor;
		border-color: @focusBoderColor;
		dd{
			color: @fontColor;
			&.layui-this{
				background-color: @selectColor;
			}
		}
	}
}

.lay-tabs {
	background-color: #fff;
	.lay-tabs-title {
		border-color: @borderColor;
		li{
			color: #a6a6a6;
			&.layui-this {
				color: @themeColor;
				&::after{
					background-color: @themeColor;
				}
			}
		}
	}
}
.layui-laydate{
	.layui-laydate-content{
		.layui-this{
			background-color: @themeColor;
		}
	}
}

.layui-table{
	thead{
		tr{
			th{
				background-color: @tableheadBgColor;
				color: @tableheadColor;
				border-color: #ddd;
				.layui-table-sort{
					.layui-table-sort-asc{
						border-bottom-color:@tableheadColor
					}
					.layui-table-sort-desc{
						border-top-color:@tableheadColor
					}
				}
			}
		}
	}
	td{
		border-color: #ddd;
		.layui-input,.layui-textarea{
			&:read-write:focus{
				color: @focusColor;
				background-color: @focusBgColor;
				border: 1px solid @focusBoderColor !important;
				position: relative;
			}
			// &.layui-form-danger:focus{
			// 	border: 1px solid #FF5722!important;
			// }
		}
	}
	.layui-table-row-col {
		background: #f3f3f3;
	}

	.layui-form-checked {
	
		span,
		&:hover span {
			background-color: transparent
		}
	
		i,
		&:hover i {
			color: @themeColor
		}
	
	}
	
	.layui-form-checkbox {
		// margin-bottom: 5px;
		&[lay-skin=primary] {
			i{
				border-color : #666666 !important;
				background-color: white
			}
			span {
				background: transparent;
			}
		
			&:hover i {
				border-color: @themeColor
			}
		}
	}
}
.lay-table-box {
	.lay-table-form
	{
		.lay-table-header {
			.icon-add {
				background-color: @themeColor;
				color           : contrast(@headerColor);
				border-color: #ddd;
			}
		}
	}
}
.lay-data-tablebox{
	td[data-edit="true"]{
		.layui-table-cell{
			border: solid 1px @borderColor;
			border-radius:3px;
		}
	}
	.lay-link {
		color : @linkColor;
		cursor: pointer;
	}
}
.layui-table-view{
	border-color: #ddd;
	background-color: @backgroundColor;
}

.lay-loading {
	i{
		color: @themeColor;
	}
}
.lay-data-table{
	.lay-toolbar{
		background-color: @backgroundColor;
		border-color: @borderColor;
		.lay-search{
			.layui-select-title input,input{
				border-color: @themeColor!important;;
			}
			i{
				border-top-color: @themeColor;
			}
		}
	}
	.lay-form-select .layui-select-title .layui-hide-input,.lay-input-box .layui-input{
		border-color: @themeColor !important;
	}
	.lay-form-select .layui-edge{ border-top-color: @themeColor;}
	.layui-btn-group {
		.layui-btn:first-child {
			border-left: 1px solid @borderColor;
			i {
				color: @themeColor;
			}
		}
	}
}
.lay-table-toolbar{
	background-color: @backgroundColor;
	border-color: @borderColor;
}
.layui-table-body{
	background-color: @backgroundColor;
}
.layui-table-page {
	.layui-laypage {
		select{
			&:focus{
				border-color:@themeColor!important;
			}
		}
	}
}
.themeColor{
	color: @themeColor;
}
.lay-layer-content{
	border-color: @borderColor;
}
.layui-layer-btn {
	.layui-layer-btn0 {
		background: @themeColor;
		background-color: @themeColor !important;
		border-color: transparent !important;
	}
}
.lay-tree-box{
	background-color: @backgroundColor;
	.lay-tree-title{
		&:hover{
			background-color: #e7f4f9;
		}
		&.active{
			background-color: #e7f4f9;
		}
	}
}
.lay-tree-bar{
	border-color: @borderColor;
	.lay-tree-search,.lay-tree-headbar{
		border-color: @borderColor;
	}
	&+.lay-form-box,&+.lay-table{
		.lay-toolbar{
			border-color: @borderColor;
		}
	}
}
.lay-tree{
	.lay-tree-checkbox{

		.layui-form-checkbox.layui-checkbox-disabled{
			i{
				color: white!important;
			}
			&:hover{
				i{
					border-color: #d2d2d2;
				}
			}
		}
	}
}
.lay-tab-tree{
	.layui-tab-title {
		border-right: solid 1px @borderColor;
	}
}
.lay-list{
	.lay-list-icon{
		color: @themeColor;
	}
	.lay-list-time{
		color: @timeColor;
	}
}
.lay-lump{
	.lay-list-item{
		&:nth-child(1n){
			.lay-list-icon{
				color: #fff;
				background-color: #F7A227;
			}
		}
		&:nth-child(2n){
			.lay-list-icon{
				color: #fff;
				background-color: #4EA06B;
			}
		}
		&:nth-child(3n){
			.lay-list-icon{
				color: #fff;
				background-color: #C45A3B;
			}
		}
	}
}
.mCSB_scrollTools{
	.mCSB_dragger{
		.mCSB_dragger_bar{
			background-color: @scrollColor!important;
		}
		.mCSB_draggerRail{
			background-color: @scrollBarColor!important;
		}
	}
}
.lay-have-submit{
	.lay-form-btns{
		background-color: @backgroundColor;
	}
}
.layui-laypage{
	.layui-laypage-curr{
		em{
			color: @themeColor;
		}
	}
}

.lay-scrollbar-horizontal,.lay-scrollbar-vertical{
	background-color: @scrollBarColor;
	.lay-scrollbar-thumb{
		background-color: @scrollColor;
	}
}

.lay-tag{
	background-color: @sideHoverBgColor;
}
.layui-radio-box {
	&.card {
		.lay-radio-box {
			border: 1px solid #EEEEEE;
		}
		.layui-form-radio {
			background: rgb(247,247,247);
			&:hover *{
				color: inherit;
			}
			&.layui-form-radioed {
				background: @themeColor;
				color: white;
			}
		}
	}
}
.lay-checkbox-box{
	.layui-disabled{
		color: #c2c2c2!important;
		span{
			color: #c2c2c2!important;
		}
		i{
			color: rgba(255,255,255,0);
			border-color: #c2c2c2!important;
		}
	}
	
}

.lay-editor{
	.layui-tab-content{
		background-color: @backgroundColor;
	}
	.lay-editor-set-box{
		color:#999 ;
	}
	.lay-seting{
		background-color: @backgroundColor;
	}
}