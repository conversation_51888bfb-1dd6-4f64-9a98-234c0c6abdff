<template>
	<div class="main">
		<div class="card-box">
			<mini-card v-for="(item, index) in card" :key="index" :card-data="item" />
		</div>
		<div class="Report-filling">
			<div class="searchContent">
				<div class="title">
					<img src="@/assets/images/systemOther/report-icon3.png" alt="" />
					<span>填表填报</span>
				</div>
				<el-form :inline="true" ref="formRef" :model="formInline">
					<el-form-item prop="name">
						<el-input
							v-model="formInline.name"
							style="width: 150px"
							placeholder="输入关键字搜索"
						></el-input>
					</el-form-item>
					<el-form-item label="填表状态" prop="status">
						<el-select v-model="formInline.status" placeholder="请选择" style="width: 150px">
							<el-option label="待填报" value="0"></el-option>
							<el-option label="已审核" value="1"></el-option>
							<el-option label="已通过" value="2"></el-option>
							<el-option label="已驳回" value="3"></el-option>
						</el-select>
					</el-form-item>
					<el-form-item label="填表周期" prop="circle">
						<el-select v-model="formInline.circle" style="width: 150px" placeholder="请选择">
							<el-option label="日报" value="0"></el-option>
							<el-option label="周报" value="1"></el-option>
							<el-option label="月报" value="2"></el-option>
							<el-option label="年报" value="3"></el-option>
						</el-select>
					</el-form-item>
					<el-form-item label="填报时间" prop="time">
						<el-date-picker
							style="width: 150px"
							v-model="formInline.time"
							type="date"
							placeholder="选择日期"
						></el-date-picker>
					</el-form-item>
					<el-form-item>
						<el-button class="s-button" type="primary" @click="onSubmit" icon="el-icon-search">
							查询
						</el-button>
						<el-button class="s-button" @click="onReset" icon="el-icon-refresh-right">
							重置
						</el-button>
					</el-form-item>
				</el-form>
			</div>
			<div class="tableContent">
				<es-data-table
					:page="page"
					:data="tableData"
					stripe
					style="width: 100%"
					:border="true"
					:sizer="false"
				>
					<template slot="prepend">
						<el-table-column type="index" label="序号" width="80"></el-table-column>
						<el-table-column prop="name" label="报表名称"></el-table-column>
						<el-table-column prop="circle" label="填报周期"></el-table-column>
						<el-table-column prop="status" label="报表状态"></el-table-column>
						<el-table-column prop="limit" label="填报期限"></el-table-column>
						<el-table-column prop="time" label="填报时间"></el-table-column>
						<el-table-column fixed="right" label="操作" width="200">
							<template slot-scope="scope">
								<el-button @click="handleCheck(scope.row)" type="text" size="small">查看</el-button>
								<el-button @click="handleFill(scope.row)" type="text" size="small">填报</el-button>
							</template>
						</el-table-column>
					</template>
				</es-data-table>
			</div>
		</div>
		<detailDialog :visible="visible" @closeDialog="closeDialog" :title="title"></detailDialog>
	</div>
</template>

<script>
import MiniCard from '@/components/show/mini-card.vue';
import detailDialog from './components/dialog.vue';
export default {
	components: {
		MiniCard,
		detailDialog
	},
	data() {
		return {
			title: '',
			visible: false,
			page: {
				pageSize: 10,
				totalCount: 0,
				position: 'right'
			},
			formInline: {},
			card: [
				{
					img: require('@/assets/images/systemOther/report-icon1.png'),
					title: '待填报',
					unit: '份',
					width: '49%',
					num: '0'
				},
				{
					img: require('@/assets/images/systemOther/report-icon2.png'),
					title: '已填报',
					unit: '份',
					width: '49%',
					num: '0'
				}
			],
			tableData: [
				// {
				// 	name: '社团(协会)基本数据子类表',
				// 	circle: '日报',
				// 	status: '待填报',
				// 	limit: '2023-10-18 至 2023-10-19',
				// 	time: '2023-10-18 10:35:58'
				// },
				// {
				// 	name: '社团(协会)基本数据子类表',
				// 	circle: '日报',
				// 	status: '待填报',
				// 	limit: '2023-10-18 至 2023-10-19',
				// 	time: '2023-10-18 10:35:58'
				// },
				// {
				// 	name: '社团(协会)基本数据子类表',
				// 	circle: '日报',
				// 	status: '待填报',
				// 	limit: '2023-10-18 至 2023-10-19',
				// 	time: '2023-10-18 10:35:58'
				// },
				// {
				// 	name: '社团(协会)基本数据子类表',
				// 	circle: '日报',
				// 	status: '待填报',
				// 	limit: '2023-10-18 至 2023-10-19',
				// 	time: '2023-10-18 10:35:58'
				// },
				// {
				// 	name: '教师画像数据子类表',
				// 	circle: '日报',
				// 	status: '待填报',
				// 	limit: '2023-10-18 至 2023-10-19',
				// 	time: '2023-10-18 10:35:58'
				// },
				// {
				// 	name: '举办社团活动数据子类表',
				// 	circle: '日报',
				// 	status: '待填报',
				// 	limit: '2023-10-18 至 2023-10-19',
				// 	time: '2023-10-18 10:35:58'
				// },
				// {
				// 	name: '奖助贷申请数据子类表',
				// 	circle: '日报',
				// 	status: '待填报',
				// 	limit: '2023-10-18 至 2023-10-19',
				// 	time: '2023-10-18 10:35:58'
				// },
				// {
				// 	name: '综合成绩与评价数据子类表',
				// 	circle: '日报',
				// 	status: '待填报',
				// 	limit: '2023-10-18 至 2023-10-19',
				// 	time: '2023-10-18 10:35:58'
				// },
				// {
				// 	name: '社团(协会)基本数据子类表',
				// 	circle: '日报',
				// 	status: '待填报',
				// 	limit: '2023-10-18 至 2023-10-19',
				// 	time: '2023-10-18 10:35:58'
				// },
				// {
				// 	name: '社团(协会)基本数据子类表',
				// 	circle: '日报',
				// 	status: '待填报',
				// 	limit: '2023-10-18 至 2023-10-19',
				// 	time: '2023-10-18 10:35:58'
				// },
				// {
				// 	name: '社团(协会)基本数据子类表',
				// 	circle: '日报',
				// 	status: '待填报',
				// 	limit: '2023-10-18 至 2023-10-19',
				// 	time: '2023-10-18 10:35:58'
				// },
				// {
				// 	name: '社团(协会)基本数据子类表',
				// 	circle: '日报',
				// 	status: '待填报',
				// 	limit: '2023-10-18 至 2023-10-19',
				// 	time: '2023-10-18 10:35:58'
				// }
			]
		};
	},
	methods: {
		onSubmit() {},
		onReset() {
			this.$refs.formRef.resetFields();
		},
		handleCheck(row) {
			this.visible = false;
			this.title = '信息查看';
			this.visible = true;
		},
		handleFill() {
			this.visible = false;
			this.title = '信息填报';
			this.visible = true;
		},
		closeDialog() {
			this.visible = false;
		}
	}
};
</script>

<style lang="scss" scoped>
@import '@ast/style/public.scss';
/**穿透修改卡片组件*/
.main {
	height: 100%;
	width: 100%;
	background: #f0f2f5;
	padding: 12px;
	overflow-y: scroll;
	.card-box {
		@include flexBox(space-between);
		width: 100%;
		margin: 12px 0;
		background: #f0f2f5;

		// .card {
		// 	width: 24.5% !important;
		// }
	}
	.Report-filling {
		margin-top: 10px;
		border-radius: 8px;
		overflow: hidden;
		background: #ffffff;
		height: 700px;
		.searchContent {
			@include flexBox(space-between);
			padding: 10px;
			border-bottom: 1px solid #f0f0f0;
			.title {
				@include flexBox(align-items);
				font-size: 16px;
				font-weight: bold;
			}
			img {
				margin-right: 10px;
				width: 40px;
				height: 40px;
			}
			.el-form-item {
				margin-bottom: 0;
			}
			.s-button {
				border-radius: 9px;
			}
		}
		.tableContent {
			width: 100%;
			padding: 10px;
		}
	}
}
</style>
