<template>
	<div class="sketch_content">
		<es-form
			ref="form"
			:model="formData"
			:contents="formItemList"
			height="100%"
			collapse
			:submit="infoPageMode !== '查看'"
			@change="handleFormItemChange"
			@submit="handleFormSubmit"
			@reset="infoPageClose(false)"
		></es-form>
	</div>
</template>
<script>
import api from '@/http/specific/specificVisitorAppt/api';

export default {
	name: 'InfoPage',
	props: {
		baseData: {
			type: Object,
			default() {
				return {};
			}
		},
		infoPageMode: {
			type: String,
			default() {
				return 'allOn';
			}
		}
	},
	data() {
		return {
			auditBtnVisible: false,
			disabled: true,
			ownId: '',
			formData: {
				//type为table，与name对应
				carNum: '',
				compList: []
			}
		};
	},
	computed: {
		formItemList() {
			const readonly = this.infoPageMode === '查看';
			return [
				{ type: 'caption', text: '基本信息' },
				{
					name: 'name',
					type: 'text',
					placeholder: '请输入',
					// hide: false,
					label: '访客姓名',
					readonly,
					col: 4,
					rules: {
						required: true,
						message: '请输入',
						trigger: 'change'
					}
				},
				{
					name: 'zjlx',
					type: 'select',
					label: '证件类型',
					readonly,
					placeholder: '',
					rules: {
						required: true,
						message: '请选择',
						trigger: 'chanrge'
					},
					col: 4,
					data: [
						{
							value: '身份证',
							name: '身份证'
						}
					]
				},
				{
					name: 'zjh',
					type: 'text',
					placeholder: '请输入',
					label: '证件号码',
					readonly,
					col: 4,
					rules: [
						{
							required: true,
							message: '请输入',
							trigger: 'change'
						},
						// 校身份证号码
						{
							validator: (rule, value, callback) => {
								if (!value) {
									callback(new Error('请输入'));
								} else if (
									!/^[1-9]\d{5}(18|19|([23]\d))\d{2}((0[1-9])|(10|11|12))(([0-2][1-9])|10|20|30|31)\d{3}[0-9Xx]$/.test(
										value
									)
								) {
									callback(new Error('请输入正确的身份证号码'));
								} else {
									callback();
								}
							},
							trigger: 'change'
						}
					]
				},
				{
					name: 'telephone',
					type: 'text',
					placeholder: '请输入',
					label: '手机电话',
					readonly,
					col: 4,
					rules: [
						{
							required: true,
							message: '请输入',
							trigger: 'change'
						},
						{
							validator: (rule, value, callback) => {
								if (!value) {
									callback(new Error('请输入'));
								} else if (!/^1[3456789]\d{9}$/.test(value)) {
									callback(new Error('请输入正确的手机号码'));
								} else {
									callback();
								}
							}
						}
					]
				},
				{
					name: 'visit',
					placeholder: '请选择时间范围',
					// disabled: readonly,
					readonly,
					col: 6,
					label: '预计来访时间',
					type: readonly ? 'text' : 'datetimerange',
					unlinkPanels: true,
					pickerOptions: {
						disabledDate(time) {
							// 禁止选择当前时间之前的日期
							return time.getTime() < Date.now() - 86400000; // 86400000是1天的毫秒数，这里示例允许选择从今天开始的时间，如果需要更精确到时分秒，需要进一步计算
						}
					},
					rules: {
						required: true,
						message: '请选择时间范围',
						trigger: 'change'
					}
				},
				{
					name: 'drive',
					label: '是否开车入园',
					type: 'radio',
					readonly,
					value: '上海',
					col: 4,
					data: [
						{
							name: '是',
							value: true
						},
						{
							name: '否',
							value: false
						}
					],
					events: {
						change: a => {
							this.formData.carNum = '';
						}
					}
				},
				{
					name: 'carNum',
					type: 'text',
					placeholder: '请输入',
					label: '车牌号',
					readonly,
					hide: !this.formData.drive,
					col: 4,
					rules: {
						required: true,
						message: '请输入',
						trigger: 'change'
					}
				},

				{
					name: 'purposes',
					type: 'textarea',
					placeholder: '请输入',
					label: '来访事由',
					readonly,
					col: 12,
					maxlength: '200',
					rules: {
						required: true,
						message: '请输入',
						trigger: 'change'
					}
				},
				{
					name: 'fj1',
					label: '上传照片',
					type: 'attachment',
					readonly,
					value: '',
					col: 12,
					onPreview: res => {
						console.log(res);
					},
					code: 'specific_visitor_appt_adjunct',
					listType: 'picture-card',
					ownId: this.formData.id,
					selectType: 'icon-plus'

					// rules: {
					// 	required: true,
					// 	message: '请上传照片',
					// 	trigger: 'blur'
					// }
				},

				{
					name: 'remark',
					type: 'textarea',
					placeholder: '请输入',
					label: '备注',
					readonly,
					col: 12,
					maxlength: '200',
					trigger: 'change'
				},
				{
					name: 'visited',
					label: '被访人姓名',
					readonly,
					col: 4,
					type: 'select',
					placeholder: '请输入完整的（姓名|工号|手机号）',
					'remote-key': 'keyword',
					filterable: true,
					remote: true,
					'value-type': 'object',
					'value-key': 'sysUserId',
					// 'label-key': 'label',
					url: '/ybzy/specificVisitorAppt/selectSysUserList',
					events: {
						change: a => {
							console.log(this.formData.visited, 'this.formData.visited');
							this.confirmSelector();
						}
					},
					rules: {
						required: true,
						message: '请选择',
						trigger: 'blur'
					}
				},
				{
					name: 'visitedTelephone',
					type: 'text',
					placeholder: '请输入',
					label: '被访人电话',
					readonly,
					rules: [
						{
							required: true,
							message: '请输入',
							trigger: 'change'
						},
						{
							validator: (rule, value, callback) => {
								if (!value) {
									callback(new Error('请输入'));
								} else if (!/^1[3456789]\d{9}$/.test(value)) {
									callback(new Error('请输入正确的手机号码'));
								} else {
									callback();
								}
							}
						}
					],
					col: 4
				},
				{
					name: 'visitedCollege',
					placeholder: '请选择被访人姓名',
					label: '被访所属学院/部门',
					disabled: true,
					col: 4,
					rules: {
						required: true,
						message: '请选择被访人姓名',
						trigger: 'blur'
					}
				},
				{
					name: 'compNum',
					type: 'text',
					placeholder: '0',
					label: '同行人数',
					min: 0,
					max: 500,
					disabled: true,
					readonly,
					col: 4
				},
				{
					type: 'table',
					form: true,
					name: 'compList',
					numbers: true,
					editable: true,
					readonly,
					thead: [
						{
							title: '同行人姓名',
							field: 'name',
							type: 'text',
							align: 'center',
							rules: {
								required: true,
								message: '请输入姓名',
								trigger: 'change'
							}
						},
						{
							title: '同行人电话',
							field: 'telephone',
							align: 'center',
							type: 'text',
							rules: [
								{
									required: true,
									message: '请输入电话',
									trigger: 'change'
								},
								{
									validator: (rule, value, callback) => {
										if (!value) {
											callback(new Error('请输入'));
										} else if (!/^1[3456789]\d{9}$/.test(value)) {
											callback(new Error('请输入正确的手机号码'));
										} else {
											callback();
										}
									}
								}
							]
						}
					]
				}
			];
		}
	},
	watch: {
		'formData.compList': {
			handler(val) {
				this.formData.compNum = val.length;
			},
			deep: true
		}
	},
	created() {
		this.formData = { ...this.formData, ...this.baseData };
		switch (this.infoPageMode) {
			case '新增':
				{
					this.disabled = false;
					this.auditBtnVisible = false;
				}
				break;
			case '查看':
				this.disabled = true;
				this.toolFormData(this.formData);
		}
	},
	methods: {
		confirmSelector() {
			// let data = JSON.parse(JSON.stringify(this.formData.visited[0]));
			// data = {
			// 	...data,
			// 	...JSON.parse(data.attr || '{}')
			// };
			// this.$set(this.formData, 'visitedUserId', data.showid);
			// this.$set(this.formData, 'visitedName', data.showname);
			// this.$set(this.formData, 'visitedCollegeId', data.depId);
			// this.$set(this.formData, 'visitedCollege', data.depName);
			// this.$set(this.formData, 'visitedTelephone', data.phone);

			let data = JSON.parse(JSON.stringify(this.formData.visited));

			this.$set(this.formData, 'visitedUserId', data.sysUserId);
			this.$set(this.formData, 'visitedName', data.userName);
			this.$set(this.formData, 'visitedCollegeId', data.deptId);
			this.$set(this.formData, 'visitedCollege', data.deptName);
			this.$set(this.formData, 'visitedTelephone', data.phone);
		},
		handleFormItemChange(key, val) {
			// if (key === 'compList') {
			// }
		},
		handleFormSubmit() {
			//校验
			this.$refs.form.validate(valid => {
				if (valid) {
					// 编辑未开发
					let apiUrl = this.infoPageMode === '编辑' ? api.updateN : api.saveN;
					const formData = JSON.parse(JSON.stringify(this.formData));
					let saveData = {
						...formData,
						putType: 3,
						applyType: 0,
						visitStart: formData.visit[0],
						visitEnd: formData.visit[1]
					};
					delete saveData.visited;
					// delete saveData.visited2;
					delete saveData.visit;
					delete saveData.fj1;

					//处理数据
					this.$request({
						url: apiUrl,
						data: saveData,
						method: 'POST',
						format: false
					}).then(res => {
						if (res.rCode === 0) {
							this.$message.success(res.results || '操作成功');
							this.infoPageClose(true);
						} else {
							this.$message.error(res.msg);
						}
					});
				}
			});
		},
		handleFormAudit(res) {
			//校验
			this.$refs['auditForm'].validate(valid => {
				//开启校验
				if (valid) {
					// 如果校验通过，请求接口
					let btnType = res;
					// 处理请求数据
					let auditData = {};
					auditData.id = this.formData.id;
					auditData.auditOpinion = this.formData.auditOpinion;

					this.$confirm('是否确认' + btnType + '？', '审核', {
						confirmButtonText: '确定',
						cancelButtonText: '取消',
						type: 'warning'
					})
						.then(() => {
							// 处理请求数据
							switch (btnType) {
								case '审核通过':
									auditData.auditStatus = 1;
									break;
								case '驳回':
									auditData.auditStatus = 2;
									break;
							}

							if (auditData.auditStatus !== -1) {
								this.$request({
									url: api.audit,
									data: auditData,
									method: 'POST'
								}).then(response => {
									if (response.success) {
										this.$message.success('审核成功');
										this.infoPageClose(true);
									} else {
										this.$message.error(response.msg);
									}
								});
							}
						})
						.catch(() => {});
				} else {
					return false;
				} //校验不通过
			});
		},
		infoPageClose(reload) {
			this.disabled = false;
			this.$emit('activelyClose', reload);
		},
		toolFormData(formData) {
			const visited = {
				sysUserId: formData.visitedUserId,
				userName: formData.visitedName,
				label: formData.visitedName,
				deptId: formData.visitedCollegeId,
				deptName: formData.visitedCollege,
				phone: formData.visitedTelephone
			};
			this.formData = {
				...formData,
				visit: formData.visitStart + '~' + formData.visitEnd,
				visited: visited
				// visited2: visited
			};
		}
	}
};
</script>
