<template>
	<div style="height: 100%">
		<es-data-table
			ref="table"
			:full="true"
			:fit="true"
			:thead="thead"
			:toolbar="toolbar"
			:border="true"
			:url="dataTableUrl"
			@search="search"
			page
			numbers
			@btnClick="btnClick"
			:scrollbar="false"
			close
		></es-data-table>
		<es-dialog v-if="visible" :title="dialogTitle" :visible.sync="visible" size="lg">
			<editDialog :selectInfo="selectInfo" :type="type" @closeEdit="closeEdit"></editDialog>
		</es-dialog>
	</div>
</template>

<script>
import api from '@/http/job/jobCoPioneerMentor/api.js';
import editDialog from './component/edit';
import httpApi from '@/http/job/jobCoPioneerMentor/api.js';
import request from 'eoss-ui/lib/utils/http';
import $ from 'eoss-ui/lib/utils/util';
export default {
	components: { editDialog },
	data() {
		return {
			selectInfo: '',
			type: '',
			visible: false,
			dialogTitle: '',
			dataTableUrl: api.jobCoPioneerMentorList,
			thead: [
				{
					title: '导师姓名',
					align: 'center',
					field: 'name'
				},
				{
					title: '所属公司',
					align: 'center',
					field: 'subordinateCompany'
				},
				{
					title: '现任职务',
					align: 'center',
					field: 'subordinateMentorJob'
				},
				{
					title: '操作',
					type: 'handle',
					width: 150,
					template: '',
					events: [
						{
							code: 'audit',
							text: '查看'
						},
						{
							code: 'audit',
							text: '修改'
						},
						{
							code: 'delete',
							text: '删除'
						}
					]
				}
			],
			toolbar: [
				{
					type: 'button',
					contents: [
						{
							name: 'xz',
							text: '新增',
							type: 'primary'
						}
					]
				},
				{
					type: 'search',
					contents: [
						{
							type: 'text',
							name: 'name',
							placeholder: '关键字查询'
						}
					]
				},
				{
					type: 'filter',
					contents: [
						{
							type: 'text',
							label: '导师姓名',
							placeholder: '请输入导师姓名',
							name: 'name',
							clearable: true,
							col: 4
						},
						{
							type: 'text',
							label: '所属公司',
							placeholder: '请输入所属公司',
							name: 'company',
							clearable: true,
							col: 4
						},
						{
							type: 'text',
							label: '现任职务',
							placeholder: '请输入现任职务',
							name: 'mentorJob',
							clearable: true,
							col: 4
						}
					]
				}
			]
		};
	},
	computed: {},
	watch: {},
	created() {},
	mounted() {},
	methods: {
		btnClick({ handle, row }) {
			switch (handle.text) {
				case '新增':
					this.visible = true;
					this.dialogTitle = '新增';
					this.type = '新增';
					break;
				case '查看':
					this.visible = true;
					this.dialogTitle = '查看';
					this.type = '查看';
					this.selectInfo = row.id;
					break;
				case '修改':
					this.visible = true;
					this.dialogTitle = '修改';
					this.type = '修改';
					this.selectInfo = row.id;
					break;
				case '删除':
					this.$confirm(`是否提交?`, '提示', {
						confirmButtonText: '确定',
						cancelButtonText: '取消',
						type: 'warning'
					})
						.then(() => {
							const loading = $.loading(this.$loading, '删除中');
							this.$request({
								url: httpApi.jobCoPioneerMentorDeleteById,
								data: { id:row.id  },
								method: 'POST'
							}).then(res => {
								loading.close();
								this.$message.success('操作成功');
								this.closeEdit();
							});
						})
						.catch(() => {});
					break;

				default:
					break;
			}
		},
		//关闭编辑框
		closeEdit() {
			this.visible = false;
			this.$refs.table.reload();
		}
	}
};
</script>
<style scoped lang="scss">
::v-deep .es-icon-jiahao {
	width: 35px;
	height: 30px;
	line-height: 30px;
	// border: 2px solid #D9D9D9;
	text-align: center;
	position: relative;
	// border-radius: 50%;
	// &::after {
	// 	content: '新增';
	// 	padding: 0 0 0 4px;
	// }
}
::v-deep .es-icon-jianhao {
	width: 35px;
	height: 30px;
	line-height: 30px;
	// border: 2px solid #D9D9D9;
	text-align: center;
	// border-radius: 50%;
}
::v-deep .es-data-table-content .es-table td.es-table-handle-box .cell {
    justify-content: center;
}
</style>
