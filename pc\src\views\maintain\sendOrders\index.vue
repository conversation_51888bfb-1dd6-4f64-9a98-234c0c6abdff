<template>
	<div class="content" style="height: 100%">
		<div style="width: 100%; height: 100%">
			<el-menu
				:default-active="activeMenus"
				mode="horizontal"
				class="es-menu"
				@select="handleSelect"
			>
				<el-menu-item v-for="item in menus" :key="item.key" :index="item.key">
					{{ item.label }}
				</el-menu-item>
			</el-menu>
			<es-data-table
				v-if="true"
				ref="table"
				:key="tableCount"
				:row-style="tableRowClassName"
				:full="true"
				:fit="true"
				:thead="thead"
				:toolbar="toolbar"
				:page="page"
				:url="dataTableUrl"
				:param="dataTableParam"
				:border="true"
				:numbers="true"
				form
				@btnClick="btnClick"
			></es-data-table>
			<!-- 派单 -->
			<es-dialog
				v-if="showSendOrderPage"
				title="派单"
				:visible.sync="showSendOrderPage"
				width="960px"
				height="730px"
				:show-scale="false"
				:drag="false"
				append-to-body
			>
				<sendOrderPage :id="maintainId" :workType="maintainWorkType" @closeSendOrderPage="closeSendOrderPage"></sendOrderPage>
			</es-dialog>
			<!-- 编辑派单 -->
			<es-dialog
				v-if="showEditSendOrderPage"
				title="编辑派单"
				:visible.sync="showEditSendOrderPage"
				width="960px"
				height="730px"
				:show-scale="false"
				:drag="false"
				append-to-body
			>
				<editSendOrderPage
					:id="maintainId"
					@closeEditSendOrderPage="closeEditSendOrderPage"
				></editSendOrderPage>
			</es-dialog>
			<!-- 查看 -->
			<es-dialog
				v-if="showViewPage"
				title="报修单"
				:visible.sync="showViewPage"
				width="960px"
				height="730px"
				:show-scale="false"
				:drag="false"
				append-to-body
			>
				<viewPage :id="maintainId"></viewPage>
			</es-dialog>
		</div>
	</div>
</template>

<script>
import api from '@/http/maintain/applicationApi';
import sendOrderPage from '@/views/maintain/sendOrders/components/send.vue';
import editSendOrderPage from '@/views/maintain/sendOrders/components/edit.vue';
import viewPage from '@/views/maintain/application/components/view.vue';

export default {
	components: { sendOrderPage, editSendOrderPage, viewPage },
	data() {
		return {
			menus: [
				{ key: '0', label: '待派单' },
				{ key: '1', label: '已完成' }
			],
			activeMenus: '0',
			page: {
				pageSize: 20,
				totalCount: 0
			},
			//弹出配置
			maintainId: null,
			maintainWorkType: null,
			showSendOrderPage: false,
			showEditSendOrderPage: false,
			showViewPage: false,

			tableCount: 1,
			dataTableUrl: api.listJson,
			dataTableParam: { reportStatusList: [0, 1, 2, 9], orderBy: 'create_time', asc: false },
			thead: [],
			toolbar: [],
			editToolbar: [
				{
					type: 'search',
					reset: true,
					contents: [
						// {
						// 	type: 'text',
						// 	name: 'repairCode',
						// 	placeholder: '编号',
						// 	clearable: true
						// },
						{
							type: 'select',
							placeholder: '报修类别',
							name: 'repairType',
							event: 'multipled',
							sysCode: 'hq_repair_type',
							'label-key': 'shortName',
							'value-key': 'cciValue',
							clearable: true
						},
						{
							type: 'select',
							placeholder: '报修状态',
							name: 'reportStatus',
							event: 'multipled',
							sysCode: 'hq_report_status',
							'label-key': 'shortName',
							'value-key': 'cciValue',
							clearable: true
						},
						{
							type: 'select',
							placeholder: '报修地点',
							name: 'addressType',
							event: 'multipled',
							// sysCode: 'hq_repair_address_type',
							// 'label-key': 'shortName',
							// 'value-key': 'cciValue',
							url: api.getBuildingParentList + '?states=1',
							'label-key': 'roomName',
							'value-key': 'id',
							clearable: true
						},
						// {
						// 	type: 'text',
						// 	name: 'reportUser',
						// 	placeholder: '报修人',
						// 	clearable: true
						// },
						// {
						// 	type: 'text',
						// 	name: 'repairPhone',
						// 	placeholder: '报修电话',
						// 	clearable: true
						// },
						{
							type: 'select',
							placeholder: '维修员',
							name: 'outRepairUserId',
							event: 'multipled',
							url: api.selectMpList,
							'label-key': 'name',
							'value-key': 'id',
							clearable: true
						},
						{
							type: 'text',
							name: 'keyword',
							placeholder: '关键词查询',
							clearable: true
						}
					]
				}
			],
			listThead: [
				{
					title: '编号',
					align: 'left',
					field: 'repairCode',
					showOverflowTooltip: true
				},
				{
					title: '报修类别',
					align: 'center',
					width: '100px',
					field: 'repairTypeName',
					showOverflowTooltip: true
				},
				// {
				// 	title: '报修描述',
				// 	width: '260px',
				// 	align: 'center',
				// 	field: 'reportContent',
				// 	showOverflowTooltip: true
				// },
				{
					title: '报修地点',
					align: 'center',
					field: 'addressTypeName',
					showOverflowTooltip: true
				},
				{
					title: '详细地址',
					align: 'center',
					field: 'address',
					showOverflowTooltip: true
				},
				{
					title: '报修人',
					align: 'center',
					field: 'reportUser',
					showOverflowTooltip: true
				},
				{
					title: '联系方式',
					align: 'center',
					field: 'repairPhone',
					showOverflowTooltip: true
				},
				{
					title: '报修时间',
					align: 'center',
					width: '180px',
					field: 'createTime',
					showOverflowTooltip: true
				},
				{
					title: '报修状态',
					width: '120px',
					align: 'center',
					field: 'reportStatusName',
					showOverflowTooltip: true,
					render: (h, param) => {
						return h(
							'p',
							{ style: { fontWeight: 'bold', color: this.formatLink(param.row.reportStatus) } },
							param.row.reportStatusName
						);
					}
				},
				{
					title: '维修员',
					align: 'center',
					field: 'repairUserName',
					showOverflowTooltip: true
				}
			],
			btnJson: {
				title: '操作',
				type: 'handle',
				width: 150,
				template: '',
				events: [
					{
						code: 'view',
						text: '查看'
					},
					{
						code: 'send',
						text: '派单',
						rules: rows => {
							return rows.isSendOrder == 1;
						}
					},
					{
						code: 'editEsDate',
						text: '编辑',
						rules: rows => {
							return rows.reportStatus == 2;
						}
					},
					{
						code: 'revocation',
						text: '撤回',
						rules: rows => {
							return rows.isRecall == 1;
						}
					}
				]
			}
		};
	},
	watch: {
		dataTableUrl() {
			this.tableCount++;
		}
	},
	created() {
		//初始化查询列表
		this.thead = this.getListThead(this.btnJson);
		this.toolbar = this.editToolbar;
	},
	methods: {
		/**
		 * 表格行高
		 */
		tableRowClassName() {
			return {
				height: '54px !important'
			};
		},
		//页签切换
		handleSelect(res) {
			this.activeMenus = res;
			if ('0' === res) {
				this.dataTableParam.reportStatusList = [0, 1, 2, 9];
				this.$refs.table.reload();
			} else if ('1' === res) {
				this.dataTableParam.reportStatusList = [3, 4];
				this.$refs.table.reload();
			}
			this.tableCount++;
		},
		// 操作按钮
		btnClick(res) {
			let code = res.handle.code;
			switch (code) {
				case 'view':
					this.openViewPage(res.row.id);
					break;
				case 'send':
					this.openAddOrEditPage(res.row);
					break;
				case 'editEsDate':
					this.openEditSendPage(res.row.id);
					break;
				case 'revocation':
					this.handleRevocation(res.row.id);
					break;
			}
		},
		//打开派单
		openAddOrEditPage(obj) {
			this.maintainId = obj.id;
			this.maintainWorkType = obj.repairType;
			this.showSendOrderPage = true;
			this.showEditSendOrderPage = false;
			this.showViewPage = false;
		},
		//打开编辑派单
		openEditSendPage(id) {
			this.maintainId = id;
			this.showSendOrderPage = false;
			this.showEditSendOrderPage = true;
			this.showViewPage = false;
		},
		//打开查看
		openViewPage(id) {
			this.maintainId = id;
			this.showSendOrderPage = false;
			this.showEditSendOrderPage = false;
			this.showViewPage = true;
		},
		handleRevocation(id) {
			this.$confirm('当前报修订单正在派单维修中，是否撤回订单？', '提示', {
				confirmButtonText: '确定',
				cancelButtonText: '取消',
				type: 'warning'
			})
				.then(() => {
					this.$request({
						url: api.cancelDispatch,
						data: { reportId: id },
						method: 'POST'
					}).then(res => {
						if (res.rCode === 0) {
							this.$message.success('操作成功！');
							this.$refs.table.reload();
						} else {
							this.$message.error(res.msg);
						}
					});
				})
				.catch(() => {});
		},
		getListThead(btnJson) {
			let tempThead = [...this.listThead];
			tempThead.push(btnJson);
			return tempThead;
		},
		closeSendOrderPage(code) {
			this.showSendOrderPage = false;
			if (code == 'success') {
				this.$refs.table.reload();
			}
		},
		closeEditSendOrderPage(code) {
			this.showEditSendOrderPage = false;
			// if (code == 'success') {
			// 	this.$refs.table.reload();
			// }
		},
		//处理状态表格列回显颜色
		formatLink(status) {
			let color = 'red';
			switch (status) {
				case '0':
					color = '#F56C6C';
					break;
				case '1':
					color = '#E6A23C';
					break;
				case '2':
					color = '#1890ff';
					break;
				case '3':
					color = '#52c41a';
					break;
				case '4':
					color = '#909399';
					break;
				case '9':
					color = '#000000';
					break;
			}
			return color;
		}
	}
};
</script>
