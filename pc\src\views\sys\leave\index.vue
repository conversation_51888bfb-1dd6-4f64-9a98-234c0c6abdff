<!-- eslint-disable no-duplicate-case -->
<template>
	<div class="content">
		<es-data-table
			ref="table"
			stripe
			:row-style="tableRowClassName"
			:thead="wd.thead"
			:toolbar="wd.toolbar"
			:border="true"
			:page="pageOption"
			:url="wd.basics.dataTableUrl"
			:numbers="true"
			:param="params"
			:option-data="optionData"
			style="width: 100%"
			close
			form
			@btnClick="btnClick"
			@sort-change="sortChange"
			@submit="hadeSubmit"
			@edit="changeTable"
		></es-data-table>
		<es-dialog
			:title="formTitle"
			:visible.sync="visibleBasicInfo"
			size="lg"
			width="75%"
			height="80%"
		>
			<BasicInfo
				v-if="visibleBasicInfo"
				:id="formId"
				:basics="wd.basics"
				:title="formTitle"
				@visible="
					e => {
						visibleBasicInfo = e;
						$refs.table.reload();
					}
				"
			/>
		</es-dialog>
	</div>
</template>

<script>
import BasicInfo from './components/basic-info';
import { v4 as uuidv4 } from 'uuid';
import { mixinList } from './mixinList';
export default {
	components: { BasicInfo },
	mixins: [mixinList],
	data() {
		return {
			visibleBasicInfo: false,
			deleteId: '',

			showForm: false,
			enpList: [],
			roleList: [],
			formReadonly: true,
			roleFormData: {
				userId: '',
				username: '',
				phone: '',
				selectedRoleList: []
			},
			formId: '',
			formTitle: '查看',
			optionData: {
				state: [
					{
						value: 1,
						name: '启用'
					},
					{
						value: 0,
						name: '禁用'
					}
				]
			},
			editBtn: {
				type: 'submit',
				skin: 'lay-form-btns',
				contents: [
					{
						type: 'primary',
						text: '确定',
						event: 'confirm'
					},
					{
						type: 'reset',
						text: '取消',
						event: 'cancel'
					}
				]
			},
			cancelBtn: {
				type: 'submit',
				skin: 'lay-form-btns',
				contents: [
					{
						type: 'reset',
						text: '取消',
						event: 'cancel'
					}
				]
			},
			pageOption: {
				layout: 'total, prev, pager, next, jumper',
				pageSize: 20,
				// hideOnSinglePage: true,
				position: 'right',
				current: 1,
				pageNum: 1
			},
			params: {
				// type: 'etipnuuezknypsv'
				orderBy: 'createTime',
				asc: 'false'
			},
			submitFilterParams: {}
		};
	},
	watch: {
		// 监听visibleBasicInfo为true时新列表
		// visibleBasicInfo: {
		// 	handler(val) {
		// 		if (!val) {
		// 			this.$refs.table.reload();
		// 		}
		// 	}
		// }
	},
	methods: {
		/**
		 * 表格行高
		 */
		tableRowClassName({ row, rowIndex }) {
			let styleRes = {
				height: '54px !important'
			};
			return styleRes;
		},
		// 高级搜索确认
		hadeSubmit(e) {
			this.submitFilterParams = e.data;
		},
		/**
		 * 操作按钮点击事件
		 * @param {*} res
		 */
		async btnClick(res) {
			let text = res.handle.text;
			this.formId = res.row?.id || '';
			switch (text) {
				case '查看':
					this.formTitle = '查看';
					this.visibleBasicInfo = true;
					break;
				case '编辑':
					this.formTitle = '编辑';
					this.visibleBasicInfo = true;
					break;
				case '因私请假':
				case '因公请假':
					this.formId = uuidv4();
					this.formTitle = '新增-' + text;
					this.visibleBasicInfo = true;
					break;
				case '撤回':
					this.$confirm(`您确定要撤回数据吗？`, '提示', {
						confirmButtonText: '确定',
						cancelButtonText: '取消',
						type: 'warning'
					})
						.then(() => {
							const loading = this.$loading();
							this.$request({
								url: this.wd.basics.revoke,
								data: {
									id: this.formId
								},
								method: 'POST'
							}).then(res => {
								loading.close();
								if (res.rCode == 0) {
									this.$message.success('撤回成功');
									this.$refs.table.reload();
								}
							});
						})
						.catch(err => {});
					break;
				case '删除':
					{
						this.$confirm(`您确定要删除数据吗？`, '提示', {
							confirmButtonText: '确定',
							cancelButtonText: '取消',
							type: 'warning'
						})
							.then(() => {
								const loading = this.$loading();
								this.$request({
									url: this.wd.basics.delete,
									data: {
										id: this.formId
									},
									method: 'POST'
								}).then(res => {
									loading.close();
									if (res.rCode == 0) {
										this.$message.success(res.msg);
										this.$refs.table.reload();
									} else {
										this.$message.error(res.msg);
									}
								});
							})
							.catch(err => {});
					}
					break;
				case '导出':
					this.exportFile();
					break;
				default:
					break;
			}
		},
		//导出函数
		exportFile() {
			let isDev = process.env.NODE_ENV === 'development' ? '/api' : '';
			const paramAll = { ...this.params, ...this.submitFilterParams };
			let url = `${isDev}/ybzy/projectSrAchievement/export?${this.objToUrlParams(paramAll)}`;
			window.open(url);
		},
		objToUrlParams(obj) {
			return Object.keys(obj)
				.map(key => encodeURIComponent(key) + '=' + encodeURIComponent(obj[key]))
				.join('&');
		},
		handleClose() {},
		handleFormItemChange() {},

		/**
		 * 排序变化事件
		 * @param {*} column
		 * @param {*} prop
		 * @param {*} order
		 */
		sortChange(column, prop, order) {
			if (column.order == 'ascending') {
				//升序
				this.params = {
					asc: 'true',
					orderBy: column.prop
				};
			} else if (column.order == 'descending') {
				//降序
				this.params = {
					asc: 'false',
					orderBy: column.prop
				};
			} else {
				//不排序
				this.params = {
					asc: 'false',
					orderBy: 'createTime'
				};
			}
			this.$refs.table.reload();
		},
		/**
		 * 编辑模式
		 */
		editModule(list, hideField) {
			for (var i in list) {
				var item = list[i];
				item.readonly = false;
				if (undefined != hideField && hideField.length > 0 && hideField.indexOf(item.name) > -1) {
					item.hide = true;
				}
			}
			list.push(this.editBtn);
		},

		changeTable(val) {
			if ('state' === val.name) {
				this.$request({
					url: this.wd.basics.updateState,
					data: {
						id: val.data.id,
						state: val.value
					},
					method: 'POST'
				}).then(res => {
					if (res.rCode == 0) {
						this.$message.success('操作成功');
					} else {
						this.$message.error(res.msg);
					}
				});
			}
		},
		reset() {
			this.visible = false;
		}
	}
};
</script>
<style scoped lang="scss">
.content {
	width: 100%;
	height: 100%;
	// height: calc(100vh - 60px);
}
::v-deep .el-dialog {
	//width: 1200px !important;
}
</style>
