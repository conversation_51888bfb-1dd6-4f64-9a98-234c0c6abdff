<template>
	<div class="content">
		<es-data-table
			ref="table"
			:row-style="tableRowClassName"
			:full="true"
			:fit="true"
			:thead="thead"
			:toolbar="toolbar"
			:border="true"
			:page="false"
			:data="tableData"
			:numbers="true"
      :param="params"
      height="70vh"
			close
			form
      @btnClick="btnClick"
		></es-data-table>
    <div class="pagination">
			<el-pagination
				:background="false"
				:current-page="currentPage"
				:page-sizes="[10, 20, 30, 40]"
				:layout="pageOption.layout"
				:total="pageOption.total"
				:pager-count="pageOption.total"
				@size-change="
					e => {
						pageOption.pageSize = e;
						queryList();
					}
				"
				@current-change="
					e => {
						pageOption.pageNum = e;
						queryList();
					}
				"
			></el-pagination>
		</div>
		<es-dialog
			:title="formTitle"
			:visible.sync="showForm"
			width="60%"
			:close-on-click-modal="false"
			:destroy-on-close="true"
		>
			<es-form
				ref="form"
				:model="formData"
				:contents="formItemList"
				height="500px"
				:genre="2"
				collapse
				@change="inputChange"
				@submit="handleFormSubmit"
				@reset="showForm = false"
			/>
		</es-dialog>
		<es-dialog
			title="删除"
			:visible.sync="showDelete"
			width="20%"
			:close-on-click-modal="false"
			:middle="true"
			height="140px"
		>
			<div>确定要删除该条数据吗</div>
			<div class="btn-box">
				<div class="btn theme" @click="deleteRow">确定</div>
				<div class="btn" @click="showDelete = false">取消</div>
			</div>
		</es-dialog>
	</div>
</template>

<script>
import interfaceUrl from '@/http/screen/jyMaterials';
import SnowflakeId from 'snowflake-id';
export default {
	name: 'jyMaterials', //救援装备
	data() {
		return {
			ownId: '',
			dataTableUrl: interfaceUrl.listJson,
			showForm: false,
			showDelete: false,
			formData: {},
			formTitle: '编辑',
			editBtn: {
				type: 'submit',
				skin: 'lay-form-btns',
				contents: [
					{
						type: 'primary',
						text: '确定',
						event: 'confirm'
					},
					{
						type: 'reset',
						text: '取消',
						event: 'cancel'
					}
				]
			},
			cancelBtn: {
				type: 'submit',
				skin: 'lay-form-btns',
				contents: [
					{
						type: 'reset',
						text: '取消',
						event: 'cancel'
					}
				]
			},
			toolbar: [
				{
					type: 'button',
					contents: [
						{
							text: '新增',
							code: 'add',
							type: 'primary'
						}
					]
				},
				// {
				// 	type: 'search',
				// 	contents: [
				// 		{
				// 			type: 'text',
				// 			name: 'keyword',
				// 			placeholder: '关键字查询'
				// 		}
				// 	]
				// }
			],
			thead: [
				{
					title: '物资名称',
					align: 'left',
					field: 'name',
					sortable: 'custom',
					showOverflowTooltip: true
				},
				{
					title: '物资类型',
					align: 'left',
					field: 'type',
					sortable: 'custom',
					showOverflowTooltip: true,
				},
				{
					title: '数量',
					align: 'left',
					field: 'count',
					sortable: 'custom',
					showOverflowTooltip: true
				},
				{
					title: '单位',
					align: 'left',
					field: 'unit',
					sortable: 'custom',
					showOverflowTooltip: true
				},
        {
					title: '供应商',
					align: 'left',
					field: 'supplier',
					sortable: 'custom',
					showOverflowTooltip: true
				},
        {
					title: '有效期',
					align: 'left',
					field: 'validityDate',
					sortable: 'custom',
					showOverflowTooltip: true
				},
        {
					title: '存放地',
					align: 'left',
					field: 'targetAddr',
					sortable: 'custom',
					showOverflowTooltip: true
				},
				{
					title: '联系人',
					align: 'left',
					field: 'contactsName',
					sortable: 'custom',
					showOverflowTooltip: true
				},
				{
					title: '联系电话',
					align: 'left',
					field: 'contactsTel',
					sortable: 'custom',
					showOverflowTooltip: true
				},
				{
					title: '操作',
					type: 'handle',
					width: 180,
					template: '',
					events: [
						{
							code: 'edit',
							text: '编辑'
						},
						{
							code: 'view',
							text: '查看'
						},
						{
							code: 'delete',
							text: '删除'
						}
					]
				}
			],
			pageOption: {
				layout: 'total, prev, pager, next, jumper',
				pageSize: 10,
				// hideOnSinglePage: true,
				position: 'right',
				current: 1,
				pageNum: 1
			},
			params: {
				asc: 'false',
				orderBy: 'createTime'
			},
      tableData: []
		};
	},
	computed: {
		formItemList() {
      const readonly = this.formTitle === '查看';
			return [
				{
					label: '物资名称',
					name: 'name',
					placeholder: '请输入名称',
					event: 'multipled',
          readonly,
					rules: {
						required: true,
						message: '请输入名称',
						trigger: 'blur'
					},
					verify: 'required',
					col: 10
				},
				{
					label: '物资类型',
					name: 'type',
          disabled: readonly,
					placeholder: '请输入物资类型',
					event: 'multipled',
					rules: {
						required: true,
						message: '请输入物资类型',
						trigger: 'blur'
					},
					verify: 'required',
					col: 10,
					type: 'select',
          data: [
            { label: '固定食品', value: '固定食品' },
            { label: '医疗用品', value: '医疗用品' },
            { label: '避难用品', value: '避难用品' },
            { label: '应急工具', value: '应急工具' },
          ]
				},
				{
					label: '数量',
					name: 'count',
          readonly,
					placeholder: '请输入数量',
					event: 'multipled',
					rules: {
						required: true,
						message: '请输入数量',
						trigger: 'blur'
					},
					verify: 'required',
					col: 10
				},
				{
					label: '单位',
					name: 'unit',
          readonly,
					placeholder: '请输入数量单位',
					event: 'multipled',
					rules: {
						required: true,
						message: '请输入数量单位',
						trigger: 'blur'
					},
					verify: 'required',
					col: 10
				},
				{
					label: '供应商',
					name: 'supplier',
          readonly,
					placeholder: '请输入供应商',
					event: 'multipled',
					rules: {
						required: true,
						message: '请输入供应商',
						trigger: 'blur'
					},
					col: 10
				},
        {
					label: '有效期',
					name: 'validityDate',
          readonly,
          type: 'date',
					placeholder: '请选择有效期',
					event: 'multipled',
					rules: {
						required: true,
						message: '请选择有效期',
						trigger: 'blur'
					},
					col: 10
				},
        {
					label: '存放地',
          readonly,
					name: 'targetAddr',
					placeholder: '请输入存放地',
					event: 'multipled',
					rules: {
						required: true,
						message: '请输入存放地',
						trigger: 'blur'
					},
					col: 10
				},
				{
					label: '联系人',
          readonly,
					name: 'contactsName',
					placeholder: '请输入联系人',
					event: 'multipled',
					rules: {
						required: true,
						message: '请输入联系人',
						trigger: 'blur'
					},
					verify: 'required',
					col: 10
				},
				{
					label: '联系电话',
          readonly,
					name: 'contactsTel',
					placeholder: '请输入联系电话',
					event: 'multipled',
					rules: [
						{
							required: true,
							message: '请输入联系电话',
							trigger: 'blur'
						},
					],
					verify: 'required',
					col: 10
				},
			];
		}
	},
	watch: {},
	created() {
    this.queryList()
  },
	mounted() {},
	methods: {
    // 请求列表
		queryList() {
			this.loading = true;
			this.$request2({
				url: interfaceUrl.info,
				data: {
					_page: this.pageOption.pageNum,
					_pageCount: this.pageOption.pageSize,
          name: ''
				},
				method: 'POST',
				type: 'JSON'
			}).then(res => {
				this.loading = false;
				if (res.code === 200) {
					this.pageOption.total = res.data.total;
					this.pageOption.pageNum = res.data.current;
					this.tableData = res.data.records;
				}
			});
		},
		/**
		 * 表格行高
		 */
		tableRowClassName({ row, rowIndex }) {
			let styleRes = {
				height: '54px !important'
			};
			return styleRes;
		},
		inputChange(key, value) {},
		hadeSubmit(data) {},
		/**
		 * 操作按钮点击事件
		 * @param {*} res
		 */
		btnClick(res) {
			let code = res.handle.code;
			switch (code) {
				case 'add':
					// 新增
					this.formTitle = '新增';
					this.editModule(this.formItemList);
					const snowflake = new SnowflakeId();
					let id = snowflake.generate();
					this.ownId = id;
					this.formData = {
						id: id,
						name: null,
						type: null,
						resource_num: null,
						units: null,
						user_name: null,
						phone: null,
						warehouse: null,
						address: null,
						remark: null
					};
					this.showForm = true;
					break;
				case 'edit':
					// 编辑
					this.formTitle = '编辑';
					this.formData = {...res.row};
					this.showForm = true;
					break;
				case 'view':
          this.formTitle = '查看';
					this.formData = res.row;
					this.showForm = true;
					break;
				case 'delete':
        this.$confirm(`确定要删除“${res.row.name}”吗？`, '提示', {
						confirmButtonText: '确定',
						cancelButtonText: '取消',
						type: 'warning'
					})
						.then(() => {
							this.loading = true;
							this.$request2({
								url: interfaceUrl.deleteBatchIds,
								data: { _id: res.row._id },
								method: 'POST',
								type: 'JSON'
							}).then(res => {
								this.loading = false;
								if (res.code === 200) {
									// this.$refs.table.reload();
									this.pageOption.pageNum = 1;
									this.queryList();
									this.$message.success('删除成功');
								} else {
									this.$message.error(res.msg);
								}
							});
						})
						.catch(() => {});
					break;
					break;
				default:
					break;
			}
		},
		handleClose() {},
		handleFormItemChange() {},
		async handleFormSubmit(data) {
			const valid = await this.$refs.form.validate();
			if (!valid) return;
			this.loading = true;
			const formData = JSON.parse(JSON.stringify(this.formData));
			const params = {
				...formData,
				size: '0'
			};
			delete params.fj;
			delete params.status;
			if (this.formTitle === '新增') {
				params.create_time = this.$.formatDate(new Date().getTime(), 'yyyy-MM-dd HH:mm:ss');
			} else {
				params.update_time = this.$.formatDate(new Date().getTime(), 'yyyy-MM-dd HH:mm:ss');
			}
			this.$request2({
				url: this.formTitle === '新增' ? interfaceUrl.save : interfaceUrl.update,
				data: params,
				method: 'POST',
				type: 'JSON'
			}).then(res => {
				this.loading = false;

        this.pageOption.pageNum = 1;
        this.queryList();
        this.showForm = false;
			});
		},
		deleteRow() {
			this.$request2({
				url: interfaceUrl.deleteBatchIds,
				data: { ids: this.deleteId },
				method: 'POST'
			}).then(res => {
				if (res.rCode == 0) {
					this.$refs.table.reload();
					this.$message.success('删除成功');
				} else {
					this.$message.error(res.msg);
				}
				this.showDelete = false;
			});
		},
		/**
		 * 排序变化事件
		 * @param {*} column
		 * @param {*} prop
		 * @param {*} order
		 */
		sortChange(column, prop, order) {
			if (column.order == 'ascending') {
				//升序
				this.params = {
					asc: 'true',
					orderBy: column.prop
				};
			} else if (column.order == 'descending') {
				//降序
				this.params = {
					asc: 'false',
					orderBy: column.prop
				};
			} else {
				//不排序
				this.params = {
					asc: 'false',
					orderBy: 'createTime'
				};
			}
			this.$refs.table.reload();
		},
		/**
		 * 编辑模式
		 */
		editModule(list) {
			for (var i in list) {
				var item = list[i];
				item.readonly = false;
			}
			list.push(this.editBtn);
		},
		/**
		 * 只读模式
		 */
		readModule(list, hideField) {
			for (var i in list) {
				var item = list[i];
				item.readonly = true;
			}
			list.push(this.cancelBtn);
		}
	}
};
</script>
<style scoped lang="scss">
::v-deep .content {
	width: 100%;
	height: 100%;
	.el-dialog__body {
		overflow: auto;
	}
}
::v-deep .pagination {
	display: flex;
	justify-content: right;
	padding: 0 16px 16px;
	background-color: #fafafa;
	.btn-next,
	.el-pager li,
	.btn-prev {
		background-color: #fafafa;
	}
}
.el-dialog__body {
	.btn-box {
		margin-top: 20px;
		display: flex;
		justify-content: flex-end;

		.btn {
			padding: 5px 10px;
			color: #666;
			border: 1px solid #eee;
			cursor: pointer;
			margin-right: 5px;
			border-radius: 4px;
			// &.theme {
			// 	background: $--color-primary;
			// 	color: #fff;
			// 	border-color: $--color-primary;
			// }
		}
	}
}
</style>
