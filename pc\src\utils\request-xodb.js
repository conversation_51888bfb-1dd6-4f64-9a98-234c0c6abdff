/**
 * @Author: <PERSON><PERSON> <EMAIL>
 * @Date: 2023-01-05 08:40:42
 * @LastEditors: <PERSON><PERSON> <EMAIL>
 * @LastEditTime: 2023-11-20 13:43:11
 * @FilePath: /ybzy-screen/src/utils/request-xodb.js
 * @Description:
 */
import axios from 'axios';
import { Message } from 'eoss-element';
// import { getToken, getCookie } from '@/utils/auth';
import store from '@/store';
// import { xodbBaseUrl } from '@/config';
// http://***********:8081
const baseURL = origin.includes('localhost') || origin.includes('192.168') ? '/api' : '';
const service = axios.create({
	baseURL: baseURL,
	timeout: 10000
});

// 请求对象
service.interceptors.request.use(
	config => {
		// 设置token
		// if (getToken()) {
		// 	config.headers['X-Token'] = getToken();
		// }

		if (config.method === 'post') {
			if (config.type === 'JSON') {
				config.headers['Content-Type'] = 'application/json;charset=utf-8';
			} else {
				config.headers['Content-Type'] = 'application/x-www-form-urlencoded;charset=UTF-8;';
			}
		}
		return config;
	},
	error => {
		console.log(error);
		return Promise.reject(error);
	}
);
// 响应对象
service.interceptors.response.use(
	response => {
		const res = response.data;
		if (res.code !== 200) {
			if (res.code === 500 || res.code === 50012 || res.code === 50014) {
				// 提示登陆失效
				Message({
					message: res.message,
					type: 'error'
				});
				// 登陆失效清除token并重新加载
				store.commit('getRoleMap/SET_LOGIN_STATE', false);
				// store.commit('app/SIGN_OUT');
				// location.reload();
			} else {
				Message({
					message: res.message || 'Error',
					type: 'error',
					duration: 2 * 1000
				});
			}
			// return Promise.reject(new Error(res.message || 'Error'));
		} else {
			return res.result;
		}
	},
	error => {
		console.log('err' + error);
		Message({
			message: error.message,
			type: 'error',
			duration: 5 * 1000
		});
		return Promise.reject(error);
	}
);

export default service;
