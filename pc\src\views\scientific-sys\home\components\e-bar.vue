<!-- 科研首页 图表创建组件 -->
<template>
	<div class="project-con">
		<div class="project-con-btn">
			<es-button @click="changeDate(1)" :class="{ active: isActive }">按年度统计</es-button>
			<es-button
				v-if="year === undefined"
				@click="changeDate('', 1)"
				:class="{ active: !isActive }"
			>
				按月度统计
			</es-button>
		</div>
		<div :id="echartDom" class="project-con-chart"></div>
	</div>
</template>
<script>
export default {
	props: {
		// 表格数据
		xData: {
			type: Array,
			required: true
		},
		seriesData: {
			type: Array,
			required: true
		},
		// 用于创建canvas的节点名称
		echartDom: {
			type: String,
			required: true
		},
		title: {
			type: String,
			required: false
		},
		colorList: {
			type: Array,
			required: true
		},
		// 年份 同时用于判断是饼图还是柱状图
		year: {
			type: String,
			defaults: '2023'
		}
	},
	data() {
		return {
			isActive: true,
			statisticsChart: null,
			histogramType: 0,
			xAxisData: [] //横坐标
		};
	},
	mounted() {
		this.draw(); //配置echarts参数
	},
	watch: {
		seriesData: {
			deep: true,
			handler() {
				this.draw(); //配置echarts参数
			}
		}
	},
	methods: {
		// push series数据
		pushSeriesData() {
			let series = [];
			for (let i = 0; i < this.seriesData.length; i++) {
				series.push({
					name: this.seriesData[i].name,
					type: 'bar',
					stack: 'total',
					label: {
						show: false
					},
					barWidth: '10%',
					emphasis: {
						focus: 'series'
					},
					data: this.seriesData[i].data
				});
			}
			return series;
		},
		// 配置echarts的options
		draw() {
			if (this.statisticsChart != null) {
				this.statisticsChart.dispose();
			}
			// if(this.xData.length <= 0) return;
			let dom = document.getElementById(this.echartDom);
			this.statisticsChart = this.$echarts.init(dom);
			// 如果year没传过来说明是柱状图，反之为饼图
			let series = this.pushSeriesData();
			let optionList = {
				title: {
					show: true,
					text: this.title,
					x: 'center', // 标题水平安放位置，可选值为'left'、'center'、'right'或具体的水平坐标值
					y: 'bottom' // 标题垂直安放位置，可选值为'top'、'bottom'、'center'或具体的垂直坐标值
					// textAlign:'center'
				},
				color: this.colorList,
				tooltip: {
					trigger: 'axis',
					backgroundColor: 'rgba(157, 158, 160)',
					textStyle: { color: '#ffffff' }
				},
				legend: {
					itemWidth: 10,
					itemHeight: 10,
					right: 20
				},
				grid: {
					left: '20px',
					right: '23px',
					bottom: '46px',
					containLabel: true
				},
				xAxis: {
					type: 'category',
					data: this.xData || [],
					axisTick: {
						lineStyle: {
							width: 1
						}
					}
				},
				yAxis: {
					type: 'value'
				},
				series
			};
			// push series数据

			this.statisticsChart.setOption(optionList);
			window.addEventListener('resize', () => {
				this.statisticsChart && this.statisticsChart.resize();
			});
		},
		changeDate(statusYear, statusMonth) {
			this.isActive = !this.isActive;
			this.$emit('changeDate', statusYear, statusMonth, this.echartDom);
		}
	}
};
</script>

<style scoped lang="scss">
.project-con {
	background: #ffffff;
	width: 49.5%;
	display: flex;
	flex-direction: column;
	align-items: center;
	height: 400px;
	&-chart {
		flex-shrink: 0;
		width: 100%;
		height: 70%;
	}
	&-btn {
		width: 90%;
		display: flex;
		margin: 20px 0 4% 0;
	}
}
.project-con2 {
	background: #ffffff;
	width: 32.7%;
	display: flex;
	align-items: center;
	height: 450px;
	flex-direction: column;
	&-chart {
		flex-shrink: 0;
		width: 100%;
		height: 100%;
	}
	&-btn {
		width: 90%;
		display: flex;
		margin: 20px 0 8% 0;
	}
}
// .ebtn {
// 	// background-color: aqua;
// 	color: #0076e8;
// 	border-color: #0076e8;
// 	background-color: #e7f3ff;
// }
.active {
	color: #0076e8;
	border-color: #0076e8;
	background-color: #e7f3ff;
}
.other {
	width: 100%;
	height: 100%;
	font-size: larger;
	text-align: center;
}
</style>
