<template>
	<div class="empty">
		<img src="@/assets/images/serveHall/no-data.png" class="empty-img" />
		<div class="desc">{{text}}</div>
	</div>
</template>

<script>
export default {
	name:"EmptyIndex",
	props:{
		text:{
			type:String,
			defalut:"暂无数据"
		}
	},
	data(){
		return {
		}
	}
};
</script>
<style lang="scss" scoped>
.empty {
	// margin: 0 auto;
	width: 100%;
	height: 100%;
	display: flex;
	flex-direction: column;
	justify-content: center;
	align-items: center;
	.empty-img {
		width: 120px;
		height: 120px;
	}
	.desc {
		font-size: 14px;
		font-family: MicrosoftYaHei;
		color: #ffffff;
		line-height: 22px;
		margin-top: 20px;
	}
}
</style>
