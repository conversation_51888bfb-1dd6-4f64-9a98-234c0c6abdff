export default {
	computed: {
		formItemList() {
			return [
				{
					name: 'reply',
					label: '报告回复',
					value: '',
					col: 12,
					rules: {
						required: true,
						message: '请输入报告回复'
					}
				},
				{
					label: '报告回复文件',
					type: 'attachment',
					code: 'lab_room_report_reply_file',
					ownId: this.formData.id, // 业务id
					dragSort: true,
					limit: 8,
					col: 12
				}
			];
		}
	}
};
