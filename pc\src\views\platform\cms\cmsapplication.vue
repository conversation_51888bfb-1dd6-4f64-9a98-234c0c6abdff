<template>
	<basic-container>
		<avue-crud
			ref="crud"
			v-model="form"
			:option="option"
			:table-loading="loading"
			:data="data"
			:page="page"
			:permission="permissionList"
			:before-open="beforeOpen"
			:cell-style="cellStyle"
			@row-update="rowUpdate"
			@row-save="rowSave"
			@row-del="rowDel"
			@search-change="searchChange"
			@search-reset="searchReset"
			@selection-change="selectionChange"
			@current-change="currentChange"
			@size-change="sizeChange"
			@on-load="onLoad"
		>
			<template slot="paramsListForm" slot-scope="scope">
				<el-button v-if="argIsEdit" style="margin-bottom: 5px" @click="addParam()">
					添加参数
				</el-button>
				<div v-for="(item, index) of paramsList" :key="index" class="paramsClass">
					<div><el-input v-model="paramsList[index].label" placeholder="参数名称"></el-input></div>
					<div><el-input v-model="paramsList[index].code" placeholder="参数编码"></el-input></div>
					<div><el-input v-model="paramsList[index].value" placeholder="参数值"></el-input></div>
					<div>
						<el-button
							v-if="argIsEdit"
							icon="el-icon-delete"
							type="danger"
							size="mini"
							@click="removeParam(index)"
						></el-button>
					</div>
				</div>
			</template>
			<template slot="menuRight">
				<div style="display: flex; flex-direction: row">
					<avue-select
						v-model="query.status"
						size="small"
						placeholder="请选择状态"
						:dic="statusDic"
						style="margin-right: 10px"
					></avue-select>
					<el-input
						v-model="query.appName"
						placeholder="请输入应用名称"
						prefix-icon="el-icon-search"
						size="small"
						style="margin-right: 10px"
					></el-input>
					<el-button type="primary" size="small" plain @click="doSearch">搜索</el-button>
					<el-button type="primary" size="small" plain @click="searchReset">重置</el-button>
				</div>
			</template>
			<template slot="menu" slot-scope="scope">
				<el-button
					v-if="scope.row.status == 0"
					type="text"
					size="small"
					plain
					@click="rowStatusOpen(scope.row.id)"
				>
					启用
				</el-button>
				<el-button
					v-if="scope.row.status == 1"
					type="text"
					size="small"
					plain
					@click="rowStatusClose(scope.row.id)"
				>
					禁用
				</el-button>
			</template>
			<template slot="menuLeft">
				<el-button type="danger" size="small" icon="el-icon-delete" plain @click="handleDelete">
					删 除
				</el-button>
			</template>
		</avue-crud>
	</basic-container>
</template>

<script>
import cmsapplicationUrl from '@/http/platform/cmsapplication.js';

const defQuery = {};
const defPage = {
	pageSize: 10,
	currentPage: 1,
	total: 0
};
const statusDic = [
	{
		label: '启用',
		value: 1
	},
	{
		label: '禁用',
		value: 0
	}
];

export default {
	data() {
		return {
			form: { appArgs: null },
			query: Object.assign({}, defQuery),
			page: Object.assign({}, defPage),
			selectionList: [],
			data: [],
			paramsList: [],
			argIsEdit: true,
			statusDic: statusDic,
			option: {
				height: 'auto',
				calcHeight: 'auto',
				align: 'center',
				searchShow: true,
				searchMenuSpan: 6,
				tip: false,
				border: true,
				index: true,
				viewBtn: true,
				columnBtn: false,
				refreshBtn: false,
				selection: false,
				labelWidth: 130,
				dialogClickModal: false,
				dialogWidth: '70%',
				dialogMenuPosition: 'center',
				column: [
					{
						label: '应用名称',
						prop: 'appName',
						rules: [
							{
								required: true,
								message: '请输入应用名称',
								trigger: 'blur'
							}
						],
						width: 160
					},
					{
						label: '应用编码',
						prop: 'appCode',
						rules: [
							{
								required: false,
								message: '请输入应用编码',
								trigger: 'blur'
							}
						],
						width: 160
					},
					{
						label: 'appId',
						prop: 'appId',
						rules: [
							{
								required: true,
								message: '请输入appId',
								trigger: 'blur'
							}
						],
						width: 160
					},
					{
						label: 'appSecret',
						prop: 'appSecret',
						rules: [
							{
								required: true,
								message: '请输入appSecret',
								trigger: 'blur'
							}
						],
						width: 200
					},
					{
						label: '应用服务地址',
						prop: 'appServer',
						rules: [
							{
								required: true,
								message: '请输入应用服务地址',
								trigger: 'blur'
							}
						]
					},
					{
						label: '状态',
						prop: 'status',
						type: 'radio',
						dicData: statusDic,
						rules: [{ required: true, trigger: 'blur', message: '请输入状态' }],
						width: 100,
						hide: false,
						display: true
					},
					{
						label: '应用参数',
						prop: 'paramsList',
						formslot: true,
						span: 24,
						hide: true,
						display: true
					}
				]
			}
		};
	},
	computed: {
		permissionList() {
			return {
				addBtn: true,
				viewBtn: true,
				delBtn: true,
				editBtn: true
			};
		},
		ids() {
			let ids = [];
			this.selectionList.forEach(ele => {
				ids.push(ele.id);
			});
			return ids.join(',');
		}
	},
	methods: {
		getColIndex(prop) {
			for (let index in this.option.column) {
				if (this.option.column[index].prop === prop) {
					return index;
				}
			}
		},
		cellStyle({ row, column, rowIndex, columnIndex }) {
			if (column.property === 'status') {
				if (row.status === 0) {
					return { color: 'red', 'text-align': 'center' };
				}
			}
		},
		rowSave(row, done, loading) {
			if (this.paramsList.length > 0) {
				let ret = this.checkParam();
				if (!ret) {
					loading();
					return;
				}
				row.appArgs = JSON.stringify(this.paramsList);
			} else {
				row.appArgs = '';
			}
			this.$request({
				url: cmsapplicationUrl.add,
				data: row,
				method: 'POST'
			}).then(res => {
				if (res.rCode === 0) {
					done();
					this.onLoad();
					this.$message({
						type: 'success',
						message: '操作成功!'
					});
				} else {
					this.$message.error(res.msg);
				}
			});
		},
		rowUpdate(row, index, done, loading) {
			if (this.paramsList.length > 0) {
				let ret = this.checkParam();
				if (!ret) {
					loading();
					return;
				}
				row.appArgs = JSON.stringify(this.paramsList);
			} else {
				row.appArgs = '';
			}
			this.$request({
				url: cmsapplicationUrl.update,
				data: row,
				method: 'POST'
			}).then(res => {
				if (res.rCode === 0) {
					done();
					this.onLoad();
					this.$message({
						type: 'success',
						message: '操作成功!'
					});
				} else {
					this.$message.error(res.msg);
				}
			});
		},
		rowDel(row) {
			this.$confirm('确定将选择数据删除?', {
				confirmButtonText: '确定',
				cancelButtonText: '取消',
				type: 'warning'
			}).then(() => {
				this.$request({
					url: cmsapplicationUrl.remove,
					data: {
						ids: row.id
					},
					method: 'POST'
				}).then(res => {
					if (res.rCode === 0) {
						this.onLoad();
						this.$message({
							type: 'success',
							message: '操作成功!'
						});
					} else {
						this.$message.error(res.msg);
					}
				});
			});
		},
		handleDelete() {
			if (this.selectionList.length === 0) {
				this.$message.warning('请选择至少一条数据');
				return;
			}
			this.$confirm('确定将选择数据删除?', {
				confirmButtonText: '确定',
				cancelButtonText: '取消',
				type: 'warning'
			})
				.then(() => {
					return remove(this.ids);
				})
				.then(() => {
					this.onLoad(this.page);
					this.$message({
						type: 'success',
						message: '操作成功!'
					});
				});
		},
		beforeOpen(done, type) {
			this.paramsList = [];
			if (['edit', 'view'].includes(type)) {
				this.$request({
					url: cmsapplicationUrl.detail,
					params: {
						id: this.form.id
					},
					method: 'GET'
				}).then(res => {
					if (res.rCode === 0) {
						this.form = res.results;
						this.paramsList = JSON.parse(this.form.appArgs);
					} else {
						this.$message.error(res.msg);
					}
				});
			}
			if (['edit', 'add'].includes(type)) {
				this.argIsEdit = true;
			} else {
				this.argIsEdit = false;
			}
			done();
		},
		doSearch() {
			this.onLoad(this.page);
		},
		searchReset() {
			this.query = {};
			this.onLoad(this.page);
		},
		searchChange(params, done) {
			this.query = params;
			this.page.currentPage = 1;
			this.onLoad(this.page, params);
			done();
		},
		selectionChange(list) {
			this.selectionList = list;
		},
		selectionClear() {
			this.selectionList = [];
		},
		currentChange(currentPage) {
			this.page.currentPage = currentPage;
		},
		sizeChange(pageSize) {
			this.page.pageSize = pageSize;
		},
		onLoad(page) {
			this.loading = true;
			let params = {
				pageNum: this.page.currentPage,
				pageSize: this.page.pageSize
			};
			params = Object.assign(params, this.query);
			this.$request({
				url: cmsapplicationUrl.page,
				params: params,
				method: 'GET'
			}).then(res => {
				if (res.rCode === 0) {
					const data = res.results;
					this.page.total = data.total;
					this.data = data.records;
					this.loading = false;
					this.selectionClear();
				}
			});
		},
		rowStatusOpen(id) {
			this.$request({
				url: cmsapplicationUrl.statusOpen,
				data: {
					id: id
				},
				method: 'POST'
			}).then(res => {
				if (res.rCode === 0) {
					this.onLoad();
					this.$message({
						type: 'success',
						message: '操作成功!'
					});
				} else {
					this.$message.error(res.msg);
				}
			});
		},
		rowStatusClose(id) {
			this.$request({
				url: cmsapplicationUrl.statusClose,
				data: {
					id: id
				},
				method: 'POST'
			}).then(res => {
				if (res.rCode === 0) {
					this.onLoad();
					this.$message({
						type: 'success',
						message: '操作成功!'
					});
				} else {
					this.$message.error(res.msg);
				}
			});
		},
		addParam() {
			let param = { label: null, code: null, value: null };
			this.paramsList.push(param);
		},
		removeParam(index) {
			this.paramsList.splice(index, 1);
		},
		checkParam() {
			if (this.paramsList.length > 0) {
				for (let m of this.paramsList) {
					if (
						m.label === '' ||
						m.label == null ||
						m.code === '' ||
						m.code == null ||
						m.value === '' ||
						m.value == null
					) {
						this.$message({ type: 'warning', duration: 1500, message: '请完善参数信息' });
						return false;
					}
				}
				let codes = this.paramsList.map(param => param.code);
				let disCodes = new Set(codes);
				if (disCodes.size < codes.length) {
					this.$message({ type: 'warning', duration: 1500, message: '参数编码存在重复' });
					return false;
				}
			}
			return true;
		}
	}
};
</script>

<style>
.paramsClass {
	width: 50%;
	margin-bottom: 5px;
	display: flex;
	flex-direction: row;
	justify-content: space-around;
}
.paramsClass div {
	margin-right: 5px;
}
</style>
