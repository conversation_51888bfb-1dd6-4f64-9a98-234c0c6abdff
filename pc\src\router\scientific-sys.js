/**
 * 计算两个数的和
 * @openWindow 是否全屏窗口打开
 * @props { contentsKey: '', isCard: true  } contentsKey：动态页面判断key  isCard：卡片列表还是表单列表
 */

//科研管理系统
export default [
	{
		path: '/approvalList',
		title: '项目清单',
		name: 'approvalList',
		component: resolve =>
			require(['@/views/scientific-sys/project-management/inventory/index.vue'], resolve)
	},
	{
		path: '/finalAcceptance',
		title: '项目验收（结题）',
		name: 'FinalAcceptance',
		component: resolve => require(['@/views/scientific-sys/finalAcceptance/index.vue'], resolve)
	},
	{
		path: '/finalAcceptanceFlow',
		title: '项目验收（结题）-流程',
		name: 'finalAcceptanceFlow',
		openWindow: true,
		component: resolve => require(['@/views/scientific-sys/finalAcceptance/flow-page.vue'], resolve)
	},
	{
		path: '/archive',
		title: '归档项目',
		name: 'archive',
		component: resolve =>
			require(['@/views/scientific-sys/project-management/inventory/archive.vue'], resolve)
	},
	{
		path: '/interimCheck',
		title: '中期检查',
		name: 'interimCheck',
		component: resolve =>
			require([
				'@/views/scientific-sys/project-management/inventory/interim-check/index.vue'
			], resolve)
	},
	{
		path: '/scientific-sys-home',
		title: '系统概述',
		name: 'scientific-sys-home',
		component: resolve => require(['@/views/scientific-sys/home'], resolve)
	},
	{
		path: '/scientific-sys-home-page',
		title: '科研首页',
		openWindow: true,
		name: 'scientific-sys-home-page',
		component: resolve => require(['@/views/scientific-sys/home/<USER>'], resolve)
	},
	{
		path: '/scientificNotice',
		title: '科研公告',
		name: 'scientificNotice',
		component: resolve => require(['@/views/scientific-sys/scientific-notice'], resolve)
	},
	{
		path: '/teacherPim',
		title: '教师个人信息管理',
		name: 'teacherPim',
		component: resolve => require(['@/views/scientific-sys/teacher-pim/index.vue'], resolve)
	},
	{
		path: '/crosswiseProject',
		title: '横向项目',
		name: 'crosswiseProject',
		props: route => ({ contentsKey: 'crosswiseProject' }), // 动态页面判断key
		component: resolve =>
			require(['@/views/scientific-sys/scientific-research-project/index.vue'], resolve)
	},
	{
		path: '/crosswiseProjectFlow',
		title: '横向项目-流程',
		name: 'crosswiseProjectFlow',
		openWindow: true,
		props: route => ({ contentsKey: 'crosswiseProject' }), // 动态页面判断key
		component: resolve =>
			require(['@/views/scientific-sys/scientific-research-project/flow-page.vue'], resolve)
	},
	{
		path: '/verticalProject',
		title: '纵向项目',
		name: 'verticalProject',
		props: route => ({ contentsKey: 'verticalProject' }), // 动态页面判断key
		component: resolve =>
			require(['@/views/scientific-sys/scientific-research-project/index.vue'], resolve)
	},
	{
		path: '/verticalProjectFlow',
		title: '纵向项目-流程',
		name: 'verticalProjectFlow',
		openWindow: true,
		props: route => ({ contentsKey: 'verticalProject' }), // 动态页面判断key
		component: resolve =>
			require(['@/views/scientific-sys/scientific-research-project/flow-page.vue'], resolve)
	},
	{
		path: '/instituteProject',
		title: '院级项目',
		name: 'instituteProject',
		props: route => ({ contentsKey: 'instituteProject' }), // 动态页面判断key
		component: resolve =>
			require(['@/views/scientific-sys/scientific-research-project/index.vue'], resolve)
	},
	{
		path: '/instituteProjectFlow',
		title: '院级项目-流程',
		name: 'instituteProjectFlow',
		openWindow: true,
		props: route => ({ contentsKey: 'instituteProject' }), // 动态页面判断key
		component: resolve =>
			require(['@/views/scientific-sys/scientific-research-project/flow-page.vue'], resolve)
	},
	{
		path: '/projectChange',
		title: '项目变更',
		name: 'ProjectChange',
		component: resolve => require(['@/views/scientific-sys/projectChange/index.vue'], resolve)
	},
	{
		path: '/projectChangeFlow',
		title: '项目变更-流程',
		name: 'projectChangeFlow',
		openWindow: true,
		component: resolve => require(['@/views/scientific-sys/projectChange/flow-page.vue'], resolve)
	},
	{
		path: '/academicPaper',
		title: '学术论文',
		name: 'academicPaper',
		props: route => ({ contentsKey: 'academicPaper', isCard: true }), // 动态页面判断key
		component: resolve =>
			require(['@/views/scientific-sys/scientific-research-achievement/index.vue'], resolve)
	},
	{
		path: '/academicPaperFlow',
		title: '学术论文-流程',
		name: 'academicPaperFlow',
		openWindow: true,
		props: route => ({ contentsKey: 'academicPaper' }), // 动态页面判断key
		component: resolve =>
			require(['@/views/scientific-sys/scientific-research-achievement/flow-page.vue'], resolve)
	},

	//---
	{
		path: '/academicBook',
		title: '学术著作',
		name: 'academicBook',
		props: route => ({ contentsKey: 'academicBook', isCard: true }), // 动态页面判断key
		component: resolve =>
			require(['@/views/scientific-sys/scientific-research-achievement/index.vue'], resolve)
	},
	{
		path: '/academicBookFlow',
		title: '学术著作-流程',
		name: 'academicBookFlow',
		openWindow: true,
		props: route => ({ contentsKey: 'academicBook' }), // 动态页面判断key
		component: resolve =>
			require(['@/views/scientific-sys/scientific-research-achievement/flow-page.vue'], resolve)
	},
	{
		path: '/academicPatent',
		title: '学术专利',
		name: 'academicPatent',
		props: route => ({ contentsKey: 'academicPatent', isCard: true }), // 动态页面判断key
		component: resolve =>
			require(['@/views/scientific-sys/scientific-research-achievement/index.vue'], resolve)
	},
	{
		path: '/academicPatentFlow',
		title: '学术专利-流程',
		name: 'academicPatentFlow',
		openWindow: true,
		props: route => ({ contentsKey: 'academicPatent' }), // 动态页面判断key
		component: resolve =>
			require(['@/views/scientific-sys/scientific-research-achievement/flow-page.vue'], resolve)
	},
	{
		path: '/platformTeam',
		title: '平台团队',
		name: 'platformTeam',
		props: route => ({ contentsKey: 'platformTeam', isCard: true }), // 动态页面判断key
		component: resolve =>
			require(['@/views/scientific-sys/scientific-research-achievement/index.vue'], resolve)
	},
	{
		path: '/platformTeamFlow',
		title: '平台团队-流程',
		name: 'platformTeamFlow',
		openWindow: true,
		props: route => ({ contentsKey: 'platformTeam' }), // 动态页面判断key
		component: resolve =>
			require(['@/views/scientific-sys/scientific-research-achievement/flow-page.vue'], resolve)
	},
	{
		path: '/technicalStandard',
		title: '技术标准',
		name: 'technicalStandard',
		props: route => ({ contentsKey: 'technicalStandard', isCard: true }), // 动态页面判断key
		component: resolve =>
			require(['@/views/scientific-sys/scientific-research-achievement/index.vue'], resolve)
	},
	{
		path: '/technicalStandardFlow',
		title: '技术标准-流程',
		name: 'technicalStandardFlow',
		openWindow: true,
		props: route => ({ contentsKey: 'technicalStandard' }), // 动态页面判断key
		component: resolve =>
			require(['@/views/scientific-sys/scientific-research-achievement/flow-page.vue'], resolve)
	},
	{
		path: '/technicalProduct',
		title: '技术产品',
		name: 'technicalProduct',
		props: route => ({ contentsKey: 'technicalProduct', isCard: true }), // 动态页面判断key
		component: resolve =>
			require(['@/views/scientific-sys/scientific-research-achievement/index.vue'], resolve)
	},
	{
		path: '/technicalProductFlow',
		title: '技术产品-流程',
		name: 'technicalProductFlow',
		openWindow: true,
		props: route => ({ contentsKey: 'technicalProduct' }), // 动态页面判断key
		component: resolve =>
			require(['@/views/scientific-sys/scientific-research-achievement/flow-page.vue'], resolve)
	},
	{
		path: '/awardAchievement',
		title: '获奖成果',
		name: 'awardAchievement',
		props: route => ({ contentsKey: 'awardAchievement', isCard: true }), // 动态页面判断key
		component: resolve =>
			require(['@/views/scientific-sys/scientific-research-achievement/index.vue'], resolve)
	},
	{
		path: '/awardAchievementFlow',
		title: '获奖成果-流程',
		name: 'awardAchievementFlow',
		openWindow: true,
		props: route => ({ contentsKey: 'awardAchievement' }), // 动态页面判断key
		component: resolve =>
			require(['@/views/scientific-sys/scientific-research-achievement/flow-page.vue'], resolve)
	},
	{
		path: '/softwareAchievement',
		title: '软著',
		name: 'softwareAchievement',
		props: route => ({ contentsKey: 'softwareAchievement', isCard: true }), // 动态页面判断key
		component: resolve =>
			require(['@/views/scientific-sys/scientific-research-achievement/index.vue'], resolve)
	},
	{
		path: '/softwareAchievementFlow',
		title: '软著-流程',
		name: 'softwareAchievementFlow',
		openWindow: true,
		props: route => ({ contentsKey: 'softwareAchievement' }), // 动态页面判断key
		component: resolve =>
			require(['@/views/scientific-sys/scientific-research-achievement/flow-page.vue'], resolve)
	},
	{
		path: '/scientificConversionAchievement',
		title: '科技转化成果',
		name: 'scientificConversionAchievement',
		props: route => ({ contentsKey: 'scientificConversionAchievement', isCard: true }), // 动态页面判断key
		component: resolve =>
			require(['@/views/scientific-sys/scientific-research-achievement/index.vue'], resolve)
	},
	{
		path: '/scientificConversionAchievementFlow',
		title: '科技转化成果-流程',
		name: 'scientificConversionAchievementFlow',
		openWindow: true,
		props: route => ({ contentsKey: 'scientificConversionAchievement' }), // 动态页面判断key
		component: resolve =>
			require(['@/views/scientific-sys/scientific-research-achievement/flow-page.vue'], resolve)
	},
	{
		path: '/pa_competition',
		title: '竞赛获奖',
		name: 'pa_competition',
		props: route => ({ contentsKey: 'pa_competition', isCard: true }), // 动态页面判断key
		component: resolve =>
			require(['@/views/scientific-sys/scientific-research-achievement/index.vue'], resolve)
	},
	{
		path: '/competitionFlow',
		title: '竞赛获奖-流程',
		name: 'competitionFlow',
		openWindow: true,
		props: route => ({ contentsKey: 'pa_competition' }), // 动态页面判断key
		component: resolve =>
			require(['@/views/scientific-sys/scientific-research-achievement/flow-page.vue'], resolve)
	},

	// 科研统计自然
	{
		path: '/kjrlzyqk_zr',
		title: '科技人力资源情况(自然)',
		name: 'kjrlzyqk_zr',
		component: resolve => require(['@/views/scientific-sys/statistics/kjrlzyqk_zr.vue'], resolve)
	},
	{
		path: '/kjxmqkb_zr',
		title: '科技项目情况表(自然)',
		name: 'kjxmqkb_zr',
		component: resolve => require(['@/views/scientific-sys/statistics/kjxmqkb_zr.vue'], resolve)
	},
	{
		path: '/jszryzscqqk_zr',
		title: '技术转让与知识产权情况表(自然)',
		name: 'jszryzscqqk_zr',
		component: resolve => require(['@/views/scientific-sys/statistics/jszryzscqqk_zr.vue'], resolve)
	},
	{
		path: '/kjcgqk_zr',
		title: '科技成果情况表(自然)',
		name: 'kjcgqk_zr',
		component: resolve => require(['@/views/scientific-sys/statistics/kjcgqk_zr.vue'], resolve)
	},
	{
		path: '/cbkjzz_zr',
		title: '出版科技著作情况表(自然)',
		name: 'cbkjzz_zr',
		component: resolve => require(['@/views/scientific-sys/statistics/cbkjzz_zr.vue'], resolve)
	},

	{
		path: '/kjcgjlqk_zr',
		title: '科技成果奖励情况表(自然)',
		name: 'kjcgjlqk_zr',
		component: resolve => require(['@/views/scientific-sys/statistics/kjcgjlqk_zr.vue'], resolve)
	},

	// 科研统计社科
	{
		path: '/skhdry_sk',
		title: '社科活动人员（社科）',
		name: 'skhdry_sk',
		component: resolve => require(['@/views/scientific-sys/statistics/skhdry_sk.vue'], resolve)
	},
	{
		path: '/xmdn_sk',
		title: '项目年度工作量（社科）',
		name: 'xmdn_sk',
		component: resolve => require(['@/views/scientific-sys/statistics/xmdn_sk.vue'], resolve)
	},
	{
		path: '/kycg_sk',
		title: '科研成果（社科）',
		name: 'kycg_sk',
		component: resolve => require(['@/views/scientific-sys/statistics/kycg_sk.vue'], resolve)
	},
	{
		path: '/kyxmtj_sk',
		title: '科研项目（社科）',
		name: 'kyxmtj_sk',
		component: resolve => require(['@/views/scientific-sys/statistics/kyxmtj_sk.vue'], resolve)
	},
	{
		path: '/kycghj_sk',
		title: '科研成果获奖（社科）',
		name: 'kycghj_sk',
		component: resolve => require(['@/views/scientific-sys/statistics/kycghj_sk.vue'], resolve)
	}
];
