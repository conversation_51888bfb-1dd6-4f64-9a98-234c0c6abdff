<template>
	<div class="content">
		<es-tree-group
			:tree="tree"
			@node-click="handleChange"
			:syncKeys="{ id: 'id', name: 'name' }"
		></es-tree-group>
		<es-data-table
			ref="table"
			:row-style="tableRowClassName"
			:full="true"
			:fit="true"
			:thead="thead"
			:toolbar="toolbar"
			:border="true"
			:page="pageOption"
			:url="dataTableUrl"
			:numbers="true"
			:param="params"
			close
			form
			@btnClick="btnClick"
			@sort-change="sortChange"
			@submit="hadeSubmit"
			@edit="changeTable"
		></es-data-table>
		<es-dialog
			:title="formTitle"
			:visible.sync="showForm"
			width="60%"
			:close-on-click-modal="false"
			:destroy-on-close="true"
		>
			<es-form
				ref="form"
				:model="formData"
				:contents="formItemList"
				height="500px"
				:genre="2"
				collapse
				@change="inputChange"
				@submit="handleFormSubmit"
				@reset="showForm = false"
			/>
		</es-dialog>
		<es-dialog
			title="删除"
			:visible.sync="showDelete"
			width="20%"
			:close-on-click-modal="false"
			:middle="true"
			height="140px"
		>
			<div>确定要删除该条数据吗</div>
			<div class="btn-box">
				<div class="btn theme" @click="deleteRow">确定</div>
				<div class="btn" @click="showDelete = false">取消</div>
			</div>
		</es-dialog>
	</div>
</template>

<script>
import interfaceUrl from '@/http/union/unionMoveRecord/api.js';
import SnowflakeId from 'snowflake-id';

export default {
	data() {
		return {
			tree: {
				defaultExpandAll: true,
				showSearch: false,
				data: [],
				defaultProps: {
					children: 'children',
					label: 'name'
				}
			},
			unionSelect: {
				label: '请选择所属工会',
				id: 'union-select'
			},
			unionSelectData: [],
			collegeSelectData: [],
			ownId: '',
			deleteId: '',
			dataTableUrl: interfaceUrl.cUserListJson,
			showForm: false,
			showRoleForm: false,
			enpList: [],
			showDelete: false,
			formData: {},
			clickNode: '',
			roleFormData: {
				userId: '',
				username: '',
				phone: '',
				selectedRoleList: []
			},
			formTitle: '编辑',
			editBtn: {
				type: 'submit',
				skin: 'lay-form-btns',
				contents: [
					{
						type: 'primary',
						text: '确定',
						event: 'confirm'
					},
					{
						type: 'reset',
						text: '取消',
						event: 'cancel'
					}
				]
			},
			cancelBtn: {
				type: 'submit',
				skin: 'lay-form-btns',
				contents: [
					{
						type: 'reset',
						text: '取消',
						event: 'cancel'
					}
				]
			},
			toolbar: [
				{
					type: 'search',
					contents: [
						{
							type: 'text',
							name: 'keyword',
							placeholder: '关键字查询'
						}
					]
				}
			],
			thead: [
				{
					title: '姓名',
					align: 'left',
					field: 'memberName',
					sortable: 'custom',
					showOverflowTooltip: true
				},
				{
					title: '所属学院',
					align: 'left',
					field: 'collegeName',
					sortable: 'custom',
					showOverflowTooltip: true
				},
				{
					title: '是否在编',
					align: 'left',
					field: 'isSystemName',
					sortable: 'custom',
					showOverflowTooltip: true
				},
				{
					title: '证件号',
					align: 'left',
					field: 'idcard',
					sortable: 'custom',
					width: 180,
					showOverflowTooltip: true
				},
				{
					title: '退出的工会',
					align: 'left',
					field: 'unionName',

					sortable: 'custom',
					showOverflowTooltip: true
				},
				{
					title: '入会时间',
					align: 'center',
					field: 'joinTime',
					sortable: 'custom',
					showOverflowTooltip: true
				},
				{
					hide: this.$route.query.code == 2 ? false : true,
					title: '退会时间',
					align: 'center',
					field: 'createTime',
					width: 170,
					sortable: 'custom',
					showOverflowTooltip: true
				},
				{
					title: '联系方式',
					align: 'center',
					field: 'phone',
					sortable: 'custom',
					showOverflowTooltip: true
				},
				{
					title: '退会原因',
					align: 'center',
					// field: 'phone',
					render: (h, params) => {
						return h('p', { calss: { p1: true } }, '退休');
					},
					showOverflowTooltip: true
				},
				{
					title: '操作',
					type: 'handle',
					align: 'center',
					width: 180,
					template: '',
					events: [
						{
							code: 'view',
							text: '查看'
						}
					]
				}
			],
			pageOption: {
				layout: 'total, prev, pager, next, jumper',
				pageSize: 10,
				// hideOnSinglePage: true,
				position: 'right',
				current: 1,
				pageNum: 1
			},
			params: {
				asc: 'false',
				orderBy: 'createTime',
				operateModule: 2
			}
		};
	},
	computed: {
		formItemList() {
			return [
				{
					label: '姓名',
					name: 'memberName',
					placeholder: '请输入姓名',
					event: 'multipled',
					rules: {
						required: true,
						message: '请输入姓名',
						trigger: 'blur'
					},
					verify: 'required',
					col: 6
				},
				{
					label: '性别',
					name: 'sex',
					placeholder: '请选择性别',
					type: 'radio',
					rules: {
						required: true,
						message: '请选择性别',
						trigger: 'blur'
					},
					verify: 'required',
					col: 5,
					sysCode: 'public_sex',
					'label-key': 'shortName',
					'value-key': 'cciValue'
				},
				{
					label: '所属学院',
					name: 'collegeId',
					placeholder: '请选择所属学院',
					type: 'select',
					tree: true,
					valueKey: 'value',
					labelKey: 'name',
					event: 'multipled',
					rules: {
						required: true,
						message: '请选择所属学院',
						trigger: 'blur'
					},
					data: this.collegeSelectData,
					valueType: 'string',
					verify: 'required',
					col: 6
				},
				{
					label: '工会',
					name: 'unionId',
					placeholder: '请选择工会',
					type: 'select',
					tree: true,
					valueKey: 'id',
					labelKey: 'name',
					events: { change: this.unionSelectChange },
					rules: {
						required: true,
						message: '请选择上级工会',
						trigger: 'blur'
					},
					data: this.unionSelectData,
					valueType: 'string',
					verify: 'required',
					col: 6
				},
				{
					label: '工号',
					name: 'jobNumber',
					placeholder: '请输入工号',
					event: 'multipled',
					rules: {
						required: true,
						message: '请输入工号',
						trigger: 'blur'
					},
					verify: 'required',
					col: 6
				},
				{
					label: '证件号',
					name: 'idcard',
					placeholder: '请输入证件号',
					event: 'multipled',
					rules: [
						{
							required: true,
							message: '请输入证件号',
							trigger: 'blur'
						},
						{ pattern: /^\d{18}$/, message: '证件号格式不正确', trigger: 'blur' }
					],
					col: 6
				},
				{
					label: '联系方式',
					name: 'phone',
					placeholder: '请输入联系方式',
					event: 'multipled',
					rules: [
						{
							required: true,
							message: '请输入联系方式',
							trigger: 'blur'
						},
						{ pattern: /^1[3456789]\d{9}$/, message: '手机号码格式不正确', trigger: 'blur' }
					],
					verify: 'required',
					col: 6
				},
				{
					label: '籍贯',
					name: 'nativePlace',
					placeholder: '请输入籍贯',
					event: 'multipled',
					rules: {
						required: true,
						message: '请输入籍贯',
						trigger: 'blur'
					},
					verify: 'required',
					col: 6
				},
				{
					label: '在编/非在编',
					name: 'isSystem',
					placeholder: '请选择在编/非在编',
					type: 'radio',
					rules: {
						required: true,
						message: '请选择在编/非在编',
						trigger: 'blur'
					},
					verify: 'required',
					col: 6,
					sysCode: 'union_is_system',
					'label-key': 'shortName',
					'value-key': 'cciValue'
				},
				{
					hide: this.$route.query.code == 1 ? false : true,
					type: 'date',
					label: '入会时间',
					name: 'joinTime',
					event: 'multipled',
					default: true,
					placeholder: '请选择入会时间',
					rules: {
						required: true,
						message: '请选择入会时间',
						trigger: 'change'
					},
					col: 6
				},
				{
					hide: this.$route.query.code == 1 ? false : true,
					label: '详细信息',
					type: 'textarea',
					name: 'remark',
					event: 'multipled',
					default: true,
					placeholder: '',
					col: 6
				},
				{
					hide: this.$route.query.code == 2 ? false : true,
					type: 'date',
					label: '退会时间',
					name: 'createTime',
					event: 'multipled',
					default: true,
					placeholder: '请选择退会时间',
					rules: {
						required: true,
						message: '请选择退会时间',
						trigger: 'change'
					},
					col: 6
				}
			];
		}
	},
	watch: {},
	created() {
		this.initTree();
		this.initCollegeTree();
	},
	mounted() {},
	methods: {
		/**
		 * 表格行高
		 */
		tableRowClassName({ row, rowIndex }) {
			let styleRes = {
				height: '54px !important'
			};
			return styleRes;
		},
		inputChange(key, value) {
			if (key == 'phone') {
			}
		},
		hadeSubmit(data) {},

		/**
		 * 树点击事件
		 */
		handleChange(tree, data) {
			this.clickNode = data.id;
			this.$set(this.params, 'unionId', data.id);
		},
		handleNodeClick(data) {
			console.log(data);
		},

		/**
		 * 树点击事件
		 */
		unionSelectChange(tree, data) {
			console.log(data);
		},

		/**
		 * 操作按钮点击事件
		 * @param {*} res
		 */
		btnClick(res) {
			let code = res.handle.code;
			switch (code) {
				case 'add':
					// 新增
					this.formTitle = '新增';
					this.editModule(this.formItemList, []);
					const snowflake = new SnowflakeId();
					let id = snowflake.generate();
					this.ownId = id;
					this.formData = { id: id, unionId: this.clickNode };
					this.showForm = true;
					break;
				case 'edit':
					// 编辑
					this.formTitle = '编辑';
					this.ownId = res.row.id;
					this.editModule(this.formItemList, ['pwd']);
					this.$request({
						url: interfaceUrl.info + '/' + res.row.id,
						method: 'GET'
					}).then(res => {
						if (res.rCode == 0) {
							this.formData = res.results;
							this.showForm = true;
						}
					});
					break;
				case 'view':
					this.formTitle = '查看';
					this.ownId = res.row.id;
					this.readModule(this.formItemList, ['pwd']);
					this.$request({
						url: interfaceUrl.info + '/' + res.row.id,
						method: 'GET'
					}).then(res => {
						if (res.rCode == 0) {
							this.formData = res.results;
							this.showForm = true;
						}
					});
					break;
				case 'setRole':
					this.showRoleForm = true;
					this.roleFormData.userId = res.row.id;
					this.roleFormData.username = res.row.username;
					this.roleFormData.phone = res.row.phone;
					// 获取企业选择列表
					this.$request({
						url: interfaceUrl.enpSelectList,
						method: 'GET',
						params: {
							userId: res.row.id
						}
					}).then(res => {
						if (res.rCode == 0) {
							this.enpList = res.results;
						}
					});
					break;
				case 'delete':
					this.deleteId = res.row.id;
					this.showDelete = true;
					break;
				default:
					break;
			}
		},
		handleClose() {},
		handleFormItemChange() {},
		handleFormSubmit(data) {
			let formData = data;
			delete formData.extMap;
			let url = '';
			if (this.formTitle == '新增') {
				url = interfaceUrl.save;
				let Base64 = require('js-base64').Base64;
				formData['password'] = Base64.encode(formData.pwd);
			} else {
				url = interfaceUrl.update;
			}
			this.$request({
				url: url,
				data: formData,
				method: 'POST'
			}).then(res => {
				if (res.rCode == 0) {
					this.$message.success('操作成功');
					this.showForm = false;
					this.formData = {};
					this.$refs.table.reload();
					this.initTree();
				} else {
					this.formData.state = 1 == this.formData.state ? true : false;
					this.$message.error(res.msg);
				}
			});
		},
		deleteRow() {
			this.$request({
				url: interfaceUrl.deleteBatchIds,
				data: { ids: this.deleteId },
				method: 'POST'
			}).then(res => {
				if (res.rCode == 0) {
					this.$refs.table.reload();
					this.$message.success('删除成功');
				} else {
					this.$message.error(res.msg);
				}
				this.showDelete = false;
				this.initTree();
			});
		},
		/**
		 * 排序变化事件
		 * @param {*} column
		 * @param {*} prop
		 * @param {*} order
		 */
		sortChange(column, prop, order) {
			if (column.order == 'ascending') {
				//升序
				this.params = {
					asc: 'true',
					orderBy: column.prop
				};
			} else if (column.order == 'descending') {
				//降序
				this.params = {
					asc: 'false',
					orderBy: column.prop
				};
			} else {
				//不排序
				this.params = {
					asc: 'false',
					orderBy: 'createTime'
				};
			}
			this.$refs.table.reload();
		},
		/**
		 * 编辑模式
		 */
		editModule(list, hideField) {
			for (var i in list) {
				var item = list[i];
				item.readonly = false;
				if (undefined != hideField && hideField.length > 0 && hideField.indexOf(item.name) > -1) {
					item.hide = true;
				}
			}
			list.push(this.editBtn);
		},
		/**
		 * 只读模式
		 */
		readModule(list, hideField) {
			for (var i in list) {
				var item = list[i];
				item.readonly = true;
				if (undefined != hideField && hideField.length > 0 && hideField.indexOf(item.name) > -1) {
					item.hide = true;
				}
			}
			list.push(this.cancelBtn);
		},
		changeTable(val) {
			if ('state' === val.name) {
				this.$request({
					url: interfaceUrl.updateState,
					data: {
						id: val.data.id,
						state: val.value
					},
					method: 'POST'
				}).then(res => {
					if (res.rCode == 0) {
						this.$message.success('操作成功');
					} else {
						this.$message.error(res.msg);
					}
				});
			}
		},

		// 初始化左侧树数据
		initTree() {
			this.$request({
				url: interfaceUrl.cUserTreeList,
				method: 'POST'
			}).then(res => {
				if (res.rCode == 0) {
					this.tree.data = res.results.baseTree;
					this.unionSelectData = res.results.parentTree;
				}
			});
		},

		// 初始化学院选择树数据
		initCollegeTree() {
			this.$request({
				url: interfaceUrl.collegeTree,
				method: 'POST'
			}).then(res => {
				if (res.rCode == 0) {
					this.collegeSelectData = res.results;
				}
			});
		}
	}
};
</script>
<style scoped lang="scss">
@import '../../../assets/style/style.scss';

.content {
	display: flex;
	width: 100%;
	height: 100%;

	::v-deep .es-data-table {
		flex: 1;
		display: flex;
		flex-direction: column;

		.es-data-table-content {
			flex: 1;
			height: 0;
			display: flex;
			flex-direction: column;

			.el-table {
				flex: 1;
				// height: 100% !important;
			}

			.es-thead-border {
				.el-table__header {
					th {
						border-right: 1px solid #e1e1e1;
					}
				}
			}
		}
	}

	::v-deep .el-form-item__label {
		background: none;
		border: 0px solid #c2c2c2;
	}
}

.el-dialog__body {
	.btn-box {
		margin-top: 20px;
		display: flex;
		justify-content: flex-end;

		.btn {
			padding: 5px 10px;
			color: #666;
			border: 1px solid #eee;
			cursor: pointer;
			margin-right: 5px;
			border-radius: 4px;
			// &.theme {
			// 	background: $--color-primary;
			// 	color: #fff;
			// 	border-color: $--color-primary;
			// }
		}
	}
}
</style>
