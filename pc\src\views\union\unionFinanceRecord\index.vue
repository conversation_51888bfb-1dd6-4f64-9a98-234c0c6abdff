<template>
	<div class="content">
		<div style="width: 100%">
			<el-menu
				:default-active="activeMenus"
				mode="horizontal"
				class="es-menu"
				@select="handleSelect"
			>
				<el-menu-item v-for="(item, index) in menus" :key="index" :index="String(index)">
					{{ item.label }}
				</el-menu-item>
			</el-menu>
			<es-data-table
				:row-style="tableRowClassName"
				v-if="activeMenus === '0'"
				ref="table"
				:key="tableCount"
				:full="true"
				:fit="true"
				:thead="thead1"
				:toolbar="toolbar"
				:border="true"
				:page="pageOption"
				:url="dataTableUrl"
				:numbers="true"
				@btnClick="btnClick"
				@sort-change="sortChange"
				:param="params"
				@submit="hadeSubmit"
				close
			></es-data-table>
			<es-data-table
				:row-style="tableRowClassName"
				v-if="activeMenus === '1'"
				ref="table"
				:key="tableCount"
				:full="true"
				:fit="true"
				:thead="thead2"
				:toolbar="toolbar"
				:border="true"
				:page="pageOption"
				:url="dataTableUrl"
				:numbers="true"
				@btnClick="btnClick"
				@sort-change="sortChange"
				:param="params"
				@submit="hadeSubmit"
				close
			></es-data-table>
		</div>
		<es-dialog
			:title="formTitle"
			:visible.sync="showForm"
			width="60%"
			:close-on-click-modal="false"
			:destroy-on-close="true"
		>
			<div>
				<es-form
					ref="form"
					:model="formData"
					:contents="formItemList"
					height="auto"
					:genre="2"
					collapse
					@reset="showForm = false"
				></es-form>
				<es-data-table
					form
					:editable="editable"
					:numbers="false"
					:data="detailTableData"
					:thead="typeSelectDataR"
					height="auto"
					@change="hanldeChange"
				></es-data-table>
				<es-form
					ref="form"
					:model="formData"
					:contents="formBtnList"
					height="auto"
					:genre="2"
					collapse
					@change="inputChange"
					@submit="handleFormSubmit"
					@reset="showForm = false"
				></es-form>
			</div>
		</es-dialog>
		<es-dialog
			title="删除"
			:visible.sync="showDelete"
			width="20%"
			:close-on-click-modal="false"
			:middle="true"
			height="140px"
		>
			<div>确定要删除该条数据吗</div>
			<div class="btn-box">
				<div class="btn theme" @click="deleteRow">确定</div>
				<div class="btn" @click="showDelete = false">取消</div>
			</div>
		</es-dialog>
	</div>
</template>

<script>
import interfaceUrl from '@/http/union/unionFinanceRecord/api.js';
import SnowflakeId from 'snowflake-id';
import { host } from '../../../../config/config';
export default {
	data() {
		return {
			visible: false,
			addFlag: false,
			auditFlag: false,
			viewFlag: false,
			showDelete: false,
			deleteId: '',
			dialogTitle: '',
			menus: [{ label: '收入' }, { label: '支出' }],
			activeMenus: '0',
			moduleCode: '1',
			moduleName: '收入',
			showForm: false,
			queryStatus: [],
			unionSelectData: [],
			dataTableUrl: interfaceUrl.listJson,
			treeData: [],
			tableCount: 1,
			ownId: '',
			selectInfo: null, //选中的数据值
			selectRow: null,
			typeSelectData: [],
			search: {
				name: 'keyword',
				placeholder: '请输入关键字筛选'
			},
			formTitle: '编辑',
			formData: {},
			editBtn: {
				type: 'submit',
				skin: 'lay-form-btns',
				contents: [
					{
						type: 'primary',
						text: '确定',
						event: 'confirm'
					},
					{
						type: 'reset',
						text: '取消',
						event: 'cancel'
					}
				]
			},
			cancelBtn: {
				type: 'submit',
				skin: 'lay-form-btns',
				contents: [
					{
						type: 'reset',
						text: '取消',
						event: 'cancel'
					}
				]
			},
			toolbar: [
				{
					type: 'button',
					contents: [
						{
							text: '新增',
							code: 'add',
							type: 'primary'
						}
					]
				},
				{
					type: 'search',
					contents: [
						{
							type: 'text',
							name: 'keyword',
							placeholder: '关键字查询'
						}
					]
				}
			],
			thead: [],
			thead1: [
				{
					title: '工会名称',
					align: 'left',
					field: 'unionName',
					sortable: 'custom',
					showOverflowTooltip: true
				},
				{
					title: '收入金额',
					align: 'left',
					field: 'money',
					sortable: 'custom',
					showOverflowTooltip: true
				},
				{
					title: '记录人',
					align: 'center',
					field: 'createUserName',
					sortable: 'custom',
					showOverflowTooltip: true
				},
				{
					title: '记录时间',
					align: 'center',
					field: 'createTime',
					sortable: 'custom',
					showOverflowTooltip: true
				},
				{
					title: '记录文件',
					align: 'center',
					field: 'recordFile',
					sortable: 'custom',
					showOverflowTooltip: true,
					render: (h, params) => {
						return h('el-button', {}, '预览');
					}
				},
				{
					title: '操作',
					type: 'handle',
					width: 180,
					template: '',
					events: [
						{
							code: 'edit',
							text: '编辑'
						},
						{
							code: 'view',
							text: '查看'
						},
						{
							code: 'delete',
							text: '删除'
						}
					]
				}
			],
			thead2: [
				{
					title: '工会名称',
					align: 'left',
					field: 'unionName',
					sortable: 'custom',
					showOverflowTooltip: true
				},
				{
					title: '支出金额',
					align: 'left',
					field: 'money',
					sortable: 'custom',
					showOverflowTooltip: true
				},
				{
					title: '记录人',
					align: 'center',
					field: 'createUserName',
					sortable: 'custom',
					showOverflowTooltip: true
				},
				{
					title: '记录时间',
					align: 'center',
					field: 'createTime',
					sortable: 'custom',
					showOverflowTooltip: true
				},
				{
					title: '操作',
					type: 'handle',
					width: 180,
					template: '',
					events: [
						{
							code: 'edit',
							text: '编辑'
						},
						{
							code: 'view',
							text: '查看'
						},
						{
							code: 'delete',
							text: '删除'
						}
					]
				}
			],
			pageOption: {
				layout: 'total, prev, pager, next, jumper',
				pageSize: 10,
				// hideOnSinglePage: true,
				position: 'right',
				current: 1,
				pageNum: 1
			},
			params: {
				asc: 'false',
				orderBy: 'createTime',
				moduleCode: '1'
			},
			editable: true,
			detailTableData: [
				{
					isActivityIn: '',
					activityName: '',
					transferor: '',
					description: '',
					sortNum: null,
					money: '',
					typeId: '',
					aboutDate: '',
					sourceOut: '',
					projectRemark: '',
				}
			],
			isNameReadonly: false
		};
	},
	computed: {
		formItemList() {
			return [
				{
					label: '所属工会',
					name: 'unionId',
					placeholder: '请选择所属工会',
					type: 'select',
					tree: true,
					valueKey: 'id',
					labelKey: 'name',
					event: 'multipled',
					rules: {
						required: true,
						message: '请选择所属工会',
						trigger: 'blur'
					},
					data: this.unionSelectData,
					valueType: 'string',
					verify: 'required',
					col: 6
				},
				{
					label: '所属类别',
					name: 'moduleCode',
					placeholder: '请选择所属类别',
					readonly: true,
					type: 'select',
					rules: {
						required: true,
						message: '请选择所属类别',
						trigger: 'blur'
					},
					verify: 'required',
					col: 6,
					sysCode: 'union_finance_module',
					'label-key': 'shortName',
					'value-key': 'cciValue'
				},
				{
					name: 'fj',
					valueType: 'string',
					label: '上传附件',
					type: 'attachment',
					code: 'union_finance_record_file',
					preview: true,
					canPreviewOffice: false,
					exclude: localStorage.getItem('excludeSuffix'),
					download: true,
					dragSort: true,
					ownId: this.ownId // 业务id
				},
				{
					type: 'submit',
					skin: 'lay-form-btns',
					contents: []
				}
			];
		},
		formBtnList() {
			return [
				{
					label: '按钮',
					hide: true
				},
				{
					type: 'submit',
					skin: 'lay-form-btns',
					contents: [
						{
							type: 'primary',
							text: '确定',
							event: 'confirm'
						},
						{
							type: 'reset',
							text: '取消',
							event: 'cancel'
						}
					]
				}
			];
		},
		typeSelectDataR() {
			return [
				{
					title: '是否活动' + this.moduleName,
					width: 140,
					field: 'isActivityIn',
					hide: this.moduleName == '支出',
					type: 'select',
					valueKey: 'value',
					labelKey: 'name',
					clearable: true,
					data: [
						{name: '是', value: '是'},
						{name: '否', value: '否'},
					],
					valueType: 'string',
					align: 'right'
				},
				{
					title: '活动名称',
					hide: this.moduleName == '支出',
					width: 120,
					field: 'activityName',
					maxlength: 60,
					type: 'text',
					align: 'right'
				},
				{
					title: '转账人',
					hide: this.moduleName == '支出',
					field: 'transferor',
					width: 120,
					maxlength: 60,
					type: 'text',
					align: 'right'
				},
				{
					title: '描述',
					field: 'description',
					width: 120,
					maxlength: 60,
					type: 'text',
					align: 'right'
				},
				{
					title: '排序',
					field: 'sortNum',
					width: 80,
					maxlength: 10,
					controls: false,
					type: 'number',
					align: 'right'
				},
				
				{
					title: this.moduleName + '金额',
					width: 120,
					field: 'money',
					type: 'text',
					align: 'right'
				},
				{
					title: this.moduleName + '类别',
					width: 120,
					field: 'typeId',
					type: 'select',
					valueKey: 'value',
					labelKey: 'name',
					data: this.typeSelectData,
					valueType: 'string',
					align: 'center'
				},
				{
					title: this.moduleName + '日期',
					width: 120,
					field: 'aboutDate',
					type: 'date',
					align: 'center'
				},
				{
					title: this.moduleName + '来源',
					width: 120,
					field: 'sourceOut',
					type: 'text',
					align: 'center'
				},
				{
					title: '备注',
					width: 140,
					field: 'projectRemark',
					type: 'text',
					align: 'center'
				}
			];
		}
	},
	watch: {
		dataTableUrl() {
			this.tableCount++;
		}
	},
	created() {
		this.initTree();
		this.initTypeSelect();
	},
	mounted() {},
	methods: {
		hanldeChange(e, h, v) {
			console.log(e, h, v);
			if (e == 'isActivityIn') {
				if (h == '是') {
					this.isNameReadonly = false // false,
					// this.$set(this.formItemList, 3, {
					// 	label: '研究开始时间',
					// 	placeholder: '请选择研究开始时间',
					// 	name: 'studyStartDate',
					// 	type: 'date',
					// 	event: 'multipled',
					// 	readonly: this.projectApprovalForm.state != 0,
					// 	rules: { required: false, trigger: 'blur', message: '请选择研究开始时间' },
					// 	col: 6
					// });
				} else  {
					this.isNameReadonly = true
				}
			}
		},
		/**
		 * 页签切换
		 * @param {*} res
		 */
		handleSelect(res) {
			this.activeMenus = res;
			this.moduleCode = parseInt(res) + 1 + '';
			this.params['moduleCode'] = this.moduleCode;
			this.tableCount++;
			this.initTypeSelect();
			if (this.moduleCode === '1') {
				this.moduleName = '收入';
			} else {
				this.moduleName = '支出';
			}
		},
		hadeSubmit(data) {
			console.log('hadeSubmit', data);
		},
		deleteRow() {
			this.$request({
				url: interfaceUrl.deleteBatchIds,
				data: { ids: this.deleteId },
				method: 'POST'
			}).then(res => {
				if (res.rCode == 0) {
					this.$refs.table.reload();
					this.$message.success('删除成功');
				} else {
					this.$message.error(res.msg);
				}
				this.showDelete = false;
			});
		},
		inputChange(key, value) {},
		/**
		 * 表格行高
		 */
		tableRowClassName({ row, rowIndex }) {
			let styleRes = {
				height: '54px !important'
			};
			return styleRes;
		},
		/**
		 * 操作按钮点击事件
		 * @param {*} res
		 */
		btnClick(res) {
			let code = res.handle.code;
			switch (code) {
				case 'add':
					// 新增
					this.formTitle = '新增';
					this.editModule(this.formItemList, this.formBtnList, []);
					const snowflake = new SnowflakeId();
					let id = snowflake.generate();
					this.ownId = id;
					this.formData = { id: id, moduleCode: this.moduleCode };
					this.showForm = true;
					this.detailTableData = [{}];
					break;
				case 'edit':
					// 编辑
					this.formTitle = '编辑';
					this.ownId = res.row.id;
					this.$request({
						url: interfaceUrl.info + '/' + res.row.id,
						method: 'GET'
					}).then(res => {
						if (res.rCode == 0) {
							this.formData = res.results;
							this.ownId = res.results.id;
							this.showForm = true;
							this.detailTableData = res.results.detailList;
						}
						this.editModule(this.formItemList, this.formBtnList, []);
					});
					break;
				case 'view':
					this.formTitle = '查看';
					this.ownId = res.row.id;
					this.$request({
						url: interfaceUrl.info + '/' + res.row.id,
						method: 'GET'
					}).then(res => {
						if (res.rCode == 0) {
							this.formData = res.results;
							this.ownId = res.results.id;
							this.showForm = true;
							this.detailTableData = res.results.detailList;
						}
						this.readModule(this.formItemList, this.formBtnList, []);
					});
					break;
				case 'delete':
					this.deleteId = res.row.id;
					this.showDelete = true;
					break;
				default:
					break;
			}
		},
		handleClose() {},
		handleFormItemChange() {},
		handleFormSubmit(data) {
			let formData = data;
			delete formData.extMap;
			delete formData.detailList;
			for (var i = 0; i < this.detailTableData.length; i++) {
				for (var key in this.detailTableData[i]) {
					var paramsName = 'detailList' + '[' + i + ']' + '.' + key;
					formData[paramsName] = this.detailTableData[i][key];
				}
			}
			let url = '';
			if (this.formTitle == '新增') {
				url = interfaceUrl.save;
			} else {
				url = interfaceUrl.update;
			}
			this.$request({
				url: url,
				data: formData,
				method: 'POST'
			}).then(res => {
				if (res.rCode == 0) {
					this.$message.success('操作成功');
					this.showForm = false;
					this.formData = {};
					this.$refs.table.reload();
				} else {
					this.formData.state = 1 == this.formData.state ? true : false;
					this.$message.error(res.msg);
				}
			});
		},
		/**
		 * 编辑模式
		 */
		editModule(list, btnList, hideField) {
			for (var i in btnList) {
				var item = btnList[i];
				item.readonly = false;
				if (undefined != hideField && hideField.length > 0 && hideField.indexOf(item.name) > -1) {
					item.hide = true;
				}
			}
			for (var i in list) {
				var item = list[i];
				item.readonly = false;
				if (undefined != hideField && hideField.length > 0 && hideField.indexOf(item.name) > -1) {
					item.hide = true;
				}
			}
			btnList.push(this.editBtn);
			this.editable = true;
			for (var i in this.detailTableData) {
				var item = this.detailTableData[i];
				item.canEdit = true;
			}
		},
		/**
		 * 只读模式
		 */
		readModule(list, btnList, hideField) {
			for (var i in btnList) {
				var item = btnList[i];
				item.readonly = true;
				if (undefined != hideField && hideField.length > 0 && hideField.indexOf(item.name) > -1) {
					item.hide = true;
				}
			}
			for (var i in list) {
				var item = list[i];
				item.readonly = true;
				if (undefined != hideField && hideField.length > 0 && hideField.indexOf(item.name) > -1) {
					item.hide = true;
				}
			}
			btnList.push(this.cancelBtn);
			this.editable = false;
			for (var i in this.detailTableData) {
				var item = this.detailTableData[i];
				// item.cantEditKey = ['money','typeId','aboutDate','sourceOut','projectRemark'];
				item.canEdit = false;
			}
		},
		changeTable(val) {
			if ('state' === val.name) {
				this.$request({
					url: interfaceUrl.updateState,
					data: {
						id: val.data.id,
						state: val.value
					},
					method: 'POST'
				}).then(res => {
					if (res.rCode == 0) {
						this.$message.success('操作成功');
					} else {
						this.$message.error(res.msg);
					}
				});
			}
		},
		/**
		 * 排序变化事件
		 * @param {*} column
		 * @param {*} prop
		 * @param {*} order
		 */
		sortChange(column, prop, order) {
			if (column.order == 'ascending') {
				//升序
				this.params = {
					asc: 'true',
					orderBy: column.prop
				};
			} else if (column.order == 'descending') {
				//降序
				this.params = {
					asc: 'false',
					orderBy: column.prop
				};
			} else {
				//不排序
				this.params = {
					asc: 'false',
					orderBy: 't1.applyTime'
				};
			}
			this.params['statusArray'] = this.queryStatus;
			this.$refs.table.reload();
		},
		dialogCancel() {
			this.visible = false;
			this.addFlag = false;
			this.auditFlag = false;
			this.viewFlag = false;
		},
		refreshData() {
			this.dialogCancel();
			this.$refs.table.reload();
		},
		//获取列表thead
		getListThead(btnJson) {
			let tempThead = this.listThead;
			if (tempThead.length > 7) {
				tempThead.pop();
			}
			tempThead.push(btnJson);
			return tempThead;
		},
		/**
		 * 获取下拉字典
		 */
		getDictionary() {},

		// 初始化工会下拉树数据
		initTree() {
			this.$request({
				url: interfaceUrl.treeJson,
				method: 'POST'
			}).then(res => {
				if (res.rCode == 0) {
					this.unionSelectData = res.results;
				}
			});
		},

		// 初始化类别下拉框数据
		initTypeSelect() {
			this.$request({
				url: interfaceUrl.typeSelect,
				method: 'POST',
				params: { moduleCode: this.moduleCode }
			}).then(res => {
				if (res.rCode == 0) {
					var list = [];
					for (var i = 0; i < res.results.length; i++) {
						list.push({ name: res.results[i].name, value: res.results[i].value });
					}
					this.typeSelectData = list;
				}
			});
		}
	}
};
</script>
<style scoped lang="scss">
@import '../../../assets/style/style.scss';

.content {
	display: flex;
	width: 100%;
	height: 100%;

	::v-deep .es-data-table {
		flex: 1;
		display: flex;
		flex-direction: column;
		height: calc(100% - 58px);

		.es-data-table-content {
			flex: 1;
			height: 0;
			display: flex;
			flex-direction: column;

			.el-table {
				flex: 1;
				// height: 100% !important;
			}

			.es-thead-border {
				.el-table__header {
					th {
						border-right: 1px solid #e1e1e1;
					}
				}
			}
		}
	}

	::v-deep .el-form-item__label {
		background: none;
		border: 0px solid #c2c2c2;
	}
}

.el-dialog__body {
	.btn-box {
		margin-top: 20px;
		display: flex;
		justify-content: flex-end;

		.btn {
			padding: 5px 10px;
			color: #666;
			border: 1px solid #eee;
			cursor: pointer;
			margin-right: 5px;
			border-radius: 4px;
			// &.theme {
			// 	background: $--color-primary;
			// 	color: #fff;
			// 	border-color: $--color-primary;
			// }
		}
	}
}
</style>
