<template>
	<div class="content">
		<es-data-table
			ref="table"
			:key="tableCount"
			:row-style="tableRowClassName"
			:full="true"
			:fit="true"
			:thead="thead"
			:toolbar="toolbar"
			:border="true"
			:page="pageOption"
			:url="dataTableUrl"
			:numbers="true"
			:param="params"
			close
			form
			@btnClick="btnClick"
		></es-data-table>
		<es-dialog
			:title="formTitle"
			:visible.sync="showForm"
			width="50%"
			:close-on-click-modal="false"
			:destroy-on-close="true"
		>
			<es-form
				ref="form"
				:model="formData"
				:contents="formItemList"
				:genre="2"
				collapse
				label-width="150px"
				:submit="false"
				:reset="true"
			/>
		</es-dialog>
		<!-- <es-dialog
			title="删除"
			:visible.sync="showDelete"
			width="20%"
			:close-on-click-modal="false"
			:middle="true"
			height="140px"
		>
			<div>确定要删除该条数据吗</div>
			<div class="btn-box">
				<div class="btn theme" @click="deleteRow">确定</div>
				<div class="btn" @click="showDelete = false">取消</div>
			</div>
		</es-dialog> -->
	</div>
</template>

<script>
import interfaceUrl from '@/http/health/psyAppointOrder.js';

export default {
	data() {
		return {
			dataTableUrl: interfaceUrl.listJson,
			tableCount: 1,
			showForm: false,
			ownId: null, //数据行Id
			collegeList: [],
			psyList: [],
			params: {},
			thead: [
				{
					title: '申请人姓名',
					align: 'center',
					field: 'name',
					render: (h, param) => {
						return h(
							'p',
							{ calss: { p1: true } },
							this.formatLink(param.row.name, param.row.phone)
						);
					},
					showOverflowTooltip: true
				},
				{
					title: '性别',
					align: 'center',
					field: 'sex',
					render: (h, param) => {
						return h('p', null, param.row.sex === 0 ? '男' : '女');
					}
				},
				{
					title: '年龄',
					align: 'center',
					field: 'age'
				},
				{
					title: '所属学院',
					align: 'center',
					field: 'college',
					showOverflowTooltip: true
				},
				{
					title: '班级',
					align: 'center',
					field: 'userClass',
					showOverflowTooltip: true
				},
				{
					title: '咨询师',
					align: 'center',
					field: 'psyName',
					showOverflowTooltip: true
				},
				{
					title: '咨询日期',
					align: 'center',
					field: 'appointTime'
				},
				{
					title: '状态',
					align: 'center',
					field: 'statusDes'
				},
				{
					title: '操作',
					type: 'handle',
					width: 140,
					template: '',
					events: [
						{ code: 'view', text: '查看' }
						// {
						// 	code: 'update',
						// 	text: '上传结果',
						// 	rules: rows => {
						// 		return rows.status == 2;
						// 	}
						// }
					]
				}
			],
			pageOption: {
				layout: 'total, prev, pager, next, jumper',
				pageSize: 10,
				// hideOnSinglePage: true,
				position: 'right',
				current: 1,
				pageNum: 1
			},
			formData: {},
			formTitle: '编辑'
		};
	},
	computed: {
		toolbar() {
			return [
				{
					type: 'search',
					reset: true,
					contents: [
						// {
						// 	type: 'select',
						// 	col: 6,
						// 	name: 'status',
						// 	label: '状态',
						// 	placeholder: '订单状态',
						// 	clearable: true,
						// 	data: [
						// 		{ value: -1, label: '已取消' },
						// 		{ value: 0, label: '待确认' },
						// 		{ value: 1, label: '已确认' },
						// 		{ value: 2, label: '已完成' },
						// 		{ value: 9, label: '驳回' }
						// 	]
						// },
						{
							type: 'select',
							name: 'college',
							label: '学院',
							placeholder: '请选择学院',
							data: this.collegeList,
							'label-key': 'label',
							'value-key': 'name',
							clearable: true,
							col: 6
						},
						{
							type: 'select',
							name: 'psyId',
							label: '咨询师',
							placeholder: '请选择咨询师',
							data: this.psyList,
							'label-key': 'name',
							'value-key': 'id',
							clearable: true,
							col: 6
						},
						{
							type: 'date',
							col: 6,
							name: 'startDate',
							label: '开始时间',
							placeholder: '开始时间'
						},
						{
							type: 'date',
							col: 6,
							name: 'endDate',
							label: '结束时间',
							placeholder: '结束时间'
						},
						{ type: 'text', name: 'keyword', placeholder: '关键字查询' }
					]
				}
			];
		},
		formItemList() {
			return [
				{
					title: '心理咨询师信息',
					contents: [
						{
							label: '头像',
							name: 'headUrlTemp',
							type: 'attachment',
							code: 'psy_head_img',
							ownId: this.formData.psyId,
							// size: 120,
							portrait: true,
							readonly: true,
							param: {
								isShowPath: true
							},
							col: 12
						},
						{
							label: '姓名',
							name: 'psyName',
							readonly: true,
							col: 6
						},
						{
							label: '性别',
							name: 'psySex',
							type: 'select',
							data: [
								{ value: 0, name: '男' },
								{ value: 1, name: '女' }
							],
							readonly: true,
							col: 6
						},
						{
							label: '擅长领域',
							name: 'psyAdept',
							type: 'textarea',
							readonly: true
						},
						{
							label: '简介',
							name: 'psyIntro',
							type: 'textarea',
							readonly: true
						}
					]
				},
				{
					title: '预约信息',
					contents: [
						{
							label: '预约时间',
							name: 'appointTime',
							readonly: true,
							col: 6
						},
						{
							label: '咨询地点',
							name: 'address',
							readonly: true,
							col: 6
						},
						{
							label: '状态',
							name: 'statusDes',
							readonly: true,
							col: 6
						},
						{
							name: 'fj',
							label: '咨询结果（*最多上传4个文件）',
							type: 'attachment',
							code: 'psy_consult_result_attachment',
							preview: true,
							ownId: this.formData.id, // 业务id
							hide: this.formData.status != 2, // 状态为已完成时显示
							limit: 4,
							col: 12
						},
						{
							label: '驳回原因',
							name: 'psyRemark',
							hide: this.formData.status != 9, // 状态为驳回时显示
							readonly: true,
							col: 12
						}
					]
				},
				{
					title: '预约用户信息',
					contents: [
						{
							label: '姓名',
							name: 'name',
							readonly: true,
							col: 6
						},
						{
							label: '学号',
							name: 'personNum',
							readonly: true,
							col: 6
						},
						{
							label: '性别',
							name: 'sex',
							type: 'select',
							data: [
								{ value: 0, name: '男' },
								{ value: 1, name: '女' }
							],
							readonly: true,
							col: 6
						},
						{
							label: '年龄',
							name: 'age',
							readonly: true,
							col: 6
						},
						{
							label: '所属学院',
							name: 'college',
							readonly: true,
							col: 6
						},
						{
							label: '所在班级',
							name: 'userClass',
							readonly: true,
							col: 6
						},
						{
							label: '辅导员姓名',
							name: 'instructor',
							readonly: true,
							col: 6
						},
						{
							label: '联系电话',
							name: 'phone',
							readonly: true,
							col: 6
						},
						{
							label: '电子邮箱',
							name: 'email',
							readonly: true,
							col: 6
						},
						{
							label: '咨询情况',
							name: 'consultingSituationDes',
							readonly: true,
							col: 6
						},
						{
							label: '咨询问题',
							name: 'consultingQuestionsDes',
							type: 'textarea',
							readonly: true
						},
						{
							label: '①咨询目的（请说明你想通过咨询达到的效果和目的）',
							name: 'question1',
							type: 'textarea',
							readonly: true
						},
						{
							label: '②你以前有无做过心理咨询，结果如何？',
							name: 'question2',
							type: 'textarea',
							readonly: true
						},
						{
							label: '③以前是否到心理科就诊过，诊断结果是什么？是否做过心理测评，结果如何？',
							name: 'question3',
							type: 'textarea',
							readonly: true
						}
					]
				}
				// {
				// 	type: 'submit',
				// 	skin: 'lay-form-btns',
				// 	contents: [{ type: 'reset', text: '取消', event: 'cancel' }]
				// }
			];
		}
	},
	watch: {
		dataTableUrl() {
			this.tableCount++;
		},
		formItemList: {
			handler(newValue, oldValue) {},
			deep: true
		}
	},
	created() {
		this.getPsyList();
		this.getCollegeList();
	},
	methods: {
		//表格行高
		tableRowClassName({ row, rowIndex }) {
			let styleRes = { height: '54px !important' };
			return styleRes;
		},
		inputChange(key, value) {},
		getCollegeList() {
			this.$request({
				url: interfaceUrl.collegeSelect,
				method: 'POST'
			}).then(res => {
				if (res.rCode === 0) {
					this.collegeList = res.results;
				}
			});
		},
		getPsyList() {
			this.$request({
				url: interfaceUrl.psySelect,
				// params: { type: '' },
				method: 'GET'
			}).then(res => {
				if (res.rCode === 0) {
					this.psyList = res.results;
				}
			});
		},
		//格式化表格联系方式
		formatLink(man, phone) {
			var des = '';
			if (man && phone) {
				des = man + '-' + phone;
			} else {
				if (man) {
					des = man;
				}
			}
			return des;
		},
		//操作按钮点击事件
		btnClick(res) {
			let code = res.handle.code;
			switch (code) {
				case 'view':
					this.formTitle = '查看';
					this.$request({ url: interfaceUrl.info + '/' + res.row.id, method: 'GET' }).then(res => {
						if (res.rCode === 0) {
							this.formData = res.results;
							// this.ownId = this.formData.psyId;
							this.showForm = true;
						}
					});
					break;
				case 'delete':
					// this.deleteId = res.row.id;
					// this.showDelete = true;
					break;
				default:
					break;
			}
		},
		//表单提交
		handleFormSubmit(data) {
			this.$refs.form.validate(valid => {
				if (!valid) {
					return;
				}
			});
		}
		//删除行
		// deleteRow() {
		// 	this.$request({
		// 		url: interfaceUrl.delete,
		// 		data: { id: this.deleteId },
		// 		method: 'POST'
		// 	}).then(res => {
		// 		if (res.rCode === 0) {
		// 			this.$refs.table.reload();
		// 			this.$message.success('删除成功');
		// 		} else {
		// 			this.$message.error(res.msg);
		// 		}
		// 		this.showDelete = false;
		// 	});
		// }
	}
};
</script>

<style scoped lang="scss">
@import '../../../assets/style/style.scss';

.content {
	width: 100%;
	height: 100%;
}

.el-dialog__body {
	.content-warning-Btn {
		float: right;
		margin: 10px 0px;
	}
	.btn-box {
		margin-top: 20px;
		display: flex;
		justify-content: flex-end;

		.btn {
			padding: 5px 10px;
			color: #666;
			border: 1px solid #eee;
			cursor: pointer;
			margin-right: 5px;
			border-radius: 4px;
		}
	}
}
</style>