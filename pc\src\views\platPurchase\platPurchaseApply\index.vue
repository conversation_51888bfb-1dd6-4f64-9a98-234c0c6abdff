<template>
  <div class="content">
    <es-data-table ref="table" row-key="id" :data="tableData" full :thead="thead" :toolbar="toolbar" :url="dataTableUrl"
      :page="pageOption" :param="params" @btnClick="btnClick"></es-data-table>
    <!-- 新增编辑 -->
    <es-dialog :title="formTitle" :visible.sync="showForm" width="1300px" :drag="false" :close-on-click-modal="false"
      :destroy-on-close="true">
      <es-form v-if="showForm" ref="formRef" :model="formData" :contents="formItemList" table
        @submit="showApprover = true" label-width='180' />
    </es-dialog>

    <es-dialog title="选择审批人" :visible.sync="showApprover" width="600px" height="305px" :drag="false"
      :close-on-click-modal="false" :destroy-on-close="true">
      <el-form label-width="100px" :model="ApproverData" style="margin-top: 10px;">
        <el-form-item label="当前节点">
          <el-input v-model="ApproverData.nowStep" disabled></el-input>
        </el-form-item>
        <el-form-item label="下步节点">
          <el-input v-model="ApproverData.nextStep" disabled></el-input>
        </el-form-item>
        <el-form-item label="下步办理人">
          <el-select v-model="ApproverData.Approver" filterable placeholder="请选择" @change="changeApprover">
            <el-option v-for="(item, index) in ApproverList" :key="item.userid" :label="item.userName"
              :value="item.userid">
            </el-option>
          </el-select>
        </el-form-item>
        <el-form-item style="display: flex; justify-content: right;">
          <es-button type="primary" @click="handleFormSubmit">确定</es-button>
          <es-button type="primary" @click="showApprover = false">取消</es-button>
        </el-form-item>
      </el-form>
    </es-dialog>


    <!-- 查看 -->
    <es-dialog title="查看" :visible.sync="showView" width="1300px" :drag="false" :close-on-click-modal="false"
      :destroy-on-close="true" @close="ResetView">
      <es-tabs v-model="activeName" v-if="showView">
        <el-tab-pane label="表单信息" name="first">
          <es-form ref="viewRef" :model="viewData" label-position='top' :contents="viewItemList" table readonly
            label-width='200' @reset="showView = false" :submit="false" />
        </el-tab-pane>
        <el-tab-pane label="审批意见" name="second">
          <es-data-table :data="auditOpinionList" :thead="auditOpinionListThead" stripe>
          </es-data-table>
        </el-tab-pane>
        <el-tab-pane label="补充资料信息" name="third"
          v-if="NowAuditNode === '3' || NowAuditNode === '5.1' || NowAuditNode === '3.2'">
          <div class="Pane-BodyBox">
            <es-form ref="formRef" :model="item" :contents="viewBcFormItemList[index]" table readonly
              v-for="item, index in bcFormDataList" :key="item.id" />
          </div>
        </el-tab-pane>
      </es-tabs>
    </es-dialog>

    <!-- 补充 -->
    <es-dialog :title="BcTitle" :visible.sync="showBcForm" width="800px" :drag="false" :close-on-click-modal="false"
      :destroy-on-close="true">
      <es-form v-if="showBcForm" ref="formRef" :model="bcFormData" :contents="bcFormItemList" table
        @submit="handleBcFormSubmit" @reset="showBcForm = false" />
    </es-dialog>


  </div>
</template>
<script>
import platPurchaseApplyApi from "@/http/plat/platPurchaseApply";
export default {
  name: "platPurchaseApply",
  data() {
    return {
      activeName: 'first',
      deptList: [],
      tableData: [],
      pageOption: {
        layout: 'total, sizes, prev, pager, next, jumper',
        pageSize: 10,
        position: 'center',
        current: 1,
        pageNum: 1
      },
      params: {
        orderBy: 't1.create_time',
        asc: 'false'
      },
      BcTitle: '',
      ViewAuditNodeList: {
        "0": '暂存',
        "0.2": '申请驳回',
        "1": '学院审核',
        "2": '处长审核',
        "3": '资料审核',
        "3.3": '完善资料',
        "4": '国资审核 ',
        "5": '审核通过',
        "5.1": '已办结'
      },
      auditNodeList: {
        "0": '暂存',
        "0.2": '申请驳回',
        "1": '待学院审核',
        "2": '待处长审核',
        "3": '资料审核',
        "3.3": '完善资料',
        "3.2": '补充资料驳回',
        "4": '待国资审核 ',
        "5": '审核通过',
        "5.1": '已办结'
      },
      NowAuditNode: '',
      ApproverList: [],
      showApprover: false,
      RowsID: null,
      bcFormDataList: [],
      viewBcFormItemList: [],
      showView: false,
      ApproverData: {
        nowStep: '申请验收',
        nextStep: '二级学院/部门审批',
      },
      viewData: {},
      formTitle: '',
      showForm: false,
      formData: {},
      showBcForm: false,
      auditOpinionList: [],
      showBcFormData: false,
      bcFormData: {},
      extractionTypeList: [
        { label: '专家抽取表', value: "1" },
        { label: '货物验收单', value: "2" },
        { label: '验收报告', value: "3" },
        { label: '其他资料', value: "4" },
      ],
    }
  },
  watch: {
    showView(val) {
      if (!val) {
        this.bcFormDataList = [];
        this.viewBcFormItemList = [];
      }
    },
    showForm(val) {
      if (!val) {
        this.formData = {};
      }
    },
    showBcForm(val) {
      if (!val) {
        this.bcFormData = {};
      }
    },
  },
  computed: {
    auditOpinionListThead() {
      return [
        {
          align: 'center',
          title: '审核节点',
          field: 'auditNodeName'
        },
        {
          align: 'center',
          title: '审核人',
          field: 'userName'
        },
        {
          align: 'center',
          title: '审核状态',
          field: 'auditStatus'
        },
        {
          align: 'center',
          title: '审批时间',
          field: 'updateTime'
        },
        {
          title: '审批意见',
          align: 'center',
          field: 'auditExplain'
        },
      ]
    },
    toolbar() {
      return [
        {
          type: 'button',
          contents: [
            {
              text: '新增',
              code: 'add',
              type: 'primary'
            }
          ]
        },
        {
          type: 'search',
          reset: true,
          contents: [
            {
              type: 'text',
              label: '项目名称',
              name: 'projectName',
              placeholder: '请输入项目名称',
              col: 3
            },
          ]
        }
      ]
    },
    thead() {
      return [
        {
          title: '项目名称',
          field: 'projectName',
          align: 'center'
        },
        {
          title: '项目负责人姓名',
          field: 'predictAcceptanceOfficial',
          align: 'center'
        },
        {
          title: '预验收时间',
          field: 'predictAcceptanceDate',
          align: 'center'
        },
        {
          title: '采购部门',
          field: 'purchaseDepartment',
          align: 'center',
          render: (h, { row }) => {
            let { purchaseDepartment } = row
            if (purchaseDepartment) {
              return h('span', null, this.deptList.find(item => item.value == purchaseDepartment).label);
            }
          }
        },
        {
          title: '预验收部门',
          field: 'predictAcceptanceDepartment',
          align: 'center',
          render: (h, { row }) => {
            let { predictAcceptanceDepartment } = row
            if (predictAcceptanceDepartment) {
              return h('span', null, this.deptList.find(item => item.value == predictAcceptanceDepartment).label);
            }
          }
        },
        {
          title: '状态',
          field: 'auditNode',
          align: 'center',
          width: 200,
          render: (h, { row }) => {
            let { auditNode } = row
            let status;
            if (auditNode === '5' || auditNode === '5.1') {
              status = 'success';
            } else if (['0', '1', '2', '3', '4', '3.3'].includes(auditNode)) {
              status = 'warning';
            } else {
              status = 'danger';
            }
            return h('el-tag', { props: { type: status } }, this.auditNodeList[auditNode]);
          }
        },
        {
          title: '操作',
          type: 'handle',
          width: 180,
          template: '',
          events: [
            {
              code: 'view',
              text: '查看',
              rules: rows => {
                return !(['0'].includes(rows.auditNode));
              }
            },
            {
              code: 'edit',
              text: '编辑',
              rules: rows => {
                return ['0', '0.2'].includes(rows.auditNode);
              }
            },
            // {
            //   code: 'del',
            //   text: '删除',
            //   rules: rows => {
            //     return rows.status === '1';
            //   }
            // },
            {
              code: 'bcEdit',
              text: '补充资料',
              rules: rows => {
                return ['5', '3.3'].includes(rows.auditNode);
              }
            },
            {
              code: 'bcUpDate',
              text: '编辑补充资料',
              rules: rows => {
                return ['3.2'].includes(rows.auditNode);
              }
            },
          ]
        },

      ]
    },
    viewItemList() {
      return [
        {
          name: 'projectName',
          label: '项目名称',
          value: '',
          col: 6,
          placeholder: '请输入项目名称',
          rules: {
            required: true,
            message: '请输入项目名称'
          }
        },
        {
          name: 'projectNumber',
          label: '项目编号',
          value: '',
          col: 6,
          placeholder: '请输入项目编号',
          rules: {
            required: true,
            message: '请输入项目编号'
          }
        },
        {
          name: 'bidSupplierName',
          label: '中标供应商名称',
          value: '',
          col: 6,
          placeholder: '请输入中标供应商名称',
          rules: {
            required: true,
            message: '请输入中标供应商名称'
          }

        },
        {
          name: 'contractAmount',
          label: '合同金额',
          value: '',
          col: 6,
          placeholder: '请输入合同金额',
          rules: {
            required: true,
            message: '请输入合同金额'
          }
        },
        {
          name: 'purchaseDepartment',
          type: 'select',
          filterable: true,
          'value-key': 'value',
          'label-key': 'label',
          clearable: true,
          label: '采购部门',
          value: '',
          col: 6,
          placeholder: '请选择采购部门',
          rules: {
            required: true,
            message: '请选择采购部门'
          },
          data: this.deptList

        },
        {
          name: 'purchaseContent',
          label: '采购内容',
          value: '',
          col: 6,
          placeholder: '请填写采购内容',
          rules: {
            required: true,
            message: '请填写采购内容'
          }
        },
        {
          name: 'predictAcceptanceDepartment',
          type: 'select',
          filterable: true,
          'value-key': 'value',
          'label-key': 'label',
          clearable: true,
          label: '预验收部门',
          value: '',
          col: 6,
          placeholder: '请输入项目负责人姓名',
          rules: {
            required: true,
            message: '请输入项目负责人姓名'
          },
          data: this.deptList

        },
        {
          name: 'predictAcceptancePersonnel',
          label: '预验收人员(专家)',
          value: '',
          col: 6,
          placeholder: '请输入预验收人员(专家)姓名',
          rules: {
            required: true,
            message: '请输入预验收人员(专家)姓名'
          }
        },
        {
          name: 'predictAcceptanceDate',
          placeholder: '请选择预验收时间',
          col: 6,
          label: '预验收时间',
          valueFormat: 'yyyy-mm-dd',
          type: 'date',
        },
        {
          name: 'predictAcceptanceOfficial',
          label: '预验负责人(项目负责人)',
          value: '',
          col: 6,
          placeholder: '请输入预验负责人(项目负责人)姓名',
          rules: {
            required: true,
            message: '请输入预验负责人(项目负责人)姓名'
          }
        },
        {
          type: 'textarea',
          name: 'applyAcceptanceReason',
          label: '申请验收理由',
          value: '',
          autosize: { minRows: 2, maxRows: 4 },
          col: 12,
          placeholder: '请输入申请验收理由',
          rules: {
            required: true,
            message: '请输申请验收理由'
          }
        },
        // {
        //   type: 'textarea',
        //   name: 'acceptanceVerdict',
        //   label: '验收结论',
        //   value: '',
        //   autosize: { minRows: 2, maxRows: 4 },
        //   col: 12,
        //   placeholder: '请输入验收结论',
        //   rules: {
        //     required: true,
        //     message: '请输入验收结论'
        //   }
        // },
        {
          type: 'remark',
          label: '备注',
          name: 'remark',
          autosize: { minRows: 2, maxRows: 4 },
          placeholder: '请输入备注',
          col: 12
        },
        {
          name: 'applyFile',
          label: '附件',
          type: 'attachment',
          value: '',
          col: 12,
          preview: true,
          code: 'plat_purchase_apply_file',
          ownId: this.viewData.id,
          rules: {
            required: true,
            message: '请上传附件'
          },
        }
      ]
    },
    formItemList() {
      return [
        {
          hide: !(this.formData.status && this.formData.status === '4'),
          type: 'textarea',
          placeholder: '无',
          autosize: { minRows: 2, maxRows: 4 },
          label: '审核驳回备注',
          name: 'verifyRemark',
          col: 12,
          readonly: true
        },
        {
          name: 'projectName',
          label: '项目名称',
          col: 6,
          placeholder: '请输入项目名称',
          rules: {
            required: true,
            message: '请输入项目名称'
          }
        },
        {
          name: 'projectNumber',
          label: '项目编号',
          col: 6,
          placeholder: '请输入项目编号',
          rules: {
            required: true,
            message: '请输入项目编号'
          }
        },
        {
          name: 'bidSupplierName',
          label: '中标供应商名称',
          col: 6,
          placeholder: '请输入中标供应商名称',
          rules: {
            required: true,
            message: '请输入中标供应商名称'
          }

        },
        {
          name: 'contractAmount',
          label: '合同金额',
          col: 6,
          placeholder: '请输入合同金额',
          rules: {
            required: true,
            message: '请输入合同金额'
          }
        },
        {
          name: 'purchaseDepartment',
          type: 'select',
          filterable: true,
          'value-key': 'value',
          'label-key': 'label',
          clearable: true,
          label: '采购部门',
          col: 6,
          placeholder: '请选择采购部门',
          rules: {
            required: true,
            message: '请选择采购部门'
          },
          data: this.deptList

        },
        {
          name: 'purchaseContent',
          label: '采购内容',
          value: '',
          col: 6,
          placeholder: '请填写采购内容',
          rules: {
            required: true,
            message: '请填写采购内容'
          }
        },
        {
          name: 'predictAcceptanceDepartment',
          type: 'select',
          filterable: true,
          'value-key': 'value',
          'label-key': 'label',
          clearable: true,
          label: '预验收部门',
          col: 6,
          placeholder: '请选择预验收部门',
          rules: {
            required: true,
            message: '请选择预验收部门'
          },
          data: this.deptList

        },
        {
          name: 'predictAcceptancePersonnel',
          label: '预验收人员(专家)',
          col: 6,
          placeholder: '请输入预验收人员(专家)姓名',
          rules: {
            required: true,
            message: '请输入预验收人员(专家)姓名'
          }
        },
        {
          name: 'predictAcceptanceDate',
          placeholder: '请选择预验收时间',
          col: 6,
          label: '预验收时间',
          valueFormat: 'yyyy-mm-dd',
          type: 'date',
        },
        {
          name: 'predictAcceptanceOfficial',
          label: '预验负责人(项目负责人)',
          col: 6,
          placeholder: '请输入预验负责人(项目负责人)姓名',
          rules: {
            required: true,
            message: '请输入预验负责人(项目负责人)姓名'
          }
        },
        {
          type: 'textarea',
          name: 'applyAcceptanceReason',
          label: '申请验收理由',
          autosize: { minRows: 2, maxRows: 4 },
          col: 12,
          placeholder: '请输入申请验收理由',
          rules: {
            required: true,
            message: '请输申请验收理由'
          }
        },
        // {
        //   type: 'textarea',
        //   name: 'acceptanceVerdict',
        //   label: '验收结论',
        //   value: '',
        //   autosize: { minRows: 2, maxRows: 4 },
        //   col: 12,
        //   placeholder: '请输入验收结论',
        //   rules: {
        //     required: true,
        //     message: '请输入验收结论'
        //   }
        // },
        {
          type: 'remark',
          label: '备注',
          name: 'remark',
          autosize: { minRows: 2, maxRows: 4 },
          placeholder: '请输入备注',
          col: 12
        },
        {
          name: 'applyFile',
          label: '附件',
          type: 'attachment',
          col: 12,
          preview: true,
          code: 'plat_purchase_apply_file',
          ownId: this.formData.id,
          rules: {
            required: true,
            message: '请上传附件'
          },

        },
        {
          type: 'submit',
          skin: 'lay-form-btns',
          contents: [
            {
              type: 'primary',
              text: '提交',
              event: 'submit'
            },
            {
              type: 'primary',
              text: '暂存',
              event: () => {
                this.HoldForm()
              }
            },
            {
              type: 'primary',
              text: '取消',
              event: () => {
                this.showForm = false
              }
            },
          ]
        }

      ]
    },
    bcFormItemList() {
      return [
        {
          name: 'extractionType',
          hide: this.BcTitle === '查看补充资料',
          type: 'select',
          filterable: true,
          clearable: true,
          label: '补充资料类型',
          multiple: true,
          col: 12,
          placeholder: '请选择补充类型',
          rules: {
            required: true,
            message: '请选择补充资料类型'
          },
          data: this.extractionTypeList,
        },

        {
          name: 'extractionTypeStr',
          hide: !(this.BcTitle === '查看补充资料'),
          label: '补充资料类型',
          multiple: true,
          col: 12,
          placeholder: '请选择补充类型',
          rules: {
            required: true,
            message: '请选择补充资料类型'
          },
        },

        {
          hide: !Array.isArray(this.bcFormData.extractionType) || !this.bcFormData.extractionType.includes('1'),
          name: 'zjFile',
          label: '专家抽取表',
          type: 'attachment',
          col: 12,
          preview: true,
          code: 'plat_purchase_apply_bc_zj_File',
          ownId: this.RowsID,
          rules: {
            required: true,
            message: '请上传专家抽取表'
          },
        },
        {
          hide: !Array.isArray(this.bcFormData.extractionType) || !this.bcFormData.extractionType.includes('1'),
          name: 'acceptanceLeaguer',
          label: '验收组成员',
          col: 12,
          placeholder: '请填写验收组成员',
          rules: {
            required: true,
            message: '请填写验收组成员'
          }
        },
        {
          hide: !Array.isArray(this.bcFormData.extractionType) || !this.bcFormData.extractionType.includes('2'),
          name: 'hwFile',
          label: '货物验收单',
          type: 'attachment',
          col: 12,
          preview: true,
          code: 'plat_purchase_apply_bc_hwfile',
          ownId: this.RowsID,
          rules: {
            required: true,
            message: '请上传货物验收单'
          },
        }, {
          hide: !Array.isArray(this.bcFormData.extractionType) || !this.bcFormData.extractionType.includes('3'),
          name: 'ysFile',
          label: '验收报告',
          type: 'attachment',
          col: 12,
          preview: true,
          code: 'plat_purchase_apply_bc_ysfile',
          ownId: this.RowsID,
          rules: {
            required: true,
            message: '请上传验收报告'
          },
        }, {
          hide: !Array.isArray(this.bcFormData.extractionType) || !this.bcFormData.extractionType.includes('3'),
          valueFormat: 'yyyy-mm-dd',
          type: 'date',
          name: 'acceptanceTime',
          label: '验收时间',
          col: 12,
          placeholder: '请选择验收时间',
          rules: {
            required: true,
            message: '请选择验收时间'
          }
        }, {
          hide: !Array.isArray(this.bcFormData.extractionType) || !this.bcFormData.extractionType.includes('3'),
          type: 'textarea',
          col: 12,
          label: '验收结论',
          name: 'acceptanceConclusion',
          placeholder: '请填写验收结论',
          rules: {
            required: true,
            message: '请填写验收结论'
          }
        }, {
          hide: !Array.isArray(this.bcFormData.extractionType) || !this.bcFormData.extractionType.includes('4'),
          name: 'qtFile',
          label: '其他资料',
          type: 'attachment',
          col: 12,
          preview: true,
          code: 'plat_purchase_apply_bc_qtfile',
          ownId: this.RowsID,
          rules: {
            required: true,
            message: '请上传其他资料'
          },
        },
      ]
    },
    dataTableUrl() {
      return platPurchaseApplyApi.myListJson
    }
  },
  mounted() {
    this.getSelectList();
    this.getApprover()
  },
  methods: {
    getSelectList() {
      this.$request({
        url: platPurchaseApplyApi.getOriList,
        method: 'POST'
      }).then(result => {
        this.deptList = result?.results || [];
      });
    },
    /**
     * headle按钮事件
     */
    async btnClick(res) {
      let code = res.handle.code;

      if (res.row) {
        let { id, auditNode } = res.row
        this.RowsID = id
        this.NowAuditNode = auditNode
      }
      switch (code) {
        case 'view':
          // 查看
          this.BcTitle = '查看补充资料'
          let ids = res.row.id
          this.ResetView()
          this.getViewData(ids)
          break;
        case 'add':
          // 新增
          this.formTitle = '新增';
          this.formData = {
            id: this.$uuidv4()
          };
          this.showForm = true;
          break;
        case 'edit':
          // 编辑
          this.formTitle = '编辑';
          this.$request({
            url: platPurchaseApplyApi.info,
            method: 'get',
            params: { id: res.row.id }
          }).then(res => {
            this.formData = Object.assign({}, res.results || {});
            this.showForm = true;
          });
          break;
        case 'bcEdit':
          // 补充资料
          this.BcTitle = '补充资料'
          this.bcFormData = {};
          this.bcFormData = Object.assign({}, {
            purchaseApplyId: res.row.id,
          });
          if (res.row.auditNode == '3.3') {
            this.getExtractionTypeList(res.row.id, 'getextractionTypeList')
          } else {
            this.extractionTypeList = [
              { label: '专家抽取表', value: "1" },
              { label: '货物验收单', value: "2" },
              { label: '验收报告', value: "3" },
              { label: '其他资料', value: "4" },
            ]
          }
          this.showBcForm = true;
          break;
        case 'bcUpDate':
          this.bcFormData = {};
          this.BcTitle = '修改补充资料'
          this.getExtractionTypeList(res.row.id, 'getinfo')
          break
        case 'del':
          // 删除
          this.onDeal(res.row.id);
          break;
        default:
          break;
      }
    },
    // 重置查看弹窗数据
    ResetView() {
      this.auditOpinionList = []
      this.bcFormData = {}
      this.viewData = {}
      this.activeName = 'first'
    },
    /**
     * 删除
     */
    onDeal(id) {
      this.$confirm(`您确定要删除数据吗？`, '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      })
        .then(() => {
          const loading = this.$loading();
          this.$request({
            url: platPurchaseApplyApi.removeById,
            data: { id },
            method: 'POST'
          }).then(res => {
            loading.close();
            if (res.rCode == 0) {
              this.$message.success('删除成功');
              this.$refs.table.reload();
            } else {
              this.$message.error(res.msg);
            }
          });
        })
        .catch(err => { });
    },

    // 审核人赋值
    changeApprover(id) {
      const { userName, userid, phoneno } = this.ApproverList.find(item => item.userid === id);
      this.formData.auditUserId = userid
      this.formData.auditUserName = userName
      this.formData.auditUserPhone = phoneno
    },

    // 获取下一步审核人
    getApprover() {
      this.$request({
        url: `${platPurchaseApplyApi.getPresidentlistJson}?auditStatus=1`,
        method: 'GET',
        format: false
      }).then(res => {
        if (res.rCode == 0) {
          this.ApproverList = res.results
        } else {
          this.$message.error(res.msg);
        }
      });
    },

    // 暂存申请表单
    async HoldForm() {
      let formData = { ...this.formData };
      formData.auditNode = '0';
      await this.submitForm(formData);
    },

    // 提交申请表单
    async handleFormSubmit() {
      let formData = { ...this.formData };
      formData.auditNode = 1;
      // this.processFormData(formData);
      await this.submitForm(formData);
    },

    // 处理表单数据
    // processFormData(formData) {
    //   if (!!formData.deptCode) {
    //     formData.deptName = this.deptList.find(item => item.value === formData.deptCode)?.label || '';
    //   } else {
    //     formData.deptName = '';
    //   }
    // },

    // 表单请求
    async submitForm(formData) {
      const url = this.formTitle === '新增' ? platPurchaseApplyApi.save : platPurchaseApplyApi.update;
      const response = await this.$request({
        url,
        data: formData,
        method: 'POST',
        format: false
      });
      if (response.rCode === 0) {
        this.$message.success('操作成功');
        this.showApprover = false;
        this.showForm = false;
        this.$refs.table.reload();
      } else {
        this.$message.error(response.msg);
      }
    },

    // 提交补充资料
    handleBcFormSubmit() {
      const param = { ...this.bcFormData }
      param.extractionType = param.extractionType.join(',')
      this.$request({
        url: this.NowAuditNode == '3.2' ? platPurchaseApplyApi.platPurchaseReplenishUpdate : platPurchaseApplyApi.replenish,
        // url: platPurchaseApplyApi.replenish,
        data: param,
        method: 'POST',
        format: false
      }).then(res => {
        if (res.rCode == 0) {
          this.$message.success('操作成功');
          this.showBcForm = false;
          this.$refs.table.reload();
        } else {
          this.$message.error(res.msg);
        }
      });
    },

    getExtractionTypeList(id, type) {
      this.$request({
        url: platPurchaseApplyApi.purchaseAuditList,
        method: 'get',
        params: { applyId: id }
      }).then(response => {
        const data = response.results || [];
        if (type === 'getinfo') {
          const latestRecord = data.reduce((latest, current) => {
            return new Date(current.updateTime) > new Date(latest.updateTime) ? current : latest;
          }, data[0]);
          if (latestRecord) {
            latestRecord.extractionType = latestRecord.extractionType.split(',');
            latestRecord.extractionTypeStr = latestRecord.extractionType
              .map(item => this.extractionTypeList.find(ele => ele.value === item)?.label)
              .filter(label => label)
              .join('  ||  ');
            this.bcFormData = latestRecord;
            this.showBcForm = true;
          }
        } else if (type === 'getextractionTypeList') {
          const extractionTypes = data.flatMap(item => item.extractionType.split(','));
          this.extractionTypeList = this.extractionTypeList.filter(item => !extractionTypes.includes(item.value));
        }
      }).catch(error => {
        console.error('Error fetching data:', error);
      });
    },

    // 获取查看信息
    async getViewData(ids) {
      const [infoRes, auditRes, bcData] = await Promise.all([
        this.$request({
          url: platPurchaseApplyApi.info,
          method: 'get',
          params: { id: ids }
        }),
        this.$request({
          url: platPurchaseApplyApi.platPurchaseAuditlist,
          method: 'get',
          params: { applyId: ids }
        }),
        this.$request({
          url: platPurchaseApplyApi.purchaseAuditList,
          method: 'get',
          params: { applyId: ids }
        })
      ]);
      // 处理 infoRes
      this.viewData = infoRes.results || {};
      // 处理 auditRes
      const data = auditRes.results;
      const auditOpinionList = data.map(element => {
        const { auditExplain, userName, auditStatus, updateTime, auditNode } = element;
        return {
          auditNodeName: this.ViewAuditNodeList[auditNode],
          auditExplain,
          userName,
          updateTime,
          auditStatus: auditStatus === '1' ? '通过' : '不通过'
        };
      });
      this.auditOpinionList = auditOpinionList;
      // 处理 bcData
      bcData.results.forEach(items => {
        items.extractionType = items.extractionType.split(',');
        let str = '';
        items.extractionType.forEach((item, index, array) => {
          str += this.extractionTypeList.find(ele => ele.value === item).label;
          if (index < array.length - 1) {
            str += '  ||  ';
          }
        });
        items.extractionTypeStr = str
      })
      this.bcFormDataList = bcData.results
      // 生成各个选项表格结构
      this.renderBcFormItemList(bcData.results)
      this.showView = true;
    },

    renderBcFormItemList(list) {
      const regionList = this.bcFormItemList;
      const finalList = [];
      const elementMapping = {
        zjFile: '1',
        acceptanceLeaguer: '1',
        hwFile: '2',
        ysFile: '3',
        acceptanceTime: '3',
        acceptanceConclusion: '3',
        qtFile: '4'
      };
      list.forEach(items => {
        const typeList = items.extractionType;
        let itemsList = JSON.parse(JSON.stringify(regionList));
        itemsList = itemsList.filter(element => element.name !== 'extractionType');
        itemsList.forEach(element => {
          const type = elementMapping[element.name];
          if (type) {
            element.hide = !typeList.includes(type);
          }
        });
        finalList.push(itemsList);
      });
      this.viewBcFormItemList = finalList;
    },

  }
}
</script>

<style scoped lang="scss">
@import '../../../assets/style/style.scss';

.content {
  width: 100%;
  height: 100%;

  .View-Bodys {
    display: flex;
  }

}

.Pane-BodyBox {
  width: 100%;
  height: 100%;
  overflow: scroll;
}
</style>