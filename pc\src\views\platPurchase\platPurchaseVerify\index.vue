<template>
  <div class="content">
    <es-data-table ref="table" row-key="id" :data="tableData" full :thead="thead" :toolbar="toolbar" :url="dataTableUrl"
      :page="pageOption" :param="params" @btnClick="btnClick"></es-data-table>
   
      <!-- 查看 -->
    <es-dialog title="查看" :visible.sync="showView" width="1300px" :drag="false" :close-on-click-modal="false"
      :destroy-on-close="true" @close="ResetView">
      <es-tabs v-model="activeName" v-if="showView">
        <el-tab-pane label="表单信息" name="first">
          <es-form ref="viewRef" :model="viewData" :contents="viewItemList" table readonly @reset="showView = false"
            label-width='200' :submit="false" />
        </el-tab-pane>
        <el-tab-pane label="补充资料信息" name="third"
          v-if="NowAuditNode === '3' || NowAuditNode === '5.1' || NowAuditNode === '3.2'">
          <div class="Pane-BodyBox">
            <es-form ref="formRef" :model="item" :contents="viewBcFormItemList[index]" table readonly
              v-for="item, index in bcFormDataList" :key="item.id" />
          </div>
        </el-tab-pane>
      </es-tabs>
    </es-dialog>

    <es-dialog title="审核" :visible.sync="showForm" width="1500px" :drag="false" :close-on-click-modal="false"
      :destroy-on-close="true">
      <div class="Form-Bodys">

        <div class="leftContent">
          <es-form ref="formRef" v-if="NowAuditNode == '3'" :model="bcFormData" :contents="bcFormItemList" table
            readonly />

          <es-form ref="viewRef" v-else :model="viewData" :contents="viewItemList" table readonly
            @reset="showView = false" label-width='200' :submit="false" />
        </div>
        <div class="rightContnet">
          <el-form label-width="120px" style="margin-top: 10px; ">
            <el-form-item label="当前节点">
              <el-input v-model="auditNodeList[NowAuditNode]" disabled></el-input>
            </el-form-item>
            <el-form-item label="审批状态">
              <el-radio-group v-model="formData.auditStatus">
                <el-radio label="1">通过</el-radio>
                <el-radio label="2">驳回</el-radio>
              </el-radio-group>
            </el-form-item>
            <el-form-item label="审批意见">
              <el-input type="textarea" :autosize="{ minRows: 2, maxRows: 4 }" placeholder="请输入内容"
                v-model="formData.auditExplain">
              </el-input>
            </el-form-item>
            <el-form-item label="下步办理部门" v-if="NowAuditNode == 2 && formData.auditStatus == '1'">
              <el-select v-model="ApproverData.nextDept" placeholder="请选择" @change="changeDept">
                <el-option label="部门/学院自行验收" :value="0">
                </el-option>
                <el-option label="国资处验收" :value="1">
                </el-option>
              </el-select>
            </el-form-item>
            <el-form-item label="下步办理人"
              v-if="(ApproverData.nextDept == 1 || NowAuditNode == 1) && formData.auditStatus == '1'">
              <el-select v-model="ApproverData.Approver" filterable placeholder="请选择" @change="changeApprover">
                <el-option v-for="(item, index) in ApproverList" :key="item.userid" :label="item.userName"
                  :value="item.userid">
                </el-option>
              </el-select>
            </el-form-item>
            <el-form-item style="display: flex; justify-content: right;">
              <es-button type="primary" @click="handleFormSubmit">确定</es-button>
              <es-button type="primary" @click="showForm = false">取消</es-button>
            </el-form-item>
          </el-form>
        </div>


      </div>

    </es-dialog>

  </div>
</template>
<script>
import platPurchaseApplyApi from "@/http/plat/platPurchaseApply";
export default {
  name: "platPurchaseVerify",
  data() {
    return {
      activeName: 'first',
      deptList: [],
      tableData: [],
      pageOption: {
        layout: 'total, sizes, prev, pager, next, jumper',
        pageSize: 10,
        position: 'center',
        current: 1,
        pageNum: 1
      },
      params: {
        orderBy: 't1.update_time',
        asc: 'false'
      },
      showView: false,
      viewData: {},
      subType: '审核',
      showForm: false,
      showBcForm: false,
      formData: {},
      bcFormData: {},
      RowsID: null,
      bcFormDataList: [],
      viewBcFormItemList: [],
      NowAuditNode: '',
      ApproverData: {},
      ApproverList: [],
      auditNodeList: {
        "0": '暂存',
        "0.2": '申请驳回',
        "1": '待学院审核',
        "2": '待处长审核',
        "3": '资料审核',
        "3.3": '完善资料',
        "3.2": '补充资料驳回',
        "4": '待国资审核 ',
        "5": '审核通过',
        "5.1": '已办结'
      },
      extractionTypeList: [
        { label: '专家抽取表', value: "1" },
        { label: '货物验收单', value: "2" },
        { label: '验收报告', value: "3" },
        { label: '其他资料', value: "4" },
      ],

    }
  },
  watch: {
    showForm(val) {
      if (!val) {
        this.formData = {};
        this.ApproverData = {}
      }
    },
    showView(val) {
      if (!val) {
        this.bcFormDataList = [];
        this.viewBcFormItemList = [];
      }
    },
  },
  computed: {
    toolbar() {
      return [
        {
          type: 'search',
          reset: true,
          contents: [
            {
              type: 'text',
              label: '项目名称',
              name: 'projectName',
              placeholder: '请输入项目名称',
              col: 3
            },
          ]
        }
      ]
    },
    thead() {
      return [
        {
          title: '项目名称',
          field: 'projectName',
          align: 'center'
        },
        {
          title: '项目负责人姓名',
          field: 'predictAcceptanceOfficial',
          align: 'center'
        },
        {
          title: '预验收时间',
          field: 'predictAcceptanceDate',
          align: 'center'
        },
        {
          title: '采购部门',
          field: 'purchaseDepartment',
          align: 'center',
          render: (h, { row }) => {
            let { purchaseDepartment } = row
            if (purchaseDepartment) {
              return h('span', null, this.deptList.find(item => item.value == purchaseDepartment).label);
            }
          }
        },
        {
          title: '预验收部门',
          field: 'predictAcceptanceDepartment',
          align: 'center',
          render: (h, { row }) => {
            let { predictAcceptanceDepartment } = row
            if (predictAcceptanceDepartment) {
              return h('span', null, this.deptList.find(item => item.value == predictAcceptanceDepartment).label);
            }

          }
        },
        {
          title: '操作',
          type: 'handle',
          width: 150,
          template: '',
          events: [
            {
              code: 'view',
              text: '查看'
            },
            {
              code: 'verify',
              text: '审核',

            },

            {
              code: 'conclude',
              text: '办结',
              rules: rows => {
                return ['3'].includes(rows.auditNode);
              },
            },
          ]
        }
      ]
    },
    viewItemList() {
      return [
        {
          name: 'projectName',
          label: '项目名称',
          col: 6,
          placeholder: '请输入项目名称',
          rules: {
            required: true,
            message: '请输入项目名称'
          }
        },
        {
          name: 'projectNumber',
          label: '项目编号',
          col: 6,
          placeholder: '请输入项目编号',
          rules: {
            required: true,
            message: '请输入项目编号'
          }
        },
        {
          name: 'bidSupplierName',
          label: '中标供应商名称',
          col: 6,
          placeholder: '请输入中标供应商名称',
          rules: {
            required: true,
            message: '请输入中标供应商名称'
          }

        },
        {
          name: 'contractAmount',
          label: '合同金额',
          col: 6,
          placeholder: '请输入合同金额',
          rules: {
            required: true,
            message: '请输入合同金额'
          }
        },

        {
          name: 'purchaseDepartment',
          type: 'select',
          filterable: true,
          'value-key': 'value',
          'label-key': 'label',
          clearable: true,
          label: '采购部门',
          col: 6,
          placeholder: '请选择采购部门',
          rules: {
            required: true,
            message: '请选择采购部门'
          },
          data: this.deptList

        },
        {
          name: 'purchaseContent',
          label: '采购内容',
          col: 6,
          placeholder: '请填写采购内容',
          rules: {
            required: true,
            message: '请填写采购内容'
          }
        },
        {
          name: 'predictAcceptanceDepartment',
          type: 'select',
          filterable: true,
          'value-key': 'value',
          'label-key': 'label',
          clearable: true,
          label: '预验收部门',
          value: '',
          col: 6,
          placeholder: '请输入项目负责人姓名',
          rules: {
            required: true,
            message: '请输入项目负责人姓名'
          },
          data: this.deptList

        },
        {
          name: 'predictAcceptancePersonnel',
          label: '预验收人员(专家)',
          col: 6,
          placeholder: '请输入预验收人员(专家)姓名',
          rules: {
            required: true,
            message: '请输入预验收人员(专家)姓名'
          }
        },
        {
          name: 'predictAcceptanceDate',
          placeholder: '请选择预验收时间',
          col: 6,
          label: '预验收时间',
          valueFormat: 'yyyy-mm-dd',
          type: 'date',
        },
        {
          name: 'predictAcceptanceOfficial',
          label: '预验负责人(项目负责人)',
          col: 6,
          placeholder: '请输入预验负责人(项目负责人)姓名',
          rules: {
            required: true,
            message: '请输入预验负责人(项目负责人)姓名'
          }
        },
        {
          type: 'textarea',
          name: 'applyAcceptanceReason',
          label: '申请验收理由',
          autosize: { minRows: 2, maxRows: 4 },
          col: 12,
          placeholder: '请输入申请验收理由',
          rules: {
            required: true,
            message: '请输申请验收理由'
          }
        },
        // {
        //   type: 'textarea',
        //   name: 'acceptanceVerdict',
        //   label: '验收结论',
        //   autosize: { minRows: 2, maxRows: 4 },
        //   col: 12,
        //   placeholder: '请输入验收结论',
        //   rules: {
        //     required: true,
        //     message: '请输入验收结论'
        //   }
        // },
        {
          type: 'remark',
          label: '备注',
          name: 'remark',
          placeholder: '请输入备注',
          col: 12
        },

        {
          name: 'applyFile',
          label: '附件',
          type: 'attachment',
          col: 12,
          preview: true,
          code: 'plat_purchase_apply_file',
          ownId: this.viewData.id,
          rules: {
            required: true,
            message: '请上传附件'
          },

        },

      ]
    },
    bcFormItemList() {
      return [
        {
          name: 'extractionTypeStr',
          label: '补充资料类型',
          multiple: true,
          col: 12,
          placeholder: '请选择补充类型',
          rules: {
            required: true,
            message: '请选择补充资料类型'
          },
        },
        {
          hide: !Array.isArray(this.bcFormData.extractionType) || !this.bcFormData.extractionType.includes('1'),
          name: 'zjFile',
          label: '专家抽取表',
          type: 'attachment',
          col: 12,
          preview: true,
          code: 'plat_purchase_apply_bc_zj_File',
          ownId: this.RowsID,
          rules: {
            required: true,
            message: '请上传专家抽取表'
          },
        },
        {
          hide: !Array.isArray(this.bcFormData.extractionType) || !this.bcFormData.extractionType.includes('1'),
          name: 'acceptanceLeaguer',
          label: '验收组成员',
          col: 12,
          placeholder: '请填写验收组成员',
          rules: {
            required: true,
            message: '请填写验收组成员'
          }
        },
        {
          hide: !Array.isArray(this.bcFormData.extractionType) || !this.bcFormData.extractionType.includes('2'),
          name: 'hwFile',
          label: '货物验收单',
          type: 'attachment',
          col: 12,
          preview: true,
          code: 'plat_purchase_apply_bc_hwfile',
          ownId: this.RowsID,
          rules: {
            required: true,
            message: '请上传货物验收单'
          },
        }, {
          hide: !Array.isArray(this.bcFormData.extractionType) || !this.bcFormData.extractionType.includes('3'),
          name: 'ysFile',
          label: '验收报告',
          type: 'attachment',
          col: 12,
          preview: true,
          code: 'plat_purchase_apply_bc_ysfile',
          ownId: this.RowsID,
          rules: {
            required: true,
            message: '请上传验收报告'
          },
        }, {
          hide: !Array.isArray(this.bcFormData.extractionType) || !this.bcFormData.extractionType.includes('3'),
          valueFormat: 'yyyy-mm-dd',
          type: 'date',
          name: 'acceptanceTime',
          label: '验收时间',
          col: 12,
          placeholder: '请选择验收时间',
          rules: {
            required: true,
            message: '请选择验收时间'
          }
        }, {
          hide: !Array.isArray(this.bcFormData.extractionType) || !this.bcFormData.extractionType.includes('3'),
          type: 'textarea',
          col: 12,
          label: '验收结论',
          name: 'acceptanceConclusion',
          placeholder: '请填写验收结论',
          rules: {
            required: true,
            message: '请填写验收结论'
          }
        }, {
          hide: !Array.isArray(this.bcFormData.extractionType) || !this.bcFormData.extractionType.includes('4'),
          name: 'qtFile',
          label: '其他资料',
          type: 'attachment',
          col: 12,
          preview: true,
          code: 'plat_purchase_apply_bc_qtfile',
          ownId: this.RowsID,
          rules: {
            required: true,
            message: '请上传其他资料'
          },
        },
      ]
    },
    dataTableUrl() {
      return platPurchaseApplyApi.auditListJson
    },
    nextAuditNode() {
      const { NowAuditNode, subType } = this;
      if (NowAuditNode === '1') return '2';
      if (NowAuditNode === '4') return '5';
      if (NowAuditNode === '3' && subType === '审核') return '3.3';
      if (NowAuditNode === '3' && subType === '资料办结') return '5.1';
      if (NowAuditNode === '3.3') return '3.3';
      if (this.ApproverData.nextDept === 0) return '5';
      if (this.ApproverData.nextDept === 1) return '4';
    },
  },
  mounted() {
    this.getSelectList();
  },
  methods: {
    // 获取当前登录用户信息
    loginUserInfo() {
      return JSON.parse(localStorage.getItem('loginUserInfo') || '{}');
    },
    getSelectList() {
      this.$request({
        url: platPurchaseApplyApi.getOriList,
        method: 'POST'
      }).then(result => {
        this.deptList = result?.results || [];
      });
    },
    ResetView() {
      this.bcFormData = {}
      this.viewData = {}
      this.activeName = 'first'
    },
    /**
     * headle按钮事件
     */
    async btnClick(res) {
      let code = res.handle.code;
      let { auditNode, id } = res.row;
      this.NowAuditNode = auditNode;
      this.formData.purchaseApplyId = id;
      this.RowsID = id
      switch (code) {
        case 'view':
          // 查看
          this.ResetView()
          this.getViewData(id)
          break;
        case 'verify':
          // 审核
          this.subType = '审核';
          this.ResetView();
          this.getApprover();
          this.getInfos(id)
          break;
        case "conclude":
          this.subType = '资料办结'
          this.$confirm(`您确定要办结申请吗？`, '提示', {
            confirmButtonText: '确定',
            cancelButtonText: '取消',
          }).then(res => {
            this.formData.auditStatus = '1'
            this.handleFormSubmit()
          }).catch(err => { });
          break
        default:
          break;
      }
    },

    async changeApprover(id) {
      const approver = this.ApproverList.find(item => item.userid === id);
      if (approver) {
        const { userName, userid, phoneno } = approver;
        this.formData.userId = userid;
        this.formData.userName = userName;
        this.formData.userPhone = phoneno;
      }
    },

    changeDept(val) {
      if (val == 1) {
        this.getApprover();
      }
    },

    async getApprover() {
      const auditStatus = this.nextAuditNode;
      if (auditStatus) {
        const url = `${platPurchaseApplyApi.getPresidentlistJson}?auditStatus=${auditStatus}`;
        const response = await this.$request({ url, method: 'GET', format: false });
        if (response.rCode === 0) {
          this.ApproverList = response.results;
        } else {
          this.$message.error(response.msg);
        }
      }
    },

    async handleFormSubmit() {
      let formData = { ...this.formData };
      // 设置 auditNode
      formData.auditNode = this.getAuditNode(formData.auditStatus);
      // 设置用户信息
      if (this.shouldSetUserInfo(formData)) {
        const infos = this.loginUserInfo();
        formData.userId = infos.userId;
        formData.userName = infos.name;
        formData.userPhone = infos.phone;
      }
      // 发送请求
      const response = await this.$request({
        url: platPurchaseApplyApi.platPurchaseAudit,
        data: formData,
        method: 'POST',
        format: false
      });
      // 处理响应
      if (response.rCode === 0) {
        this.$message.success('操作成功');
        this.showForm = false;
        this.ResetView();
        this.$refs.table.reload();
      } else {
        this.$message.error(response.msg);
      }
    },

    // 获取 auditNode
    getAuditNode(auditStatus) {
      const auditNodeMap = {
        '1': this.nextAuditNode,
        '2': this.NowAuditNode === '3' ? '3.2' : '0.2'
      };
      return auditNodeMap[auditStatus] || null;
    },

    // 判断是否需要设置用户信息
    shouldSetUserInfo(formData) {
      return formData.auditNode === '5' || formData.auditStatus === '2' || formData.auditStatus === '3' || formData.auditStatus === '3.3';
    },

    // 获取查看信息
    async getViewData(ids) {
      const [infoRes, bcData] = await Promise.all([
        this.$request({
          url: platPurchaseApplyApi.info,
          method: 'get',
          params: { id: ids }
        }),
        this.$request({
          url: platPurchaseApplyApi.purchaseAuditList,
          method: 'get',
          params: { applyId: ids }
        })
      ]);
      // 处理 infoRes
      this.viewData = infoRes.results || {};
      // 处理 bcData
      bcData.results.forEach(items => {
        items.extractionType = items.extractionType.split(',');
        let str = '';
        items.extractionType.forEach((item, index, array) => {
          str += this.extractionTypeList.find(ele => ele.value === item).label;
          if (index < array.length - 1) {
            str += '  ||  ';
          }
        });
        items.extractionTypeStr = str
      })
      this.bcFormDataList = bcData.results
      // 生成各个选项表格结构
      this.renderBcFormItemList(bcData.results)
      this.showView = true;
    },

    renderBcFormItemList(list) {
      const regionList = this.bcFormItemList;
      const finalList = [];
      const elementMapping = {
        zjFile: '1',
        acceptanceLeaguer: '1',
        hwFile: '2',
        ysFile: '3',
        acceptanceTime: '3',
        acceptanceConclusion: '3',
        qtFile: '4'
      };
      list.forEach(items => {
        const typeList = items.extractionType;
        let itemsList = JSON.parse(JSON.stringify(regionList));
        itemsList.forEach(element => {
          const type = elementMapping[element.name];
          if (type) {
            element.hide = !typeList.includes(type);
          }
        });
        finalList.push(itemsList);
      });
      this.viewBcFormItemList = finalList;
    },
    // 获取审核对应信息
    getInfos(ids) {
      let auditNode = this.NowAuditNode
      if (auditNode === '3') {
        this.$request({
          url: platPurchaseApplyApi.purchaseAuditList,
          method: 'get',
          params: { applyId: ids }
        }).then(res => {
          let data = res.results || []
          const latestRecord = data.reduce((latest, current) => {
            return new Date(current.updateTime) > new Date(latest.updateTime) ? current : latest;
          });
          latestRecord.extractionType = latestRecord.extractionType.split(',');
          let str = '';
          latestRecord.extractionType.forEach((item, index, array) => {
            str += this.extractionTypeList.find(ele => ele.value === item).label;
            if (index < array.length - 1) {
              str += '  ||  ';
            }
          });
          latestRecord.extractionTypeStr = str
          this.bcFormData = latestRecord
          this.showForm = true;
        })
      } else {
        this.$request({
          url: platPurchaseApplyApi.info,
          method: 'get',
          params: { id: ids }
        }).then(infoRes => {
          this.viewData = infoRes.results || {};
          this.showForm = true;
        })
      }

    },



  }
}
</script>

<style scoped lang="scss">
@import '../../../assets/style/style.scss';
.content {
  width: 100%;
  height: 100%;
}

.Pane-BodyBox {
  width: 100%;
  height: 100%;
  overflow: scroll;
}

.Form-Bodys {
  width: 100%;
  height: 100%;
  display: flex;
  justify-content: space-around;

  .leftContent {
    width: 63%;
    height: 100%;

  }

  .rightContnet {
    width: 35%;
  }

}
</style>