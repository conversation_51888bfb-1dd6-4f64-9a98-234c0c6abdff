<template>
	<div v-loading="loading" class="BacgroundBox">
		<div class="report-card">
			<div v-if="ContentTyep === 'Reading'">
				<h1>网上举报:</h1>
				<p>您的监督与举报对我们的廉政建设至关重要</p>
				<div class="report-step">
					<div id="step1" class="report-step-content">
						<ul>
							<h3>（一）根据有关规定，学院纪检监审处受理以下信访举报：</h3>
							<li>
								1.对党组织、党员违反政治纪律、组织纪律、廉洁纪律、群众纪律、工作纪律、生活纪律等党的纪律行为的检举控告。
							</li>
							<li>
								2.对监察对象（监察法规定的六类公职人员，下同）不依法履职，违反秉公用权、廉洁从政从业以及道德操守等规定，涉嫌贪污贿赂、滥用职权、玩忽职守、权力寻租、利益输送、徇私舞弊以及浪费国家资财等职务违法犯罪行为的检举控告。
							</li>
							<li>3.党员对党纪处分或者纪律检查机关所作的其他处理不服，提出的申诉。</li>
							<li>
								4.监察对象对监察机关涉及本人的处理决定不服，提出的申诉；被调查人及其近亲属对监察机关及其工作人员违反法律法规、侵害被调查人合法权益的行为，提出的申诉。
							</li>
							<li>5.对廉政建设和反腐败工作的批评建议。</li>
							<h3>（二）学院纪检监审处对反映的以下事项，不予受理：</h3>
							<li>1.已经或者依法应当通过诉讼、仲裁、行政裁决、行政复议等途径解决的；</li>
							<li>2.依照有关规定，属于其他机关或者单位职责范围的；</li>
							<li>3.仅列举出违纪或者职务违法、职务犯罪行为名称但无实质内容的。</li>
							<li>
								4.对不属于学院纪检监审处受理范围的信访问题，可直接向相关职能部门或学校信访部门反映；对执法和司法机关的处理、判决不服的，可直接向作出处理判决的上一级执法、司法机关提出申诉。
							</li>
							<h3>
								（三）检举、控告、申诉人在检举、控告、申诉活动中必须对所检举、控告、申诉的事实的真实性负责。接受调查、询问时，应如实提供情况和证据。如有诬陷、制造假证行为，必须承担纪律和法律责任。
							</h3>
							<h3>（四）提倡实名举报（请填写真实姓名、身份证号和准确联系方式等内容）。</h3>
						</ul>
					</div>

					<div class="CheckBox">
						<input v-model="checked" type="checkbox" @change="getChekboxVal" />
						<span>我已阅读以上条款</span>
					</div>

					<div class="ButtonBox">
						<div class="buttons" :class="checked ? '' : 'noChecked'" @click="reportFn(0)">
							署名举报
						</div>
						<div class="buttons" :class="checked ? '' : 'noChecked'" @click="reportFn(1)">
							匿名举报
						</div>
					</div>
				</div>
			</div>

			<div v-else class="FormBox">
				<BackButtom v-if="!id" title="返回" @click="backReading" />
				<h1>{{ formData.isAnonymity == 1 ? '匿名举报' : '署名举报' }}:</h1>

				<div class="FormModule" :style="{ 'pointer-events': readonly ? 'none' : 'auto' }">
					<div class="titleCard">举报人信息</div>
					<div class="Formes">
						<div class="items">
							<div class="Lable">
								姓名
								<span v-if="formData.isAnonymity === 0">*</span>
							</div>
							<el-input v-model="formData.reporterName" @focus="ReError('reporterName')"></el-input>
							<span v-show="errors.reporterName" class="error-msg" style="color: red">
								{{ errors.reporterName }}
							</span>
						</div>
						<div class="items">
							<div class="Lable">
								身份证号
								<span v-if="formData.isAnonymity === 0">*</span>
							</div>
							<el-input
								v-model="formData.reporterIdcard"
								@focus="ReError('reporterIdcard')"
							></el-input>
							<span v-show="errors.reporterIdcard" class="error-msg" style="color: red">
								{{ errors.reporterIdcard }}
							</span>
						</div>
						<div class="items">
							<div class="Lable">
								联系方式
								<span v-if="formData.isAnonymity === 0">*</span>
							</div>
							<el-input
								v-model="formData.reporterPhone"
								@focus="ReError('reporterPhone')"
							></el-input>
							<span v-show="errors.reporterPhone" class="error-msg" style="color: red">
								{{ errors.reporterPhone }}
							</span>
						</div>
						<div class="items">
							<div class="Lable">政治面貌</div>
							<el-select v-model="formData.reporterPolitical" style="width: 100%">
								<el-option
									v-for="item in politicsStatusList"
									:key="item.value"
									:label="item.value"
									:value="item.value"
								></el-option>
							</el-select>
							<span v-show="errors.reporterPolitical" class="error-msg" style="color: red">
								{{ errors.reporterPolitical }}
							</span>
						</div>
						<div class="items">
							<div class="Lable">
								现居住地址
								<span v-if="formData.isAnonymity === 0">*</span>
							</div>
							<el-input
								v-model="formData.reporterAddress"
								@focus="ReError('reporterAddress')"
							></el-input>
							<span v-show="errors.reporterAddress" class="error-msg" style="color: red">
								{{ errors.reporterAddress }}
							</span>
						</div>
						<div class="items">
							<div class="Lable">级别</div>
							<el-select v-model="formData.reporterRank" style="width: 100%">
								<el-option
									v-for="item in LevelList"
									:key="item.value"
									:label="item.label"
									:value="item.value"
								></el-option>
							</el-select>
							<span v-show="errors.reporterRank" class="error-msg" style="color: red">
								{{ errors.reporterRank }}
							</span>
						</div>
					</div>
				</div>

				<div class="FormModule" :style="{ 'pointer-events': readonly ? 'none' : 'auto' }">
					<div class="titleCard">被举报人（单位）信息</div>
					<div class="Formes">
						<div class="items">
							<div class="Lable">
								被举报人
								<span>*</span>
							</div>
							<el-input
								v-model="formData.beReporterName"
								@focus="ReError('beReporterName')"
							></el-input>
							<span v-show="errors.beReporterName" class="error-msg" style="color: red">
								{{ errors.beReporterName }}
							</span>
						</div>
						<div class="items">
							<div class="Lable">
								单位
								<span>*</span>
							</div>
							<el-input
								v-model="formData.beReporterDepartment"
								@focus="ReError('beReporterDepartment')"
							></el-input>
							<span v-show="errors.beReporterDepartment" class="error-msg" style="color: red">
								{{ errors.beReporterDepartment }}
							</span>
						</div>
						<div class="items">
							<div class="Lable">
								职务
								<span>*</span>
							</div>
							<el-input
								v-model="formData.beReporterJob"
								@focus="ReError('beReporterJob')"
							></el-input>
							<span v-show="errors.beReporterJob" class="error-msg" style="color: red">
								{{ errors.beReporterJob }}
							</span>
						</div>
						<div class="items">
							<div class="Lable">
								所在地区
								<span>*</span>
							</div>
							<el-input
								v-model="formData.beReporterArea"
								@focus="ReError('beReporterArea')"
							></el-input>
							<span v-show="errors.beReporterArea" class="error-msg" style="color: red">
								{{ errors.beReporterArea }}
							</span>
						</div>
						<div class="items">
							<div class="Lable">
								级别
								<span>*</span>
							</div>
							<el-select v-model="formData.beReporterRank" style="width: 100%">
								<el-option
									v-for="item in LevelreportedOptions"
									:key="item.value"
									:label="item.label"
									:value="item.value"
								></el-option>
							</el-select>
							<span v-show="errors.beReporterRank" class="error-msg" style="color: red">
								{{ errors.beReporterRank }}
							</span>
						</div>
					</div>
				</div>

				<div class="FormModule">
					<div class="titleCard">举报正文</div>
					<div class="Formes" :style="{ 'pointer-events': readonly ? 'none' : 'auto' }">
						<div class="items maxItem">
							<div class="Lable Lable1">
								标题(最多50字)
								<span>*</span>
							</div>
							<el-input v-model="formData.title" @focus="ReError('title')"></el-input>
							<span v-show="errors.title" class="error-msg" style="color: red; left: 210px">
								{{ errors.title }}
							</span>
						</div>
						<div class="items">
							<div class="Lable">问题类别</div>
							<el-select
								v-model="formData.questionType"
								style="width: 100%"
								@change="getProblemSubclassList"
							>
								<el-option
									v-for="item in questionTypeList"
									:key="item.value"
									:label="item.value"
									:value="item.value"
								></el-option>
							</el-select>
							<span v-show="errors.questionType" class="error-msg" style="color: red">
								{{ errors.questionType }}
							</span>
						</div>
						<div class="items">
							<div class="Lable">问题细类</div>
							<el-select
								v-model="formData.questionSub"
								:placeholder="formData.questionType ? '请选择' : '请先选择问题类别'"
								style="width: 100%"
							>
								<el-option
									v-for="item in ProblemSubclassList"
									:key="item.value"
									:label="item.value"
									:value="item.value"
								></el-option>
							</el-select>
							<span v-show="errors.questionSub" class="error-msg" style="color: red">
								{{ errors.questionSub }}
							</span>
						</div>
						<div class="items maxItem">
							<div class="Lable">
								主要问题
								<span>*</span>
							</div>
							<el-input
								v-model="formData.content"
								type="textarea"
								:rows="8"
								show-word-limit
								maxlength="1000"
								@focus="ReError('content')"
							></el-input>
							<span v-show="errors.content" class="error-msg" style="color: red">
								{{ errors.content }}
							</span>
						</div>
					</div>
				</div>

				<div class="FormModule">
					<div class="titleCard">上传附件</div>
					<div class="Formes">
						<div class="items maxItem">
							<div class="Lable" style="border-right: 1px solid #ccc">附件</div>
							<es-upload
								v-bind="{
									code: 'plat_discipline_inspection_reach',
									'select-type': 'icon-plus',
									listType: 'picture-card',
									readonly: readonly,
									ownId: formData.id,
									limit: 8,
									download: true,
									dragSort: true
								}"
							></es-upload>
						</div>
					</div>
				</div>

				<div v-if="!readonly" class="ButtonBox">
					<div class="buttons" @click="SubmitForm">提交</div>
					<div class="buttons" @click="RestForm">重填</div>
				</div>
			</div>
		</div>
	</div>
</template>

<script>
import questionTypeList from './questionTypeList.json';
import BackButtom from '@/components/backButtom.vue';

// 提取表单初始状态为常量
const INITIAL_FORM_DATA = {
	id: '',
	isAnonymity: 0
};

// 提取必填字段验证规则
const REQUIRED_FIELDS = [
	{ field: 'reporterName', message: '请填写姓名' },
	{ field: 'reporterIdcard', message: '请填写身份证号' },
	{ field: 'reporterPhone', message: '请填写联系方式' },
	{ field: 'reporterAddress', message: '请填写现居住地址' },
	{ field: 'beReporterName', message: '请填写被举报人' },
	{ field: 'beReporterDepartment', message: '请填写单位' },
	{ field: 'beReporterJob', message: '请填写职务' },
	{ field: 'beReporterArea', message: '请填写所在地区' },
	{ field: 'beReporterRank', message: '请选择被举报人级别' },
	{ field: 'title', message: '请填写标题' },
	{ field: 'content', message: '请填写主要问题' }
];

// 提取初始错误状态
const INITIAL_ERRORS = {
	reporterName: '',
	reporterIdcard: '',
	reporterPhone: '',
	reporterAddress: '',
	beReporterName: '',
	beReporterDepartment: '',
	beReporterJob: '',
	beReporterArea: '',
	beReporterRank: '',
	title: '',
	content: ''
};

export default {
	components: {
		BackButtom
	},
	props: {
		id: {
			type: statusbar,
			default: ''
		}
	},
	data() {
		return {
			checked: false,
			ContentTyep: 'Reading', // 举报内容类型
			loading: false,
			formData: { ...INITIAL_FORM_DATA },
			errors: { ...INITIAL_ERRORS },
			politicsStatusList: [
				{ value: '中国共产党党员' },
				{ value: '中共预备党员' },
				{ value: '共青团员' },
				{ value: '民主党派人士' },
				{ value: '无党派民主人士' },
				{ value: '群众' }
			],
			// 其他数据保持不变
			LevelList: [
				{ value: '正省部级' },
				{ value: '副省部级' },
				{ value: '正厅局级' },
				{ value: '副厅局级' },
				{ value: '正县处级' },
				{ value: '副县处级' },
				{ value: '正乡科级' },
				{ value: '副乡科级' },
				{ value: '一般干部' },
				{ value: '军队' },
				{ value: '金融机构' },
				{ value: '一般企业' },
				{ value: '事业' },
				{ value: '农村' },
				{ value: '社区' },
				{ value: '其他' }
			],
			LevelreportedOptions: [
				{ value: '正乡科级' },
				{ value: '副乡科级' },
				{ value: '一般干部' },
				{ value: '军队' },
				{ value: '金融机构' },
				{ value: '一般企业' },
				{ value: '事业' },
				{ value: '农村' },
				{ value: '社区' },
				{ value: '其他' }
			],
			questionTypeList: [],
			ProblemSubclassList: []
		};
	},
	computed: {
		readonly() {
			return this.ContentTyep === 'view';
		}
	},
	created() {
		this.questionTypeList = questionTypeList;
		const id = this.$route.query.id || this.id;
		if (id) {
			this.ContentTyep = 'view';
			this.getDetail(id);
		} else {
			this.ContentTyep = 'Reading';
		}
	},
	methods: {
		// 获取详情数据
		getDetail(id) {
			this.loading = true;
			this.$request({
				url: '/ybzy/platDisciplineInspectionReach/info',
				method: 'get',
				params: { id }
			})
				.then(res => {
					this.loading = false;
					if (res.rCode === 0) {
						this.formData = { ...res.results };
						if (this.formData.questionType) {
							this.getProblemSubclassList(this.formData.questionType, 'init');
						}
					} else {
						this.$message.error(res.msg || '获取详情失败');
					}
				})
				.catch(() => {
					this.loading = false;
					this.$message.error('获取详情失败');
				});
		},

		// 选择举报类型
		reportFn(type) {
			if (!this.checked) {
				this.$notify.info({
					title: '消息',
					message: '请先勾选阅读条款',
					duration: 2000
				});
				return;
			}
			this.ContentTyep = 'report';
			const id = this.$route.query.id || this.id;
			this.formData.id = id || this.$uuidv4();
			this.formData.isAnonymity = type;
		},

		// 返回阅读页面
		backReading() {
			if (this.ContentTyep === 'view') {
				// 判断是否有上一页历史记录
				if (window.history.length > 1) {
					this.$router.go(-1);
				} else {
					// 如果没有上一页，则关闭当前窗口
					window.close();
				}
				return;
			}
			this.ContentTyep = 'Reading';
			this.resetForm();
		},

		// 获取问题子类列表
		getProblemSubclassList(val, type) {
			if (type !== 'init') {
				this.formData.questionSub = '';
			}
			const selectedType = this.questionTypeList.find(item => item.value === val);
			this.ProblemSubclassList = selectedType ? selectedType.chldren : [];
		},

		// 表单验证
		validateForm() {
			this.restError();
			let isValid = true;

			// 只验证非匿名举报或必填字段
			REQUIRED_FIELDS.forEach(field => {
				// 如果是匿名举报，不验证举报人信息
				if (
					this.formData.isAnonymity === 1 &&
					['reporterName', 'reporterIdcard', 'reporterPhone', 'reporterAddress'].includes(
						field.field
					)
				) {
					return;
				}

				if (!this.formData[field.field]) {
					this.errors[field.field] = field.message;
					isValid = false;
				}
			});

			return isValid;
		},

		// 提交表单
		SubmitForm() {
			if (!this.validateForm()) return;

			this.loading = true;
			this.$request({
				url: '/ybzy/platDisciplineInspectionReach/save',
				method: 'post',
				params: this.formData
			})
				.then(res => {
					this.loading = false;
					if (res.rCode !== 0) {
						this.$message.error(res.msg || '提交失败');
						return;
					}
					this.$message.success('提交成功');
					this.$router.push(
						this.ContentTyep === 'view' ? '/disciplineInspectionReach' : '/DirectTrainList'
					);
				})
				.catch(() => {
					this.loading = false;
					this.$message.error('提交失败');
				});
		},

		// 重置错误信息
		restError() {
			this.errors = { ...INITIAL_ERRORS };
		},

		// 清除单个字段错误
		ReError(key) {
			this.errors[key] = '';
		},

		// 重置表单
		resetForm() {
			this.formData = { ...INITIAL_FORM_DATA };
			this.restError();
		},

		// 重填表单（带确认）
		RestForm() {
			this.$confirm('您确定要重置提交内容吗？', '提示', {
				confirmButtonText: '确定',
				cancelButtonText: '取消'
			})
				.then(() => {
					this.resetForm();
				})
				.catch(() => {});
		},

		// 复选框变化事件（保留但简化）
		getChekboxVal() {
			// 仅用于响应事件，不需要额外逻辑
		}
	}
};
</script>

<style lang="scss" scoped>
// 定义变量
$primary-color: #003366; // 主色调（深蓝色）
$secondary-color: #f2f2f2; // 辅助色（浅灰色）
$accent-color: #cc0000; // 强调色（红色）
$white-color: #fff;
$black-color: #000;
$gray-color: #999;
$font-family: Arial, sans-serif;

.BacgroundBox {
	background: #d3e6fc;
	height: 100vh;
	overflow: scroll;
	padding: 20px;
}

.report-card {
	margin: 0 auto;
	width: 60%; // 调整宽度
	background-color: #ffffff; // 改为白色背景
	border-radius: 12px;
	padding: 30px 60px;
	box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
	margin-bottom: 20px;
	letter-spacing: 1px;

	h1 {
		color: #000;
		margin-top: 0;
		font-size: 34px;
		font-weight: 1000;
		font-family: 仿宋;
	}

	p {
		color: #555;
		margin-bottom: 15px;
		font-size: 20px;
		margin: 20px 0;
		font-family: 仿宋;
	}

	.report-step {
		margin-bottom: 15px;
		font-family: 仿宋;

		.report-step-content {
			padding-left: 20px;

			h3 {
				color: $primary-color;
				cursor: pointer;
				margin-bottom: 5px;
				border-bottom: 1px solid #ccc;
				padding-bottom: 5px;
				margin: 20px 0;
				font-size: 22px;
			}

			ul {
				li {
					margin: 20px 0;
					font-size: 19px;
				}
			}
		}

		.CheckBox {
			width: 100%;
			margin-top: 30px;
			margin-left: 20px;
			display: flex;
			align-items: center;

			input[type='checkbox'] {
				width: 20px;
				height: 20px;
				border: 2px solid #ccc;
				border-radius: 5px;
				cursor: pointer;
			}

			input[type='checkbox']:hover {
				border-color: #0056b3;
			}

			span {
				margin-left: 10px;
				font-size: 19px;
			}
		}
	}

	.FormBox {
		h1 {
			color: #000;
			margin-top: 0;
			font-size: 34px;
			font-weight: 1000;
			font-family: sans-serif;
		}

		.FormModule {
			width: 100%;
			max-width: 1000px;
			margin: 20px auto;

			.titleCard {
				font-size: 20px;
				font-weight: bold;
				color: #333;
				text-align: center;
				background: linear-gradient(to right, #007bff, #0056b3);
				color: white;
				padding: 10px 0;
			}

			.Formes {
				display: flex;
				flex-wrap: wrap;
				justify-content: space-between;

				.items {
					width: calc(50%);
					display: flex;
					border: 1px solid #ccc;
					border-right: none;
					border-top: none;
					position: relative;

					&:nth-child(even) {
						border-right: none;
					}

					.Lable {
						min-width: 140px;
						font-size: 16px;
						color: #000;
						display: flex;
						height: 40px;
						height: 100%;
						font-weight: bold;
						align-items: center;
						padding: 5px 10px;
						background-color: #f9f9f9;

						span {
							color: red;
						}
					}

					.error-msg {
						position: absolute;
						font-size: 12px;
						top: 12px;
						left: 170px;
						pointer-events: none;
					}

					.myUpLoad {
						display: flex;
						flex-direction: column;
						align-items: center;

						.el-button {
							margin: 10px 0;
						}
					}
				}

				.maxItem {
					width: 100%;
					border-right: 1px solid #ccc;
				}
			}
		}

		.BtnBox {
			display: flex;
		}
	}

	.ButtonBox {
		display: flex;
		justify-content: center;
		margin-top: 20px;

		.buttons {
			padding: 10px 20px;
			border-radius: 5px;
			cursor: pointer;
			font-size: 19px;
			transition: all 0.3s ease;
			font-weight: bold;
			margin: 0 15px;
			background-color: #007bff;
			color: white;

			&:hover {
				background-color: #0056b3;
			}
		}

		.noChecked {
			background: #b2b2b2;
			color: white;

			&:hover {
				background-color: #b2b2b2;
			}
		}
	}
}

// 美化滚动条
.BacgroundBox {
	&::-webkit-scrollbar {
		width: 8px;
	}

	&::-webkit-scrollbar-track {
		background: #f1f1f1;
		border-radius: 4px;
	}

	&::-webkit-scrollbar-thumb {
		background: #888;
		border-radius: 4px;

		&:hover {
			background: #555;
		}
	}
}
</style>
