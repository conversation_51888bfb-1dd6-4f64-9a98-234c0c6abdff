<template>
	<div class="content">
		<div style="display: inline-block; width: 200px; border-right: 1px #f6efef solid">
			<es-tree
				class="filter-tree"
				:data="treeData"
				:props="treeProps"
				default-expand-all
				:expand-on-click-node="false"
				node-key="id"
				:current-node-key="null"
				@node-click="treeNodeClick"
			></es-tree>
		</div>
		<div class="form-box">
			<es-data-table
				ref="table"
				:key="changeFlag"
				:row-style="tableRowClassName"
				:full="true"
				:fit="true"
				:thead="thead"
				:toolbar="toolbar"
				:border="true"
				:page="pageOption"
				:option-data="optionData"
				:url="dataTableUrl"
				:numbers="true"
				:param="params"
				close
				form
				@btnClick="btnClick"
				@search="hadeSubmit"
				@reset="resetTable"
			></es-data-table>
		</div>
		<es-dialog
			:title="formTitle"
			:visible.sync="showForm"
			width="60%"
			:drag="false"
			:close-on-click-modal="false"
			:destroy-on-close="true"
		>
			<es-form
				ref="form"
				:model="formData"
				:contents="formItemList"
				height="500px"
				:genre="2"
				collapse
			/>
		</es-dialog>
	</div>
</template>

<script>
import interfaceUrl from '@/http/logistics/statistics.js';
import storeroomApi from '@/http/logistics/hqStoeroom';
import { host } from '../../../../config/config';

export default {
	data() {
		return {
			treeProps: {
				label: 'name',
				children: 'children'
			},
			treeData: [],
			changeFlag: 1.234, //给表格组件加唯一key(解决左侧树节点切换后，表格的搜索未重置)
			ownId: '',
			dataTableUrl: interfaceUrl.listJson,
			showForm: false,
			showDelete: false,
			formData: {},
			formTitle: '编辑',
			editBtn: {
				type: 'submit',
				skin: 'lay-form-btns',
				contents: [
					{
						type: 'primary',
						text: '确定',
						event: 'confirm'
					},
					{
						type: 'reset',
						text: '取消',
						event: 'cancel'
					}
				]
			},
			cancelBtn: {
				type: 'submit',
				skin: 'lay-form-btns',
				contents: [
					{
						type: 'reset',
						text: '取消',
						event: 'cancel'
					}
				]
			},

			thead: [
				{
					title: '原料名称',
					field: 'name',
					align: 'center',
					showOverflowTooltip: true
				},
				{
					title: '原料编号',
					field: 'code',
					align: 'center',
					showOverflowTooltip: true
				},
				{
					title: '原料分类',
					field: 'categoryName',
					minWidth: 60,
					align: 'center',
					showOverflowTooltip: true
				},
				{
					title: '供应商',
					field: 'brand',
					align: 'center',
					minWidth: 100,
					showOverflowTooltip: true
				},
				{
					title: '规格',
					field: 'specification',
					align: 'center',
					showOverflowTooltip: true
				},

				{
					title: '库存数量',
					field: 'inventoryNum',
					minWidth: 60,
					align: 'center',
					showOverflowTooltip: true
				},
				{
					title: '库存单位',
					field: 'unit',
					width: 90,
					align: 'center',
					showOverflowTooltip: true
				},
				// {
				// 	title: '启用状态',
				// 	field: 'status',
				// 	align: 'center'
				// },
				{
					title: '操作',
					type: 'handle',
					width: 100,
					template: '',
					events: [
						{
							code: 'view',
							text: '查看'
						}
					]
				}
			],
			optionData: {
				categoryId: [],
				status: [
					{ name: '禁用中', value: '0' },
					{ name: '启用中', value: '1' }
				]
			},
			pageOption: {
				pageSize: 20,
				totalCount: 0
			},
			params: {
				storeroomId: null
			},
			submitFilterParams: {}
		};
	},
	computed: {
		toolbar() {
			return [
				{
					type: 'button',
					contents: [
						{
							text: '导出',
							code: 'download',
							type: 'primary'
						}
					]
				},
				{
					type: 'search',
					reset: true,
					contents: [
						{
							type: 'select',
							name: 'categoryId',
							placeholder: '原料分类',
							valueType: 'string',
							default: true,
							parentCheck: true,
							tree: true,
							url: '/ybzy/hqbasematerialcategory/getTreeListOne',
							'value-key': 'id',
							'label-key': 'name'
						},
						{
							type: 'text',
							label: '原料名称/编号',
							name: 'keyword',
							placeholder: '原料名称/编号',
							col: 6
						}
					]
				}
			];
		},
		formItemList() {
			return [
				{
					label: '原料名称',
					name: 'name',
					col: 6
				},
				{
					name: 'categoryId',
					label: '原料分类',
					col: 6,
					type: 'select',
					url: '/ybzy/hqbasematerialcategory/getListAll',
					'label-key': 'name',
					'value-key': 'id'
				},
				{
					label: '原料编号',
					name: 'code',
					col: 6
				},
				{
					label: '库存单位',
					name: 'unit',
					col: 6
				},
				{
					label: '库存数量',
					name: 'inventoryNum',
					col: 6
				},
				{
					label: '供应商',
					name: 'brand',
					col: 6
				},
				{
					label: '规格',
					name: 'specification',
					col: 6
				},
				{
					label: '能量',
					name: 'energy',
					col: 6
				},
				{
					label: '蛋白质',
					name: 'protein',
					col: 6
				},
				{
					label: '脂肪',
					name: 'fat',
					col: 6
				},
				{
					label: '碳水化合物',
					name: 'carbohydrate',
					col: 6
				},
				{
					label: '钠',
					name: 'sodium',
					col: 6
				}
			];
		}
	},
	watch: {},
	created() {},
	mounted() {
		this.$request({
			url: '/ybzy/hqbasematerialcategory/list',
			method: 'POST'
		}).then(res => {
			if (res.rCode !== 0) {
				this.$message.error(res.msg);
				return;
			}
			this.optionData.categoryId = res.results.records;
		});
		this.storeroomList();
	},
	methods: {
		/**
		 * 表格行高
		 */
		tableRowClassName({ row, rowIndex }) {
			let styleRes = {
				height: '54px !important'
			};
			return styleRes;
		},
		hadeSubmit(e) {
			this.submitFilterParams = e;
		},
		/**
		 * 操作按钮点击事件
		 * @param {*} res
		 */
		btnClick(res) {
			let code = res.handle.code;
			switch (code) {
				case 'view':
					this.formTitle = '查看';
					this.readModule(this.formItemList);
					this.formData = {};
					this.formData = Object.assign({}, res.row);
					this.showForm = true;
					break;
				case 'export':
					this.$request({
						url: interfaceUrl.export,
						method: 'GET'
					});
					break;
				case 'download':
					const paramAll = { ...this.params, ...this.submitFilterParams };
					let url = host + interfaceUrl.download;
					let urlParams = this.objToUrlParams(paramAll);
					if (urlParams) {
						url += '?' + urlParams;
					}
					window.open(url, '_self');
					break;
				default:
					break;
			}
		},
		//导出库存操作中，拼接参数
		objToUrlParams(obj) {
			return Object.keys(obj)
				.filter(key => obj[key] !== '' && obj[key] !== null)
				.map(key => encodeURIComponent(key) + '=' + encodeURIComponent(obj[key]))
				.join('&');
		},
		/**
		 * 只读模式
		 */
		readModule(list, hideField) {
			for (var i in list) {
				var item = list[i];
				item.readonly = true;
			}
			list.push(this.cancelBtn);
		},
		storeroomList() {
			this.$request({
				url: storeroomApi.listAll,
				method: 'GET'
			}).then(res => {
				if (res.rCode === 0) {
					let storerooms = res.results;
					this.treeData = [
						{
							id: null,
							name: '全部',
							children: storerooms
						}
					];
				}
			});
		},
		treeNodeClick(node) {
			this.submitFilterParams = {};
			this.params.storeroomId = node.id;
			this.changeFlag = Math.random();
		},
		resetTable() {
			this.submitFilterParams = {};
			this.$refs.table.resetTable();
		}
	}
};
</script>
<style scoped lang="scss">
@import '../../../assets/style/style.scss';

.content {
	display: flex;
	width: 100%;
	height: 100%;
	.form-box {
		width: calc(100% - 200px);
		height: 100%;
	}
}

.el-dialog__body {
	.btn-box {
		margin-top: 20px;
		display: flex;
		justify-content: flex-end;

		.btn {
			padding: 5px 10px;
			color: #666;
			border: 1px solid #eee;
			cursor: pointer;
			margin-right: 5px;
			border-radius: 4px;
			// &.theme {
			// 	background: $--color-primary;
			// 	color: #fff;
			// 	border-color: $--color-primary;
			// }
		}
	}
}
</style>
