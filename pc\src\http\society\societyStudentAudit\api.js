/*  接口地址定义
    方式1：
        //获取xxx信息
        export const getInfoStatistical = '/hr/shycInfo/getInfoStatistical.dhtml';
    调用方式1：
        import {getInfoStatistical} from '@/http/system.js';
        console.log(getInfoStatistical)

    方式2：
        //系统管理模块
        const system = {
            //用户、用户组树
            getDeptTree: '/bootdemo/simplesysDept/getDeptTree.djson',
        }
        export default system;
    调用方式2:
        import system from '@/http/system.js';
        console.log(system.getDeptTree)
        
*/

//接口地址
const api = {
	// 消息发送
	societyStudentAuditList: '/ybzy/societyStudentRecord/listJson',
	societyStudentAuditInfo: '/ybzy/societyStudentRecord/',
	societyStudentAuditSave: '/ybzy/societyStudentRecord/save',
	societyStudentAuditUpdate: '/ybzy/societyStudentRecord/update',
	societyStudentAuditAudit: '/ybzy/societyStudentRecord/audit',
	societyStudentAuditDeleteById: '/ybzy/societyStudentRecord/deleteById',
	societyStudentAuditDeleteByIds: '/ybzy/societyStudentRecord/deleteBatchIds',
	societyList: '/ybzy/society/list',
	studentList: '/ybzy/platperson/getStudentSelectList' // 学生下拉框
};
export default api;
