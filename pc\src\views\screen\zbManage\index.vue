<template>
  <div class="content">
    <es-data-table ref="table" :row-style="tableRowClassName" :full="true" :fit="true" :thead="thead" :toolbar="toolbar"
                   :border="true" :page="pageOption" :url="dataTableUrl" :numbers="true" @btnClick="btnClick"
                   @sort-change="sortChange" :param="params" @submit="hadeSubmit"
                   close form></es-data-table>
    <es-dialog :title="formTitle" :visible.sync="showForm" width="60%" :close-on-click-modal="false"
               :destroy-on-close="true">
      <es-form ref="form" :model="formData" :contents="formItemList" height="500px" :genre="2" collapse
               @change="inputChange" @submit="handleFormSubmit" @reset="showForm = false" />
    </es-dialog>
    <es-dialog title="删除" :visible.sync="showDelete" width="20%" :close-on-click-modal="false" :middle="true"
               height="140px">
      <div>确定要删除该条数据吗</div>
      <div class="btn-box">
        <div class="btn theme" @click="deleteRow">确定</div>
        <div class="btn" @click="showDelete = false">取消</div>
      </div>
    </es-dialog>
  </div>
</template>

<script>
import interfaceUrl from '@/http/screen/zbManage';
import SnowflakeId from "snowflake-id";
export default {
  name: "zbManage",//值班管理
  data() {
    return {
      ownId: '',
      dataTableUrl: interfaceUrl.listJson,
      showForm: false,
      showDelete: false,
      formData: {},
      formTitle: '编辑',
      editBtn: {
        type: 'submit',
        skin: 'lay-form-btns',
        contents: [
          {
            type: 'primary',
            text: '确定',
            event: 'confirm'
          },
          {
            type: 'reset',
            text: '取消',
            event: 'cancel'
          },
        ]
      },
      cancelBtn: {
        type: 'submit',
        skin: 'lay-form-btns',
        contents: [
          {
            type: 'reset',
            text: '取消',
            event: 'cancel'
          },
        ]
      },
      toolbar: [
        {
          type: 'button',
          contents: [
            {
              text: '新增',
              code: 'add',
              type: 'primary'
            }
          ]
        },
        {
          type: 'search',
          contents: [
            {
              type: 'text',
              name: 'collegeName',
              placeholder: '关键字查询'
            },
          ]
        }
      ],
      thead: [
        {
          title: '学院',
          align: 'left',
          field: 'collegeName',
          sortable: 'custom',
          showOverflowTooltip: true,
        },
        {
          title: '值班领导',
          align: 'left',
          field: 'workLeaderName',
          sortable: 'custom',
          showOverflowTooltip: true,
          sysCode:this.selectLeaderList,
        },
        {
          title: '值班领导电话',
          align: 'left',
          field: 'workLeaderTel',
          sortable: 'custom',
          showOverflowTooltip: true,
        },
        {
          title: '值班人员',
          align: 'left',
          field: 'workMonitorName',
          sortable: 'custom',
          showOverflowTooltip: true,
        },
        {
          title: '值班人员电话',
          align: 'left',
          field: 'workMonitorTel',
          sortable: 'custom',
          showOverflowTooltip: true,
        },
        {
          title: '值班时间',
          align: 'left',
          field: 'workTime',
          sortable: 'custom',
          showOverflowTooltip: true,
        },
        {
          title: '操作',
          type: 'handle',
          width: 180,
          template: '',
          events: [
            {
              code: 'edit',
              text: '编辑'
            },
            {
              code: 'view',
              text: '查看'
            },
            {
              code: 'delete',
              text: '删除'
            },
          ]
        }
      ],
      pageOption: {
        layout: 'total, prev, pager, next, jumper',
        pageSize: 10,
        // hideOnSinglePage: true,
        position: 'right',
        current: 1,
        pageNum: 1
      },
      params: {
        asc: "true",
        orderBy: "workTime"
      },
      selectSchoolList: [],
      selectLeaderList: [],
      selectEmpList: [],
    };
  },
  computed: {
    formItemList() {
      return [
        // {
        //     type: 'select',
        //     label: '学院',
        //     name: 'collegeId',
        //     placeholder: '请选择学院',
        //     filterable: true,
        //     isNoParamRequest: false,
        //     'value-key': 'value',
        //     'label-key': 'label',
        //     'filter-method': this.selectSchool,
        //     clearable: true,
        //     col: 10,
        //     rules: {
        //         required: true,
        //         message: '请选择学院',
        //         trigger: 'blur'
        //     },
        //     verify: 'required',
        //     data: this.selectSchoolList
        // },
        // {
        //   type: 'select',
        //   label: '值班领导',
        //   name: 'leader',
        //   placeholder: '请输入值班领导',
        //   filterable: true,
        //   isNoParamRequest: false,
        //   'value-key': 'value',
        //   'label-key': 'label',
        //   // 'filter-method': this.selectLeaderOrEmp("值班领导"),
        //   clearable: true,
        //   col: 10,
        //   rules: {
        //     required: true,
        //     message: '请输入值班领导',
        //     trigger: 'blur'
        //   },
        //   verify: 'required',
        //   data: this.selectLeaderList,
        // },
        // {
        //   type: 'select',
        //   label: '值班员',
        //   name: 'emp',
        //   placeholder: '请输入值班员',
        //   filterable: true,
        //   isNoParamRequest: false,
        //   'value-key': 'value',
        //   'label-key': 'label',
        //   // 'filter-method': this.selectLeaderOrEmp("值班长"),
        //   clearable: true,
        //   col: 10,
        //   rules: {
        //     required: true,
        //     message: '请输入值班员',
        //     trigger: 'blur'
        //   },
        //   verify: 'required',
        //   data: this.selectEmpList,
        // },
        {
          label: '学院名称',
          name: 'collegeName',
          placeholder: '学院名称',
          event: 'multipled',
          rules: {
            required: true,
            message: '请输入学院名称',
            trigger: 'blur'
          },
          verify: 'required',
          col: 10
        },
        {
          label: '值班领导',
          name: 'workLeaderName',
          placeholder: '值班领导',
          event: 'multipled',
          rules: {
            required: true,
            message: '请输入值班领导',
            trigger: 'blur'
          },
          verify: 'required',
          col: 10
        },
        {
          label: '值班领导电话',
          name: 'workLeaderTel',
          placeholder: '值班领导电话',
          event: 'multipled',
          rules: {
            required: true,
            message: '请输入值班领导电话',
            trigger: 'blur',
            pattern: /^1[3456789]\d{9}$/,
          },
          verify: 'required',
              col: 10
        },
        {
          label: '值班长姓名',
          name: 'workMonitorName',
          placeholder: '值班长姓名',
          event: 'multipled',
          rules: {
            required: true,
            message: '请输入值班长姓名',
            trigger: 'blur'
          },
          verify: 'required',
          col: 10
        },
        {
          label: '值班长电话',
          name: 'workMonitorTel',
          placeholder: '值班长姓名电话',
          event: 'multipled',
          rules: {
            required: true,
            message: '请输入值班长姓名电话',
            trigger: 'blur',
            pattern: /^1[3456789]\d{9}$/,
          },
          verify: 'required',
          col: 10
        },
        {
          label: '值班时间',
          name: 'workTime',
          placeholder: '值班时间',
          event: 'multipled',
          rules: {
            required: true,
            message: '请输入值班时间',
            trigger: 'blur'
          },
          verify: 'required',
          col: 10,
          type: 'date'
        },
        {
          name: 'remark',
          label: '备注',
          placeholder: '请输入备注',
          type: 'textarea',
          col: 10
        }
      ]
    },
  },
  watch: {
      showForm(val) {
          if (!val) {
            this.formData = {};
          }else {
            // this.selectSchool();
            // this.selectLeaderOrEmp("值班领导");
            // this.selectLeaderOrEmp("值班长");
          }
      }
  },
  created() {
    // this.selectLeaderOrEmp("值班领导");
    // this.selectLeaderOrEmp("值班长");
  },
  mounted() {

  },
  methods: {
    /**
     * 表格行高
     */
    tableRowClassName({ row, rowIndex }) {
      let styleRes = {
        "height": "54px !important"
      }
      return styleRes

    },
    inputChange(key, value) {

    },
    hadeSubmit(data) {

    },
    /**
     * 操作按钮点击事件
     * @param {*} res
     */
    btnClick(res) {
      let code = res.handle.code;
      switch (code) {
        case 'add':
          // 新增
          this.formTitle = '新增';
          this.editModule(this.formItemList);
          const snowflake = new SnowflakeId();
          let id = snowflake.generate();
          this.ownId = id;
          this.formData = { id: id, state: true, photoUrlTemp: null,collegeId: null,leader: null,emp: null,startTime: null,endTime: null,remark: null };
          this.showForm = true;
          break;
        case 'edit':
          // 编辑
          this.formTitle = '编辑';
          this.ownId = res.row.id;
          this.editModule(this.formItemList);
          this.$request({
            url: interfaceUrl.info + '/' + res.row.id,
            method: 'GET'
          }).then(res => {
            if (res.rCode == 0) {
              this.formData = res.results;
              if(undefined !== this.formData.photoUrl && null !== this.formData.photoUrl){
                this.formData.photoUrlTemp = '/main2/mecpfileManagement/previewAdjunct?adjunctId='+this.formData.photoUrl + '&pic=' + Math.random();
              }
              this.formData.state = 1 == this.formData.state ? true : false;
              this.formData.sex = this.formData.sex + '';
              this.showForm = true;
              this.$forceUpdate();
            }
          });
          break;
        case 'view':
          this.formTitle = '查看';
          this.ownId = res.row.id;
          this.readModule(this.formItemList);
          this.$request({
            url: interfaceUrl.info + '/' + res.row.id,
            method: 'GET'
          }).then(res => {
            if (res.rCode == 0) {
              this.formData = res.results;
              if(undefined !== this.formData.photoUrl && null !== this.formData.photoUrl){
                this.formData.photoUrlTemp = '/main2/mecpfileManagement/previewAdjunct?adjunctId='+this.formData.photoUrl + '&pic=' + Math.random();
              }
              this.formData.state = 1 == this.formData.state ? true : false;
              this.formData.sex = this.formData.sex + '';
              this.showForm = true;
              this.$forceUpdate();
            }
          });
          break;
        case 'delete':
          this.deleteId = res.row.id;
          this.showDelete = true;
          break;
        default:
          break;
      }
    },
    handleClose() { },
    handleFormItemChange() {

    },
    handleFormSubmit(data) {
      let formData = data;
      delete formData.extMap;
      let url = "";
      if (this.formTitle == '新增') {
        url = interfaceUrl.save;
      } else {
        url = interfaceUrl.update;
      }
      formData.state = formData.state ? 1 : 0;
      if (undefined != formData.photoUrlTemp && undefined != formData.photoUrlTemp.response) {
        formData.photoUrl = formData.photoUrlTemp.response.adjunctId;
      }
      this.$request({
        url: url,
        data: formData,
        method: 'POST'
      }).then(res => {
        if (res.rCode == 0) {
          this.$message.success('操作成功');
          this.showForm = false;
          this.formData = {};
          this.$refs.table.reload();
        } else {
          this.formData.state = 1 == this.formData.state ? true : false;
          this.$message.error(res.msg);
        }
      });
    },
    deleteRow() {
      this.$request({
        url: interfaceUrl.deleteBatchIds,
        data: { ids: this.deleteId },
        method: 'POST'
      }).then(res => {
        if (res.rCode == 0) {
          this.$refs.table.reload();
          this.$message.success('删除成功');
        } else {
          this.$message.error(res.msg);
        }
        this.showDelete = false;
      });
    },
    /**
     * 排序变化事件
     * @param {*} column
     * @param {*} prop
     * @param {*} order
     */
    sortChange(column, prop, order) {
      if (column.order == 'ascending') {//升序
        this.params = {
          asc: "true",
          orderBy: column.prop
        }

      } else if (column.order == 'descending') {//降序
        this.params = {
          asc: "false",
          orderBy: column.prop
        }
      } else { //不排序
        this.params = {
          asc: "false",
          orderBy: "createTime"
        }
      }
      this.$refs.table.reload()

    },
    /**
     * 编辑模式
     */
    editModule(list) {
      for (var i in list) {
        var item = list[i];
        item.readonly = false;
      }
      list.push(this.editBtn);
    },
    /**
     * 只读模式
     */
    readModule(list, hideField) {
      for (var i in list) {
        var item = list[i];
        item.readonly = true;
      }
      list.push(this.cancelBtn);
    },
      /**
       * 获取学院信息
       * @param res
       */
      selectSchool(res) {
        this.$request({
            url: interfaceUrl.selectSchool,
            params: { name: res }
        }).then(res => {
            this.selectSchoolList = res.results;
        });
      },
      /**
       * 获取值班领导或值班长
       * @param res
       */
      selectLeaderOrEmp(type) {
        this.$request({
          url: interfaceUrl.selectLeaderOrEmp,
          params: { type: type }
        }).then(res => {
          if(type=="值班领导"){
            this.selectLeaderList = res.results;
          }else{
            this.selectEmpList = res.results;
          }
        });
      }
    
  }
};
</script>
<style scoped lang="scss">
@import '../../../assets/style/style.scss';

.content {
  display: flex;
  width: 100%;
  height: 100%;

  ::v-deep .es-data-table {
    // flex: 1;
    // display: flex;
    // flex-direction: column;
    width: 100%;

    .es-data-table-content {
      // flex: 1;
      // height: 0;
      // display: flex;
      // flex-direction: column;

      .el-table {
        flex: 1;
        // height: 100% !important;
      }

      .es-thead-border {
        .el-table__header {
          th {
            border-right: 1px solid #E1E1E1;
          }
        }
      }
    }
  }

  ::v-deep .el-form-item__label {
    background: none;
    border: 0px solid #c2c2c2;
  }
}

.el-dialog__body {
  .btn-box {
    margin-top: 20px;
    display: flex;
    justify-content: flex-end;

    .btn {
      padding: 5px 10px;
      color: #666;
      border: 1px solid #eee;
      cursor: pointer;
      margin-right: 5px;
      border-radius: 4px;
      // &.theme {
      // 	background: $--color-primary;
      // 	color: #fff;
      // 	border-color: $--color-primary;
      // }
    }
  }


}
</style>
