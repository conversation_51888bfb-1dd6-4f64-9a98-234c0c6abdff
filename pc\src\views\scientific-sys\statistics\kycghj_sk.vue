<template>
	<div v-loading="loading" class="content">
		<es-data-table
			ref="table"
			:row-style="tableRowClassName"
			:thead="thead"
			:toolbar="toolbar"
			:border="true"
			:page="pageOption"
			:url="basics.dataTableUrl"
			filter
			:numbers="true"
			show-label
			:param="params"
			close
			@search="search"
			@reset="search({})"
			@btnClick="btnClick"
		></es-data-table>
	</div>
</template>

<script>
import { host } from '@/../config/config';
import qs from 'qs';
export default {
	data() {
		return {
			basics: {
				dataTableUrl: '/ybzy/paBaseInfo/statisticsAwardSocial', // 列表接口
				download: '/ybzy/paBaseInfo/exportAwardSocial' // 导出
			},
			loading: false,
			params: {},
			pageOption: {
				layout: 'total, prev, pager, next, jumper',
				pageSize: 20,
				// hideOnSinglePage: true,
				position: 'center',
				current: 1,
				pageNum: 1
			},
			codesObj: {} //批量数据字典
		};
	},
	computed: {
		thead() {
			return [
				{
					title: '获奖成果',
					field: 'name',
					align: 'center',
					showOverflowTooltip: true,
					// width: 200,
					fixed: 'left'
				},
				{
					title: '第一作者',
					field: 'prizewinnerName',
					align: 'center',
					// width: 100
				},
				{
					title: '成果形式',
					field: 'form',
					align: 'center',
					// width: 100
				},
				{
					title: '项目来源',
					field: 'source',
					align: 'center',
					showOverflowTooltip: true,
					// width: 120
				},
				{
					title: '一级学科',
					field: 'firstLevelDiscipline',
					align: 'center',
					// width: 120
				},
				{
					title: '奖励名称',
					field: 'rewardName',
					align: 'center',
					showOverflowTooltip: true,
					// width: 180
				},
				{
					title: '奖励级别',
					field: 'rewardRank',
					align: 'center',
					// width: 100
				},
				{
					title: '奖励等级',
					field: 'rewardGrade',
					align: 'center',
					// width: 100
				},
				{
					title: '奖励日期',
					field: 'acquisitionTime',
					align: 'center',
					// width: 100
				},
				{
					title: '奖励批准号',
					field: 'rewardApprovalNum',
					align: 'center',
					// width: 120
				},
				{
					title: '奖励单位',
					field: 'rewardUnit',
					align: 'center',
					showOverflowTooltip: true,
					// width: 150
				},
				{
					title: '备注',
					field: 'remark',
					align: 'center',
					showOverflowTooltip: true,
					// width: 150
				}
			];
		},
		toolbar() {
			return [
				{
					type: 'button',
					contents: [
						{
							text: '导出',
							exportXls: true,
							icon: 'es-icon-daochu',
							type: 'primary',
							event: () => {
								const params = { ...this.params, ...this.selectParams };
								const url = host + this.basics.download + '?' + qs.stringify(params);
								window.open(url, '_self');
							}
						}
					]
				},
				{
					type: 'search',
					reset: true,
					contents: [
						{
							type: 'daterange',
							name: 'dates',
							label: '时间范围',
							placeholder: '请选择时间范围'
						},
						{ type: 'text', label: '关键字', name: 'keyword', placeholder: '请输入关键字查询' }
					]
				}
			];
		}
	},
	created() {
		const codes = 'member_nation,hq_person_education,hq_person_state';
		this.$utils.findSysCodeList(codes).then(res => {
			this.codesObj = res;
		});
	},
	methods: {
		//表格行高
		tableRowClassName({ row, rowIndex }) {
			let styleRes = { height: '54px !important' };
			return styleRes;
		},
		//操作按钮点击事件
		btnClick(res) {},
		search(res) {
			const paramsN = { ...res };
			if (paramsN.dates) {
				paramsN.startDate = paramsN.dates[0] || '';
				paramsN.endDate = paramsN.dates[1] || '';
				delete paramsN.dates;
			}
			this.params = {
				...{
					projectType: this.params.projectType,
					orderBy: this.params.orderBy,
					asc: this.params.asc // true 升序，false 降序
				},
				...paramsN
			};
			this.selectParams = paramsN;
		},
		refreshData() {
			this.$refs.table.reload();
		}
	}
};
</script>

<style scoped lang="scss">
.content {
	width: 100%;
	height: 100%;
}
</style>
