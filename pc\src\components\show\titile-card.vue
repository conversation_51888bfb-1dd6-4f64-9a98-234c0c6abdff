<template>
	<div class="title-card">
		<header>
			<div class="title layout y-center space-between">
				<div class="layout y-center">
					<img :src="imgUrl" alt="" />
					<span>{{ title }}</span>
				</div>
				<slot name="titleRight" />
			</div>
			<slot name="filtre" />
		</header>
		<article>
			<slot name="content" />
		</article>
	</div>
</template>

<script>
export default {
	props: {
		title: {
			type: String,
			required: true
		},
		imgUrl: {
			type: String,
			required: true
		}
	},
	data() {
		return {
			options: [
				{
					value: '1',
					label: '本月'
				},
				{
					value: '2',
					label: '本周'
				},
				{
					value: '3',
					label: '今日'
				}
			],
			value: {
				value: '1'
			},
			card: [
				{
					img: require('@ast/images/sys/card.png'),
					title: '应出勤人数',
					unit: '人',
					num: '0'
				},
				{
					img: require('@ast/images/sys/card.png'),
					title: '实出勤人数',
					unit: '人',
					num: '0'
				},
				{
					img: require('@ast/images/sys/card.png'),
					title: '请假人数',
					unit: '人',
					num: '0'
				},
				{
					img: require('@ast/images/sys/card.png'),
					title: '出差人数',
					unit: '人',
					num: '0'
				},
				{
					img: require('@ast/images/sys/card.png'),
					title: '旷工人数',
					unit: '人',
					num: '0'
				}
			]
		};
	},
	methods: {
		handleChange(val) {
			console.log(val);
		}
	}
};
</script>
<style lang="scss" scoped>
@import '@ast/style/public.scss';

.title-card {
	width: 100%;
	border-radius: 8px;
	header {
		@include flexBox(space-between);
		padding: 0 16px;
		height: 60px;
		border-bottom: 1px solid rgb(241, 244, 247);
		.title {
			width: 100%;
			font-size: 18px;
			font-weight: 550;

			img {
				width: 40px;
				height: 40px;
				margin-right: 0;
			}
		}
	}
	article {
		padding: 12px;
	}
}
</style>
