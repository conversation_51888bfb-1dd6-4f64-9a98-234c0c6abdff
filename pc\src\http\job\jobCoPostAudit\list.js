import httpApi from '@/http/job/jobCoPostAudit/api.js';

export default {
	computed: {
		component() {
			return {
				// 树表结构
				//name: 'es-tree-group'
				// 纯列表结构
				name: 'es-data-table'
			};
		},
		treeParam() {
			return {
				// 不需要左边树 可以
				//url: httpApi.jobCoPostTree,
				//title: '分类树'
			};
		},
		table() {
			return {
				url: httpApi.jobCoPostList,
				param: {},
				full: true,
				fit: true,
				page: true,
				stripe: true,
				numbers: true,
				border: true,
				'row-key': 'id',
				toolbar: [
					// {
					// 	type: 'button',
					// 	contents: [
					// 		{
					// 			text: '新增',
					// 			type: 'primary'
					// 		}
					// 	]
					// },
					{
						type: 'search',
						reset: true,
						contents: [
							{
								type: 'select',
								placeholder: '审核状态查询',
								name: 'auditStatus',
								event: 'multipled',
								sysCode: 'job_audit_status',
								'label-key': 'shortName',
								'value-key': 'cciValue',
								clearable: true,
								col: 4
							},
							{
								type: 'text',
								name: 'keyword',
								placeholder: '关键字查询'
							}
						]
					}
				],
				thead: [
					{
						title: '岗位',
						align: 'center',
						field: 'name'
					},
					{
						title: '岗位类型',
						align: 'center',
						field: 'postType',
						sysCode: 'post_job_type'
					},
					{
						title: '薪资范围',
						align: 'center',
						field: 'salaryStructure'
					},
					{
						title: '地址',
						align: 'center',
						field: 'address'
					},
					{
						title: '发布人',
						align: 'center',
						field: 'createUserName'
					},
					{
						title: '发布时间',
						align: 'center',
						width: 180,
						field: 'createTime',
						// valueFormat: "yyyy-MM-dd"
					},
					{
						title: '面向学院',
						align: 'center',
						width: 180,
						field: 'orgName'
					},
					{
						title: '状态',
						align: 'center',
						width: 120,
						field: 'statusStr'
					},
					{
						title: '审核状态',
						align: 'center',
						width: 120,
						field: 'auditStatusStr',
						render: (h, params) => {
							let typeStr
							switch (params.row.auditflowStatus) {
								case 0: typeStr = 'info'; break
								case 1: typeStr = ''; break
								case 2: typeStr = 'success'; break
								case 3: typeStr = 'danger'; break
							}
							return h(
								'el-tag',
								{props:{ type: typeStr } },
								params.row.auditflowStatusName
							)
						}
					},
					{
						title: '操作',
						type: 'handle',
						width: 120,
						events: [
							{
								code: 'view',
								text: '查看'
							},
							{
								code: 'audit',
								text: '审核',
								rules: rows => {
									return rows.auditable;
								}
							},
							{
								code: 'postResumePageBtn',
								text: '投递情况',
								rules: rows => {
									return rows.status == 1 && rows.auditStatus == 1;
								}
							},
							// {
							// 	code: 'delete',
							// 	text: '删除'
							// }
						]
					}
				]
			};
		}
	}
};
