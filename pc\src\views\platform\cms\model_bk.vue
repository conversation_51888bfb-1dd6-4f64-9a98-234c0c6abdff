<template>
	<div class="content">
		<es-data-table
			ref="table"
			:key="tableCount"
			:row-style="tableRowClassName"
			:full="true"
			:fit="true"
			:thead="thead"
			:toolbar="toolbar"
			:border="true"
			:page="pageOption"
			:url="dataTableUrl"
			:numbers="true"
			:param="params"
			:option-data="optionData"
			close
			form
			@btnClick="btnClick"
			@sort-change="sortChange"
			@submit="hadeSubmit"
		>
			<!--			<el-table-column label="状态" width="180">-->
			<!--				<template slot-scope="scope">-->
			<!--					<i class="el-icon-time"></i>-->
			<!--					<span style="margin-left: 10px">{{ scope.row.date }}</span>-->
			<!--				</template>-->
			<!--			</el-table-column>-->
		</es-data-table>
		<es-dialog
			:title="formTitle"
			:visible.sync="showForm"
			width="60%"
			:close-on-click-modal="false"
			:destroy-on-close="true"
		>
			<es-form
				ref="form"
				:model="formData"
				:contents="formItemList"
				height="500px"
				:genre="2"
				collapse
				@change="inputChange"
				@submit="handleFormSubmit"
				@reset="showForm = false"
			/>
		</es-dialog>
		<es-dialog
			title="删除"
			:visible.sync="showDelete"
			width="20%"
			:close-on-click-modal="false"
			:middle="true"
			height="140px"
		>
			<div>确定要删除该条数据吗</div>
			<div class="btn-box">
				<div class="btn theme" @click="deleteRow">确定</div>
				<div class="btn" @click="showDelete = false">取消</div>
			</div>
		</es-dialog>
	</div>
</template>

<script>
import modelUrl from '@/http/platform/model.js';
const typeDic = [
	{
		value: 'info',
		name: '文章模型'
	},
	{
		value: 'node',
		name: '目录模型'
	}
];
export default {
	data() {
		return {
			dataTableUrl: modelUrl.page,
			treeData: [],
			fixedId: '',
			showForm: false,
			showDelete: false,
			formData: {},
			formTitle: '编辑',
			formItemList: [
				{
					label: '模型名称',
					name: 'name',
					placeholder: '请输入模型名称',
					rules: {
						required: true,
						message: '请输入模型名称',
						trigger: 'blur'
					},
					col: 5
				},
				{
					label: '模型编码',
					name: 'code',
					placeholder: '请输入模型编码',
					event: 'input',
					rules: {
						required: true,
						message: '请输入模型编码',
						trigger: 'blur'
					},
					col: 5
				},
				{
					type: 'select',
					genre: 'button',
					label: '模型类型',
					name: 'type',
					data: typeDic,
					rules: {
						required: true,
						message: '请选择类型',
						trigger: 'blur'
					},
					col: 5
				},
				{
					type: 'radio',
					label: '状态',
					name: 'status',
					'label-key': 'shortName',
					'value-key': 'cciValue',
					sysCode: 'cms_enable_status',
					rules: {
						required: true,
						message: '请选择状态',
						trigger: 'blur'
					},
					col: 5
				}
			],
			editBtn: {
				type: 'submit',
				skin: 'lay-form-btns',
				contents: [
					{
						type: 'primary',
						text: '确定',
						event: 'confirm'
					},
					{
						type: 'reset',
						text: '取消',
						event: 'cancel'
					}
				]
			},
			cancelBtn: {
				type: 'submit',
				skin: 'lay-form-btns',
				contents: [
					{
						type: 'reset',
						text: '取消',
						event: 'cancel'
					}
				]
			},
			tableCount: 1,
			search: {},
			toolbar: [
				{
					type: 'button',
					contents: [
						{
							text: '新增',
							code: 'add',
							type: 'primary'
						}
					]
				},
				{
					type: 'search',
					reset: true,
					contents: [
						{
							type: 'select',
							name: 'type',
							placeholder: '请选择模型类型',
							data: typeDic
						},
						{
							type: 'select',
							name: 'status',
							placeholder: '请选择模型状态',
							'label-key': 'shortName',
							'value-key': 'cciValue',
							sysCode: 'cms_enable_status'
						},
						{
							type: 'text',
							name: 'name',
							placeholder: '请输入模型名称'
						}
					]
				}
			],
			thead: [
				{
					title: '模型名称',
					align: 'center',
					field: 'name'
				},
				{
					title: '模型编码',
					align: 'center',
					field: 'code'
				},
				{
					title: '类型',
					align: 'center',
					field: 'type'
				},
				{
					title: '状态',
					align: 'center',
					field: 'status',
					type: 'radio',
					genre: 'button',
					'label-key': 'shortName',
					'value-key': 'cciValue',
					sysCode: 'cms_enable_status'
				},
				{
					title: '操作',
					type: 'handle',
					width: 180,
					template: '',
					events: [
						{
							code: 'fieldList',
							text: '字段列表'
						},
						{
							code: 'edit',
							text: '编辑'
						},
						{
							code: 'view',
							text: '查看'
						},
						{
							code: 'delete',
							text: '删除'
						}
					]
				}
			],
			optionData: {},
			pageOption: {
				layout: 'total, prev, pager, next, jumper',
				pageSize: 10,
				// hideOnSinglePage: true,
				position: 'right',
				current: 1,
				pageNum: 1
			},
			params: {
				asc: 'false',
				orderBy: 'createTime'
			}
		};
	},
	computed: {},
	watch: {
		dataTableUrl() {
			this.tableCount++;
		}
	},
	created() {},
	mounted() {},
	methods: {
		/**
		 * 表格行高
		 */
		tableRowClassName({ row, rowIndex }) {
			let styleRes = {
				height: '54px !important'
			};
			return styleRes;
		},
		inputChange(key, value) {
		},
		hadeSubmit(data) {
		},
		/**
		 * 操作按钮点击事件
		 * @param {*} res
		 */
		btnClick(res) {
			let code = res.handle.code;
			switch (code) {
				case 'add':
					// 新增
					this.formTitle = '新增';
					this.editModule(this.formItemList);
					this.formData = {};
					this.showForm = true;
					break;
				case 'edit':
					// 编辑
					this.formTitle = '编辑';
					this.fixedId = res.row.id;
					this.editModule(this.formItemList);
					this.$request({
						url: modelUrl.detail + '?id=' + res.row.id,
						method: 'GET'
					}).then(res => {
						if (res.rCode == 0) {
							this.formData = res.results.modelDetail;
							this.showForm = true;
						}
					});
					break;
				case 'view':
					this.readModule(this.formItemList);
					this.formTitle = '查看';
					this.$request({
						url: modelUrl.detail + '?id=' + res.row.id,
						method: 'GET'
					}).then(res => {
						if (res.rCode == 0) {
							this.formData = res.results.modelDetail;
							this.showForm = true;
						}
					});
					break;
				case 'delete':
					this.deleteId = res.row.id;
					this.showDelete = true;
					break;
				case 'fieldList':
					this.$router.push({
						path: 'cmsmodelfield',
						query: { modelId: res.row.id, modelType: res.row.type }
					}); // 主动更改路由界面
					break;
				default:
					break;
			}
		},
		handleClose() {},
		handleFormItemChange() {},
		handleFormSubmit(data) {
			let formData = data;
			console.log(formData);
			delete formData.extMap;
			let url = '';
			if (this.formTitle == '新增') {
				// 新增
				url = modelUrl.add;
			} else {
				url = modelUrl.update;
				formData.id = this.fixedId;
			}
			this.$request({
				url: url,
				data: formData,
				method: 'POST'
			}).then(res => {
				if (res.rCode == 0) {
					this.$message.success('操作成功');
					this.showForm = false;
					this.tableCount++;
					this.formData = {};
				} else {
					this.$message.error(res.msg);
				}
			});
		},
		deleteRow() {
			this.$request({
				url: modelUrl.remove,
				data: { ids: this.deleteId },
				method: 'POST'
			}).then(res => {
				if (res.rCode == 0) {
					this.$message.success('删除成功');
				} else {
					this.$message.error(res.msg);
				}
				this.showDelete = false;
				this.tableCount++;
			});
		},
		/**
		 * 排序变化事件
		 * @param {*} column
		 * @param {*} prop
		 * @param {*} order
		 */
		sortChange(column, prop, order) {
			if (column.order == 'ascending') {
				//升序
				this.params = {
					asc: 'true',
					orderBy: column.prop
				};
			} else if (column.order == 'descending') {
				//降序
				this.params = {
					asc: 'false',
					orderBy: column.prop
				};
			} else {
				//不排序
				this.params = {
					asc: 'false',
					orderBy: 'createTime'
				};
			}
			this.$refs.table.reload();
		},
		/**
		 * 编辑模式
		 */
		editModule(list) {
			for (var i in list) {
				var item = list[i];
				item.readonly = false;
			}
			list.push(this.editBtn);
		},
		/**
		 * 只读模式
		 */
		readModule(list) {
			for (var i in list) {
				var item = list[i];
				item.readonly = true;
			}
			list.push(this.cancelBtn);
		}
	}
};
</script>
<style scoped lang="scss">
@import '../../../assets/style/style.scss';

.content {
	display: flex;
	width: 100%;
	height: 100%;

	::v-deep .es-data-table {
		flex: 1;
		display: flex;
		flex-direction: column;

		.es-data-table-content {
			flex: 1;
			height: 0;
			display: flex;
			flex-direction: column;

			.el-table {
				flex: 1;
				// height: 100% !important;
			}

			.es-thead-border {
				.el-table__header {
					th {
						border-right: 1px solid #e1e1e1;
					}
				}
			}
		}
	}

	::v-deep .el-form-item__label {
		background: none;
		border: 0px solid #c2c2c2;
	}
}

.el-dialog__body {
	.btn-box {
		margin-top: 20px;
		display: flex;
		justify-content: flex-end;

		.btn {
			padding: 5px 10px;
			color: #666;
			border: 1px solid #eee;
			cursor: pointer;
			margin-right: 5px;
			border-radius: 4px;
			// &.theme {
			// 	background: $--color-primary;
			// 	color: #fff;
			// 	border-color: $--color-primary;
			// }
		}
	}
}
</style>
