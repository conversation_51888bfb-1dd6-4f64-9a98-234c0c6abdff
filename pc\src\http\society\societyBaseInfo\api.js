/**
 * 社团相关接口
 */
const interfaceUrl = {
	societyBaseInfoListJson: '/ybzy/society/listJson', // 列表
	societyBaseInfoInfo: '/ybzy/society', // 获取
	societyBaseInfoSave: '/ybzy/society/save', // 保存
	societyBaseInfoUpdate: '/ybzy/society/update', // 修改
	societyBaseInfoAudit: '/ybzy/society/audit', // 审核
	societyBaseInfoSetMain: '/ybzy/society/setMain', // 设置负责人
	societyBaseInfoDeleteIds: '/ybzy/society/deleteBatchIds', // 删除
	societyBaseInfoStudentMapping: '/ybzy/societyStudentMapping/listJson', // 社团成员列表
	societyStudentMappingMemberSelectList: '/ybzy/societyStudentMapping/memberSelectList', // 社团成员下拉框
	societyTypeSelectList: '/ybzy/societyType/selectList', // 社团类型下拉框
	studentSelectList: '/ybzy/platperson/getStudentSelectList', // 学生下拉框
	teacherSelectList: '/ybzy/platperson/getTeacherSelectListFullParam', // 教师下拉框
	majorSelectList: '/ybzy/platmajor/getMajorSelectList', // 专业下拉框
	collegeSelectList: '/ybzy/platmajor/getCollegeSelectList', // 学院下拉框
	orgSelectList: '/ybzy/platmajor/getOrgSelectList' // 机构下拉框
};
export default interfaceUrl;
