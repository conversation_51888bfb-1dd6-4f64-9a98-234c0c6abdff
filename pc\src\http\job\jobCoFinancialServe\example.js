// 渲染示例, 自行删除不需要加入提交此文件
export default {
    computed: {
        // 表单示例
        formItemList() {
            return [
                // 必填校验，以及带tips提示的label
                {
                    name: 'accountName',
                    // 带tips提示的label
                    label: {
                        text: '接收人标识ID',
                        tips: '用于联系用户的标识ID, 可为手机号、邮箱、身份ID等'
                    },
                    value: '',
                    col: 12,
                    // 必填校验，更多参考文档
                    rules: {
                        required: true,
                        message: '请输入配置名称'
                    }
                },
                {
                    type: 'select',
                    name: 'handlerType',
                    label: '平台类型',
                    value: '',
                    col: 6,
                    readonly: true,
                    data: this.extraData.sendType,
                    'value-key': 'rawName',
                    'label-key': 'text'
                },
                // 自定义数据下拉树， data可改为url 指定树数据地址
                {
                    type: 'select',
                    data: this.subsystemData,
                    labelKey: 'name',
                    valueKey: 'id',
                    tree: true,
                    parentCheck: false,
                    name: 'systemCode',
                    label: '系统编码',
                    value: '',
                    col: 6
                },
                // 基础数据选择树，更多类型参考文档
                {
                    type: 'selector',
                    types: ['enterprise'],
                    name: 'accountGroupObj',
                    label: '分组',
                    multiple: false,
                    value: '',
                    col: 6
                },
                // 状态 1启用 0禁用
                {
                    type: 'switch',
                    name: 'useStatus',
                    label: '状态',
                    value: 1,
                    col: 6,
                    data: [
                        {
                            value: 1,
                            name: '启用'
                        },
                        {
                            value: 0,
                            name: '禁用'
                        }
                    ]
                },
                // 表单带列表
                {
                    title: '消息模版编码映射',
                    contents: [
                        {
                            name: 'templateIdMap',
                            type: 'table',
                            form: true,
                            editable: true,
                            height: '200',
                            thead: [
                                {
                                    title: '系统模版编码',
                                    field: 'key',
                                    type: 'text'
                                },
                                {
                                    title: '平台模版编码',
                                    field: 'val',
                                    type: 'text'
                                }
                            ],
                            value: '',
                            col: 12
                        }
                    ]
                }
            ];
        },
        // 列表示例
        table() {
            return {
                toolbar: [
                    {
                        type: 'button',
                        contents: [
                            {
                                text: '新增',
                                type: 'primary'
                            }
                        ]
                    },
                    {
                        type: 'search',
                        reset: true,
                        contents: [
                            {
                                name: 'accountName_like',
                                placeholder: '配置名称',
                                label: '配置名称',
                                col: 6
                            },
                            {
                                type: 'date',
                                col: 6,
                                name: 'updateTime_begin',
                                label: '更新开始时间',
                                placeholder: '更新开始时间'
                            },
                            {
                                type: 'date',
                                col: 6,
                                name: 'updateTime_end',
                                label: '更新结束时间',
                                placeholder: '更新结束时间'
                            }
                        ]
                    }
                ],
                thead: [
                    {
                        title: '平台名称',
                        field: 'handlerName',
                        align: 'center',
                        width: 120,
                        render: (h, params) => {
                            return h('p', {}, this.formartHandlerName(params.row.handlerName));
                        }
                    },
                    {
                        title: '系统名称',
                        align: 'left',
                        field: 'systemCode',
                        width: 150,
                        render: (h, params) => {
                            return h(
                                'el-popover',
                                {
                                    props: {
                                        placement: 'bottom',
                                        trigger: 'hover',
                                        content: '系统编码: ' + params.row.systemCode
                                    }
                                },
                                [h('p', { slot: 'reference' }, params.row.systemName)]
                            );
                        }
                    },
                    {
                        title: '分组',
                        field: 'accountGroup',
                        align: 'center',
                        width: 150,
                        render: (h, params) => {
                            return h(
                                'el-popover',
                                {
                                    props: {
                                        placement: 'bottom',
                                        trigger: 'hover',
                                        content: '分组ID: ' + params.row.accountGroup
                                    }
                                },
                                [
                                    h(
                                        'p',
                                        { slot: 'reference' },
                                        params.row.accountGroupName == null ? '--' : params.row.accountGroupName
                                    )
                                ]
                            );
                        }
                    },
                    {
                        title: '状态',
                        field: 'useStatus',
                        align: 'center',
                        width: 50,
                        render: (h, params) => {
                            return h(
                                'el-tag',
                                { slot: 'reference', props: { type: params.row.useStatus == 1 ? '' : 'danger' } },
                                params.row.useStatus == 1 ? '启用' : '禁用'
                            );
                        }
                    }
                ]
            };
        }
    }
};
