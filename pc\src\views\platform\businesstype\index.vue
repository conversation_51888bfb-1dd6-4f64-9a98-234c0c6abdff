<template>
    <div class="content">
        <es-data-table ref="table" :row-style="tableRowClassName" :full="true" :fit="true" :thead="thead" :toolbar="toolbar"
            :border="true" :page="pageOption" :url="dataTableUrl" :numbers="true" @btnClick="btnClick"
            @sort-change="sortChange" :param="params" @submit="hadeSubmit" close>
        </es-data-table>
        <es-dialog :title="formTitle" :visible.sync="showForm" width="60%" height="700px" :close-on-click-modal="false"
            :destroy-on-close="true">
            <es-form ref="form" :model="formData" :contents="formItemList" height="100%" :genre="2" collapse
                @change="inputChange" @submit="handleFormSubmit" @reset="showForm = false" />
        </es-dialog>
        <es-dialog title="删除" :visible.sync="showDelete" width="20%" :close-on-click-modal="false" :middle="true"
            height="140px">
            <div>确定要删除该条数据吗</div>
            <div class="btn-box">
                <div class="btn theme" @click="deleteRow">确定</div>
                <div class="btn" @click="showDelete = false">取消</div>
            </div>
        </es-dialog>
    </div>
</template>

<script>
import interfaceUrl from '@/http/platform/businesstype.js';
import SnowflakeId from "snowflake-id";
export default {
    data() {
        return {
            ownId: '',
            dataTableUrl: interfaceUrl.listJson,
            showForm: false,
            showDelete: false,
            formData: {},
            formTitle: '编辑',
            roleList: [],
            certStuffList: [],
            editBtn: {
                type: 'submit',
                skin: 'lay-form-btns',
                contents: [
                    {
                        type: 'primary',
                        text: '确定',
                        event: 'confirm'
                    },
                    {
                        type: 'reset',
                        text: '取消',
                        event: 'cancel'
                    },
                ]
            },
            cancelBtn: {
                type: 'submit',
                skin: 'lay-form-btns',
                contents: [
                    {
                        type: 'reset',
                        text: '取消',
                        event: 'cancel'
                    },
                ]
            },
            toolbar: [
                {
                    type: 'button',
                    contents: [
                        {
                            text: '新增',
                            code: 'add',
                            type: 'primary'
                        }
                    ]
                },
                {
                    type: 'search',
                    contents: [
                        {
                            type: 'text',
                            name: 'keyword',
                            placeholder: '关键字查询'
                        },
                    ]
                }
            ],
            thead: [
                {
                    title: '标题',
                    align: 'left',
                    field: 'title',
                    sortable: 'custom',
					showOverflowTooltip: true,
                },
                {
                    title: '名称',
                    align: 'left',
                    field: 'name',
                    sortable: 'custom',
					showOverflowTooltip: true,
                },
                {
                    title: '编码',
                    align: 'left',
                    field: 'code',
                    sortable: 'custom',
					showOverflowTooltip: true,
                },
                {
                    width: '100px',
                    title: '排序号',
                    align: 'center',
                    field: 'sortNum',
                    sortable: 'custom',
					showOverflowTooltip: true,
                },
                {
                    title: '更新时间',
                    align: 'center',
                    field: 'createTime',
                    sortable: 'custom',
					showOverflowTooltip: true,
                },
                {
                    title: '更新人',
                    align: 'center',
                    field: 'createUserName',
                    sortable: 'custom',
					showOverflowTooltip: true,
                },
                {
                    title: '操作',
                    type: 'handle',
                    width: 180,
                    template: '',
                    events: [
                        {
                            code: 'edit',
                            text: '编辑'
                        },
                        {
                            code: 'view',
                            text: '查看'
                        },
                        {
                            code: 'delete',
                            text: '删除'
                        },
                    ]
                }
            ],
            pageOption: {
                layout: 'total, prev, pager, next, jumper',
                pageSize: 10,
                // hideOnSinglePage: true,
                position: 'right',
                current: 1,
                pageNum: 1
            },
            params: {
                asc: "false",
                orderBy: "sortNum asc,createTime"
            },
        };
    },
    computed: {
        formItemList() {
            return [
                {
                    label: '标题',
                    name: 'title',
                    placeholder: '请输入标题',
                    event: 'multipled',
                    rules: {
                        required: true,
                        message: '请输入标题',
                        trigger: 'blur'
                    },
                    verify: 'required',
                    col: 10
                },
                {
                    label: '名称',
                    name: 'name',
                    placeholder: '请输入名称',
                    event: 'multipled',
                    rules: {
                        required: true,
                        message: '请输入名称',
                        trigger: 'blur'
                    },
                    verify: 'required',
                    col: 5
                },
                {
                    label: '编码',
                    name: 'code',
                    placeholder: '请输入编码',
                    event: 'input',
                    rules: {
                        required: true,
                        message: '请输入编码',
                        trigger: 'blur'
                    },
                    verify: 'required',
                    col: 5
                },
                {
                    type: 'select',
                    placeholder: '请选择关联角色',
                    label: '关联角色',
                    placeholder: '',
                    name: 'roleIds',
                    event: 'multipled',
                    multiple: true,
                    rules: {
                        required: true,
                        message: '请选择关联角色',
                        trigger: 'blur'
                    },
                    data: this.roleList,
                    verify: 'required',
                    col: 10
                },
                {
                    type: 'select',
                    placeholder: '请选择资质认证项',
                    label: '资质认证项',
                    placeholder: '',
                    name: 'certStuffIds',
                    event: 'multipled',
                    multiple: true,
                    rules: {
                        required: true,
                        message: '请选择资质认证项',
                        trigger: 'blur'
                    },
                    data: this.certStuffList,
                    verify: 'required',
                    col: 10
                },
                {
                    label: '互斥编码',
                    name: 'mutualExclusion',
                    placeholder: '请输入互斥编码',
                    event: 'input',
                    col: 5
                },
                {
                    label: '排序号',
                    name: 'sortNum',
                    placeholder: '请输入排序号',
                    type: 'number',
                    controls: false,
                    rules: {
                        required: true,
                        message: '请输入排序号',
                        trigger: 'blur'
                    },
                    verify: 'required',
                    col: 5
                },
                {
                    name: 'remark',
                    label: '说明',
                    placeholder: '请输入说明',
                    type: 'textarea',
                    col: 10
                },
                {
                    label: '上传logo',
                    name: 'logoTemp',
                    type: 'attachment',
                    code: 'platform_enp_type_logo',
                    ownId: this.ownId, // 业务id
                    // size: 100,
                    portrait: true,
                    readonly: false,
                    param: {
                        'isShowPath': true
                    },
                },
            ]
        },
    },
    watch: {

    },
    created() {
    },
    mounted() {

    },
    methods: {
        initOptionData() {
            this.$request({
                url: interfaceUrl.roleList,
                method: 'GET'
            }).then(res => {
                if (res.rCode == 0) {
                    this.roleList = res.results;
                }
            });

            this.$request({
                url: interfaceUrl.certStuffList,
                method: 'GET'
            }).then(res => {
                if (res.rCode == 0) {
                    this.certStuffList = res.results;
                }
            });

        },
        /**
        * 表格行高
        */
        tableRowClassName({ row, rowIndex }) {
            let styleRes = {
                "height": "54px !important"
            }
            return styleRes

        },
        inputChange(key, value) {
            if (key == 'phone') {
            }
        },
        hadeSubmit(data) {

        },
        /**
         * 操作按钮点击事件
         * @param {*} res 
         */
        btnClick(res) {
            let code = res.handle.code;
            switch (code) {
                case 'add':
                    // 新增
                    this.formTitle = '新增';
                    this.editModule(this.formItemList);
                    this.initOptionData();
                    const snowflake = new SnowflakeId();
                    this.formData = { id: snowflake.generate(), sortNum: 1, logoTemp: null };
                    this.ownId = this.formData.id;
                    this.showForm = true;
                    break;
                case 'edit':
                    // 编辑
                    this.formTitle = '编辑';
                    this.ownId = res.row.id;
                    this.editModule(this.formItemList);
                    this.initOptionData();
                    this.$request({
                        url: interfaceUrl.info + '/' + res.row.id,
                        method: 'GET'
                    }).then(res => {
                        if (res.rCode == 0) {
                            this.formData = res.results;
                            if(undefined !== this.formData.logo && null !== this.formData.logo){
                                this.formData.logoTemp = '/main2/mecpfileManagement/previewAdjunct?adjunctId='+this.formData.logo;
                            }
                            this.showForm = true;
                        }
                    });
                    break;
                case 'view':
                    this.formTitle = '查看';
                    this.ownId = res.row.id;
                    this.readModule(this.formItemList);
                    this.$request({
                        url: interfaceUrl.info + '/' + res.row.id,
                        method: 'GET'
                    }).then(res => {
                        if (res.rCode == 0) {
                            this.formData = res.results;
                            if(undefined !== this.formData.logo && null !== this.formData.logo){
                                this.formData.logoTemp = '/main2/mecpfileManagement/previewAdjunct?adjunctId='+this.formData.logo;
                            }
                            this.showForm = true;
                        }
                    });
                    break;
                case 'delete':
                    this.deleteId = res.row.id;
                    this.showDelete = true;
                    break;
                default:
                    break;
            }
        },
        handleClose() { },
        handleFormItemChange() {

        },
        handleFormSubmit(data) {
            let formData = data;
            delete formData.extMap;
            let url = "";
            if (this.formTitle == '新增') {
                url = interfaceUrl.save;
            } else {

                let roleIdList = [];
                formData.roleIds.forEach((elem, index) => {
                    if (undefined != elem && undefined != elem.value) {
                        roleIdList.push(elem.value);
                    }
                });
                if (roleIdList.length > 0) {
                    formData.roleIds = roleIdList;
                }


                let certStuffIdList = [];
                formData.certStuffIds.forEach((elem, index) => {
                    if (undefined != elem && undefined != elem.value) {
                        certStuffIdList.push(elem.value);
                    }
                });
                if (certStuffIdList.length > 0) {
                    formData.certStuffIds = certStuffIdList;
                }

                url = interfaceUrl.update;
            }

            if (undefined != formData.logoTemp) {
                formData.logo = formData.logoTemp;
            }

            this.$request({
                url: url,
                data: formData,
                method: 'POST'
            }).then(res => {
                if (res.rCode == 0) {
                    this.$message.success('操作成功');
                    this.showForm = false;
                    this.formData = {};
                    this.$refs.table.reload();
                } else {
                    this.$message.error(res.msg);
                }
            });
        },
        deleteRow() {
            this.$request({
                url: interfaceUrl.deleteBatchIds,
                data: { ids: this.deleteId },
                method: 'POST'
            }).then(res => {
                if (res.rCode == 0) {
                    this.$refs.table.reload();
                    this.$message.success('删除成功');
                } else {
                    this.$message.error(res.msg);
                }
                this.showDelete = false;
            });
        },
        /**
         * 排序变化事件
         * @param {*} column 
         * @param {*} prop 
         * @param {*} order 
         */
        sortChange(column, prop, order) {
            if (column.order == 'ascending') {//升序
                this.params = {
                    asc: "true",
                    orderBy: column.prop
                }

            } else if (column.order == 'descending') {//降序 
                this.params = {
                    asc: "false",
                    orderBy: column.prop
                }
            } else { //不排序
                this.params = {
                    asc: "false",
                    orderBy: "sortNum asc,createTime"
                }
            }
            this.$refs.table.reload()

        },
        /**
         * 编辑模式
         */
        editModule(list) {
            for (var i in list) {
                var item = list[i];
                item.readonly = false;
            }
            list.push(this.editBtn);
        },
        /**
         * 只读模式
         */
        readModule(list) {
            for (var i in list) {
                var item = list[i];
                item.readonly = true;
            }
            list.push(this.cancelBtn);
        },
    }
};
</script>
<style scoped lang="scss">
@import '../../../assets/style/style.scss';

.content {
    display: flex;
    width: 100%;
    height: 100%;

    ::v-deep .es-data-table {
        flex: 1;
        display: flex;
        flex-direction: column;

        .es-data-table-content {
            flex: 1;
            height: 0;
            display: flex;
            flex-direction: column;

            .el-table {
                flex: 1;
                // height: 100% !important;
            }

            .es-thead-border {
                .el-table__header {
                    th {
                        border-right: 1px solid #E1E1E1;
                    }
                }
            }
        }
    }

    ::v-deep .el-form-item__label {
        background: none;
        border: 0px solid #c2c2c2;
    }
}

.el-dialog__body {
    .btn-box {
        margin-top: 20px;
        display: flex;
        justify-content: flex-end;

        .btn {
            padding: 5px 10px;
            color: #666;
            border: 1px solid #eee;
            cursor: pointer;
            margin-right: 5px;
            border-radius: 4px;
            // &.theme {
            // 	background: $--color-primary;
            // 	color: #fff;
            // 	border-color: $--color-primary;
            // }
        }
    }


}
</style>
