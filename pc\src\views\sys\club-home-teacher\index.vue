<!--
 @desc:社团管理系统-教职工
 @author: 
 @date: 2023/11/13
 -->
<template>
	<div class="main">
		<div class="card-box">
			<mini-card v-for="(item, index) in card" :key="index" :card-data="item" />
		</div>
		<titile-card title="社团统计" :img-url="require('@/assets/images/sys/sttj.png')">
			<template #content>
				<div class="table-box">
					<es-data-table
						ref="table"
						:thead="thead"
						:page="true"
						:url="tableUrl"
						:toolbar="toolbar"
						:response="func"
						method="get"
						style="width: 100%"
						height="400px"
						@change="pageSizeChange"
						@current="pageCurrentChange"
					></es-data-table>
				</div>
			</template>
		</titile-card>
		<div class="e-box">
			<titile-card title="活动统计" :img-url="require('@/assets/images/sys/sthdtj.png')">
				<template #content>
					<div class="is-pie">
						<e-pie id="hdtj" :series-data="hdtjData" />
					</div>
				</template>
			</titile-card>
			<titile-card title="人数统计" :img-url="require('@/assets/images/sys/rstj.png')">
				<template #content>
					<div class="is-pie">
						<e-line id="rstj" :series-data="rstjData" />
					</div>
				</template>
			</titile-card>
			<titile-card title="收支统计" :img-url="require('@/assets/images/sys/sztj.png')">
				<template #filtre>
					<el-date-picker
						v-model="szjlMonth"
						type="month"
						value-format="yyyyMM"
						placeholder="选择月"
						size="mini"
						class="date-box"
						@change="sztjDateChange"
						:editable="false"
						:clearable="false"
					></el-date-picker>
				</template>
				<template #content>
					<div class="is-pie">
						<e-bar id="szjl" :series-data="sztjData" />
					</div>
				</template>
			</titile-card>
		</div>
	</div>
</template>

<script>
import api from '@/http/society/societyBaseInfo/api';
import MiniCard from '@/components/show/mini-card.vue';
import TitileCard from '@/components/show/titile-card.vue';
import EPieBase from '@/components/echarts/pie-base.vue';
import ELine from '@/components/echarts/line.vue';
import EBar from '@/components/echarts/bar.vue';
import EPie from '@/components/echarts/pie.vue';

export default {
	components: { EPie, MiniCard, TitileCard, EPieBase, ELine, EBar },

	data() {
		return {
			szjlMonth: '', // 收支统计选择时间
			card: [
				{
					img: require('@/assets/images/sys/gh_icon1.png'),
					title: '社团总数',
					unit: '个',
					width: '24.5%',
					num: '0'
				},
				{
					img: require('@/assets/images/sys/gh_icon2.png'),
					title: '社团总人数',
					unit: '人',
					width: '24.5%',
					num: '0'
				},
				{
					img: require('@/assets/images/sys/gh_icon3.png'),
					title: '社团总收入',
					unit: '元',
					width: '24.5%',
					num: '0'
				},
				{
					img: require('@/assets/images/sys/gh_icon4.png'),
					title: '社团总支出',
					unit: '元',
					width: '24.5%',
					num: '0'
				}
			], //统计部分卡片展示内容
			tableUrl: '/ybzy/society/home/<USER>', //表格请求接口

			thead: [
				{
					title: '学院',
					field: 'collegeName',
					fixed: true
				},
				{
					title: '社团名称',
					field: 'name',
					fixed: false
				},
				{
					title: '社团人数',
					field: 'memberNum',
					width: 80,
					fixed: false
				},
				// {
				// 	title: '创办活动数',
				// 	field: 'fillUsername',
				// 	fixed: false
				// },
				{
					title: '社长',
					field: 'principalName',
					width: 120,
					fixed: false
				},
				{
					title: '联系电话',
					field: 'principalTelephone',
					fixed: false
				},
				{
					title: '社团简介',
					field: 'introduction',
					width: '200px',
					showOverflowTooltip: true,
					fixed: false
				}
			], //表格表头展示内容
			sztjData: [], //收支统计图表数据
			rstjData: [], //人数统计图表数据
			hdtjData: [
				{
					name: '已预约活动',
					value: '10'
				},
				{
					name: '待预约活动',
					value: '14'
				},
				{
					name: '预约中活动',
					value: '20'
				}
			], // 活动统计图表数据
			collegeList: [] //学院数据
		};
	},
	computed: {
		toolbar() {
			return [
				{
					type: 'search',
					reset: true,
					contents: [
						{
							type: 'text',
							label: '关键字',
							name: 'keyword',
							placeholder: '请输入关键字'
						},
						{
							type: 'select',
							label: '二级学院',
							name: 'collegeId',
							placeholder: '请选择二级学院',
							data: this.collegeList
						}
					]
				}
			]; //表格搜索内容
		}
	},
	mounted() {
		let now = new Date();
		// 获取当前月份如202409
		let month = '09';
		if (now.getMonth() + 1 < 10) {
			month = '0' + (now.getMonth() + 1);
		} else {
			month = now.getMonth() + 1;
		}
		this.szjlMonth = now.getFullYear() + month;
		this.getCardData();
		this.getCollege();
		this.getActivityPieData();
		this.getMemberLineData();
		this.getIncExoBarData();
	},
	methods: {
		pageCurrentChange(current) {
			console.log('current', current);
		},
		pageSizeChange(size) {
			console.log('size', size);
		},
		func(res) {
			let { data } = res;
			let param = {};
			for (let i in data) {
				if (i === 'startdate') {
					param['start'] = data[i][0];
					param['end'] = data[i][1];
				} else {
					param[i] = data[i];
				}
			}
			return param;
		},
		// 顶部综合数据
		getCardData() {
			this.$.ajax({
				method: 'GET',
				url: '/ybzy/society/home/<USER>'
			}).then(data => {
				if (data.success) {
					this.card[0].num = String(data.results.societyCount);
					this.card[1].num = String(data.results.memberCount);
					this.card[2].num = String(data.results.societyIn);
					this.card[3].num = String(data.results.societyEx);
					//console.info('打印原始数据:', data.results);
					//console.info('打印收入支出柱状图数据', ArrList);
				} else {
					this.$message.error(data.msg);
				}
			});
		},
		//获取学院数据
		getCollege() {
			this.$request({
				url: api.collegeSelectList,
				method: 'POST'
			}).then(result => {
				if (result.success) {
					this.collegeList = result.results;
				} else {
					this.$message.error('操作失败');
				}
			});
		},
		// 活动统计饼图
		getActivityPieData() {
			this.$.ajax({
				method: 'GET',
				url: '/museumcloud/activity/activityItem/front/activityStatusCount'
			}).then(data => {
				if (data.success) {
					let activityList = [];
					data.data.list.forEach(el => {
						activityList.push({ name: el.name, value: el.value });
					});
					this.hdtjData = activityList;
				} else {
					this.$message.error(data.msg);
				}
			});
		},
		// 人数统计柱状图
		getMemberLineData() {
			this.$.ajax({
				method: 'GET',
				url: '/ybzy/society/home/<USER>'
			}).then(data => {
				if (data.success) {
					let yAxis0 = data.results?.memberNums;
					let sum = yAxis0.reduce(function (prev, next, index, array) {
						return prev + next;
					});
					let yAxis1 = [...yAxis0];
					yAxis1.forEach((item, index) => {
						yAxis1[index] = ((item / sum) * 100).toFixed(2); // 取两位小数
					});
					this.rstjData.push(data.results.xAxis);
					this.rstjData.push(yAxis1);
					this.rstjData.push(yAxis0);
				} else {
					this.$message.error(data.msg);
				}
			});
		},
		// 收支统计柱状图
		getIncExoBarData() {
			this.$.ajax({
				method: 'GET',
				params: { date: this.szjlMonth },
				url: '/ybzy/society/home/<USER>'
			}).then(data => {
				if (data.success) {
					this.sztjData = [];
					this.sztjData.push(data.results.xAxis);
					this.sztjData.push(data.results.income);
					this.sztjData.push(data.results.expend);
				} else {
					this.$message.error(data.msg);
				}
			});
		},
		// 收支统计查询时间改变事件
		sztjDateChange() {
			this.getIncExoBarData();
		}
	}
};
</script>
<style lang="scss" scoped>
@import '@ast/style/public.scss';

.main {
	background: #f0f2f5;
	padding: 12px;
	overflow: auto;
	height: 100%;
	width: 100%;
	& > * {
		background: #fff;
	}
	header {
		@include flexBox(space-between);
		padding: 6px 20px;
		border-radius: 8px;
		font-size: 20px;
		font-weight: 550;
	}
	.card-box {
		@include flexBox(space-between);
		width: 100%;
		margin: 0 0 12px;
		background: #f0f2f5;

		.card {
			width: 24.5%;
		}
	}
	.tabs {
		// width: 300px;
		@include flexBox();
		p {
			margin-left: 10px;
			font-weight: 550;
			cursor: pointer;
		}
		.is-active {
			color: #0377e8;
		}
	}
	.e-box {
		@include flexBox(space-between);
		background: #f0f2f5;
		margin-top: 12px;
		width: 100%;
		& > * {
			background: #fff;
		}
		.title-card {
			width: 32.8%;
		}
	}
	.date-box {
		width: 100px;
		::v-deep .el-input__inner {
			padding-right: 0 !important;
		}
	}
	.is-pie {
		width: 100%;
		height: 400px;
	}
}
</style>
