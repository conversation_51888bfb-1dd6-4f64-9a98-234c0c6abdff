//接口地址
const api = {
	getOriList: '/ybzy/sys/common/getOrgList', //获取学校部门列表
	listJson: '/ybzy/platPurchaseApply/listJson', //获取学校部门列表
	myListJson: '/ybzy/platPurchaseApply/myListJson', //获取我的审核列表
	// verifyListJson: '/ybzy/platPurchaseApply/verifyListJson',
	exportData: '/ybzy/platPurchaseApply/exportData', // 导出数据
	info: '/ybzy/platPurchaseApply/info', //获取申请详情
	save: '/ybzy/platPurchaseApply/save', //保存申请详情
	update: '/ybzy/platPurchaseApply/update', //修改申请详情
	removeById: '/ybzy/platPurchaseApply/removeById',
	// subById: '/ybzy/platPurchaseApply/subById',
	// bcById: '/ybzy/platPurchaseApply/bcById',
	// verify: '/ybzy/platPurchaseApply/verify',
	// platmajorGetCollegeSelectList: '/ybzy/platmajor/getCollegeSelectList',
	auditListJson: '/ybzy/platPurchaseApply/auditListJson', //获取审核列表
	getPresidentlistJson: '/ybzy/platPurchaseApply/getPresidentlistJson', //获取审核人员列表
	platPurchaseAudit: '/ybzy/platPurchaseAudit/audit', //审核申请
	platPurchaseAuditlist: '/ybzy/platPurchaseAudit/purchaseAuditList', // 获取审核记录
	replenish: '/ybzy/platPurchaseReplenish/replenish', //提交补充资料
	purchaseAuditList: '/ybzy/platPurchaseReplenish/purchaseAuditList', //获取补充资料详情
	platPurchaseReplenishUpdate: '/ybzy/platPurchaseReplenish/update' //修改补充资料
};
export default api;
