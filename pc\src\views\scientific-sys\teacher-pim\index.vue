<template>
	<div v-loading="loading" class="content">
		<es-data-table
			ref="table"
			:row-style="tableRowClassName"
			:thead="thead"
			:toolbar="toolbar"
			:border="true"
			:page="pageOption"
			:url="basics.dataTableUrl"
			:numbers="true"
			:immediate="true"
			:param="params"
			close
			form
			@btnClick="btnClick"
		></es-data-table>
		<es-dialog
			v-if="showForm"
			:title="formTitle"
			:visible.sync="showForm"
			:drag="false"
			width="1200px"
			height="72%"
			:close-on-click-modal="false"
			:destroy-on-close="true"
		>
			<resumeView
				v-if="showForm"
				:title="formTitle"
				:form-id="formId"
				:basics="basics"
				@close="
					() => {
						$refs.table.reload();
						showForm = false;
					}
				"
			></resumeView>
		</es-dialog>
	</div>
</template>

<script>
import { host } from '@/../config/config';
import resumeView from './components/view.vue';
export default {
	components: { resumeView },
	data() {
		return {
			basics: {
				dataTableUrl: '/ybzy/projectMemberInfoTem/listJson', // 列表接口
				info: '/ybzy/projectMemberInfoTem/info', // 详情接口
				save: '/ybzy/projectMemberInfoTem/save', // 新增
				edit: '/ybzy/projectMemberInfoTem/update', // 修改
				delete: '/ybzy/projectMemberInfoTem/deleteById', // 删除 removeById逻辑删除 deleteById真删
				revoke: '', // 撤销
				isScienceManager: '/ybzy/projectMemberInfoTem/isScienceManager', // 判断是否科技处管理员
				download: '/ybzy/projectMemberInfoTem/export', // 导出
				selectList: '/ybzy/projectMultilevelDict/selectList' // 多级学科
			},
			isScienceManagerRole: false,
			loading: false,
			showForm: false,
			params: {
				orderBy: 'createTime',
				asc: 'false' // true 升序，false 降序
			},
			// toolbar: [
			// 	{
			// 		type: 'button',
			// 		contents: [
			// 			{
			// 				text: '新增',
			// 				code: 'add',
			// 				icon: 'el-icon-circle-plus-outline',
			// 				type: 'primary'
			// 			},
			// 			{
			// 				text: '导出',
			// 				exportXls: true,
			// 				icon: 'es-icon-daochu',
			// 				type: 'primary',
			// 				event: () => {
			// 					const params = { ...this.params, ...this.selectParams };
			// 					const url = host + this.basics.download + '?' + qs.stringify(params);
			// 					window.open(url, '_self');
			// 				}
			// 			}
			// 		]
			// 	},
			// 	{
			// 		type: 'search',
			// 		contents: [{ type: 'text', name: 'keyword', placeholder: '请输入关键字查询' }]
			// 	}
			// ],

			pageOption: {
				layout: 'total, prev, pager, next, jumper',
				pageSize: 20,
				hideOnSinglePage: false,
				position: 'center',
				current: 1,
				pageNum: 1
			},
			formTitle: '查看',
			formId: '',
			cmsmodelCodeObj: {}, // 文章模型编码
			codesObj: {} //批量数据字典
		};
	},
	computed: {
		thead() {
			return [
				{
					title: '人员类型',
					align: 'center',
					showOverflowTooltip: true,
					field: 'typeTxt'
				},
				{
					title: '姓名',
					align: 'center',
					showOverflowTooltip: true,
					field: 'name'
				},
				{
					title: '性别',
					align: 'center',
					field: 'sexTxt'
				},
				{
					title: '教工号',
					align: 'center',
					showOverflowTooltip: true,
					field: 'number'
				},
				{
					title: '部门/学院',
					align: 'center',
					showOverflowTooltip: true,
					field: 'orgName'
				},
				{
					title: '岗位类型',
					align: 'center',
					showOverflowTooltip: true,
					field: 'postTypeTxt'
				},
				{
					title: '研究方向',
					align: 'center',
					field: 'researchDirection',
					showOverflowTooltip: true
				},
				{
					title: '是否在编',
					align: 'center',
					field: 'isPermanentStaffTxt'
				},
				{
					title: '操作',
					type: 'handle',
					template: '',
					width: 180,
					align: 'center',
					events: [
						{ code: 'view', text: '查看', icon: 'el-icon-view' },
						{ code: 'view', text: '编辑', icon: 'el-icon-edit' },
						{ code: 'view', text: '删除', icon: 'el-icon-delete' }
					]
				}
			];
		},
		toolbar() {
			return [
				{
					type: 'button',
					contents: [
						{
							text: '新增',
							code: 'add',
							icon: 'el-icon-circle-plus-outline',
							type: 'primary'
						},
						{
							text: '导出',
							exportXls: true,
							icon: 'es-icon-daochu',
							hide: !this.isScienceManagerRole,
							type: 'primary',
							event: () => {
								// const params = { ...this.params, ...this.selectParams };
								// const url = host + this.basics.download + '?' + qs.stringify(params);
								const url = host + this.basics.download;
								window.open(url, '_self');
							}
						}
					]
				},
				{
					type: 'search',
					contents: [{ type: 'text', name: 'keyword', placeholder: '请输入关键字查询' }]
				}
			];
		}
	},
	created() {
		this.$request({
			url: this.basics.isScienceManager,
			method: 'GET'
		}).then(res => {
			if (res.rCode == 0) {
				this.isScienceManagerRole = res.results === 'ONE';
			}
		});
		const codes = 'project_type,project_cooperation_type';
		this.$utils.findSysCodeList(codes).then(res => {
			this.codesObj = res;
		});
	},
	methods: {
		//表格行高
		tableRowClassName({ row, rowIndex }) {
			let styleRes = { height: '54px !important' };
			return styleRes;
		},
		inputChange(key, value) {},
		//操作按钮点击事件
		btnClick(res) {
			let text = res.handle.text;
			this.formTitle = text;
			this.formId = res?.row?.id || '';
			switch (text) {
				case '查看':
					this.showForm = true;
					break;
				case '编辑':
					this.showForm = true;
					break;
				case '新增':
					this.showForm = true;
					break;
				case '删除':
					this.$confirm('确定删除该数据？', '提示', {
						confirmButtonText: '确定',
						cancelButtonText: '取消',
						type: 'warning'
					}).then(() => {
						this.$request({
							url: this.basics.delete,
							data: {
								id: this.formId
							},
							method: 'POST'
						}).then(res => {
							if (res.rCode === 0) {
								this.$message.success(res.msg);
								this.$refs.table.reload();
							} else {
								this.$message.error(res.msg);
							}
						});
					});
					break;
				default:
					break;
			}
		},

		refreshData() {
			this.$refs.table.reload();
		}
	}
};
</script>

<style scoped lang="scss">
.content {
	width: 100%;
	height: 100%;
}
</style>
