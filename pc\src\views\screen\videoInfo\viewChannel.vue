<template>
	<div class="view-channel">
		<es-data-table
			ref="dataTable"
			class="table-box"
			v-bind="table"
			:table="table"
			:border="true"
			:full="true"
			:fit="true"
			:param="params"
			form
			@selection-change="handleSelectionChange"
			@btnClick="btnClick"
			@success="successAfter"
		>
			<!-- 表格内容部分 -->
			<es-dialog
				v-if="showForm"
				:title="formTitle"
				:visible.sync="showForm"
				:close-on-click-modal="false"
				:middle="true"
				:drag="false"
				width="450px"
				height="400px"
			>
				<es-form
					ref="form"
					:model="formData"
					:contents="formItemList"
					:genre="2"
					collapse
					:readonly="formTitle === '查看'"
					@change="handleFormItemChange"
					@submit="handleFormSubmit"
					@click="handleFormAudit"
					@reset="showForm = false"
				/>
			</es-dialog>
		</es-data-table>
	</div>
</template>

<script>
import interfaceUrl from '@/http/screen/videoInfo/api';
import { v4 as uuidv4 } from 'uuid';

export default {
	name: 'ViewChannel',
	props: {
		deviceId: {
			type: String,
			default: ''
		},
		deviceCode: {
			type: String,
			default: ''
		}
	},
	data() {
		return {
			ownId: '',
			showForm: false,
			showDelete: false,
			formData: {},
			extraData: {},
			formTitle: '编辑',

			showChannel: false //通道弹窗
		};
	},
	computed: {
		params() {
			return {
				asc: 'false',
				orderBy: 'createTime',
				deviceId: this.deviceId,
				deviceCode: this.deviceCode
			};
		},
		// 表单配置
		formItemList() {
			return [
				{
					label: '通道名称',
					name: 'channelName',
					placeholder: '请输入通道名称',
					event: 'multipled',
					rules: {
						required: true,
						message: '请输入通道名称',
						trigger: 'blur'
					},
					verify: 'required',
					col: 12
				},
				{
					label: '通道编码',
					name: 'channelCode',
					placeholder: '请输入通道编码',
					event: 'multipled',
					rules: {
						required: true,
						message: '请输入通道编码',
						trigger: 'blur'
					},
					verify: 'required',
					col: 12
				},
				{
					label: '在线状态',
					name: 'isOnline',
					placeholder: '请输入在线状态',
					event: 'multipled',
					rules: {
						required: true,
						message: '请输入在线状态',
						trigger: 'blur'
					},
					data: [
						{
							label: '在线',
							value: 1
						},
						{
							label: '离线',
							value: 0
						}
					],
					verify: 'required',
					col: 12,
					type: 'select'
				},
				{
					name: 'remark',
					label: '备注',
					placeholder: '请输入备注',
					type: 'textarea',
					col: 12
				}
			];
		},
		// 列表配置
		table() {
			return {
				url: interfaceUrl.listJsonChannel,
				param: { handlerType: '' },
				full: true,
				fit: true,
				page: true,
				stripe: true,
				numbers: true,
				// checkbox: true,
				'row-key': 'id',
				toolbar: [
					{
						type: 'button',
						contents: [
							{
								text: '新增',
								type: 'primary',
								code: 'toolbar'
							}
						]
					},
					{
						type: 'search',
						reset: true,
						contents: [
							{
								col: 6,
								name: 'keyword',
								label: '关键字',
								placeholder: '通道名称'
							}
						]
					}
				],
				thead: [
					{
						title: '通道名称',
						align: 'left',
						field: 'channelName',
						showOverflowTooltip: true
					},
					{
						title: '通道编码',
						align: 'left',
						field: 'channelCode',
						showOverflowTooltip: true
					},
					{
						title: '在线状态',
						field: 'isOnline',
						align: 'center',
						width: 100,
						render: (h, params) => {
							return h(
								'el-tag',
								{
									props: {
										size: 'mini',
										type: params.row.isOnline === 1 ? 'success' : 'danger'
									}
								},
								params.row.isOnline === 1 ? '在线' : '离线'
							);
						}
					},
					{
						title: '操作',
						type: 'handle',
						width: 150,
						template: '',
						events: [
							{
								code: 'edit',
								text: '编辑'
							},
							{
								code: 'view',
								text: '查看'
							},
							{
								code: 'delete',
								text: '删除'
							}
						]
					}
				]
			};
		}
	},
	// watch: {
	// 	deviceId(val) {
	// 		this.params.deviceId = val;
	// 		this.$refs.dataTable.reload();
	// 	}
	// },

	mounted() {},
	methods: {
		// 初始化扩展数据，字典等
		successAfter(data) {
			this.extraData = data.results.extraData;
		},
		// 数据列表多选回调
		handleSelectionChange(data) {
			let ids = [];
			data.forEach(row => {
				ids.push(row.id);
			});
			this.selectRowData = data;
			this.selectRowIds = ids;
		},
		handleFormAudit(val) {
			console.log(val);
		},
		/**
		 * 表格行高
		 */
		tableRowClassName({ row, rowIndex }) {
			let styleRes = {
				height: '54px !important'
			};
			return styleRes;
		},
		inputChange(key, value) {},
		hadeSubmit(data) {},
		/**
		 * 操作按钮点击事件
		 * @param {*} res
		 */
		btnClick(res) {
			let text = res.handle.text;

			switch (text) {
				case '编辑':
					this.viewPageLoad(res.row.id, '编辑');
					break;
				case '查看':
					this.viewPageLoad(res.row.id, '查看');
					break;
				case '删除':
					this.deleteRow(res.row);
					break;
				case '新增':
					this.viewPageLoad('', '新增');
					break;
				default:
					break;
			}
		},
		viewPageLoad(id, formTitle) {
			this.formTitle = formTitle;
			this.ownId = id || uuidv4();
			if (formTitle === '新增') {
				this.formData = {
					id: this.ownId,
					deviceId: this.deviceId
				};
				this.showForm = true;
				return;
			}
			this.ownId = id;
			this.$request({
				url: interfaceUrl.infoChannel + '/' + id,
				method: 'GET'
			}).then(res => {
				this.formData = res.results;
				// 打开查看弹窗
				this.showForm = true;
			});
		},

		handleClose() {},
		handleFormItemChange() {},
		handleFormSubmit(data) {
			let formData = data;
			formData.deviceCode = this.deviceCode;
			let url = '';
			if (this.formTitle == '新增') {
				url = interfaceUrl.saveChannel;
			} else {
				url = interfaceUrl.updateChannel;
			}
			this.$request({
				url: url,
				data: formData,
				method: 'POST',
				format: false
			}).then(res => {
				if (res.rCode == 0) {
					this.$message.success('操作成功');
					this.showForm = false;
					this.formData = {};
					this.$refs.dataTable.reload();
				} else {
					this.formData.state = 1 == this.formData.state ? true : false;
					this.$message.error(res.msg);
				}
			});
		},
		deleteRow(row) {
			this.$confirm(`确定要删除"${row.channelName}"吗？`, '删除', {
				confirmButtonText: '确定',
				cancelButtonText: '取消',
				type: 'warning'
			})
				.then(() => {
					this.$request({
						url: interfaceUrl.deleteByIdChannel,
						data: { id: row.id },
						method: 'POST'
					}).then(res => {
						if (res.rCode == 0) {
							this.$refs.dataTable.reload();
							this.$message.success('删除成功');
						} else {
							this.$message.error(res.msg);
						}
						this.showDelete = false;
					});
				})
				.catch(() => {});
		},
		/**
		 * 排序变化事件
		 * @param {*} column
		 * @param {*} prop
		 * @param {*} order
		 */
		sortChange(column, prop, order) {
			if (column.order == 'ascending') {
				//升序
				this.params = {
					asc: 'true',
					orderBy: column.prop
				};
			} else if (column.order == 'descending') {
				//降序
				this.params = {
					asc: 'false',
					orderBy: column.prop
				};
			} else {
				//不排序
				this.params = {
					asc: 'false',
					orderBy: 'createTime'
				};
			}
			this.$refs.table.reload();
		}
	}
};
</script>
<style scoped lang="scss">
.view-channel {
	width: 100%;
	height: 100%;
}
</style>
