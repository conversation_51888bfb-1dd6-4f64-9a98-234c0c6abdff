<template>
	<div class="app-container">
		<el-form
			v-show="showSearch"
			ref="queryForm"
			:model="queryParams"
			size="small"
			:inline="true"
			label-width="68px"
		>
			<el-form-item label="名称：消防车、运输车等" prop="name">
				<el-input
					v-model="queryParams.name"
					placeholder="请输入名称：消防车、运输车等"
					clearable
					@keyup.enter.native="handleQuery"
				/>
			</el-form-item>
			<el-form-item label="经度" prop="longitude">
				<el-input
					v-model="queryParams.longitude"
					placeholder="请输入经度"
					clearable
					@keyup.enter.native="handleQuery"
				/>
			</el-form-item>
			<el-form-item label="纬度" prop="latitude">
				<el-input
					v-model="queryParams.latitude"
					placeholder="请输入纬度"
					clearable
					@keyup.enter.native="handleQuery"
				/>
			</el-form-item>
			<el-form-item label="图片" prop="filelist">
				<el-input
					v-model="queryParams.filelist"
					placeholder="请输入图片"
					clearable
					@keyup.enter.native="handleQuery"
				/>
			</el-form-item>
			<el-form-item label="车牌号" prop="carNumber">
				<el-input
					v-model="queryParams.carNumber"
					placeholder="请输入车牌号"
					clearable
					@keyup.enter.native="handleQuery"
				/>
			</el-form-item>
			<el-form-item label="所属单位" prop="unit">
				<el-input
					v-model="queryParams.unit"
					placeholder="请输入所属单位"
					clearable
					@keyup.enter.native="handleQuery"
				/>
			</el-form-item>
			<el-form-item label="所在位置" prop="address">
				<el-input
					v-model="queryParams.address"
					placeholder="请输入所在位置"
					clearable
					@keyup.enter.native="handleQuery"
				/>
			</el-form-item>
			<el-form-item label="联系人" prop="contactPerson">
				<el-input
					v-model="queryParams.contactPerson"
					placeholder="请输入联系人"
					clearable
					@keyup.enter.native="handleQuery"
				/>
			</el-form-item>
			<el-form-item label="联系电话" prop="tel">
				<el-input
					v-model="queryParams.tel"
					placeholder="请输入联系电话"
					clearable
					@keyup.enter.native="handleQuery"
				/>
			</el-form-item>
			<el-form-item label="资源数量" prop="numb">
				<el-input
					v-model="queryParams.numb"
					placeholder="请输入资源数量"
					clearable
					@keyup.enter.native="handleQuery"
				/>
			</el-form-item>
			<el-form-item label="数量单位" prop="units">
				<el-input
					v-model="queryParams.units"
					placeholder="请输入数量单位"
					clearable
					@keyup.enter.native="handleQuery"
				/>
			</el-form-item>
			<el-form-item>
				<el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">
					搜索
				</el-button>
				<el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
			</el-form-item>
		</el-form>

		<el-row :gutter="10" class="mb8">
			<el-col :span="1.5">
				<el-button
					v-hasPermi="['big:yjVehicle:add']"
					type="primary"
					plain
					icon="el-icon-plus"
					size="mini"
					@click="handleAdd"
				>
					新增
				</el-button>
			</el-col>
			<el-col :span="1.5">
				<el-button
					v-hasPermi="['big:yjVehicle:edit']"
					type="success"
					plain
					icon="el-icon-edit"
					size="mini"
					:disabled="single"
					@click="handleUpdate"
				>
					修改
				</el-button>
			</el-col>
			<el-col :span="1.5">
				<el-button
					v-hasPermi="['big:yjVehicle:remove']"
					type="danger"
					plain
					icon="el-icon-delete"
					size="mini"
					:disabled="multiple"
					@click="handleDelete"
				>
					删除
				</el-button>
			</el-col>
			<el-col :span="1.5">
				<el-button
					v-hasPermi="['big:yjVehicle:export']"
					type="warning"
					plain
					icon="el-icon-download"
					size="mini"
					@click="handleExport"
				>
					导出
				</el-button>
			</el-col>
			<right-toolbar :show-search.sync="showSearch" @queryTable="getList"></right-toolbar>
		</el-row>

		<el-table v-loading="loading" :data="yjVehicleList" @selection-change="handleSelectionChange">
			<el-table-column type="selection" width="55" align="center" />
			<el-table-column label="主键" align="center" prop="id" />
			<el-table-column label="名称：消防车、运输车等" align="center" prop="name" />
			<el-table-column label="经度" align="center" prop="longitude" />
			<el-table-column label="纬度" align="center" prop="latitude" />
			<el-table-column label="图片" align="center" prop="filelist" />
			<el-table-column label="车牌号" align="center" prop="carNumber" />
			<el-table-column label="所属单位" align="center" prop="unit" />
			<el-table-column label="所在位置" align="center" prop="address" />
			<el-table-column label="联系人" align="center" prop="contactPerson" />
			<el-table-column label="联系电话" align="center" prop="tel" />
			<el-table-column label="资源数量" align="center" prop="numb" />
			<el-table-column label="数量单位" align="center" prop="units" />
			<el-table-column label="备注" align="center" prop="remark" />
			<el-table-column label="操作" align="center" class-name="small-padding fixed-width">
				<template slot-scope="scope">
					<el-button
						v-hasPermi="['big:yjVehicle:edit']"
						size="mini"
						type="text"
						icon="el-icon-edit"
						@click="handleUpdate(scope.row)"
					>
						修改
					</el-button>
					<el-button
						v-hasPermi="['big:yjVehicle:remove']"
						size="mini"
						type="text"
						icon="el-icon-delete"
						@click="handleDelete(scope.row)"
					>
						删除
					</el-button>
				</template>
			</el-table-column>
		</el-table>

		<pagination
			v-show="total > 0"
			:total="total"
			:page.sync="queryParams.pageNum"
			:limit.sync="queryParams.pageSize"
			@pagination="getList"
		/>

		<!-- 添加或修改应急车辆对话框 -->
		<el-dialog :title="title" :visible.sync="open" width="500px" append-to-body>
			<el-form ref="form" :model="form" :rules="rules" label-width="80px">
				<el-form-item label="名称：消防车、运输车等" prop="name">
					<el-input v-model="form.name" placeholder="请输入名称：消防车、运输车等" />
				</el-form-item>
				<el-form-item label="经度" prop="longitude">
					<el-input v-model="form.longitude" placeholder="请输入经度" />
				</el-form-item>
				<el-form-item label="纬度" prop="latitude">
					<el-input v-model="form.latitude" placeholder="请输入纬度" />
				</el-form-item>
				<el-form-item label="图片" prop="filelist">
					<el-input v-model="form.filelist" placeholder="请输入图片" />
				</el-form-item>
				<el-form-item label="车牌号" prop="carNumber">
					<el-input v-model="form.carNumber" placeholder="请输入车牌号" />
				</el-form-item>
				<el-form-item label="所属单位" prop="unit">
					<el-input v-model="form.unit" placeholder="请输入所属单位" />
				</el-form-item>
				<el-form-item label="所在位置" prop="address">
					<el-input v-model="form.address" placeholder="请输入所在位置" />
				</el-form-item>
				<el-form-item label="联系人" prop="contactPerson">
					<el-input v-model="form.contactPerson" placeholder="请输入联系人" />
				</el-form-item>
				<el-form-item label="联系电话" prop="tel">
					<el-input v-model="form.tel" placeholder="请输入联系电话" />
				</el-form-item>
				<el-form-item label="资源数量" prop="numb">
					<el-input v-model="form.numb" placeholder="请输入资源数量" />
				</el-form-item>
				<el-form-item label="数量单位" prop="units">
					<el-input v-model="form.units" placeholder="请输入数量单位" />
				</el-form-item>
				<el-form-item label="备注" prop="remark">
					<el-input v-model="form.remark" type="textarea" placeholder="请输入内容" />
				</el-form-item>
			</el-form>
			<div slot="footer" class="dialog-footer">
				<el-button type="primary" @click="submitForm">确 定</el-button>
				<el-button @click="cancel">取 消</el-button>
			</div>
		</el-dialog>
	</div>
</template>

<script>
import {
	listYjVehicle,
	getYjVehicle,
	delYjVehicle,
	addYjVehicle,
	updateYjVehicle
} from '@/http/screen/yjVehicle';

export default {
	name: 'YjVehicle',
	data() {
		return {
			// 遮罩层
			loading: true,
			// 选中数组
			ids: [],
			// 非单个禁用
			single: true,
			// 非多个禁用
			multiple: true,
			// 显示搜索条件
			showSearch: true,
			// 总条数
			total: 0,
			// 应急车辆表格数据
			yjVehicleList: [],
			// 弹出层标题
			title: '',
			// 是否显示弹出层
			open: false,
			// 查询参数
			queryParams: {
				pageNum: 1,
				pageSize: 10,
				name: null,
				longitude: null,
				latitude: null,
				filelist: null,
				carNumber: null,
				unit: null,
				address: null,
				contactPerson: null,
				tel: null,
				numb: null,
				units: null
			},
			// 表单参数
			form: {},
			// 表单校验
			rules: {}
		};
	},
	created() {
		this.getList();
	},
	methods: {
		/** 查询应急车辆列表 */
		getList() {
			this.loading = true;
			listYjVehicle(this.queryParams).then(response => {
				this.yjVehicleList = response.rows;
				this.total = response.total;
				this.loading = false;
			});
		},
		// 取消按钮
		cancel() {
			this.open = false;
			this.reset();
		},
		// 表单重置
		reset() {
			this.form = {
				id: null,
				name: null,
				longitude: null,
				latitude: null,
				filelist: null,
				carNumber: null,
				unit: null,
				address: null,
				contactPerson: null,
				tel: null,
				numb: null,
				units: null,
				remark: null
			};
			this.resetForm('form');
		},
		/** 搜索按钮操作 */
		handleQuery() {
			this.queryParams.pageNum = 1;
			this.getList();
		},
		/** 重置按钮操作 */
		resetQuery() {
			this.resetForm('queryForm');
			this.handleQuery();
		},
		// 多选框选中数据
		handleSelectionChange(selection) {
			this.ids = selection.map(item => item.id);
			this.single = selection.length !== 1;
			this.multiple = !selection.length;
		},
		/** 新增按钮操作 */
		handleAdd() {
			this.reset();
			this.open = true;
			this.title = '添加应急车辆';
		},
		/** 修改按钮操作 */
		handleUpdate(row) {
			this.reset();
			const id = row.id || this.ids;
			getYjVehicle(id).then(response => {
				this.form = response.data;
				this.open = true;
				this.title = '修改应急车辆';
			});
		},
		/** 提交按钮 */
		submitForm() {
			this.$refs['form'].validate(valid => {
				if (valid) {
					if (this.form.id != null) {
						updateYjVehicle(this.form).then(response => {
							this.$modal.msgSuccess('修改成功');
							this.open = false;
							this.getList();
						});
					} else {
						addYjVehicle(this.form).then(response => {
							this.$modal.msgSuccess('新增成功');
							this.open = false;
							this.getList();
						});
					}
				}
			});
		},
		/** 删除按钮操作 */
		handleDelete(row) {
			const ids = row.id || this.ids;
			this.$modal
				.confirm('是否确认删除应急车辆编号为"' + ids + '"的数据项？')
				.then(function () {
					return delYjVehicle(ids);
				})
				.then(() => {
					this.getList();
					this.$modal.msgSuccess('删除成功');
				})
				.catch(() => {});
		},
		/** 导出按钮操作 */
		handleExport() {
			this.download(
				'big/yjVehicle/export',
				{
					...this.queryParams
				},
				`yjVehicle_${new Date().getTime()}.xlsx`
			);
		}
	}
};
</script>
