<template>
	<div v-loading="loading" class="content">
		<es-data-table
			ref="table"
			:row-style="tableRowClassName"
			:thead="thead"
			:toolbar="toolbar"
			:border="true"
			filter
			:page="pageOption"
			:url="basics.dataTableUrl"
			:numbers="true"
			:param="params"
			show-label
			close
			@search="search"
			@reset="search({})"
			@btnClick="btnClick"
		></es-data-table>
	</div>
</template>

<script>
import { host } from '@/../config/config';
import qs from 'qs';
export default {
	data() {
		return {
			basics: {
				dataTableUrl: '/ybzy/projectBaseInfo/queryPageMapNaturePerson', // 列表接口
				download: '/ybzy/projectBaseInfo/exportNaturePersons' // 导出
			},
			loading: false,
			params: {
				projectType: 'nature',
				orderBy: 'createTime',
				asc: 'false' // true 升序，false 降序
			},
			selectParams: {},
			pageOption: {
				layout: 'total, prev, pager, next, jumper',
				pageSize: 20,
				// // hideOnSinglePage: true,
				position: 'center',
				current: 1,
				pageNum: 1
			},
			codesObj: {} //批量数据字典
		};
	},
	computed: {
		thead() {
			return [
				{
					title: '姓名',
					field: 'name',
					showOverflowTooltip: true,
					align: 'center',
					// width: 100
				},
				{
					title: '性别',
					field: 'sex',
					showOverflowTooltip: true,
					align: 'center',
					// width: 80
				},
				{
					title: '出生年月',
					field: 'birthdate',
					showOverflowTooltip: true,
					align: 'center',
					// width: 100
				},
				{
					title: '最后学历',
					field: 'highestEducation',
					showOverflowTooltip: true,
					align: 'center',
					// width: 100
				},
				{
					title: '技术职务',
					field: 'technicalPosition',
					showOverflowTooltip: true,
					align: 'center',
					// width: 100
				},
				{
					title: '职务类别',
					field: 'positionType',
					showOverflowTooltip: true,
					align: 'center',
					// width: 100
				},
				{
					title: '所属学科',
					field: 'relatedDiscipline',
					showOverflowTooltip: true,
					align: 'center'
				},
				{
					title: '证件类型',
					field: 'idCardType',
					showOverflowTooltip: true,
					align: 'center',
					// width: 100
				},
				{
					title: '证件号码',
					field: 'idCard',
					showOverflowTooltip: true,
					align: 'center'
				},
				{
					title: '是否在编',
					field: 'isPermanentStaff',
					showOverflowTooltip: true,
					align: 'center',
					// width: 100
				},
				{
					title: '岗位类型',
					field: 'postType',
					showOverflowTooltip: true,
					align: 'center'
				}
			];
		},
		toolbar() {
			return [
				{
					type: 'button',
					contents: [
						{
							text: '导出',
							exportXls: true,
							icon: 'es-icon-daochu',
							type: 'primary',
							event: () => {
								const params = { ...this.params, ...this.selectParams };
								const url = host + this.basics.download + '?' + qs.stringify(params);
								window.open(url, '_self');
							}
						}
					]
				},
				{
					type: 'search',
					reset: true,
					showLabel: true,
					contents: [
						{
							type: 'daterange',
							name: 'dates',
							label: '时间范围',
							placeholder: '请选择时间范围'
						},
						{ type: 'text', label: '关键字', name: 'keyword', placeholder: '请输入关键字查询' }
					]
				}
			];
		}
	},
	created() {
		const codes = 'member_nation,hq_person_education,hq_person_state';
		this.$utils.findSysCodeList(codes).then(res => {
			this.codesObj = res;
		});
	},
	methods: {
		//表格行高
		tableRowClassName({ row, rowIndex }) {
			let styleRes = { height: '54px !important' };
			return styleRes;
		},
		//操作按钮点击事件
		btnClick(res) {},
		search(res) {
			const paramsN = { ...res };
			if (paramsN.dates) {
				paramsN.startDate = paramsN.dates[0] || '';
				paramsN.endDate = paramsN.dates[1] || '';
				delete paramsN.dates;
			}
			this.params = {
				...{
					projectType: this.params.projectType,
					orderBy: this.params.orderBy,
					asc: this.params.asc // true 升序，false 降序
				},
				...paramsN
			};
			this.selectParams = paramsN;
		},
		refreshData() {
			this.$refs.table.reload();
		}
	}
};
</script>

<style scoped lang="scss">
.content {
	width: 100%;
	height: 100%;
}
</style>
