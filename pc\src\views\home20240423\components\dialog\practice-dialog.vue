<template>
	<!-- 顶部筛选部分 -->
	<!--  -->
	<div
		class="experiment"
		:style="{
			transform: `scale(${scale}) translateZ(0)`
		}"
	>
		<div class="">
			<span class="experiment-title">实习</span>
			<!-- 选择区域 -->
			<!-- v-model="levelMonth" clearable @change="levelPramsChange" -->
			<el-select placeholder="请选择毕业年份">
				<el-option
					v-for="item in options"
					:key="item.value"
					:label="item.label"
					:value="item.value"
				></el-option>
			</el-select>
			<el-select v-model="collegeName" placeholder="请选择院系" @change="collegeSelect">
				<el-option
					v-for="item in collegeList"
					:key="item.value"
					:label="item.label"
					:value="item.label"
				></el-option>
			</el-select>
		</div>
		<!-- 内容区域 -->
		<div class="experiment-content">
			<graduates :propr-sxqksj="infoData.sxqksj" :propr-sxblfx="infoData.sxblfx" class="card-box" />
			<employment-analysis :propr-sxqksj="infoData.sxqksj" class="card-box" />
			<Statistics :table-data1="infoData.sxqksj" :title="title" class="card-box" />
			<Analyse :title="title" class="card-box" />
		</div>
	</div>
</template>

<script>
import Graduates from './practice-graduates.vue';
import EmploymentAnalysis from './practice-employment.vue';
import Statistics from './statistics.vue';
import Analyse from './analyse.vue';
import { xodbApi } from '@/api/xodb';
import api from '@/http/society/societyBaseInfo/api';
import { createNamespacedHelpers } from 'vuex';
const { mapState, mapMutations } = createNamespacedHelpers('getRoleMap');
export default {
	components: {
		Graduates,
		EmploymentAnalysis,
		Statistics,
		Analyse
	},
	props: {
		title: {
			type: String, // 毕业就业 实习
			default: '实习'
		}
	},
	computed: {
		...mapState(['collegeInfo'])
	},
	data() {
		return {
			scale: 1,
			options: [
				{
					label: '2011',
					value: '2011'
				},
				{
					label: '2012',
					value: '2012'
				},
				{
					label: '2013',
					value: '2013'
				},
				{
					label: '2014',
					value: '2014'
				},
				{
					label: '2015',
					value: '2015'
				},
				{
					label: '2016',
					value: '2016'
				}
			],
			userInfo: {},
			collegeList: [], //院系数据
			collegeName: '', // 当前学院
			infoData: {}
		};
	},
	created() {
		this.getCollege(); //获取院系数据
		this.scale = this.$utils.toolViewRatio();
		this.userInfo = JSON.parse(localStorage.getItem('userInfo')) || {};
		// this.collegeName = this.userInfo.collegeName || '';
		this.collegeName = '电子信息与人工智能学院';
		// this.getData('warehouseConfig_ybzy_dw_dwb_jw_sxqksj_query', 'sxqksj'); //实习情况数据_查询
		// this.getData('warehouseConfig_ybzy_dw_dwb_jw_sxblfx_query', 'sxblfx'); //实习比例分析_查询
	},
	methods: {
		...mapMutations(['SET_COLLEGE_INFO']),
		// 获取院系数据
		getCollege() {
			this.$request({
				url: api.collegeSelectList,
				method: 'POST'
			}).then(result => {
				if (result.success) {
					this.collegeList = result.results;
					// 默认指定一个学院，防止一些数据需要用学院信息来查询的接口
					this.collegeName = this.collegeList[0].label;
				} else {
					this.$message.error('操作失败');
				}
			});
		},
		// 院系选择器选中事件
		collegeSelect(label) {
			this.collegeName = label;
			this.collegeList.find(item => {
				if (item.label == label) {
					this.SET_COLLEGE_INFO(item);
					console.log(this.collegeInfo, '----this.collegeInfo-----');
				}
			});
		},
		// 查询匹配对应数据
		async getData(url, key) {
			try {
				const {
					dataResult: { dataList }
				} = await xodbApi.get({
					url
				});
				const data = dataList?.find(item => item.xyjgmc === this.collegeName) || {};
				this.$set(this.infoData, key, data);
				console.log(this.infoData, '查询匹配对应数据');
			} catch (error) {
				console.error(`${key}处理数据失败:`, error);
			}
		}
	}
};
</script>

<style lang="scss" scoped>
.experiment {
	width: 1778px;
	height: 934px;
	transform-origin: top left;
	background: #e7f6ff;
	padding: 29px 11px 13px 12px;
	border-radius: 5px;
	overflow: hidden;
	&-title {
		font-family: MicrosoftYaHei, MicrosoftYaHei;
		font-weight: bold;
		font-size: 24px;
		color: #454545;
		line-height: 31px;
		margin-left: 27px;
	}
	&-content {
		margin-top: 22px;
	}
	.card-box {
		width: 100%;
		margin-top: 12px;
		display: flex;
		justify-content: space-between;
	}
}
::v-deep .el-input--suffix .el-input__inner {
	background: rgba(255, 255, 255, 0.5) !important;
}
::v-deep .el-input--suffix {
	padding-left: 20px;
}
</style>
