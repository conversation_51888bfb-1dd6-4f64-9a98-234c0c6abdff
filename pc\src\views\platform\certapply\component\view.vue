<template>
	<es-form
		ref="form"
		:model="formData"
		:contents="formList"
		v-bind="$attrs"
		@submit="handleSubmit"
		@reset="handleCancel"
		
	></es-form>
</template>
<script>

import interfaceUrl from '@/http/platform/certapply.js';
import $ from 'eoss-ui/lib/utils/util';
import request from 'eoss-ui/lib/utils/http';
import SnowflakeId from "snowflake-id";

export default {
	name: 'viewDialog',
	props: {

	},
	data() {
		return {
			formData: {}
		};
	},
	computed: {
		formList() {
			return [
				{
					name: 'name',
					label: '应用名称',
					placeholder: '请输入应用名称',
					col: 12,
					rules: {
						required: true,
						message: '请输入应用名称',
						trigger: 'blur'
					}
				},
				{
					name: 'url',
					label: '应用地址',
					placeholder: '请输入应用地址',
					col: 12,
					rules: {
						required: true,
						message: '请输入应用地址',
						trigger: 'blur'
					}
				},
				{
					label: '排序号',
					name: 'sortNum',
					placeholder: '请输入排序号',
					type: 'number',
					controls: false,
					rules: {
						required: true,
						message: '请输入排序号',
						trigger: 'blur'
					},
					verify: 'required',
					col: 12
				},
				{
					name: 'params',
					label: '其它参数',
					placeholder: '请输入其它参数',
					type: 'textarea',
					col: 12
				},
				{
					name: 'remark',
					label: '说明',
					placeholder: '请输入说明',
					type: 'textarea',
					col: 12
				}
			];
		}
	},
	created() {
		const snowflake = new SnowflakeId();
		this.formData = {id:snowflake.generate()};
	},
	methods: {
		initparentMenu(){
			// 获取父菜单选择列表
			let id = this.formInfo.id;
			let appId = this.formInfo.appId;
			this.$request({
				url: interfaceUrl.getTreeList,
				params: {
					"id":id,
					"appId":appId,
				},
				method: 'GET'
			}).then(res => {
				if (res.rCode == 0) {
					this.parentMenuList = res.results;
				} else {
					this.$message.error(res.msg);
				}
			});
		},
		handleSubmit(data) {
			const loading = $.loading(this.$loading, '提交中');
			let url = interfaceUrl.appUpdate;
			let method = 'POST';
			let formData = { ...data};
			let flag = 'left'; // 定义左表格刷新还是右表格刷新
			request({
				url,
				method,
				data: formData
			}).then(res => {
				loading.close();
				this.$emit('cancel');
				if (res.rCode === 0) {
					this.$emit('refresh', flag); // 刷新数据
					this.$message.success(res.msg);
				} else {
					this.$message.error(res.msg);
				}
			});
		},
		// 隐藏对话框
		handleCancel(event) {
			if (event.type === 'reset') {
				this.$emit('cancel');
			}
		}
	}
};
</script>
