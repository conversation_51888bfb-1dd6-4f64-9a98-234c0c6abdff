<template>
	<es-data-table
		ref="materialTable"
		:row-style="tableRowClassName"
		checkbox
		:full="true"
		:fit="true"
		:thead="materialThead"
		:toolbar="materialToolbar"
		:border="true"
		:page="pageOption"
		:url="materialDataUrl"
		:option-data="materialOptionData"
		:numbers="true"
		:param="{
			asc: false,
			orderBy: 'create_time'
		}"
		close
		form
	></es-data-table>
</template>

<script>
import materialApi from '@/http/logistics/material/api.js';
import materialCategoryApi from '@/http/logistics/materialcategory.js';

export default {
	name: 'Material',
	data() {
		return {
			materialToolbar: [
				{
					type: 'button',
					contents: [
						{
							text: '确定',
							code: 'selected',
							type: 'primary',
							event: res => {
								this.$emit('selected', res.ele.selected);
								this.$emit('update:visible', false);
							}
						},
						{
							text: '取消',
							code: 'cancel',
							event: res => {
								this.$emit('update:visible', false);
							}
						}
					]
				},
				{
					type: 'search',
					reset: true,
					contents: [
						{
							type: 'text',
							name: 'name',
							placeholder: '原料名称'
						},
						{
							type: 'select',
							name: 'categoryId',
							placeholder: '原料分类',
							valueType: 'string',
							default: true,
							parentCheck: true,
							tree: true,
							url: '/ybzy/hqbasematerialcategory/getTreeListOne',
							'value-key': 'id',
							'label-key': 'name'
						}
						// {
						// 	type: 'select',
						// 	name: 'categoryId',
						// 	placeholder: '原料分类',
						// 	url: '/ybzy/hqbasematerialcategory/getListAll',
						// 	'value-key': 'id',
						// 	'label-key': 'name'
						// }
					]
				}
			],
			materialThead: [
				{
					title: '原料编号',
					field: 'code',
					align: 'center',
					showOverflowTooltip: true
				},
				{
					title: '原料名称',
					field: 'name',
					align: 'center',
					showOverflowTooltip: true
				},
				{
					title: '原料分类',
					field: 'categoryName',
					align: 'center',
					minWidth: 50,
					showOverflowTooltip: true
				},
				{
					title: '原料单位',
					field: 'unit',
					width: 80,
					align: 'center'
				},
				{
					title: '规格',
					field: 'specification',
					minWidth: 70,
					align: 'center'
				},
				{
					title: '供应商',
					field: 'brand',
					minWidth: 100,
					align: 'center',
					showOverflowTooltip: true
				}
			],
			pageOption: {
				layout: 'total, prev, pager, next, jumper',
				pageSize: 10,
				// hideOnSinglePage: true,
				position: 'right',
				current: 1,
				pageNum: 1
			},
			materialOptionData: {
				categoryId: []
			},
			materialDataUrl: materialApi.listJson
		};
	},
	mounted() {
		this.$nextTick(() => {
			this.init();
		});
	},
	methods: {
		/**
		 * 表格行高
		 */
		tableRowClassName({ row, rowIndex }) {
			let styleRes = {
				height: '54px !important'
			};
			return styleRes;
		},
		categoryList() {
			this.$request({
				url: materialCategoryApi.categoryListSel,
				method: 'POST'
			}).then(res => {
				if (res.rCode === 0) {
					this.materialOptionData.categoryId = res.results.records;
				}
			});
		}
	}
};
</script>

<style></style>
