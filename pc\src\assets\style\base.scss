html,
body {
	--btnColor: #4064e1;
	// font-family: "Source Han Sans CN" ;
	// font-weight: bold;
	margin: 0;
	padding: 0;
}

* {
	margin: 0;
	padding: 0;
}

.el-cascader__dropdown {
	height: 300px;
	overflow: auto;
}

// .el-dialog__header {
// 	background-color: #f0f0f0;
// 	padding: 10px 20px;
// }

.el-dialog__title {
	font-size: 14px;
}

.el-divider--horizontal {
	margin: 10px 0px;
}

.el-table th {
	background-color: #f0f0f0;
	text-align: center;
}

.el-input.is-disabled .el-input__inner {
	color: unset;
}
// 下拉时候
.el-select-dropdown__list {
	padding: 6px 8px;
}

// button.el-button.el-button--submit {
// 	background-color: var(--btnColor);
// 	border: 1px solid var(--btnColor);
// 	color: #fff;
// }
// .es-dialog.is-middle .el-dialog {
// 	left: unset;
// 	top: unset;
// 	-webkit-transform: unset !important;
// 	transform: unset !important;
// 	margin: 0 auto !important;
// 	position: relative !important;
// }

// .el-dialog__wrapper {
// 	display: flex;
// 	align-items: center;
// }

.el-dialog__body {
	overflow: auto;
}