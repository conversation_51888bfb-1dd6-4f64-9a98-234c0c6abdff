<template>
	<es-form ref="form" :model="formData" :contents="formItemList" @submit="handleFormSubmit" height="100%" :genre="2"/>
</template>

<script>
import interfaceUrl from '@/http/alumna/member.js';

const defFormData = {
	isAudit:null,
	auditContent:null
}
export default {
	name:'memberAudit',
	props:{
		id:{
			type:String,
			default: null
		}
	},
	data(){
		return {
			formData:Object.assign({},defFormData),
			qualificationsList:[],
			formItemList:[
				{
					label: '审核状态',
					name: 'isAudit',
					type: 'radio',
					data: [
						{value: 1,name: '审核通过'}
						,{value: 2,name: '审核不通过'}
					],
					verify: 'required',
					rules: {
						required: true,
						message: '请确定是否审核通过',
						trigger: 'blur'
					},
					col: 12
				},
				{
					label: '审核意见',
					name: 'auditContent',
					placeholder: '请填写审核意见',
					type: 'textarea',
					col: 12
				},
				{
					type: 'submit',
					skin: 'lay-form-btns',
					contents: [
						{type: 'primary',text: '确定',event: 'confirm'}
						,{type: 'reset',text: '取消',event: 'cancel'}
					]
				},
			]
		}
	},
	methods:{
		handleFormSubmit(data){
			this.$refs.form.validate(valid => {
				if (!valid) {
					return;
				}
				let formData = data;
				let url = interfaceUrl.info + '/' + this.id + '/audit';
				formData = {
					isAudit: formData.isAudit,
					auditContent: formData.auditContent
				};
				this.$request({url: url,data: formData,method: 'POST'}).then(res => {
					if (res.rCode !== 0) {
						this.$message.error(res.msg);
						return;
					}
					this.$message.success('操作成功');
					this.formData = Object.assign({},defFormData);
					this.$emit('submit')
				});
			});
		}
	}
}

</script>

<style scoped lang='scss'>

</style>