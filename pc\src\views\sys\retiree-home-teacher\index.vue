<!--
 @desc:考勤管理系统-教职工-人事
 @author: 
 @date: 2023/11/13
 -->
<template>
	<div class="main">
		<div class="card-box">
			<mini-card v-for="(item, index) in card" :key="index" :card-data="item" />
		</div>
		<div class="e-box">
			<titile-card
				title="人数统计"
				class="box-big"
				:img-url="require('@/assets/images/sys/rstj.png')"
			>
				<template #content>
					<div class="is-pie">
						<e-line-base id="rstj" :legend-data="rstjData.legend" :series-data="rstjData.data" />
					</div>
				</template>
			</titile-card>
			<titile-card
				title="收支统计"
				class="box-big"
				:img-url="require('@/assets/images/sys/sztj.png')"
			>
				<template #filtre>
					<el-date-picker
						v-model="szjlYear"
						type="month"
						placeholder="选择月"
						format="yyyy-MM"
						value-format="yyyy-MM"
						size="mini"
						class="date-box"
						:editable="false"
						:clearable="false"
						@change="szdate()"
					></el-date-picker>
				</template>
				<template #content>
					<div class="is-pie">
						<e-bar-base id="szjl" :legend-data="sztjData.legend" :series-data="sztjData.data" />
					</div>
				</template>
			</titile-card>
		</div>
		<div class="e-box">
			<titile-card title="活动统计" :img-url="require('@/assets/images/sys/hdtj.png')">
				<template #content>
					<div class="is-pie">
						<e-pie-base id="hdtj" :legend-data="hdtjData.legend" :series-data="hdtjData.data" />
					</div>
				</template>
			</titile-card>
			<titile-card title="报刊统计" :img-url="require('@/assets/images/sys/bktj.png')">
				<template #filtre>
					<el-date-picker
						v-model="bktjMonth"
						type="year"
						placeholder="选择年"
						format="yyyy"
						value-format="yyyy"
						size="mini"
						class="date-box"
						:editable="false"
						:clearable="false"
						@change="bkDate()"
					></el-date-picker>
				</template>
				<template #content>
					<div class="is-pie">
						<e-bar-base
							id="bktj"
							:legend-data="bktjData.legend"
							:series-data="bktjData.data"
							type="cricle"
						/>
					</div>
				</template>
			</titile-card>
			<titile-card title="体检统计" :img-url="require('@/assets/images/sys/tjtj.png')">
				<template #filtre>
					<el-date-picker
						v-model="tjtjMonth"
						type="year"
						placeholder="选择年"
						format="yyyy"
						value-format="yyyy"
						size="mini"
						class="date-box"
						:editable="false"
						:clearable="false"
						@change="tjDate()"
					></el-date-picker>
				</template>
				<template #content>
					<div class="is-pie">
						<e-pie-base
							id="tjtj"
							:legend-data="tjtjData.legend"
							:series-data="tjtjData.data"
							:series-options="tjtjData.option"
						/>
					</div>
				</template>
			</titile-card>
			<titile-card title="离退休慰问统计" :img-url="require('@/assets/images/sys/ltxwwtj.png')">
				<template #filtre>
					<el-date-picker
						v-model="txwwMonth"
						type="month"
						placeholder="选择月"
						format="yyyy-MM"
						value-format="yyyy-MM"
						size="mini"
						class="date-box"
						:editable="false"
						:clearable="false"
						@change="txDate()"
					></el-date-picker>
				</template>
				<template #content>
					<div class="is-pie">
						<e-radar id="twwww"  ref="radar"  />
					</div>
				</template>
			</titile-card>
		</div>
	</div>
</template>

<script>
import MiniCard from '@/components/show/mini-card.vue';
import TitileCard from '@/components/show/titile-card.vue';
import EPieBase from '@/components/echarts/pie-base.vue';
import ERadar from '@/components/echarts/radar.vue';
import EBarBase from '@/components/echarts/bar-base.vue';
import ELineBase from '@/components/echarts/line-base.vue';
import interfaceUrl from '@/http/retire/index.js';
export default {
	components: { MiniCard, TitileCard, ERadar, EPieBase, EBarBase, ELineBase },

	data() {
		return {
			szjlYear: '', //收支统计选择时间
			tjtjMonth: '', //体检统计选择时间
			bktjMonth: '', //报刊统计选择时间
			txwwMonth: '', //离退休慰问统计选择时间
			rslegend: [], //人数统计 legend
			rsdata: [[], []], //人数统计 legend
			szlegend: [], //收支统计
			szData: [[], []], //收支统计
			bklegend: ['下发福利', '计划订阅'], //报刊统计
			bkdata: [[], []],
			tjvalueOne: 100,
			tjvalueTwo: 20,
			tjvalueThree: 100,
			card: [
				{
					img: require('@/assets/images/sys/gh_icon1.png'),
					title: '组织总数',
					unit: '个',
					num: '0'
				},
				{
					img: require('@/assets/images/sys/gh_icon2.png'),
					title: '组织总人数',
					unit: '人',
					num: '0'
				},
				{
					img: require('@/assets/images/sys/gh_icon3.png'),
					title: '组织总收入',
					unit: '元',
					num: '0'
				},
				{
					img: require('@/assets/images/sys/gh_icon4.png'),
					title: '组织总支出',
					unit: '元',
					num: '0'
				}
			], //统计部分卡片展示内容
			toolbar: [
				{
					type: 'search',
					reset: true,
					contents: [
						{
							type: 'text',
							label: '关键字',
							name: 'keyword',
							placeholder: '请输入关键字'
						},
						{
							type: 'select',
							label: '二级学院',
							name: 'keyword',
							placeholder: '请选择二级学院'
						}
					]
				}
			], //表格配置
			thead: [
				{
					title: '学院',
					field: 'createTime',
					fixed: true
				},
				{
					title: '社团名称',
					field: 'formName',
					fixed: false
				},
				{
					title: '社团人数',
					field: 'companyname',
					fixed: false
				},
				{
					title: '创办活动数',
					field: 'fillUsername',
					fixed: false
				},
				{
					title: '社长',
					field: 'telephonenum',
					fixed: false
				},
				{
					title: '联系电话',
					field: 'telephonenum',
					fixed: false
				},
				{
					title: '社团简介',
					field: 'startdate',
					width: '200px',
					fixed: false
				}
			], //表格表头信息配置
			hdtjData: {
				legend: ['已预约活动', '待预约活动', '预约中活动'],
				data: [
					{
						name: '已预约活动',
						value: '10',
						itemStyle: {
							color: 'rgba(13, 215, 141, 1)'
						}
					},
					{
						name: '待预约活动',
						value: '14',
						itemStyle: {
							color: '#de7963'
						}
					},
					{
						name: '预约中活动',
						value: '20',
						itemStyle: {
							color: 'rgba(44, 158, 247, 1)'
						}
					}
				]
				// option: {
				// 	radius:['30%', '30%']
				// }
			}, // 活动统计图表数据
			txData:[],
			txwwData: [
				{
					data: [
						{
							name: '计划慰问',
							value: ['50']
						},
						{
							name: '实际慰问',
							value: ['50']
						},
						{
							name: '经费支出',
							value: ['50']
						}
					]
				}
			] // 离退休慰问统计图表数据
		};
	},
	computed: {
		// 人数统计图表数据
		rstjData() {
			return {
				legend: this.rslegend,
				data: this.rsdata,
				itemWidth: 20
			};
		},
		// 收支统计图标数据
		sztjData() {
			return {
				legend: this.szlegend,
				data: this.szData
			};
		},
		// 报刊统计图表数据
		bktjData() {
			return { legend: this.bklegend, data: this.bkdata };
		},
		//体检统计图表数据
		tjtjData() {
			return {
				legend: ['未体检', '计划体检', '实际体检'],
				data: [
					{
						name: '未体检',
						value: this.tjvalueOne,
						itemStyle: {
							color: '#de7963'
						}
					},
					{
						name: '计划体检',
						value: this.tjvalueTwo,
						itemStyle: {
							color: 'rgba(44, 158, 247, 1)'
						}
					},
					{
						name: '实际体检',
						value: this.tjvalueThree,
						itemStyle: {
							color: 'rgba(13, 215, 141, 1)'
						}
					}
				],
				option: {
					radius: ['35%', '60%'],
					roseType: 'area'
				}
			};
		},

		
	},
	methods: {
		// 获取第一排数据
		getFirstLine() {
			this.$request({
				url: interfaceUrl.titleLine,
				method: 'GET'
			}).then(res => {
				if (res.rCode == 0) {
					this.card[0].num = res.results.top1;
					this.card[1].num = res.results.top2;
					this.card[2].num = res.results.top3;
					this.card[3].num = res.results.top4;
				}
			});
		},
		// 人数统计
		getPeopleCount() {
			this.$request({
				url: interfaceUrl.peopleCount,
				method: 'GET'
			}).then(res => {
				if (res.rCode == 0) {
					for (let key in res.results) {
						this.rslegend.push(res.results[key].org_name);
						this.rsdata[0].push(res.results[key].percent);
						this.rsdata[1].push(res.results[key].count);
						
					}
				}
			});
		},
		// 收支统计
		szCount() {
			this.szlegend=[]
			this.szData=[[],[]]
			this.$request({
				url: interfaceUrl.szCount,
				data: { date: this.szjlYear },
				method: 'POST'
			}).then(res => {
				if (res.rCode == 0) {
					for (let key in res.results) {
						this.szlegend.push(res.results[key].fee_name);
						this.szData[0].push(res.results[key].sr);
						this.szData[1].push(res.results[key].zc);
					}
				}
			});
		},
		// 收支时间搜索
		szdate() {
			this.szCount();
		},
		// 报刊统计
		bkCount() {
			this.bkdata=[[],[]]
			this.$request({
				url: interfaceUrl.bkCount,
				data: { date: this.bktjMonth },
				method: 'POST'
			}).then(res => {
				if (res.rCode == 0) {
					this.bkdata[0].push(res.results.newspaper_num_before);
					this.bkdata[1].push(res.results.newspaper_num_final);
					
				}
			});
		},
		// 报刊时间搜索
		bkDate() {
			this.bkCount();
		},
		// 体检统计
		tjCount() {
			this.$request({
				url: interfaceUrl.tjCount,
				data: { date: this.tjtjMonth },
				method: 'POST'
			}).then(res => {
				if (res.rCode == 0) {
					this.tjvalueOne = res.results.had_no_test_num;
					this.tjvalueTwo = res.results.had_test_num;
					this.tjvalueThree = res.results.test_all_count;
				}
			});
		},
		tjDate() {
			
			this.tjCount();
		},
		txCount() {
			this.$request({
				url: interfaceUrl.txCount,
				data: { date: this.txwwMonth },
				method: 'POST'
			}).then(res => {
				if (res.rCode == 0) {
					this.txData=[]
					for(let key in res.results){
					
						this.txData.push(res.results[key])
					}
					this.$refs.radar.dataInfo = this.txData
					this.$refs.radar.draw()
					
				}
			});
		},

		txDate() {
			this.txCount();
			this.$refs.radar.draw()
		}
	},
	mounted() {
		this.getFirstLine();
		this.getPeopleCount();
		this.szCount();
		this.bkCount();
		this.tjCount();
		this.txCount();
	}
};
</script>
<style lang="scss" scoped>
@import '@ast/style/public.scss';

.main {
	background: #f0f2f5;
	padding: 12px;
	margin-right: 16px;
	overflow: auto;
	height: 100%;
	width: 100%;
	& > * {
		background: #fff;
	}
	header {
		@include flexBox(space-between);
		padding: 6px 20px;
		border-radius: 8px;
		font-size: 20px;
		font-weight: 550;
	}
	.card-box {
		@include flexBox(space-between);
		width: 100%;
		background: #f0f2f5;

		.card {
			width: 24.3%;
		}
	}
	.tabs {
		// width: 300px;
		@include flexBox();
		p {
			margin-left: 10px;
			font-weight: 550;
			cursor: pointer;
		}
		.is-active {
			color: #0377e8;
		}
	}
	.e-box {
		@include flexBox(space-between);
		background: #f0f2f5;
		margin-top: 12px;
		width: 100%;
		& > * {
			background: #fff;
		}
		.title-card {
			width: 24%;
		}
		.box-big {
			width: 49.5%;
		}
	}
	.date-box {
		width: 100px;
		::v-deep .el-input__inner {
			padding-right: 0 !important;
		}
	}
	.is-pie {
		width: 100%;
		height: 400px;
	}
}
</style>
