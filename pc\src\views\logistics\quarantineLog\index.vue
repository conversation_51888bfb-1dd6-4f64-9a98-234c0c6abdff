<template>
	<div class="content">
		<div v-loading="loading" style="width: 100%; height: 100%">
			
			<es-data-table
				ref="table"
				:key="tableCount"
				:row-style="tableRowClassName"
				:full="true"
				:fit="true"
				:thead="thead"
				:toolbar="toolbar"
				:border="true"
				:page="pageOption"
				:url="dataTableUrl"
				:numbers="true"
				:option-data="optionData"
				:param="params"
				close
				form
				@btnClick="btnClick"
				@sort-change="sortChange"
			></es-data-table>

			
			<es-dialog
				title="查看"
				:visible.sync="showViewForm"
				width="90%"
				height="90%"
				:drag="false"
				:close-on-click-modal="false"
				:destroy-on-close="true"
			>
				<div v-if="showViewForm" style="margin: 10px">
					<el-form
						ref="form"
						:inline="false"
						label-position="right"
						label-width="110px"
						:model="formData"
						:disabled="true"
					>
						<el-row>
							<el-col :span="6">
								<el-form-item label="入库单号" prop="receiptCode">
									<el-input
										v-model="formData.receiptCode"
										placeholder="请输入入库单号"
										maxlength="30"
									></el-input>
								</el-form-item>
							</el-col>
							<el-col :span="6">
								<el-form-item label="采购单号" prop="purchaseCode">
									<el-input
										v-model="formData.purchaseCode"
										placeholder="请输入采购单号"
										maxlength="30"
									></el-input>
								</el-form-item>
							</el-col>
							<el-col :span="6">
								<el-form-item label="库房" prop="storeroomId">
									<el-select
										v-model="formData.storeroomId"
										placeholder="请选择库房"
										@change="changeStoreroom"
										:disabled="true"
									>
										<el-option
											v-for="item in storeroomOptions"
											:key="item.id"
											:label="item.name"
											:value="item.id"
										></el-option>
									</el-select>
								</el-form-item>
							</el-col>
							<el-col :span="6">
								<el-form-item label="入库时间" prop="receiptTime">
									<el-date-picker
										v-model="formData.receiptTime"
										type="datetime"
										value-format="yyyy-MM-dd HH:mm:ss"
										placeholder="选择入库时间"
									></el-date-picker>
								</el-form-item>
							</el-col>
							<el-col :span="6">
								<el-form-item label="入库人" prop="receiptPerson">
									<el-input
										v-model="formData.receiptPerson"
										placeholder="请输入入库人"
										maxlength="30"
									></el-input>
								</el-form-item>
							</el-col>
							<el-col :span="6">
								<el-form-item label="质检员" prop="administratorId">
									<el-select v-model="formData.administratorId" placeholder="请选择质检员">
										<el-option
											v-for="item in administratorList"
											:key="item.value"
											:label="item.label"
											:value="item.value"
										></el-option>
									</el-select>
								</el-form-item>
							</el-col>
							<el-col :span="6">
								<el-form-item label="库房管理员" prop="storeKeeperId">
									<el-select v-model="formData.storeKeeperId" placeholder="请选择库房管理员" disabled>
										<el-option
											v-for="item in storeKeeperList"
											:key="item.value"
											:label="item.label"
											:value="item.value"
										></el-option>
									</el-select>
								</el-form-item>
							</el-col>
						</el-row>
					</el-form>
					<div style="display: flex; flex-direction: column; align-items: center;">
						<h3 style="margin-bottom: 10px">原料单</h3>
						<el-table
							:data="formData.details"
							border
							size="mini"
							style="width: 100%"
							max-height="260px"
							v-loading="loading"
						>
							<el-table-column label="序号" type="index"></el-table-column>
							<el-table-column
								prop="code"
								label="原料编号"
								min-width="80"
								:show-overflow-tooltip="true"
							></el-table-column>
							<el-table-column
								prop="name"
								label="原料名称"
								min-width="100"
								:show-overflow-tooltip="true"
							></el-table-column>
							<el-table-column prop="unit" label="单位" min-width="60"></el-table-column>
							<el-table-column prop="specification" label="规格" min-width="60"></el-table-column>
							<el-table-column prop="brand" label="供应商"></el-table-column>
							<el-table-column prop="num" label="入库量" width="130"></el-table-column>
							<!-- <el-table-column prop="opt" label="操作" width="60px">
								<template slot-scope="scope">
									<i
										class="el-icon-remove"
										style="color: red; cursor: pointer"
										@click="removeDetail(scope.$index, scope.row)"
									></i>
								</template>
							</el-table-column> -->
						</el-table>
					</div>
					<h3 style="margin: 20px 0 10px;text-align: center;">检疫记录</h3>
					<el-form
						ref="form2"
						label-position="right"
						label-width="110px"
						:model="formData"
						:disabled="true"
						style="margin-top: 20px;"
					>
						<el-row>
							<el-col :span="6">
								<el-form-item label="检疫人员" prop="quarantinePersonnel">
									<el-input
										v-model="formData.quarantinePersonnel"
										placeholder="请输入检疫人员"
										maxlength="25"
									></el-input>
								</el-form-item>
							</el-col>
							<el-col :span="6">
								<el-form-item label="检疫时间" prop="quarantineTime">
									<el-date-picker
										v-model="formData.quarantineTime"
										type="datetime"
										value-format="yyyy-MM-dd HH:mm:ss"
										placeholder="请输入检疫时间"
									></el-date-picker>
								</el-form-item>
							</el-col>
							<el-col :span="12">
								<el-form-item label="检疫结果" prop="quarantineResults">
									<el-input
										v-model="formData.quarantineResults"
										type="textarea"
										placeholder="请输入检疫结果"
										maxlength="500"
										style="width: 100%;"
									></el-input>
								</el-form-item>
							</el-col>
						</el-row>
						<el-row>
							<el-col :span="6">
								<el-form-item label="处理日期" prop="processingDate">
									<el-date-picker
										v-model="formData.processingDate"
										type="date"
										value-format="yyyy-MM-dd"
										placeholder="请输入处理日期"
									></el-date-picker>
								</el-form-item>
							</el-col>
							<el-col :span="6">
								<el-form-item label="处理措施" prop="handlingMeasures">
									<el-input
										v-model="formData.handlingMeasures"
										type="textarea"
										placeholder="请输入处理措施"
										maxlength="500"
									></el-input>
								</el-form-item>
							</el-col>
							<el-col :span="12">
								<el-form-item label="处理结果" prop="processingResults">
									<el-input
										v-model="formData.processingResults"
										type="textarea"
										placeholder="请输入处理结果"
										maxlength="500"
									></el-input>
								</el-form-item>
							</el-col>
						</el-row>
					</el-form>
				</div>
				
			</es-dialog>
		</div>
	</div>
</template>

<script>
import storeroomApi from '@/http/logistics/hqStoeroom.js';
import quarantineLogUrl from '@/http/logistics/quarantineLog.js';
import receiptOrderUrl from '@/http/logistics/receiptOrder.js';
import operatePersonApi from '@/http/logistics/operatePerson.js';

import interfaceUrl from '@/http/logistics/shipmentOrder.js';
import materialApi from '@/http/logistics/material/api.js';
import SnowflakeId from 'snowflake-id';
import { host } from '../../../../config/config';

//默认表单字段
const defFormData = {
	id: null,
	type: '2',
	status: null,
	storeroomId: null,
	receiveUser: null,
	administratorId: null,
	storeKeeperId: null,
	meals: null,
	stallOpening: null,
	receiveTime: null,
	details: []
};

export default {
	data() {
		return {
			loading: false,
			tableCount: 1,
			ownId: '',
			dataTableUrl: quarantineLogUrl.listJson,
			showForm: false,
			showViewForm: false,
			showDelete: false,
			formTitle: '编辑',

			//领料人，质检员、库房管理员
			receiveUserList: [],
			administratorList: [],
			storeKeeperList: [],

			
			editBtn: {
				type: 'submit',
				skin: 'lay-form-btns',
				contents: [
					{
						type: 'primary',
						text: '确定',
						event: 'confirm'
					},
					{
						type: 'reset',
						text: '取消',
						event: 'cancel'
					}
				]
			},
			cancelBtn: {
				type: 'submit',
				skin: 'lay-form-btns',
				contents: [
					{
						type: 'reset',
						text: '取消',
						event: 'cancel'
					}
				]
			},
			thead: [],
			theadForReceiptOrder: [
				{
					title: '入库单号',
					align: 'left',
					field: 'receiptCode',
					showOverflowTooltip: true
				},
				{
					title: '采购单号',
					align: 'center',
					field: 'purchaseCode',
					showOverflowTooltip: true
				},
				{
					title: '检疫时间',
					align: 'left',
					field: 'quarantineTime',
					showOverflowTooltip: true
				},
				{
					title: '检疫结果',
					align: 'left',
					field: 'quarantineResults',
					showOverflowTooltip: true
				},
				{
					title: '检疫人员',
					align: 'left',
					field: 'quarantinePersonnel',
					showOverflowTooltip: true
				},
				{
					title: '处理措施',
					align: 'center',
					field: 'handlingMeasures',
					readonly: true
				},
				{
					title: '处理结果',
					align: 'center',
					field: 'processingResults',
					readonly: true
				},
				{
					title: '操作',
					type: 'handle',
					width: 100,
					template: '',
					events: [
						{
							code: 'view',
							text: '查看'
						},
					]
				}
			],
			theadForCustom: [
				{
					title: '退料单号',
					align: 'left',
					field: 'code',
					showOverflowTooltip: true
				},
				{
					title: '退料时间',
					align: 'left',
					field: 'receiveTime',
					showOverflowTooltip: true
				},
				{
					title: '退料人',
					align: 'left',
					field: 'receiveUserName',
					showOverflowTooltip: true
				},
				{
					title: '退料状态',
					align: 'center',
					field: 'status',
					type: 'switch',
					readonly: true
				},
				{
					title: '操作',
					type: 'handle',
					width: 180,
					template: '',
					events: [
						{
							code: 'view',
							text: '查看'
						},
						{
							code: 'edit',
							text: '编辑',
							rules: rows => {
								return rows.status === '0';
							}
						},
						{
							code: 'submit',
							text: '提交',
							rules: rows => {
								return rows.status === '0';
							}
						},
						{
							code: 'download',
							text: '导出'
						}
					]
				}
			],
			optionData: {
				storeroomId: [],
				supplierId: [],
				status: [
					{ name: '待提交', value: 0 },
					{ name: '已退料', value: 1 }
				]
			},
			pageOption: true,
			params: {
				asc: false,
				orderBy: 'create_time',
				type: '2'
			},
			formData: Object.assign({}, defFormData),
			formRules: {
				storeroomId: [{ required: true, message: '请选择库房', trigger: 'change' }],
				purchaseCode: [{ required: true, message: '请选择采购单号', trigger: 'change' }],
				receiveUser: [{ required: true, message: '请选择退料人', trigger: 'change' }],
				administratorId: [{ required: true, message: '请选择质检员', trigger: 'change' }],
				storeKeeperId: [{ required: true, message: '请选择库房管理员', trigger: 'change' }]
			},
			storeroomOptions: [], //库房选择列表
			mealsOptions: [], //餐次选择列表
			receiptOptions: [], //采购入库单列表(用于采购单号选择)

			curMaterialIds: [], //当前选择的原料(用于重新选择时去重)
			showSubmit: false,
			showMaterial: false,
			materialDataUrl: materialApi.listJson,
			materialToolbar: [
				{
					type: 'button',
					contents: [
						{
							text: '确定',
							code: 'selected',
							type: 'primary',
							event: res => {
								this.selMaterial(res.ele.selected);
								this.showMaterial = false;
							}
						},
						{
							text: '取消',
							code: 'cancel',
							event: res => {
								this.showMaterial = false;
							}
						}
					]
				},
				{
					type: 'search',
					reset: true,
					contents: [
						{
							type: 'text',
							name: 'name',
							placeholder: '原料名称'
						},
						{
							type: 'select',
							name: 'categoryId',
							placeholder: '原料分类',
							valueType: 'string',
							default: true,
							parentCheck: true,
							tree: true,
							url: '/ybzy/hqbasematerialcategory/getTreeListOne',
							'value-key': 'id',
							'label-key': 'name'
						}
					]
				}
			],
			materialThead: [
				{
					title: '原料编号',
					field: 'code',
					align: 'center',
					showOverflowTooltip: true
				},
				{
					title: '原料名称',
					field: 'name',
					align: 'center',
					showOverflowTooltip: true
				},
				{
					title: '原料分类',
					field: 'categoryName',
					align: 'center',
					showOverflowTooltip: true
				},
				{
					title: '原料单位',
					field: 'unit',
					align: 'center'
				},
				{
					title: '供应商',
					field: 'brand',
					align: 'center',
					showOverflowTooltip: true
				},
				{
					title: '规格',
					field: 'specification',
					align: 'center'
				}
			],
			materialOptionData: {
				categoryId: []
			}
		};
	},
	computed: {
		formItemList() {
			return this.formItemForPurchase;
		},
		toolbar() {
			return [
				{
					type: 'search',
					reset: true,
					contents: [
						{
							type: 'text',
							name: 'receiptCode',
							placeholder: '入库单号'
						}
					]
				}
			];
		},
		formItemForPurchase() {
			return [
				{
					label: '采购单号',
					name: 'purchaseCode',
					placeholder: '请输入采购单号',
					rules: {
						required: true,
						message: '请选择采购单号',
						trigger: 'blur'
					},
					col: 4
				},
				{
					label: '库房',
					name: 'storeroomId',
					type: 'select',
					placeholder: '请选择库房',
					event: 'multipled',
					rules: {
						required: true,
						message: '请选择库房',
						trigger: 'blur'
					},
					col: 4,
					data: this.storeroomOptions,
					'label-key': 'name',
					'value-key': 'id'
				},
				{
					label: '退料时间',
					name: 'receiveTime',
					type: 'date',
					placeholder: '请选择退料时间',
					event: 'multipled',
					rules: {
						required: true,
						message: '请选择库房',
						trigger: 'blur'
					},
					col: 4
				},
				{
					label: '退料人',
					name: 'receiveUser',
					type: 'select',
					placeholder: '请选择退料人',
					event: 'multipled',
					rules: {
						required: true,
						message: '请选择退料人',
						trigger: 'change'
					},
					col: 4,
					data: this.receiveUserList,
					'label-key': 'label',
					'value-key': 'value'
				},
				{
					label: '质检员',
					name: 'administratorId',
					type: 'select',
					placeholder: '请选择质检员',
					event: 'multipled',
					rules: {
						required: true,
						message: '请选择质检员',
						trigger: 'change'
					},
					col: 4,
					data: this.administratorList,
					'label-key': 'label',
					'value-key': 'value'
				},
				{
					label: '库房管理员',
					name: 'storeKeeperId',
					type: 'select',
					placeholder: '请选择库房管理员',
					event: 'multipled',
					rules: {
						required: true,
						message: '请选择库房管理员',
						trigger: 'change'
					},
					col: 4,
					data: this.storeKeeperList,
					'label-key': 'label',
					'value-key': 'value'
				},
				{
					type: 'table',
					name: 'details',
					// lazy: true,
					form: true,
					numbers: true,
					border: true,
					thead: [
						{
							title: '原料编号',
							field: 'materialCode',
							align: 'center',
							showOverflowTooltip: true,
							readonly: true
						},
						{
							title: '原料名称',
							field: 'materialName',
							align: 'center',
							showOverflowTooltip: true,
							readonly: true
						},
						// {
						// 	title: '库存数量',
						// 	field: 'residueNum',
						// 	align: 'center',
						// 	readonly: true
						// },
						{
							title: '单位',
							field: 'unit',
							align: 'center',
							width: '80',
							readonly: true
						},
						{
							title: '规格',
							field: 'specification',
							align: 'center',
							showOverflowTooltip: true,
							readonly: true
						},
						{
							title: '供应商',
							field: 'brand',
							align: 'center',
							showOverflowTooltip: true,
							readonly: true
						},
						{
							title: '退料数量',
							field: 'num',
							align: 'center',
							readonly: true
						},
						{
							title: '退料原因',
							field: 'reason',
							align: 'center',
							showOverflowTooltip: true,
							readonly: true
						}
					]
				}
			];
		}
	},
	created() {
		this.thead = this.getListThead(this.theadForReceiptOrder);
		this.getSelectPersonList(1);
		this.getSelectPersonList(3);
	},
	mounted() {
	},
	methods: {
		storeroomList() {
			this.$request({
				url: storeroomApi.listAll,
				method: 'GET'
			}).then(res => {
				if (res.rCode === 0) {
					this.storeroomOptions = res.results;
				}
			});
		},
		//获取相关人员下拉列表 类型：质检员：1 领料人：2 库房管理员：3
		getSelectPersonList(value) {
			this.$request({
				url: operatePersonApi.getSelectList,
				params: { type: value, status: 1 },
				method: 'GET'
			}).then(res => {
				if (res.rCode === 0) {
					if (value == 1) {
						this.administratorList = res.results;
					}
					if (value == 3) {
						this.storeKeeperList = res.results;
					}
				}
			});
		},
		getListThead(org) {
			return Object.assign([], org);
		},
		purchaseCodeQuery(keyword) {
			if (keyword !== '') {
				this.loading = true;
				this.$request({
					url: '/ybzy/hqreceiptorder/listJson',
					method: 'GET',
					params: {
						pageNum: 1,
						pageSize: 10,
						type: '1',
						purchaseCode: keyword
					}
				}).then(res => {
					this.loading = false;
					if (res.rCode !== 0) {
						this.$message.error(res.msg);
						return;
					}
					this.receiptOptions = res.results.records;
				});
			} else {
				this.receiptOptions = [];
			}
		},
		changePurchase(val) {
			if (val) {
				let order = this.receiptOptions.filter(v => v.receiptId === this.formData.receiptId);
				if (order) {
					this.formData.purchaseCode = order[0].purchaseCode;
					// this.formData.supplierId = order[0].supplierId;
					this.formData.storeroomId = order[0].storeroomId;
					//填充库房管理员
					let storeroom = this.storeroomOptions.filter(v => v.id === this.formData.storeroomId);
					if (storeroom) {
						this.formData.storeKeeperId = storeroom[0].manager;
					}
				}
				this.$request({
					url: '/ybzy/hqshipmentorder/receiptOrder/' + val,
					method: 'GET'
				}).then(res => {
					if (res.rCode !== 0) {
						this.$message.error(res.msg);
						return;
					}
					let details = res.results;
					let snowflake = new SnowflakeId();
					for (let item of details) {
						if (!item.id) {
							//还未保存过的退料查询记录,只有商品和订购信息
							item.id = snowflake.generate();
							item.shipmentOrderId = this.ownId;
							item.num = 0;
						}
						item.maxNum =
							item.residueNum > item.receiptOrderNum ? item.receiptOrderNum : item.residueNum;
					}
					this.formData.details = details;
				});
			}
		},
		/**
		 * 表格行高
		 */
		tableRowClassName({ row, rowIndex }) {
			let styleRes = {
				height: '54px !important'
			};
			return styleRes;
		},
		inputChange(key, value) {},
		hadeSubmit(data) {},
		/**
		 * 操作按钮点击事件
		 * @param {*} res
		 */
		btnClick(res) {
			console.log(res)
			let code = res.handle.code;
			switch (code) {
				case 'view':
					this.formTitle = '查看';
					this.ownId = res.row.id;
					this.curRowType = res.row.type;
					this.$request({
						url: receiptOrderUrl.info + '/' + res.row.id,
						method: 'GET'
					}).then(res => {
						if (res.rCode === 0) {
							this.formData = res.results;
							this.showViewForm = true;
						}
					});
					break;
				case 'download':
					window.open(host + interfaceUrl.download + '?requestType=2&id=' + res.row.id, '_self');
					break;
				default:
					break;
			}
		},
		handleClose() {},
		handleFormItemChange() {},
		handleFormSubmit(data) {
			let formData = data;
			let url = '';
			if (this.formTitle === '新增') {
				url = interfaceUrl.save;
			} else {
				url = interfaceUrl.update;
			}
			this.$request({
				url: url,
				data: formData,
				method: 'POST',
				format: false
			}).then(res => {
				if (res.rCode === 0) {
					this.$message.success('操作成功');
					this.showForm = false;
					this.formData = {};
					this.$refs.table.reload();
				} else {
					this.$message.error(res.msg);
				}
			});
		},
		deleteRow() {
			this.$request({
				url: interfaceUrl.deleteBatchIds,
				data: { ids: this.deleteId },
				method: 'POST'
			}).then(res => {
				if (res.rCode == 0) {
					this.$refs.table.reload();
					this.$message.success('删除成功');
				} else {
					this.$message.error(res.msg);
				}
				this.showDelete = false;
			});
		},
		/**
		 * 排序变化事件
		 * @param {*} column
		 * @param {*} prop
		 * @param {*} order
		 */
		sortChange(column, prop, order) {
			if (column.order == 'ascending') {
				//升序
				this.params = {
					asc: 'true',
					orderBy: column.prop
				};
			} else if (column.order == 'descending') {
				//降序
				this.params = {
					asc: 'false',
					orderBy: column.prop
				};
			} else {
				//不排序
				this.params = {
					asc: 'false',
					orderBy: 'createTime'
				};
			}
			this.$refs.table.reload();
		},
		/**
		 * 编辑模式
		 */
		editModule(list) {
			for (var i in list) {
				var item = list[i];
				item.readonly = false;
			}
			list.push(this.editBtn);
		},
		/**
		 * 只读模式
		 */
		readModule(list, hideField) {
			for (var i in list) {
				var item = list[i];
				item.readonly = true;
			}
			list.push(this.cancelBtn);
		},
		removeDetail(index, row) {
			this.formData.details.splice(index, 1);
			let i = this.curMaterialIds.indexOf(row.materialId);
			this.curMaterialIds.splice(i, 1);
		},
		changeStoreroom(val) {
			if (val) {
				let storeroom = this.storeroomOptions.filter(v => v.id === this.formData.storeroomId);
				if (storeroom) {
					this.formData.storeKeeperId = storeroom[0].manager;
				}
			}
			if (this.formData.type === '3') {
				this.formData.details = [];
				this.curMaterialIds = [];
			}
		},
		//点击添加原料按钮
		doShowMaterial() {
			//判断是否已选定了库房
			if (!this.formData.storeroomId) {
				this.$message.warning('请先确定库房');
				return;
			}
			this.params.storeroomId = this.formData.storeroomId;
			this.showMaterial = true;
		},
		//原料选择
		selMaterial(rows) {
			const snowflake = new SnowflakeId();
			for (let row of rows) {
				let materialId = row.id;
				if (this.curMaterialIds.includes(materialId)) {
					continue;
				}
				this.curMaterialIds.push(materialId);
				let ownId = snowflake.generate();
				let createDetail = {
					id: ownId,
					materialCode: row.code,
					materialName: row.name,
					residueNum: row.inventoryNum,
					maxNum: row.inventoryNum,
					unit: row.unit,
					num: 0,
					brand: row.brand,
					specification: row.specification,
					shipmentOrderId: this.ownId,
					materialId: materialId,
					reason: row.reason
				};
				this.formData.details.push(createDetail);
			}
		},
		ensureSubmit() {
			this.$request({
				url: interfaceUrl.submit + '/' + this.ownId,
				method: 'GET'
			}).then(res => {
				if (res.rCode !== 0) {
					this.$message.error(res.msg);
					return;
				}
				this.$message.success('操作成功');
				this.$refs.table.reload();
				this.showSubmit = false;
			});
		},
		save(status) {
			this.$refs['form'].validate(valid => {
				if (!valid) {
					return;
				}
				let details = this.formData.details;
				if (details === null || details === undefined || details.length === 0) {
					this.$message.warning('请完善原料单！');
					return;
				}
				let numAll = 0;
				for (let item of details) {
					numAll += item.num;
					if (item.num > item.residueNum) {
						this.$message.warning('退料数量不能超过库存数量！');
						return;
					}
					let str = item.reason;
					if (Number(item.num) > 0 && (!str || /^\s*$/.test(str))) {
						this.$message.warning(`原料编号${item.materialCode}的退料原因不能为空！`);
						return;
					}
				}
				if (numAll <= 0) {
					this.$message.warning('退料数量至少一个需大于0！');
					return;
				}
				this.formData.status = status;
				this.handleFormSubmit(this.formData);
			});
		}
	}
};
</script>
<style scoped lang="scss">
@import '../../../assets/style/style.scss';

.content {
	width: 100%;
	height: 100%;
}

::v-deep .el-dialog__body {
	overflow: auto;
	.btn-box {
		margin-top: 20px;
		display: flex;
		justify-content: flex-end;

		.btn {
			padding: 5px 10px;
			color: #666;
			border: 1px solid #eee;
			cursor: pointer;
			margin-right: 5px;
			border-radius: 4px;
		}
	}
}
</style>
