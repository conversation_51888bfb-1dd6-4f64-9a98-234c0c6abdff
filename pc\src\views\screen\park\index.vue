<template>
	<div class="content">
		<es-data-table
			ref="table"
			:row-style="tableRowClassName"
			:full="true"
			:fit="true"
			:thead="thead"
			:toolbar="toolbar"
			:border="true"
			:page="pageOption"
			:url="dataTableUrl"
			:numbers="true"
			@btnClick="btnClick"
			@sort-change="sortChange"
			:param="params"
			@submit="hadeSubmit"
			close
			form
		></es-data-table>
		<el-dialog :title="formTitle" :visible.sync="open" width="800px" append-to-body>
			<el-form ref="form" :model="form" :rules="rules" label-width="100px">
				<el-form-item label="名称" prop="name">
					<el-input v-model="form.name" placeholder="请输入名称" />
				</el-form-item>
				<el-form-item label="详细地址" prop="address">
					<el-input v-model="form.address" placeholder="请输入详细地址" />
				</el-form-item>
				<el-form-item label="联系人" prop="userName">
					<el-input v-model="form.userName" placeholder="请输入联系人" />
				</el-form-item>
				<el-form-item label="联系电话" prop="userName">
					<el-input v-model="form.phone" placeholder="请输入联系电话" />
				</el-form-item>
				<el-form-item label="备注" prop="remark">
					<el-input v-model="form.remark" placeholder="请输入备注" />
				</el-form-item>
				<el-form-item label="经纬度" prop="lngLat" key="selectDeptList">
					<lng-lat v-model="form.lngLat"></lng-lat>
				</el-form-item>
			</el-form>
			<div slot="footer" class="dialog-footer">
				<el-button v-show="formTitle != '查看'" type="primary" @click="submitForm">确 定</el-button>
				<el-button @click="cancel">取 消</el-button>
			</div>
		</el-dialog>
		<es-dialog
			title="删除"
			:visible.sync="showDelete"
			:close-on-click-modal="false"
			:middle="true"
			height="140px"
			@close="imageList = []"
		>
			<div>确定要删除该条数据吗</div>
			<div class="btn-box">
				<div class="btn theme" @click="deleteRow">确定</div>
				<div class="btn" @click="showDelete = false">取消</div>
			</div>
		</es-dialog>
	</div>
</template>

<script>
import interfaceUrl from '@/http/screen/park';
import SnowflakeId from 'snowflake-id';
import LngLat from '@/components/lngLat.vue';
import { Base64 } from 'js-base64';

export default {
	name: 'YjRefuge', //避难场所
	components: { LngLat },
	data() {
		return {
			dataTableUrl: interfaceUrl.listJson,
			showForm: false,
			showDelete: false,
			formData: {},
			formTitle: '编辑',
			editBtn: {
				type: 'submit',
				skin: 'lay-form-btns',
				contents: [
					{
						type: 'primary',
						text: '确定',
						event: 'confirm'
					},
					{
						type: 'reset',
						text: '取消',
						event: 'cancel'
					}
				]
			},
			cancelBtn: {
				type: 'submit',
				skin: 'lay-form-btns',
				contents: [
					{
						type: 'reset',
						text: '取消',
						event: 'cancel'
					}
				]
			},
			toolbar: [
				{
					type: 'button',
					contents: [
						{
							text: '新增',
							code: 'add',
							type: 'primary'
						}
					]
				},
				{
					type: 'search',
					contents: [
						{
							type: 'text',
							name: 'keyword',
							placeholder: '关键字查询'
						}
					]
				}
			],
            thead: [
                {
                    title: '名称',
                    align: 'left',
                    field: 'name',
                    sortable: 'custom',
                    showOverflowTooltip: true,
                },
                {
                    title: '经度',
                    align: 'left',
                    field: 'longitude',
                    sortable: 'custom',
                    showOverflowTooltip: true,
                },
                {
                    title: '纬度',
                    align: 'left',
                    field: 'latitude',
                    sortable: 'custom',
                    showOverflowTooltip: true,
                },
                {
                    title: '详细地址',
                    align: 'left',
                    field: 'address',
                    sortable: 'custom',
                    showOverflowTooltip: true,
                },
                {
                    title: '联系方式',
                    align: 'left',
                    field: 'phone',
                    sortable: 'custom',
                    showOverflowTooltip: true,
                },
                {
                    title: '管理人员',
                    align: 'left',
                    field: 'userName',
                    sortable: 'custom',
                    showOverflowTooltip: true,
                },
                {
                    title: '描述',
                    align: 'left',
                    field: 'remark',
                    sortable: 'custom',
                    showOverflowTooltip: true,
                },
                {
                    title: '操作',
                    type: 'handle',
                    width: 180,
                    template: '',
                    events: [
                        {
                            code: 'edit',
                            text: '编辑'
                        },
                        {
                            code: 'view',
                            text: '查看'
                        },
                        {
                            code: 'delete',
                            text: '删除'
                        },
                    ]
                }
            ],
			pageOption: {
				layout: 'total, prev, pager, next, jumper',
				pageSize: 10,
				// hideOnSinglePage: true,
				position: 'right',
				current: 1,
				pageNum: 1
			},
			params: {
				asc: 'false',
				orderBy: 'createTime'
			},
			open: false,
			// 表单参数
			form: {
				id: null,
				name: null,
                phone: null,
                userName: null,
				address: null,
				longitude: null,
				latitude: null,
				remark: null,
				lngLat: [],
			},
			// 表单校验
			rules: {
				name: [{ required: true, message: '需要填写信息', trigger: 'blur' }],
				address: [{ required: true, message: '需要填写信息', trigger: 'blur' }]
			},
		};
	},
	computed: {
	},
	watch: {},
	created() {},
	mounted() {},
	methods: {
		/**
		 * 表格行高
		 */
		tableRowClassName({ row, rowIndex }) {
			let styleRes = {
				height: '54px !important'
			};
			return styleRes;
		},
		inputChange(key, value) {},
		hadeSubmit(data) {},

		deleteRow() {
			this.$request({
				url: interfaceUrl.deleteBatchIds,
				data: { ids: this.deleteId },
				method: 'POST'
			}).then(res => {
				if (res.rCode == 0) {
					this.$refs.table.reload();
					this.$message.success('删除成功');
				} else {
					this.$message.error(res.msg);
				}
				this.showDelete = false;
			});
		},
		/**
		 * 排序变化事件
		 * @param {*} column
		 * @param {*} prop
		 * @param {*} order
		 */
		sortChange(column, prop, order) {
			if (column.order == 'ascending') {
				//升序
				this.params = {
					asc: 'true',
					orderBy: column.prop
				};
			} else if (column.order == 'descending') {
				//降序
				this.params = {
					asc: 'false',
					orderBy: column.prop
				};
			} else {
				//不排序
				this.params = {
					asc: 'false',
					orderBy: 'createTime'
				};
			}
			this.$refs.table.reload();
		},
		btnClick(res) {
			let code = res.handle.code;
			switch (code) {
				case 'add':
					// 新增
					this.reset();
					this.formTitle = '新增';
					const snowflake = new SnowflakeId();
					let id = snowflake.generate();
					this.form.id = id;
					this.open = true;
					break;
				case 'edit':
					// 编辑
					this.reset();
					this.formTitle = '编辑';
					this.$request({
						url: interfaceUrl.info + '/' + res.row.id,
						method: 'GET'
					}).then(res => {
						if (res.rCode == 0) {
							this.form = res.results;
							let lngLat = [res.results.longitude,res.results.latitude];
                            this.form.lngLat = lngLat;
							this.open = true;
						}
					});
					break;
				case 'view':
					this.reset();
					this.formTitle = '查看';
					this.$request({
						url: interfaceUrl.info + '/' + res.row.id,
						method: 'GET'
					}).then(res => {
						if (res.rCode == 0) {
                            this.form = res.results;
                            let lngLat = [res.results.longitude,res.results.latitude];
                            this.form.lngLat = lngLat;
							this.open = true;
						}
					});
					break;
				case 'delete':
					this.deleteId = res.row.id;
					this.showDelete = true;
					break;
				default:
					break;
			}
		},
		//提交按钮
		submitForm() {
			this.$refs['form'].validate(valid => {
				if (valid) {
					let url = '';
					if (this.formTitle == '新增') {
						url = interfaceUrl.save;
					} else {
						url = interfaceUrl.update;
					}
					if (this.form.lngLat && this.form.lngLat.length == 2) {
						this.form.longitude = this.form.lngLat[0];
						this.form.latitude = this.form.lngLat[1];
					}
					this.$request({
						url: url,
						data: this.form,
						method: 'POST'
					}).then(res => {
						if (res.rCode == 0) {
							this.$refs.table.reload();
							this.$message.success(this.formTitle + '成功');
							this.cancel();
						} else {
							this.$message.error(res.msg);
						}
						this.showDelete = false;
					});
				}
			});
		},
		// 取消按钮
		cancel() {
			this.open = false;
			this.reset();
		},
		// 表单重置
		reset() {
			this.form = {
                id: null,
                name: null,
                phone: null,
                userName: null,
                address: null,
                longitude: null,
                latitude: null,
                remark: null,
                lngLat: [],
			};
		},
	}
};
</script>
<style scoped lang="scss">
@import '../../../assets/style/style.scss';

.content {
	display: flex;
	width: 100%;
	height: 100%;

	::v-deep .es-data-table {
		flex: 1;
		display: flex;
		flex-direction: column;

		.es-data-table-content {
			flex: 1;
			height: 0;
			display: flex;
			flex-direction: column;

			.el-table {
				flex: 1;
				// height: 100% !important;
			}

			.es-thead-border {
				.el-table__header {
					th {
						border-right: 1px solid #e1e1e1;
					}
				}
			}
		}
	}

	::v-deep .el-form-item__label {
		background: none;
		border: 0px solid #c2c2c2;
	}
}

::v-deep {
	.el-dialog {
		max-height: 100%;
		top: 35%;
		.el-dialog__body {
			max-height: 70vh;
			overflow-y: auto;
			.btn-box {
				margin-top: 20px;
				display: flex;
				justify-content: flex-end;

				.btn {
					padding: 5px 10px;
					color: #666;
					border: 1px solid #eee;
					cursor: pointer;
					margin-right: 5px;
					border-radius: 4px;
					// &.theme {
					// 	background: $--color-primary;
					// 	color: #fff;
					// 	border-color: $--color-primary;
					// }
				}
			}
		}
	}
}
</style>
