/*
 * @Author: 龙飞尘
 * @LastEditors: 龙飞尘
 * @Description: 资料管理相关接口
 */

//接口地址	
const api = {
	createClassFy: '/ybzy/journal/create', // 创建分类
	DeleteClassFy: '/ybzy/journal/delete', // 删除分类
	UpdateClassFy: '/ybzy/journal/update', // 修改分类
    getClassFyTree: '/ybzy/journal/tree', // 获取分类树
    getClassFyList: '/ybzy/journal/list', // 获取分类表格列表数据
    getIndexCount: '/ybzy/journal/count', // 获取首页展示数据
	
	createFile: '/ybzy/file/create', // 创建文件
	deleteFile: '/ybzy/file/delete', // 删除文件
	UpdateFile: '/ybzy/file/update', // 修改文件
	getFileList: '/ybzy/file/list', // 获取文件列表
	addDownLoadsum:'/ybzy/file/sum', // 添加下载量
	transferFile:'/ybzy/file/transfer', // 移交文件
	rangeFile:'/ybzy/file/range', // 共享文件
	collectFile:'/ybzy/file/collect', // 收藏文件
	clearFile:'/ybzy/file/clear', // 取消收藏文件
};
export default api;
