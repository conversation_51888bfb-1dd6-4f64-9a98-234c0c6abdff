<template>
	<div style="width: 100%; height: 100%">
		<es-data-table
			form
			:data="tableData"
			:fit="true"
			:thead="thead"
			:toolbar="toolbar"
			size="mini"
			checkbox
			close
			@btn-click="btnClick"
			numbers
			style="height: 100%"
			page
			@selection-change="handleSelectionChange"
			ref="form"
			@submit="submit"
		></es-data-table>
		<es-dialog
			title="新增/编辑通知公告"
			size="md"
			:visible.sync="visible"
			v-if="visible"
			ref="visible"
			@close="handleclose"
			:drag="false"
		>
			<viewComponent :id="id" @cancel="cancel" v-if="visible"></viewComponent>
		</es-dialog>
	</div>
</template>

<script>
import viewComponent from './components/view.vue';
export default {
	components: {
		viewComponent
	},
	data() {
		return {
			id: '',
			type: 'view',
			visible: false,
			toolbar: [
				{
					type: 'filter',
					contents: [
						{
							type: 'text',
							label: '通知标题',
							name: 'sendTitle',
							placeholder: '请输入',
							col: 4
						},
						{
							type: 'select',
							label: '通知类型',
							placeholder: '请选择',
							name: 'sendType',
							col: 4,
							sysCode: 'send_type'
						},
						{
							name: 'sendTimeStr',
							placeholder: '请选择',
							col: 4,
							label: '通知时间',
							type: 'daterange',
							value: '',
							rules: {
								required: true,
								message: '请选择',
								trigger: 'change'
							}
						}
					]
				}
			],
			thead: [
				{
					title: '任务/通知标题',
					field: 'sendTitle',
					align: 'left'
				},
				{
					title: '任务/通知类型',
					field: 'sendTypeText',
					align: 'center'
				},
				{
					title: '通知日期',
					field: 'sendTimeStr',
					align: 'center'
				},
				{
					title: '操作',
					type: 'handle',
					fixed: 'right',
					width: '80',
					align: 'center',
					events: [
						{
							text: '查看'
						}
					]
				}
			],
			tableData: [],
			selectList: [],
			selectId: ''
		};
	},
	watch: {},
	mounted() {
		this.$.ajax({
			url: '/ybzy/projectTask/listJson'
		})
			.then(res => {
				this.tableData = res.results.records;
			})
			.catch(err => {});
	},
	methods: {
		cancel() {
			this.visible = false;
		},
		closeDia(data) {
			this.$.ajax({
				url: '/ybzy/projectTask/listJson'
			})
				.then(res => {
					this.tableData = res.results.records;
				})
				.catch(err => {});
			this.visible = false;
		},
		btnClick(data) {
			switch (data.handle.text) {
				case '删除':
					this.$confirm('是否将选择的全部删除?', '提示', {
						confirmButtonText: '确定',
						cancelButtonText: '取消',
						type: 'warning'
					}).then(() => {
						this.$.ajax({
							url: '/ybzy/projectTask/deleteById',
							method: 'POST',
							data: {
								id: data.row.id
							}
						})
							.then(res => {
								this.$message.success('删除成功');
								this.$.ajax({
									url: '/ybzy/projectTask/listJson'
								})
									.then(res => {
										this.tableData = res.results.records;
									})
									.catch(err => {});
							})
							.catch(err => {});
					});

					break;
				case '全部删除':
					if (this.selectList.length === 0) {
						break;
					}
					// console.log(this.selectList, 'this.selectList');
					this.$confirm('是否将选择的全部删除?', '提示', {
						confirmButtonText: '确定',
						cancelButtonText: '取消',
						type: 'warning'
					}).then(() => {
						this.$.ajax({
							url: '/ybzy/projectTask/delete',
							method: 'post',
							data: this.selectList,

							format: false
						})
							.then(res => {
								this.$.ajax({
									url: '/ybzy/projectTask/listJson'
								})
									.then(res => {
										this.tableData = res.results.records;
									})
									.catch(err => {});
							})
							.catch(err => {});
					});

					break;
				case '新增':
					this.visible = true;
					this.type = 'add';
					break;
				case '编辑':
					this.visible = true;
					this.type = 'edit';
					this.id = data.row.id;
					break;
				case '查看':
					this.visible = true;
					this.type = 'view';
					this.id = data.row.id;
					break;
				default:
					break;
			}
		},
		handleSelectionChange(data) {
			this.selectList = [];
			data.forEach(i => {
				this.selectList.push(i.id);
			});
		},
		handleclose() {},
		submit(data) {
			console.log(data);
			this.$.ajax({
				url: '/ybzy/projectTask/listJson',
				params: {
					sendTitle: data.data.sendTitle,
					sendType: data.data.sendType,
					startTime: data.data.startdate[0],
					endTime: data.data.startdate[1]
				}
			})
				.then(res => {
					this.tableData = res.results.records;
				})
				.catch(err => {});
		}
	}
};
</script>
<style lang="scss" scoped>
::v-deep .el-dialog__body {
	overflow: auto;
}
</style>
