<template>
	<es-tabs v-model="activeName" @tab-click="handleClick">
		<el-tab-pane label="回复信息" name="1">
			<es-form
				ref="form"
				v-loading="loading"
				:model="formData"
				:contents="formItemList"
				label-width="100px"
				:genre="2"
				collapse
				@click="click"
			/>
		</el-tab-pane>
		<el-tab-pane label="流程信息" name="2">
			<div class="content-info">
				<div class="content-title">1、企业回复</div>
				<el-descriptions
					v-for="(item, index) in flowList?.[0]"
					:key="index"
					class="margin-top"
					title=""
					:column="2"
					border
				>
					<el-descriptions-item label="是否参加：">
						{{ item?.isJoin || '' }}
					</el-descriptions-item>
					<el-descriptions-item label="操作人：">
						{{ item?.operator || '' }}
					</el-descriptions-item>
					<el-descriptions-item label="操作时间：">
						{{ item?.operationTime || '' }}
					</el-descriptions-item>
				</el-descriptions>
				<div class="content-title">2、二级学院审核</div>
				<el-descriptions
					v-for="(item, index) in flowList?.[0]"
					:key="index"
					class="margin-top"
					title=""
					:column="2"
					border
				>
					<el-descriptions-item label="学院名称：">
						{{ item?.unit || '' }}
					</el-descriptions-item>
					<el-descriptions-item label="审核结果：">
						{{ item?.auditStatesDes || '' }}
					</el-descriptions-item>
					<el-descriptions-item label="操作人：">
						{{ item?.operator || '' }}
					</el-descriptions-item>
					<el-descriptions-item label="操作时间：">
						{{ item?.operationTime || '' }}
					</el-descriptions-item>
				</el-descriptions>
				<div class="content-title">3、招就处审核</div>
				<el-descriptions
					v-for="(item, index) in flowList?.[0]"
					:key="index"
					class="margin-top"
					title=""
					:column="2"
					border
				>
					<el-descriptions-item label="审核结果：">
						{{ item?.auditStatesDes || '' }}
					</el-descriptions-item>
					<el-descriptions-item label="操作人：">
						{{ item?.operator || '' }}
					</el-descriptions-item>
					<el-descriptions-item label="操作时间：">
						{{ item?.operationTime || '' }}
					</el-descriptions-item>
				</el-descriptions>
			</div>
		</el-tab-pane>
		<Next :id="formData.flowId" ref="nextRef" @closeDialog="$emit('closeDialog')" />
		<Detail ref="detailRef" :form-data="detailFormData" />
	</es-tabs>
</template>

<script>
import Next from './next.vue';
import Detail from './detail.vue';
import interfaceUrl from '@/http/job/jobDualSelect/api.js';
import httpApi from '@/http/job/jobCoPost/api.js';
export default {
	name: 'ResumeView',
	components: {
		Next,
		Detail
	},
	props: {
		info: {
			type: Object,
			default: () => {
				return {};
			}
		}
	},
	data() {
		return {
			loading: false,
			activeName: '1',
			formData: {
				table: []
			},
			flowList: [],
			detailFormData: {}
		};
	},
	computed: {
		formItemList() {
			const readonly = true;
			return [
				{
					title: '企业信息',
					contents: [
						{
							name: 'title',
							placeholder: '',
							label: '双选会标题',
							readonly,
							col: 12,
							event: 'handleChange'
						},
						{
							name: 'enterpriseName',
							placeholder: '',
							label: '企业名称',
							readonly,
							col: 12,
							event: 'handleChange'
						},
						{
							name: 'joinStartEndDes',
							placeholder: '',
							label: '参加双选会时间',
							readonly,
							col: 12,
							event: 'handleChange'
						},
						{
							name: 'contacts',
							placeholder: '',
							label: '联系人',
							readonly,
							col: 6,
							event: 'handleChange'
						},
						{
							name: 'contactNum',
							placeholder: '',
							label: '联系方式',
							readonly,
							col: 6,
							event: 'handleChange'
						}
					]
				},
				{
					title: '岗位信息',
					contents: [
						{
							type: 'table',
							name: 'table',
							label: '',
							events: {
								change: () => {},
								'cell-click': () => {
									console.log(222);
								}
							},
							thead: [
								{
									title: '职位名称',
									field: 'name',
									type: 'text',
									align: 'center',
									filterable: true
									// width: '280'
								},
								{
									title: '工作性质',
									field: 'jobNature',
									align: 'center',
									type: 'text'
									// width: '180',
									// labelKey: 'mname',
									// symbol: '、'
								},
								{
									title: '学历要求',
									field: 'education',
									align: 'center',
									type: 'text'
									// width: '180'
								},
								{
									title: '工作经验',
									field: 'workExperience',
									align: 'center',
									type: 'text'
									// width: '180'
								},
								{
									title: '薪资区间',
									field: 'salaryStructure',
									align: 'center',
									type: 'text'
									// width: '180'
								},
								{
									title: '岗位类型',
									field: 'postTypeDes',
									align: 'center',
									type: 'text'
									// width: '180'
								},
								// {
								// 	title: '所属二级学院',
								// 	field: 'date',
								// 	align: 'center',
								// 	type: 'date',
								// 	width: '180'
								// },
								// {
								// 	title: '审核状态',
								// 	field: 'auditStatusStr',
								// 	align: 'center',
								// 	type: 'text'
								// 	// width: '180'
								// },
								{
									title: '操作',
									type: 'handle',
									align: 'center',
									// width: '100',
									template: '',
									fixed: 'right',
									events: [
										{
											code: 'look',
											text: '查看',
											icon: 'el-icon-view'
										}
									]
								}
							]
						}
					]
				},
				{
					type: 'submit',
					skin: 'lay-form-btns',
					contents:
						this.formData?.auditStates === 0
							? [
									{
										type: 'primary',
										text: '审核'
									},
									{
										type: 'reset',
										plain: true,
										text: '关闭'
									}
							  ]
							: [
									{
										type: 'reset',
										plain: true,
										text: '关闭'
									}
							  ]
				}
			];
		}
	},
	created() {
		this.doHandleFormData(this.info);
	},
	methods: {
		// tab切换
		handleClick() {},
		click(objK, value) {
			const text = objK.text || objK.handle.text;
			switch (text) {
				case '审核':
					this.$refs.nextRef.showForm = true;
					break;
				case '查看':
					this.loading = true;
					this.$request({
						url: httpApi.jobCoPostInfo,
						params: { id: objK.row.id }
					}).then(res => {
						this.loading = false;
						if (res.rCode === 0) {
							this.detailFormData = res.results;
							this.$refs.detailRef.showForm = true;
						}
					});
					break;
				default:
					break;
			}
		},
		doHandleFormData(newData) {
			this.loading = true;
			this.formData = newData;
			//企业回复中职位列表
			this.$request({
				url: interfaceUrl.postListByMappingId,
				params: { ids: newData.postIds },
				method: 'GET'
			}).then(res => {
				this.loading = false;
				if (res.rCode === 0) {
					this.$set(this.formData, 'table', res.results);
				}
			});
			//根据邀请id查询审核列表
			this.$request({
				url: interfaceUrl.getAuditList + this.formData.id,
				method: 'GET'
			}).then(res => {
				if (res.rCode === 0) {
					this.flowList = res.results;
				}
			});
		}
	}
};
</script>

<style scoped lang="scss">
.content-info {
	padding: 10px 20px;
	height: 100%;
	overflow: auto;
	.content-title {
		padding: 10px 0;
	}
	::v-deep .is-bordered-label {
		width: 100px !important;
	}
	::v-deep .el-descriptions-item__content {
		width: 33%;
	}
}
</style>
