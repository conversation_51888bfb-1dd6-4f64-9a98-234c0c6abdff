/**
 * 角色相关接口
 */
const interfaceUrl = {
	listJson: '/ybzy/platrole/listJson', // 列表
	info: '/ybzy/platrole', // 获取
	save: '/ybzy/platrole/save', // 保存
	update: '/ybzy/platrole/update', // 修改
	deleteBatchIds: '/ybzy/platrole/deleteBatchIds', // 删除
	userSelectList: '/ybzy/platuser/selectList', // 用户列表
	resourceTreeList: '/ybzy/platroleresource/getTreeList', // 应用菜单树
	apiTreeList: '/ybzy/platfrontapi/getTreeList', // api菜单树
};
export default interfaceUrl;
