<template>
	<div class="card" :style="{ width: cardData.width || '300px' }">
		<img :src="cardData.img || require('@ast/images/sys/card.png')" alt="" />
		<div class="content">
			<p>
				{{ cardData.num }}
				<span>{{ cardData.unit }}</span>
			</p>
			<span>{{ cardData.title }}</span>
		</div>
	</div>
</template>

<script>
export default {
	props: {
		cardData: {
			type: Object,
			defuilt: () => {
				return {
					width: '300px',
					img: require('@ast/images/sys/card.png'),
					title: '标题',
					unit: '人',
					num: '0'
				};
			}
		}
	},
	data() {
		return {
			options: [
				{
					value: '1',
					label: '本月'
				},
				{
					value: '2',
					label: '本周'
				},
				{
					value: '3',
					label: '今日'
				}
			],
			value: {
				value: '1'
			}
		};
	},
	methods: {
		handleChange(val) {
			console.log(val);
		}
	}
};
</script>
<style lang="scss" scoped>
@import '@ast/style/public.scss';

.card {
	@include flexBox(flex-start);
	width: 300px;
	padding: 20px;
	border-radius: 8px;
	background: #fff;
	img {
		width: 50px;
		height: 50px;
	}
	.content {
		margin-left: 16px;
		p {
			font-size: 22px;
			font-weight: 550;
		}
		span {
			font-size: 16px;
			font-weight: 500;
			color: #999;
		}
	}
}
</style>
