<template>
	<es-form
		ref="form"
		:model="formData"
		:contents="formList"
		v-bind="$attrs"
		:submit="
			type === 'audit' ||
            type === 'view'
		"
		@submit="handleSubmit"
		@reset="handleCancel"
		height="100%" :genre="2" collapse
	></es-form>
</template>
<script>

import interfaceUrl from '@/http/platform/certapply.js';
import $ from 'eoss-ui/lib/utils/util';
import request from 'eoss-ui/lib/utils/http';
import SnowflakeId from "snowflake-id";

export default {
	name: 'auditDialog',
	props: {
		selectInfo: {
			type: String
		},
		formInfo: {
			type: Object,
			default: () => {}
		},
		type: {
			type: String
		},
	},
	watch: {
        selectInfo: {
            handler(newValue, oldValue) {
				let id = this.selectInfo;
				this.getDictionary();
				this.getCertificationStuffList(this.formInfo.enterpriseTypeId);
				this.$request({
					url: interfaceUrl.info+'/'+id,
					method: 'GET'
				}).then(res => {
					if (res.rCode == 0) {
						this.formData = res.results;
						this.handleFormData(res);
					} else {
						this.$message.error(res.msg);
					}
				});
            },
            deep: true 
        },
    },
	data() {
		return {
			formData: {},
			typeDicData:[],
			auditBtn: {
                type: 'submit',
                skin: 'lay-form-btns',
                contents: [
                    {
                        type: 'primary',
                        text: '审核通过',
                        event: 'confirm',
                    },
                    {
                        type: 'primary',
                        text: '审核不通过',
                        event: 'confirm',
                    },
                ]
            },
			viewBtn:{
                type: 'submit',
                skin: 'lay-form-btns',
                contents: [
                    {
                        type: 'reset',
                        text: '关闭',
                        event: 'cancel',
                    },
                ]
            },
			certificationStuffList:[],//资料列表
		};
	},
	computed: {
		formList() {
			let indexFormList =  [
				{
					title: '基本信息',
					contents: [
						{
							label: '认证来源',
							name: 'certifyFrom',
							type: 'select',
							placeholder: '请选择认证来源',
							readonly: true,
							rules: {
								required: false,
								message: '请选择认证来源',
								trigger: 'blur'
							},
							verify: 'required',
							col: 5,
							sysCode: 'plat_person_cert_apply_certify_from',
							'label-key': 'shortName',
							'value-key': 'cciValue',
						},
						{
							label: '申请时间',
							name: 'applyTime',
							placeholder: '请选择申请时间',
							readonly: true,
							controls: false,
							rules: {
								required: false,
								message: '请选择申请时间',
								trigger: 'blur'
							},
							verify: 'required',
							col: 5,
						},
						{
							label: '申请人',
							name: 'applyUserName',
							placeholder: '请选择申请人账号',
							readonly: true,
							rules: {
								required: false,
								message: '请选择申请人账号',
								trigger: 'blur'
							},
							verify: 'required',
							col: 5,
						},
						{
							label: '姓名',
							name: 'xm',
							placeholder: '请输入姓名',
							readonly: true,
							rules: {
								required: false,
								message: '请输入姓名',
								trigger: 'blur'
							},
							verify: 'required',
							col: 5
						},
						{
							label: '业务类型',
							name: 'businessTypeId',
							type: 'select',
							placeholder: '请输入业务类型',
							readonly: true,
							rules: {
								required: false,
								message: '请输入业务类型',
								trigger: 'blur'
							},
							verify: 'required',
							col: 10,
							data: this.typeDicData,
							'label-key': 'name',
							'value-key': 'value',
						}
					]
				},
			];
			if(this.certificationStuffList.length>0){
				//放入认证资料信息
				indexFormList.push({
					title: '认证资料',
					contents: this.certificationStuffList
				})
			}

			let auditRequired = this.type==='audit';
			let audit = {
                title: '审核意见',
                contents: [
					{
						label: '审核状态',
						name: 'status',
						type: 'select',
						placeholder: '请选择审核状态',
						readonly: true,
						rules: {
							required: false,
							message: '请选择审核状态',
							trigger: 'blur'
						},
						verify: 'required',
						col: 10,
						sysCode: 'plat_bus_cert_apply_status',
						'label-key': 'shortName',
						'value-key': 'cciValue',
					},
                    {
                        label: '审核意见',
                        name: 'auditComment',
                        placeholder: '请输入审核意见',
                        type: 'textarea',
                        rules: {
                            required: auditRequired,
                            message: '请输入审核意见',
                            trigger: 'blur'
                        },
						verify: 'required',
                        rows: 5,
                        col: 10
                    }
                ]
            }
            
            if(this.type==="audit"){
                indexFormList.push(audit); 
				indexFormList.push(this.auditBtn);
            }else{
                for(var i in audit.contents){
                    audit.contents[i].readonly=true;
					audit.contents[i].required=false;
                }
                indexFormList.push(audit); 
				indexFormList.push(this.viewBtn);
            }

			
			
			return indexFormList;
		}
	},
	created() {
		let id = this.selectInfo;
		this.getDictionary();
		this.getCertificationStuffList(this.formInfo.enterpriseTypeId);
		this.$request({
            url: interfaceUrl.info+'/'+id,
            method: 'GET'
        }).then(res => {
            if (res.rCode == 0) {
                this.formData = res.results;
                this.handleFormData(res);
            } else {
                this.$message.error(res.msg);
            }
        });
	},
	methods: {
		
		/**
         * 处理formData数据为可展示数据
         */
        handleFormData(res){
			this.formData.status = res.results.status+"";
        },
		handleSubmit(data,obj) {
			const loading = $.loading(this.$loading, '提交中');
            let auditComment = data.auditComment;
            let status;
            if(obj.text==="审核通过"){
                status = 1;
            }else{
                status = 2;
            }
            this.$request({
				url: interfaceUrl.audit,
				params: {"id":this.selectInfo,"status":status,"auditComment":auditComment},
				method: 'POST'
			}).then(res => {
                loading.close();
				this.$emit('cancel', false);
				if (res.rCode == 0) {
					this.$emit('refresh'); // 刷新数据
					this.$message.success(res.msg);
                    this.formData1 = {};
				} else {
					this.$message.error(res.msg);
				}
			});
		},
		// 隐藏对话框
		handleCancel(event) {
			if (event.type === 'reset') {
				this.$emit('refresh');
			}
		},
		/**
         * 获取下拉字典
         */
         getDictionary(){
             
             this.$request({
                 url: interfaceUrl.typeDic,
                 method: 'get'
             }).then(res => {
                 if (res.rCode == 0) {
                     this.typeDicData = res.results;
                 }
             });
 
         },
		 /**
		  * 获取企业类型认证资料配置
		  * @param {*} enterpriseTypeId 
		  */
		 getCertificationStuffList(enterpriseTypeId){
			this.$request({
                 url: interfaceUrl.certificationStuff+'?enterpriseTypeId=' + enterpriseTypeId,
                 method: 'get'
             }).then(res => {
                 if (res.rCode == 0) {
					 let stuffList = [];
					 if(res.results.length>0){
						for(let i in res.results){
							stuffList.push({
								name: 'fj',
								label: res.results[i].dataName,
								type: 'attachment',
								value: '',
								readonly: true,
								code: res.results[i].dataCode,
								preview:true,
								// onPreview: res => {
								// console.log(res);
								// },
								append: {
								label: '下载模板',
								event: res => {
									console.log(res);
								}
								},
								ownId: this.selectInfo, // 业务id
								//rules: {
									//required: true,
								//    message: '请上传营业执照',
								//    trigger: 'blur'
								//}
							});
						}
						
					}
                     this.certificationStuffList = stuffList;
                 }
             });
		 }
	}
};
</script>
<style  lang="scss">
@import '../../../../assets/style/style.scss';


.el-dialog__body {
    overflow: auto !important;
	.es-form .es-collapse{
        height: 100%;
    }
    


}
</style>