<template>
	<div class="content" style="height: 100%">
		<div style="width: 100%; height: 100%">
			<es-data-table
				:thead="thead"
				:toolbar="toolbar"
				:page="page"
				:url="dataTableUrl"
				style="width: 100%"
				:param="params"
				@btnClick="btnClick"
				@search="hadeSubmit"
				@reset="resetTable"
			></es-data-table>
		</div>
	</div>
</template>

<script>
import api from '@/http/maintain/applicationApi';
export default {
	name: 'WorkStatistics', //维修人员工作统计
	data() {
		return {
			params: {},
			submitFilterParams: {},
			dataTableUrl: api.statisticsPersonWork,
			thead: [
				{
					label: '基本信息',
					childHead: [
						{
							label: '姓名',
							align: 'center',
							field: 'name'
						},
						{
							label: '性别',
							align: 'center',
							field: 'sex',
							data: [
								{ label: '男', value: '0' },
								{ label: '女', value: '1' }
							]
						},
						{
							label: '联系电话',
							align: 'center',
							field: 'phone'
						},
						{
							label: '人员类型',
							align: 'center',
							field: 'type',
							data: [
								{ label: '内部', value: '0' },
								{ label: '外部', value: '1' }
							]
						}
					]
				},
				{
					label: '工作量',
					childHead: [
						{
							label: '派单数',
							field: 'dispatchNum',
							align: 'center',
							value: 0
						},
						{
							label: '已完成',
							field: 'accomplishNum',
							align: 'center',
							value: 0
						},
						{
							label: '未完成',
							field: 'unfinishedNum',
							align: 'center',
							value: 0
						},
						{
							label: '协助已完成',
							field: 'assistNum',
							width: '120px',
							align: 'center',
							value: 0
						}
					]
				},
				{
					label: '时效性',
					childHead: [
						{
							label: '正常',
							field: 'normalNum',
							align: 'center',
							value: 0
						},
						{
							label: '超时',
							field: 'overtimeNum',
							align: 'center',
							value: 0
						}
					]
				},
				{
					label: '服务满意度',
					childHead: [
						{
							label: '五星',
							field: 'scoreFiveNum',
							align: 'center',
							value: 0
						},
						{
							label: '四星',
							field: 'scoreFourNum',
							align: 'center',
							value: 0
						},
						{
							label: '三星',
							field: 'scoreThreeNum',
							align: 'center',
							value: 0
						},
						{
							label: '二星',
							field: 'scoreTwoNum',
							align: 'center',
							value: 0
						},
						{
							label: '一星',
							field: 'scoreOneNum',
							align: 'center',
							value: 0
						}
					]
				}
			],
			toolbar: [
				{
					type: 'button',
					contents: [
						{
							text: '导出',
							code: 'daying',
							type: 'primary'
						}
					]
				},
				{
					type: 'search',
					reset: true,
					contents: [
						{
							type: 'text',
							name: 'userName',
							placeholder: '姓名',
							clearable: true
						},
						{
							type: 'daterange',
							name: 'dispatchDateRange',
							default: true,
							placeholder: '日期',
							clearable: true
						},
						{
							type: 'select',
							name: 'personType',
							placeholder: '人员类型',
							data: [
								{ label: '内部', value: '0' },
								{ label: '外部', value: '1' }
							],
							clearable: true
						}
					]
				}
			],
			page: {
				pageSize: 20,
				totalCount: 0,
				hideOnSinglePage: true
			}
		};
	},
	created() {},
	mounted() {},
	methods: {
		/**
		 * 表格行高
		 */
		tableRowClassName() {
			return {
				height: '54px !important'
			};
		},
		hadeSubmit(e) {
			this.submitFilterParams = e;
		},
		// 操作按钮
		btnClick(res) {
			let code = res.handle.code;
			//打印下载
			if (code == 'daying') {
				this.exportFile();
			}
		},
		//导出函数
		exportFile() {
			let isDev = process.env.NODE_ENV === 'development' ? '/api' : '';
			const paramAll = { ...this.params, ...this.submitFilterParams };
			let url = `${isDev}/ybzy/hqMp/statisticsExport`;
			let urlParams = this.objToUrlParams(paramAll);
			if (urlParams) {
				url += '?' + urlParams;
			}
			window.open(url, '_self');
		},
		objToUrlParams(obj) {
			return Object.keys(obj)
				.filter(key => obj[key] !== '' && obj[key] !== null)
				.map(key => encodeURIComponent(key) + '=' + encodeURIComponent(obj[key]))
				.join('&');
		},
		resetTable() {
			this.submitFilterParams = {};
			this.$refs.table.resetTable();
		}
	}
};
</script>
