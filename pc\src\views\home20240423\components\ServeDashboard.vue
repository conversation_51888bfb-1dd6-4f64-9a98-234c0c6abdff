<template>
	<div
		class="serve-dashboard"
		:style="{
			transform: `scale(${scale})`
		}"
	>
		<Card title="教师情况" :my-style="{ width: '24.48%', height: '45%' }"><Teacher /></Card>
		<Card title="毕业就业" :my-style="{ width: '24.48%', height: '53.63%' }"><Employment /></Card>
		<Card :my-style="{ width: '48.33%', height: '18.89%' }"><CollegeInformation /></Card>
		<Card title="科研成果" :my-style="{ width: '48.33%', height: '28.66%' }">
			<ScientificResearch />
		</Card>
		<Card title="课程情况" :my-style="{ width: '48.33%', height: '14.76%' }">
			<CourseSituation />
		</Card>
		<Card :my-style="{ width: '48.33%', height: '32.94%' }"><Experiment /></Card>
		<Card title="学生情况" :my-style="{ width: '24.48%', height: '44.62%' }">
			<StudentSituation />
		</Card>
		<Card title="访客预约" :my-style="{ width: '24.48%', height: '14%' }">
			<VisitorAppointment />
		</Card>
		<Card title="公共服务" :my-style="{ width: '24.48%', height: '38.65%' }">
			<PublicService />
		</Card>
	</div>
</template>

<script>
import Card from './card.vue';
import Teacher from './cockpit/teacher.vue';
import Employment from './cockpit/employment.vue';
import ScientificResearch from './cockpit/scientific-research.vue';
import CollegeInformation from './cockpit/college-information.vue';
import CourseSituation from './cockpit/course-situation.vue';
import Experiment from './cockpit/experiment.vue';
import StudentSituation from './cockpit/student-situation.vue';
import VisitorAppointment from './cockpit/visitor-appointment.vue';
import PublicService from './cockpit/public-service.vue';

export default {
	name: 'ServeDashboard',
	components: {
		Card,
		Teacher,
		Employment,
		ScientificResearch,
		CollegeInformation,
		CourseSituation,
		Experiment,
		StudentSituation,
		VisitorAppointment,
		PublicService
	},
	data() {
		return {
			scale: 1
		};
	},
	created() {
		this.scale = this.$utils.toolViewRatio();
	},
	methods: {}
};
</script>

<style lang="scss" scoped>
.serve-dashboard {
	transform-origin: top left;
	width: 1920px;
	height: 940px;
	padding: 0 0.73% 1.85% 0.73%;
	display: flex;
	flex-wrap: wrap;
	flex-direction: column;
	justify-content: space-between;
	align-items: center;
}
</style>
