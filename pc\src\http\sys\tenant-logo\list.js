import httpApi from './api.js';
export default {
	computed: {
		component() {
			return {
				// 树表结构
				//name: 'es-tree-group'
				// 纯列表结构
				name: 'es-data-table'
			};
		},
		treeParam() {
			return {
				// 不需要左边树 可以
				//url: httpApi.platTenantLogoUrlTree,
				//title: '分类树'
			};
		},
		table() {
			return {
				url: httpApi.platTenantLogoUrlList,
				param: { handlerType: '' },
				full: true,
				fit: true,
				page: true,
				stripe: true,
				numbers: true,
				'row-key': 'id',
				toolbar: [
					{
						type: 'button',
						contents: [
							{
								text: '新增',
								type: 'primary'
							}
						]
					},
					{
						type: 'search',
						reset: true,
						contents: [
							//{
							//	name: 'accountName_like',
							//	placeholder: '配置名称',
							//	label: '配置名称',
							//	col: 6
							//},
							{
								type: 'date',
								col: 6,
								name: 'updateTime_begin',
								label: '更新开始时间',
								placeholder: '更新开始时间'
							},
							{
								type: 'date',
								col: 6,
								name: 'updateTime_end',
								label: '更新结束时间',
								placeholder: '更新结束时间'
							}
						]
					}
				],
				thead: [
					{
						title: '租户名称',
						align: 'left',
						field: 'name'
					},
					{
						title: '标题',
						align: 'left',
						field: 'title'
					},
					{
						title: '管理端LOGO',
						align: 'left',
						field: 'manageLogo'
					},
					{
						title: '管理端登录LOGO',
						align: 'left',
						field: 'manageLoginLogo'
					},
					{
						title: '管理端登录背景图',
						align: 'left',
						field: 'manageLoginBg'
					},
					{
						title: 'PC用户端LOGO',
						align: 'left',
						field: 'pcLogo'
					},
					{
						title: 'PC用户端登陆背景图',
						align: 'left',
						field: 'pcLoginBj'
					},
					{
						title: 'PC用户端职教服务大厅背景图',
						align: 'left',
						field: 'pcTecherBj'
					},
					
					{
						title: '操作',
						type: 'handle',
						width: 180,
						events: [
							{
								text: '查看'
							},
							{
								text: '编辑'
							},
							{
								text: '删除'
							}
						]
					}
				]
			};
		}
	}
};
