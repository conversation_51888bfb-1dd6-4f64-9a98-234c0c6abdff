<template>
	<div class="bodyBox">
		<div class="statsBox">
			<div class="statsItemBox" style="background-color: #65d8ff">
				<div>
					<h3>社团类型</h3>
					<span>{{ societyTypeCount }}个</span>
				</div>
			</div>
			<div class="statsItemBox" style="background-color: #ff6b0c">
				<div>
					<h3>社团数量</h3>
					<span>{{ societyCount }}个</span>
				</div>
			</div>
			<div class="statsItemBox" style="background-color: #f7c162">
				<div>
					<h3>收入金额</h3>
					<span>{{ societyIn }}￥</span>
				</div>
			</div>
			<div class="statsItemBox" style="background-color: #c0a1ff">
				<div>
					<h3>支出金额</h3>
					<span>{{ societyEx }}￥</span>
				</div>
			</div>
		</div>
		<div class="tableBox">
			<!-- 社团审核 -->
			<div class="tableModule">
				<div class="tableTopDiv">
					<div style="float: left">社团审核</div>
					<div style="float: right"><a href="#">更多&gt;&gt;&gt;</a></div>
				</div>
				<!-- <h3>社团审核</h3> -->
				<es-data-table
					v-if="true"
					ref="table"
					:row-style="tableRowClassName"
					:thead="societyThead"
					:toolbar="toolbar"
					:url="societyDataTableUrl"
					:numbers="true"
				></es-data-table>
			</div>
			<!-- 成员审核 -->
			<div class="tableModule" style="margin-left: 20px">
				<div class="tableTopDiv">
					<div style="float: left">入社审核</div>
					<div style="float: right"><a href="#">更多&gt;&gt;&gt;</a></div>
				</div>
				<es-data-table
					v-if="true"
					ref="table"
					:row-style="tableRowClassName"
					:thead="memberThead"
					:toolbar="toolbar"
					:url="memberDataTableUrl"
					:numbers="true"
					form
				></es-data-table>
			</div>
		</div>
	</div>
</template>

<script>
import interfaceUrl from '@/http/society/societyHome/stats.js';
import api from '@/http/society/societyBaseInfo/api';
import societyMemberApi from '@/http/society/societyStudentAudit/api';

export default {
	data() {
		return {
			societyTypeCount: 0,
			societyCount: 0,
			societyIn: 0.0,
			societyEx: 0.0,
			//社团申请列表
			societyDataTableUrl: api.societyBaseInfoListJson,
			memberDataTableUrl: societyMemberApi.societyStudentAuditList,
			societyThead: [
				{
					title: '社团名称',
					align: 'left',
					field: 'name',
					showOverflowTooltip: true
				},
				{
					title: '社团社长',
					width: '150px',
					align: 'center',
					field: 'principalName',
					showOverflowTooltip: true
				},
				{
					title: '申请人',
					width: '150px',
					align: 'center',
					field: 'createUserName',
					showOverflowTooltip: true
				},
				{
					title: '申请时间',
					width: '150px',
					align: 'center',
					field: 'createTime',
					showOverflowTooltip: true
				},
				{
					title: '审核状态',
					width: '150px',
					align: 'center',
					field: 'auditStatusVO',
					showOverflowTooltip: true,
					render: (h, param) => {
						return h(
							'el-tag',
							{ props: { type: param.row.auditStatus === 3 ? 'danger' : '' } },
							param.row.auditStatusVO
						);
					}
				}
			],
			memberThead: [
				{
					title: '社团名称',
					align: 'left',
					field: 'societyName',
					label: 'label',
					showOverflowTooltip: true
				},
				{
					title: '申请人',
					width: '140px',
					align: 'left',
					field: 'applyUserName',
					showOverflowTooltip: true
				},
				{
					title: '所属学院',
					width: '150px',
					align: 'center',
					field: 'applyUserCollege',
					showOverflowTooltip: true
				},
				{
					title: '所属班级',
					width: '150px',
					align: 'center',
					field: 'applyUserClass',
					showOverflowTooltip: true
				},
				{
					title: '审核状态',
					width: '150px',
					align: 'center',
					field: 'auditStatusVO',
					showOverflowTooltip: true,
					render: (h, param) => {
						return h(
							'el-tag',
							{ props: { type: param.row.auditStatus === 3 ? 'danger' : '' } },
							param.row.auditStatusVO
						);
					}
				}
			],
			toolbar: []
		};
	},
	created() {
		this.$nextTick(() => {
			this.init();
		});
	},
	mounted() {},
	methods: {
		/**
		 * 表格行高
		 */
		tableRowClassName() {
			return {
				height: '54px !important'
			};
		},
		init() {
			this.loadBaseData();
		},
		//获取基本统计数据（社团类型数量、社团数量、收入支出金额）
		loadBaseData() {
			this.$request({ url: interfaceUrl.numStats, method: 'GET' }).then(res => {
				if (res.rCode !== 0) {
					return;
				}
				//填充基本数据
				this.societyTypeCount = res.results.societyTypeCount;
				this.societyCount = res.results.societyCount;
				this.societyIn = res.results.societyIn;
				this.societyEx = res.results.societyEx;
			});
		}
	}
};
</script>

<style scoped lang="scss">
.bodyBox {
	width: 100%;
	height: 100%;
	padding: 10px;
	overflow: auto;
}
.bodyBox > div {
	padding-top: 10px;
	padding-bottom: 10px;
	// border-bottom: 1px solid grey;
}

.bodyBox > div:nth-child(3) {
	border-bottom: none;
}

.statsBox {
	width: 100%;
	height: 20%;
	display: flex;
	flex-direction: row;
	justify-content: space-around;
}
.statsItemBox {
	width: 19%;
	height: 120px;
	border-radius: 10px;
	padding: 20px;
	display: flex;
	color: rgb(255, 255, 255);
	flex-direction: row;
}
.statsItemBox > div {
	margin-left: 20px;
	h3 {
		margin-bottom: 10px;
		font-size: 26px;
	}
}

.statsItemBox span {
	font-size: 20px;
}

.tableBox {
	width: 100%;
	height: 80%;
	display: flex;
}

.tableModule{
	width: 49%; height: 100%;
}

.tableTopDiv {
	height: 20px;
	font-size: 1.17em;
	font-weight: bold;
}
</style>
