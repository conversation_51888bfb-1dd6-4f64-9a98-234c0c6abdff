<template>
	<div :id="id" style="width: 100%; height: 100%"></div>
</template>
<script>
export default {
	props: {
		id: {
			type: String,
			required: true
		},
		// title: {
		// 	type: String,
		// 	required: true
		// },
		// color: {
		// 	type: Array,
		// 	required: true
		// },
		chartOptions: {
			type: Object,
			default: () => {
				return {
					legend: {}
				};
			}
		},
		seriesData: {
			type: Array,
			required: true
		}
	},
	data() {
		return {
			chart: null
		};
	},
	watch: {
		seriesData() {
			this.draw();
		}
	},
	mounted() {
		this.draw();
	},

	methods: {
		// lookDetail() {
		// 	this.$emit('getDialogList', 'dwzsfz', this.title);
		// },
		/**
		 * @description: 绘制图形
		 * @param {String} id -dom id
		 * @param {Arrayj} data -数据源
		 **/
		draw() {
			let { legend, legendDatas, series } = this.chartOptions;
			let legendData = legendDatas ? legendDatas : ['待开放', '预约中', '已结束'];
			let colorList = ['#5FDA8A', '#DE7963', '#0377E8', '#F5D559', '#67C8CA'];
			let option = {
				// backgroundColor: {
				// 	type: 'linear',
				// 	x: 0,
				// 	y: 0,
				// 	x2: 1,
				// 	y2: 1,
				// 	colorStops: [
				// 		{
				// 			offset: 0,
				// 			color: '#0f2c70' // 0% 处的颜色
				// 		},
				// 		{
				// 			offset: 1,
				// 			color: '#091732' // 100% 处的颜色
				// 		}
				// 	],
				// 	globalCoord: false // 缺省为 false
				// },
				// title: {
				// 	text: '品种',
				// 	x: 'center',
				// 	y: 'center',
				// 	textStyle: {
				// 		color: '#fff'
				// 	}
				// },
				tooltip: {
					trigger: 'item',
					backgroundColor: 'rgba(128,187,224,0.9)',
					// backgroundColor: 'rgba(13,5,30,.6)',
					textStyle: {
						color: '#ffffff'
					},
					borderWidth: 1,
					padding: 1,
					formatter: function (parms) {
						var str =
							parms.marker +
							'' +
							parms.data.name +
							'</br>' +
							'数量：' +
							parms.data.value +
							'个</br>' +
							'占比：' +
							parms.percent +
							'%';
						return str;
					}
				},
				legend: {
					type: 'scroll',
					icon: 'circle',
					left: 'center',
					align: 'auto',
					bottom: '10',
					textStyle: {
						color: '#000'
					},
					...legend,
					data: legendData
				},
				series: {
					type: 'pie',
					z: 3,
					center: series?.center || ['50%', '50%'],
					radius: ['30%', '45%'],
					// clockwise: true,
					avoidLabelOverlap: true,
					itemStyle: {
						color: function (params) {
							return colorList[params.dataIndex];
						}
					},
					label: {
						formatter: '{b}\n\n',
						show: true,
						position: 'outside',
						padding: [0, -80]
					},
					labelLine: {
						length: 20,
						length2: 80,
						lineStyle: {
							width: 1
						}
					},
					data: this.seriesData
				}
			};
			// if (this.axisData.length === 0) return;
			this.chart && this.chart.dispose();
			this.chart = this.$echarts.init(document.getElementById(this.id));
			this.chart.setOption(option);
			window.addEventListener('resize', () => {
				this.chart && this.chart.resize();
			});
		}
	}
};
</script>
