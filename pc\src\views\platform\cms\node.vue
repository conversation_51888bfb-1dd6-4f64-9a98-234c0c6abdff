<template>
	<basic-container>
		<avue-crud
			ref="crud"
			v-model="form"
			:option="option"
			:table-loading="loading"
			:data="data"
			:permission="permissionList"
			:before-open="beforeOpen"
			:cell-style="cellStyle"
			@row-update="rowUpdate"
			@row-save="rowSave"
			@row-del="rowDel"
			@search-change="searchChange"
			@search-reset="searchReset"
			@selection-change="selectionChange"
			@refresh-change="searchReset"
			@on-load="onLoad"
		>
			<template v-for="(item, index) in editors" :slot="item.slot" slot-scope="scope">
				<wangeditor :key="index" v-model="form[item.model]" :data-id="id"></wangeditor>
			</template>
			<template v-for="(map, index) in maps" :slot="map.slot" slot-scope="scope">
				<div class="mapButton">
					<el-image
						style="width: 60px"
						:src="require('@/assets/image/mapIcon.png')"
						@click="openMap(map.model)"
					>
						选择坐标
					</el-image>
					<span style="margin-left: 10px">点击选择经纬度</span>
				</div>
				<div>
					<bmChooseAddressGT
						ref="baiduMap"
						:show-map="showMap"
						:gt="form[map.model]"
						@checkedAddress="checkedAddress"
						@closeMapDialog="closeMapDialog"
					></bmChooseAddressGT>
				</div>
			</template>
			<template slot="paramsListForm" slot-scope="scope">
				<el-button style="margin-bottom: 5px" @click="addParam()">添加固定参数</el-button>
				<div v-for="(item, index) of paramsList" :key="index" class="paramsClass">
					<div><el-input v-model="paramsList[index].label" placeholder="参数名称"></el-input></div>
					<div><el-input v-model="paramsList[index].code" placeholder="参数编码"></el-input></div>
					<div><el-input v-model="paramsList[index].value" placeholder="参数值"></el-input></div>
					<div>
						<el-button
							icon="el-icon-delete"
							type="danger"
							size="mini"
							@click="removeParam(index)"
						></el-button>
					</div>
				</div>
			</template>
			<template slot="menuLeft">
			</template>
			<template slot="menuRight">
				<div style="display: flex; flex-direction: row">
					<es-input
						v-model="query.name"
						placeholder="目录名称"
						prefix-icon="el-icon-search"
						size="small"
						style="width: 200px"
						@keyup.enter.native="doSearch"
					></es-input>
					<es-button type="primary" size="medium" @click="doSearch">搜索</es-button>
					<es-button size="medium" @click="searchReset">重置</es-button>
					<!--					<es-button type="primary" size="medium" @click="openAdvSearch">高级搜索</es-button>-->
				</div>
			</template>
			<template slot="menu" slot-scope="scope">
				<el-button v-if="false" type="text" size="small" plain @click="handleAudit(scope.row)">
					审核
				</el-button>
			</template>
		</avue-crud>
		<el-dialog
			title="目录审核"
			:visible.sync="dialogVisible"
			width="60%"
			:before-close="handleClose"
		>
			<div style="padding-left: 20px">
				<h3>审核结论</h3>
				<el-form ref="auditForm" :model="auditForm" :rules="auditRules" label-width="120px">
					<el-form-item label="审核意见" prop="auditOpinion">
						<el-input
							v-model="auditForm.auditOpinion"
							type="textarea"
							:rows="6"
							style="width: 60%"
						></el-input>
					</el-form-item>
					<el-form-item label="是否通过" prop="publishAudit">
						<el-radio v-model="auditForm.publishAudit" :label="4">是</el-radio>
						<el-radio v-model="auditForm.publishAudit" :label="3">否</el-radio>
					</el-form-item>
				</el-form>
				<h3>详情内容</h3>
				<avue-form ref="form" v-model="form" :option="option"></avue-form>
				<span slot="footer" class="dialog-footer dialogBtnClass">
					<el-button size="small" type="info" @click="handleClose">取 消</el-button>
					<el-button size="small" type="primary" @click="doAudit()">确 定</el-button>
				</span>
			</div>
		</el-dialog>
		<!--		<el-dialog-->
		<!--			title="高级检索"-->
		<!--			:visible.sync="searchDialogVisible"-->
		<!--			width="25%"-->
		<!--			:before-close="handleCloseSearch"-->
		<!--		>-->
		<!--			<avue-form v-model="query" :option="advSearchOption" @submit="doSearch">-->
		<!--				<template slot="menuForm">-->
		<!--					<el-button icon="el-icon-search" type="primary" @click="doSearch">搜索</el-button>-->
		<!--				</template>-->
		<!--			</avue-form>-->
		<!--		</el-dialog>-->
	</basic-container>
</template>

<script>
import nodeUrl from '@/http/platform/node.js';
import modelUrl from '@/http/platform/model.js';
import modelFieldUrl from '@/http/platform/modelfield.js';
import cmsapplicationUrl from '@/http/platform/cmsapplication.js';
import SnowflakeId from 'snowflake-id';
import componentList from '@/views/platform/cms/component';
import bmChooseAddressGT from '@/components/map/bmChooseAddressGT.vue';
const snowflake = new SnowflakeId();
const defForm = {
	id: null,
	name: null,
	code: null,
	nodeModelId: null,
	infoModelId: null,
	nodeType: 3,
	linkUrl: null,
	parentId: null,
	sortIndex: 1,
	navShow: 0,
	status: 1,
	allowComment: 0,
	publishAudit: 1,
	sysApplicationId: null,
	params: null,
	testColText: [],
	testColSel: [],
	sortMode: 1
	// dynamic:{}//动态字段信息,
};

const defAuditForm = {
	id: null,
	publishAudit: null,
	auditOpinion: null
};

const defQueryForm = {
	name: null,
	nodeModelId: null,
	nodeType: null,
	status: null
};

const defPage = {
	pageSize: 10,
	currentPage: 1,
	total: 0
};
const sortModeDic = [
	{
		label: '排序号正序',
		value: 1
	},
	{
		label: '排序号倒序',
		value: 2
	},
	{
		label: '发布时间正序',
		value: 3
	},
	{
		label: '发布时间倒序',
		value: 4
	}
];
export default {
	components: { bmChooseAddressGT },
	data() {
		return {
			id: '',
			form: Object.assign({}, defForm),
			query: {},
			loading: false,
			selectionList: [],
			data: [],
			dialogVisible: false,
			auditForm: Object.assign({}, defAuditForm),
			auditRules: {
				publishAudit: [{ required: true, message: '请填写审核意见', trigger: 'blur' }],
				auditOpinion: [{ required: true, message: '请确定是否通过审核', trigger: 'blur' }]
			},
			colNum: 17, //默认表单字段数量(option.column),便于后续动态字段删除
			curOpt: null, //当前打开对话框的操作
			dynamicField: {}, //请求模型时，临时保存当前模板字段信息,
			editors: [], //富文本字段
			maps: [], //地图
			showMap: false,
			corMapCol: null,
			paramsList: [],
			option: {
				size: 'medium',
				height: 'auto',
				calcHeight: 'auto',
				align: 'center',
				searchShow: true,
				searchMenuSpan: 6,
				tip: false,
				border: true,
				index: false,
				viewBtn: true,
				addBtnText: '新增',
				selection: false,
				submitBtn: false,
				emptyBtn: false,
				disabled: false,
				columnBtn: false,
				refreshBtn: false,
				defaultExpandAll: false,
				dialogClickModal: false,
				dialogMenuPosition: 'center',
				dialogWidth: '80%',
				width: '99%',
				labelWidth: 200,
				menuWidth: 300,
				column: [
					{
						label: '栏目编号',
						prop: 'id',
						rules: [
							{
								required: false,
								message: '请输入',
								trigger: 'blur'
							}
						],
						hide: true,
						display: false
					},
					{
						label: '目录名称',
						prop: 'name',
						span: 12,
						type: 'input',
						rules: [
							{
								required: true,
								message: '请输入栏目名称',
								trigger: 'blur'
							}
						],
						display: true
					},
					{
						label: '目录编码',
						prop: 'code',
						span: 12,
						rules: [
							{
								required: true,
								message: '请输入栏目编码',
								trigger: 'blur'
							},
							{
								pattern: /^[a-z][a-z0-9A-Z]+[-]?[a-z0-9A-Z]+$/,
								message: '编码格式需为小写字母开头的大小写字母+数字的组合'
							}
						],
						display: true
					},
					{
						label: '类型',
						prop: 'nodeType',
						span: 12,
						value: 1,
						editDisabled: true,
						type: 'select',
						dicData: [
							{
								label: '外部链接',
								value: 1
							},
							{
								label: '文档',
								value: 2
							},
							{
								label: '目录',
								value: 3
							},
							{
								label: '外部应用数据',
								value: 4
							}
						],
						rules: [
							{
								required: true,
								message: '请输入目录类型',
								trigger: 'blur'
							}
						],
						display: true
					},
					{
						label: '父级目录',
						prop: 'parentId',
						span: 12,
						rules: [
							{
								required: false,
								message: '请选择父级目录',
								trigger: 'blur'
							}
						],
						type: 'tree',
						dicUrl: (process.env.NODE_ENV === 'development' ? 'api' : '') + nodeUrl.getTreeNodeList,
						props: {
							label: 'name',
							value: 'id'
						},
						dicFormatter: res => {
							return res.results;
						},
						hide: true,
						display: true
					},
					{
						label: '目录模型',
						prop: 'nodeModelId',
						span: 12,
						editDisabled: true,
						rules: [
							{
								required: false,
								message: '请选择目录模型',
								trigger: 'blur'
							}
						],
						type: 'select',
						dicUrl:
							(process.env.NODE_ENV === 'development' ? 'api' : '') +
							modelUrl.getList +
							'?status=1&pageNum=1&pageSize=-1',
						props: {
							label: 'name',
							value: 'id'
						},
						dicFormatter: res => {
							return res.results;
						},
						display: true
					},
					{
						label: '内容模型',
						prop: 'infoModelId',
						span: 12,
						type: 'select',
						dicUrl:
							(process.env.NODE_ENV === 'development' ? 'api' : '') +
							modelUrl.getList +
							'?&status=1&pageNum=1&pageSize=-1',
						props: {
							label: 'name',
							value: 'id'
						},
						dicFormatter: res => {
							return res.results;
						},
						hide: true,
						display: true
					},
					{
						label: '内容排序方式',
						prop: 'sortMode',
						span: 24,
						value: 1,
						type: 'radio',
						dicData: sortModeDic,
						rules: [
							{
								required: true,
								message: '请选择内容排序方式',
								trigger: 'blur'
							}
						],
						display: true
					},
					{
						label: '选择应用',
						prop: 'sysApplicationId',
						span: 12,
						type: 'select',
						dicUrl:
							(process.env.NODE_ENV === 'development' ? 'api' : '') +
							cmsapplicationUrl.page +
							'?current=1&size=-1',
						props: { label: 'appName', value: 'id' },
						dicFormatter: res => {
							return res.results.records;
						},
						rules: [{ required: true, trigger: 'blur', message: '请选择应用' }],
						hide: true,
						display: false
					},
					{
						label: '固定参数',
						prop: 'paramsList',
						formslot: true,
						span: 24,
						hide: true,
						display: false
					},
					{
						label: '外部链接',
						prop: 'linkUrl',
						span: 12,
						rules: [
							{
								required: true,
								message: '请输入链接地址',
								trigger: 'blur'
							}
						],
						overHidden: true,
						hide: true,
						display: false
					},
					{
						label: '站点ID',
						prop: 'siteId',
						rules: [
							{
								required: false,
								message: '请输入站点ID',
								trigger: 'blur'
							}
						],
						hide: true,
						display: false
					},
					{
						label: '租户ID',
						prop: 'rentId',
						rules: [
							{
								required: false,
								message: '请输入租户ID',
								trigger: 'blur'
							}
						],
						hide: true,
						display: false
					},
					{
						label: '创建时间',
						prop: 'createTime',
						rules: [
							{
								required: false,
								message: '请输入创建时间',
								trigger: 'blur'
							}
						],
						hide: true,
						display: false
					},
					{
						label: '修改时间',
						prop: 'updateTime',
						rules: [
							{
								required: false,
								message: '请输入修改时间',
								trigger: 'blur'
							}
						],
						hide: true,
						display: false
					},
					{
						label: '排序',
						prop: 'sortIndex',
						rules: [
							{
								required: false,
								message: '请输入排列顺序',
								trigger: 'blur'
							}
						],
						type: 'number',
						minRows: 1
					},
					{
						label: '状态',
						prop: 'status',
						type: 'radio',
						dicData: [
							{
								label: '启用',
								value: 1
							},
							{
								label: '禁用',
								value: 0
							}
						],
						rules: [
							{
								required: true,
								message: '请选择状态',
								trigger: 'blur'
							}
						]
					},
					{
						label: '审核意见',
						prop: 'auditOpinion',
						rules: [
							{
								required: false,
								message: '请填写审核意见',
								trigger: 'blur'
							}
						],
						hide: true,
						display: false
					}
				]
			},
			searchDialogVisible: false,
			advSearchOption: {
				submitBtn: false,
				column: []
			}
		};
	},
	computed: {
		permissionList() {
			return {
				addBtn: true,
				viewBtn: true,
				delBtn: true,
				editBtn: true,
				auditBtn: true
			};
		},
		ids() {
			let ids = [];
			this.selectionList.forEach(ele => {
				ids.push(ele.id);
			});
			return ids.join(',');
		}
	},
	watch: {
		'form.nodeType'() {
			let index_linkUrl = this.getColIndex('linkUrl');
			let index_nodeModelId = this.getColIndex('nodeModelId');
			let index_infoModelId = this.getColIndex('infoModelId');

			let index_name = this.getColIndex('name');
			let index_code = this.getColIndex('code');
			let index_parentId = this.getColIndex('parentId');
			let index_nodeType = this.getColIndex('nodeType');

			let index_sysApplicationId = this.getColIndex('sysApplicationId');
			let index_paramsList = this.getColIndex('paramsList');

			this.option.column[index_linkUrl].display = false;
			this.option.column[index_nodeModelId].display = false;
			this.option.column[index_infoModelId].display = false;

			this.option.column[index_sysApplicationId].display = false;
			this.option.column[index_paramsList].display = false;

			this.option.column[index_name].span = 12;
			this.option.column[index_code].span = 12;
			this.option.column[index_parentId].span = 12;
			this.option.column[index_nodeType].span = 12;
			this.option.column[index_linkUrl].span = 12;
			this.option.column[index_nodeModelId].span = 12;
			if (this.form.nodeType === 4) {
				//外部应用数据
				this.form.nodeModelId = null;
				// this.doClearDynamicForm();
				this.option.column[index_nodeModelId].display = true;
				this.option.column[index_infoModelId].display = true;
				this.option.column[index_sysApplicationId].display = true;
				this.option.column[index_paramsList].display = true;
			} else if (this.form.nodeType === 3) {
				//目录
				this.form.nodeModelId = null;
				this.option.column[index_nodeModelId].display = true;
				this.option.column[index_infoModelId].display = true;
			} else if (this.form.nodeType === 2) {
				//文档
				// if(this.form.id){
				//   this.doGetModelFormInfo({modelCode:"detailNode",objType:"node",objId:this.form.id});
				// }else{
				//   this.doGetModelFormInfo({modelCode:"detailNode"});
				// }
				this.option.column[index_nodeModelId].display = true;
			} else if (this.form.nodeType === 1) {
				//外部链接
				this.option.column[index_name].span = 24;
				this.option.column[index_code].span = 24;
				this.option.column[index_parentId].span = 24;
				this.option.column[index_nodeType].span = 24;
				this.option.column[index_linkUrl].span = 24;
				this.option.column[index_linkUrl].display = true;
				this.form.nodeModelId = null;
				this.option.column[index_nodeModelId].span = 24;
				this.option.column[index_nodeModelId].display = true;
			}
		},
		'form.nodeModelId'() {
			if (this.curOpt == 'add') {
				if (this.form.nodeModelId) {
					this.doClearDynamicForm();
					this.doGetModelFormInfo({ modelId: this.form.nodeModelId });
				} else {
					this.doClearDynamicForm();
				}
			}
		}
	},
	created() {},
	methods: {
		cellStyle({ row, column, rowIndex, columnIndex }) {
			if (column.property === 'status') {
				if (row.status === 0) {
					return { color: 'red', 'text-align': 'center' };
				}
			} else if (column.property === 'name') {
				return { 'text-align': 'left' };
			} else if (column.property === 'publishAudit') {
				// 1.草稿、2.待审核、3.审核不通过、4.已发布、5.已下线
				if (row.publishAudit === 2) {
					return { color: '#F59A23' };
				} else if (row.publishAudit === 3) {
					return { color: '#D9001B' };
				} else if (row.publishAudit === 4) {
					return { color: '#219D3D' };
				} else if (row.publishAudit === 5) {
					return { color: '#7F7F7F' };
				}
			}
		},
		getColIndex(prop) {
			for (let index in this.option.column) {
				if (this.option.column[index].prop === prop) {
					return index;
				}
			}
		},
		rowSave(row, done, loading) {
			let temForm = {};
			let dynamicForm = {};
			for (let item in defForm) {
				temForm[item] = this.form[item];
			}
			for (let item in this.dynamicField) {
				dynamicForm[item] = this.form[item];
			}
			temForm.dynamic = dynamicForm;
			if (temForm.nodeType === 4) {
				if (this.paramsList.length > 0) {
					let ret = this.checkParam();
					if (!ret) {
						loading();
						return;
					}
					temForm.params = JSON.stringify(this.paramsList);
				} else {
					temForm.params = '';
				}
			}
			temForm.id = this.id;
			temForm.isUpdate = false;
			this.$request({
				url: nodeUrl.add,
				data: temForm,
				method: 'POST',
				format: false
			}).then(res => {
				if (res.rCode === 0) {
					this.$message({
						type: 'success',
						message: '操作成功!'
					});
					done();
					this.onLoad();
				} else {
					this.$message({
						type: 'error',
						message: res.msg
					});
					loading();
				}
			});
		},
		rowUpdate(row, index, done, loading) {
			let temForm = {};
			let dynamicForm = {};
			for (let itme in defForm) {
				temForm[itme] = this.form[itme];
			}
			for (let itme in this.dynamicField) {
				dynamicForm[itme] = this.form[itme];
			}
			temForm.dynamic = dynamicForm;
			if (temForm.nodeType === 4) {
				if (this.paramsList.length > 0) {
					let ret = this.checkParam();
					if (!ret) {
						loading();
						return;
					}
					temForm.params = JSON.stringify(this.paramsList);
				} else {
					temForm.params = '';
				}
			}
			temForm.isUpdate = true;
			this.$request({
				url: nodeUrl.update,
				data: temForm,
				method: 'POST',
				format: false
			}).then(res => {
				if (res.rCode === 0) {
					this.$message({
						type: 'success',
						message: '操作成功!'
					});
					done();
					this.onLoad();
				} else {
					this.$message({
						type: 'error',
						message: res.msg
					});
					loading();
				}
			});
		},
		rowDel(row) {
			this.$confirm('确定将选择数据删除?', {
				confirmButtonText: '确定',
				cancelButtonText: '取消',
				type: 'warning'
			}).then(() => {
				this.$request({
					url: nodeUrl.remove,
					params: {
						ids: row.id
					},
					method: 'POST'
				}).then(res => {
					if (res.rCode === 0) {
						this.$message({
							type: 'success',
							message: '操作成功!'
						});
						this.onLoad();
					} else {
						this.$message({
							type: 'error',
							message: '操作失败!'
						});
					}
				});
			});
		},
		handleDelete() {
			if (this.selectionList.length === 0) {
				this.$message.warning('请选择至少一条数据');
				return;
			}
			this.$confirm('确定将选择数据删除?', {
				confirmButtonText: '确定',
				cancelButtonText: '取消',
				type: 'warning'
			})
				.then(() => {
					return remove(this.ids);
				})
				.then(() => {
					this.onLoad();
					this.$message({
						type: 'success',
						message: '操作成功!'
					});
				});
		},
		beforeOpen(done, type) {
			this.curOpt = type;
			if (['edit', 'view'].includes(type)) {
				this.id = this.form.id;
				this.$request({
					url: nodeUrl.detail,
					params: {
						id: this.form.id
					},
					method: 'GET'
				}).then(res => {
					this.doClearDynamicForm();
					this.form = res.results;
					if (this.form.nodeType === 4 && this.form.params) {
						this.paramsList = JSON.parse(this.form.params);
					}
					if (this.form.nodeModelId) {
						this.doGetModelFormInfo({
							modelId: this.form.nodeModelId,
							objType: 'node',
							objId: this.form.id
						});
					}
				});
			} else if (type == 'add') {
				this.id = snowflake.generate();
				this.doClearFormForAdd();
			}
			done();
		},
		//获取模型信息
		doGetModelFormInfo(params) {
			this.$request({
				url: modelFieldUrl.getModelForm,
				params: params,
				method: 'GET'
			}).then(res => {
				if (res.rCode === 0) {
					let results = res.results;
					let modelColumn = res.results.modelColumn;
					for (let col of modelColumn) {
						let component = componentList.find(
							item => item.value === col.type || item.value === col.originType
						);
						col = Object.assign(col, component.option);
						if (col.type.startsWith('upload')) {
							col.data = {
								ownId: this.id,
								code: 'platform_cms_dynamic',
								isShowPath: true
							};
						}
					}
					this.option.column = this.option.column.concat(modelColumn);
					this.form.nodeModelId = results.modelId;
					this.dynamicField = results.modelForm;
					for (let item in this.dynamicField) {
						this.$set(this.form, item, this.dynamicField[item]);
					}
					this.editors = results.editorFields;
					this.maps = results.mapFields;
				} else {
				}
			});
		},
		//清空表单及字段的动态字段定义信息
		doClearDynamicForm() {
			let temForm = {};
			for (let itme in defForm) {
				temForm[itme] = this.form[itme] || defForm[itme];
			}
			this.form = Object.assign({}, temForm);
			this.dynamicField = {};
			this.paramsList = [];
			let dynamicLen = this.option.column.length;
			if (dynamicLen > this.colNum) {
				this.option.column.splice(this.colNum, dynamicLen - this.colNum);
			}
			this.editors = [];
		},
		//新增时清空表单
		doClearFormForAdd() {
			this.form = Object.assign({}, defForm);
			this.form.infoModelId = this.infoModelId;
			this.form.nodeId = this.nodeId;
			this.dynamicField = {};
			let dynamicLen = this.option.column.length;
			if (dynamicLen > this.colNum) {
				this.option.column.splice(this.colNum, dynamicLen - this.colNum);
			}
			this.editors = [];
		},
		searchReset() {
			this.page = Object.assign({}, defPage);
			this.query = Object.assign({}, defQueryForm);
			this.onLoad();
		},
		searchChange(params, done) {
			this.query = params;
			this.onLoad();
			done();
		},
		doSearch(done, loading) {
			for (let key in this.query) {
				let type = typeof this.query[key];
				if (type === 'string' && this.query[key] === '') {
					this.query[key] = null;
				}
			}
			this.page = Object.assign({}, defPage);
			this.onLoad();
			this.searchDialogVisible = false;
			this.query = Object.assign({}, defQueryForm);
			this.option.defaultExpandAll = true;
			if (loading) {
				loading();
			}
		},
		openAdvSearch() {
			this.query = Object.assign({}, defQueryForm);
			this.searchDialogVisible = true;
		},
		selectionChange(list) {
			this.selectionList = list;
		},
		selectionClear() {
			this.selectionList = [];
		},
		onLoad() {
			this.$request({
				url: nodeUrl.getTreeNodeList,
				params: this.query,
				method: 'GET'
			}).then(res => {
				if (res.rCode === 0) {
					this.data = res.results;
				} else {
				}
			});
		},
		handleAudit(row) {
			getDetail(row.id).then(res => {
				this.doClearDynamicForm();
				this.form = res.data.data;
				if (this.form.nodeType === 4 && this.form.params) {
					this.paramsList = JSON.parse(this.form.params);
				}
				if (row.nodeModelId) {
					this.doGetModelFormInfo({ modelId: row.nodeModelId, objType: 'node', objId: row.id });
				}
			});
			this.option.disabled = true;
			this.dialogVisible = true;
			this.auditForm.id = row.id;
			this.auditForm.auditOpinion = row.auditOpinion;
			if (row.publishAudit === 1 || row.publishAudit === 2) {
				this.auditForm.publishAudit = null;
			} else if (row.publishAudit === 3) {
				this.auditForm.publishAudit = row.publishAudit;
			} else if (row.publishAudit === 4 || row.publishAudit === 5) {
				this.auditForm.publishAudit = 4;
			}
			this.auditForm.publishAudit = row.publishAudit;
		},
		handleClose() {
			this.option.disabled = false;
			this.dialogVisible = false;
			this.auditForm = Object.assign({}, defAuditForm);
		},
		doAudit() {
			this.$refs.auditForm.validate(valid => {
				if (!valid) {
					return;
				}
				auditPublish(this.auditForm).then(res => {
					this.option.disabled = false;
					this.dialogVisible = false;
					this.auditForm = Object.assign({}, defAuditForm);
					this.onLoad();
				});
			});
		},
		addParam() {
			let param = { label: null, code: null, value: null };
			this.paramsList.push(param);
		},
		removeParam(index) {
			this.paramsList.splice(index, 1);
		},
		checkParam() {
			if (this.paramsList.length > 0) {
				for (let m of this.paramsList) {
					if (
						m.label === '' ||
						m.label == null ||
						m.code === '' ||
						m.code == null ||
						m.value === '' ||
						m.value == null
					) {
						this.$message({ type: 'warning', duration: 1500, message: '请完善参数信息' });
						return false;
					}
				}
				let codes = this.paramsList.map(param => param.code);
				let disCodes = new Set(codes);
				if (disCodes.size < codes.length) {
					this.$message({ type: 'warning', duration: 1500, message: '参数编码存在重复' });
					return false;
				}
			}
			return true;
		},
		openMap(mapCol) {
			this.corMapCol = mapCol;
			this.showMap = true;
		},
		checkedAddress(pointLngLat) {
			this.form[this.corMapCol] = pointLngLat;
			this.showMap = false;
		},
		closeMapDialog() {
			this.showMap = false;
		}
	}
};
</script>
<style scoped="scoped">
.dialogBtnClass {
	display: flex;
	justify-content: center;
}
.paramsClass {
	width: 50%;
	margin-bottom: 5px;
	display: flex;
	flex-direction: row;
	justify-content: space-around;
}
.paramsClass div {
	margin-right: 5px;
}
</style>
