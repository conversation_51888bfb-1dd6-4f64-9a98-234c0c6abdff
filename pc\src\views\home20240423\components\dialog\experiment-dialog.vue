<template>
	<!-- 顶部筛选部分 -->
	<!--  -->
	<div
		class="experiment"
		:style="{
			transform: `scale(${scale}) translate3d(0, 0, 0)`
		}"
	>
		<div class="">
			<span class="experiment-title">毕业就业</span>
			<!-- 选择区域 -->
			<!-- v-model="levelMonth" clearable @change="levelPramsChange" -->
			<el-select v-model="year" placeholder="请选择毕业年份">
				<el-option
					v-for="item in options"
					:key="item.value"
					:label="item.label"
					:value="item.value"
				></el-option>
			</el-select>
			<el-select v-model="faculty" placeholder="请选择院系">
				<el-option
					v-for="item in collegeList"
					:key="item.value"
					:label="item.label"
					:value="item.value"
				></el-option>
			</el-select>
		</div>
		<!-- 内容区域 -->
		<div class="experiment-content">
			<graduates class="card-box" />
			<employment-analysis class="card-box" />
			<Statistics :title="title" class="card-box" />
			<Analyse :title="title" class="card-box" />
		</div>
	</div>
</template>

<script>
import Graduates from './experiment-graduates.vue';
import EmploymentAnalysis from './experiment-employment.vue';
import Statistics from './statistics.vue';
import Analyse from './analyse.vue';
import api from '@/http/society/societyBaseInfo/api';
export default {
	components: {
		Graduates,
		EmploymentAnalysis,
		Statistics,
		Analyse
	},
	props: {
		title: {
			type: String, // 毕业就业 实习
			default: '毕业就业'
		}
	},
	data() {
		return {
			scale: 1,
			year: '',
			faculty: '',
			options: [
				{
					label: '2011',
					value: '2011'
				},
				{
					label: '2012',
					value: '2012'
				},
				{
					label: '2013',
					value: '2013'
				},
				{
					label: '2014',
					value: '2014'
				},
				{
					label: '2015',
					value: '2015'
				},
				{
					label: '2016',
					value: '2016'
				}
			],
			collegeList:[],//院系数据
		};
	},
	created() {
		this.scale = this.$utils.toolViewRatio();
		this.getCollege();//获取院系数据
	},
	mounted() {},
	methods: {
		// 获取院系数据
		getCollege() {
			this.$request({
				url: api.collegeSelectList,
				method: 'POST'
			}).then(result => {
				if (result.success) {
					this.collegeList = result.results;
				} else {
					this.$message.error('操作失败');
				}
			});
		},
	}
};
</script>

<style lang="scss" scoped>
.experiment {
	width: 1778px;
	height: 934px;
	transform-origin: top left;
	background: #e7f6ff;
	padding: 29px 11px 13px 12px;
	border-radius: 5px;
	overflow: hidden;
	&-title {
		font-family: MicrosoftYaHei, MicrosoftYaHei;
		font-weight: bold;
		font-size: 24px;
		color: #454545;
		line-height: 31px;
		margin-left: 27px;
	}
	&-content {
		margin-top: 22px;
	}
	.card-box {
		width: 100%;
		margin-top: 12px;
		display: flex;
		justify-content: space-between;
	}
}
::v-deep .el-input--suffix .el-input__inner {
	background: rgba(255, 255, 255, 0.5) !important;
}
::v-deep .el-input--suffix {
	padding-left: 20px;
}
</style>
