<template>
	<div class="experiment">
		<div class="card-list">
			<div v-for="(item, index) in list" :key="index" class="card-item">
				<p class="item-title">{{ item.name }}</p>
				<div class="item-list">
					<div v-for="(arrItem, index) in item.arr" :key="index" class="list-item">
						<p class="list-item-title">
							<span class="title-span">{{ arrItem.name }}</span>
						</p>
						<div class="list-item-content">
							<img class="list-item-img" :src="arrItem.img" alt="" />
							<p>
								<span class="list-item-num">{{ dataRes[arrItem.key] }}</span>
								<span class="list-item-unit">{{ arrItem.unit }}</span>
							</p>
						</div>
					</div>
				</div>
			</div>
		</div>
	</div>
</template>

<script>
import { xodbApi2 } from '@/api/xodb';
export default {
	name: '',
	components: {},
	props: {},
	data() {
		return {
			dataRes: {},
			list: [
				{
					name: '实验实训',
					arr: [
						{
							name: '本学期已开实验实训课程',
							value: '0',
							key: 'sxxmmc',
							unit: '门',
							img: require('@/assets/images/home20240423/practical-courses.png')
						},
						{
							name: '本学期参加实验实训课程人数',
							value: '0',
							key: 'cjsxkcrs',
							unit: '人',
							img: require('@/assets/images/home20240423/practical-number.png')
						}
					]
				},
				{
					name: '社团协会',
					arr: [
						{
							name: '社团总数',
							value: '0',
							key: 'stzs',
							unit: '个',
							img: require('@/assets/images/home20240423/clubs-total.png')
						},
						{
							name: '社团总人数',
							value: '0',
							key: 'stzrs',
							unit: '人',
							img: require('@/assets/images/home20240423/clubs-person-number.png')
						}
					]
				},
				{
					name: '心理预约',
					arr: [
						{
							name: '咨询次数',
							value: '0',
							key: 'zxcs',
							unit: '次',
							img: require('@/assets/images/home20240423/consulting-times.png')
						},
						{
							name: '咨询人数',
							value: '0',
							key: 'zxrs',
							unit: '人',
							img: require('@/assets/images/home20240423/consulting-number.png')
						}
					]
				},
				{
					name: '奖助贷申',
					arr: [
						{
							name: '申请次数',
							value: '0',
							key: 'sqcs',
							unit: '次',
							img: require('@/assets/images/home20240423/application-times.png')
						},
						{
							name: '申请总额',
							value: '0',
							key: 'sqze',
							unit: '万元',
							img: require('@/assets/images/home20240423/application-total.png')
						}
					]
				}
			]
		};
	},
	computed: {
		loginUserInfo() {
			return JSON.parse(localStorage.getItem('loginUserInfo')) || {};
		}
	},
	created() {
		// this.getConsultingList(); //心理咨询
		this.getDataN('ads_jx_jzdssj_query', 'dataRes'); // 奖助贷申数据查询服务
		this.getDataN('ads_xx_stxhsj_query', 'dataRes'); // 社团协会数据查询服务
		this.getDataN('ads_jx_sysxsj_query', 'dataRes'); // 实验实训数据查询服务
	},

	mounted() {},
	methods: {
		async getDataN(url, listName) {
			const LeaderRole = localStorage.getItem('LeaderRole');

			try {
				const {
					data: { list }
				} = await xodbApi2.post({
					url,
					params: {
						// xyjgbh: '1',
						xyjgbh: LeaderRole === 'xLeader' ? '1' : this.loginUserInfo.orgCode
					}
				});
				const data = list[0] || {};
				switch (listName) {
					case 'dataRes':
						this[listName] = { ...this[listName], ...data };
						break;
				}
			} catch (error) {
				console.error(`处理数据失败${url}:`, error);
			}
		}

		// 心理咨询数据
		// async getConsultingList() {
		// 	const {
		// 		dataResult: { dataList }
		// 	} = await xodbApi.get({
		// 		url: 'warehouseConfig_ybzy_dw_dwb_xs_xlyysj_query'
		// 	});
		// 	this.dataRes = { ...this.dataRes, ...dataList[0] };
		// }
	}
};
</script>

<style lang="scss" scoped>
.experiment {
	height: 100%;
}
.card-list {
	height: 100%;
	display: flex;
	justify-content: space-between;
	.card-item {
		width: calc(25% - 6px);
		height: 100%;
	}
	.item-title {
		font-family: MicrosoftYaHei, MicrosoftYaHei;
		font-weight: bold;
		font-size: 16px;
		color: #194b7a;
		line-height: 21px;
		text-align: center;
	}
	.item-list {
		width: 100%;
		height: calc(100% - 36px);
		padding: 14px 13px 14px 19px;
		margin-top: 15px;
		background: linear-gradient(90deg, rgba(233, 255, 252, 0.8) 0%, rgba(255, 255, 255, 0) 100%);
		border-radius: 4px;
		border: 1px solid;
		border-image: linear-gradient(92deg, rgba(255, 255, 255, 0), rgba(255, 255, 255, 1)) 1 1;
	}
	.list-item {
		height: 50%;
		width: 100%;
		&-title {
			font-family: MicrosoftYaHei, MicrosoftYaHei;
			font-weight: bold;
			font-size: 14px;
			color: #2b83c8;
			line-height: 19px;
			overflow: hidden;
			display: flex;
			flex-wrap: nowrap;
			position: relative;
			.title-span {
				&::after {
					content: '';
					width: 100%;
					position: absolute;
					top: 50%;
					border-top: 1px dashed rgba(56, 139, 204, 0.48);
				}
			}
		}
		&-content {
			padding-top: 18px;
			padding-left: 11px;
			display: flex;
			align-items: center;
		}
		&-img {
			width: 68px;
			margin-right: 13px;
		}
		&-num {
			font-family: DINPro, DINPro;
			font-weight: bold;
			font-size: 24px;
			color: #0a325b;
			line-height: 31px;
		}
		&-unit {
			font-family: MicrosoftYaHei;
			font-size: 14px;
			color: #294d79;
			line-height: 19px;
		}
	}
}
</style>
