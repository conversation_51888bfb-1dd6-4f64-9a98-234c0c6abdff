.es-main {
	.es-main-header {
		z-index: 0;

		.es-handler-item {
			color: #ffffff;
		}
	}
}

.es-popper-sub-system {
	.es-sub-system-item {
		color: #ffffff;
	}
}

.es-body-main {
	border-color: #f2f2f2;
}

@mixin mp($type, $name, $num) {
	$B: 'es-'+$type +'-'+$name +'-'+$num  !global;

	@if $name ==false {
		$B: 'es-'+$type +'-'+$num  !global;
	}

	.#{$B} {
		@if $name ==false {
			#{$type}: #{$num}px;
		}

		@else {
			#{$type}-#{$name}: #{$num}px;
		}
	}
}

@mixin flex($num) {
	$B: 'es-flex'+'-'+$num  !global;

	.#{$B} {
		flex: $num;
	}
}

@mixin gutter($type, $num) {
	$B: 'es-gutter'+'-'+$type +'-'+$num  !global;

	.#{$B}+.#{$B} {
		margin-#{$type}: #{$num}px;
	}
}

$start: 50;
$end: -51;

@while $start >$end {
	@include mp(margin, false, $start);
	@include mp(margin, left, $start);
	@include mp(margin, top, $start);
	@include mp(margin, right, $start);
	@include mp(margin, bottom, $start);
	@include mp(padding, false, $start);
	@include mp(padding, left, $start);
	@include mp(padding, top, $start);
	@include mp(padding, right, $start);
	@include mp(padding, bottom, $start);
	@include gutter(left, $start);
	@include gutter(top, $start);

	@if $start >0 && $start < 13 {
		@include flex($start);
	}

	$start: $start - 1;
}