<template>
	<basic-container>
		<el-row>
			<el-col
				:span="4"
				style="border: 1px #ebf4ff solid; overflow-x: auto; overflow-y: auto; padding-right: 5px"
			>
				<el-scrollbar class="nodeTreeScrollbar">
					<avue-tree :option="treeOption" :data="treeData" @node-click="nodeClick">
						<span slot-scope="{ node, treeData }" class="el-tree-node__label">
							<span :id="node.data.id">
								<i v-if="node.data.nodeType === 1" class="el-icon-link" />
								<i v-if="node.data.nodeType === 2" class="el-icon-document" />
								<i v-if="node.data.nodeType === 3" class="el-icon-folder-opened" />
								<i v-if="node.data.nodeType === 4" class="el-icon-cloudy-and-sunny" />
								{{ (node || {}).label }}
							</span>
						</span>
					</avue-tree>
				</el-scrollbar>
			</el-col>
			<el-col :span="20">
				<avue-crud
					ref="crud"
					v-model="form"
					:option="option"
					:table-loading="loading"
					:data="data"
					:page="page"
					:permission="permissionList"
					:before-open="beforeOpen"
					:cell-style="cellStyle"
					@row-update="rowUpdate"
					@row-save="rowSave"
					@row-del="rowDel"
					@search-change="searchChange"
					@search-reset="searchReset"
					@selection-change="selectionChange"
					@refresh-change="searchReset"
					@current-change="currentChange"
					@size-change="sizeChange"
					@on-load="onLoad"
				>
					<template slot="publisherInfo" slot-scope="scope">
						<div
							style="
								display: flex;
								flex-direction: column;
								justify-content: center;
								align-items: center;
							"
						>
							<el-avatar
								v-if="scope.row.publisherAvatar"
								:size="30"
								:src="getFile(scope.row.publisherAvatar)"
							></el-avatar>
							<el-avatar v-else :size="30">用户</el-avatar>
							<span>{{ scope.row.publisherName }}</span>
						</div>
					</template>
					<template slot="menuLeft">
						<span style="margin-left: 40px">排序方式：{{ sortMode }}</span>

						<!--						<el-button type="danger" size="small" icon="el-icon-delete" plain @click="handleDelete">-->
						<!--							删 除-->
						<!--						</el-button>-->
					</template>
					<template slot="menuRight">
						<div style="display: flex; flex-direction: row">
							<el-input
								v-model="query.title"
								placeholder="文章标题"
								prefix-icon="el-icon-search"
								size="small"
								style="margin-right: 10px"
								@keyup.enter.native="doSearch"
							></el-input>
							<es-button type="primary" size="medium" @click="doSearch">搜索</es-button>
							<es-button size="medium" plain @click="searchReset">重置</es-button>
							<!--							<el-button type="primary" size="small" plain @click="openAdvSearch">-->
							<!--								高级搜索-->
							<!--							</el-button>-->
						</div>
					</template>
					<!--					<template slot="menu" slot-scope="scope">-->
					<!--						<el-button type="text" @click="handleAudit(scope.row)">审核</el-button>-->
					<!--					</template>-->

					<template v-for="(item, index) in editors" :slot="item.slot" slot-scope="scope">
						<wangeditor v-model="form[item.model]" :data-id="id"></wangeditor>
					</template>

					<template v-for="(map, index) in maps" :slot="map.slot" slot-scope="scope">
						<div class="mapButton">
							<el-image
								style="width: 60px"
								:src="require('@/assets/image/mapIcon.png')"
								@click="openMap(map.model)"
							>
								选择坐标
							</el-image>
							<span style="margin-left: 10px">点击选择经纬度</span>
							<span style="margin-left: 10px">{{ form[map.model] }}</span>
						</div>
						<div>
							<bmChooseAddressGT
								ref="baiduMap"
								:show-map="showMap"
								:gt="form[map.model]"
								@checkedAddress="checkedAddress"
								@closeMapDialog="closeMapDialog"
							></bmChooseAddressGT>
						</div>
					</template>
				</avue-crud>
			</el-col>
		</el-row>
		<el-dialog
			title="内容审核"
			:visible.sync="dialogVisible"
			width="50%"
			:before-close="handleClose"
		>
			<div style="padding-left: 20px">
				<h3>审核结论</h3>
				<el-form ref="auditForm" :model="auditForm" :rules="auditRules" label-width="120px">
					<el-form-item label="审核意见" prop="auditOpinion">
						<el-input
							v-model="auditForm.auditOpinion"
							type="textarea"
							:rows="6"
							style="width: 60%"
						></el-input>
					</el-form-item>
					<el-form-item label="是否通过" prop="publishAudit">
						<el-radio v-model="auditForm.publishAudit" :label="4">是</el-radio>
						<el-radio v-model="auditForm.publishAudit" :label="3">否</el-radio>
					</el-form-item>
				</el-form>
				<h3>详情内容</h3>
				<avue-form ref="form" v-model="form" :option="option"></avue-form>
				<span slot="footer" class="dialog-footer dialogBtnClass">
					<el-button size="small" type="info" @click="handleClose">取 消</el-button>
					<el-button size="small" type="primary" @click="doAudit()">确 定</el-button>
				</span>
			</div>
		</el-dialog>
		<!--		<el-dialog-->
		<!--			title="高级检索"-->
		<!--			:visible.sync="searchDialogVisible"-->
		<!--			width="25%"-->
		<!--			:before-close="handleCloseSearch"-->
		<!--		>-->
		<!--			<avue-form v-model="query" :option="advSearchOption" @submit="doSearch">-->
		<!--				<template slot="menuForm">-->
		<!--					<el-button icon="el-icon-search" type="primary" @click="doSearch">搜索</el-button>-->
		<!--				</template>-->
		<!--			</avue-form>-->
		<!--		</el-dialog>-->
		<!--		<el-dialog :title="appInfoTitle" :visible.sync="appDataDialogVisible" width="80%">-->
		<!--			<infoOfApp :node-id="appNodeId" :info-model="appInfoModel"></infoOfApp>-->
		<!--		</el-dialog>-->
	</basic-container>
</template>

<script>
import nodeUrl from '@/http/platform/node.js';
import modelUrl from '@/http/platform/model.js';
import modelFieldUrl from '@/http/platform/modelfield.js';
import infoUrl from '@/http/platform/info.js';
// import infoOfApp from '@/views/cms/infoOfApp.vue';
import bmChooseAddressGT from '@/components/map/bmChooseAddressGT.vue';
import componentList from './component.js';
import { host } from '../../../../config/config';
import SnowflakeId from 'snowflake-id';

const snowflake = new SnowflakeId();
const defForm = {
	id: null,
	code: null,
	title: null,
	keywords: null,
	description: null,
	infoType: 2, //显示类型(2:普通,1:外链)
	linkUrl: null,
	CreateName:'',
	infoModelId: null,
	nodeId: null,
	createTime: null,
	sortIndex: 1,
	status: 1, //状态(0:禁用,1:启用),
	scenicId: null, //景区编号
	coverImg: null //封面图片
};
const defAuditForm = {
	id: null,
	publishAudit: null,
	auditOpinion: null
};

const defQueryForm = {
	nodeId: null,
	title: null,
	infoModelId: null,
	publishDate: [],
	STime: null,
	ETime: null,
	status: null
};

const defPage = {
	pageSize: 10,
	currentPage: 1,
	total: 0
};

export default {
	components: { bmChooseAddressGT },
	data() {
		return {
			id: '',
			nodeCode: '',
			sortMode: '', // 排序方式
			form: Object.assign({}, defForm),
			query: Object.assign({}, defQueryForm),
			nodeId: null,
			nodeType: null,
			infoModelId: null,
			loading: true,
			page: Object.assign({}, defPage),
			selectionList: [],
			data: [],
			dialogVisible: false,
			auditForm: Object.assign({}, defAuditForm),
			auditRules: {
				publishAudit: [{ required: true, message: '请填写审核意见', trigger: 'blur' }],
				auditOpinion: [{ required: true, message: '请确定是否通过审核', trigger: 'blur' }]
			},
			customReqCols: [], //自定义字段中设置为必填的字段的编码集合
			appInfoTitle: null,
			appNodeId: null,
			appInfoModel: null,
			appDataDialogVisible: false, //外部应用数据对话框

			colNum: 18, //默认表单字段数量(option.column),便于后续动态字段删除
			curOpt: null, //当前打开对话框的操作
			dynamicField: {}, //请求模型时，临时保存当前模板字段信息
			editors: [],
			maps: [],
			showMap: false,
			corMapCol: null,
			modelColumn: [],

			searchDialogVisible: false,
			advSearchOption: {
				submitBtn: false,
				column: [
					// {
					//   label: "文章模型",
					//   prop: "infoModelId",
					//   type:"select",
					//   span:24,
					//   dicUrl:"/cms/model/list?type=info",
					//   props: {
					//     label: 'name',
					//     value: 'id'
					//   },
					// },
					{
						label: '发布日期',
						prop: 'publishDate',
						type: 'daterange',
						format: 'yyyy-MM-dd',
						valueFormat: 'yyyy-MM-dd',
						startPlaceholder: '开始日期',
						endPlaceholder: '结束日期',
						span: 24
					},
					{
						label: '状态',
						prop: 'status',
						type: 'radio',
						dicUrl: '/blade-system/dict/dictionary?code=common_status',
						props: {
							label: 'dictValue',
							value: 'dictKey'
						},
						span: 24
					}
				]
			},

			treeData: [],
			treeOption: {
				nodeKey: 'id',
				props: {
					labelText: '栏目',
					label: 'name',
					value: 'id',
					children: 'children'
				},
				size: 'small',
				menu: false,
				addBtn: false
			}
		};
	},
	computed: {
		permissionList() {
			return {
				addBtn: true,
				viewBtn: true,
				delBtn: true,
				editBtn: true,
				auditBtn: true
			};
		},
		ids() {
			let ids = [];
			this.selectionList.forEach(ele => {
				ids.push(ele.id);
			});
			return ids.join(',');
		},
		option() {
			return {
				size: 'medium',
				height: '615',
				calcHeight: 'auto',
				searchShow: true,
				searchMenuSpan: 6,
				align: 'center',
				tip: false,
				border: true,
				addBtn: true,
				addBtnText: '新增',
				viewBtn: true,
				selection: false,
				submitBtn: false,
				emptyBtn: false,
				disabled: false,
				columnBtn: false,
				refreshBtn: false,
				dialogClickModal: false,
				dialogMenuPosition: 'center',
				dialogWidth: '80%',
				labelWidth: 200,
				menuWidth: 300,
				column: [
					{
						label: '文章编号',
						prop: 'id',
						rules: [
							{
								required: false,
								message: '请输入',
								trigger: 'blur'
							}
						],
						width: 180,
						hide: false,
						display: false
					},
					{
						label: '文章标题',
						prop: 'title',
						width: 160,
						maxlength: 200,
						overHidden: true,
						rules: [
							{
								required: true,
								message: '请输入文章标题',
								trigger: 'blur'
							}
						]
					},
					{
						label: '文章编码',
						prop: 'code',
						width: 160,
						overHidden: true,
						rules: [
							{
								required: false,
								message: '请输入文章编码',
								trigger: 'blur'
							},
							{
								pattern: /^[a-z][a-z0-9A-Z]+[-]?[a-z0-9A-Z]+$/,
								message: '编码格式需为小写字母开头的大小写字母+数字的组合'
							}
						]
					},
					{
						label: '关键字',
						maxlength: 200,
						prop: 'keywords',
						rules: [
							{
								required: false,
								message: '请输入关键字',
								trigger: 'blur'
							}
						],
						hide: true
					},
					{
						label: '描述',
						prop: 'description',
						rules: [
							{
								required: false,
								message: '请输入描述',
								trigger: 'blur'
							}
						],
						hide: true,
						display: true
					},
					{
						label: '所属目录',
						prop: 'nodeId',
						type: 'select',
						dicUrl:
							(process.env.NODE_ENV === 'development' ? 'api' : '') +
							nodeUrl.getList +
							'?pageNum=1&pageSize=-1',
						dicFormatter: res => {
							return res.results;
						},
						props: {
							label: 'name',
							value: 'id'
						},
						rules: [
							{
								required: true,
								message: '请选择所属目录',
								trigger: 'blur'
							}
						],
						disabled: true,
						hide: true
					},
					{
						label: '内容显示类型',
						prop: 'infoType',
						rules: [
							{
								required: true,
								message: '请确定显示类型',
								trigger: 'blur'
							}
						],
						type: 'radio',
						dicData: [
							{ label: '普通', value: 2 },
							{ label: '跳转外链', value: 1 }
						],
						editDisabled: false,
						hide: true
					},
					{
						label: '文章模型',
						prop: 'infoModelId',
						type: 'select',
						dicUrl:
							(process.env.NODE_ENV === 'development' ? 'api' : '') +
							modelUrl.getList +
							'?status=1&pageNum=1&pageSize=-1',
						dicFormatter: res => {
							return res.results;
						},
						props: {
							label: 'name',
							value: 'id'
						},
						rules: [
							{
								required: true,
								message: '请选择文章模型ID',
								trigger: 'blur'
							}
						],
						disabled: true,
						display: true
					},
					{
						label: '发布者',
						prop: 'publisherInfo',
						width: 140,
						overHidden: true,
						slot: true,
						hide: true,
						display: false
					},
					{
						label: '发布时间',
						prop: 'createTime',
						type: 'datetime',
						format: 'yyyy-MM-dd HH:mm:ss',
						valueFormat: 'yyyy-MM-dd HH:mm:ss',
						width: 160,
						rules: [
							{
								required: false,
								message: '请输入发布时间',
								trigger: 'blur'
							}
						],
						hide: false,
						display: true
					},
					{
						label: '排序',
						prop: 'sortIndex',
						width: 70,
						rules: [
							{
								required: true,
								message: '请输入排列顺序',
								trigger: 'blur'
							}
						],
						type: 'number',
						minRows: 1
					},
					{
						label: '状态',
						prop: 'status',
						width: 70,
						type: 'radio',
						dicData: [
							{
								label: '启用',
								value: 1
							},
							{
								label: '禁用',
								value: 0
							}
						],
						rules: [
							{
								required: true,
								message: '请选择状态',
								trigger: 'blur'
							}
						]
					},
					{
						label: '创建人',
						prop: 'CreateName',
						rules: [
							{
								required: false,
								message: '请输入创建人',
								trigger: 'blur'
							}
						],
						minRows: 1,
						row: true
					},
					{
						label: '封面图片（1张）',
						prop: 'coverImg',
						// value: this.form.coverImg,
						component: 'esUpload', //ele分割线
						hide: true,
						listType: 'picture-card',
						selectType: 'icon-plus',
						code: 'scientific_gg_img',
						ownId: this.id,
						size: 16999,
						tip: '最多一张，文件格式jpg,png,gif,bmp,jpeg',
						limit: 1,
						row: true
					},
					{
						label: '附件',
						// prop: 'coverImg',
						// value: this.form.coverImg,
						component: 'esUpload', //ele分割线
						hide: true,
						code: 'scientific_gg_fj',
						ownId: this.id,
						size: 16999,
						row: true,
						preview: true
					},
					{
						label: '外链地址',
						prop: 'linkUrl',
						rules: [
							{
								required: true,
								message: '请输入外链地址',
								trigger: 'blur'
							}
						],
						overHidden: true,
						hide: true,
						display: false
					},
					{
						label: '站点ID',
						prop: 'siteId',
						rules: [
							{
								required: false,
								message: '请输入站点ID',
								trigger: 'blur'
							}
						],
						hide: true,
						display: false
					},
					{
						label: '租户ID',
						prop: 'rentId',
						rules: [
							{
								required: false,
								message: '请输入租户ID',
								trigger: 'blur'
							}
						],
						hide: true,
						display: false
					},
					{
						label: '修改时间',
						prop: 'updateTime',
						rules: [
							{
								required: false,
								message: '请输入修改时间',
								trigger: 'blur'
							}
						],
						hide: true,
						display: false
					},
					{
						label: '发布状态',
						prop: 'publishAudit',
						type: 'radio',
						// dicUrl: '/blade-system/dict/dictionary?code=publish_audit',
						props: {
							label: 'dictValue',
							value: 'dictKey'
						},
						width: 80,
						rules: [
							{
								required: false,
								message: '请选择发布状态',
								trigger: 'blur'
							}
						],
						hide: true,
						display: false
					},
					{
						label: '审核意见',
						prop: 'auditOpinion',
						rules: [
							{
								required: false,
								message: '请填写审核意见',
								trigger: 'blur'
							}
						],
						hide: true,
						display: false
					},
					...this.modelColumn
				]
			};
		}
	},
	watch: {
		// 'form.infoType'(){
		//   let index_linkUrl = this.getColIndex("linkUrl");
		//   let index_infoModelId = this.getColIndex("infoModelId");
		//   this.option.column[index_linkUrl].display=false;
		//   this.option.column[index_infoModelId].display=false;
		//   if(this.form.infoType === 2){
		//     this.option.column[index_infoModelId].display=true;
		//     this.doClearDynamicForm();
		//     if(this.infoModelId){
		//       this.doGetModelFormInfo({modelId:this.infoModelId});
		//     }
		//   }else if(this.form.infoType === 1){
		//     this.option.column[index_linkUrl].display=true;
		//     this.doClearDynamicForm();
		//   }else{
		//     this.doClearDynamicForm();
		//   }
		// }
		'form.infoType'() {
			//当内容显示类型为 外链时 也展示自定义字段填写
			let index_linkUrl = this.getColIndex('linkUrl');
			this.option.column[index_linkUrl].display = false;
			if (this.form.infoType === 1) {
				this.option.column[index_linkUrl].display = true;
			}
			this.doChangeCustomColReq();
		}
	},
	created() {
		if (this.$route.query.code) {
			this.nodeCode = this.$route.query.code;
		}
		this.treeNodeListInfo();
	},
	methods: {
		cellStyle({ row, column, rowIndex, columnIndex }) {
			if (column.property === 'status') {
				if (row.status === 0) {
					return { color: 'red', 'text-align': 'center' };
				}
			} else if (column.property === 'publishAudit') {
				// 1.草稿、2.待审核、3.审核不通过、4.已发布、5.已下线
				if (row.publishAudit === 2) {
					return { color: '#F59A23' };
				} else if (row.publishAudit === 3) {
					return { color: '#D9001B' };
				} else if (row.publishAudit === 4) {
					return { color: '#219D3D' };
				} else if (row.publishAudit === 5) {
					return { color: '#7F7F7F' };
				}
			} else if (column.property === 'id' || column.property === 'title') {
				return { 'text-align': 'left' };
			}
		},
		treeNodeListInfo() {
			this.$request({
				url: nodeUrl.getTreeNodeList + '?nodeCode=' + this.nodeCode,
				method: 'GET'
			}).then(res => {
				if (res.rCode === 0) {
					let results = res.results;
					this.treeData = results;
					if (results == null || results.length === 0) {
						return;
					}
					this.treeOption.defaultExpandedKeys = res.results[0].id;
					this.nodeClick(this.treeData[0]);
					this.$nextTick(() => {
						let selNode = document.getElementById(results[0].id);
						selNode.classList.add('treenode-default-checked');
					});
				} else {
				}
			});
		},
		getColIndex(prop) {
			for (let index in this.option.column) {
				if (this.option.column[index].prop === prop) {
					return index;
				}
			}
		},
		rowSave(row, done, loading) {
			let temForm = {};
			let dynamicForm = {};
			for (let itme in defForm) {
				temForm[itme] = this.form[itme];
			}
			for (let itme in this.dynamicField) {
				dynamicForm[itme] = this.form[itme];
			}
			temForm.id = this.id;
			temForm.dynamic = dynamicForm;
			temForm.isUpdate = false;
			delete temForm.coverImg;
			this.$request({
				url: infoUrl.add,
				data: temForm,
				method: 'POST',
				format: false
			}).then(res => {
				if (res.rCode === 0) {
					done();
					this.onLoad();
					this.$message({
						type: 'success',
						message: '操作成功!'
					});
				} else {
					this.$message.error(res.msg);
					loading();
				}
			});
		},
		rowUpdate(row, index, done, loading) {
			let temForm = {};
			let dynamicForm = {};
			for (let itme in defForm) {
				temForm[itme] = this.form[itme];
			}
			for (let itme in this.dynamicField) {
				dynamicForm[itme] = this.form[itme];
			}
			temForm.dynamic = dynamicForm;
			temForm.isUpdate = true;
			delete temForm.coverImg;
			this.$request({
				url: infoUrl.update,
				data: temForm,
				method: 'POST',
				format: false
			}).then(res => {
				if (res.rCode === 0) {
					done();
					this.onLoad();
					this.$message({
						type: 'success',
						message: '操作成功!'
					});
				} else {
					this.$message.error(res.msg);
					loading();
				}
			});
		},
		rowDel(row) {
			this.$confirm('确定将选择数据删除?', {
				confirmButtonText: '确定',
				cancelButtonText: '取消',
				type: 'warning'
			}).then(() => {
				this.$request({
					url: infoUrl.remove,
					params: {
						ids: row.id
					},
					method: 'POST'
				}).then(res => {
					if (res.rCode === 0) {
						this.$message({
							type: 'success',
							message: '操作成功!'
						});
						this.onLoad();
					} else {
						this.$message({
							type: 'error',
							message: '操作失败!'
						});
					}
				});
			});
		},
		handleDelete() {
			if (this.selectionList.length === 0) {
				this.$message.warning('请选择至少一条数据');
				return;
			}
			this.$confirm('确定将选择数据删除?', {
				confirmButtonText: '确定',
				cancelButtonText: '取消',
				type: 'warning'
			})
				.then(() => {
					return remove(this.ids);
				})
				.then(() => {
					this.onLoad();
					this.$message({
						type: 'success',
						message: '操作成功!'
					});
				});
		},
		beforeOpen(done, type) {
			console.log(this.form);
			this.curOpt = type;
			if (['edit', 'view'].includes(type)) {
				this.id = this.form.id;
				// let index_coverImg = this.getColIndex('coverImg');
				// this.option.column[index_coverImg].data.ownId = this.id;
				this.$request({
					url: infoUrl.getDetail,
					params: {
						id: this.form.id
					},
					method: 'GET'
				}).then(res => {
					this.doClearDynamicForm();
					if (this.form.infoModelId) {
						this.doGetModelFormInfo({
							modelId: this.form.infoModelId,
							objType: 'info',
							objId: this.form.id
						});
					}
					this.form = Object.assign(this.form, res.results);
				});
			} else if (type == 'add') {
				this.id = snowflake.generate();
				this.form.id = this.id;
				// let index_coverImg = this.getColIndex('coverImg');
				// this.option.column[index_coverImg].data.ownId = this.id;
				let infoModelId = this.infoModelId;
				if (
					this.nodeType !== 3 &&
					(infoModelId === '' || infoModelId === null || infoModelId === undefined)
				) {
					this.$message({ type: 'warning', duration: 2000, message: '此目录下没有内容模型选项!' });
					return;
				}
				this.doClearFormForAdd();
				if (infoModelId !== '' && infoModelId !== null && infoModelId !== undefined) {
					this.doGetModelFormInfo({ modelId: infoModelId });
				}
			}
			done();
		},
		//获取模型信息
		doGetModelFormInfo(params) {
			this.$request({
				url: modelFieldUrl.getModelForm,
				params: params,
				method: 'GET'
			}).then(res => {
				if (res.rCode === 0) {
					let modelColumn = res.results.modelColumn;
					this.customReqCols = [];
					for (let col of modelColumn) {
						if (col.rules[0].required) {
							this.customReqCols.push(col.prop);
						}
						let component = componentList.find(
							item => item.value === col.type || item.value === col.originType
						);
						col = Object.assign(col, component.option);
						if (col.type.startsWith('upload')) {
							col.data = {
								ownId: this.id,
								code: 'platform_cms_dynamic',
								isShowPath: true
							};
						}
					}
					this.modelColumn = modelColumn;
					// this.option.column = this.option.column.concat(modelColumn);
					this.dynamicField = res.results.modelForm;
					for (let item in this.dynamicField) {
						this.$set(this.form, item, this.dynamicField[item]);
					}
					this.editors = res.results.editorFields;
					this.maps = res.results.mapFields;
					this.doChangeCustomColReq();
				}
			});
		},
		//清空表单及字段的动态字段定义信息
		doClearDynamicForm() {
			let temForm = {};
			for (let item in defForm) {
				temForm[item] = this.form[item] || defForm[item];
			}
			this.form = Object.assign({}, temForm);
			this.form.infoModelId = this.infoModelId;
			this.form.nodeId = this.nodeId;
			this.dynamicField = {};
			let dynamicLen = this.option.column.length;
			if (dynamicLen > this.colNum) {
				this.option.column.splice(this.colNum, dynamicLen - this.colNum);
			}
			this.editors = [];
			this.maps = [];
		},
		//新增时清空表单
		doClearFormForAdd() {
			this.form = Object.assign({}, defForm);
			this.form.infoModelId = this.infoModelId;
			this.form.nodeId = this.nodeId;
			this.dynamicField = {};
			let dynamicLen = this.option.column.length;
			if (dynamicLen > this.colNum) {
				this.option.column.splice(this.colNum, dynamicLen - this.colNum);
			}
			this.editors = [];
			this.maps = [];
		},
		doChangeCustomColReq() {
			//将自定义字段的必填设置改变
			//若是显示普通，则编辑的时候需要还原自定义字段配置的必填
			let req = this.form.infoType === 2;
			for (let col of this.option.column) {
				if (this.customReqCols.includes(col.prop)) {
					col.rules[0].required = req;
				}
			}
		},
		searchReset() {
			this.page = Object.assign({}, defPage);
			let nodeId = this.query.nodeId;
			this.query = Object.assign({}, defQueryForm);
			this.query.nodeId = nodeId;
			this.onLoad();
		},
		searchChange(params, done) {
			this.query = params;
			this.page.currentPage = 1;
			this.onLoad();
			done();
		},
		doSearch(done, loading) {
			for (let key in this.query) {
				let type = typeof this.query[key];
				if (type === 'string' && this.query[key] === '') {
					this.query[key] = null;
				}
			}
			if (
				this.query.publishDate != null &&
				this.query.publishDate != undefined &&
				this.query.publishDate.length === 2
			) {
				this.query.STime = this.query.publishDate[0] + ' 00:00:00';
				this.query.ETime = this.query.publishDate[1] + ' 23:59:59';
			}
			this.page = Object.assign({}, defPage);
			this.onLoad();
			this.searchDialogVisible = false;
			let nodeId = this.query.nodeId;
			this.query = Object.assign({}, defQueryForm);
			this.query.nodeId = nodeId;
			if (loading) {
				loading();
			}
		},
		openAdvSearch() {
			let nodeId = this.query.nodeId;
			this.query = Object.assign({}, defQueryForm);
			this.query.nodeId = nodeId;
			this.searchDialogVisible = true;
		},
		selectionChange(list) {
			this.selectionList = list;
		},
		selectionClear() {
			this.selectionList = [];
		},
		currentChange(currentPage) {
			this.page.currentPage = currentPage;
		},
		sizeChange(pageSize) {
			this.page.pageSize = pageSize;
		},
		onLoad() {
			if (this.query.nodeId) {
				this.loading = true;
				let params = {
					pageNum: this.page.currentPage,
					pageSize: this.page.pageSize
				};
				params = Object.assign(params, this.query);
				this.$request({
					url: infoUrl.page,
					params: params,
					method: 'GET'
				}).then(res => {
					const data = res.results;
					this.page.total = data.total;
					this.data = data.records;
					this.loading = false;
					this.selectionClear();
				});
			}
			this.loading = false;
		},

		nodeClick(data) {
			this.query = Object.assign({}, { nodeId: data.id });
			this.infoModelId = data.infoModelId;
			this.nodeId = data.id;
			this.nodeType = data.nodeType;
			this.sortMode = data.sortModeStr;
			//1.外部链接、2.文档、3.目录、4.外部应用数据
			if (data.nodeType === 4) {
				this.option.addBtn = false;
				this.appInfoTitle = data.name;
				this.appNodeId = data.id;
				this.appInfoModel = data.infoModelId;
				this.appDataDialogVisible = true;
				this.page = Object.assign({}, defPage);
				this.data = [];
			} else if (data.nodeType === 3) {
				this.option.addBtn = true;
				this.onLoad();
			} else {
				this.option.addBtn = false;
				this.page.total = 0;
				this.data = [];
				this.selectionClear();
			}
		},
		handleAudit(row) {
			// getDetail(row.id).then(res => {
			// 	this.doClearDynamicForm();
			// 	this.form = Object.assign({}, res.data.data);
			// 	if (row.infoType != 1 && row.infoModelId) {
			// 		this.doGetModelFormInfo({ modelId: row.infoModelId, objType: 'info', objId: row.id });
			// 	}
			// });
			// this.option.disabled = true;
			// this.dialogVisible = true;
			// this.auditForm.id = row.id;
			// this.auditForm.auditOpinion = row.auditOpinion;
			// if (row.publishAudit === 1 || row.publishAudit === 2) {
			// 	this.auditForm.publishAudit = null;
			// } else if (row.publishAudit === 3) {
			// 	this.auditForm.publishAudit = row.publishAudit;
			// } else if (row.publishAudit === 4 || row.publishAudit === 5) {
			// 	this.auditForm.publishAudit = 4;
			// }
			// this.auditForm.publishAudit = row.publishAudit;
		},
		handleClose() {
			this.dialogVisible = false;
			this.option.disabled = false;
			this.auditForm = Object.assign({}, defAuditForm);
		},
		doAudit() {
			this.$refs.auditForm.validate(valid => {
				if (!valid) {
					return;
				}
				auditPublish(this.auditForm).then(res => {
					this.option.disabled = false;
					this.dialogVisible = false;
					this.auditForm = Object.assign({}, defAuditForm);
					this.onLoad();
				});
			});
		},
		openMap(mapCol) {
			this.corMapCol = mapCol;
			this.showMap = true;
		},
		checkedAddress(pointLngLat) {
			this.form[this.corMapCol] = pointLngLat;
			this.showMap = false;
		},
		closeMapDialog() {
			this.showMap = false;
		}
	}
};
</script>

<style>
.dialogBtnClass {
	display: flex;
	justify-content: center;
}
.nodeTreeScrollbar {
	height: 659px;
	overflow: hidden;
}
.avue-crud__dialog {
	display: grid;
	align-items: self-start;
}
</style>
