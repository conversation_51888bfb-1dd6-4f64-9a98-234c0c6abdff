// @--算法管理

const api = {
	add: '/prod-api/owap-data-engine/dataForm/execute?id=1804067773341532161', // 算法新增
	edit: '/prod-api/owap-data-engine/dataForm/execute?id=1804071365855182850', // 算法修改
	delete: '/prod-api/owap-data-engine/dataForm/execute?id=1804071495320764418', // 算法删除
	info: '/prod-api/owap-data-engine/dataApi/execute/1804073003097554946', // 算法查询

	classifyAdd: '/prod-api/owap-data-engine/dataForm/execute?id=1804070415182626818', // 算法分类新增
	classifyEdit: '/prod-api/owap-data-engine/dataForm/execute?id=1804391849922691074', // 算法分类修改
	classifyDelete: '/prod-api/owap-data-engine/dataForm/execute?id=1804073127106347009', // 算法分类删除
	classifyInfo: '/prod-api/owap-data-engine/dataApi/execute/1804073216214335490', // 算法分类查询
};
export default api;
