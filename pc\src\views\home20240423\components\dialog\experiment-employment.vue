<template>
	<div class="card-box">
		<div class="student-box">
			<div :style="{ width: '100%', height: '110px', marginBottom: '12px' }" class="card-style">
				<p class="card-title">毕业就业学生性别</p>
				<div class="sex">
					<div
						v-for="(item, index) in [
							{ count: '', ratio: 100, ratioUp: 60, isUp: true },
							{ count: '', ratio: 80, ratioUp: 60, isUp: false }
						]"
						:key="index"
						class="sex-box"
					>
						<div class="l">
							<span class="bg" :style="{ width: item.ratio + '%' }" style=""></span>
						</div>
						<div class="c">
							{{ item.count }}
							<span class="unit">人</span>
						</div>
						<div class="r">
							较上年 {{ item.ratioUp || '-' }}%
							<img
								v-if="item.isUp === true"
								class="desc-img"
								:src="require('@/assets/images/home20240423/up-icon.png')"
								alt=""
							/>
							<img
								v-if="item.isUp === false"
								class="desc-img"
								:src="require('@/assets/images/home20240423/down-icon.png')"
								alt=""
							/>
						</div>
					</div>
				</div>
			</div>
			<div :style="{ width: '100%', height: '110px' }" class="card-style">
				<p class="card-title">毕业未就业学生性别</p>
				<div class="sex">
					<div
						v-for="(item, index) in [
							{ count: '', ratio: 60, ratioUp: 60, isUp: true },
							{ count: '', ratio: 30, ratioUp: 60, isUp: false }
						]"
						:key="index"
						class="sex-box"
					>
						<div class="l">
							<span class="bg" :style="{ width: item.ratio + '%' }"></span>
						</div>
						<div class="c">
							{{ item.count }}
							<span class="unit">人</span>
						</div>
						<div class="r">
							较上年 {{ item.ratioUp || '-' }}%
							<img
								v-if="item.isUp === true"
								class="desc-img"
								:src="require('@/assets/images/home20240423/up-icon.png')"
								alt=""
							/>
							<img
								v-if="item.isUp === false"
								class="desc-img"
								:src="require('@/assets/images/home20240423/down-icon.png')"
								alt=""
							/>
						</div>
					</div>
				</div>
			</div>
		</div>
		<div :style="{ width: '26%', height: '232px' }" class="card-style">
			<p class="card-title">本省就业top5分析</p>
			<p class="card-desc">
				<span>
					本省就业较上年
					<span class="num-text">0%</span>
				</span>
				<img class="desc-img" :src="require('@/assets/images/home20240423/down-icon.png')" alt="" />
			</p>
			<div id="thisProvince"></div>
		</div>
		<div :style="{ width: '26%', height: '232px' }" class="card-style">
			<p class="card-title">外省就业top5分析</p>
			<p class="card-desc">
				<span>
					省外就业较上年
					<span class="num-text">0%</span>
				</span>
				<img class="desc-img" :src="require('@/assets/images/home20240423/down-icon.png')" alt="" />
			</p>
			<div id="otherProvince"></div>
		</div>
		<div :style="{ width: '26%', height: '232px' }" class="card-style">
			<p class="card-title">市内就业top5分析</p>
			<p class="card-desc">
				<span>
					市内就业较上年
					<span class="num-text">0%</span>
				</span>
				<img class="desc-img" :src="require('@/assets/images/home20240423/up-icon.png')" alt="" />
			</p>
			<div id="inProvince"></div>
		</div>
	</div>
</template>

<script>
var symbols = [
	'path://M18.2629891,11.7131596 L6.8091608,11.7131596 C1.6685112,11.7131596 0,13.032145 0,18.6237673 L0,34.9928467 C0,38.1719847 4.28388932,38.1719847 4.28388932,34.9928467 L4.65591984,20.0216948 L5.74941883,20.0216948 L5.74941883,61.000787 C5.74941883,65.2508314 11.5891201,65.1268798 11.5891201,61.000787 L11.9611506,37.2137775 L13.1110872,37.2137775 L13.4831177,61.000787 C13.4831177,65.1268798 19.3114787,65.2508314 19.3114787,61.000787 L19.3114787,20.0216948 L20.4162301,20.0216948 L20.7882606,34.9928467 C20.7882606,38.1719847 25.0721499,38.1719847 25.0721499,34.9928467 L25.0721499,18.6237673 C25.0721499,13.032145 23.4038145,11.7131596 18.2629891,11.7131596 M12.5361629,1.11022302e-13 C15.4784742,1.11022302e-13 17.8684539,2.38997966 17.8684539,5.33237894 C17.8684539,8.27469031 15.4784742,10.66467 12.5361629,10.66467 C9.59376358,10.66467 7.20378392,8.27469031 7.20378392,5.33237894 C7.20378392,2.38997966 9.59376358,1.11022302e-13 12.5361629,1.11022302e-13',
	'path://M28.9624207,31.5315864 L24.4142575,16.4793596 C23.5227152,13.8063773 20.8817445,11.7111088 17.0107398,11.7111088 L12.112691,11.7111088 C8.24168636,11.7111088 5.60080331,13.8064652 4.70917331,16.4793596 L0.149791395,31.5315864 C-0.786976655,34.7595013 2.9373074,35.9147532 3.9192135,32.890727 L8.72689855,19.1296485 L9.2799493,19.1296485 C9.2799493,19.1296485 2.95992025,43.7750224 2.70031069,44.6924335 C2.56498417,45.1567684 2.74553639,45.4852068 3.24205501,45.4852068 L8.704461,45.4852068 L8.704461,61.6700801 C8.704461,64.9659872 13.625035,64.9659872 13.625035,61.6700801 L13.625035,45.360657 L15.5097899,45.360657 L15.4984835,61.6700801 C15.4984835,64.9659872 20.4191451,64.9659872 20.4191451,61.6700801 L20.4191451,45.4852068 L25.8814635,45.4852068 C26.3667633,45.4852068 26.5586219,45.1567684 26.4345142,44.6924335 C26.1636859,43.7750224 19.8436568,19.1296485 19.8436568,19.1296485 L20.3966199,19.1296485 L25.2043926,32.890727 C26.1862111,35.9147532 29.9105828,34.7595013 28.9625083,31.5315864 L28.9624207,31.5315864 Z M14.5617154,0 C17.4960397,0 19.8773132,2.3898427 19.8773132,5.33453001 C19.8773132,8.27930527 17.4960397,10.66906 14.5617154,10.66906 C11.6274788,10.66906 9.24611767,8.27930527 9.24611767,5.33453001 C9.24611767,2.3898427 11.6274788,0 14.5617154,0 L14.5617154,0 Z',
	'path://M512 292.205897c80.855572 0 146.358821-65.503248 146.358821-146.358821C658.358821 65.503248 592.855572 0 512 0 431.144428 0 365.641179 65.503248 365.641179 146.358821 365.641179 227.214393 431.144428 292.205897 512 292.205897zM512 731.282359c-80.855572 0-146.358821 65.503248-146.358821 146.358821 0 80.855572 65.503248 146.358821 146.358821 146.358821 80.855572 0 146.358821-65.503248 146.358821-146.358821C658.358821 796.273863 592.855572 731.282359 512 731.282359z'
];
import * as echarts from 'echarts';
// import Card from '../card.vue';
import { xodbApi } from '@/api/xodb';
export default {
	// components: { Card },
	data() {
		return {
			thisProvinceCharts: null,
			// 本省

			thisProvinceList: [], //本省实习数据
			otherProvinceCharts: null,
			otherProvinceList: [], //外省实习数据
			// 外省

			inProvinceCharts: null,
			// 市内
			inProvinceOptions: {
				title: {
					text: '0人',
					subtext: '市内就业人数',
					left: '34%',
					top: '40%',
					textAlign: 'center',
					textStyle: {
						fontWeight: 'bold',
						fontSize: 16,
						color: ' #454545'
					}
				},
				tooltip: {
					trigger: 'item',
					formatter: '{a} <br/>{b} : {c} ({d}%)'
				},
				legend: {
					top: '10%',
					right: '7%',
					orient: 'vertical',
					itemWidth: 10, // 标志图形的长度
					itemHeight: 8, // 标志图形的宽度
					formatter: function (name, value) {
						// 添加
						let total = 0;
						let target;
						// for (let i = 0; i < data.length; i++) {
						// 	total += data[i].value;
						// 	if (data[i].name === name) {
						// 		target = data[i].value;
						// 	}
						// }
						var arr = [name, '{a|' + 0 + '人/' + 0 + '%' + '}'];
						return arr.join('  ');
					},
					textStyle: {
						fontSize: 12, // 字体大小
						color: '#454545', // 字体颜色
						rich: {
							a: {
								fontSize: 14,
								color: '#0A325B'
							}
						}
					},
					itemGap: 10,
					data: ['宜宾市', '南溪县', '高县', '南溪县1', '南溪县2']
				},
				series: {
					name: '市内就业人数',
					type: 'pie',
					label: {
						show: false
					},
					emphasis: {
						label: {
							show: true
						}
					},
					radius: [30, 50],
					center: ['11%', '50%'],
					itemStyle: {
						borderRadius: 2
					},
					data: [
						{ value: 0, name: '宜宾市' },
						{ value: 0, name: '南溪县' },
						{ value: 0, name: '高县' },
						{ value: 0, name: '南溪县1' },
						{ value: 0, name: '南溪县2' }
					]
				},
				color: ['#AC592A', '#FFFA4E', '#87B3D8', '#FF82E5', '#41BD95']
			}
		};
	},
	computed: {
		// 外省实习数据图表
		otherProvinceOptions() {
			return {
				title: {
					text: '0人',
					subtext: '外省就业人数',
					left: '34%',
					top: '40%',
					textAlign: 'center',
					textStyle: {
						fontWeight: 'bold',
						fontSize: 16,
						color: ' #454545'
					}
				},
				tooltip: {
					trigger: 'item',
					formatter: '{a} <br/>{b} : {c} ({d}%)'
				},
				legend: {
					top: '10%',
					right: '3%',
					orient: 'vertical',
					itemWidth: 10, // 标志图形的长度
					itemHeight: 8, // 标志图形的宽度
					formatter: (name, value) => {
						// 添加
						let total = 0;
						let target;
						for (let i = 0; i < this.otherProvinceList.length; i++) {
							total += this.otherProvinceList[i].value;
							if (this.otherProvinceList[i].name === name) {
								target = this.otherProvinceList[i].value;
							}
						}
						var arr = [
							name,
							'{a|' + target + '人/' + ((target / total) * 100).toFixed(2) + '%' + '}'
						];
						return arr.join('  ');
					},
					textStyle: {
						fontSize: 12, // 字体大小
						color: '#454545', // 字体颜色
						rich: {
							a: {
								fontSize: 14,
								color: '#0A325B'
							}
						}
					},
					itemGap: 10,
					data: []
				},
				series: {
					name: '外省就业人数',
					type: 'pie',
					label: {
						show: false
					},
					emphasis: {
						label: {
							show: true
						}
					},
					radius: [30, 50],
					center: ['13%', '50%'],
					itemStyle: {
						borderRadius: 2
					},
					data: []
				},
				color: ['#DDED8C', '#91C778', '#304E73', '#50E4F4', '#C392F3']
			};
		},
		// 本省实习数据图标
		thisProvinceOptions() {
			return {
				title: {
					text: '0',
					subtext: '本省就业人数',
					left: '34%',
					top: '40%',
					textAlign: 'center',
					textStyle: {
						fontWeight: 'bold',
						fontSize: 16,
						color: ' #454545'
					}
				},
				tooltip: {
					trigger: 'item',
					formatter: '{a} <br/>{b} : {c} ({d}%)'
				},
				legend: {
					top: '10%',
					right: '1%',
					orient: 'vertical',
					itemWidth: 10, // 标志图形的长度
					itemHeight: 8, // 标志图形的宽度
					formatter: (name, value) => {
						// 添加
						let total = 0;
						let target;
						for (let i = 0; i < this.thisProvinceList.length; i++) {
							total += this.thisProvinceList[i].value;
							if (this.thisProvinceList[i].name === name) {
								target = this.thisProvinceList[i].value;
							}
						}
						var arr = [
							name,
							'{a|' + target + '人/' + ((target / total) * 100).toFixed(2) + '%' + '}'
						];
						return arr.join('  ');
					},
					textStyle: {
						fontSize: 12, // 字体大小
						color: '#454545', // 字体颜色
						rich: {
							a: {
								fontSize: 14,
								color: '#0A325B'
							}
						}
					},
					itemGap: 10,
					data: []
				},
				series: {
					name: '本省就业人数',
					type: 'pie',
					label: {
						show: false
					},
					emphasis: {
						label: {
							show: true
						}
					},
					radius: [30, 50],
					center: ['13%', '50%'],
					itemStyle: {
						borderRadius: 2
					},
					data: []
				},
				color: ['#FFD56D', '#FFFA4E', '#50E4F4', '#FF82E5', '#7AEB81']
			};
		}
	},
	async mounted() {
		// this.createThisProvinceOptionsECharts(); //本省就业
		// this.createOtherProvinceOptionsECharts(); //外省就业
		this.createInProvinceOptionsECharts(); //市内
		// await this.getSxfxrsList(); //实习人数分析
	},
	methods: {
		// 本省就业人数
		createThisProvinceOptionsECharts(data, legend) {
			if (this.thisProvinceCharts) {
				this.thisProvinceCharts.dispose();
			}
			let chartDom = document.getElementById('thisProvince');
			if (!chartDom) {
				return;
			}
			this.thisProvinceCharts = echarts.init(chartDom);
			let options = this.thisProvinceOptions;
			options.series.data = data;
			options.legend.data = legend;
			this.thisProvinceCharts.setOption(options);
		},
		// 外省就业人数
		createOtherProvinceOptionsECharts(data, legend) {
			if (this.otherProvinceCharts) {
				this.otherProvinceCharts.dispose();
			}
			let chartDom = document.getElementById('otherProvince');
			if (!chartDom) {
				return;
			}
			this.otherProvinceCharts = echarts.init(chartDom);
			let options = this.otherProvinceOptions;
			console.log(this.otherProvinceOptions, 'this.otherProvinceOptions');
			options.series.data = data;
			options.legend.data = legend;
			this.otherProvinceCharts.setOption(options);
		},
		// 市内就业人数
		createInProvinceOptionsECharts(data) {
			if (this.inProvinceCharts) {
				this.inProvinceCharts.dispose();
			}
			let chartDom = document.getElementById('inProvince');
			if (!chartDom) {
				return;
			}
			this.inProvinceCharts = echarts.init(chartDom);
			let options = this.inProvinceOptions;
			// options.series[0].data = this.data;
			this.inProvinceCharts.setOption(options);
		},
		// 实习人数分析
		async getSxfxrsList() {
			const {
				dataResult: { dataList }
			} = await xodbApi.get({
				url: 'warehouseConfig_ybzy_dw_dwb_jw_sxfxrs_query'
			});
			for (let item of dataList) {
				if (item.ssmc == '四川省') {
					this.thisProvinceOptions.title.text = item.sxrs + '人';
				} else if (item.ssmc == '非四川省') {
					this.otherProvinceOptions.title.text = item.sxrs + '人';
				}
			}
			await this.getWssxfxList(); //外省实习分析
			await this.getBssxfxList(); //本省实习分析
		},
		// 外省实习分析
		async getWssxfxList() {
			let params = {
				xyjgmc: '电子信息与人工智能学院' //学院机构名称
			};
			let {
				dataResult: { dataList }
			} = await xodbApi.get({
				url: 'warehouseConfig_ybzy_dw_dwb_jw_wssxfx_query',
				params: JSON.stringify(params)
			});
			dataList.sort((a, b) => {
				return b.xssl - a.xssl;
			});
			let List = dataList.slice(0, 5);
			let legend = List.reduce((acc, cur) => {
				this.otherProvinceList.push({
					...cur,
					name: cur.sxddszds,
					value: cur.xssl
				});
				acc.push(cur.sxddszds);
				return acc;
			}, []);
			this.createOtherProvinceOptionsECharts(this.otherProvinceList, legend);
		},
		// 本省实习分析
		async getBssxfxList() {
			let params = {
				xyjgmc: '电子信息与人工智能学院' //学院机构名称
			};
			let {
				dataResult: { dataList }
			} = await xodbApi.get({
				url: 'warehouseConfig_ybzy_dw_dwb_jw_bssxfx_query',
				params: JSON.stringify(params)
			});
			dataList.sort((a, b) => {
				return b.xssl - a.xssl;
			});
			let List = dataList.slice(0, 5);
			let legend = List.reduce((acc, cur) => {
				this.thisProvinceList.push({
					...cur,
					name: cur.sxddszds,
					value: cur.xssl
				});
				acc.push(cur.sxddszds);
				return acc;
			}, []);
			this.createThisProvinceOptionsECharts(this.thisProvinceList, legend);
		}
	}
};
</script>

<style lang="scss" scoped>
.card-title {
	font-family: MicrosoftYaHei, MicrosoftYaHei;
	font-weight: bold;
	font-size: 16px;
	color: #21252b;
	line-height: 21px;
	.num {
		color: #0b6fad;
	}
}
.card-style {
	background: rgba(252, 253, 254, 0.7);
	box-shadow: 0px 0px 10px 0px rgba(5, 32, 70, 0.1);
	border-radius: 8px;
	border: 2px solid #ffffff;
	padding: 14px 12px;
}
.card-desc {
	font-family: MicrosoftYaHei;
	font-size: 14px;
	color: #454545;
	line-height: 19px;
	margin-top: 11px;
	.num-text {
		color: #0a325b;
		font-weight: bold;
	}
	.desc-img {
		width: 8px;
		margin-left: 5px;
	}
}
.student-box {
	width: calc(20% - 6px);
	.sex {
		display: flex;
		flex-direction: column;
		justify-content: space-between;
		margin-top: 10px;

		.sex-box {
			display: flex;
			align-items: center;
			justify-content: space-between;
			.l {
				min-width: 110px;
				height: 14.5px;
				.bg {
					display: inline-block;
					width: 100%;
					height: 100%;
					background-image: url('~@/assets/images/home20240423/man.png');
					background-size: contain;
				}
			}

			.c {
				height: 23px;
				font-family: DINPro, DINPro;
				font-weight: bold;
				font-size: 18px;
				color: #0a325b;
				line-height: 23px;
				.unit {
					font-family: MicrosoftYaHei;
					font-size: 12px;
					color: #294d79;
					line-height: 1;
					font-weight: normal;
				}
			}
			.r {
				font-family: MicrosoftYaHei;
				font-size: 14px;
				color: #7b96b1;
				line-height: 19px;
				.desc-img {
					width: 8px;
					height: 13px;
					margin-left: 5px;
				}
			}
			&:last-child {
				.l {
					.bg {
						background-image: url('~@/assets/images/home20240423/woman.png');
					}
				}
			}
		}
	}
}
#thisProvince,
#otherProvince,
#inProvince {
	width: 100%;
	height: calc(100% - 50px);
	position: relative;
	&::after {
		content: '';
		height: 112px;
		position: absolute;
		border-left: 1px dashed #a9bed5;
		top: 16%;
		left: 52%;
	}
}
</style>
