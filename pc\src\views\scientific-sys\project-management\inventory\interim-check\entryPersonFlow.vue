<template>
	<div style="height: 100%">
		<es-flow-group :contents="contents" :flow="flow" @success="handleSuccess"></es-flow-group>
	</div>
</template>

<script>
import { saveInterimByid } from '@/api/scientific-sys.js';
import { v4 as uuidv4 } from 'uuid';

export default {
	props: {
		rowId: {
			type: String,
			required: true
		}
	},
	data() {
		return {
			flow: {
				isStartFlow: true, //是否发起流程
				flowTypeCode: 'inspection', //流程类型 code 码
				// businessId: '', //业务 id 后端自己提供
				btnList: [
					{ text: '提交', event: 'sub', type: 'primary' }
					// { text: '暂存', event: 'save', code: 0 }
				]
			},
			model: { projectId: '', inspectionFile: uuidv4() }
		};
	},
	computed: {
		contents() {
			return [
				{
					label: '基本信息',
					contents: {
						type: 'form',
						collapse: true,
						action: saveInterimByid,
						model: this.model,
						contents: [
							{
								name: 'inspectionContent',
								placeholder: '请输入',
								label: '中检说明',
								type: 'textarea',
								col: 12,
								rules: {
									required: true,
									message: '请输入中检说明',
									trigger: 'blur'
								}
							},
							{
								name: 'inspectionTime',
								placeholder: '请选择',
								label: '中检时间',
								type: 'date',
								col: 12,
								rules: {
									required: false,
									message: '请选择中检时间',
									trigger: 'blur'
								}
							},
							{
								name: 'declarationAdjunctFile',
								label: '附件',
								type: 'attachment',
								value: '',
								preview: true,
								download: true,
								ownId: this.model.inspectionFile, // 业务id
								code: 'inspectionfile',
								action: '/main2/mecpfileManagement/upload',
								col: 12
							}
						]
					}
				}
				// {
				// 	label: '正文',
				// 	contents: {
				// 		type: 'iframe',
				// 		blank: true,
				// 		url: 'http://localhost:8080/#/interimCheck'
				// 	}
				// }
			];
		}
	},
	created() {
		// this.flow.businessId = this.rowId;
		// this.contents[0].contents.model.projectId = this.rowId;
		// this.contents[0].contents.contents[2].ownId = this.rowId;
		this.model.projectId = this.rowId;
	},
	methods: {
		handleSuccess() {
			this.$emit('handleSuccess');
		}
	}
};
</script>
