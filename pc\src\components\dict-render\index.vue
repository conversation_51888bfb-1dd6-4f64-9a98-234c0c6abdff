<!--
 * @Description: 数据字典查询
 * @Version: 1.0
 * @Autor: zhaodongming
 * @Date: 2023-02-09 09:36:36
 * @LastEditors: zhaodongming
 * @LastEditTime: 2023-05-27 19:56:58
-->
<template>
	<span v-if="valString">{{ valString }}</span>
	<span v-else>-</span>
</template>

<script>
import interfaceUrl from '@/http/common/system.js';
export default {
	name: 'DictRender',
	props: {
		code: {
			type: String,
			default: ''
		},
		value: {
			type: [String, Number, null],
			default: null
		}
	},
	data() {
		return {
			valString: null
		};
	},
	watch: {
		value: {
			deep: true,
			immediate: true,
			handler(newVal) {
				if (this.code && newVal) {
					this.findCodeData(this.code);
				}
			}
		}
	},
	methods: {
		// 查询数据
		findCodeData(code) {
			this.$request({
				url: interfaceUrl.findSysCodeList,
				data: {
					sysAppCodes: code
				},
				method: 'POST'
			}).then(res => {
				if (res.rCode == 0) {
					res.results[code].forEach(list => {
						if (list.cciValue == this.value) {
							this.valString = list.shortName;
						}
					});
				}
			});
		}
	}
};
</script>

<style scoped></style>
