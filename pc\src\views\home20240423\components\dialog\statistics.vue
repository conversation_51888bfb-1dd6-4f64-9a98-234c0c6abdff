<template>
	<div class="floor-box">
		<div class="content1">
			<div class="title">{{ title === '实习' ? '实习就业情况' : '毕业就业情况' }}</div>
			<table border="1" class="table">
				<tbody>
					<tr>
						<td>
							<div class="table-con">
								<span class="label">专业对口</span>
								<span class="value">
									{{ tableData1.zydkrs }}
									<span class="unit">人</span>
								</span>
							</div>
						</td>
						<td>
							<div class="table-con">
								<span class="label">自由职业</span>
								<span class="value">
									{{ tableData1.zyzyrs }}
									<span class="unit">人</span>
								</span>
							</div>
						</td>
					</tr>
					<tr>
						<td>
							<div class="table-con">
								<span class="label">自主创业</span>
								<span class="value">
									{{ tableData1.zycyrs }}
									<span class="unit">人</span>
								</span>
							</div>
						</td>

						<td>
							<div class="table-con">
								<span class="label">签订就业协议</span>
								<span class="value">
									{{ tableData1.qdjyxyrs }}
									<span class="unit">人</span>
								</span>
							</div>
						</td>
					</tr>
					<tr>
						<td>
							<div class="table-con">
								<span class="label">签订劳动合同</span>
								<span class="value">
									{{ tableData1.qdldhtrs }}
									<span class="unit">人</span>
								</span>
							</div>
						</td>
					</tr>
				</tbody>
			</table>
		</div>
		<div class="content2">
			<div class="title">
				<span>就业企业类型分布</span>
				<span class="unit">单位：人</span>
			</div>
			<div id="chart1" class="chart"></div>
		</div>
		<div class="content3">
			<div class="title">
				<span>就业薪资趋势年度统计</span>
				<span class="unit">单位：元</span>
			</div>
			<div v-if="title === '毕业就业'" class="text-group">
				<div class="group-item">
					<span class="label">本年就业平均薪资</span>
					<span class="value">0元</span>
				</div>
				<div class="group-item">
					<span class="label">较上年</span>
					<span class="value">
						0%
						<img
							v-if="true"
							class="desc-img"
							:src="require('@/assets/images/home20240423/up-icon.png')"
							alt=""
						/>
						<img
							v-else
							class="desc-img"
							:src="require('@/assets/images/home20240423/down-icon.png')"
							alt=""
						/>
					</span>
				</div>
			</div>
			<div v-else class="text-group">
				<div class="group-item">
					<span class="label">本年就业平均薪资</span>
					<span class="value">0元</span>
				</div>
				<div class="group-item">
					<span class="label">最高薪资</span>
					<span class="value">0元</span>
				</div>
				<div class="group-item">
					<span class="label">最低薪资</span>
					<span class="value">0元</span>
				</div>
			</div>
			<div id="chart2" class="chart"></div>
		</div>
		<div class="content4">
			<div class="title">就业行业top5</div>
			<table border="1" class="table">
				<tbody>
					<tr>
						<td>
							<div class="table-con">
								<span class="label">电子信息技术</span>
								<span class="value">
									0
									<span class="unit">人</span>
								</span>
							</div>
						</td>
						<td>
							<div class="table-con">
								<span class="label">制造业</span>
								<span class="value">
									0
									<span class="unit">人</span>
								</span>
							</div>
						</td>
					</tr>
					<tr>
						<td>
							<div class="table-con">
								<span class="label">信息工程</span>
								<span class="value">
									0
									<span class="unit">人</span>
								</span>
							</div>
						</td>

						<td>
							<div class="table-con">
								<span class="label">建筑行业</span>
								<span class="value">
									0
									<span class="unit">人</span>
								</span>
							</div>
						</td>
					</tr>
					<tr>
						<td>
							<div class="table-con">
								<span class="label">销售行业</span>
								<span class="value">
									0
									<span class="unit">人</span>
								</span>
							</div>
						</td>
					</tr>
				</tbody>
			</table>
		</div>
	</div>
</template>

<script>
export default {
	name: 'Teacher',
	props: {
		title: {
			type: String, // 毕业就业 实习
			default: '毕业就业'
		},
		tableData1: {
			//就业情况
			type: Object,
			default: () => {
				return {};
			}
		},
		tableData2: {
			type: Object,
			default: () => {
				return {};
			}
		}
	},
	data() {
		return {
			myChart: []
		};
	},
	computed: {
		classify() {
			const type = '毕业就业';
			if (type === 'type') {
				return [
					{ title: '就业率年度分析', color: ['#17CBBA', '#BFF3507'] },
					{ title: '专业对口率年度分析', color: ['#FF9B33', '#EBCC32'] },
					{ title: '升学率年度分析', color: ['#1293DF', '#4FDCB3'] }
				];
			}
			return [
				{ title: '实习就业率年度分析', color: ['#17CBBA', '#BFF3507'] },
				{ title: '实习对口上岸率年度分析', color: ['#FF9B33', '#EBCC32'] },
				{ title: '实习稳定率年度分析', color: ['#1293DF', '#4FDCB3'] }
			];
		}
	},
	created() {},
	mounted() {
		window.addEventListener('resize', this.onResize);
		this.toolChart('chart1', 0);
		this.toolChart('chart2', 1);
		// this.toolChart('myEchart3', 2);
	},
	beforeDestroy() {
		window.removeEventListener('resize', this.onResize);
		this.myChart.forEach(item => {
			if (item) {
				item.dispose();
			}
		});
	},
	methods: {
		toolChart(dom, i) {
			let chartDom = document.getElementById(dom);
			if (this.myChart[i]) {
				this.myChart[i].clear();
			} else {
				this.myChart[i] = this.$echarts.init(chartDom);
			}

			let option = null;
			if (i === 0) {
				option = this.getOption1();
			} else {
				option = this.getOption2();
			}

			option && this.myChart[i].setOption(option);
		},
		// 柱状图option
		getOption1() {
			const data = [
				['类型1', 100],
				['类型2', 50],
				['类型3', 150],
				['类型4', 200],
				['类型5', 210],
				['类型6', 200],
				['类型7', 300]
			];
			const option = {
				dataset: {
					source: data
				},
				tooltip: {
					trigger: 'axis',
					axisPointer: {
						type: 'shadow'
					},
					distance: 2,
					color: '#FFFFFF',
					fontSize: 12,
					borderColor: '#09354F',
					borderWidth: 1,
					borderRadius: 4,
					padding: [0, 10],
					backgroundColor: 'rgba(9,53,79,0.55)',
					height: 20,
					lineHeight: 18,
					formatter: function (params) {
						return params[0].value[1] + '人';
					},
					textStyle: {
						color: '#FFFFFF'
					},
					position: function (point, params, dom, rect, size) {
						return [point[0] + 10, point[1] - 20];
					}
				},
				grid: {
					left: '12%',
					right: '6%',
					bottom: '15%',
					top: '11%'
				},
				xAxis: {
					type: 'category',
					axisLine: {
						lineStyle: {
							color: '#dddddd'
						}
					},
					axisTick: {
						show: false,
						alignWithLabel: true,
						lineStyle: {
							color: '#f3f3f3'
						}
					},
					axisLabel: {
						textStyle: {
							color: '#0A325B',
							fontSize: 14
						}
					}
				},

				yAxis: {
					type: 'value',
					min: 0,
					axisLine: {
						show: false,
						lineStyle: {
							color: '#dddddd'
						}
					},
					data: [1, 2, 3],
					axisTick: {
						show: true,
						alignWithLabel: true,
						lineStyle: {
							color: '#B2C3DA'
						},
						textStyle: {
							color: '#8997A5',
							fontSize: 14
						}
					},
					splitLine: {
						//刻度线
						show: true,
						lineStyle: {
							color: '#B2C3DA',
							type: [3, 3],
							dashOffset: 2
						}
					},
					axisLabel: {
						padding: [0, 5, 0, 0],
						textStyle: {
							color: '#8997A5',
							fontSize: 14
						}
					}
				},
				series: [
					{
						type: 'bar',
						barWidth: 10,
						// 背景颜色
						itemStyle: {
							color: 'rgba(32,190,86,0.31)'
						}
					}
				]
			};
			return option;
		},
		// 折线图option
		getOption2() {
			const xData = ['2019年', '2020年', '2021年', '2022年', '2023年'];
			const yData = [1500, 1800, 2300, 3000, 4000];
			const option = {
				tooltip: {
					trigger: 'axis',
					axisPointer: {
						type: 'shadow'
					},
					distance: 2,
					color: '#FFFFFF',
					fontSize: 12,
					borderColor: '#09354F',
					borderWidth: 1,
					borderRadius: 4,
					padding: [0, 10],
					backgroundColor: 'rgba(9,53,79,0.55)',
					height: 20,
					lineHeight: 18,
					formatter: function (params) {
						return params[0].value + '元';
					},
					textStyle: {
						color: '#FFFFFF'
					},
					position: function (point, params, dom, rect, size) {
						return [point[0] + 10, point[1] - 20];
					}
				},
				grid: {
					left: '12%',
					right: '6%',
					bottom: '15%',
					top: '11%'
				},
				xAxis: [
					{
						type: 'category',
						boundaryGap: false,
						axisLine: {
							lineStyle: {
								color: '#dddddd'
							}
						},
						axisTick: {
							show: false,
							alignWithLabel: true,
							lineStyle: {
								color: '#f3f3f3'
							}
						},
						axisLabel: {
							textStyle: {
								color: '#0A325B',
								fontSize: 14
							}
						},
						data: xData
					}
				],
				yAxis: {
					type: 'value',
					// data: yData,
					axisTick: {
						show: true,
						alignWithLabel: true,
						lineStyle: {
							color: '#B2C3DA'
						},
						textStyle: {
							color: '#8997A5',
							fontSize: 14
						}
					},

					splitLine: {
						//刻度线
						show: true,
						lineStyle: {
							color: '#B2C3DA',
							type: [3, 3],
							dashOffset: 2
						}
					},
					axisLabel: {
						padding: [0, 5, 0, 0],
						textStyle: {
							color: '#8997A5',
							fontSize: 14
						}
					}
				},
				series: [
					{
						name: '次数',
						type: 'line',
						symbol: 'none',
						itemStyle: {
							normal: {
								color: '#2af7ff',
								lineStyle: {
									color: '#3E8AF6',
									width: 1
									// 去圆的
								},
								areaStyle: {
									color: new this.$echarts.graphic.LinearGradient(0, 1, 0, 0, [
										{
											offset: 0,
											color: 'RGBA(131,223,255,0)'
										},
										{
											offset: 1,
											color: 'RGBA(41,116,241,0.4)'
										}
									])
								}
							}
						},
						data: yData
					}
				]
			};
			return option;
		},
		onResize() {
			this.myChart.forEach(item => {
				if (item) {
					item.resize();
				}
			});
		}
	}
};
</script>

<style lang="scss" scoped>
.floor-box {
	display: flex;
	justify-content: space-between;
	width: 100%;
	.title {
		width: 100%;
		height: 21px;
		font-family: MicrosoftYaHei, MicrosoftYaHei;
		font-weight: bold;
		font-size: 16px;
		color: #21252b;
		line-height: 21px;
		text-align: left;
		font-style: normal;
		margin-bottom: 12px;
		display: flex;
		justify-content: space-between;
		align-items: center;
		.unit {
			height: 16px;
			font-size: 12px;
			color: #7b96b1;
			line-height: 16px;
			font-weight: normal;
		}
	}
	.content1,
	.content2,
	.content3,
	.content4 {
		padding: 12px 18px 10px 19px;
		width: 456px;
		height: 220px;
		background: rgba(252, 253, 254, 0.7);
		box-shadow: 0px 0px 10px 0px rgba(5, 32, 70, 0.1);
		border-radius: 8px;
		border: 2px solid #ffffff;
		margin-right: 12px;
		&:last-child {
			margin-right: 0;
		}
	}
	.content1 {
		width: 344px;
	}
	.content1,
	.content4 {
		padding: 12px 11px 20px 20px;
		.table {
			width: 100%;
			height: 155px;
			background: rgba(222, 234, 244, 0.25);
			border: 1px solid #c1cedb;
			border-radius: 3px;
			// border-collapse: separate; /* 保持边框独立，这样可以单独控制表格边框 */
			// border-spacing: 0; /* 防止单元格间的额外间隔，模拟边框合并的效果 */
			.table-con {
				width: 100%;
				height: 100%;
				padding: 0 13px;
				display: flex;
				justify-content: space-between;
				align-items: center;
				font-family: MicrosoftYaHei;
				font-size: 12px;
				color: #454545;
				line-height: 1;
				.value {
					font-weight: bold;
					.unit {
						font-weight: normal;
						margin-left: 2px;
					}
				}
			}
		}
		table,
		th,
		td {
			border: 1px solid #c1cedb; /* 这里设置边框宽度和颜色，例如红色边框 */
		}
	}

	.content2 {
		.chart {
			width: 100%;
			height: 165px;
		}
	}
	.content3 {
		.text-group {
			width: 100%;
			display: flex;

			.group-item {
				font-family: MicrosoftYaHei;
				font-size: 14px;
				// color: #454545;
				line-height: 1;
				margin-right: 19px;
				&:last-child {
					margin-right: 0;
				}
				.label {
					color: #454545;
				}

				.value {
					color: #0a325b;
					font-weight: bold;
					.desc-img {
						width: 8px;
						height: 13px;
					}
				}
			}
		}
		.chart {
			width: 100%;
			height: 145px;
		}
	}
}
</style>
