<template>
	<div class="sketch_content">
		<el-form ref="form" :model="formData" class="resumeTable" :rules="rules">
			<el-row>
				<el-col :span="12">
					<el-form-item label="社团" label-width="90px" label-position="left" prop="societyId">
						<el-select
							v-model="formData.societyId"
							:disabled="editDisabled"
							placeholder="选择社团"
							clearable
							:remote-method="getSocieties"
							filterable
							remote
							@change="societyChange"
						>
							<el-option
								v-for="item in sociSelectList"
								:key="item.value"
								:label="item.label"
								:value="item.value"
							></el-option>
						</el-select>
					</el-form-item>
				</el-col>
			</el-row>
			<el-row>
				<el-col :span="12">
					<el-form-item label="收支金额" label-width="90px" label-position="left" prop="money">
						<el-input
							v-model="formData.money"
							:disabled="true"
							@input="v => (formData.money = v.replace(/[^\d.]/g, ''))"
						></el-input>
					</el-form-item>
				</el-col>
				<el-col :span="12">
					<el-form-item label="收支时间" label-width="90px" label-position="left" prop="inExTime">
						<el-date-picker
							v-model="formData.inExTime"
							type="datetime"
							:disabled="allDisabled"
							:value-format="dateformat"
							:format="dateformat"
						></el-date-picker>
					</el-form-item>
				</el-col>
			</el-row>
			<el-row>
				<el-col :span="5">
					<el-form-item label="类别" label-width="90px" label-position="left">
						<el-select
							v-model="formData.incExpType"
							value-key="value"
							:disabled="allDisabled"
							placeholder="收入类别"
							clearable
							@change="inExTypeChange"
						>
							<el-option :value="1" label="收入"></el-option>
							<el-option :value="2" label="支出"></el-option>
						</el-select>
					</el-form-item>
				</el-col>
				<el-col :span="12">
					<el-form-item
						:label="(formData.incExpType === 1 ? '收入' : '支出') + '类型'"
						label-width="90px"
						label-position="left"
						prop="typeId"
					>
						<el-select
							ref="type"
							v-model="formData.typeId"
							value-key="value"
							:disabled="
								allDisabled || formData.incExpType === undefined || formData.incExpType === ''
							"
							clearable
							:remote-method="getTypes"
							filterable
							remote
						>
							<el-option
								v-for="item in tpSelectList"
								:key="item.value"
								:label="item.label"
								:value="item.value"
							></el-option>
						</el-select>
					</el-form-item>
				</el-col>
			</el-row>
			<el-row>
				<el-col :span="22">
					<el-form-item label="附件" label-width="90px" label-position="left" prop="detail">
						<es-upload v-bind="uploadAttrs" :limit="1" :file-list="uploadFileList"></es-upload>
					</el-form-item>
				</el-col>
			</el-row>
		</el-form>
		<el-form>
			<el-row>
				<el-col :span="24">
					<el-form-item label="收支明细" label-width="90px" label-position="right" prop="detail">
						<es-data-table
							ref="detailTable"
							:form="true"
							:thead="detailThead"
							:toolbar="detailToolbar"
							:page="detailPage"
							:data="detailDataList"
							:option-data="optionData"
							collapse
							height="auto"
							:border="true"
							checkbox
							@btnClick="btnClick"
							@edit="detailEdit"
							@selection-change="detailSelectionChange"
						></es-data-table>
					</el-form-item>
				</el-col>
			</el-row>
		</el-form>
		<el-row><br /></el-row>
		<el-row>
			<el-col :span="3" style="float: right">
				<el-button type="reset" @click="infoPageClose(false)">取消</el-button>
			</el-col>
			<el-col :span="3" style="float: right">
				<el-button v-show="!allDisabled" type="primary" @click="handleFormSubmit">保存</el-button>
			</el-col>
		</el-row>
	</div>
</template>
<script>
import api from '@/http/society/societyIncomeExpendRecord/api';

export default {
	name: 'InfoPage',
	props: {
		baseData: {
			type: Object
		},
		infoPageMode: {
			type: String
		}
	},
	data() {
		return {
			allDisabled: false,
			detailDeleteBuffer: [],
			detailSelected: [],
			editDisabled: false,
			formData: {},
			memberList: [],
			pageMode: 'allOn',
			tpSelectList: [],
			sociSelectList: [],
			uploadFileList: [],
			filUploadApi: api.mecpfileUploadOnlyOne,
			dateformat: 'yyyy-MM-dd HH:mm:ss',
			rules: {
				societyId: { required: true, message: '请选择社团', trigger: 'blur' },
				money: { required: true, message: '请输入收入金额', trigger: 'blur' },
				typeId: { required: true, message: '请选择收入类型', trigger: 'blur' },
				inExTime: { required: true, message: '请选择收入时间', trigger: 'blur' }
			},
			detailDataList: [],
			detailTableUrl: api.societyIncomeExpendRecordDetailList,
			detailPage: {
				pageSize: 10,
				totalCount: 0
			}
		};
	},
	computed: {
		uploadAttrs() {
			return {
				code: 'society_incomeExpend_record_attach',
				ownId: this.formData.id,
				preview: true,
				download: true,
				operate: true
			};
		},
		detailToolbar() {
			return [
				{
					type: 'button',
					contents: [
						{
							text: '新增',
							type: 'primary',
							code: 'toolbar'
						},
						{
							text: '删除',
							type: 'danger',
							code: 'toolbar'
						},
						{
							text: '提交更改',
							disabled: this.pageMode !== '编辑',
							type: 'success',
							code: 'toolbar'
						}
					]
				}
			];
		},
		detailParam() {
			return { rootId: this.formData.id, isLeaf: 1, asc: false, orderBy: 'in_ex_time' };
		},
		detailThead() {
			return [
				{
					title: '缴费人',
					align: 'left',
					field: 'member',
					type: 'select',
					valueKey: 'value',
					labelKey: 'label',
					filterable: true,
					clearable: true,
					required: true,
					showOverflowTooltip: true
				},
				{
					title: '金额',
					align: 'left',
					field: 'money',
					type: 'text',
					maxlength: '8',
					'show-word-limit': true,
					showOverflowTooltip: true,
					required: true
				},
				{
					title: '日期',
					align: 'left',
					field: 'inExTime',
					type: 'datetime',
					showOverflowTooltip: true,
					required: true
				},
				{
					title: '备注',
					align: 'left',
					field: 'remark',
					type: 'text',
					showOverflowTooltip: true
				}
			];
		},
		optionData() {
			return {
				member: this.memberList
			};
		}
	},
	watch: {
		detailDataList: {
			handler(newL) {
				let newMoney = 0;
				newL.forEach(obj => {
					if (obj.money != null && obj.money !== '') {
						let temp = parseFloat(obj.money);
						if (!isNaN(temp)) {
							newMoney += temp;
						}
					}
				});
				this.formData.money = newMoney;
			},
			deep: true
		}
	},
	mounted() {
		this.formData = { ...this.baseData };
		this.pageMode = this.infoPageMode;
		switch (this.pageMode) {
			case 'allOn':
			case '新增':
				this.allDisabled = false;
				break;
			case '编辑':
				this.editDisabled = true;
				this.allDisabled = false;
				break;
			case '查看':
				this.allDisabled = true;
		}

		this.getSocieties();
		if (this.pageMode !== '新增') {
			this.getTypes();
			this.fileList();
			this.detailReload();
			this.getMembers();
		}
	},
	methods: {
		detailEdit(data, tableData) {
			data.data.edited = 1;
		},

		// 详情列表多选回调
		detailSelectionChange(data) {
			this.detailSelected = data;
		},

		btnClick(res) {
			let btn = res.handle.text;

			if (this.formData.societyId != null) {
				switch (btn) {
					case '新增':
						this.detailDataList.unshift({ edited: 1 });
						break;
					case '删除':
						this.detailSelectedToDeleteBuffer();
						break;
					case '提交更改':
						this.detailSubmit();
						break;
				}
			} else this.$message.warning('请先选择社团');
		},
		handleFormSubmit() {
			this.$refs.form.validate(valid => {
				if (valid) {
					let submitData = { ...this.formData };
					//处理数据
					submitData.status = submitData.status === true ? 1 : 0;
					if (typeof submitData.societyId == 'object') {
						submitData.societyId = submitData.societyId.value;
					}
					this.$delete(submitData, 'societyName');

					if (typeof submitData.typeId == 'object') {
						submitData.typeId = submitData.typeId.value;
					}
					this.$delete(submitData, 'typeName');

					submitData.details = this.getDetailSubmitData();

					const jsonString = JSON.stringify(submitData);

					this.$request({
						url: api.societyIncomeExpendRecordSaveOrWithDetail,
						data: jsonString,
						method: 'POST',
						headers: { 'content-type': 'application/json;charset=UTF-8' },
						format: false
					}).then(res => {
						if (res.rCode === 0) {
							this.$message.success(res.msg);
							this.detailDeleteBuffer = [];
						} else {
							this.$message.error(res.msg);
						}
						this.detailReload();
					});
				}
			});
		},
		infoPageClose(reload) {
			this.pageMode = 'allOn';
			this.infoDisabled = false;
			this.$emit('activelyClose', reload);
		},
		closeMapDialog() {
			this.showMapIntLo = false;
		},
		societyChange() {
			this.detailDataList = [];
			this.getMembers();
		},
		inExTypeChange(e) {
			this.$set(this.formData, 'typeId', null);
			this.$set(this.formData, 'inExType', e);
			this.getTypes();
		},
		getTypes(res) {
			console.log(res, this.formData.inExType, 'res,this.formData.inExType');
			this.$request({
				url: api.societyIncomeExpendTypeSelectData,
				params: { typeName: res, type: this.formData.inExType },
				method: 'GET'
			}).then(res => {
				if (res.success) {
					this.tpSelectList = [...res.results.records];
				} else {
					this.$message.error(res.msg);
				}
			});
		},
		getSocieties(res) {
			this.$request({
				url: api.societyList,
				params: { label: res, pageNum: 1, pageSize: 10 },
				method: 'GET'
			}).then(res => {
				if (res.results != null) {
					if (res.success) {
						this.sociSelectList = [...res.results.records];
					} else {
						this.$message.error(res.msg);
					}
				}
			});
		},
		getMembers(res) {
			let societyId = '';
			if (typeof this.formData.societyId == 'object') societyId = this.formData.societyId.value;
			else societyId = this.formData.societyId;

			this.$request({
				url: api.societyStudentMappingMemberSelectList,
				params: { societyId: societyId, memberName: res },
				method: 'GET'
			}).then(result => {
				if (result.success) {
					this.memberList = result.results;
				} else {
					this.$message.error(result.msg);
				}
			});
		},
		fileList() {
			this.$request({
				url: '/ybzy/mecpfileManagement/front/getAdjunctFileInfos',
				params: { code: 'society_incomeExpend_record_attach', ownId: this.formData.id },
				method: 'GET'
			}).then(res => {
				this.uploadFileList = res.results;
			});
		},
		// 重新加载收支详情
		detailReload() {
			this.$request({
				url: this.detailTableUrl,
				params: this.detailParam,
				method: 'GET'
			}).then(res => {
				if (res.success) {
					this.detailDataList = res.results.records;
					//处理返回数据
					this.detailDataList.forEach(e => {
						e.edited = 0;
						e.member = { value: e.memberId, label: e.memberName };
					});
				} else {
					this.$message.error(res.msg);
				}
			});
		},
		// 处理提交更改按钮
		async detailSubmit() {
			let submitList = this.getDetailSubmitData();

			if (submitList.length > 0) {
				//处理请求数据
				let params = { rootId: this.formData.id };
				let data = JSON.stringify(submitList);

				this.$request({
					url: api.societyIncomeExpendRecordDetailUpdate,
					headers: { 'content-type': 'application/json' },
					params: params,
					data: data,
					method: 'POST',
					format: false,
					mix: true
				}).then(res => {
					if (res.success) {
						this.$message.success('明细已保存');
						this.detailDeleteBuffer = [];
					} else {
						this.$message.error(res.msg);
					}
				});
			}
		},
		// 生成收支详情提交数据
		getDetailSubmitData() {
			let submitList = [];
			// 遍历detailDataList
			this.detailDataList.forEach(e => {
				let temp = {};
				if (e.edited === 1) {
					temp.id = e.id;
					temp.inExTime = e.inExTime;
					temp.remark = e.remark;
					temp.money = e.money;
					if (typeof e.member == 'object') {
						temp.memberId = e.member.value;
					} else {
						temp.memberId = e.member;
					}
					submitList.push(temp);
				}
			});
			// 遍历detailDeleteBuffer
			submitList.push(...this.detailDeleteBuffer);
			return submitList;
		},
		// 被选中的详情移动至待删除列表
		detailSelectedToDeleteBuffer() {
			this.detailSelected.forEach(selected => {
				this.detailDataList.splice(this.detailDataList.indexOf(selected), 1);
				if (selected.id && selected.id !== '') {
					selected.deleted = -1;
					this.detailDeleteBuffer.push(selected);
				}
			});
		}
	}
};
</script>
<style scoped>
.sketch_content {
	overflow: auto;
	height: 100%;
	border-top: 1px solid #eff1f4;
	border-bottom: 1px solid #eff1f4;
	padding: 0 30px 11px 27px;
}
.resumeTable td {
	height: 50px;
	font-weight: bold;
}
::v-deep .es-data-table-content {
	padding: 1px !important;
	width: 111%;
	position: relative;
	right: 90px;
}
</style>
