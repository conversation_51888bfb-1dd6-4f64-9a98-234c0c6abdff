<template>
	<div :id="id" style="width: 100%; height: 100%"></div>
</template>
<script>
export default {
	props: {
		id: {
			type: String,
			required: true
		},
		// title: {
		// 	type: String,
		// 	required: true
		// },
		// color: {
		// 	type: Array,
		// 	required: true
		// },
		seriesData: {
			type: Array,
			required: true
		}
	},
	data() {
		return {
			chart: null
		};
	},
	watch: {
		seriesData() {
			this.draw();
		}
	},
	mounted() {
		this.draw();
	},

	methods: {
		draw() {
			let option = {
				grid: {
					top: '15%',
					bottom: '10%' //也可设置left和right设置距离来控制图表的大小
				},
				legend: { top: '2%', right: '20', itemWidth: 16 },
				tooltip: {},
				xAxis: {
					type: 'category',
					data: this.seriesData[0] || [],
					axisLine: {
						show: false //隐藏X轴轴线
					},
					axisTick: {
						show: false //隐藏X轴刻度
					},
					axisLabel: {
						show: true,
						color: 'balck', //X轴文字颜色
						fontSize: 10,
						interval: 1
					}
				},
				yAxis: {
					name: '单位：元',
					nameTextStyle: {
						padding: [0, 20, 10, 0]
					}
				},
				dataZoom: [
					{
						type: 'slider',
						realtime: true,
						start: 0,
						end: 40, // 初始展示40%
						height: 4,
						fillerColor: 'rgba(17, 100, 210, 0.42)', // 滚动条颜色
						borderColor: 'rgba(17, 100, 210, 0.12)',
						handleSize: 0, // 两边手柄尺寸
						showDetail: false, // 拖拽时是否展示滚动条两侧的文字
						top: '96%'

						// zoomLock:true, // 是否只平移不缩放
						// moveOnMouseMove:true, //鼠标移动能触发数据窗口平移
						// zoomOnMouseWheel :true, //鼠标移动能触发数据窗口缩放
					},
					{
						type: 'inside', // 支持内部鼠标滚动平移
						start: 0,
						end: 40,
						zoomOnMouseWheel: false, // 关闭滚轮缩放
						moveOnMouseWheel: true, // 开启滚轮平移
						moveOnMouseMove: true // 鼠标移动能触发数据窗口平移
					}
				],
				// Declare several bar series, each will be mapped
				// to a column of dataset.source by default.
				series: [
					{
						type: 'bar',
						barWidth: '20',
						name: '收入',
						barGap: 0,
						data: this.seriesData[1] || [],
						itemStyle: {
							borderRadius: [50, 50, 0, 0],
							color: new this.$echarts.graphic.LinearGradient(0, 0, 0, 1, [
								{
									offset: 0,
									color: '#0D7DE9'
								},
								{
									offset: 1,
									color: '#E5F1FD'
								}
							])
						}
					},
					{
						type: 'bar',
						barWidth: '20',
						name: '支出',
						data: this.seriesData[2] || [],
						itemStyle: {
							borderRadius: [50, 50, 0, 0],
							color: new this.$echarts.graphic.LinearGradient(0, 0, 0, 1, [
								{
									offset: 0,
									color: '#ED666F'
								},
								{
									offset: 1,
									color: '#FCE3E3'
								}
							])
						}
					}
				]
			};
			this.chart && this.chart.dispose();
			this.chart = this.$echarts.init(document.getElementById(this.id));
			// console.log('>>>this.chart', this.$echarts);
			this.chart.setOption(option);
			window.addEventListener('resize', () => {
				this.chart && this.chart.resize();
			});
		}
	}
};
</script>
