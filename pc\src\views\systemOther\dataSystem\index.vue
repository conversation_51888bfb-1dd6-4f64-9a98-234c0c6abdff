<template>
	<div class="main">
		<div class="banner"></div>
		<!--  头部  -->
		<div class="main-header">
			<es-select
				v-model="value"
				class="select"
				placeholder="请选择"
				:data="options"
				value-type="object"
				size="mini"
				@change="handleChange"
			></es-select>
			<es-select
				v-model="value2"
				class="select"
				placeholder="请选择"
				:data="options2"
				value-type="object"
				size="mini"
				@change="handleChange"
			></es-select>
		</div>
		<!--  卡片tab切换  -->
		<div class="card-box">
			<mini-card v-for="(item, index) in card" :key="index" :card-data="item" />
		</div>
		<!--  内容区域  -->
		<div class="main-center">
			<div class="box1">
				<div class="title">
					<img class="title-img" src="@ast/images/systemOther/data-icon5.png" alt="" />
					<span>文件类型分析</span>
					<i class="el-icon-refresh icon"></i>
				</div>
				<div id="myEchart1" class="chart-con"></div>
			</div>
			<div class="box2">
				<div class="title">
					<img class="title-img" src="@ast/images/systemOther/data-icon5.png" alt="" />
					<span>文件类别分析</span>
					<i class="el-icon-refresh icon"></i>
				</div>
				<div id="myEchart2" class="chart-con"></div>
			</div>
			<div class="box3">
				<div class="title">
					<img class="title-img" src="@ast/images/systemOther/data-icon5.png" alt="" />
					<span>部门资料数量趋势</span>
					<i class="el-icon-refresh icon"></i>
				</div>
				<div id="myEchart3" class="chart-con"></div>
			</div>
			<div class="box4">
				<div class="title">
					<img class="title-img" src="@ast/images/systemOther/data-icon5.png" alt="" />
					<span>资料存储趋势</span>
					<i class="el-icon-refresh icon"></i>
				</div>
				<div id="myEchart4" class="chart-con"></div>
			</div>
			<div class="box5">
				<div class="title">
					<img class="title-img" src="@ast/images/systemOther/data-icon5.png" alt="" />
					<span>下载次数排行榜</span>
					<div class="top-badge">TOP5</div>
					<i class="el-icon-refresh icon"></i>
				</div>
				<div class="list">
					<div v-for="(item, index) in listNext" :key="index" class="list-item">
						<div class="floor1">
							<span class="order">NO.{{ index + 1 }}</span>
							<span class="floor1-text">{{ item.name }}</span>
							<span class="floor1-text2">{{ item.count }} 【75{{ item.rate }}%】</span>
						</div>
						<el-progress
							class="floor2"
							stroke-width="5"
							:percentage="item.rate"
							:show-text="false"
						></el-progress>
					</div>
				</div>
			</div>
			<div class="box6">
				<div class="title">
					<img class="title-img" src="@ast/images/systemOther/data-icon5.png" alt="" />
					<span>文件大小排行榜</span>
					<div class="top-badge">TOP5</div>
					<i class="el-icon-refresh icon"></i>
				</div>
				<div class="list">
					<div v-for="(item, index) in listSize" :key="index" class="list-item">
						<div class="floor1">
							<span class="order">NO.{{ index + 1 }}</span>
							<span class="floor1-text">{{ item.name }}</span>
							<span class="floor1-text2">{{ item.count }}KB</span>
						</div>
						<el-progress
							class="floor2"
							stroke-width="5"
							:percentage="item.rate"
							:show-text="false"
						></el-progress>
					</div>
				</div>
			</div>
		</div>
	</div>
</template>
<script>
import MiniCard from '@/components/show/mini-card.vue';
export default {
	name: 'AttendanceManagementPersonnel',
	components: { MiniCard },
	data() {
		return {
			options: [
				{
					value: '1',
					label: '本人及下属'
				},
				{
					value: '2',
					label: '本部门'
				}
			],
			value: {
				value: '1'
			},

			options2: [
				{
					value: '1',
					label: '本月'
				},
				{
					value: '2',
					label: '本周'
				},
				{
					value: '3',
					label: '今日'
				}
			],
			value2: {
				value: '1'
			},

			card: [
				{
					img: require('@/assets/images/systemOther/data-icon1.png'),
					title: '资料文件总数',
					unit: '个',
					width: '24.3%',
					num: '0'
				},
				{
					img: require('@/assets/images/systemOther/data-icon2.png'),
					title: '资料文件分类',
					unit: '个',
					width: '24.3%',
					num: '0'
				},
				{
					img: require('@/assets/images/systemOther/data-icon3.png'),
					title: '系统使用人数',
					unit: '人',
					width: '24.3%',
					num: '0'
				},
				{
					img: require('@/assets/images/systemOther/data-icon4.png'),
					title: '资料存储空间使用占比',
					unit: 'GB',
					width: '24.3%',
					num: '0'
				}
			],

			listNext: [
				{
					name: '全国职业教育智慧大脑院校中台高职学校数据对接标准',
					count: 236,
					rate: 75
				},
				{
					name: '职业院校数字校园标杆校采集指标',
					count: 151,
					rate: 65
				},
				{
					name: '职业院校数字校园标杆校采集指标',
					count: 135,
					rate: 55
				},
				{
					name: '全国职业教育智慧大脑院校中台接口加密示例',
					count: 122,
					rate: 51
				},
				{
					name: '宜宾职业技术学院教职工请假制度',
					count: 79,
					rate: 46
				}
			],
			listSize: [
				{
					name: '全国职业教育智慧大脑院校中台高职学校数据对接标准',
					count: 2173,
					rate: 75
				},
				{
					name: '职业院校数字校园标杆校采集指标',
					count: 1407,
					rate: 65
				},
				{
					name: '职业院校数字校园标杆校采集指标',
					count: 444,
					rate: 55
				},
				{
					name: '全国职业教育智慧大脑院校中台接口加密示例',
					count: 130,
					rate: 51
				},
				{
					name: '宜宾职业技术学院教职工请假制度',
					count: 116,
					rate: 46
				}
			],
			myChart: []
		};
	},
	mounted() {
		window.addEventListener('resize', this.onResize);
		this.toolChart1('myEchart1', 0);
		this.toolChart2('myEchart2', 1);
		this.toolChart3('myEchart3', 2);
		this.toolChart4('myEchart4', 3);
	},
	beforeDestroy() {
		window.removeEventListener('resize', this.onResize);
		this.myChart.forEach(item => {
			if (item) {
				item.dispose();
			}
		});
	},
	methods: {
		onResize() {
			this.myChart.forEach(item => {
				if (item) {
					item.resize();
				}
			});
		},
		toolChart1(dom, i) {
			let chartDom = document.getElementById(dom);
			if (this.myChart[i]) {
				this.myChart[i].clear();
			} else {
				this.myChart[i] = this.$echarts.init(chartDom);
			}
			const option = {
				tooltip: {
					trigger: 'item',
					formatter: '{b}: {c}个 ({d}%)'
				},
				legend: {
					orient: 'vertical', // 设置图例为竖直方向
					icon: 'circle',
					y: 'bottom', // 设置图例在y轴上的位置为底部
					right: 15 // 设置图例在x轴上的位置为右边
				},

				series: [
					{
						name: '文件详情',
						type: 'pie',
						// radius: [50, 250],
						center: ['40%', '50%'],
						roseType: 'area',
						// itemStyle: {
						// 	borderRadius: 8
						// },
						data: [
							{ value: 40, name: '文档' },
							{ value: 38, name: '图片' },
							{ value: 32, name: '阴影' },
							{ value: 30, name: '其他' }
						]
					}
				]
			};

			option && this.myChart[i].setOption(option);
		},
		toolChart2(dom, i) {
			let chartDom = document.getElementById(dom);
			if (this.myChart[i]) {
				this.myChart[i].clear();
			} else {
				this.myChart[i] = this.$echarts.init(chartDom);
			}
			var scale = 1;
			var weekData = [
				{ text: '图片', max: 100 },
				{ text: '其他', max: 100 },
				{ text: '软件', max: 100 },
				{ text: '音频', max: 100 },
				{ text: '视频', max: 100 },
				{ text: '文档', max: 100 }
			];
			var student = [60, 73, 85, 40, 50, 40, 30];
			const option = {
				tooltip: {
					trigger: 'item'
				},
				radar: [
					{
						indicator: weekData,
						center: ['50%', '55%'],
						radius: '60%',
						name: {
							textStyle: {
								color: '#6c6c6c'
								// fontSize: 18 * scale,
								// fontWeight: 'bold'
							}
						},
						splitLine: {
							lineStyle: {
								color: '#fef1ce',
								opacity: 0.5,
								width: 1
							}
						},
						// splitArea: {
						// 	show: true,
						// 	areaStyle: {
						// 		color: '#fef1ce',
						// 		opacity: 0.2
						// 	}
						// },
						axisLine: {
							show: true,
							lineStyle: {
								color: '#f4c9aa',
								type: 'dashed'
							}
						}
					}
				],
				series: [
					{
						type: 'radar',
						tooltip: {
							trigger: 'item'
						},
						data: [
							{
								value: student,
								name: '文件类别'
							}
						],
						symbolSize: 3 * scale,
						// 空圆圈
						symbol: 'circle',

						itemStyle: {
							normal: {
								borderColor: '#c82a23',
								borderWidth: 1 * scale
							}
						},
						lineStyle: {
							normal: {
								color: '#c82a23',
								width: 1 * scale
							}
						},
						areaStyle: {
							normal: {
								color: '#c82a23',
								opacity: 0.5
							}
						}
					}
				]
			};

			option && this.myChart[i].setOption(option);
		},
		toolChart3(dom, i) {
			let chartDom = document.getElementById(dom);
			if (this.myChart[i]) {
				this.myChart[i].clear();
			} else {
				this.myChart[i] = this.$echarts.init(chartDom);
			}
			const xData = ['党政办', '组织部', '宣统部', '学生部', '招就处'];
			const yData = [43, 20, 24, 5, 8];

			const styleData = { title: '就业率年度分析', color: ['#0076e8', '#ebf3fb'] };

			const option = {
				tooltip: {
					trigger: 'axis'
					// axisPointer: {
					// 	type: 'shadow'
					// }
				},
				// title: {
				// 	text: styleData.title,
				// 	top: '5.4%',
				// 	left: '3.47%',
				// 	textStyle: {
				// 		fontSize: 16,
				// 		color: '#21252B'
				// 	}
				// },

				grid: {
					left: '9.17%',
					top: '10%',
					bottom: '8%',
					right: '4.95%'
				},
				xAxis: {
					data: xData,
					axisTick: {
						show: false
					},
					axisLine: {
						lineStyle: {
							color: 'rgba(255, 129, 109, 0.1)',
							width: 1 //这里是为了突出显示加上的
						}
					},
					axisLabel: {
						//
						textStyle: {
							color: '#999'
							// fontSize: 14
						}
					}
				},
				yAxis: [
					{
						splitNumber: 2,
						axisTick: {
							show: false
						},
						axisLine: {
							lineStyle: {
								color: 'rgba(255, 129, 109, 0.1)',
								width: 1 //这里是为了突出显示加上的
							}
						},
						axisLabel: {
							textStyle: {
								color: '#8997A5',
								fontSize: 14
							},
							formatter: function (value) {
								return value;
							}
						},
						splitArea: {
							areaStyle: {
								color: 'rgba(255,255,255,.5)'
							}
						},
						splitLine: {
							//刻度线
							show: true,
							lineStyle: {
								color: '#B2C3DA',
								type: [3, 3],
								dashOffset: 2
							}
						}
					}
				],
				series: [
					{
						name: '数量',
						type: 'pictorialBar',
						barCategoryGap: '0%', //柱间距离
						symbol: 'path://M0,10 L10,10 C5.5,10 5.5,5 5,0 C4.5,5 4.5,10 0,10 z',
						// label: {
						// 	show: true,
						// 	position: 'top',
						// 	distance: 2,
						// 	color: '#FFFFFF',
						// 	fontSize: 12,
						// 	borderColor: '#09354F',
						// 	borderWidth: 1,
						// 	borderRadius: 4,
						// 	padding: [0, 10],
						// 	backgroundColor: 'rgba(9,53,79,0.55)',
						// 	height: 20,
						// 	lineHeight: 18,
						// 	formatter: function (params) {
						// 		return params.value + '%';
						// 	}
						// },
						itemStyle: {
							normal: {
								color: {
									type: 'linear',
									x: 0,
									y: 0,
									x2: 0,
									y2: 1,
									colorStops: [
										{
											offset: 0,
											color: styleData.color[0] //  0%  处的颜色
										},
										{
											offset: 1,
											color: styleData.color[1] //  100%  处的颜色
										}
									],
									global: false //  缺省为  false
								}
							},
							emphasis: {
								opacity: 1
							}
						},
						data: yData,
						z: 10
					}
				]
			};

			option && this.myChart[i].setOption(option);
		},
		toolChart4(dom, i) {
			let chartDom = document.getElementById(dom);
			if (this.myChart[i]) {
				this.myChart[i].clear();
			} else {
				this.myChart[i] = this.$echarts.init(chartDom);
			}
			const xData = ['图片', '文档', '视频', '音频', '软件', '其他'];
			const yData = [298, 263, 500, 300, 360, 180];

			const styleData = { title: '就业率年度分析', color: ['#0076e8', '#ebf3fb'] };

			const option = {
				tooltip: {
					trigger: 'axis'
					// axisPointer: {
					// 	type: 'shadow'
					// }
				},
				// title: {
				// 	text: styleData.title,
				// 	top: '5.4%',
				// 	left: '3.47%',
				// 	textStyle: {
				// 		fontSize: 16,
				// 		color: '#21252B'
				// 	}
				// },

				grid: {
					left: '9.17%',
					top: '10%',
					bottom: '8%',
					right: '4.95%'
				},
				xAxis: {
					data: xData,
					axisTick: {
						show: false
					},
					axisLine: {
						lineStyle: {
							color: 'rgba(255, 129, 109, 0.1)',
							width: 1 //这里是为了突出显示加上的
						}
					},
					axisLabel: {
						//
						textStyle: {
							color: '#999'
							// fontSize: 14
						}
					}
				},
				yAxis: [
					{
						splitNumber: 2,
						axisTick: {
							show: false
						},
						axisLine: {
							lineStyle: {
								color: 'rgba(255, 129, 109, 0.1)',
								width: 1 //这里是为了突出显示加上的
							}
						},
						axisLabel: {
							textStyle: {
								color: '#8997A5',
								fontSize: 14
							},
							formatter: function (value) {
								return value;
							}
						},
						splitArea: {
							areaStyle: {
								color: 'rgba(255,255,255,.5)'
							}
						},
						splitLine: {
							//刻度线
							show: true,
							lineStyle: {
								color: '#B2C3DA',
								type: [3, 3],
								dashOffset: 2
							}
						}
					}
				],
				series: [
					{
						name: '大小',
						// 柱状图
						type: 'bar',
						// 柱子宽度
						barWidth: 20,
						// barCategoryGap: '0%', //柱间距离
						symbol: 'path://M0,10 L10,10 C5.5,10 5.5,5 5,0 C4.5,5 4.5,10 0,10 z',
						itemStyle: {
							normal: {
								color: {
									type: 'linear',
									x: 0,
									y: 0,
									x2: 0,
									y2: 1,
									colorStops: [
										{
											offset: 0,
											color: styleData.color[0] //  0%  处的颜色
										},
										{
											offset: 1,
											color: styleData.color[0] //  100%  处的颜色
										}
									],
									global: false //  缺省为  false
								}
							},
							emphasis: {
								opacity: 1
							}
						},
						data: yData,
						z: 10
					}
				]
			};

			option && this.myChart[i].setOption(option);
		},
		/**年月切换下拉选择*/
		handleChange(val) {
			console.log(val);
		}
	}
};
</script>

<style lang="scss" scoped>
@import '@ast/style/public.scss';
.main {
	height: 100%;
	width: 100%;
	background: #f0f2f5;
	padding: 12px;
	overflow-y: scroll;
	.banner {
		width: 100%;
		border-radius: 6px;
		height: 110px;
		background: url(~@ast/images/systemOther/data-banner.png);
		margin-bottom: 12px;
	}
	/**头部*/
	&-header {
		::v-deep .select {
			border: none;
			margin-right: 8px;
			.el-input__inner {
				width: 160px;
				border: none;
			}
		}
	}
	/**中间部分*/
	&-center {
		display: flex;
		flex-wrap: wrap;
		justify-content: space-between;
		.box1,
		.box2,
		.box3,
		.box4,
		.box5,
		.box6 {
			flex-basis: 32.8%;
			background: #fff;
			padding: 6px 0;
			border-radius: 6px;
			font-size: 16px;
			margin-bottom: 12px;
			height: 280px;
			.chart-con {
				width: 100%;
				height: calc(100% - 36px);
			}
			.list {
				// width: 100%;
				height: 100%;
				padding: 20px 15px;
				.list-item {
					// width: 100%;
					display: flex;
					flex-direction: column;
					font-size: 12px;
					margin-bottom: 16px;
					color: #949faf;
					.floor1 {
						margin-bottom: 5px;
						.order {
							font-size: 13px;
							font-weight: bold;
							margin-right: 5px;
							font-style: italic;
							display: inline-block;
							line-height: 1;
						}
						.floor1-text,
						.floor1-text2 {
							overflow: hidden;
							text-overflow: ellipsis;
							white-space: nowrap;
							display: inline-block;
							line-height: 1;
						}
						.floor1-text {
							max-width: 300px;
						}
						.floor1-text2 {
							float: right;
						}
					}
					&:nth-child(1) {
						.order {
							color: #0b7ce9;
						}
					}
					&:nth-child(2) {
						.order {
							color: #40a9ff;
						}
					}
					&:nth-child(3) {
						.order {
							color: #2dba4f;
						}
					}
				}
			}
		}
	}
	/**穿透修改卡片组件*/
	.card-box {
		@include flexBox(space-between);
		width: 100%;
		margin: 12px 0;
		background: #f0f2f5;
	}
}
.title {
	position: relative;
	display: flex;
	align-items: center;
	font-size: 16px;
	font-weight: 550;
	padding: 0 20px 6px;
	border-bottom: 1px solid rgba(241, 244, 247, 1);
	&-img {
		width: 22px;
		height: 22px;
		margin-right: 8px;
	}
	.top-badge {
		position: relative;
		display: inline-block;
		background-color: #fceee1;
		color: #ee9a55;
		font-weight: bold;
		padding: 0 5px;
		border-radius: 4px;
		font-size: 12px;
		font-style: italic;
		position: relative;
		margin-left: 8px;
		margin-top: 2px;
		line-height: 1.3;

		&::before {
			// 右边小三角形
			content: '';
			position: absolute;
			width: 0;
			height: 0;
			border-top: 5px solid transparent;
			border-bottom: 5px solid transparent;
			border-right: 7px solid #fceee1;
			top: 3px;
			left: -4.8px;

			z-index: 1;
		}
	}

	.icon {
		position: absolute;
		top: 3px;
		right: 10px;
		color: #707070;
	}
}
</style>
