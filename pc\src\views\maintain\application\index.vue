<template>
	<div class="content" style="height: 100%">
		<div style="width: 100%; height: 100%">
			<es-data-table
				v-if="true"
				ref="table"
				:key="tableCount"
				:row-style="tableRowClassName"
				:full="true"
				:fit="true"
				:thead="thead"
				:toolbar="toolbar"
				:page="page"
				:url="dataTableUrl"
				:param="dataTableParam"
				:border="true"
				:numbers="true"
				form
				@btnClick="btnClick"
			></es-data-table>
			<!-- 新增 -->
			<es-dialog
				v-if="showAddOrEditPage"
				:title="formTitle"
				:visible.sync="showAddOrEditPage"
				width="960px"
				height="710px"
				:showScale="false"
				:drag="false"
				append-to-body
			>
				<addOrEditPage
					:id="maintainId"
					:page-mode="infoPageMode"
					@closeAddOrEditPage="closeAddOrEditPage"
				></addOrEditPage>
			</es-dialog>
			<!-- 查看 -->
			<es-dialog
				v-if="showViewPage"
				title="报修单"
				:visible.sync="showViewPage"
				width="960px"
				height="730px"
				:showScale="false"
				:drag="false"
				append-to-body
			>
				<viewPage :id="maintainId"></viewPage>
			</es-dialog>
			<!-- 评价 -->
			<es-dialog
				v-if="showReviewPage"
				title="评价"
				:visible.sync="showReviewPage"
				width="670px"
				height="530px"
				:showScale="false"
				:drag="false"
				append-to-body
			>
				<reviewPage :report-id="maintainId" @closeReviewPage="closeReviewPage"></reviewPage>
			</es-dialog>
		</div>
	</div>
</template>

<script>
import api from '@/http/maintain/applicationApi';
import addOrEditPage from '@/views/maintain/application/components/add.vue';
import viewPage from '@/views/maintain/application/components/view.vue';
import reviewPage from '@/views/maintain/application/components/review.vue';

export default {
	components: { addOrEditPage, viewPage, reviewPage },
	data() {
		return {
			page: {
				pageSize: 20,
				totalCount: 0
			},
			selectRowData: [],
			selectRowIds: [],

			//弹出配置
			maintainId: null,
			formTitle: '新增',
			infoPageMode: 'add',
			showAddOrEditPage: false,
			showViewPage: false,
			showReviewPage: false,
			tableCount: 1,
			dialogType: '',
			dataTableUrl: api.reportList,
			dataTableParam: { orderBy: 'create_time', asc: false },
			validityOfDateDisable: false,
			thead: [],
			toolbar: [],
			editToolbar: [
				{
					type: 'button',
					contents: [
						{
							text: '新增',
							code: 'add',
							type: 'primary'
						}
					]
				},
				{
					type: 'search',
					reset: true,
					contents: [
						{
							type: 'text',
							name: 'repairCode',
							placeholder: '编号',
							clearable: true
						},
						{
							type: 'select',
							placeholder: '报修类别',
							name: 'repairType',
							event: 'multipled',
							sysCode: 'hq_repair_type',
							'label-key': 'shortName',
							'value-key': 'cciValue',
							clearable: true
						},
						{
							type: 'select',
							placeholder: '报修状态',
							name: 'reportStatus',
							event: 'multipled',
							sysCode: 'hq_report_status',
							'label-key': 'shortName',
							'value-key': 'cciValue',
							clearable: true
						}
					]
				}
			],
			listThead: [
				{
					title: '编号',
					align: 'left',
					field: 'repairCode',
					showOverflowTooltip: true
				},
				{
					title: '报修类别',
					align: 'center',
					field: 'repairTypeName',
					showOverflowTooltip: true
				},
				// {
				// 	title: '报修描述',
				// 	width: '260px',
				// 	align: 'center',
				// 	field: 'reportContent',
				// 	showOverflowTooltip: true
				// },
				{
					title: '报修地点',
					align: 'center',
					field: 'addressTypeName',
					showOverflowTooltip: true
				},
				{
					title: '详细地址',
					align: 'center',
					field: 'address',
					showOverflowTooltip: true
				},
				{
					title: '报修人',
					align: 'center',
					field: 'reportUser',
					showOverflowTooltip: true
				},
				{
					title: '联系方式',
					align: 'center',
					field: 'repairPhone',
					showOverflowTooltip: true
				},
				{
					title: '报修时间',
					align: 'center',
					field: 'createTime',
					showOverflowTooltip: true
				},
				{
					title: '报修状态',
					width: '150px',
					align: 'center',
					field: 'reportStatusName',
					showOverflowTooltip: true,
					render: (h, param) => {
						return h(
							'p',
							{ style: { fontWeight: 'bold', color: this.formatLink(param.row.reportStatus) } },
							param.row.reportStatusName
						);
					}
				}
			],
			btnJson: {
				title: '操作',
				type: 'handle',
				width: 150,
				template: '',
				events: [
					{
						code: 'view',
						text: '查看'
					},
					// {
					// 	code: 'edit',
					// 	text: '编辑',
					// 	rules: rows => {
					// 		return rows.reportStatus == 0;
					// 	}
					// },
					{
						code: 'review',
						text: '评价',
						rules: rows => {
							return rows.isReview == 1;
						}
					}
					// {
					// 	code: 'delete',
					// 	text: '删除',
					// 	rules: rows => {
					// 		return rows.reportStatus == 0;
					// 	}
					// }
				]
			}
		};
	},
	created() {
		//初始化查询待审核列表
		this.thead = this.getListThead(this.btnJson);
		this.toolbar = this.editToolbar;
	},
	methods: {
		/**
		 * 表格行高
		 */
		tableRowClassName() {
			return {
				height: '54px !important'
			};
		},
		// 操作按钮
		btnClick(res) {
			let code = res.handle.code;
			switch (code) {
				case 'add':
					this.openAddOrEditPage(null, code);
					break;
				case 'edit':
					this.openAddOrEditPage(res.row.id, code);
					break;
				case 'view':
					this.openViewPage(res.row.id);
					break;
				case 'review':
					this.openReviewPage(res.row.id);
					break;
				case 'delete':
					this.deleteRows([res.row.id]);
					break;
			}
		},
		//打开新增或编辑
		openAddOrEditPage(id, code) {
			if (code !== 'add') {
				this.maintainId = id;
				this.formTitle = '编辑';
			}
			this.infoPageMode = code;
			this.showAddOrEditPage = true;
			this.showReviewPage = false;
			this.showViewPage = false;
		},
		//关闭编辑页面，编辑成功刷新表格
		closeAddOrEditPage() {
			this.showAddOrEditPage = false;
			this.$refs.table.reload();
		},
		//打开查看
		openViewPage(id) {
			this.maintainId = id;
			this.showAddOrEditPage = false;
			this.showReviewPage = false;
			this.showViewPage = true;
		},
		//打开评价
		openReviewPage(id) {
			this.maintainId = id;
			this.showAddOrEditPage = false;
			this.showReviewPage = true;
			this.showViewPage = false;
		},
		closeReviewPage(code) {
			this.showReviewPage = false;
			if(code == 'success'){
				this.$refs.table.reload();
			}
		},
		deleteRows(ids) {
			this.$confirm('确定要删除选中的数据吗？', '删除', {
				confirmButtonText: '确定',
				cancelButtonText: '取消',
				type: 'warning'
			})
				.then(() => {
					this.$request({
						url: api.societyBaseInfoDeleteIds,
						data: { ids: ids.join(',') },
						method: 'POST'
					}).then(res => {
						if (res.rCode === 0) {
							this.$message.success('操作完成');
							this.$refs.table.reload();
						} else {
							this.$message.error(res.msg);
						}
					});
				})
				.catch(() => {});
		},
		getListThead(btnJson) {
			let tempThead = [...this.listThead];
			tempThead.push(btnJson);
			return tempThead;
		},
		//处理状态表格列回显颜色
		formatLink(status) {
			let color = 'red';
			switch (status) {
				case '0':
					color = '#F56C6C';
					break;
				case '1':
					color = '#E6A23C';
					break;
				case '2':
					color = '#1890ff';
					break;
				case '3':
					color = '#52c41a';
					break;
				case '4':
					color = '#909399';
					break;
				case '9':
					color = '#000000';
					break;
			}
			return color;
		}
	}
};
</script>
