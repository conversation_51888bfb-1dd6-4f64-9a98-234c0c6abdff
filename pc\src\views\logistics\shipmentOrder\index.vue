
<template>
	<div class="content">
		<es-data-table
			ref="table"
			:row-style="tableRowClassName"
			:full="true"
			:fit="true"
			:thead="thead"
			:toolbar="toolbar"
			:border="true"
			:page="pageOption"
			:url="dataTableUrl"
			:option-data="optionData"
			:numbers="true"
			:param="params"
			close
			form
			@btnClick="btnClick"
			@sort-change="sortChange"
			@submit="hadeSubmit"
		></es-data-table>
		<es-dialog
			:title="formTitle"
			:visible.sync="showForm"
			width="1400px"
			height="740px"
			:drag="false"
			:close-on-click-modal="false"
			:destroy-on-close="true"
		>
			<div v-if="showForm" style="margin: 10px">
				<el-form
					ref="form"
					:inline="true"
					label-position="right"
					label-width="110px"
					:model="formData"
					:rules="formRules"
				>
					<el-form-item label="领料库房" prop="storeroomId">
						<el-select
							v-model="formData.storeroomId"
							placeholder="请选择库房"
							@change="changeStoreroom"
						>
							<el-option
								v-for="item in storeroomOptions"
								:key="item.id"
								:label="item.name"
								:value="item.id"
							></el-option>
						</el-select>
					</el-form-item>
					<el-form-item label="档口/食堂" prop="stallOpening">
						<el-select v-model="formData.stallOpening" placeholder="请选择档口">
							<el-option
								v-for="item in stallOpeningList"
								:key="item.value"
								:label="item.label"
								:value="item.value"
							></el-option>
						</el-select>
					</el-form-item>
					<el-form-item label="餐次" prop="meals">
						<el-select v-model="formData.meals" placeholder="请选择餐次">
							<el-option
								v-for="item in mealsOptions"
								:key="item.cciValue"
								:label="item.shortName"
								:value="item.cciValue"
							></el-option>
						</el-select>
					</el-form-item>
					<el-form-item label="领料时间" prop="receiveTime">
						<el-date-picker
							v-model="formData.receiveTime"
							type="datetime"
							value-format="yyyy-MM-dd HH:mm:ss"
							placeholder="选择领料时间"
						></el-date-picker>
					</el-form-item>
					<el-form-item label="领料人" prop="receiveUser">
						<el-select v-model="formData.receiveUser" placeholder="请选择领料人">
							<el-option
								v-for="item in receiveUserList"
								:key="item.value"
								:label="item.label"
								:value="item.value"
							></el-option>
						</el-select>
					</el-form-item>
					<el-form-item label="质检员" prop="administratorId">
						<el-select v-model="formData.administratorId" placeholder="请选择质检员">
							<el-option
								v-for="item in administratorList"
								:key="item.value"
								:label="item.label"
								:value="item.value"
							></el-option>
						</el-select>
					</el-form-item>
					<el-form-item label="库房管理员" prop="storeKeeperId">
						<el-select v-model="formData.storeKeeperId" placeholder="请选择库房管理员" disabled>
							<el-option
								v-for="item in storeKeeperList"
								:key="item.value"
								:label="item.label"
								:value="item.value"
							></el-option>
						</el-select>
					</el-form-item>
				</el-form>
				<el-button
					size="small"
					style="background-color: #0076e9; border-color: #0076e9; color: #ffffff"
					@click="doShowMaterial"
				>
					添加原料
				</el-button>
				<div style="display: flex; flex-direction: column; align-items: center; height: 460px">
					<h3 style="margin-bottom: 10px">原料单</h3>
					<el-table :data="formData.details" border size="mini" style="width: 100%" height="100%">
						<el-table-column label="序号" type="index"></el-table-column>
						<el-table-column
							prop="materialCode"
							label="原料编号"
							min-width="100"
							:show-overflow-tooltip="true"
						></el-table-column>
						<el-table-column
							prop="materialName"
							label="原料名称"
							min-width="120"
							:show-overflow-tooltip="true"
						></el-table-column>
						<el-table-column prop="unit" label="单位"></el-table-column>
						<el-table-column prop="specification" label="规格"></el-table-column>
						<el-table-column prop="brand" label="供应商" min-width="140"></el-table-column>
						<el-table-column prop="residueNum" label="库存数量" width="100"></el-table-column>
						<el-table-column prop="num" label="领用数量" width="130">
							<template slot-scope="scope">
								<el-input-number
									v-model="scope.row.num"
									:controls="false"
									:min="1"
									:max="scope.row.residueNum - scope.row.lossNum"
									size="small"
									style="width: 100px"
								></el-input-number>
							</template>
						</el-table-column>
						<el-table-column prop="num" label="损耗数量" width="130">
							<template slot-scope="scope">
								<el-input-number
									v-model="scope.row.lossNum"
									:controls="false"
									:min="0"
									:max="scope.row.residueNum - scope.row.num"
									size="small"
									style="width: 100px"
								></el-input-number>
							</template>
						</el-table-column>
						<el-table-column prop="opt" label="操作">
							<template slot-scope="scope">
								<i
									class="el-icon-remove"
									style="color: red; cursor: pointer"
									@click="removeDetail(scope.$index, scope.row)"
								></i>
							</template>
						</el-table-column>
					</el-table>
				</div>
				<div
					style="
						display: flex;
						flex-direction: row;
						justify-content: center;
						padding-top: 15px;
						padding-right: 20px;
					"
				>
					<el-button
						size="small"
						style="background-color: #0076e9; border-color: #0076e9; color: #ffffff"
						@click="save('0')"
					>
						暂存
					</el-button>
					<el-button
						size="small"
						style="background-color: #0076e9; border-color: #0076e9; color: #ffffff"
						@click="save('1')"
					>
						提交
					</el-button>
					<el-button
						size="small"
						style="background-color: #f5f5f5; border-color: #d9d9d9; color: #000000"
						@click="showForm = false"
					>
						取消
					</el-button>
				</div>
			</div>
		</es-dialog>

		<es-dialog
			title="原料列表"
			:visible.sync="showMaterial"
			width="60%"
			:drag="false"
			:close-on-click-modal="false"
			:middle="true"
		>
			<es-data-table
				v-if="showMaterial"
				ref="materialTable"
				:row-style="tableRowClassName"
				checkbox
				:full="true"
				:fit="true"
				:thead="materialThead"
				:toolbar="materialToolbar"
				:border="true"
				:page="pageOption"
				:url="materialDataUrl"
				:option-data="materialOptionData"
				:numbers="true"
				:param="params"
				close
				form
			></es-data-table>
		</es-dialog>
		<es-dialog
			title="查看"
			:visible.sync="showViewForm"
			width="1150px"
			height="620px"
			:drag="false"
			:close-on-click-modal="false"
			:destroy-on-close="true"
		>
			<es-form
				ref="form"
				:model="formData"
				:contents="formItemList"
				height="500px"
				:genre="2"
				collapse
			/>
		</es-dialog>
		<es-dialog
			title="提交领料"
			:visible.sync="showSubmit"
			width="20%"
			:close-on-click-modal="false"
			:drag="false"
			:middle="true"
			height="140px"
		>
			<div>确定要提交该条数据吗</div>
			<div class="btn-box">
				<div class="btn theme" @click="ensureSubmit">确定</div>
				<div class="btn" @click="showSubmit = false">取消</div>
			</div>
		</es-dialog>
		<es-dialog
			title="删除"
			:visible.sync="showDelete"
			width="20%"
			:close-on-click-modal="false"
			:middle="true"
			:drag="false"
			height="140px"
		>
			<div>确定要删除该条数据吗</div>
			<div class="btn-box">
				<div class="btn theme" @click="deleteRow">确定</div>
				A
				<div class="btn" @click="showDelete = false">取消</div>
			</div>
		</es-dialog>
	</div>
</template>

<script>
import interfaceUrl from '@/http/logistics/shipmentOrder.js';
import storeroomApi from '@/http/logistics/hqStoeroom.js';
import materialApi from '@/http/logistics/material/api.js';
import materialCategoryApi from '@/http/logistics/materialcategory.js';
import stallOpeningApi from '@/http/logistics/stallOpening.js';
import operatePersonApi from '@/http/logistics/operatePerson.js';
import SnowflakeId from 'snowflake-id';
import { host } from '../../../../config/config';

//默认表单字段
const defFormData = {
	id: null,
	type: '1',
	status: null,
	storeroomId: null,
	receiveUser: null,
	administratorId: null,
	storeKeeperId: null,
	meals: null,
	stallOpening: null,
	receiveTime: null,
	details: []
};

export default {
	data() {
		return {
			ownId: '',
			dataTableUrl: interfaceUrl.listJson,
			showForm: false,
			showViewForm: false,
			showDelete: false,
			formTitle: '编辑',

			//当口列表
			stallOpeningList: [],

			//领料人，质检员、库房管理员
			receiveUserList: [],
			administratorList: [],
			storeKeeperList: [],

			editBtn: {
				type: 'submit',
				skin: 'lay-form-btns',
				contents: [
					{
						type: 'primary',
						text: '确定',
						event: 'confirm'
					},
					{
						type: 'reset',
						text: '取消',
						event: 'cancel'
					}
				]
			},
			cancelBtn: {
				type: 'submit',
				skin: 'lay-form-btns',
				contents: [
					{
						type: 'reset',
						text: '取消',
						event: 'cancel'
					}
				]
			},
			thead: [
				{
					title: '库房名称',
					align: 'left',
					field: 'storeroomName',
					showOverflowTooltip: true
				},
				{
					title: '领料单号',
					align: 'left',
					field: 'code',
					showOverflowTooltip: true
				},
				{
					title: '领料时间',
					align: 'left',
					field: 'receiveTime',
					showOverflowTooltip: true
				},
				{
					title: '领用人',
					align: 'left',
					field: 'receiveUserName',
					showOverflowTooltip: true
				},
				{
					title: '档口/食堂',
					align: 'left',
					field: 'stallOpeningName',
					showOverflowTooltip: true
				},
				{
					title: '餐次',
					align: 'left',
					field: 'mealsName',
					showOverflowTooltip: true
				},
				{
					title: '领用状态',
					align: 'center',
					field: 'status',
					type: 'switch',
					readonly: true
				},
				{
					title: '操作',
					type: 'handle',
					width: 180,
					template: '',
					events: [
						{
							code: 'view',
							text: '查看'
						},
						{
							code: 'edit',
							text: '编辑',
							rules: rows => {
								return rows.status === '0';
							}
						},
						{
							code: 'submit',
							text: '提交',
							rules: rows => {
								return rows.status === '0';
							}
						},
						{
							code: 'download',
							text: '导出'
						}
					]
				}
			],
			optionData: {
				// storeroomId: [],
				status: [
					{ name: '待提交', value: 0 },
					{ name: '已领取', value: 1 }
				]
			},
			pageOption: {
				layout: 'total, prev, pager, next, jumper',
				pageSize: 10,
				// hideOnSinglePage: true,
				current: 1,
				pageNum: 1
			},
			params: {
				asc: false,
				orderBy: 'create_time',
				type: '1'
			},
			formData: Object.assign({}, defFormData),
			formRules: {
				storeroomId: [{ required: true, message: '请选择库房', trigger: 'change' }],
				meals: [{ required: true, message: '请选择餐次', trigger: 'change' }],
				stallOpening: [{ required: true, message: '请填写档口/食堂', trigger: 'change' }],
				receiveUser: [{ required: true, message: '请选择领料人姓名', trigger: 'change' }],
				administratorId: [{ required: true, message: '请选择质检员', trigger: 'change' }],
				storeKeeperId: [{ required: true, message: '请选择库房管理员', trigger: 'change' }]
			},
			// supplierOptions: [], //供应商选择列表
			storeroomOptions: [], //库房选择列表
			mealsOptions: [], //餐次选择列表

			curMaterialIds: [], //当前选择的原料(用于重新选择时去重)
			showSubmit: false,
			showMaterial: false,
			materialDataUrl: materialApi.listJson,
			materialToolbar: [
				{
					type: 'button',
					contents: [
						{
							text: '确定',
							code: 'selected',
							type: 'primary',
							event: res => {
								this.selMaterial(res.ele.selected);
								this.showMaterial = false;
							}
						},
						{
							text: '取消',
							code: 'cancel',
							event: res => {
								this.showMaterial = false;
							}
						}
					]
				},
				{
					type: 'search',
					reset: true,
					contents: [
						{
							type: 'text',
							name: 'name',
							placeholder: '原料名称'
						},
						{
							type: 'select',
							name: 'categoryId',
							placeholder: '原料分类',
							valueType: 'string',
							default: true,
							parentCheck: true,
							tree: true,
							url: '/ybzy/hqbasematerialcategory/getTreeListOne',
							'value-key': 'id',
							'label-key': 'name'
						}
					]
				}
			],
			materialThead: [
				{
					title: '原料编号',
					field: 'code',
					align: 'left',
					showOverflowTooltip: true
				},
				{
					title: '原料名称',
					field: 'name',
					align: 'left',
					showOverflowTooltip: true
				},
				{
					title: '原料分类',
					field: 'categoryName',
					align: 'left',
					showOverflowTooltip: true
				},
				{
					title: '原料单位',
					field: 'unit',
					align: 'center'
				},
				{
					title: '供应商',
					field: 'brand',
					align: 'center',
					minWidth: 100,
					showOverflowTooltip: true
				},
				{
					title: '规格',
					field: 'specification',
					align: 'center'
				}
			],
			materialOptionData: {
				categoryId: []
			}
		};
	},
	computed: {
		toolbar() {
			return [
				{
					type: 'button',
					contents: [
						{
							text: '新增',
							code: 'add',
							type: 'primary'
						}
					]
				},
				{
					type: 'search',
					reset: true,
					contents: [
						{
							type: 'text',
							name: 'code',
							placeholder: '领料单号'
						},
						{
							type: 'select',
							name: 'userName',
							placeholder: '出库人',
							data: this.receiveUserList,
							'label-key': 'label',
							'value-key': 'value',
							clearable: true
						},
						{
							type: 'select',
							name: 'roomId',
							placeholder: '库房',
							data: this.storeroomOptions,
							'label-key': 'name',
							'value-key': 'id',
							clearable: true
						}
					]
				}
			];
		},
		formItemList() {
			return [
				{
					label: '领料库房',
					name: 'storeroomId',
					type: 'select',
					placeholder: '请选择库房',
					event: 'multipled',
					rules: {
						required: true,
						message: '请选择库房',
						trigger: 'change'
					},
					col: 4,
					data: this.storeroomOptions,
					'label-key': 'name',
					'value-key': 'id',
					readonly: true
				},
				{
					label: '档口/食堂',
					name: 'stallOpening',
					type: 'select',
					placeholder: '请选择档口/食堂',
					event: 'multipled',
					rules: {
						required: true,
						message: '请选择档口/食堂',
						trigger: 'change'
					},
					col: 4,
					data: this.stallOpeningList,
					'label-key': 'label',
					'value-key': 'value',
					readonly: true
				},
				{
					label: '餐次',
					name: 'meals',
					placeholder: '请选择餐次',
					col: 4,
					type: 'select',
					sysCode: 'hq_meals',
					readonly: true
				},
				{
					label: '领料时间',
					name: 'receiveTime',
					type: 'date',
					placeholder: '请选择领取时间',
					event: 'multipled',
					col: 4,
					readonly: true
				},
				{
					label: '领取人',
					name: 'receiveUser',
					type: 'select',
					placeholder: '请选择领取人',
					event: 'multipled',
					rules: {
						required: true,
						message: '请选择领取人',
						trigger: 'change'
					},
					col: 4,
					data: this.receiveUserList,
					'label-key': 'label',
					'value-key': 'value',
					readonly: true
				},
				{
					label: '质检员',
					name: 'administratorId',
					type: 'select',
					placeholder: '请选择质检员',
					event: 'multipled',
					rules: {
						required: true,
						message: '请选择质检员',
						trigger: 'change'
					},
					col: 4,
					data: this.administratorList,
					'label-key': 'label',
					'value-key': 'value',
					readonly: true
				},
				{
					label: '库房管理员',
					name: 'storeKeeperId',
					type: 'select',
					placeholder: '请选择库房管理员',
					event: 'multipled',
					rules: {
						required: true,
						message: '请选择库房管理员',
						trigger: 'change'
					},
					col: 4,
					data: this.storeKeeperList,
					'label-key': 'label',
					'value-key': 'value',
					readonly: true
				},
				{
					type: 'table',
					name: 'details',
					// lazy: true,
					from: true,
					numbers: true,
					thead: [
						{
							title: '原料编号',
							field: 'materialCode',
							align: 'center',
							showOverflowTooltip: true
						},
						{
							title: '原料名称',
							field: 'materialName',
							align: 'center',
							showOverflowTooltip: true
						},
						{
							title: '单位',
							field: 'unit',
							align: 'center',
							showOverflowTooltip: true
						},
						{
							title: '规格',
							field: 'specification',
							align: 'center',
							showOverflowTooltip: true
						},
						{
							title: '供应商',
							field: 'brand',
							align: 'center',
							minWidth: 100,
							showOverflowTooltip: true
						},
						// {
						// 	title: '库存数量',
						// 	field: 'residueNum',
						// 	align: 'center',
						// 	showOverflowTooltip: true
						// },
						{
							title: '领取数量',
							field: 'num',
							align: 'center',
							showOverflowTooltip: true
						},
						{
							title: '损耗数量',
							field: 'lossNum',
							align: 'center',
							showOverflowTooltip: true
						}
					]
				},
				{
					type: 'submit',
					skin: 'lay-form-btns',
					contents: [
						{
							type: 'reset',
							text: '取消',
							event: 'cancel'
						}
					]
				}
			];
		}
	},
	watch: {},
	created() {
		this.storeroomList();
		this.categoryList();
		this.mealsList();
		this.getSelectStallOpeningList();
		this.getSelectPersonList(1);
		this.getSelectPersonList(2);
		this.getSelectPersonList(3);
	},
	mounted() {
		// this.$request({
		// 	url: '/ybzy/hqstoreroom/list',
		// 	method: 'POST'
		// }).then(res => {
		// 	if (res.rCode !== 0) {
		// 		this.$message.error(res.msg);
		// 		return;
		// 	}
		// 	this.optionData.storeroomId = res.results.records;
		// });
	},
	methods: {
		/**
		 * 表格行高
		 */
		tableRowClassName({ row, rowIndex }) {
			let styleRes = {
				height: '54px !important'
			};
			return styleRes;
		},
		//获取当口下拉列表
		getSelectStallOpeningList() {
			this.$request({
				url: stallOpeningApi.getSelectList,
				params: { status: 1 },
				method: 'GET'
			}).then(res => {
				if (res.rCode === 0) {
					this.stallOpeningList = res.results;
				}
			});
		},
		//获取相关人员下拉列表 类型：质检员：1 领料人：2 库房管理员：3
		getSelectPersonList(value) {
			this.$request({
				url: operatePersonApi.getSelectList,
				params: { type: value, status: 1 },
				method: 'GET'
			}).then(res => {
				if (res.rCode === 0) {
					if (value == 1) {
						this.administratorList = res.results;
					}
					if (value == 2) {
						this.receiveUserList = res.results;
					}
					if (value == 3) {
						this.storeKeeperList = res.results;
					}
				}
			});
		},
		inputChange(key, value) {},
		hadeSubmit(data) {},
		/**
		 * 操作按钮点击事件
		 * @param {*} res
		 */
		btnClick(res) {
			let code = res.handle.code;
			switch (code) {
				case 'add':
					// 新增
					this.formTitle = '新增';
					const snowflake = new SnowflakeId();
					let id = snowflake.generate();
					this.curMaterialIds = [];
					this.ownId = id;
					this.formData = Object.assign({}, defFormData);
					this.formData.id = this.ownId;
					this.formData.details = [];
					this.showForm = true;
					break;
				case 'edit':
					// 编辑
					this.formTitle = '编辑';
					this.ownId = res.row.id;
					this.$request({
						url: interfaceUrl.info + '/' + res.row.id,
						method: 'GET'
					}).then(res => {
						if (res.rCode === 0) {
							this.curMaterialIds = [];
							this.formData = res.results;
							let details = this.formData.details;
							for (let item of details) {
								this.curMaterialIds.push(item.materialId);
							}
							this.showForm = true;
						}
					});
					break;
				case 'view':
					this.formTitle = '查看';
					this.ownId = res.row.id;
					// this.readModule(this.formItemList);
					this.$request({
						url: interfaceUrl.info + '/' + res.row.id,
						method: 'GET'
					}).then(res => {
						if (res.rCode === 0) {
							this.formData = res.results;
							this.showViewForm = true;
						}
					});
					break;
				case 'delete':
					this.deleteId = res.row.id;
					this.showDelete = true;
					break;
				case 'submit':
					this.ownId = res.row.id;
					this.showSubmit = true;
					break;
				case 'download':
					window.open(host + interfaceUrl.download + '?requestType=1&id=' + res.row.id, '_self');
					break;
				default:
					break;
			}
		},
		handleClose() {},
		handleFormItemChange() {},
		handleFormSubmit(data) {
			let formData = data;
			let url = '';
			if (this.formTitle === '新增') {
				url = interfaceUrl.save;
			} else {
				url = interfaceUrl.update;
			}
			this.$request({
				url: url,
				data: formData,
				method: 'POST',
				format: false
			}).then(res => {
				if (res.rCode === 0) {
					this.$message.success('操作成功');
					this.showForm = false;
					this.formData = {};
					this.$refs.table.reload();
				} else {
					this.$message.error(res.msg);
				}
			});
		},
		deleteRow() {
			this.$request({
				url: interfaceUrl.deleteBatchIds,
				data: { ids: this.deleteId },
				method: 'POST'
			}).then(res => {
				if (res.rCode == 0) {
					this.$refs.table.reload();
					this.$message.success('删除成功');
				} else {
					this.$message.error(res.msg);
				}
				this.showDelete = false;
			});
		},
		/**
		 * 排序变化事件
		 * @param {*} column
		 * @param {*} prop
		 * @param {*} order
		 */
		sortChange(column, prop, order) {
			if (column.order == 'ascending') {
				//升序
				this.params = {
					asc: 'true',
					orderBy: column.prop
				};
			} else if (column.order == 'descending') {
				//降序
				this.params = {
					asc: 'false',
					orderBy: column.prop
				};
			} else {
				//不排序
				this.params = {
					asc: 'false',
					orderBy: 'createTime'
				};
			}
			this.$refs.table.reload();
		},
		/**
		 * 编辑模式
		 */
		editModule(list) {
			for (var i in list) {
				var item = list[i];
				item.readonly = false;
			}
			list.push(this.editBtn);
		},
		/**
		 * 只读模式
		 */
		// readModule(list, hideField) {
		// 	for (var i in list) {
		// 		var item = list[i];
		// 		item.readonly = true;
		// 	}
		// 	list.push(this.cancelBtn);
		// },
		removeDetail(index, row) {
			this.formData.details.splice(index, 1);
			let i = this.curMaterialIds.indexOf(row.materialId);
			this.curMaterialIds.splice(i, 1);
		},
		storeroomList() {
			this.$request({
				url: storeroomApi.listAll,
				method: 'GET'
			}).then(res => {
				if (res.rCode === 0) {
					this.storeroomOptions = res.results;
				}
			});
		},
		categoryList() {
			this.$request({
				url: materialCategoryApi.categoryListSel,
				method: 'POST'
			}).then(res => {
				if (res.rCode === 0) {
					this.materialOptionData.categoryId = res.results.records;
				}
			});
		},
		mealsList() {
			this.$request({
				url: '/sys/v1/mecpSys/findSysCode.dhtml?sysAppCode=hq_meals',
				method: 'GET'
			}).then(res => {
				if (res.rCode === 0) {
					this.mealsOptions = res.results;
				}
			});
		},
		changeStoreroom(val) {
			if (val) {
				let storeroom = this.storeroomOptions.filter(v => v.id === this.formData.storeroomId);
				if (storeroom) {
					this.formData.storeKeeperId = storeroom[0].manager;
				}
			}
			this.formData.details = [];
			this.curMaterialIds = [];
		},
		//点击添加原料按钮
		doShowMaterial() {
			//判断是否已选定了库房
			if (!this.formData.storeroomId) {
				this.$message.warning('请先确定库房');
				return;
			}
			this.params.storeroomId = this.formData.storeroomId;
			this.showMaterial = true;
		},
		//原料选择
		selMaterial(rows) {
			const snowflake = new SnowflakeId();
			for (let row of rows) {
				let materialId = row.id;
				if (this.curMaterialIds.includes(materialId)) {
					continue;
				}
				this.curMaterialIds.push(materialId);
				let ownId = snowflake.generate();
				let createDetail = {
					id: ownId,
					materialCode: row.code,
					materialName: row.name,
					residueNum: row.inventoryNum,
					unit: row.unit,
					num: 0,
					brand: row.brand,
					specification: row.specification,
					shipmentOrderId: this.ownId,
					materialId: materialId
				};
				this.formData.details.push(createDetail);
			}
		},
		ensureSubmit() {
			this.$request({
				url: interfaceUrl.submit + '/' + this.ownId,
				method: 'GET'
			}).then(res => {
				if (res.rCode !== 0) {
					this.$message.error(res.msg);
					return;
				}
				this.$message.success('操作成功');
				this.$refs.table.reload();
				this.showSubmit = false;
			});
		},
		save(status) {
			this.$refs['form'].validate(valid => {
				if (!valid) {
					return;
				}
				let details = this.formData.details;
				if (details === null || details === undefined || details.length === 0) {
					this.$message.warning('请完善原料单！');
					return;
				}
				for (let item of details) {
					if (item.num <= 0) {
						this.$message.warning('领取数量需大于0！');
						return;
					}
					if (item.num > item.residueNum) {
						this.$message.warning('领取数量不能超过库存数量！');
						return;
					}
				}
				this.formData.status = status;
				this.handleFormSubmit(this.formData);
			});
		}
	}
};
</script>
<style scoped lang="scss">
@import '../../../assets/style/style.scss';

.content {
	display: flex;
	width: 100%;
	height: 100%;

	::v-deep .es-data-table {
		flex: 1;
		display: flex;
		flex-direction: column;

		.es-data-table-content {
			flex: 1;
			height: 0;
			display: flex;
			flex-direction: column;

			.el-table {
				flex: 1;
				// height: 100% !important;
			}

			.es-thead-border {
				.el-table__header {
					th {
						border-right: 1px solid #e1e1e1;
					}
				}
			}
		}
	}

	::v-deep .el-form-item__label {
		background: none;
		border: 0px solid #c2c2c2;
	}
}

.el-dialog__body {
	.btn-box {
		margin-top: 20px;
		display: flex;
		justify-content: flex-end;

		.btn {
			padding: 5px 10px;
			color: #666;
			border: 1px solid #eee;
			cursor: pointer;
			margin-right: 5px;
			border-radius: 4px;
		}
	}
}
</style>
