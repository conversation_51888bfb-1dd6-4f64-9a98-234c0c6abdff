//科研管理系统

//项目清单分页列表
export const getPorjectList = '/ybzy/projectBaseInfo/listJson';
//项目变更选择变更项目分页接口（登录用户个人项目）
export const getPersonPorjectList = '/ybzy/projectBaseInfo/personList';
//项目清单根据id删除
export const delPorjectByid = '/ybzy/projectBaseInfo/deleteById';
//项目清单详情按钮数据
export const getPorjectInfo = '/ybzy/projectBaseInfo/info';
//修改项目清单详情基本信息
export const updatePorjectInfo = '/ybzy/projectBaseInfo/update';

//中期检查申请表分页列表
export const getInterimList = '/ybzy/projectBaseInfo/listJson';
//保存中期检查申请表 发起流程
export const saveInterimByid = '/ybzy/projectInspection/save';
//修改中期检查申请表 办理流程
export const updateInterimByid = '/ybzy/projectInspection/update';
//通过业务id获取流程id 办理流程用
export const getFlowByid = '/ybzy/projectOther/getBusinessId';
//获取中期检查申请表信息
export const getFlowInfo = '/ybzy/projectInspection/info';
//回显表单内容 查看详情用
export const getFlowFormInfo = '/ybzy/projectInspection/getByProjectId';

//项目经费使用记录表分页列表
export const getCapitalUseList = '/ybzy/projectExpenditureUse/listJson';
//项目经费使用记录表分页列表 横向表
export const getCSCapitalUseList = '/ybzy/projectCrosswiseExpenditureUse/listJson';
//新增项目经费使用记录表
export const createCapitalUseByid = '/ybzy/projectExpenditureUse/save';
//新增项目经费使用记录表 横向表
export const saveCSCapitalUseByid = '/ybzy/projectCrosswiseExpenditureUse/save';
//项目经费使用记录表详情
export const readCapitalUseByid = '/ybzy/projectExpenditureUse/info';
//修改项目经费使用记录表
export const updateCapitalUseByid = '/ybzy/projectExpenditureUse/update';
//删除项目经费使用记录表
export const delCapitalUseByid = '/ybzy/projectExpenditureUse/deleteById';

//项目经费信息表分页列表
export const getCapitalInfoList = '/ybzy/projectExpenditureInfo/listJson';
//新增项目经费使用记录表
export const createCapitalInfoByid = '/ybzy/projectExpenditureInfo/save';
//项目经费使用记录表详情
export const readCapitalInfoByid = '/ybzy/projectExpenditureInfo/info';
//修改项目经费使用记录表
export const updateCapitalInfoByid = '/ybzy/projectExpenditureInfo/update';
//删除项目经费使用记录表
export const delCapitalInfoByid = '/ybzy/projectExpenditureInfo/deleteById';

//通过项目ID获取结题项目 查看详情用
export const getFinalAcceptanceInfo = '/ybzy/projectCloseApply/getByProjectId';

//通过项目ID获取已归档归档项目 查看详情用
export const getArchiveInfo = '/ybzy/projectFile/getByProjectId';

//通过项目ID获取变更项目列表 查看详情用
export const getChangeInfoByid = '/ybzy/projectChange/findListByProjectId';

//项目变更分页列表
export const getChangeList = '/ybzy/projectChange/listJson';

//结项验收分页列表
export const getFileList = '/ybzy/projectBaseInfo/projectConclusionPage';

// 查看成果转换
export const getAchievementList = '/ybzy/projectSrAchievement/viewTransition';

// 新增成果转换
export const createAchievement = '/ybzy/projectSrAchievement/addTransition';

// 新增立项
export const createBaseInfo = '/ybzy/projectBaseInfo/initiation';

// 通过项目ID获取配套经费总额
export const getAssortAmount = '/ybzy/projectExpenditureInfo/getAssortAmount';

// 项目结题发起流程要提交保存的表单
// export const saveFinalAcceptance = '/ybzy/projectConclusion/save';
export const saveFinalAcceptance = '/ybzy/projectCloseApply/save';

//获取结题申请信息
export const getFinalAcceptance = '/ybzy/projectCloseApply/getCloseApplyInfo';

//获取用户角色权限
export const getRoleMap = '/ybzy/projectBaseInfo/getRoleMap';

//项目归档发起提交保存的表单
export const savePigeonhole = '/ybzy/projectFile/save';
//项目归档发起提交保存的表单（批量）
export const saveBatchPigeonhole = '/ybzy/projectFile/saveBatch';

//系统首页获取饼图数据
export const projectClassifyStatistics = '/ybzy/projectBaseInfo/projectClassifyStatistics';

//系统首页获取柱形图数据
export const projectStatisticsByGroup = '/ybzy/projectBaseInfo/projectStatisticsByGroup';

//系统首页获取柱形图数据-成果
export const paStatisticsByGroup = '/ybzy/paBaseInfo/paStatisticsByGroup';


//获取变更基本详情
export const getChangeDetailInfo = '/ybzy/projectChange/info';

//变更流程表单保存
export const saveChangeFlow = '/ybzy/projectChange/save';
//变更流程表单修改
export const updateChangeFlow = '/ybzy/projectChange/update';

// 科研公告-列表
export const cmsinfoList = '/ybzy/cmsinfo/front/paging';
// 科研公告-文章模型字典
export const cmsmodelCode = '/ybzy/cmsmodel/list';
// 科研公告-所属目录字典
export const cmsnodeCode = '/ybzy/cmsnode/list';
// 科研公告-详情
export const cmsinfoDetail = '/ybzy/cmsinfo/detail';
// 科研公告-获取表单字段
export const modelForm = '/ybzy/cmsmodelfield/modelForm';
