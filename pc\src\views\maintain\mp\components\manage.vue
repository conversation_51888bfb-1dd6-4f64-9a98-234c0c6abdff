<template>
	<div style="width: 100%; height: 100%">
		<es-data-table
			ref="multipleTable"
			:key="tableCount"
			:row-style="tableRowClassName"
			:page="page"
			:url="dataTableUrl"
			:param="dataTableParam"
			:thead="thead"
			:toolbar="toolbar"
			:selectable="selectable"
			:border="true"
			:numbers="true"
			checkbox
			form
			@selection-change="handleSelectionChange"
			@btnClick="btnClick"
		></es-data-table>
		<es-dialog
			title="设置维修类型"
			:visible.sync="visibleWorkType"
			:show-scale="false"
			:drag="false"
			size="mini"
			width="300px"
		>
			<es-select
				style="width: 100%"
				v-model="workType"
				sysCode="hq_repair_type"
				label-key="shortName"
				value-key="cciValue"
			></es-select>
			<p class="botn-box">
				<el-button size="small" @click="cancelWorkType()">取 消</el-button>
				<el-button size="small" type="primary" @click="btnWorkType()" :loading="btnWorkTypeLoading">
					确 定
				</el-button>
			</p>
		</es-dialog>
	</div>
</template>
<script>
import api from '@/http/maintain/applicationApi';

export default {
	props: {},
	data() {
		return {
			visibleWorkType: false,
			page: {
				pageSize: 10,
				totalCount: 0
			},
			tableCount: 1,
			dataTableUrl: api.getDataList,
			dataTableParam: {},

			toolbar: [
				{
					type: 'button',
					contents: [
						{
							text: '确定',
							code: 'add',
							type: 'primary'
						},
						{
							text: '取消',
							code: 'exit',
							type: 'primary'
						}
					]
				},
				{
					type: 'search',
					reset: true,
					contents: [
						{
							type: 'text',
							name: 'keyword',
							placeholder: '关键词查询',
							clearable: true
						}
					]
				}
			],
			thead: [
				{
					title: '编号',
					align: 'left',
					field: 'loginName',
					showOverflowTooltip: true
				},
				{
					title: '姓名',
					align: 'center',
					field: 'userName',
					showOverflowTooltip: true
				},
				{
					title: '联系方式',
					align: 'center',
					field: 'phone',
					showOverflowTooltip: true
				},
				{
					title: '性别',
					align: 'center',
					field: 'sex',
					showOverflowTooltip: true,
					render: (h, param) => {
						return h('p', null, param.row.sex == '0' ? '男' : '女');
					}
				},
				{
					title: '部门',
					align: 'center',
					field: 'deptName',
					showOverflowTooltip: true
				}
			],
			multipleSelection: [],
			//维修类型
			workType: '',
			btnWorkTypeLoading: false
		};
	},
	computed: {},
	watch: {},
	created() {},
	methods: {
		/**
		 * 表格行高
		 */
		tableRowClassName() {
			return {
				height: '54px !important'
			};
		},
		handleSelectionChange(val) {
			this.multipleSelection = val;
		},
		selectable(row, index) {
			return row.isAble != 0;
		},
		// 操作按钮
		btnClick(res) {
			let code = res.handle.code;
			switch (code) {
				case 'add':
					this.handleFormSubmit();
					break;
				case 'exit':
					this.cancel();
					break;
			}
		},
		async btnWorkType() {
			if (!this.workType) {
				this.$message.warning('请选择维修类别！');
				return;
			}
			this.btnWorkTypeLoading = true;
			let userIds = [],
				personList = [];
			for (let item of this.multipleSelection) {
				userIds.push(item.id);
				let person = {
					number: item.loginName,
					name: item.userName,
					userId: item.id,
					phone: item.phone,
					sex: item.sex,
					unit: item.deptName,
					workType: this.workType,
					type: 0
				};
				personList.push(person);
			}
			let { rCode, msg } = await this.$.ajax({
				url: api.saveInMp,
				data: personList,
				format: false,
				method: 'POST'
			});
			if (rCode === 0) {
				this.visibleWorkType = false;
				this.$message.success('操作成功');
				this.$emit('closeAddPage', 'success');
			} else {
				this.$message.error(msg);
			}
			this.btnWorkTypeLoading = false;
		},
		cancelWorkType() {
			this.visibleWorkType = false;
			this.workType = '';
		},
		handleFormSubmit() {
			if (this.multipleSelection.length === 0) {
				this.$message.warning('至少选中一条数据！');
				return;
			}
			this.visibleWorkType = true;
		},
		cancel() {
			this.$emit('closeAddPage', 'cancel');
		}
	}
};
</script>
<style lang="scss" scoped>
.botn-box {
	display: flex;
	justify-content: flex-end;
	margin-top: 20px;
}
</style>
