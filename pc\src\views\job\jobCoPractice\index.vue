<template>
	<div class="content">
		<es-data-table
			ref="table"
			:row-style="tableRowClassName"
			:full="true"
			:fit="true"
			:thead="thead"
			:toolbar="toolbar"
			:border="true"
			:page="pageOption"
			:url="dataTableUrl"
			:numbers="true"
			:param="params"
			:option-data="optionData"
			close
			form
			@btnClick="btnClick"
			@sort-change="sortChange"
			@submit="hadeSubmit"
			@edit="changeTable"
		></es-data-table>
		<es-dialog
			:title="formTitle"
			:visible.sync="showForm"
			width="900px"
			height="600px"
			:close-on-click-modal="false"
			:destroy-on-close="true"
		>
			<es-form
				ref="form"
				:model="formData"
				:contents="formItemList"
				height="500px"
				:genre="2"
				collapse
				@change="inputChange"
				@submit="handleFormSubmit"
				@reset="showForm = false"
			/>
		</es-dialog>
	</div>
</template>

<script>
import interfaceUrl from '@/http/job/jobCoPractice/api';
export default {
	data() {
		return {
			ownId: '',
			dataTableUrl: interfaceUrl.listJson,
			showForm: false,
			showRoleForm: false,
			enpList: [],
			formData: {},
			formTitle: '编辑',
			optionData: {
				isPractice: [
					{
						value: 1,
						name: '是'
					},
					{
						value: 0,
						name: '否'
					}
				]
			},
			editBtn: {
				type: 'submit',
				skin: 'lay-form-btns',
				contents: [
					{
						type: 'primary',
						text: '确定',
						event: 'confirm'
					},
					{
						type: 'reset',
						text: '取消',
						event: 'cancel'
					}
				]
			},
			cancelBtn: {
				type: 'submit',
				skin: 'lay-form-btns',
				contents: [
					{
						type: 'reset',
						text: '取消',
						event: 'cancel'
					}
				]
			},
			toolbar: [
				{
					type: 'search',
					contents: [
						{
							type: 'select',
							placeholder: '是否实习网点',
							name: 'isPractice',
							event: 'multipled',
							data: [
								{ value: 1, name: '是' },
								{ value: 0, name: '否' }
							],
							clearable: true,
							col: 4
						},
						{
							type: 'text',
							name: 'keyword',
							placeholder: '关键字查询'
						}
					]
				}
			],
			thead: [
				{
					title: '企业名称',
					align: 'left',
					field: 'corpName',
					sortable: 'custom',
					showOverflowTooltip: true
				},
				{
					title: '所属行业',
					align: 'left',
					field: 'corpIndustry',
					showOverflowTooltip: true
				},
				{
					title: '详细地址',
					align: 'left',
					field: 'contactAddress',
					showOverflowTooltip: true
				},
				{
					title: '联系方式',
					align: 'left',
					field: 'linkPhone',
					render: (h, param) => {
						return h(
							'p',
							{ calss: { p1: true } },
							this.formatLink(param.row.linkMan, param.row.linkPhone)
						);
					},
					showOverflowTooltip: true
				},
				{
					title: '成立时间',
					align: 'center',
					width: 160,
					field: 'establishDate'
					// valueFormat: "yyyy-MM-dd"
				},
				{
					title: '是否实习网点',
					width: 120,
					field: 'isPractice',
					align: 'center',
					type: 'switch'
				},
				{
					title: '操作',
					type: 'handle',
					width: 150,
					template: '',
					events: [
						{
							code: 'view',
							text: '查看'
						}
						// ,{
						// 	code: 'edit',
						// 	text: '编辑'
						// }
					]
				}
			],
			pageOption: {
				layout: 'total, prev, pager, next, jumper',
				pageSize: 10,
				// hideOnSinglePage: true,
				position: 'right',
				current: 1,
				pageNum: 1
			},
			params: {
				asc: 'false',
				orderBy: 'createTime'
			}
		};
	},
	computed: {
		formItemList() {
			return [
				{
					label: '企业名',
					name: 'corpName',
					placeholder: '请输入用户名',
					event: 'multipled',
					rules: {
						required: true,
						message: '请输入用户名',
						trigger: 'blur'
					},
					verify: 'required',
					col: 12
				},
				{
					label: '所属行业',
					name: 'corpIndustry',
					type: 'select',
					placeholder: '请选择所属行业',
					col: 6,
					rules: {
						required: false,
						message: '请选择所属行业',
						trigger: 'blur'
					},
					sysCode: 'plat_enp_industry',
					'label-key': 'shortName',
					'value-key': 'cciValue'
				},
				{
					label: '人数规模',
					name: 'peopleNumCode',
					type: 'select',
					placeholder: '请选择人数规模',
					col: 6,
					rules: {
						required: false,
						message: '请选择人数规模',
						trigger: 'blur'
					},
					sysCode: 'post_company_scale',
					'label-key': 'shortName',
					'value-key': 'cciValue'
				},
				{
					label: '联系人',
					name: 'linkMan',
					placeholder: '请输入联系人',
					event: 'multipled',
					rules: {
						required: false,
						message: '请输入联系人',
						trigger: 'blur'
					},
					verify: 'required',
					col: 6
				},
				{
					label: '联系方式',
					name: 'linkPhone',
					placeholder: '请输入联系方式',
					event: 'multipled',
					rules: {
						required: false,
						message: '请输入联系方式',
						trigger: 'blur'
					},
					verify: 'required',
					col: 6
				},
				{
					label: '地址',
					name: 'contactAddress',
					placeholder: '请输入地址',
					event: 'multipled',
					rules: {
						required: false,
						message: '请输入地址',
						trigger: 'blur'
					},
					verify: 'required',
					col: 12
				},
				{
					label: '经营范围',
					type: 'textarea',
					name: 'businessScope',
					placeholder: '请输入经营范围',
					event: 'multipled',
					rules: {
						required: false,
						message: '请输入经营范围',
						trigger: 'blur'
					},
					verify: 'required',
					col: 12
				},
				{
					label: '简介',
					type: 'textarea',
					name: 'introduction',
					placeholder: '请输入简介',
					event: 'multipled',
					rules: {
						required: false,
						message: '请输入简介',
						trigger: 'blur'
					},
					verify: 'required',
					col: 12
				},
				{
					label: '经营状态',
					name: 'managementFormsCode',
					type: 'select',
					placeholder: '请选择经营状态',
					col: 6,
					rules: {
						required: false,
						message: '请选择经营状态',
						trigger: 'blur'
					},
					sysCode: 'plat_enterprise_management_forms',
					'label-key': 'shortName',
					'value-key': 'cciValue'
				},
				{
					type: 'switch',
					label: '是否实习网点',
					verify: 'required',
					name: 'isPractice',
					placeholder: '',
					col: 6,
					data: [
						{
							value: 1,
							name: '是'
						},
						{
							value: 0,
							name: '否'
						}
					]
				}
			];
		}
	},
	watch: {},
	created() {},
	mounted() {},
	methods: {
		/**
		 * 表格行高
		 */
		tableRowClassName({ row, rowIndex }) {
			let styleRes = {
				height: '54px !important'
			};
			return styleRes;
		},
		inputChange(key, value) {},
		hadeSubmit(data) {},
		//格式化表格联系方式
		formatLink(man, phone) {
			var des = '';
			if (man && phone) {
				des = man + '-' + phone;
			} else {
				if (phone) {
					des = phone;
				}
			}
			return des;
		},
		/**
		 * 操作按钮点击事件
		 * @param {*} res
		 */
		btnClick(res) {
			let code = res.handle.code;
			switch (code) {
				// case 'edit':
				// 	// 编辑
				// 	this.formTitle = '编辑';
				// 	this.ownId = res.row.id;
				// 	this.editModule(this.formItemList, []);
				// 	this.$request({
				// 		url: interfaceUrl.info + '/' + res.row.id,
				// 		method: 'GET'
				// 	}).then(res => {
				// 		if (res.rCode == 0) {
				// 			this.formData = res.results;
				// 			this.showForm = true;
				// 		}
				// 	});
				// 	break;
				case 'view':
					this.formTitle = '查看';
					this.ownId = res.row.id;
					this.readModule(this.formItemList, []);
					this.$request({
						url: interfaceUrl.info + '/' + res.row.id,
						method: 'GET'
					}).then(res => {
						if (res.rCode == 0) {
							this.formData = res.results;
							this.showForm = true;
						}
					});
					break;
				default:
					break;
			}
		},
		handleClose() {},
		handleFormItemChange() {},
		handleFormSubmit(data) {
			let formData = data;
			delete formData.extMap;
			let url = '';
			if (this.formTitle == '新增') {
				url = interfaceUrl.save;
			} else {
				url = interfaceUrl.update;
			}
			this.$request({
				url: url,
				data: formData,
				method: 'POST'
			}).then(res => {
				if (res.rCode == 0) {
					this.$message.success('操作成功');
					this.showForm = false;
					this.formData = {};
					this.$refs.table.reload();
				} else {
					this.$message.error(res.msg);
				}
			});
		},
		/**
		 * 排序变化事件
		 * @param {*} column
		 * @param {*} prop
		 * @param {*} order
		 */
		sortChange(column, prop, order) {
			if (column.order == 'ascending') {
				//升序
				this.params = {
					asc: 'true',
					orderBy: column.prop
				};
			} else if (column.order == 'descending') {
				//降序
				this.params = {
					asc: 'false',
					orderBy: column.prop
				};
			} else {
				//不排序
				this.params = {
					asc: 'false',
					orderBy: 'createTime'
				};
			}
			this.$refs.table.reload();
		},
		/**
		 * 编辑模式
		 */
		editModule(list, hideField) {
			for (var i in list) {
				var item = list[i];
				item.readonly = false;
				if (undefined != hideField && hideField.length > 0 && hideField.indexOf(item.name) > -1) {
					item.hide = true;
				}
			}
			list.push(this.editBtn);
		},
		/**
		 * 只读模式
		 */
		readModule(list, hideField) {
			for (var i in list) {
				var item = list[i];
				item.readonly = true;
				if (undefined != hideField && hideField.length > 0 && hideField.indexOf(item.name) > -1) {
					item.hide = true;
				}
			}
			list.push(this.cancelBtn);
		},
		changeTable(val) {
			if ('isPractice' === val.name) {
				this.$request({
					url: interfaceUrl.updatePractice,
					data: {
						id: val.data.id
					},
					method: 'POST'
				}).then(res => {
					if (res.rCode == 0) {
						this.$message.success('设置成功！');
					} else {
						this.$message.error(res.msg);
					}
				});
			}
		}
	}
};
</script>
<style scoped lang="scss">
.content {
	width: 100%;
	height: 100%;
}

.el-dialog__body {
	.btn-box {
		margin-top: 20px;
		display: flex;
		justify-content: flex-end;

		.btn {
			padding: 5px 10px;
			color: #666;
			border: 1px solid #eee;
			cursor: pointer;
			margin-right: 5px;
			border-radius: 4px;
		}
	}
}
</style>
