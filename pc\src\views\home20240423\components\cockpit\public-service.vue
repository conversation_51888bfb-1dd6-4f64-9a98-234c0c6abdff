<template>
	<div class="public">
		<!-- 内u容展示区 -->
		<div class="sub-title">本周学生消费</div>
		<div class="amount-box">
			<p class="amount">
				学生消费总额
				<span class="num">{{ amountNum }}</span>
				<span class="unit">万元</span>
			</p>
			<p class="amount text-right">
				学生消费总人次
				<span class="num">{{ amountPeronNum }}</span>
				<span class="unit">万人次</span>
			</p>
		</div>
		<!-- 消费金额展示区域 -->
		<div class="content">
			<!-- 左边 -->
			<div class="content-left">
				<!--  -->
				<p class="card-title border-box">校内赛事活动</p>
				<div class="card-list border-box activity">
					<div v-for="(item, index) in cardList" :key="index" class="item">
						<span class="item-name">{{ item.name }}</span>
						<span class="item-num">{{ item.value }}</span>
					</div>
				</div>
			</div>
			<!-- 右边 -->
			<div class="content-right">
				<!--  -->
				<p class="card-title border-box">生活综合服务</p>
				<div class="card-list border-box service">
					<div v-for="(item, index) in rightList" :key="index" class="item">
						<span class="item-name">{{ item.name }}</span>
						<span class="item-num">{{ item.value }}</span>
					</div>
				</div>
			</div>
		</div>
	</div>
</template>

<script>
import { xodbApi2 } from '@/api/xodb';
export default {
	name: '',
	components: {},
	props: {},
	data() {
		return {
			amountNum: '0', //消费总额
			amountPeronNum: '0', // 消费人次
			//
			cardList: [
				{
					name: '德育思政',
					value: '0次'
				},
				{
					name: '专业活动',
					value: '0次'
				},
				{
					name: '体验活动',
					value: '0次'
				},
				{
					name: '美育活动',
					value: '0次'
				},
				{
					name: '劳动活动',
					value: '0次'
				}
			],
			rightList: [
				{
					name: '场地使用数',
					value: '0次'
				},
				{
					name: '后勤报修数后勤报修数',
					value: '0次'
				},
				{
					name: '课外运动数',
					value: '0次'
				},
				{
					name: '社团活动数社团活动数',
					value: '0次'
				},
				{
					name: '数字图书馆访问数',
					value: '0次'
				},
				{
					name: '网络使用数',
					value: '0次'
				},
				{
					name: '场馆预约数',
					value: '0次'
				}
			]
		};
	},
	computed: {
		loginUserInfo() {
			return JSON.parse(localStorage.getItem('loginUserInfo')) || {};
		}
	},
	mounted() {
		this.getAmount();
	},
	methods: {
		// 公共消费  学生消费总额和学生消费总人数
		async getAmount() {
			const LeaderRole = localStorage.getItem('LeaderRole');
			try {
				const {
					data: { list }
				} = await xodbApi2.post({
					url: 'ads_jx_xsbzxfqk_query',
					params: {
						// xyjgbh: this.loginUserInfo.orgCode，
						xyjgbh: LeaderRole === 'xLeader' ? '1' : this.loginUserInfo.orgCode
					}
				});
				const data = list[0] || {};
				this.amountNum = data.bzxf || 0;
				this.amountPeronNum = data.xfcs || 0;
			} catch (error) {
				console.error('处理数据失败:', error);
			}
		}
	}
};
</script>

<style lang="scss" scoped>
.border-box {
	background: rgba(255, 255, 255, 0.25);
	border-radius: 3px;
	border: 1px solid #ffffff;
}
.public {
	margin-top: 14px;
	.sub-title {
		font-family: MicrosoftYaHei, MicrosoftYaHei;
		font-weight: bold;
		font-size: 14px;
		color: #0a325b;
		line-height: 19px;
		text-align: left;
		font-style: normal;
		position: relative;
		&::after {
			content: '';
			position: absolute;
			right: 0;
			display: inline-block;
			width: calc(100% - 96px);
			border-top: 1px dashed #b2c6dc;
			top: 50%;
		}
	}
	.amount-box {
		display: flex;
		justify-content: space-between;
		.amount {
			width: calc(50%);
			font-family: MicrosoftYaHei;
			font-size: 14px;
			color: #454545;
			line-height: 19px;
		}
		.text-right {
			text-align: right;
		}
		.num {
			font-family: DINPro, DINPro;
			font-weight: bold;
			font-size: 22px;
			color: #0a325b;
			line-height: 28px;
			//margin: 0 8px;
		}
		.unit {
			color: #294d79;
		}
	}
	.content {
		display: flex;
		justify-content: space-between;
		margin-top: 14px;
		&-left,
		&-right {
			width: calc(50% - 5px);
		}
		.card-title {
			width: 100%;
			height: 30px;
			font-family: MicrosoftYaHei, MicrosoftYaHei;
			font-weight: bold;
			font-size: 14px;
			color: #0a325b;
			line-height: 30px;
			text-align: center;
		}
		.card-list {
			height: 186px;
			margin-top: 7px;
			padding: 6px 14px;
			font-family: MicrosoftYaHei;
			font-size: 12px;
			line-height: 25px;
			.item {
				display: flex;
				justify-content: space-between;
			}
			.item-name {
				color: #454545;
			}
			.item-num {
				color: #0055aa;
			}
		}
		.activity {
			line-height: 36px;
		}
	}
}
</style>
