<template>
	<div class="content">
		<es-data-table
			ref="table"
			:row-style="tableRowClassName"
			:full="true"
			:fit="true"
			:thead="thead"
			:toolbar="toolbar"
			:border="true"
			:page="pageOption"
			:url="dataTableUrl"
			:numbers="true"
			:param="params"
			close
			form
			@btnClick="btnClick"
			@sort-change="sortChange"
			@submit="hadeSubmit"
		></es-data-table>
		<!--   编辑  -->
		<es-dialog :drag="false" :title="formTitle" :visible.sync="open" append-to-body>
			<el-form
				v-if="open"
				ref="form"
				:model="form"
				:rules="rules"
				label-width="100px"
				:disabled="formTitle == '查看'"
			>
				<el-form-item label="标题" prop="title">
					<el-input v-model="form.title" placeholder="请输入标题" />
				</el-form-item>
				<el-form-item label="描述" prop="content">
					<el-input
						v-model="form.content"
						type="textarea"
						placeholder="请输入描述"
						:autosize="{ minRows: 4, maxRows: 4 }"
						:style="{ width: '100%' }"
					></el-input>
				</el-form-item>
				<el-form-item label="事件类型" prop="eventType">
					<el-select
						v-model="form.eventType"
						placeholder="请选择事件类型"
						clearable
						:style="{ width: '100%' }"
					>
						<el-option
							v-for="(item, index) in eventTypeOption"
							:key="index"
							:label="item.label"
							:value="item.value"
							:disabled="item.disabled"
						></el-option>
					</el-select>
				</el-form-item>
				<el-form-item label="发生地址" prop="address">
					<el-input v-model="form.address" placeholder="请输入发生地址" />
				</el-form-item>
				<el-form-item label="发生时间" prop="happenTime">
					<el-date-picker
						v-model="form.happenTime"
						type="datetime"
						placeholder="选择日期"
						:picker-options="reportDateOptions"
						style="width: 100%"
						value-format="yyyy-MM-dd HH:mm:ss"
						format="yyyy-MM-dd HH:mm:ss"
					></el-date-picker>
				</el-form-item>
				<el-form-item label="备注" prop="remark">
					<el-input
						v-model="form.remark"
						type="textarea"
						placeholder="请输入备注"
						:autosize="{ minRows: 3, maxRows: 3 }"
						:style="{ width: '100%' }"
					></el-input>
				</el-form-item>
				<el-form-item key="selectDeptList" label="经纬度" prop="lngLat">
					<lng-lat v-model="form.lngLat"></lng-lat>
				</el-form-item>
				<el-form-item label="上传照片">
					<es-upload
						v-bind="attrs"
						ref="upload"
						select-type="icon-plus"
						list-type="picture-card"
					></es-upload>
				</el-form-item>
			</el-form>
			<div v-if="form.flow?.length > 0 && formTitle === '查看'" class="flow-box">
				<div class="flow-title">流程</div>
				<el-timeline :reverse="false" class="flow-timeline">
					<el-timeline-item
						v-for="(activity, index) in form.flow"
						:key="index"
						:timestamp="activity.time"
						:type="form.flow?.length === index + 1 ? 'primary' : ''"
					>
						{{ activity.desc }}
					</el-timeline-item>
				</el-timeline>
			</div>

			<div class="dialog-footer" style="text-align: right;">
				<el-button v-show="formTitle != '查看'" type="primary" @click="submitForm">确 定</el-button>
				<el-button @click="cancel">取 消</el-button>
			</div>
		</es-dialog>
		<!--  派发  -->
		<es-dialog :drag="false"
			:title="formTitle"
			:visible.sync="dispatchOpen"
			width="600px"
			size="sm"
			append-to-body
		>
			<el-form ref="dispatchForm" :model="dispatchForm" :rules="rules" label-width="100px">
				<el-form-item label="处置人员" prop="dealUserId">
					<es-selector
						v-model="value"
						value-type="string"
						:multiple="false"
						filterable
						:types="['employee']"
						:tabs="tabs"
						title="处置人员选择"
						@confirm="handleConfirm"
						@change="handleCancel"
					>
						<i class="es-icon-user"></i>
					</es-selector>
				</el-form-item>
				<el-form-item label="指导意见" prop="dispatchContent">
					<el-input
						v-model="dispatchForm.dispatchContent"
						type="textarea"
						placeholder="请输入分派指导意见"
						:autosize="{ minRows: 3, maxRows: 3 }"
						:style="{ width: '100%' }"
					></el-input>
				</el-form-item>
			</el-form>
			<div class="dialog-footer" style="position: absolute;bottom: 20px;right: 20px;">
				<el-button type="warning" size="small" @click="submitStop">终止</el-button>
				<el-button type="primary" size="small" @click="submitDispatch">派发</el-button>
				<el-button size="small" @click="cancel">取 消</el-button>
			</div>
		</es-dialog>

		<es-dialog 
			:drag="false"
			title="删除"
			size="sm"
			:visible.sync="showDelete"
			:close-on-click-modal="false"
			:middle="true"
			width="260px"
			height="140px"
		>
			<div>确定要删除该条数据吗</div>
			<div class="btn-box">
				<div class="btn theme" @click="deleteRow">确定</div>
				<div class="btn" @click="showDelete = false">取消</div>
			</div>
		</es-dialog>
	</div>
</template>

<script>
import interfaceUrl from '@/http/screen/reportEvent';
import SnowflakeId from 'snowflake-id';
import LngLat from '@/components/lngLat.vue';

export default {
	name: 'YjRefuge', //避难场所
	components: { LngLat },
	data() {
		return {
			value: [],
			tabs: {
				employee: {
					label: '用户选择',
					name: 'employee',
					url: '/sys/v1/mecpSys/getSelectorOrgTree.dhtml',
					data: [],
					nodeData: '',
					selection: [],
					value: [],
					param: {
						showarea: 1,
						id: 0,
						filid: 'all'
					}
				}
			},
			selection: '/sys/v1/mecpSys/getSelectorOrgDetail.dhtml',
			//发生时间配置
			reportDateOptions: {
				disabledDate(time) {
					return time.getTime() > Date.now();
				}
			},
			eventTypeOption: [
				{ label: '防火', value: '防火' },
				{ label: '安全', value: '安全' }
			],
			ownId: '',
			dataTableUrl: interfaceUrl.listJson,
			showForm: false,
			showDelete: false,
			formData: {},
			formTitle: '编辑',
			editBtn: {
				type: 'submit',
				skin: 'lay-form-btns',
				contents: [
					{
						type: 'primary',
						text: '确定',
						event: 'confirm'
					},
					{
						type: 'reset',
						text: '取消',
						event: 'cancel'
					}
				]
			},
			cancelBtn: {
				type: 'submit',
				skin: 'lay-form-btns',
				contents: [
					{
						type: 'reset',
						text: '取消',
						event: 'cancel'
					}
				]
			},
			toolbar: [
				{
					type: 'button',
					contents: [
						{
							text: '新增',
							code: 'add',
							type: 'primary'
						}
					]
				},
				{
					type: 'search',
					contents: [
						{
							type: 'text',
							name: 'keyword',
							placeholder: '关键字查询'
						}
					]
				}
			],
			thead: [
				{
					title: '标题',
					align: 'left',
					field: 'title',
					sortable: 'custom',
					showOverflowTooltip: true
				},
				{
					title: '内容',
					align: 'left',
					field: 'content',
					sortable: 'custom',
					showOverflowTooltip: true
				},
				{
					title: '状态',
					align: 'left',
					field: 'status',
					sortable: 'custom',
					showOverflowTooltip: true,
					sysCode: 'zhdd_event_state'
				},
				{
					title: '地址',
					align: 'left',
					field: 'address',
					sortable: 'custom',
					showOverflowTooltip: true
				},
				{
					title: '经度',
					align: 'left',
					field: 'longitude',
					sortable: 'custom',
					showOverflowTooltip: true
				},
				{
					title: '纬度',
					align: 'left',
					field: 'latitude',
					sortable: 'custom',
					showOverflowTooltip: true
				},
				{
					title: '上报时间',
					align: 'left',
					field: 'reportTime',
					sortable: 'custom',
					showOverflowTooltip: true
				},
				{
					title: '发生时间',
					align: 'left',
					field: 'happenTime',
					sortable: 'custom',
					showOverflowTooltip: true
				},
				{
					title: '上报人姓名',
					align: 'left',
					field: 'reportUserName',
					sortable: 'custom',
					showOverflowTooltip: true
				},
				{
					title: '上报人联系电话',
					align: 'left',
					field: 'reportUserTel',
					sortable: 'custom',
					showOverflowTooltip: true
				},
				{
					title: '备注',
					align: 'left',
					field: 'remark',
					sortable: 'custom',
					showOverflowTooltip: true
				},
				{
					title: '操作',
					type: 'handle',
					width: 130,
					template: '',
					events: [
						{
							code: 'view',
							text: '查看'
						},
						{
							code: 'delete',
							text: '删除',
							rules: rows => {
								return rows.deleteAndEidtStatus == '1';
							}
						}
					]
				}
			],
			pageOption: {
				layout: 'total, prev, pager, next, jumper',
				pageSize: 10,
				// hideOnSinglePage: true,
				position: 'right',
				current: 1,
				pageNum: 1
			},
			params: {
				asc: 'true',
				orderBy: 'createTime',
				status: 1,
				reportUserId: ''
				// 用户id
			},
			currentUserId: '',
			open: false,
			dispatchOpen: false,
			// 表单参数
			form: {
				id: null,
				title: null,
				content: null,
				eventType: null,
				status: null,
				address: null,
				longitude: null,
				latitude: null,
				reportTime: null,
				happenTime: null,
				reportUserId: null,
				reportUserName: null,
				reportUserTel: null,
				remark: null,
				lngLat: [],
				pwd: null
			},
			dispatchForm: {
				id: null,
				title: null,
				content: null,
				eventType: null,
				status: null,
				address: null,
				longitude: null,
				latitude: null,
				reportTime: null,
				happenTime: null,
				reportUserId: null,
				reportUserName: null,
				reportUserTel: null,
				remark: null,
				lngLat: [],
				pwd: null,
				dispatchContent: null,
				dealUserId: null,
				dealUserName: null,
				dealUserTel: null,
				dealContent: null
			},
			// 表单校验
			rules: {
				title: [{ required: true, message: '需要填写信息', trigger: 'blur' }],
				content: [{ required: true, message: '需要填写信息', trigger: 'blur' }],
				eventType: [{ required: true, message: '需要填写信息', trigger: 'blur' }],
				address: [{ required: true, message: '需要填写信息', trigger: 'blur' }],
				happenTime: [{ required: true, message: '需要填写信息', trigger: 'blur' }],
				dealContent: [{ required: true, message: '需要填写信息', trigger: 'blur' }]
			},
			imgListSize: 1,
			ydlxOptions: [] //用地类型
		};
	},
	computed: {
		attrs() {
			return {
				code: 'zhdd_report_event',
				ownId: this.ownId,
				preview: true,
				download: true,
				operate: true
			};
		}
	},
	watch: {},
	created() {
		const userInfo = JSON.parse(localStorage.getItem('loginUserInfo')) || {};
		// this.username = this.userInfo && this.userInfo.name ? this.userInfo.name : '';
		if(userInfo.userId) {
			this.currentUserId = userInfo.userId
			this.params.reportUserId = userInfo.userId
		}
	},
	mounted() {
		
	},
	methods: {
		handleConfirm(res) {
			console.log(this.value, res, 3333);
		},
		handleCancel() {},
		/**
		 * 表格行高
		 */
		tableRowClassName({ row, rowIndex }) {
			let styleRes = {
				height: '54px !important'
			};
			return styleRes;
		},
		inputChange(key, value) {},
		hadeSubmit(data) {},
		deleteRow() {
			this.$request({
				url: interfaceUrl.deleteBatchIds,
				data: { ids: this.deleteId },
				method: 'POST'
			}).then(res => {
				if (res.rCode == 0) {
					this.$refs.table.reload();
					this.$message.success('删除成功');
				} else {
					this.$message.error(res.msg);
				}
				this.showDelete = false;
			});
		},
		/**
		 * 排序变化事件
		 * @param {*} column
		 * @param {*} prop
		 * @param {*} order
		 */
		sortChange(column, prop, order) {
			if (column.order == 'ascending') {
				//升序
				this.params = {
					asc: 'true',
					orderBy: column.prop,
					status: 1,
					reportUserId: this.currentUserId
				};
			} else if (column.order == 'descending') {
				//降序
				this.params = {
					asc: 'false',
					orderBy: column.prop,
					status: 1,
					reportUserId: this.currentUserId
				};
			} else {
				//不排序
				this.params = {
					asc: 'false',
					orderBy: 'createTime',
					status: 1,
					reportUserId: this.currentUserId
				};
			}
			this.$refs.table.reload();
		},
		btnClick(res) {
			let code = res.handle.code;
			switch (code) {
				case 'dispatch':
					this.formTitle = '派发';
					this.reset();
					this.ownId = res.row.id;
					this.$request({
						url: interfaceUrl.info + '/' + res.row.id,
						method: 'GET'
					}).then(res => {
						if (res.rCode == 0) {
							this.dispatchForm = res.results;
							this.dispatchOpen = true;
						}
					});
					break;
				case 'add':
					// 新增
					this.reset();
					this.formTitle = '新增';
					const snowflake = new SnowflakeId();
					let id = snowflake.generate();
					this.ownId = id;
					this.form.id = id;
					this.open = true;
					break;
				case 'edit':
					this.reset();
					this.formTitle = '编辑';
					this.ownId = res.row.id;
					this.$request({
						url: interfaceUrl.info + '/' + res.row.id,
						method: 'GET'
					}).then(res => {
						if (res.rCode == 0) {
							this.form = res.results;
							this.$set(this.form, 'lngLat', [this.form.longitude, this.form.latitude]);
							this.open = true;
						}
					});
					break;
				case 'view':
					this.reset();
					this.formTitle = '查看';
					this.ownId = res.row.id;
					this.$request({
						url: interfaceUrl.info + '/' + res.row.id,
						method: 'GET'
					}).then(res => {
						if (res.rCode == 0) {
							this.form = res.results;
							this.$set(this.form, 'lngLat', [this.form.longitude, this.form.latitude]);
							this.open = true;
						}
					});
					break;
				case 'delete':
					this.deleteId = res.row.id;
					this.showDelete = true;
					break;
				default:
					break;
			}
		},
		//提交按钮
		submitForm() {
			this.$refs['form'].validate(valid => {
				if (valid) {
					let url = '';
					if (this.formTitle == '新增') {
						url = interfaceUrl.save;
						let Base64 = require('js-base64').Base64;
						this.form.password = Base64.encode(this.form.pwd);
					} else {
						url = interfaceUrl.update;
					}
					if (this.form.lngLat && this.form.lngLat.length == 2) {
						this.form.longitude = this.form.lngLat[0];
						this.form.latitude = this.form.lngLat[1];
					}
					if (!this.form.longitude || !this.form.latitude) {
						this.$message.error('请正确填写经纬度');
						return false;
					}
					console.log(this.form);
					this.$request({
						url: url,
						data: this.form,
						method: 'POST'
					}).then(res => {
						if (res.rCode == 0) {
							this.$refs.table.reload();
							this.$message.success(this.formTitle + '成功');
							this.cancel();
						} else {
							this.$message.error(res.msg);
						}
						this.showDelete = false;
					});
				}
			});
		},
		//派发按钮
		submitDispatch() {
			this.$refs['dispatchForm'].validate(valid => {
				if (this.value.length > 0) {
					let dispatchObj = JSON.parse(this.value[0].attr);
					this.dispatchForm.dealUserId = dispatchObj.userId; //处置用户id
					this.dispatchForm.dealUserName = dispatchObj.username; //处置用户名称
					this.dispatchForm.dealDept = dispatchObj.depId; //处置部门id
					this.dispatchForm.dealDeptName = dispatchObj.depName; //处置部门名称
					this.dispatchForm.dealUserTel = dispatchObj.phone; //处置人电话
				} else {
					this.$message.error('请选择处置人');
					return false;
				}
				if (valid) {
					let url = interfaceUrl.dispatchHandle;
					console.log(this.dispatchForm);
					this.$request({
						url: url,
						data: this.dispatchForm,
						method: 'POST'
					}).then(res => {
						if (res.rCode == 0) {
							this.$refs.table.reload();
							this.$message.success(this.formTitle + '成功');
							this.cancel();
						} else {
							this.$message.error(res.msg);
						}
						this.showDelete = false;
					});
				}
			});
		},
		//终止按钮
		submitStop() {
			let url = interfaceUrl.breakHandle;
			this.$confirm('确定要终止该事件？', '终止', {
				confirmButtonText: '确定',
				cancelButtonText: '取消',
				type: 'warning'
			})
				.then(() => {
					this.$request({
						url: url,
						data: this.dispatchForm,
						method: 'POST'
					}).then(res => {
						if (res.rCode == 0) {
							this.$refs.table.reload();
							this.$message.success('事件终止成功');
							this.cancel();
						} else {
							this.$message.error(res.msg);
						}
						this.showDelete = false;
					});
				})
				.catch(() => {});
		},
		// 取消按钮
		cancel() {
			this.open = false;
			this.dispatchOpen = false;
			this.reset();
		},
		// 表单重置
		reset() {
			this.form = {
				id: null,
				title: null,
				content: null,
				eventType: null,
				status: null,
				address: null,
				longitude: null,
				latitude: null,
				reportTime: null,
				happenTime: null,
				reportUserId: null,
				reportUserName: null,
				reportUserTel: null,
				remark: null,
				lngLat: [],
				pwd: null
			};
		}
	}
};
</script>
<style scoped lang="scss">
@import '../../../assets/style/style.scss';

.content {
	width: 100%;
	height: 100%;
}

::v-deep {
	.el-dialog {
		// max-height: 100%;
		// top: 35%;
		.el-dialog__body {
			overflow-y: auto;
			.btn-box {
				margin-top: 20px;
				display: flex;
				justify-content: flex-end;

				.btn {
					padding: 5px 10px;
					color: #666;
					border: 1px solid #eee;
					cursor: pointer;
					margin-right: 5px;
					border-radius: 4px;
					// &.theme {
					// 	background: $--color-primary;
					// 	color: #fff;
					// 	border-color: $--color-primary;
					// }
				}
			}
		}
	}
}
.flow-box {
	display: flex;
	.flow-title {
		width: 100px;
		height: 40px;
		line-height: 36px;
		padding: 2px 10px;
		text-align: end;
	}
}
</style>
