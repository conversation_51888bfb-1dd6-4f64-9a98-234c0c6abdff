<template>
	<div class="select-more" :style="{ width: width }">
		<el-input v-model="inputValue" placeholder="请选择内容" readonly class="input-with-select">
			<el-button slot="append" type="primary" :disabled="readonly" @click="showForm = true">
				选择
			</el-button>
		</el-input>
		<es-dialog
			v-if="showForm"
			:title="'选择企业'"
			:visible.sync="showForm"
			:drag="false"
			width="1200px"
			height="94%"
			:close-on-click-modal="false"
			:destroy-on-close="true"
		>
			<div v-loading="loading" class="content">
				<es-tree-group
					class="tree"
					:tree="tree"
					:sync-keys="{ id: 'id', name: 'name' }"
					@node-click="handleChange"
				></es-tree-group>
				<es-data-table
					ref="table"
					:row-style="tableRowClassName"
					:full="true"
					:fit="true"
					:thead="thead"
					:toolbar="toolbar"
					:border="true"
					:page="pageOption"
					:url="dataTableUrl"
					:numbers="true"
					:param="params"
					close
					checkbox
					form
					checked-key="id"
					:checked="selectRowIds"
					@success="success"
					@btnClick="btnClick"
					@sort-change="sortChange"
					@edit="changeTable"
					@select="handleSelection"
					@select-all="handleSelectionAll"
				></es-data-table>
				<div class="content-r">
					<div class="top">选中值</div>
					<div class="tag-box">
						<el-tag
							v-for="item in selectRowData"
							:key="item.id"
							size="medium"
							type="success"
							class="el-tag"
							closable
							@close="closeIcon(item)"
						>
							<span class="text">
								{{ item.corpName }}
							</span>
						</el-tag>
					</div>
				</div>
			</div>
		</es-dialog>
	</div>
</template>

<script>
import interfaceUrl from '@/http/job/jobDualSelect/api.js';
export default {
	name: 'SelectMore',
	props: {
		width: {
			type: String,
			default: '100%'
		},
		list: {
			// 选择的值
			type: Array,
			default: () => {
				return [];
			}
		},
		readonly: {
			type: Boolean,
			default: false
		}
	},
	data() {
		return {
			loading: false,
			inputValue: '',
			tree: {
				defaultExpandAll: true,
				showSearch: false,
				data: [],
				defaultProps: {
					children: 'children',
					label: 'name'
				}
			},

			collegeSelectData: [],
			dataTableUrl: interfaceUrl.enterprisePageList,
			showForm: false,
			formData: {},
			activeNode: {},
			roleFormData: {
				userId: '',
				username: '',
				phone: '',
				selectedRoleList: []
			},
			formTitle: '编辑',
			toolbar: [
				{
					type: 'search',
					contents: [
						{
							type: 'text',
							name: 'keyword',
							placeholder: '关键字查询'
						}
					]
				},
				{
					type: 'button',
					contents: [
						{
							text: '确认选中',
							code: 'toolbar',
							type: 'primary'
						}
					]
				}
			],

			pageOption: {
				layout: 'total, prev, pager, next, jumper',
				pageSize: 10,
				// hideOnSinglePage: true,
				position: 'right',
				current: 1,
				pageNum: 1
			},
			params: {
				asc: 'false',
				orderBy: 'createTime',
				collegeId: ''
			},
			selectRowData: [],
			selectRowIds: [],
			corpNames: []
		};
	},
	computed: {
		thead() {
			return [
				{
					title: '企业名称',
					align: 'left',
					field: 'corpName',
					showOverflowTooltip: true
				},
				{
					title: '统一社会信用代码',
					align: 'left',
					field: 'socialCode',
					showOverflowTooltip: true
				},
				{
					title: '法定代表人',
					align: 'left',
					field: 'lawPerson',
					showOverflowTooltip: true
				},
				{
					title: '企业性质',
					align: 'left',
					field: 'corpType',
					showOverflowTooltip: true
				},
				{
					title: '推荐学院',
					align: 'center',
					field: 'recommenderName'
				}
			];
		}
	},
	watch: {
		list: {
			handler(val) {
				if (val.length > 0) {
					this.selectRowData = val;
					this.toolSelect();
					this.inputValue = this.corpNames.length > 0 ? this.corpNames.join(',') : '';
				}
			},
			deep: true,
			immediate: true
		}
	},
	created() {
		this.initTree();
	},
	methods: {
		/**
		 * 表格行高
		 */
		tableRowClassName({ row, rowIndex }) {
			let styleRes = {
				height: '54px !important'
			};
			return styleRes;
		},
		/**
		 * 树点击事件
		 */
		handleChange(tree, data) {
			this.loading = true;
			this.activeNode = data;
			this.$set(this.params, 'collegeId', data.code);
			this.$refs.table.reload();
		},
		handleSelectionAll(e) {
			const list = this.$refs.table.list;
			// 有值说明是全选
			if (Array.isArray(e) && e.length > 0) {
				// 数组对象去重
				const arr = [...e, ...this.selectRowData];
				const uniqueSet = new Set(arr.map(item => JSON.stringify(item)));
				const uniqueArr = Array.from(uniqueSet).map(item => JSON.parse(item));
				this.selectRowData = uniqueArr;
				this.selectRowIds = this.selectRowData.map(item => item.id);
				this.toolSelect();
			} else {
				// 取消全选一个个删除
				list.forEach(row => {
					this.toolSelect(row);
				});
			}
		},
		// 数据列表多选回调
		handleSelection(selection, row) {
			this.toolSelect(row);
		},
		// 请求成功回调函数
		success() {
			this.toolSelect();
			this.loading = false;
		},
		closeIcon(item) {
			this.toolSelect(item);
			this.$refs.table.clearSelection();
		},
		// 初始化数据
		toolSelect(row) {
			// 更新selectRowIds为selectRowData中所有项的id
			const updateSelectRowIds = () => {
				let selectRowIds = [];
				let corpNames = [];
				this.selectRowData.forEach(item => {
					selectRowIds.push(item.id);
					corpNames.push(item.corpName);
				});
				this.selectRowIds = selectRowIds;
				this.corpNames = corpNames;
				this.$forceUpdate();
			};

			// 当行对象的id不存在时，更新selectRowIds为selectRowData中所有项的id
			if (!row?.id) {
				updateSelectRowIds();
				return;
			}
			// 查找当前行id在selectRowData中的索引
			const idI = this.selectRowData.findIndex(item => item.id === row.id);
			// 如果行id不存在于selectRowData中，将其添加；否则，将其移除
			if (idI === -1) {
				this.selectRowData.push(row);
				updateSelectRowIds();
			} else {
				this.selectRowData.splice(idI, 1);
				updateSelectRowIds();
			}
		},

		/**
		 * 操作按钮点击事件
		 * @param {*} res
		 */
		btnClick(res) {
			let code = res.handle.code;
			switch (code) {
				case 'toolbar':
					this.inputValue = this.corpNames?.length > 0 ? this.corpNames.join(',') : '';
					this.$emit('onSelect', this.selectRowData);
					this.showForm = false;
					break;
				default:
					break;
			}
		},
		/**
		 * 排序变化事件
		 * @param {*} column
		 * @param {*} prop
		 * @param {*} order
		 */
		sortChange(column, prop, order) {
			if (column.order == 'ascending') {
				//升序
				this.params = {
					asc: 'true',
					orderBy: column.prop
				};
			} else if (column.order == 'descending') {
				//降序
				this.params = {
					asc: 'false',
					orderBy: column.prop
				};
			} else {
				//不排序
				this.params = {
					asc: 'false',
					orderBy: 'createTime'
				};
			}
			this.$refs.table.reload();
		},
		changeTable(val) {
			if ('state' === val.name) {
				this.$request({
					url: interfaceUrl.updateState,
					data: {
						id: val.data.id,
						state: val.value
					},
					method: 'POST'
				}).then(res => {
					if (res.rCode == 0) {
						this.$message.success('操作成功');
					} else {
						this.$message.error(res.msg);
					}
				});
			}
		},

		// 初始化左侧树数据
		initTree() {
			if (this.tree.data.length > 0) return;
			this.$request({
				url: interfaceUrl.treeJson,
				method: 'GET'
			}).then(res => {
				if (res.rCode == 0) {
					this.tree.data = res.results;
				}
			});
		},

		// 初始化学院选择树数据
		initCollegeTree() {
			this.$request({
				url: interfaceUrl.collegeTree,
				method: 'POST'
			}).then(res => {
				if (res.rCode == 0) {
					this.collegeSelectData = res.results;
				}
			});
		}
	}
};
</script>
<style scoped lang="scss">
@import '~@/assets/style/style.scss';
.select-more {
	width: 100%;
	.input-with-select {
		width: 100%;

		.prefix {
			height: 100%;
			line-height: 3;
		}
	}
}
.content {
	display: flex;
	width: 100%;
	height: 100%;
	// .tree {
	// 	min-width: 30%;
	// }
	::v-deep .es-data-table {
		width: 75%;
	}
	.content-r {
		width: 25%;
		margin-left: 6px;
		.top {
			margin-bottom: 20px;
		}
		.tag-box {
			height: 93%;
			overflow-y: scroll;

			.el-tag {
				margin-right: 5px;
				margin-bottom: 5px;
				.text {
					display: inline-block;
					max-width: 185px;
					overflow: hidden;
					text-overflow: ellipsis;
					white-space: nowrap;
					line-height: 1;
					// cursor: pointer;
				}
			}
		}
	}
}
</style>
