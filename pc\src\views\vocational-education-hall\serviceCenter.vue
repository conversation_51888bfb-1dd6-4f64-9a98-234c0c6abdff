<template>
	<div class="container">
		<div class="serviceCenter" v-if="!(list.length == 0)">
			<ItemServe
				:title="item.text"
				v-for="(item, index) in list"
				:key="index"
				:dataList="JSON.stringify(item.children)"
			></ItemServe>
		</div>
	</div>
</template>

<script>
import ItemServe from './components/ServiceItem';
// 加载框就是用父窗口的loading
export default {
	components: {
		ItemServe
	},
	data() {
		return {
			list: []
		};
	},
	methods: {
		//获取接口数据
		getListData() {
			this.loading = true;
			this.$request({
				url: '/sys/v1/mecpSys/getSysMenu.dhtml?userId=u6231eda47ddc4714904e686e33111615',
				method: 'GET'
			})
				.then(res => {
					this.list = res.results;
					// if (res.results.length) {
					// 	this.itemContent = res.results[0].children;
					// }
					// this.$nextTick(() => {
					// 	let dom = document.getElementsByClassName('guidance');
					// 	let height = dom[0].clientHeight;
					// 	window.parent.setIframeHeight(height);
					// 	let name = 'frameStart';
					// 	window.parent.postMessage(name, '*');
					// });
				})
				.catch(error => {});
		}
	},
	mounted() {
		this.getListData();
	}
};
</script>

<style lang="scss" scoped>
* {
	margin: 0;
	padding: 0;
}
.container {
	width: 100%;
	background: #f2f5fc;
	height: 100%;
	display: flex;
	flex-direction: column;
	.serviceCenter {
		flex: 1;
		overflow-x: hidden;
		overflow-y: auto;
		box-sizing: border-box;
		.search-box {
			width: 100%;
			height: 48px;
			padding: 12px 12px 0 12px;
		}
	}
}
</style>
