<template>
	<div class="serveHall">
		<!-- 头部 -->
		<div class="serverHallTop">
			<VocationalHeader></VocationalHeader>
		</div>
		<!-- 底部 -->
		<div class="serverHallBotton">
			<div class="boxLeft">
				<div class="boxLeftTop">
					<div class="inforPeople">
						<img src="@/assets/images/serveHall/topPng.png" alt="" width="100%" height="100%" />
						<div class="infor">
							<div class="headPic">
								<div style="width: 60px; height: 60px; overflow: hidden; border-radius: 50%">
									<headAvator
										v-if="_userinfo"
										:own-id="_userinfo.id"
										:user-id="_userinfo.id"
										class="navbar-right-avator"
									/>
								</div>
							</div>
							<div class="center" v-if="_userinfo">
								<div class="top">
									<span class="name">{{ _userinfo.username }}</span>
									<span class="ip">{{ _userinfo.loginname }}</span>
								</div>
								<div class="bottom">
									{{ _userinfo.collegeName
									}}{{ _userinfo.majorName ? ` - ${_userinfo.majorName}` : '' }}
								</div>
							</div>
							<div class="right">
								<div class="content">
									<div class="top">
										<div class="pic">
											<img src="@/assets/images/serveHall/yun.png" alt="" width="100%" />
										</div>
									</div>
									<div class="bottom">我的云盘</div>
								</div>
							</div>
						</div>
					</div>
				</div>
				<div class="boxLeftCenter">
					<div class="headTitle">
						<div class="icon">
							<div class="iconBox">
								<img src="@/assets/images/serveHall/rc.png" alt="" width="14px" />
							</div>
						</div>
						<div class="titleText">我的日程</div>
						<div class="more">
							更多
							<img src="@/assets/images/serveHall/rightArrow.png" alt="" width="10px" />
						</div>
					</div>
					<div class="dateBox">
						<div class="date">
							<div class="dateHead">
								<div class="yearAndMonth">{{ getNowTime() }}</div>
								<div class="dateRightSelect">
									<span
										:class="
											weekNow === selectNowDay ? 'dateSelectNow activeNowDay' : 'dateSelectNow'
										"
										@click="nowDayFunc()"
									>
										今日
									</span>
								</div>
							</div>
							<div class="tableDate">
								<div
									v-for="(item, index) in dateList"
									:key="index"
									class="item"
									@click="dateItemBtn(item)"
								>
									<div class="boxItem">
										<div
											:class="
												weekNow == item.index ? 'activeDateWeekBorder boxItemTop' : 'boxItemTop'
											"
										>
											<div :class="weekNow == item.index ? 'activeDateWeek text' : 'text'">
												{{ item.title }}
											</div>
											<div :class="weekNow == item.index ? 'activeDateWeek number' : 'number'">
												{{ item.day }}
											</div>
										</div>
									</div>
								</div>
							</div>
							<div class="allDay">
								<div class="allDayItem">全天日程</div>
							</div>
							<div class="travel"></div>
						</div>
					</div>
				</div>
				<div class="boxLeftBottom">
					<div class="headTitle">
						<div class="icon">
							<div class="iconBox">
								<img src="@/assets/images/serveHall/notice.png" alt="" width="14px" />
							</div>
						</div>
						<div class="titleText">通知公告</div>
						<div class="more" @click="toList('educationServiceNotice')">
							更多
							<img src="@/assets/images/serveHall/rightArrow.png" alt="" width="10px" />
						</div>
					</div>
					<div v-loading="noticeLoading" class="content">
						<div v-if="noticeList && noticeList.length" style="width: 100%; height: 100%">
							<div
								v-for="(item, index) in noticeList.slice(0, 6)"
								:key="index"
								class="itemContent"
								@click="toDetail(item)"
							>
								<div
									:class="
										index == 0
											? 'activeNoticeOne dot'
											: index == 1
											? 'activeNoticeTwo dot'
											: index == 2
											? 'activeNoticeThree dot'
											: 'dot'
									"
								>
									{{ index + 1 }}
								</div>
								<div class="text">{{ item.title }}</div>
								<div class="time">{{ item.createTime }}</div>
							</div>
						</div>
						<!-- <Empty v-else :tips="'暂无数据'" class="noticeStyle" /> -->
					</div>
				</div>
			</div>
			<div class="boxRight">
				<div class="boxRightTop">
					<div class="RightTopBox">
						<div class="leftBox">
							<div class="headTitle">
								<div class="icon">
									<div class="iconBox">
										<img src="@/assets/images/serveHall/waitDo.png" alt="" width="14px" />
									</div>
								</div>
								<div class="titleText">我的待办</div>
								<div class="more" @click="openNewWindow()">
									更多
									<img src="@/assets/images/serveHall/rightArrow.png" alt="" width="10px" />
								</div>
							</div>
							<div class="leftBoxContent">
								<div class="sixContent">
									<Backlog></Backlog>
								</div>
							</div>
						</div>
						<div class="rightBox">
							<div class="headTitle">
								<div class="icon">
									<div class="iconBox">
										<img src="@/assets/images/serveHall/gn.png" alt="" width="14px" />
									</div>
								</div>
								<div class="titleText">我的常用功能</div>
								<div class="more" @click="openSetUsully()">
									<img src="@/assets/images/serveHall/setBule.png" alt="" width="20px" />
									设置
								</div>
							</div>
							<div class="usullayApp" v-if="usullayAppIf">
								<Usefulapp></Usefulapp>
							</div>
						</div>
					</div>
				</div>
				<div class="boxRightCenter">
					<div class="myApp">
						<div class="headTitle">
							<div class="icon">
								<div class="iconBox">
									<img src="@/assets/images/serveHall/notice.png" alt="" width="14px" />
								</div>
							</div>
							<div class="titleText">我的应用</div>
							<div class="more">
								<div class="more">
									<span @click="myAppBtn('全部应用')">更多</span>
									<img src="@/assets/images/serveHall/rightArrow.png" alt="" width="10px" />
								</div>
							</div>
						</div>
						<div class="appContent">
							<div v-for="(item, index) in myAppData" :key="index" class="appContentItem">
								<div
									v-if="item.name == '' ? false : true"
									class="topIp"
									@click="myAppDataBtn(item)"
								>
									<div class="picIp">
										<div class="imagePic">
											<img :src="item.logo ? item.logo : item.icon" alt="" srcset="" width="50px" />
										</div>
									</div>
									<div class="textIpParent">
										<div class="textIp">{{ item.name }}</div>
									</div>
								</div>
							</div>
						</div>
					</div>
				</div>
				<div class="boxRightBottom">
					<div class="leftBottom">
						<div class="headTitle">
							<div class="icon">
								<div class="iconBox">
									<img src="@/assets/images/serveHall/zl.png" alt="" width="14px" />
								</div>
							</div>
							<div class="titleText">资料下载</div>
							<div class="more" @click="fileDownToWindow()">
								更多
								<img src="@/assets/images/serveHall/rightArrow.png" alt="" width="10px" />
							</div>
						</div>
						<div
							v-if="dataDownloadData && dataDownloadData.length"
							v-loading="dataDownloadDataLoading"
							class="content"
						>
							<div
								v-for="(item, index) in dataDownloadData.slice(0, 5)"
								:key="index"
								class="boxItem"
								@click="fileDownBtn(item)"
							>
								<div class="icon">
									<img
										:src="
											isHavefileTypePng(item.file_ext)
												? require(`@/assets/images/serveHall/zl5.png`)
												: require(`@/assets/images/serveHall/fileType/${item.file_ext}.png`)
										"
										alt=""
										width="15px"
									/>
								</div>
								<div class="textItem">{{ item.file_name }}</div>
								<div class="timeItem">{{ timestampChange(item.create_time) }}</div>
							</div>
						</div>
						<!-- <Empty v-else :tips="'暂无数据'" /> -->
					</div>
					<div class="RightBottom">
						<div class="headTitle">
							<div class="icon">
								<div class="iconBox">
									<img src="@/assets/images/serveHall/zhcx.png" alt="" width="14px" />
								</div>
							</div>
							<div class="titleText">综合查询</div>
							<div class="more" @click="reportEvent()">进入综合指挥调度中心</div>
						</div>
						<div class="zhcxbg">
							<img src="@/assets/images/serveHall/zhcxbg.png" alt="" width="100%" />
						</div>
					</div>
				</div>
			</div>
		</div>

		<es-dialog :title="title" :drag="false" :visible.sync="visible">
			<div class="appBox">
				<div
					v-for="(item, index) in myAppDataTotol"
					:key="index"
					class="appItem"
					@click="myAppDataBtn(item)"
				>
					<div class="appBoxPic">
						<div class="imagePic-box">
							<div class="imagePic">
								<img :src="item.logo ? item.logo : item.icon" alt="" srcset="" width="100%" />
							</div>
						</div>
					</div>
					<div class="appBoxText">{{ item.name }}</div>
				</div>
			</div>
		</es-dialog>

		<!-- 常用应用设置弹框 -->
		<es-dialog
			title="常用功能入口设置"
			size="lg"
			:visible.sync="visibleUsullay"
			:drag="false"
			width="1036px"
		>
			<UsullySet @cancleUuslly="cancleUuslly"></UsullySet>
		</es-dialog>
	</div>
</template>

<script>
import UsullySet from './components/UsullySet.vue';
import Usefulapp from './components/usefulapp.vue';
import Backlog from './components/backlog.vue';
import VocationalHeader from './header/VocationalHeader.vue';
import headAvator from './header/headAvator.vue';
import { tenantId, alumniUrl } from '@/config';
export default {
	name: 'ServeHall',
	components: {
		UsullySet,
		Usefulapp,
		Backlog,
		VocationalHeader,
		headAvator
	},
	created() {
		let value = localStorage.getItem('userInfo');
		let parsedValue = JSON.parse(value);
		this._userinfo = parsedValue;
	},
	data() {
		return {
			usullayAppIf: true,
			_userinfo: {},
			options: [
				{
					value: '周一',
					label: '周一'
				},
				{
					value: '周二',
					label: '周二'
				},
				{
					value: '周三',
					label: '周三'
				},
				{
					value: '周四',
					label: '周四'
				},
				{
					value: '周五',
					label: '周五'
				},
				{
					value: '周六',
					label: '周六'
				},
				{
					value: '周天',
					label: '周天'
				}
			],
			value: '',
			noticeList: [], //通知公告数据
			noticeLoading: false,
			tenantId,
			myAppData: [],
			visible: false,
			visibleUsullay: false,
			title: '我的应用',
			title2: '我的常用',
			myAppDataTotol: [],
			weekNow: 1,
			dateList: [],
			selectNowDay: 2,
			dataDownloadData: [],
			dataDownloadDataLoading: false
		};
	},
	mounted() {
		this.getList();
		this.getInformation();
		this.getWeekDates();
		this.dataDownloadInterface();
	},
	methods: {
		cancleUuslly(type) {
			if (type == '取消') {
				this.visibleUsullay = false;
			} else {
				this.visibleUsullay = false;
				this.usullayAppIf = false;
				this.$nextTick(() => {
					this.usullayAppIf = true;
				});
			}
		},
		openSetUsully() {
			this.visibleUsullay = true;
		},
		fileDownToWindow() {
			window.open(
				alumniUrl + '/project-ybzy/museum-front/backFrame/page/media/resource/archives_list.html'
			);
		},
		openNewWindow() {
			window.open(alumniUrl + '/oa/wfPending/wfPending/list.dhtml?serverId=ybzyDtcSso&authType=6');
		},
		// 指挥调度
		reportEvent() {
			window.open(alumniUrl + '/project-ybzy/ybzy_scheduling/#/scheduling');
		},
		//判断有没有该类型的文件
		isHavefileTypePng(type) {
			let bool = true;
			let typeList = [
				'avi',
				'css',
				'csv',
				'doc',
				'eml',
				'eps',
				'html',
				'jpg',
				'mov',
				'pdf',
				'png',
				'ppt',
				'rar',
				'raw',
				'ttf',
				'txt',
				'wav',
				'xls',
				'zip'
			];
			typeList.forEach(item => {
				if (type == item) {
					bool = false;
				}
			});
			return bool;
		},
		fileDownBtn(item) {
			window.open(
				`${this.$host}/project-ybzy/museum-admin/backFrame/page/media/resource/file_browse.html?archivesId=${item.archives_id}&parentId=${item.folder_id}&fileId=${item.file_id}`
			);
		},
		// 时间戳转2020-01-11
		timestampChange(timestamp) {
			let date = new Date(timestamp);
			let year = date.getFullYear();
			let month = date.getMonth() + 1;
			let data = date.getDate();
			if (month < 10) {
				month = '0' + month;
			}
			if (data < 10) {
				month = '0' + month;
			}
			return `${year}-${month}-${data}`;
		},
		/**资料下载**/
		dataDownloadInterface() {
			this.dataDownloadDataLoading = true;
			let data = {
				tenementId: tenantId
			};
			this.$.ajax({
				url: '/museumcloud/media/file/getOpenArchivesFileList',
				params: data
			}).then(res => {
				this.dataDownloadDataLoading = false;
				this.dataDownloadData = res.data.list;
			});
		},
		dateItemBtn(item) {
			this.weekNow = item.index;
		},
		nowDayFunc() {
			this.weekNow = this.selectNowDay;
		},
		getWeekDates() {
			// 获取当前日期
			let today = new Date();
			let week = today.getDay();
			this.weekNow = week;
			this.selectNowDay = week;
			let startDay = null;
			let arr = [];
			if (week == 0) {
				startDay = new Date(today.getFullYear(), today.getMonth(), today.getDate());
				for (let index = 0; index < 7; index++) {
					let obj = {};
					switch (index) {
						case 0:
							obj.title = '日';
							obj.index = index;
							break;
						case 1:
							obj.title = '一';
							obj.index = index;
							break;
						case 2:
							obj.title = '二';
							obj.index = index;
							break;
						case 3:
							obj.title = '三';
							obj.index = index;
							break;
						case 4:
							obj.title = '四';
							obj.index = index;
							break;
						case 5:
							obj.title = '五';
							obj.index = index;
							break;
						case 6:
							obj.title = '六';
							obj.index = index;
							break;
						default:
							break;
					}
					obj.day = new Date(
						startDay.getFullYear(),
						startDay.getMonth(),
						startDay.getDate() + index
					).getDate();
					arr.push(obj);
				}
				console.log(startDay, '1');
			} else {
				startDay = new Date(today.getFullYear(), today.getMonth(), today.getDate() - week);
				for (let index = 0; index < 7; index++) {
					let obj = {};
					switch (index) {
						case 0:
							obj.title = '日';
							obj.index = index;
							break;
						case 1:
							obj.title = '一';
							obj.index = index;
							break;
						case 2:
							obj.title = '二';
							obj.index = index;
							break;
						case 3:
							obj.title = '三';
							obj.index = index;
							break;
						case 4:
							obj.title = '四';
							obj.index = index;
							break;
						case 5:
							obj.title = '五';
							obj.index = index;
							break;
						case 6:
							obj.title = '六';
							obj.index = index;
							break;
						default:
							break;
					}
					obj.day = new Date(
						startDay.getFullYear(),
						startDay.getMonth(),
						startDay.getDate() + index
					).getDate();
					arr.push(obj);
				}
			}
			this.dateList = arr;
		},
		// 获取年月
		getNowTime() {
			let date = new Date();
			let year = date.getFullYear();
			let month = date.getMonth() + 1;
			if (month < 10) {
				month = '0' + month;
			}
			return `${year}年${month}月`;
		},
		// 跳转详情页面
		toList(code) {
			// this.jumpPage(`/information-list?code=${code}`);
			window.open(`${alumniUrl}/project-ybzy/ybzy_zhxy/index.html#/information-list?code=${code}}`);
		},
		/**
		 * @description 点击跳转对应页面
		 * */
		jumpPage(url) {
			this.$router.push(url);
		},
		toDetail(item) {
			// this.jumpPage(`/information-detail?id=${item.id}&code=${item.nodeCode}`);
			window.open(
				`${alumniUrl}/project-ybzy/ybzy_zhxy/index.html#/information-detail?id=${item.id}&code=${item.nodeCode}`
			);
		},
		/**
		 * @description 获取列表
		 * */
		getInformation() {
			this.noticeLoading = true;
			this.loading = true;
			let data = {
				nodeCode: 'educationServiceNotice',
				// tenantId: this._userinfo.tenantId || tenantId,
				tenantId: tenantId,
				pageNum: 1,
				pageSize: 6
			};
			this.$.ajax({
				url: '/ybzy/cmsinfo/front/paging',
				params: data
			}).then(res => {
				this.noticeList = res?.results?.records || [];
				this.noticeLoading = false;
			});
		},
		myAppDataBtn(item) {
			// /ybzy/platauth/front/loginEoss?redirectUri=/project-ybzy/ybzy/index.html#/main?cookie=1
			if (item.url.includes('redirectUri=/project-ybzy/ybzy/index.html')) {
				window.location.href = item.url + '&isTeacher=true';
			} else {
				window.open(item.url);
			}
		},
		handleClose() {},
		myAppBtn(name) {
			this.title = name;
			this.visible = true;
		},
		/**获取外链*/
		getList() {
			this.$.ajax({
				url: '/ybzy/platuser/front/appList',
				method: 'post'
			}).then(res => {
				if (res.rCode === 0) {
					this.myAppDataTotol = JSON.parse(JSON.stringify(res.results));
					this.myAppData = res.results || [];
					if (this.myAppData.length < 10) {
						let number = 10 - this.myAppData.length;
						for (let index = 0; index < number; index++) {
							let obj = {};
							obj.name = '';
							obj.src = '';
							this.myAppData.push(obj);
						}
					} else if (this.myAppData.length > 10) {
						let newArr = this.myAppData.slice(0, 10);
						this.myAppData = newArr;
					}
				} else {
					this.$message.error(res.msg);
				}
			});
		}
	}
};
</script>

<style lang="scss" scoped>
.serveHall {
	width: 100%;
	height: 100%;
	background: #ecf0f4;
	// display: flex;
	// padding: 16px;
	min-width: 1550px;
	position: relative;
	overflow: auto;
	.serverHallTop {
		width: 100%;
		// background: red;
	}
	.serverHallBotton {
		width: 100%;
		display: flex;
		padding: 16px;
		.boxLeft {
			width: 500px;
			height: 982px;
			.boxLeftTop {
				width: 100%;
				height: 100px;
				overflow: hidden;
				border-radius: 6px;
				.inforPeople {
					width: 100%;
					height: 100%;
					overflow: hidden;
					position: relative;
					.infor {
						top: 0;
						left: 0;
						position: absolute;
						width: 100%;
						height: 100%;
						display: flex;
						align-items: center;
						.headPic {
							width: 130px;
							height: 70px;
							display: flex;
							padding: 0 0 0 20px;
							justify-content: center;
							align-items: center;
						}
						.center {
							flex: 1;
							height: 70px;
							.top {
								width: 100%;
								height: 50px;
								line-height: 50px;
								display: flex;
								.name {
									display: inline-block;
									margin-right: 10px;
									color: #ffffff;
									font-weight: bold;
									font-size: 20px;
									line-height: 50px;
								}
								.ip {
									display: inline-block;
									flex: 1;
									font-size: 14px;
									font-weight: 400;
									color: #ffffff;
									line-height: 54px;
								}
							}
							.bottom {
								color: #ffffff;
								font-size: 14px;
								font-weight: 400;
							}
						}
						.right {
							width: 140px;
							height: 70px;
							display: flex;
							justify-content: center;
							align-items: center;
							.content {
								width: 80px;
								height: 70px;
								cursor: pointer;
								.top {
									width: 100%;
									height: 50px;
									display: flex;
									justify-content: center;
									align-items: center;
									.pic {
										overflow: hidden;
										width: 32px;
										height: 32px;
									}
								}
								.bottom {
									width: 100%;
									height: 20px;
									line-height: 20px;
									font-size: 8pt;
									text-align: center;
									color: #ffffff;
									cursor: pointer;
								}
							}
						}
					}
				}
			}
			//height 100
			.boxLeftCenter {
				margin-top: 20px;
				width: 100%;
				height: 504px;
				border-radius: 6px;
				background: #fff;
				overflow: hidden;
				.headTitle {
					width: 100%;
					height: 60px;
					display: flex;
					align-items: center;
					position: relative;
					.icon {
						width: 62px;
						height: 26px;
						padding: 0 14px 0 20px;
						.iconBox {
							width: 26px;
							height: 26px;
							background: linear-gradient(-42deg, #1f6aff, #689cff);
							box-shadow: 0px 3px 7px 0px rgba(94, 102, 205, 0.35);
							border-radius: 8px;
							display: flex;
							justify-content: center;
							align-items: center;
						}
					}
					.titleText {
						flex: 1;
						height: 60px;
						line-height: 60px;
						color: #333333;
						font-size: 16px;
						font-weight: bold;
					}
					.more {
						right: 20px;
						position: absolute;
						width: 50px;
						height: 100%;
						line-height: 60px;
						font-size: 14px;
						font-weight: 400;
						color: var(--brand-6, #0076e8);
						display: flex;
						align-items: center;
						cursor: pointer;
					}
				}
				.dateBox {
					width: 100%;
					height: 160px;
					padding: 0 20px;
					.date {
						width: 100%;
						height: 100%;
						background: #faf9ff;
						.dateHead {
							width: 100%;
							height: 60px;
							position: relative;
							.yearAndMonth {
								width: 140px;
								height: 60px;
								line-height: 60px;
								margin-left: 19px;
								font-size: 14px;
								font-weight: 400;
								color: #7a8392;
							}
							.dateRightSelect {
								position: absolute;
								width: 120px;
								right: 20px;
								top: 0;
								height: 100%;
								display: flex;
								justify-content: right;
								align-items: center;
								.dateSelectNow {
									cursor: pointer;
									display: inline-block;
									width: 44px;
									height: 24px;
									border-radius: 12px;
									background: rgba(255, 255, 255, 0);
									border: 1px solid #d2d2d2;
									text-align: center;
									font-size: 14px;
									font-weight: 400;
									line-height: 24px;
								}
								.dateSelectWeek {
									margin-left: 8px;
									cursor: pointer;
									display: inline-block;
									width: 50px;
									height: 24px;
									border-radius: 12px;
									background: rgba(255, 255, 255, 0);
									border: 1px solid var(--brand-6, #0076e8);
									color: var(--brand-6, #0076e8);
									text-align: center;
									font-size: 14px;
									font-weight: 400;
									line-height: 24px;
								}
							}
						}
						.tableDate {
							width: 100%;
							height: 90px;
							display: flex;
							overflow: hidden;
							.item {
								cursor: pointer;
								flex: 1;
								height: 100%;
								display: flex;
								text-align: center;
								justify-content: center;
								.boxItem {
									width: 38px;
									height: 100%;
									.boxItemTop {
										width: 100%;
										height: 76px;
										background: #faf9ff;
										border-radius: 19px;
										.text {
											height: 44px;
											line-height: 44px;
											width: 100%;
											font-size: 14px;
											color: #7a8392;
										}
										.number {
											height: 20px;
											line-height: 20px;
											width: 100%;
											font-size: 20px;
											font-weight: bold;
											color: #333333;
										}
									}
									.dot {
										width: 100%;
										height: 14px;
										display: flex;
										justify-content: center;
										align-items: center;
										.dotdot {
											width: 9px;
											height: 9px;
											border-radius: 9px;
											background: #ff3333;
										}
									}
								}
							}
						}
						.allDay {
							width: 100%;
							height: 36px;
							margin-top: 20px;
							.allDayItem {
								padding: 0 10px;
								width: 100%;
								height: 100%;
								background: #f0f3fc;
								line-height: 36px;
								font-size: 14px;
								font-weight: bold;
								color: #333333;
							}
						}
						.travel {
							margin-top: 20px;
							width: 100%;
							height: 210px;
							.travelItem {
								width: 100%;
								.headTravel {
									width: 100%;
									height: 13px;
									display: flex;
									align-items: center;
									.dot {
										width: 13px;
										height: 13px;
										border-radius: 13px;
										background: 50%;
										background: #cddcf6;
									}
									.textContentTime {
										padding: 0 0 0 11px;
										font-weight: 400;
										color: #7a8392;
										font-size: 14px;
										line-height: 13px;
									}
								}
								.go {
									width: 100%;
									margin-left: 6px;
									border-left: 1px solid #e8e9ee;
									padding: 10px 0 10px 16px;
									.content {
										background: #f8f8f8;
										border-radius: 6px;
										.textStylehead {
											padding: 0 0 0 19px;
											height: 40px;
											line-height: 44px;
											font-weight: bold;
											color: #333333;
											font-size: 15px;
											overflow: hidden;
											text-overflow: ellipsis;
											white-space: nowrap;
										}
										.textStylecon {
											padding: 0 0 0 19px;
											height: 32px;
											font-weight: 400;
											color: #7a8392;
											font-size: 14px;
										}
									}
								}
							}
						}
					}
				}
			}
			//height 524
			.boxLeftBottom {
				margin-top: 20px;
				width: 100%;
				height: 336px;
				background: #ffffff;
				border-radius: 6px;
				.headTitle {
					width: 100%;
					height: 60px;
					display: flex;
					align-items: center;
					position: relative;
					.icon {
						width: 62px;
						height: 26px;
						padding: 0 14px 0 20px;
						.iconBox {
							width: 26px;
							height: 26px;
							background: linear-gradient(-42deg, #1f6aff, #689cff);
							box-shadow: 0px 3px 7px 0px rgba(94, 102, 205, 0.35);
							border-radius: 8px;
							display: flex;
							justify-content: center;
							align-items: center;
						}
					}
					.titleText {
						flex: 1;
						height: 60px;
						line-height: 60px;
						color: #333333;
						font-size: 16px;
						font-weight: bold;
					}
					.more {
						right: 20px;
						position: absolute;
						width: 50px;
						height: 100%;
						line-height: 60px;
						font-size: 14px;
						font-weight: 400;
						color: var(--brand-6, #0076e8);
						display: flex;
						align-items: center;
						cursor: pointer;
					}
				}
				.content {
					width: 100%;
					height: 276px;
					padding: 0 20px;
					.itemContent {
						cursor: pointer;
						width: 100%;
						height: 46px;
						line-height: 46px;
						border-bottom: 1px solid #ebebeb;
						display: flex;
						align-items: center;
						.dot {
							width: 18px;
							height: 18px;
							background: #ecf4ff;
							font-weight: normal;
							color: #878787;
							line-height: 18px;
							font-size: 12px;
							text-align: center;
							border-radius: 50%;
						}
						.text {
							padding: 0 0 0 10px;
							flex: 1;
							height: 46px;
							line-height: 46px;
							font-size: 14px;
							font-weight: 400;
							color: #666666;
							overflow: hidden;
							text-overflow: ellipsis;
							white-space: nowrap;
							padding: 0 18px 0 0;
						}
						.time {
							width: 140px;
							height: 46px;
							line-height: 46px;
							font-size: 14px;
							font-weight: 400;
							color: #7a8392;
						}
					}
				}
			}
			//356
		}
		.boxRight {
			height: 982px;
			min-width: 600px;
			flex: 1;
			padding: 0 0 0 20px;
			// background: yellow;
			.boxRightTop {
				width: 100%;
				min-width: 600px;
				height: 470px;
				.RightTopBox {
					width: 100%;
					height: 100%;
					display: flex;
					.leftBox {
						width: 56%;
						height: 100%;
						background: #ffffff;
						border-radius: 6px;
						.headTitle {
							width: 100%;
							height: 60px;
							display: flex;
							align-items: center;
							position: relative;
							.icon {
								width: 62px;
								height: 26px;
								padding: 0 14px 0 20px;
								.iconBox {
									width: 26px;
									height: 26px;
									background: linear-gradient(-42deg, #1f6aff, #689cff);
									box-shadow: 0px 3px 7px 0px rgba(94, 102, 205, 0.35);
									border-radius: 8px;
									display: flex;
									justify-content: center;
									align-items: center;
								}
							}
							.titleText {
								flex: 1;
								height: 60px;
								line-height: 60px;
								color: #333333;
								font-size: 16px;
								font-weight: bold;
							}
							.more {
								right: 20px;
								position: absolute;
								width: 50px;
								height: 100%;
								line-height: 60px;
								font-size: 14px;
								font-weight: 400;
								color: var(--brand-6, #0076e8);
								display: flex;
								align-items: center;
								cursor: pointer;
							}
						}
						.leftBoxContent {
							width: 100%;
							height: 410px;
							.tabBar {
								padding: 0 0 0 20px;
								width: 100%;
								height: 30px;
								line-height: 30px;
								border-bottom: 1px solid #f0f3fc;
								.tabBarItem {
									cursor: pointer;
									display: inline-block;
									width: 80px;
									height: 100%;
									font-weight: bold;
									color: #7a8392;
									font-size: 14px;
								}
							}
							.sixContent {
								width: 100%;
								height: 380px;
								.sixContentItem {
									padding: 0 0 0 20px;
									width: 100%;
									height: 61px;
									line-height: 63px;
									display: flex;
									align-items: center;
									.dot {
										width: 13px;
										height: 13px;
										background: #ec993e;
										border-radius: 50%;
									}
									.text {
										flex: 1;
										height: 100%;
										padding: 0 4px 0 17px;
										display: flex;
										align-items: center;
										overflow: hidden;
										position: relative;
										.urgency {
											display: inline-block;
											width: 40px;
											height: 24px;
											background: linear-gradient(-28deg, #ff7a29 0%, #ff1717 100%);
											border-radius: 8px;
											font-size: 12px;
											font-family: Microsoft YaHei;
											font-weight: 400;
											color: #ffffff;
											line-height: 24px;
											text-align: center;
											cursor: default;
										}
										.textContent {
											padding: 0 8px;
											display: inline-block;
											height: 24px;
											line-height: 24px;
											font-size: 14px;
											font-family: Microsoft YaHei;
											font-weight: bold;
											color: #333333;
											overflow: hidden;
											white-space: nowrap;
											text-overflow: ellipsis;
										}
										.wait {
											display: inline-block;
											width: 60px;
											height: 22px;
											background: #ecf4ff;
											font-size: 12px;
											font-family: Microsoft YaHei;
											font-weight: 400;
											color: var(--brand-6, #0076e8);
											line-height: 22px;
											text-align: center;
											cursor: default;
										}
										.sendTimeConetent {
											padding: 0 4px 0 17px;
											width: 100%;
											height: 20px;
											bottom: 0;
											left: 0;
											position: absolute;
											display: flex;
											.sendName {
												display: inline-block;
												width: 140px;
												height: 28px;
												line-height: 28px;
												font-size: 14px;
												font-family: Microsoft YaHei;
												font-weight: 400;
												color: #7a8392;
											}
											.sendTime {
												display: inline-block;
												width: 200px;
												height: 28px;
												line-height: 28px;
												font-size: 14px;
												font-family: Microsoft YaHei;
												font-weight: 400;
												color: #7a8392;
											}
										}
									}
								}
							}
						}
					}
					.rightBox {
						min-width: 500px;
						margin-left: 20px;
						flex: 1;
						height: 100%;
						background: #ffffff;
						border-radius: 6px;
						.headTitle {
							width: 100%;
							height: 60px;
							display: flex;
							align-items: center;
							position: relative;
							.icon {
								width: 62px;
								height: 26px;
								padding: 0 14px 0 20px;
								.iconBox {
									width: 26px;
									height: 26px;
									background: linear-gradient(-42deg, #1f6aff, #689cff);
									box-shadow: 0px 3px 7px 0px rgba(94, 102, 205, 0.35);
									border-radius: 8px;
									display: flex;
									justify-content: center;
									align-items: center;
								}
							}
							.titleText {
								flex: 1;
								height: 60px;
								line-height: 60px;
								color: #333333;
								font-size: 16px;
								font-weight: bold;
							}
							.more {
								right: 20px;
								position: absolute;
								width: 50px;
								height: 100%;
								line-height: 60px;
								font-size: 14px;
								font-weight: 400;
								color: var(--brand-6, #0076e8);
								display: flex;
								align-items: center;
								cursor: pointer;
							}
						}
						.usullayApp {
							width: 100%;
							height: 380px;
							padding: 0 30px;
							.rowUsullay {
								width: 100%;
								height: 130px;
								display: flex;
								.rowUsullayItem {
									flex: 1;
									height: 100%;
									display: flex;
									justify-content: center;
									.itemUsullay {
										width: 70px;
										height: 100%;

										.topPicUsullay {
											width: 100%;
											height: 70px;
											border-radius: 50%;
											display: flex;
											justify-content: center;
											align-items: center;
											background: #4545ef;
										}
										.textUsullay {
											width: 100%;
											text-align: center;
											height: 36px;
											line-height: 36px;
											font-size: 14px;
											font-family: Microsoft YaHei;
											font-weight: bold;
											color: #333333;
										}
									}
								}
							}
						}
					}
				}
			}
			.boxRightCenter {
				margin-top: 18px;
				width: 100%;
				height: 200px;
				border-radius: 6px;
				overflow: hidden;
				.myApp {
					width: 100%;
					height: 200px;
					background: #ffffff;
					.headTitle {
						width: 100%;
						height: 60px;
						display: flex;
						align-items: center;
						position: relative;
						.icon {
							width: 62px;
							height: 26px;
							padding: 0 14px 0 20px;
							.iconBox {
								width: 26px;
								height: 26px;
								background: linear-gradient(-42deg, #1f6aff, #689cff);
								box-shadow: 0px 3px 7px 0px rgba(94, 102, 205, 0.35);
								border-radius: 8px;
								display: flex;
								justify-content: center;
								align-items: center;
							}
						}
						.titleText {
							flex: 1;
							height: 60px;
							line-height: 60px;
							color: #333333;
							font-size: 16px;
							font-weight: bold;
						}
						.more {
							right: 10px;
							position: absolute;
							width: 40px;
							height: 100%;
							line-height: 60px;
							font-size: 14px;
							font-weight: 400;
							color: var(--brand-6, #0076e8);
							display: flex;
							align-items: center;
							cursor: pointer;
							.fen {
								display: inline-block;
								width: 10px;
								text-align: center;
							}
						}
					}
					.appContent {
						width: 100%;
						height: 140px;
						display: flex;
						overflow: hidden;
						padding: 0 4px;
						.appContentItem {
							height: 100%;
							// width: 110px;
							flex: 1;
							display: flex;
							justify-content: center;
							.topIp {
								// background: #1f6aff;
								// border: 1px solid red;
								width: 100%;
								height: 140px;
								overflow: hidden;
								justify-content: center;
								cursor: pointer;
								.picIp {
									width: 100%;
									height: 70px;
									display: flex;
									justify-content: center;
									.imagePic {
										width: 70px;
										height: 70px;
										// background: #ecf4ff;
										border-radius: 20px;
										display: flex;
										justify-content: center;
										align-items: center;
									}
								}
								.textIpParent {
									width: 100%;
									height: 100%;
									display: flex;
									justify-content: center;
									.textIp {
										// background: red;
										// width: 100%;
										width: 100px;
										padding: 0;
										height: 40px;
										line-height: 40px;
										text-align: center;
										font-size: 14px;
										font-family: Microsoft YaHei;
										font-weight: bold;
										color: #333333;
										white-space: nowrap;
										overflow: hidden;
										text-overflow: ellipsis;
									}
								}
							}
						}
					}
				}
			}
			.boxRightBottom {
				margin-top: 20px;
				width: 100%;
				height: 272px;
				display: flex;
				.leftBottom {
					width: 56%;
					height: 100%;
					background: #ffffff;
					border-radius: 6px;
					overflow: hidden;
					.headTitle {
						width: 100%;
						height: 60px;
						display: flex;
						align-items: center;
						position: relative;
						.icon {
							width: 62px;
							height: 26px;
							padding: 0 14px 0 20px;
							.iconBox {
								width: 26px;
								height: 26px;
								background: linear-gradient(-42deg, #1f6aff, #689cff);
								box-shadow: 0px 3px 7px 0px rgba(94, 102, 205, 0.35);
								border-radius: 8px;
								display: flex;
								justify-content: center;
								align-items: center;
							}
						}
						.titleText {
							flex: 1;
							height: 60px;
							line-height: 60px;
							color: #333333;
							font-size: 16px;
							font-weight: bold;
						}
						.more {
							right: 20px;
							position: absolute;
							width: 50px;
							height: 100%;
							line-height: 60px;
							font-size: 14px;
							font-weight: 400;
							color: var(--brand-6, #0076e8);
							display: flex;
							align-items: center;
							cursor: pointer;
						}
					}
					.content {
						width: 100%;
						height: 212px;
						padding: 0 19px 0 26px;
						.boxItem {
							width: 100%;
							height: 42px;
							border-bottom: 1px dotted #ebebeb;
							display: flex;
							align-items: center;
							cursor: pointer;
							.icon {
								width: 15px;
								height: 20px;
							}
							.textItem {
								padding: 0 0 0 8px;
								flex: 1;
								height: 100%;
								line-height: 42px;
								font-size: 14px;
								color: #666666;
								font-weight: 400;
								white-space: nowrap;
								overflow: hidden;
								text-overflow: ellipsis;
							}
							.timeItem {
								height: 100%;
								line-height: 42px;
								font-size: 12px;
								color: #808897;
								font-weight: 400;
							}
						}
					}
				}
				.RightBottom {
					margin-left: 20px;
					flex: 1;
					height: 100%;
					background: #ffffff;
					border-radius: 6px;
					.headTitle {
						width: 100%;
						height: 60px;
						display: flex;
						align-items: center;
						position: relative;
						.icon {
							width: 62px;
							height: 26px;
							padding: 0 14px 0 20px;
							.iconBox {
								width: 26px;
								height: 26px;
								background: linear-gradient(-42deg, #1f6aff, #689cff);
								box-shadow: 0px 3px 7px 0px rgba(94, 102, 205, 0.35);
								border-radius: 8px;
								display: flex;
								justify-content: center;
								align-items: center;
							}
						}
						.titleText {
							flex: 1;
							height: 60px;
							line-height: 60px;
							color: #333333;
							font-size: 16px;
							font-weight: bold;
						}
						.more {
							right: 20px;
							position: absolute;
							width: 140px;
							height: 100%;
							line-height: 60px;
							font-size: 14px;
							font-weight: 400;
							color: var(--brand-6, #0076e8);
							display: flex;
							align-items: center;
							cursor: pointer;
						}
					}
					.zhcxbg {
						// background: hotpink;
						display: flex;
						justify-content: center;
						align-items: center;
						padding: 0 20px;
						width: 100%;
						overflow: hidden;
						height: calc(100% - 80px);
					}
				}
			}
		}
	}
}

::v-deep .el-dialog__body {
	overflow-y: scroll;
}
.appBox {
	width: 100%;
	display: flex;
	flex-wrap: wrap;
	// justify-content: space-between;
	padding-top: 10px;
	overflow: auto;
	.appItem {
		cursor: pointer;
		width: 140px;
		height: 100px;
		margin: 0 3px 26px 3px;
		// margin-bottom: 26px;
		.appBoxPic {
			width: 100%;
			height: 70px;
			// background: #ffffff;
			// border: 1px solid #c5d1e0;
			// border-radius: 10px;
			display: flex;
			justify-content: center;
			align-items: center;
			// background: #ecf4ff;
			.imagePic-box {
				width: 70px;
				height: 70px;
				background: #ffffff;
				border: 1px solid #c5d1e0;
				border-radius: 10px;
				display: flex;
				justify-content: center;
				align-items: center;
				background: #ecf4ff;
				.imagePic {
					width: 42px;
					height: 42px;
				}
			}
		}
		.appBoxText {
			margin-top: 6px;
			width: 100%;
			height: 40px;
			text-align: center;
			font-size: 14px;
			font-family: Microsoft YaHei;
			font-weight: 400;
			color: #7a8392;
			// line-height: 30px;
			// white-space: nowrap;
			// overflow: hidden;
			// text-overflow: ellipsis;
			white-space: normal;
			text-overflow: ellipsis;
			overflow: hidden;
			display: -webkit-box;
			-webkit-line-clamp: 2;
			-webkit-box-orient: vertical;
		}
	}
}
.noticeStyle {
	padding: 30px 0 !important;
}

.activeNoticeOne {
	background: #e63b0d !important;
	color: #ffffff !important;
}
.activeNoticeTwo {
	background: #f1c40f !important;
	color: #ffffff !important;
}
.activeNoticeThree {
	background: #3498db !important;
	color: #ffffff !important;
}

.activeDateWeek {
	color: #fff !important;
}
.activeDateWeekBorder {
	background: #0076e8 !important;
}
.activeNowDay {
	border: 1px solid #0076e8 !important;
	color: #0076e8 !important;
}
.empty {
	padding: 0;
}

/* 视口滚动条 */
::-webkit-scrollbar {
	width: 6px; /* 滚动条宽度 */
}

/* 滚动条轨道 */
::-webkit-scrollbar-track {
	background-color: #f1f1f1; /* 滚动条轨道背景色 */
}

/* 滚动条滑块 */
::-webkit-scrollbar-thumb {
	background-color: #888; /* 滚动条滑块颜色 */
	border-radius: 5px; /* 滚动条滑块圆角 */
}

/* 鼠标悬停在滚动条上时的滑块样式 */
::-webkit-scrollbar-thumb:hover {
	background-color: #555; /* 滚动条滑块颜色 */
}
</style>
