<template>
	<!-- 课表 -->
	<div class="performance">
		<div class="performance-title">成绩</div>
		<!-- 内容区域 -->
		<div class="performance-content">
			<es-data-table
				v-loading="loading"
				:key="keyI"
				:thead="thead"
				:page="page"
				:data="tableData"
				:toolbar="toolbar"
				seach-value="111"
				:search-value="searchValue"
				retain-searh
				style="width: 100%"
				:full="true"
				@search="onSearch"
				@reset="onSearch({})"
			></es-data-table>
		</div>
	</div>
</template>

<script>
import { xodbApi2 } from '@/api/xodb';

export default {
	components: {},
	props: {},
	data() {
		return {
			scale: 1,
			toolbar: [
				{
					type: 'search',
					reset: true,
					contents: [
						{
							type: 'select',
							label: '下拉选择',
							placeholder: '请选择学年',
							name: 'xn',
							event: 'multipled',
							// valueType: 'object',
							collapseTags: true,
							'label-key': 'name',
							'value-key': 'value',
							//autoComplete: true,
							//url:'../json/select.json',
							data: (() => {
								// 获取现在的年
								let nowYear = new Date().getFullYear();
								const arr = [];
								for (let i = 0; i < 20; i++) {
									arr.push({
										value: `${nowYear - i - 1}-${nowYear - i}`,
										name: `${nowYear - i - 1}年-${nowYear - i}年`
									});
								}
								return arr;
							})(),
							verify: 'required',
							col: 6
						},
						{
							type: 'select',
							label: '下拉选择',
							placeholder: '请选择学期',
							name: 'xqdm',
							event: 'multipled',
							// valueType: 'object',
							collapseTags: true,
							//autoComplete: true,
							//url:'../json/select.json',
							data: [
								{
									value: '1',
									name: '第一学期'
								},
								{
									value: '2',
									name: '第二学期'
								}
							],
							verify: 'required',
							col: 6
						}
					]
				}
			],
			thead: [
				{
					title: '学年',
					field: 'xn',
					showOverflowTooltip: true
				},
				{
					title: '学期',
					field: 'xqdm',
					showOverflowTooltip: true
				},
				{
					title: '课程名称',
					field: 'kcmc',
					showOverflowTooltip: true
				},
				{
					title: '课程代码',
					field: 'kcbh',
					showOverflowTooltip: true
				},
				{
					title: '学分',
					field: 'xf',
					showOverflowTooltip: true
				},
				{
					title: '成绩',
					field: 'cj',
					showOverflowTooltip: true
				},
				{
					title: '绩点',
					field: 'jd',
					showOverflowTooltip: true
				},
				{
					title: '课程性质',
					field: 'kcxz',
					showOverflowTooltip: true
				},
				{
					title: '考试性质',
					field: 'ksxz',
					showOverflowTooltip: true
				}
			],
			loading: false,
			keyI: 0,
			searchValue: {},
			page: {
				pageSize: 10,
				totalCount: 0
			},
			tableData: []
		};
	},
	computed: {
		loginUserInfo() {
			return JSON.parse(localStorage.getItem('loginUserInfo')) || {};
		}
	},
	created() {
		this.scale = this.$utils.toolViewRatio();
	},
	mounted() {
		this.getDataN('ads_jx_xskscjb_query', 'tableData'); // 学生考试成绩表查询服务
	},
	methods: {
		onSearch(e) {
			this.searchValue = e;
			this.getDataN('ads_jx_xskscjb_query', 'tableData', e);
		},
		async getDataN(url, listName, params) {
			const LeaderRole = localStorage.getItem('LeaderRole');
			this.loading = true;
			try {
				const {
					data: { list }
				} = await xodbApi2.post({
					url,
					pageSize: 300,
					params: {
						// xyjgbh: '1',
						xn: '', //学年
						xqdm: '', //学期
						...params,
						// xh: '202311176', //学号
						xh: this.loginUserInfo.code,
						xyjgbh: LeaderRole === 'xLeader' ? '1' : this.loginUserInfo.orgCode

					}
				});
				this.loading = false;
				switch (listName) {
					case 'tableData':
						this[listName] = list;
						this.keyI++;
						break;
				}
			} catch (error) {
				this.loading = false;
				console.error(`处理数据失败${url}:`, error);
			}
		}
	}
};
</script>

<style lang="scss" scoped>
.performance {
	// width: 1826px;
	// height: 934px;
	width: 100%;
	transform-origin: top left;
	background: #e7f6ff;
	padding: 29px 11px 13px 12px;
	border-radius: 5px;
	overflow: hidden;
	&-title {
		font-family: MicrosoftYaHei, MicrosoftYaHei;
		font-weight: bold;
		font-size: 24px;
		color: #454545;
		line-height: 31px;
		margin-left: 27px;
	}
	&-content {
		margin-top: 22px;
	}
	.class-card {
		background: rgba(255, 255, 255, 0.25);
		box-shadow: 0px 0px 6px 0px rgba(0, 0, 0, 0.09);
		border-radius: 3px;
		border: 1px solid #ffffff;
		padding: 12px 10px 6px;
		margin-bottom: 6px;
		font-size: 12px;
		color: #454545;
		line-height: 16px;
		font-style: normal;
		text-align: left;
		.title {
			font-weight: bold;
			color: #053b6d;
			font-style: normal;
		}
		.tag-item {
			display: inline-block;
			padding: 3px 13px;
			background: #e2fcff;
			border-radius: 2px;
			border: 1px solid #17d7ec;
			margin-right: 4px;
		}
		.tag-item + .tag-item {
			background: #f9e1f9;
			border-radius: 2px;
			border: 1px solid #ff81e4;
		}
		> p {
			margin-bottom: 6px;
		}
	}
	.colse-cell {
		font-size: 14px;
		color: #0275e8;
		line-height: 19px;
		font-style: normal;
		margin-top: 10px;
		cursor: pointer;
	}
}
::v-deep .performance-content {
	height: calc(85vh - 131px);
	.el-table__row .colseCellName .cell {
		height: 60px;
		overflow: auto;
	}
	.el-table .cell {
		padding: 0 !important;
	}
	.el-scrollbar__wrap {
		background: #ecf6ff;
	}
	.el-table th,
	.el-table tr {
		background-color: #ecf6ff !important;
	}
	.el-table--border th,
	.el-table--border td {
		border: 1px solid #d2dee2;
	}
	.es-toolbar {
		background-color: #ecf6ff !important;
	}
	.es-data-table-content {
		background-color: #ecf6ff !important;
	}
	.es-table-page {
		background-color: #ecf6ff !important;
	}
}
</style>
