<template>
	<div class="main">
		<div class="body">
			<es-tree-group
				ref="refEsTreeGroup"
				:table="table"
				:tree="{
					title: '全部',
					data: labeldata,
					remote: false,
					showCheckbox: false,
					showSearch: true,
					search: search,
					props: defaultProps
				}"
				:clickedAsSearc="false"
				@btn-click="handleClick"
				:syncKeys="syncKeysOption"
				@node-click="handleNodeClick"
			></es-tree-group>
		</div>

		<es-selector
			ref="refTransferDOM"
			title="选择移交人"
			v-model="transferList"
			:multiple="false"
			@confirm="TransferConfirm"
			@cancel="handleCancel"
			value-type="string"
			:types="['employee']"
			method="post"
			style="display: none"
		></es-selector>

		<es-selector
			ref="refShareDom"
			title="选择共享范围"
			v-model="shareList"
			@confirm="shareConfirm"
			@cancel="handleCancel"
			value-type="string"
			:multiple="false"
			style="display: none"
			:types="['employee']"
		></es-selector>

		<es-dialog
			title="新增"
			:visible.sync="visible"
			width="600px"
			height="350px"
			ref="visible"
			@close="handleclose"
		>
			<es-form
				v-if="visible"
				ref="form"
				:model="formData"
				:formatSubmit="false"
				:contents="formItemList"
				enter-submit
				@submit="handleFormSubmit"
				label-position="top"
			></es-form> 
		</es-dialog>

		<editFile ref="refeditFile"></editFile>
		<el-dialog title="共享状态" :visible.sync="shareVisible" append-to-body>
			<div class="shareContainer">
				<span style="margin-right: 5px">请选择共享状态</span>
				<el-radio-group v-model="shareStatus" @input="changeStatus">
					<el-radio :label="0">不共享</el-radio>
					<el-radio :label="1">部门共享</el-radio>
					<el-radio :label="2">指定共享</el-radio>
				</el-radio-group>
			</div>
			<div slot="footer" class="dialog-footer">
				<el-button @click="shareVisible = false">取 消</el-button>
				<el-button type="primary" @click="nextStep">
					{{ shareStatus == 2 ? '下一步' : '确认' }}
				</el-button>
			</div>
		</el-dialog>
	</div>
</template>

<script>
import api from '@/http/dataManage/data-manager';
import editFile from './component/editFile';
// import { method } from 'lodash';

export default {
	components: {
		editFile
	},
	data() {
		return {
			nowStatus: 0,
			shareStatus: 0,
			shareVisible: false,
			formData: {
				classId: null,
				fileName: '',
				fileMat: '',
				fileSize: '',
				remark: '',
				fileSysId: '',
				id: ''
			},

			SelectFileID: null,
			visible: false,
			TreeUrl: api.getClassFyTree,
			search: {
				name: 'label',
				placeholder: '请输入关键字筛选'
			},
			transferList: [],
			shareList: [],
			SelectParentId: null,
			FileshareStatus: null,
			table: {
				immediate: true,
				reload: true,
				url: api.getFileList,
				method: 'POST',
				format: false,
				param: {
					pageNum: 1,
					pageSize: 10,
					privates: true
				},
				page: {
					pageSize: 10,
					position: 'right',
					current: 1,
					pageNum: 1
				},
				toolbar: [
					{
						type: 'button',
						contents: [
							{
								text: '新增',
								type: 'primary',
								event: res => {
									if (!this.SelectParentId) {
										this.$message.warning('请选择一个节点');
										return;
									}
									this.formData.classId = this.SelectParentId;
									this.formData.id = this.$uuidv4();
									this.formData.fileSysId = this.formData.id;
									this.visible = true;
								}
							}
						]
					},
					{
						type: 'search',
						contents: [
							{
								type: 'text',
								name: 'fileName',
								placeholder: '请输入文件名'
							}
						]
					}
				],
				thead: [
					{
						title: '文件名',
						field: 'fileName',
						align: 'center',
						fixed: true
					},
					{
						title: '文件格式',
						field: 'fileMat',
						align: 'center',
						fixed: false
					},
					{
						title: '所属人',
						field: 'failName',
						align: 'center'
					},
					{
						title: '文件大小',
						field: 'fileSize',
						align: 'center'
					},
					{
						title: '上传时间',
						field: 'createTime',
						align: 'center'
					},
					{
						title: '备注',
						field: 'remark',
						align: 'center'
					},
					{
						title: '操作',
						type: 'handle',
						template: '',
						events: [
							{
								text: '下载'
							},
							{
								text: '编辑'
							},
							{
								text: '删除'
							},
							{
								text: '移交'
							},
							{
								text: '共享'
							}
						]
					}
				]
			},

			syncKeysOption: {
				classId: 'id'
			},
			labeldata: [],
			defaultProps: {
				children: 'child',
				label: 'journalName'
			}
		};
	},
	computed: {
		formItemList() {
			return [
				{
					label: '上传附件',
					type: 'attachment',
					name: 'fj',
					code: 'doc_manage_upload',
					ownId: this.formData.id,
					rules: {
						required: true,
						message: '请上传附件',
						trigger: 'blur'
					}
				},
				{
					name: 'remark',
					type: 'textarea',
					row: true,
					col: 12,
					placeholder: '备注',
					label: '备注'
				}
			];
		}
	},
	mounted() {
		this.getTreeData();
	},
	methods: {
		nowStatus(val) {
			this.shareStatus = val;
		},
		nextStep(val) {
			if (this.shareStatus == this.nowStatus) {
				this.$message.warning('目前该文件是该状态');
				return;
			} else {
				this.shareVisible = false;
				if (this.shareStatus == 2) {
					this.$refs.refShareDom.openDialog();
				} else {
					let params = {
						id: this.SelectFileID,
						shareStatus: this.shareStatus
					};
					this.$request({
						url: api.rangeFile,
						format: false,
						data: params,
						method: 'POST'
					}).then(res => {
						if (res.rCode === 0) {
							// this.$refs.refEsTreeGroup.reload();
							this.SelectFileID = null;
							this.$message.success('共享成功');
						} else {
							this.$message.error(res.msg);
						}
					});
				}
			}
		},
		handleClick(data) {
			let { row } = data;

			let handle = data.handle.text;
			this.SelectFileID = row.id;
			this.FileshareStatus = row.shareStatus;

			switch (handle) {
				case '下载':
					this.DownLoadFile(row);
					break;
				case '删除':
					this.DeletFile(row);
					break;
				case '编辑':
					this.$refs.refeditFile.open(row);
					break;
				case '移交':
					this.$refs.refTransferDOM.openDialog();
					break;
				case '共享':
					this.nowStatus = row.shareStatus;
					this.shareVisible = true;
					// this.$refs.refShareDom.openDialog();
					break;
				default:
					break;
			}
		},

		getTreeData() {
			this.$request({
				url: api.getClassFyTree,
				method: 'GET'
			}).then(res => {
				if (res.rCode === 0) {
					this.labeldata = res.results;
					// this.$message.success('删除成功');
				} else {
					this.$message.error(res.msg);
				}
			});
		},

		handleNodeClick(dom, param, Node) {
			let { id } = param;
			this.SelectParentId = id;
		},

		// 上传文件
		handleFormSubmit() {
			const { fj, ...restFormData } = this.formData;
			const { name, fileSize, suffix } = fj[0];
			const formData = {
				...restFormData,
				fileName: name,
				fileSize,
				fileMat: suffix
			};
			try {
				this.$request({
					url: api.createFile,
					format: false,
					data: formData,
					method: 'POST'
				}).then(res => {
					if (res.rCode === 0) {
						this.$refs.refEsTreeGroup.reload();
						this.$message.success('创建成功');
					} else {
						this.$message.error(res.msg);
					}
				});
			} catch (error) {
			} finally {
				this.visible = false;
			}
		},

		handleclose() {},

		async DownLoadFile(row) {
			try {
				const { fileSysId, id } = row;
				let msg = await this.$utils.DownLoadFile('doc_manage_upload', fileSysId);
				if (msg === '您的操作已成功！') {
					this.$request({
						url: api.addDownLoadsum,
						params: { id },
						method: 'GET'
					}).then(res => {
						if (res.rCode === 0) {
							this.$message.success(msg);
						}
					});
				}
			} catch (error) {}
		},

		DeletFile(row) {
			// console.log(row);
			const { fileSysId, id } = row;
			this.$utils.deleteFile('doc_manage_upload', fileSysId);

			this.$request({
				url: api.deleteFile,
				format: false,
				data: [id],
				method: 'POST'
			}).then(res => {
				if (res.rCode === 0) {
					this.$refs.refEsTreeGroup.reload();
					this.$message.success('删除成功');
				} else {
					this.$message.error(res.msg);
				}
			});
		},

		change(e) {
			console.log(e);
			console.log(111);
		},
		ckicked() {
			console.log(222);
		},

		//  确认移交
		TransferConfirm() {
			let data = this.transferList;
			let id = this.SelectFileID;

			data.forEach(item => {
				let { username, userId } = JSON.parse(item.attr);
				let params = {
					id,
					failId: userId,
					failName: username
				};
				this.$request({
					url: api.transferFile,
					format: false,
					data: params,
					method: 'POST'
				}).then(res => {
					if (res.rCode === 0) {
						this.SelectFileID = null;
						this.$refs.refEsTreeGroup.reload();
						this.$message.success('移交成功');
					} else {
						this.$message.error(res.msg);
					}
				});
			});
		},

		shareConfirm() {
			const data = this.shareList;
			const id = this.SelectFileID;
			data.forEach(item => {
				const { stype } = item;
				const { username, userId } = JSON.parse(item.attr);
				const shareStatusMap = {
					employee: 2,
					department: 1
				};
				const shareStatus = shareStatusMap[stype] || 0;
				let params = {
					id,
					shareStatus,
					shareUserIds: userId
				};
				this.$request({
					url: api.rangeFile,
					format: false,
					data: params,
					method: 'POST'
				}).then(res => {
					if (res.rCode === 0) {
						// this.$refs.refEsTreeGroup.reload();
						this.SelectFileID = null;
						this.$message.success('共享成功');
					} else {
						this.$message.error(res.msg);
					}
				});
			});
		},
		handleCancel(res) {
			console.log(res, 3333);
		}
	}
};
</script>

<style scoped lang="scss">
.main {
	position: relative;
	width: 100%;
	height: 100%;
	// padding: 10px;
	.body {
		width: 100%;
		height: 100%;
	}
}
.shareContainer {
	display: flex;
}
</style>
