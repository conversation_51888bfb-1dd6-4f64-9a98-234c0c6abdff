<template>
	<div class="floor-box">
		<div id="myEchart1" class="content1"></div>
		<div id="myEchart2" class="content2"></div>
		<div id="myEchart3" class="content3"></div>
	</div>
</template>

<script>
export default {
	name: 'Teacher',
	props: {
		title: {
			type: String, // 毕业就业 实习
			default: '毕业就业'
		}
	},
	data() {
		return {
			myChart: []
		};
	},
	computed: {
		classify() {
			// 毕业就业
			if (this.title === '毕业就业') {
				return [
					{ title: '就业率年度分析', color: ['#17CBBA', '#BFF388'] },
					{ title: '专业对口率年度分析', color: ['#FF9B33', '#EBCC32'] },
					{ title: '升学率年度分析', color: ['#1293DF', '#4FDCB3'] }
				];
			}
			// 实习
			return [
				{ title: '实习就业率年度分析', color: ['#17CBBA', '#BFF388'] },
				{ title: '实习对口上岸率年度分析', color: ['#FF9B33', '#EBCC32'] },
				{ title: '实习稳定率年度分析', color: ['#1293DF', '#4FDCB3'] }
			];
		}
	},
	mounted() {
		window.addEventListener('resize', this.onResize);
		this.toolChart('myEchart1', 0);
		this.toolChart('myEchart2', 1);
		this.toolChart('myEchart3', 2);
	},
	beforeDestroy() {
		window.removeEventListener('resize', this.onResize);
		this.myChart.forEach(item => {
			if (item) {
				item.dispose();
			}
		});
	},
	methods: {
		toolChart(dom, i) {
			let chartDom = document.getElementById(dom);
			if (this.myChart[i]) {
				this.myChart[i].clear();
			} else {
				this.myChart[i] = this.$echarts.init(chartDom);
			}
			const xData = ['2020年', '2021年', '2022年', '2023年', '2024年'];
			const yData = [43, 20, 24, 5, 8];

			const toolClassify = () => {
				return this.classify[i];
			};

			const option = {
				tooltip: {
					trigger: 'axis',
					axisPointer: {
						type: 'shadow'
					}
				},
				title: {
					text: toolClassify().title,
					top: '5.4%',
					left: '3.47%',
					textStyle: {
						fontSize: 16,
						color: '#21252B'
					}
				},

				grid: {
					left: '9.17%',
					top: '25%',
					bottom: '12%',
					right: '4.95%'
				},
				xAxis: {
					data: xData,
					axisTick: {
						show: false
					},
					axisLine: {
						lineStyle: {
							color: 'rgba(255, 129, 109, 0.1)',
							width: 1 //这里是为了突出显示加上的
						}
					},
					axisLabel: {
						textStyle: {
							color: '#0A325B',
							fontSize: 14
						}
					}
				},
				yAxis: [
					{
						splitNumber: 2,
						axisTick: {
							show: false
						},
						axisLine: {
							lineStyle: {
								color: 'rgba(255, 129, 109, 0.1)',
								width: 1 //这里是为了突出显示加上的
							}
						},
						axisLabel: {
							textStyle: {
								color: '#8997A5',
								fontSize: 14
							},
							formatter: function (value) {
								return value + '%';
							}
						},
						splitArea: {
							areaStyle: {
								color: 'rgba(255,255,255,.5)'
							}
						},
						splitLine: {
							//刻度线
							show: true,
							lineStyle: {
								color: '#B2C3DA',
								type: [3, 3],
								dashOffset: 2
							}
						}
					}
				],
				series: [
					{
						name: '占比',
						type: 'pictorialBar',
						barCategoryGap: '0%', //柱间距离
						symbol: 'path://M0,10 L10,10 C5.5,10 5.5,5 5,0 C4.5,5 4.5,10 0,10 z',
						label: {
							show: true,
							position: 'top',
							distance: 2,
							color: '#FFFFFF',
							fontSize: 12,
							borderColor: '#09354F',
							borderWidth: 1,
							borderRadius: 4,
							padding: [0, 10],
							backgroundColor: 'rgba(9,53,79,0.55)',
							height: 20,
							lineHeight: 18,
							formatter: function (params) {
								return params.value + '%';
							}
						},
						itemStyle: {
							normal: {
								color: {
									type: 'linear',
									x: 0,
									y: 0,
									x2: 0,
									y2: 1,
									colorStops: [
										{
											offset: 0,
											color: toolClassify().color[0] //  0%  处的颜色
										},
										{
											offset: 1,
											color: toolClassify().color[1] //  100%  处的颜色
										}
									],
									global: false //  缺省为  false
								}
							},
							emphasis: {
								opacity: 1
							}
						},
						data: yData,
						z: 10
					}
				]
			};

			option && this.myChart[i].setOption(option);
		},
		onResize() {
			this.myChart.forEach(item => {
				if (item) {
					item.resize();
				}
			});
		}
	}
};
</script>

<style lang="scss" scoped>
.floor-box {
	display: flex;
	justify-content: space-between;
	width: 100%;
	.content1,
	.content2,
	.content3,
	.content4 {
		width: 576px;
		height: 220px;
		background: rgba(252, 253, 254, 0.7);
		box-shadow: 0px 0px 10px 0px rgba(5, 32, 70, 0.1);
		border-radius: 8px;
		border: 2px solid #ffffff;
		margin-right: 12px;
		&:last-child {
			margin-right: 0;
		}
	}
}
</style>
