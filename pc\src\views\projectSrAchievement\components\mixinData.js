export const mixinData = {
	computed: {
		contents() {
			const formReadonly = this.formReadonly;
			const formData = this.formData;
			let arr = [];
			switch (this.basics.contentsKey) {
				// 学术论文
				case 'academicPaper':
					arr = [
						{
							name: 'firstAuthor',
							placeholder: '请输入',
							label: '第一作者',
							rules: {
								required: !formReadonly,
								message: '请输入',
								trigger: 'blur'
							},
							col: 6
						},
						{
							name: 'thesisName',
							placeholder: '请输入',
							label: '论文名称',
							rules: {
								required: !formReadonly,
								message: '请输入',
								trigger: 'blur'
							},
							col: 6
						},
						{
							name: 'thesisUrl',
							label: '论文查询网址',
							type: 'text',
							sysCode: 'project_type',
							rules: {
								required: !formReadonly,
								message: '请输入',
								trigger: 'blur'
							},
							col: 12
						},
						{
							name: 'projectNum',
							label: '论文依托的项目编号',
							type: 'text',
							rules: {
								required: !formReadonly,
								message: '请输入',
								trigger: 'blur'
							},
							col: 6
						},
						{
							name: 'projectName',
							label: '论文依托的项目名称',
							type: 'textarea',
							rules: {
								required: !formReadonly,
								message: '请输入',
								trigger: 'blur'
							},
							col: 6
						},
						{
							name: 'periodicalName',
							label: '刊物名称',
							type: 'text',
							rules: {
								required: !formReadonly,
								message: '请输入',
								trigger: 'blur'
							},
							col: 6
						},

						{
							name: 'periodicalPublishDate',
							placeholder: '请选择',
							col: 6,
							label: '刊物出版时间',
							rules: {
								required: !formReadonly,
								message: '请选择',
								trigger: 'blur'
							},
							type: 'date'
						},

						{
							name: 'periodicalPageNum',
							placeholder: '请输入',
							col: 6,
							label: '论文所在刊物页码',
							rules: [
								{
									required: !formReadonly,
									message: '请输入',
									trigger: 'blur'
								},
								{
									max: 100,
									message: '长度应在1到10个字符之间',
									trigger: 'blur'
								}
							]
						},

						{
							name: 'thesisLevel',
							placeholder: '请选择',
							col: 6,
							label: '论文级别',
							type: 'select',
							rules: {
								required: !formReadonly,
								message: '请选择',
								trigger: 'blur'
							},
							sysCode: 'thesis_level_code'
						},

						{
							name: 'fj1',
							label: '支撑材料（封面、目录、论文页）',
							type: 'attachment',
							value: '',
							col: 12,
							preview: true,
							code: 'transationform_editfile',
							ownId: formData.id, // 业务id
							rules: {
								required: !formReadonly,
								message: '请上传',
								trigger: 'blur'
							}
						},
						{
							name: formReadonly ? 'studentIsFirstTxt' : 'studentIsFirst',
							placeholder: '请选择',
							col: 12,
							label: '学生是否为第一作者',
							type: formReadonly ? 'text' : 'select',
							data: [
								{
									label: '是',
									value: '1'
								},
								{
									label: '否',
									value: '0'
								}
							],
							rules: {
								required: !formReadonly,
								message: '请选择',
								trigger: 'blur'
							},
							events: {
								change: a => {
									formData.studentName = '';
									formData.studentNum = '';
								}
							}
						},

						{
							name: 'studentName',
							placeholder: '',
							label: '学生姓名',
							hide: formData.studentIsFirst === '0',
							rules: {
								required: !formReadonly,
								message: '请输入',
								trigger: 'blur'
							},
							col: 6
						},
						{
							name: 'studentNum',
							placeholder: '',
							label: '学生学号',
							hide: formData.studentIsFirst === '0',
							rules: {
								required: !formReadonly,
								message: '请输入',
								trigger: 'blur'
							},
							col: 6
						}
					];
					break;
				default:
					break;
			}
			return arr;
		}
	}
};
