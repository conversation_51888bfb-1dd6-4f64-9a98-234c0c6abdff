<template>
	<div class="content">
		<es-data-table
			ref="table"
			:row-style="tableRowClassName"
			:full="true"
			:fit="true"
			:thead="thead"
			:toolbar="toolbar"
			:border="true"
			:page="pageOption"
			:url="dataTableUrl"
			:numbers="true"
			:param="params"
			close
			form
			@btnClick="btnClick"
			@sort-change="sortChange"
			@submit="hadeSubmit"
			@edit="changeTable"
		></es-data-table>
		<!-- <es-dialog
			:title="formTitle"
			:visible.sync="showForm"
			width="900px"
			height="600px"
			:close-on-click-modal="false"
			:destroy-on-close="true"
		>
			<es-form
				ref="form"
				:model="formData"
				:contents="formItemList"
				height="500px"
				:genre="2"
				collapse
				@change="inputChange"
				@submit="handleFormSubmit"
				@reset="showForm = false"
			/>
		</es-dialog> -->
	</div>
</template>

<script>
import apiUrl from '@/http/job/jobCoPostResume/api';
export default {
	props: ['postId'],
	data() {
		return {
			ownId: '',
			dataTableUrl: apiUrl.listJson,
			showForm: false,
			showRoleForm: false,
			enpList: [],
			formData: {},
			formTitle: '编辑',
			editBtn: {
				type: 'submit',
				skin: 'lay-form-btns',
				contents: [
					{
						type: 'primary',
						text: '确定',
						event: 'confirm'
					},
					{
						type: 'reset',
						text: '取消',
						event: 'cancel'
					}
				]
			},
			cancelBtn: {
				type: 'submit',
				skin: 'lay-form-btns',
				contents: [
					{
						type: 'reset',
						text: '取消',
						event: 'cancel'
					}
				]
			},
			toolbar: [
				{
					type: 'search',
					contents: [
						{
							type: 'select',
							placeholder: '是否面邀',
							name: 'isInvite',
							event: 'multipled',
							data: [
								{ value: 1, name: '是' },
								{ value: 0, name: '否' }
							],
							clearable: true,
							col: 4
						},
						{
							type: 'text',
							name: 'keyword',
							placeholder: '关键字查询'
						}
					]
				}
			],
			thead: [
				{
					title: '姓名',
					align: 'center',
					field: 'userName',
					showOverflowTooltip: true
				},
				{
					title: '性别',
					align: 'center',
					width: 80,
					field: 'userSex',
					showOverflowTooltip: true
				},
				{
					title: '联系方式',
					align: 'center',
					field: 'userPhone'
				},
				{
					title: '投递状态',
					align: 'center',
					field: 'status'
				},
				{
					title: '是否面邀',
					align: 'center',
					field: 'isInvite'
				},
				{
					title: '投递时间',
					align: 'center',
					width: 160,
					field: 'createTime'
					// valueFormat: "yyyy-MM-dd"
				}
				// {
				// 	title: '操作',
				// 	type: 'handle',
				// 	width: 150,
				// 	template: '',
				// 	events: [
				// 		{
				// 			code: 'view',
				// 			text: '查看'
				// 		}
				// 	]
				// }
			],
			pageOption: {
				layout: 'total, prev, pager, next, jumper',
				pageSize: 20,
				hideOnSinglePage: true,
				position: 'right',
				current: 1,
				pageNum: 1
			},
			params: {
				postId: null,
				asc: 'false',
				orderBy: 'createTime'
			}
		};
	},
	computed: {
		formItemList() {
			return [
				{
					label: '用户名',
					name: 'userName',
					placeholder: '请输入用户名',
					event: 'multipled',
					rules: {
						required: true,
						message: '请输入用户名',
						trigger: 'blur'
					},
					verify: 'required',
					col: 12
				}
			];
		}
	},
	watch: {},
	created() {
		this.params.postId = this.postId;
	},
	mounted() {},
	methods: {
		/**
		 * 表格行高
		 */
		tableRowClassName({ row, rowIndex }) {
			let styleRes = {
				height: '54px !important'
			};
			return styleRes;
		},
		inputChange(key, value) {},
		hadeSubmit(data) {},
		//格式化表格联系方式
		formatLink(man, phone) {
			var des = '';
			if (man && phone) {
				des = man + '-' + phone;
			} else {
				if (phone) {
					des = phone;
				}
			}
			return des;
		},
		/**
		 * 操作按钮点击事件
		 * @param {*} res
		 */
		btnClick(res) {
			let code = res.handle.code;
			switch (code) {
				// case 'edit':
				// 	// 编辑
				// 	this.formTitle = '编辑';
				// 	this.ownId = res.row.id;
				// 	this.editModule(this.formItemList, []);
				// 	this.$request({
				// 		url: apiUrl.info + '/' + res.row.id,
				// 		method: 'GET'
				// 	}).then(res => {
				// 		if (res.rCode == 0) {
				// 			this.formData = res.results;
				// 			this.showForm = true;
				// 		}
				// 	});
				// 	break;
				case 'view':
					this.formTitle = '查看';
					this.ownId = res.row.id;
					this.readModule(this.formItemList, []);
					this.$request({
						url: apiUrl.info + '/' + res.row.id,
						method: 'GET'
					}).then(res => {
						if (res.rCode == 0) {
							this.formData = res.results;
							this.showForm = true;
						}
					});
					break;
				default:
					break;
			}
		},
		handleClose() {},
		handleFormItemChange() {},
		handleFormSubmit(data) {
			let formData = data;
			delete formData.extMap;
			let url = '';
			if (this.formTitle == '新增') {
				url = apiUrl.save;
			} else {
				url = apiUrl.update;
			}
			this.$request({
				url: url,
				data: formData,
				method: 'POST'
			}).then(res => {
				if (res.rCode == 0) {
					this.$message.success('操作成功');
					this.showForm = false;
					this.formData = {};
					this.$refs.table.reload();
				} else {
					this.$message.error(res.msg);
				}
			});
		},
		/**
		 * 排序变化事件
		 * @param {*} column
		 * @param {*} prop
		 * @param {*} order
		 */
		sortChange(column, prop, order) {
			if (column.order == 'ascending') {
				//升序
				this.params = {
					asc: 'true',
					orderBy: column.prop
				};
			} else if (column.order == 'descending') {
				//降序
				this.params = {
					asc: 'false',
					orderBy: column.prop
				};
			} else {
				//不排序
				this.params = {
					asc: 'false',
					orderBy: 'createTime'
				};
			}
			this.$refs.table.reload();
		},
		/**
		 * 编辑模式
		 */
		editModule(list, hideField) {
			for (var i in list) {
				var item = list[i];
				item.readonly = false;
				if (undefined != hideField && hideField.length > 0 && hideField.indexOf(item.name) > -1) {
					item.hide = true;
				}
			}
			list.push(this.editBtn);
		},
		/**
		 * 只读模式
		 */
		readModule(list, hideField) {
			for (var i in list) {
				var item = list[i];
				item.readonly = true;
				if (undefined != hideField && hideField.length > 0 && hideField.indexOf(item.name) > -1) {
					item.hide = true;
				}
			}
			list.push(this.cancelBtn);
		},
		changeTable(val) {
			if ('isPractice' === val.name) {
				this.$request({
					url: apiUrl.updatePractice,
					data: {
						id: val.data.id
					},
					method: 'POST'
				}).then(res => {
					if (res.rCode == 0) {
						this.$message.success('设置成功！');
					} else {
						this.$message.error(res.msg);
					}
				});
			}
		}
	}
};
</script>
<style scoped lang="scss">
.content {
	width: 100%;
	height: 100%;
}

.el-dialog__body {
	height: 100%;
	.btn-box {
		margin-top: 20px;
		display: flex;
		justify-content: flex-end;

		.btn {
			padding: 5px 10px;
			color: #666;
			border: 1px solid #eee;
			cursor: pointer;
			margin-right: 5px;
			border-radius: 4px;
		}
	}
}
</style>
