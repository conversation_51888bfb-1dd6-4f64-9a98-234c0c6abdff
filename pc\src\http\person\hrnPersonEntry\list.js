import httpApi from './api.js';
export default {
	computed: {
		component() {
			return {
				// 树表结构
				//name: 'es-tree-group'
				// 纯列表结构
				name: 'es-data-table'
			};
		},
		treeParam() {
			return {
				// 不需要左边树 可以
				//url: httpApi.hrnPersonEntryTree,
				//title: '分类树'
			};
		},
		table() {
			return {
				url: httpApi.hrnPersonEntryList,
				param: { handlerType: '' },
				full: true,
				fit: true,
				page: true,
				stripe: true,
				numbers: true,
				'row-key': 'id',
				toolbar: [
					{
						type: 'button',
						contents: [
							{
								text: '新增',
								type: 'primary'
							}
						]
					},
					{
						type: 'search',
						reset: true,
						contents: [
							//{
							//	name: 'accountName_like',
							//	placeholder: '配置名称',
							//	label: '配置名称',
							//	col: 6
							//},
							{
								type: 'date',
								col: 6,
								name: 'updateTime_begin',
								label: '更新开始时间',
								placeholder: '更新开始时间'
							},
							{
								type: 'date',
								col: 6,
								name: 'updateTime_end',
								label: '更新结束时间',
								placeholder: '更新结束时间'
							}
						]
					}
				],
				thead: [
					{
						title: '年份',
						align: 'left',
						field: 'years'
					},
					{
						title: '序号',
						align: 'left',
						field: 'number'
					},
					{
						title: '教工号',
						align: 'left',
						field: 'staffNumber'
					},
					{
						title: '姓名',
						align: 'left',
						field: 'name'
					},
					{
						title: '身份证号',
						align: 'left',
						field: 'idNumber'
					},
					{
						title: '出生年月',
						align: 'left',
						field: 'birthday'
					},
					{
						title: '年龄',
						align: 'left',
						field: 'age'
					},
					{
						title: '性别',
						align: 'left',
						field: 'sex'
					},
					{
						title: '联系电话',
						align: 'left',
						field: 'contactNumber'
					},
					{
						title: '籍贯',
						align: 'left',
						field: 'nativePlace'
					},
					{
						title: '曾用名',
						align: 'left',
						field: 'beforeName'
					},
					{
						title: '政治面貌',
						align: 'left',
						field: 'politicalStatus'
					},
					{
						title: '民族',
						align: 'left',
						field: 'nation'
					},
					{
						title: '是否具有高校教师资格证',
						align: 'left',
						field: 'isCollegeDiploma'
					},
					{
						title: '证书附件',
						align: 'left',
						field: 'diplomaFile'
					},
					{
						title: '入团时间',
						align: 'left',
						field: 'joinLeagueTime'
					},
					{
						title: '入党时间',
						align: 'left',
						field: 'joinPartyTime'
					},
					{
						title: '编制类别',
						align: 'left',
						field: 'authorizedCategory'
					},
					{
						title: '是否有试用期',
						align: 'left',
						field: 'isTryout'
					},
					{
						title: '试用期限',
						align: 'left',
						field: 'tryoutTime'
					},
					{
						title: '试用到期',
						align: 'left',
						field: 'tryoutTerm'
					},
					{
						title: '是否双师型教师',
						align: 'left',
						field: 'isDoubleQualified'
					},
					{
						title: '合同年限',
						align: 'left',
						field: 'contractYears'
					},
					{
						title: '是否长期合同',
						align: 'left',
						field: 'isLongContract'
					},
					{
						title: '合同期限开始',
						align: 'left',
						field: 'contractBegin'
					},
					{
						title: '合同期限结束',
						align: 'left',
						field: 'contractEnd'
					},
					{
						title: '合同附件',
						align: 'left',
						field: 'contractFile'
					},
					{
						title: '实际转正日期',
						align: 'left',
						field: 'formalTime'
					},
					{
						title: '转正附件',
						align: 'left',
						field: 'formalFile'
					},
					{
						title: '是否双肩挑',
						align: 'left',
						field: 'isDoubleShoulder'
					},
					{
						title: '职位状态',
						align: 'left',
						field: 'personPostStatus'
					},
					{
						title: '状态',
						align: 'left',
						field: 'status'
					},
					{
						title: '操作',
						type: 'handle',
						width: 180,
						events: [
							{
								text: '查看'
							},
							{
								text: '编辑'
							},
							{
								text: '删除'
							}
						]
					}
				]
			};
		}
	}
};
