export default {
	computed: {
		viewItemList() {
			return [
				{
					name: 'name',
					label: '机构名称',
					value: '',
					readonly: true,
					col: 12,
					rules: {
						required: true,
						message: '请输入机构名称'
					}
				},
				{
					label: '机构LOGO',
					type: 'attachment',
					code: 'job_co_finance_logo',
					ownId: this.formData.id, // 业务id
					disabled: true,
					height: 'auto',
					portrait: true,
					param: {
						isShowPath: true
					},
					col: 12
				},
				{
					name: 'contactWay',
					label: '联系方式',
					value: '',
					readonly: true,
					col: 12,
					rules: {
						required: true,
						message: '请输入联系方式'
					}
				},
				{
					name: 'status',
					label: '启用状态',
					disabled: true,
					col: 3,
					type: 'switch',
					rules: {
						required: true,
					},
					data: [
						{ value: 1, text: '启用'},
						{ value: 0, text: '禁用'}
					]
				},
			]
		}
	}
};
