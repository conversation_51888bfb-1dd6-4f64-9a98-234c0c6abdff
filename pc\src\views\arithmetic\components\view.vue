<template>
	<es-form
		:key="formData._id"
		ref="form"
		v-loading="loading"
		:model="formData"
		:contents="formItemList"
		label-width="100px"
		:genre="2"
		collapse
		:submit="formTitle !== '查看'"
		@change="inputChange"
		@submit="handleFormSubmit"
	/>
</template>

<script>
import interfaceUrl from '@/api/arithmetic.js';
import { size } from 'lodash';
export default {
	name: 'ResumeView',

	props: {
		info: {
			type: Object,
			default: () => {
				return {};
			}
		},
		formTitle: {
			type: String,
			default: ''
		}
	},
	data() {
		return {
			loading: false,
			formData: { _id: '', size: '' },
			classifyArr: []
		};
	},
	computed: {
		formItemList() {
			const readonly = this.formTitle === '查看';
			return [
				{
					label: '算法名称',
					name: 'title',
					readonly: readonly,
					rules: {
						required: true,
						message: '请输入',
						trigger: 'change'
					},
					col: 6
				},
				{
					label: '算法介绍',
					name: 'introduce',
					readonly: readonly,
					rules: {
						required: true,
						message: '请输入',
						trigger: 'change'
					},
					col: 6
				},
				{
					label: '算法版本',
					name: 'version',
					readonly: readonly,
					rules: {
						required: true,
						message: '请输入',
						trigger: 'change'
					},
					col: 6
				},
				{
					label: '生产日期',
					name: 'build_time',
					type: 'datetime',
					readonly: readonly,
					rules: {
						required: true,
						message: '请输入',
						trigger: 'change'
					},
					col: 6
				},
				{
					label: '选择分类',
					name: 'classes_id',
					type: 'select',
					data: this.classifyArr,
					readonly: readonly,
					rules: {
						required: true,
						message: '请输入',
						trigger: 'change'
					},
					col: 6
				},
				{
					label: '作者信息',
					name: 'author',
					readonly: readonly,
					rules: {
						required: true,
						message: '请输入',
						trigger: 'change'
					},
					col: 6
				},
				{
					name: 'fj',
					label: '上传算法附件(最多1个)',
					type: 'attachment',
					col: 12,
					code: 'plat_algorithm_file',
					param: {
						isShowPath: true
					},
					rules: {
						required: true,
						message: '请输上传',
						trigger: 'change'
					},
					limit: 1,
					preview: true,
					readonly: readonly,
					ownId: this.formData.target // 业务id
				}
			];
		}
	},
	watch: {
		info: {
			handler(val) {
				this.formData = val;
			},
			deep: true,
			immediate: true
		}
	},
	created() {
		this.classifyList();
	},
	methods: {
		// 请求算法分类
		classifyList() {
			this.$request2({
				url: interfaceUrl.classifyInfo,
				method: 'POST'
			}).then(res => {
				if (res.code === 200) {
					this.classifyArr = res.data.map(e => {
						return {
							label: e.title,
							value: e._id
						};
					});
				}
			});
		},

		inputChange(key, value) {},
		// 保存
		async handleFormSubmit(e) {
			const valid = await this.$refs.form.validate();
			if (!valid) return;
			this.loading = true;
			const formData = JSON.parse(JSON.stringify(this.formData));
			const params = {
				...formData,
				size: '0'
			};
			delete params.fj;
			delete params.status;

			if (this.formTitle === '新增') {
				params.create_time = this.$.formatDate(new Date().getTime(), 'yyyy-MM-dd HH:mm:ss');
			} else {
				params.update_time = this.$.formatDate(new Date().getTime(), 'yyyy-MM-dd HH:mm:ss');
			}
			this.$request2({
				url: this.formTitle === '新增' ? interfaceUrl.add : interfaceUrl.edit,
				data: params,
				method: 'POST',
				type: 'JSON'
			}).then(res => {
				this.loading = false;
				this.$emit('close');
			});
		},
		doHandleFormData(newData) {
			this.formData = newData;
		}
	}
};
</script>

<style scoped lang="scss">
.el-upload--handle {
	width: 100%;
}
</style>
