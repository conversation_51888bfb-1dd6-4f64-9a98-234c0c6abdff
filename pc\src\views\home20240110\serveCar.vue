<template>
	<div class="serve-car">
		<!-- 顶部导航栏 -->
		<div class="hall-top">
			<VocationalHeader class="box-center"></VocationalHeader>
		</div>
		<!-- 通知公告 -->
		<div class="car">
			<div class="car-search"><Search @onSearch="onSearch" /></div>
			<div class="car-btn">
				<BackButtom :type="'transparent'" />
			</div>
			<div class="car-box">
				<!-- 系统直通车 -->
				<template v-if="type == 'system'">
					<div class="service-list">
						<div
							v-for="item in serviceListSearch"
							:key="item.id"
							class="service-item"
							@click="myAppDataBtn(item)"
						>
							<img :src="item.logo" class="item-img" />
							<div class="item-right">
								<span class="name">{{ item.name }}</span>
								<span class="desc">{{ item.remark }}</span>
							</div>
							<div
								class="collect-box"
								@click.stop="
									collectedList.includes(item.id) ? deleteBatchIds(item, 0) : saveCollect(item, 0)
								"
							>
								<div v-if="collectedList.includes(item.id)" style="color: #ffdc86">
									<i class="el-icon-star-on icon"></i>
									<span>已收藏</span>
								</div>
								<div v-else>
									<i class="el-icon-star-off icon"></i>
									<span>收藏</span>
								</div>
							</div>
							<img src="@/assets/images/serveHall/active-bg.png" class="active-bg" />
						</div>
					</div>
				</template>
				<!-- 业务直通车 -->
				<template v-else>
					<div v-for="serve in serviceListSearch" :key="serve.id" class="service-box">
						<p v-if="serve.text" class="title-box">
							<img src="@/assets/images/serveHall/left-arrows.png" alt="" class="left-arrows" />
							<span class="text">{{ serve.text }}</span>
						</p>
						<div class="service-list">
							<div
								v-for="item in serve.children"
								:key="item.id"
								class="service-item"
								@click="toOpenWindow(item)"
							>
								<img :src="handelPicUrl(item.icons)" class="item-img" />
								<div class="item-right">
									<span class="name">{{ item.text }}</span>
									<span class="desc">{{ item.appCode }}</span>
								</div>
								<div
									class="collect-box"
									@click.stop="
										collectedList.includes(item.id) ? deleteBatchIds(item, 0) : saveCollect(item, 0)
									"
								>
									<div v-if="collectedList.includes(item.id)" style="color: #ffdc86">
										<i class="el-icon-star-on icon"></i>
										<span>已收藏</span>
									</div>
									<div v-else>
										<i class="el-icon-star-off icon"></i>
										<span>收藏</span>
									</div>
								</div>
								<img src="@/assets/images/serveHall/active-bg.png" class="active-bg" />
							</div>
						</div>
					</div>
				</template>
				<div v-if="serviceListSearch.length < 1" class="none-box"><Empty /></div>
			</div>
			<!-- <div v-else class="car-box">
				<div class="service-box">
					<div class="service-list">
						<div v-for="item in serviceListSearch" :key="item.id" class="service-item">
							<template v-if="type == 'system'">
								<img :src="item.logo" class="item-img" />
								<div class="item-right">
									<span class="name">{{ item.name }}</span>
									<span class="desc">{{ item.remark }}</span>
								</div>
							</template>
							<template v-else>
								<img :src="handelPicUrl(item.icons)" class="item-img" />
								<div class="item-right">
									<span class="name">{{ item.text }}</span>
									<span class="desc">{{ item.remark }}</span>
								</div>
							</template>
							<div
								class="collect-box"
								@click.stop="
									collectedList.includes(item.id) ? deleteBatchIds(item, 0) : saveCollect(item, 0)
								"
							>
								<div v-if="collectedList.includes(item.id)" style="color: #ffdc86">
									<i class="el-icon-star-on icon"></i>
									<span>已收藏</span>
								</div>
								<div>
									<i class="el-icon-star-off icon"></i>
									<span>收藏</span>
								</div>
								<img src="@/assets/images/serveHall/active-bg.png" class="active-bg" />
							</div>
						</div>
					</div>
					<div v-if="serviceListSearch.length < 1" class="none-box"><Empty /></div>
				</div>
			</div> -->
		</div>
		<es-dialog ref="visible" title="业务直通车" size="max" :visible.sync="visible">
			<iframe
				ref="iframe"
				src=""
				width="100%"
				height="100%"
				frameborder="0"
				allowfullscreen
				allow-same-origin
			>
				<div>浏览器不兼容</div>
			</iframe>
		</es-dialog>
		<Navigation />
	</div>
</template>

<script>
import VocationalHeader from './header/VocationalHeader.vue';
import BackButtom from '@/components/backButtom.vue';
import Search from './components/search.vue';
import Empty from './components/empty.vue';
import Navigation from './components/navigation.vue';

import { tenantId, alumniUrl } from '@/config';
import { getTransactionList, checkLogin, getEossAuthentication } from '@/api/home.js';

export default {
	name: 'ServeHall',
	components: {
		VocationalHeader,
		BackButtom,
		Search,
		Empty,
		Navigation
	},
	data() {
		return {
			serviceList: [], //数据列表，接口查出来的全部数据
			serviceData: [], //用于展示的数据，用于前端筛选的数据
			serviceChildrenAll: [],
			serviceListSearch: [],
			collectedList: [],
			searchValue: '',
			visible: false,
			type: '' //展示的类型数据
		};
	},
	created() {
		this.getCollected();
		this.type = this.$route.query.type || '';
		if (this.$route.query.type) {
			this.getList();
		} else {
			this.getListData();
		}
	},
	methods: {
		//业务直通车列表接口
		getListData() {
			let userId = localStorage.getItem('mobileUserId') || '';
			/**原有代码基础加上判断，没有授权的话请求授权*/
			if (!userId) {
				// checkLogin()
				this.$.ajax({
					url: checkLogin
				}).then(res => {
					let code = res.results.code;
					localStorage.setItem('ssoCode', res.results.code);
					let data = {
						serverId: 'ybzyDtcSso',
						authType: '6',
						code
					};
					if (!(code === null)) {
						this.$.ajax({
							url: getEossAuthentication,
							method: 'POST',
							data: data
						})
							.then(res => {
								localStorage.setItem('mobileToken', res.results.mobileToken);
								localStorage.setItem('mobileUserId', res.results.userId);
								this.getListData();
							})
							.catch(err => {
								console.log(err, '认证失败err');
								return '认证失败';
							});
					}
				});
			} else {
				this.$.ajax({
					url: getTransactionList + `?menuCode=cygn&userId=${userId}&type=2`
				})
					.then(res => {
						this.serviceList = res.results;
						this.serviceListSearch = res.results;
						
						let arr = [];
						res.results.forEach(i => {
							i.children.forEach(item => {
								arr.push(item);
							});
						});
						this.serviceChildrenAll = arr;
					})
					.catch(error => {
						console.log(error, 'error');
					});
			}
		},
		// 系统直通车列表数据
		getList() {
			this.$.ajax({
				url: '/ybzy/platuser/front/appList',
				method: 'post'
			}).then(res => {
				if (res.rCode === 0) {
					this.serviceList = res.results || [];
					this.serviceListSearch = res.results || [];
				} else {
					this.$message.error(res.msg);
				}
			});
		},
		// 搜索
		onSearch(e) {
			this.searchValue = e;
			const searchQuery = e.trim().toLowerCase(); // 将搜索关键词去除前后多余空格并转为小写
			if (!searchQuery) {
				// 数据恢复
				this.serviceListSearch = this.serviceList;
			} else {
				if (this.type == 'system') {
					let appList = [];
					// 系统直通车数据筛选
					appList = this.serviceList.reduce((acc, cur) => {
						if (cur.name.trim().toLowerCase().includes(searchQuery)) {
							acc.push(cur);
						}
						return acc;
					}, []);
					this.serviceListSearch = appList;
				} else {
					// 业务直通车筛选
					let arr = [];
					arr = this.serviceChildrenAll.filter(
						// 在所有服务子项集合中过滤匹配项
						i => {
							const text = i.text.trim().toLowerCase();
							return text.includes(searchQuery); // 如果搜索关键词包含子项的文本（去除多余空格并转为小写）则保留该子项
						}
					);
					this.serviceListSearch = [];
					if(arr.length){
						this.serviceListSearch.push({
							children:arr
						})
					}
					
					// this.serviceListSearch.children = arr;
					// this.serviceListSearch = arr; // 将过滤后匹配的服务子项赋值给this.serviceListSearch
				}
			}
		},
		// 应用跳转对应链接判断
		myAppDataBtn(item) {
			this.saveRecent(item, 0);
			// /ybzy/platauth/front/loginEoss?redirectUri=/project-ybzy/ybzy/index.html#/main?cookie=1
			if (item.url.includes('redirectUri=/project-ybzy/ybzy/index.html')) {
				window.location.href = item.url + '&isTeacher=true';
			} else {
				window.open(item.url);
			}
		},
		// 业务直通车跳转判断
		toOpenWindow(item) {
			if (item.id == '11111111') {
				return;
			}
			this.visible = true;
			this.$nextTick(() => {
				this.$refs.iframe.src = `${alumniUrl}${item.url}`;
			});
			this.saveRecent(item, 1);
		},
		/**
		 * @description 最近访问--创建
		 * */
		saveRecent(item, type) {
			let data = {
				sourceId: item.id, //目标Id
				type: 1, // 0, "收藏"  1, "最近访问" 2, "公告已读"
				sourceType: type //目标类型（0：系统，1：菜单）
			};
			this.$.ajax({
				url: '/ybzy/specificHomepageShortcut/save',
				method: 'POST',
				data: data
			});
		},
		//处理图片路径
		handelPicUrl(url) {
			return `${alumniUrl}/static/andyui2.0/css/picIcon/img/${url}`;
		},
		/**
		 * @description 收藏--查询
		 * */
		getCollected() {
			this.$.ajax({
				url: '/ybzy/specificHomepageShortcut/getList',
				params: {
					pageSize: 1000,
					type: 0 // 0, "收藏"  1, "最近访问" 2, "公告已读"
				}
			}).then(res => {
				if (res.rCode == 0) {
					let list = res?.results?.records || [];
					this.collectedList = list.reduce((acc, cur) => {
						acc.push(cur.sourceId);
						return acc;
					}, []);
				}
			});
		},
		/**
		 * @description 收藏--创建
		 * */
		saveCollect(item, type) {
			let data = {
				sourceId: item.id, //目标Id
				sourceType: type //目标类型（0：系统，1：菜单）
			};
			this.$.ajax({
				url: '/ybzy/specificHomepageShortcut/saveCollect',
				method: 'POST',
				data: data
			})
				.then(res => {
					if (res.rCode == 0) {
						this.$message.success(res.msg);
						this.getCollected();
					} else {
						this.$message.info(res.msg);
					}
				})
				.catch(res => {
					this.$message.warning(res.msg);
				});
		},
		/**
		 * @description 取消收藏--查询
		 * */
		deleteBatchIds(item) {
			let ids = [];
			ids.push(item.id);
			this.$.ajax({
				url: '/ybzy/specificHomepageShortcut/deleteCollect',
				method: 'POST',
				data: {
					ids: item.id //目标Id
				}
			})
				.then(res => {
					if (res.rCode == 0) {
						this.$message.success(res.msg);
						this.getCollected();
					} else {
						this.$message.info(res.msg);
					}
				})
				.catch(res => {
					this.$message.warning(res.msg);
				});
		}
	}
};
</script>

<style lang="scss" scoped>
.serve-car {
	background-image: url(~@/assets/images/serveHall/serve-bg.png);
	background-size: 100% calc(100% - 60px);
	background-position-y: 60px;
	font-family: MicrosoftYaHei;
	width: 100%;
	height: 100%;
	overflow-y: scroll;

	.hall-top {
		width: 100%;
		background: #0175e8;
		position: sticky;
		top: 0;
		z-index: 10;
	}
	.car-btn {
		margin: 20px auto;
	}
	.car {
		width: 1200px;
		margin: 0 auto;
		padding-bottom: 60px;

		// 隐藏滚动条
		&::-webkit-scrollbar {
			display: none;
		}

		.car-search {
			display: flex;
			justify-content: center;
			margin-top: 50px;
		}
		.car-box {
			width: 100%;
			border-radius: 8px;
			padding: 20px 20px 30px 20px;
			width: 1200px;
			min-height: 504px;
			background: rgba(0, 0, 0, 0.5);
			position: relative;

			.title-box {
				display: flex;
				align-items: center;
				margin-top: 10px;
				.left-arrows {
					width: 12px;
					height: 16px;
					margin-right: 10px;
				}
				.text {
					font-size: 16px;
					font-family: MicrosoftYaHei, MicrosoftYaHei;
					font-weight: bold;
					color: #95daff;
					line-height: 1;
				}
			}
			.collect-box {
				position: absolute;
				right: 5px;
				top: 5px;
				padding: 0 8px;
				height: 22px;
				line-height: 20px;
				background: rgba(0, 0, 0, 0.2);
				border-radius: 10px;
				border: 1px solid rgba(255, 255, 255, 0.4);
				color: #ffffff;
				font-size: 12px;
				font-family: MicrosoftYaHei;
				display: flex;
				align-items: center;
				display: none;
				z-index: 3;
				.icon {
					font-size: 12px;
					margin-right: 4px;
				}
			}
			.active-bg {
				width: 124px;
				height: 74px;
				position: absolute;
				right: 10px;
				bottom: 0;
				display: none;
			}
			.service-list {
				display: flex;
				flex-wrap: wrap;
				margin-top: 20px;
				.service-item {
					width: 224px;
					height: 90px;
					padding: 16px 15px;
					border-radius: 8px;
					margin-right: 10px;
					margin-bottom: 10px;
					display: flex;
					align-items: center;
					background: rgba(0, 0, 0, 0.35);
					cursor: pointer;
					position: relative;
					&:nth-child(5n) {
						margin-right: 0;
					}
					.item-img {
						width: 58px;
						height: 58px;
						border-radius: 12px;
						margin-right: 10px;
					}
					.item-right {
						display: flex;
						flex-direction: column;
						.name {
							font-size: 14px;
							font-family: MicrosoftYaHei;
							line-height: 20px;
							color: #ffffff;
						}
						.desc {
							font-size: 12px;
							font-family: ArialMT;
							color: #808890;
							line-height: 18px;
							margin-top: 4px;
						}
					}
					&:hover {
						background: linear-gradient(180deg, #4fcdff 0%, #0076ec 100%);
						box-shadow: 0px 2px 10px 0px rgba(0, 38, 75, 0.5);
						.collect-box {
							display: block;
						}
						.active-bg {
							display: block;
						}
						.name,
						.desc {
							color: #ffffff;
						}
					}
				}
			}
			.none-box {
				margin-top: 150px;
			}
		}
	}
}
</style>
