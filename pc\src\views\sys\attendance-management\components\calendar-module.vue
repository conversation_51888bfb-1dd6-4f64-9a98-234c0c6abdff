<template>
	<div class="main-center-right">
		<div class="main-center-right-title title">
			<img class="title-img" src="@ast/images/sys/card.png" alt="" />
			<div>我的考勤</div>
		</div>
		<!--  日历  -->
		<vCalendar
			class="main-center-right-calendar"
			:attributes="attributes"
			@dayclick="changeDate"
		></vCalendar>
		<!--  打卡详情  -->
		<div class="main-center-right-subTitle">
			<div class="subTitle">上下班打卡（工作9小时30分钟）</div>
			<div class="desc">打卡规则：周一至周五打卡</div>
		</div>
		<div class="main-center-right-time">
			<div class="time-line">
				<div class="time-line-sTime">9:00</div>
				<div class="time-line-line"></div>
				<div class="time-line-eTime">17:00</div>
			</div>
			<div class="time-detail">
				<div class="time-detail-start">
					<div class="time">上班</div>
					<div class="desc">正常（9:00）</div>
				</div>
				<div class="time-detail-end">
					<div class="time">下班</div>
					<div class="desc">正常（17:00）</div>
				</div>
				<div class="time-detail-add">加班：1小时30分钟</div>
			</div>
		</div>
		<!--  图表  -->
		<div class="main-center-right-chart">
			<div id="moonChat" class="chart-con"></div>
			<div class="chart-desc">
				<div v-for="(item, index) of tabs" :key="index" class="chart-desc-item">
					<div v-if="item.key" class="chart-desc-item-value">1</div>
					<div v-else class="chart-desc-item-novalue">-</div>
					<div class="chart-desc-item-name">{{ item.name }}</div>
				</div>
			</div>
		</div>
	</div>
</template>

<script>
export default {
	name: 'CalendarModule',
	data() {
		return {
			/**自定义打卡日期的标记*/
			attributes: [
				{
					key: 'vODay',
					dates: new Date(),
					highlight: {
						fillMode: 'light',
						contentStyle: {
							color: '#ffffff'
						},
						style: {
							backgroundColor: '#0377e8'
						}
					},
					dot: false
					// popover: {
					// 	label: '美好的一天!要开心呦!'
					// }
				},
				{
					key: 'V1Day',
					dates: [
						new Date('2023/11/11'),
						new Date('2023/11/12'),
						new Date('2023/11/13'),
						new Date('2023/11/14')
					],
					dot: {
						style: {
							backgroundColor: '#0377e8',
							width: '8px',
							height: '8px'
						}
					}
				},
				{
					key: 'V2Day',
					dot: {
						style: {
							backgroundColor: '#de7963',
							width: '8px',
							height: '8px'
						}
					},
					dates: new Date('2023/11/15')
				}
			],
			/**考勤详情统计展示的tab*/
			tabs: [
				{
					name: '迟到',
					key: 'delay'
				},
				{
					name: '早退',
					key: ''
				},
				{
					name: '缺卡',
					key: ''
				},
				{
					name: '旷工',
					key: ''
				},
				{
					name: '请假',
					key: ''
				},
				{
					name: '外出',
					key: ''
				},
				{
					name: '出差',
					key: ''
				},
				{
					name: '外勤',
					key: ''
				}
			],
			/**饼状图的数据*/
			pieData: [
				{ value: 21, name: '正常天数' },
				{ value: 2, name: '异常天数' }
			],
			moonChart: null // 月考勤统计的表格实例
		};
	},
	computed: {
		/**月考勤统计*/
		moonOption() {
			return {
				tooltip: {
					trigger: 'item'
				},
				legend: {
					right: '10%',
					top: '32%',
					orient: 'vertical',
					itemGap: 20,
					icon: 'circle',
					itemWidth: 8,
					formatter: name => {
						let obj = this.pieData.find(item => {
							return item.name === name;
						});
						if (name === '正常天数') {
							return name + '{a|' + obj.value + '}';
						} else {
							return name + '{b|' + obj.value + '}';
						}
					},
					textStyle: {
						rich: {
							a: { color: '#0377E8', fontSize: 18, fontWeight: 'bold' },
							b: {
								color: '#DE7963',
								fontSize: 18,
								fontWeight: 'bold'
							}
						}
					}
				},
				color: ['#0377E8', '#DE7963'],
				series: [
					{
						name: '考勤',
						type: 'pie',
						radius: ['50%', '90%'],
						center: ['25%', '55%'],
						avoidLabelOverlap: false,
						label: {
							show: false,
							position: 'center'
						},
						emphasis: {
							label: {
								show: false
							}
						},
						labelLine: {
							show: false
						},
						data: this.pieData
					}
				]
			};
		}
	},
	mounted() {
		this.initMoon();
	},
	methods: {
		/**初始化月考勤饼状图*/
		initMoon() {
			let charDom = document.getElementById('moonChat');
			this.moonChart = this.$echarts.init(charDom);
			this.moonChart.setOption(this.moonOption);
			window.addEventListener('resize', () => {
				this.moonChart && this.moonChart.resize();
			});
		},
		/**切换日期*/
		changeDate(date) {
			console.log(date);
		}
	}
};
</script>

<style scoped lang="scss">
@import '@ast/style/public.scss';
/**日历*/
.main-center-right {
	background: #fff;
	flex: 1;
	margin-left: 12px;
	border-radius: 8px;
	padding: 12px 20px;
	&-title {
		margin-bottom: 20px;
		padding-bottom: 12px;
		border-bottom: 1px solid rgba(241, 244, 247, 1);
	}
	&-calendar {
		width: 100%;
		height: 340px;
		background: #fbfaff;
		::v-deep .vc-day {
			min-height: 50px !important;
		}
	}
	&-subTitle {
		margin-top: 20px;
		background: #fbfaff;
		padding: 8px 12px;
		border-radius: 8px;
		.subTitle {
			font-weight: 600;
			margin-bottom: 4px;
		}
		.desc {
			color: #808998;
		}
	}
	&-time {
		margin: 8px 0;
		@include flexBox(flex-start);
		width: 100%;
		position: relative;
		.time-line {
			@include flexBox();
			flex-direction: column;
			margin-right: 8px;
			flex-shrink: 0;
			font-size: 18px;
			&-sTime {
				font-weight: 600;
			}
			&-line {
				width: 1px;
				height: 60px;
				background: #f1f4f7;
			}
			&-eTime {
				font-weight: 600;
			}
		}
		.time-detail {
			flex: 1;
			&-start {
				border-bottom: 1px solid #f1f4f7;
				padding: 16px 0;
			}
			&-end {
				padding: 16px 0;
			}
			&-add {
				background: #fbfaff;
				padding: 12px;
				color: #808998;
				position: absolute;
				bottom: 15px;
				right: 0;
				border-radius: 8px;
			}
			.time {
				font-weight: 600;
				font-size: 18px;
				margin-bottom: 4px;
			}
			.desc {
				color: #808998;
			}
		}
	}
	&-chart {
		@include flexBox(space-between);
		.chart-con {
			width: 60%;
			height: 120px;
			flex-shrink: 0;
		}
		.chart-desc {
			@include flexBox(flex-end);
			flex-wrap: wrap;
			&-item {
				@include flexBox();
				flex-direction: column;
				margin-left: 10px;
				&-value {
					height: 24px;
					color: #de7963;
					font-weight: 600;
					font-size: 20px;
				}
				&-novalue {
					height: 24px;
					color: #f1f3fb;
					font-weight: 600;
					font-size: 20px;
				}
				&-name {
				}
			}
		}
	}
}
.title {
	display: flex;
	align-items: center;
	font-size: 16px;
	font-weight: 550;
	&-img {
		width: 22px;
		height: 22px;
		margin-right: 8px;
	}
}
</style>
