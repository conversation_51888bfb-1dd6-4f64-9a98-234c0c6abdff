<template>
	<div v-loading="loading" class="content">
		<es-data-table
			ref="table"
			:key="dataTableUrl"
			:row-style="tableRowClassName"
			:thead="thead"
			:toolbar="toolbar"
			:border="true"
			:page="pageOption"
			:url="dataTableUrl"
			:numbers="true"
			:immediate="true"
			:param="params"
			close
			form
			@btnClick="btnClick"
		></es-data-table>
		<es-dialog
			v-if="showForm"
			:title="formTitle"
			:visible.sync="showForm"
			:drag="false"
			width="1200px"
			height="92%"
			:close-on-click-modal="false"
			:destroy-on-close="true"
		>
			<resumeView
				:form-title="formTitle"
				:info="formData"
				:cmsmodel-code-list="cmsmodelCodeList"
				:cmsnode-code-list="cmsnodeCodeList"
				@close="
					() => {
						$refs.table.reload();
						showForm = false;
					}
				"
			></resumeView>
		</es-dialog>
	</div>
</template>

<script>
import { cmsinfoList, cmsmodelCode, cmsnodeCode, cmsinfoDetail } from '@/api/scientific-sys.js';
import { tenantId } from '@/config';
import resumeView from './components/view.vue';
export default {
	components: { resumeView },
	data() {
		return {
			loading: false,
			dataTableUrl: cmsinfoList,
			showForm: false,
			params: {
				nodeCode: 'kygg',
				tenantId: tenantId
			},
			toolbar: [
				{
					type: 'button',
					contents: [
						// {
						// 	text: '新增',
						// 	code: 'add',
						// 	icon: 'el-icon-circle-plus-outline',
						// 	type: 'primary'
						// },
						// {
						// 	text: '导出',
						// 	exportXls: true,
						// 	// badge: 12
						// }
					]
				},
				{
					type: 'search',
					contents: [{ type: 'text', name: 'title', placeholder: '请输入文章标题' }]
				}
			],

			pageOption: {
				layout: 'total, prev, pager, next, jumper',
				pageSize: 10,
				// hideOnSinglePage: true,
				position: 'right',
				current: 1,
				pageNum: 1
			},
			formData: {},
			formTitle: '查看',
			cmsmodelCodeObj: {}, // 文章模型编码
			cmsmodelCodeList: [], // 文章模型列表
			cmsnodeCodeList: [] // 所属目录列表
		};
	},
	computed: {
		thead() {
			return [
				{
					title: '文章编号',
					width: 180,
					align: 'center',
					// showOverflowTooltip: true,
					field: 'id'
				},
				{
					title: '文章标题',
					minWidth: 120,
					align: 'center',
					showOverflowTooltip: true,
					field: 'title'
				},
				{
					title: '文章编码',
					align: 'center',
					field: 'code'
				},
				{
					title: '文章模型',
					align: 'center',
					field: 'siteId',
					render: (h, data) => {
						return h('span', this.cmsmodelCodeObj[data.row.siteId]);
					}
				},
				{
					title: '发布时间',
					align: 'center',
					minWidth: 80,
					field: 'createTime'
				},
				{
					title: '排序',
					align: 'center',
					minWidth: 30,
					field: 'sortIndex'
				},
				// {
				// 	title: '状态',
				// 	align: 'center',
				// 	width: 70,
				// 	field: 'status',
				// 	render: (h, data) => {
				// 		return h(
				// 			'el-tag',
				// 			{
				// 				props: { type: data.row.status === 1 ? 'success' : 'danger' }
				// 			},
				// 			data.row.status === 1 ? '启用' : '禁用'
				// 		);
				// 	}
				// },
				{
					title: '操作',
					type: 'handle',
					width: 120,
					template: '',
					align: 'center',
					events: [{ code: 'view', text: '查看', icon: 'el-icon-view' }]
				}
			];
		}
	},
	created() {
		this.cmsmodelCode();
		this.cmsnodeCode();
	},
	methods: {
		// 文章模型
		cmsmodelCode() {
			this.$request({
				url: cmsmodelCode,
				params: { status: 1, pageNum: 1, pageSize: -1 },
				method: 'GET'
			}).then(res => {
				if (res.rCode === 0 && res.results.length > 0) {
					const codes = {};
					res.results.forEach(e => {
						codes[e.tenantId] = e.name;
					});
					this.cmsmodelCodeObj = codes;
					this.cmsmodelCodeList = res.results;
				}
			});
		},
		// 所属目录
		cmsnodeCode() {
			this.$request({
				url: cmsnodeCode,
				params: { pageNum: 1, pageSize: -1 },
				method: 'GET'
			}).then(res => {
				if (res.rCode === 0 && res.results.length > 0) {
					this.cmsnodeCodeList = res.results;
				}
			});
		},
		//表格行高
		tableRowClassName({ row, rowIndex }) {
			let styleRes = { height: '54px !important' };
			return styleRes;
		},
		inputChange(key, value) {},
		//操作按钮点击事件
		btnClick(res) {
			let code = res.handle.code;
			switch (code) {
				case 'view':
					this.formTitle = '查看';
					this.loading = true;
					this.$request({ url: cmsinfoDetail, params: { id: res.row.id }, method: 'GET' }).then(
						res => {
							this.loading = false;
							if (res.rCode === 0) {
								this.formData = res.results;
								this.showForm = true;
							}
						}
					);
					break;
				default:
					break;
			}
		},

		refreshData() {
			this.$refs.table.reload();
		}
	}
};
</script>

<style scoped lang="scss">
.content {
	width: 100%;
	height: 100vh;
}
</style>
