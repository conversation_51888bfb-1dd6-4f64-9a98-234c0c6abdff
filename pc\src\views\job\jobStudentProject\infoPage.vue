<template>
  <div class="sketch_content">
    <el-form :model="formData" class="resumeTable" :rules="rules" ref="form">
      <el-collapse v-model="activeName">
        <el-collapse-item title="基本信息" :name="1" >
          <el-row>
            <el-col :span="12">
              <el-form-item label="项目名称" label-width="90px" label-position="left" prop="name">
                <el-input v-model="formData.name" :disabled="infoDisabled"></el-input>
              </el-form-item>
            </el-col>
          </el-row>
          <el-row>
            <el-col :span="22">
              <el-form-item label="项目地址" label-width="90px" label-position="left" prop="address">
                <el-input v-model="formData.address" :disabled="infoDisabled"></el-input>
              </el-form-item>
            </el-col>
          </el-row>
          <el-row>
            <el-col :span="11">
              <el-form-item label="选择地点" label-width="90px" label-position="left">
                <baidu-map
                    v-if="mapRefresh"
                    :center="center"
                    :zoom="13"
                    class="baiduMap"
                    :scroll-wheel-zoom="true"
                    @click="mapClick"
                    @ready="mapHandler" >
                  <bm-view style="width: 100%; height: 150px; flex: 1"></bm-view>
                  <bmChooseAddressGT
                      ref="baiduMap"
                      :show-map="showMapIntLo"
                      :gt="this.corMapCol"
                      @checkedAddress="checkedAddress"
                      @closeMapDialog="closeMapDialog"
                  ></bmChooseAddressGT>
                </baidu-map>
              </el-form-item>
            </el-col>
            <el-col :span="11">
              <el-form-item label="项目封面" label-width="90px" label-position="left">
                <es-upload v-bind="coverAttrs" v-model="formData.coverUrl" :disabled="infoDisabled"
                           select-type="icon-plus" list-type="picture"></es-upload>
              </el-form-item>
            </el-col>
          </el-row>
          <el-row>
            <el-col :span="11">
              <el-form-item label="发起人" label-width="90px" label-position="left" prop="createUserName">
                <el-input v-model="formData.createUserName" :disabled="true"></el-input>
              </el-form-item>
            </el-col>
          </el-row>
          <el-row>
            <el-col :span="11">
              <el-form-item label="电话号码" label-width="90px" label-position="left" prop="telephone">
                <el-input v-model="formData.telephone" :disabled="infoDisabled"></el-input>
              </el-form-item>
            </el-col>
            <el-col :span="11">
              <el-form-item label="电子邮箱" label-width="90px" label-position="left" prop="email">
                <el-input v-model="formData.email" :disabled="infoDisabled"></el-input>
              </el-form-item>
            </el-col>
          </el-row>
          <el-row>
            <el-col :span="22">
              <el-form-item label="项目简介" label-width="90px" label-position="left" prop="profiles">
                <el-input v-model="formData.profiles" :disabled="infoDisabled" type="textarea"></el-input>
              </el-form-item>
            </el-col>
          </el-row>
          <el-row>
            <el-col :span="6">
              <el-form-item label="状态" label-width="90px" label-position="left" prop="status">
                <el-switch class="switchStyle" v-model="formData.status" :disabled="infoDisabled" active-text="启用" inactive-text="禁用"></el-switch>
              </el-form-item>
            </el-col>
            <el-col :span="18">
              <el-form-item label="审核状态" label-width="90px" label-position="left" prop="auditStatus">
                <el-radio-group v-model="formData.auditStatus" :disabled="infoDisabled">
                  <el-radio :label="0">待审核</el-radio>
                  <el-radio :label="1">已通过</el-radio>
                  <el-radio :label="2">已驳回</el-radio>
                  <el-radio :label="3">已结束</el-radio>
                </el-radio-group>
              </el-form-item>
            </el-col>
          </el-row>
          <el-row>
            <el-col :span="22" v-show="auditVisible">
              <el-form-item label="审核意见" label-width="90px" label-position="left" prop="auditOpinion"
                            :rules="[{ required: this.auditBtnVisible, message: '请输入审核意见', trigger: 'blur' }]">
                <el-input v-model="formData.auditOpinion" :disabled="auditDisabled" type="textarea" ></el-input>
              </el-form-item>
            </el-col>
          </el-row>
        </el-collapse-item>
        <el-collapse-item title="项目介绍" :name="2" >
          <el-row>
            <el-col :span="24">
              <el-form-item prop="introduction">
                <div style="height:100%">
                  <wangeditor v-model="formData.introduction" :read-only="infoDisabled"></wangeditor>
                </div>
              </el-form-item>
            </el-col>
          </el-row>
        </el-collapse-item>
      </el-collapse>
      <el-row><br></el-row>
      <el-row>
        <el-col :span="3" style="float:right">
          <el-button type="reset" @click="infoPageClose(false)">取消</el-button>
        </el-col>
        <el-col :span="3" style="float:right" v-show="!infoDisabled">
          <el-button type="primary" @click="handleFormSubmit">保存</el-button>
        </el-col>
        <el-col :span="3" style="float:right" v-show="auditBtnVisible">
          <el-button type="danger" @click="handleFormAudit('驳回')">驳回</el-button>
        </el-col>
        <el-col :span="4" style="float:right" v-show="auditBtnVisible">
          <el-button type="primary" @click="handleFormAudit('审核通过')">审核通过</el-button>
        </el-col>
      </el-row>
    </el-form>
  </div>
</template>
<script>

import api from "@/http/job/jobStudentProject/api";
import httpApi from "@/http/job/jobStudentProject/api";
import bmChooseAddressGT from "@/components/map/bmChooseAddressGT.vue";

export default {
  name: 'infoPage',
  components: {bmChooseAddressGT},
  props: {
    baseData: {
      type: Object
    },
    infoPageMode: {
      type: String
    }
  },
  data() {
    return {
      // 地图相关
      center: {
        lng: 116.395645038,
        lat: 39.9299857781
      },
      BMap: {},
      map: {},
      corMapCol: null,
      showMapIntLo: false,
      mapRefresh: true,
      // 地图相关end
      infoDisabled: false,
      auditDisabled: false,
      auditVisible: true,
      auditBtnVisible: false,
      formData: {},
      pageMode: 'allOn',
      activeName: [1,2],
      rules: {
        name: { required: true, message: '请输入项目名称', trigger: 'blur' },
        introduction: { required: true, message: '请输入项目介绍', trigger: 'blur' },
        address: { required: true, message: '请输入输入项目地址', trigger: 'blur' },
        telephone: { required: true, message: '请输入电话号码', trigger: 'blur' },
        email: { required: true, message: '请输入电子邮箱', trigger: 'blur' },
      }
    };
  },
  computed: {
    coverAttrs() {
      return {
        code: 'job_student_project_cover',
        ownId: this.formData.id,
        portrait: true,
        preview: true,
        download: true,
        operate: true,
      }
    }
  },
  created() {
    this.formData = { ...this.baseData };
    this.pageMode = this.infoPageMode;
    switch (this.pageMode){
      case '审核':
        this.infoDisabled = true;
        this.auditDisabled = false;
        this.auditVisible = true;
        this.auditBtnVisible = true;
        break;
      case 'allOn': case '新增': case '编辑':
        this.infoDisabled = false;
        this.auditDisabled = true;
        this.auditVisible = false;
        this.auditBtnVisible = false;
        break;
      case '查看':
        this.infoDisabled = true;
        this.auditDisabled = true;
        this.auditVisible = true;
        this.auditBtnVisible = false;
        break;
    }
  },
  methods: {
    handleFormSubmit(){
      this.$refs.form.validate((valid) => {
        if(valid){
          //处理请求数据
          let saveData = {...this.formData};

          if(typeof saveData.coverUrl == 'object'){
            saveData.cover = saveData.coverUrl.response.adjunctId;
            this.$delete(saveData,'coverUrl');
          } else if(!saveData.coverUrl.startsWith('http')){
            saveData.cover = saveData.coverUrl;
            this.$delete(saveData,'coverUrl');
          }else {this.$delete(saveData,'coverUrl');}
          saveData.status = saveData.status === true ? 1:0;

          this.$request({
            url:
                this.pageMode === '新增'
                    ? api.jobStudentProjectSave
                    : api.jobStudentProjectUpdate,
            data: saveData,
            method: 'POST'
          }).then(res => {
            if (res.rCode === 0) {
              this.$message.success('操作成功');
              this.infoPageClose(true);
            } else {
              this.$message.error(res.msg);
            }
          });
        }
      })
    },
    handleFormAudit(res){
      //校验
      this.$refs['form'].validate((valid) => {  //开启校验
        if (valid) {   // 如果校验通过，请求接口
          let id = this.formData.id;
          let btnType = res;
          let auditStatus = -1;
          let auditOpinion = this.formData.auditOpinion;

          this.$confirm('是否确认'+btnType+'？', '审核', {
            confirmButtonText: '确定',
            cancelButtonText: '取消',
            type: 'warning'
          })
              .then(() => {
                switch (btnType){
                  case '审核通过': auditStatus = 1;break;
                  case '驳回': auditStatus = 2;break;
                }
                if(auditStatus !== -1){
                  this.$request({
                    url: httpApi.jobStudentProjectAudit,
                    data:{id: id,auditStatus: auditStatus, auditOpinion: auditOpinion},
                    method: 'POST'
                  }).then(response =>{
                    if(response.success){
                      this.$message.success('审核成功');
                      this.infoPageClose(true);
                    }else {
                      this.$message.error(response.msg);
                    }
                  })
                }
              }).catch(() => {});
        } else { return false; }//校验不通过
      });},
    infoPageClose(reload){
      this.pageMode = 'allOn';
      this.infoDisabled = false;
      this.$emit('activelyClose',reload);
    },
    //点击地图开启选择地点dialog
    mapClick(){
      if(this.formData.longitude ==null || this.formData.longitude === ''
         || this.formData.latitude == null || this.formData.latitude === ''){
        this.corMapCol = '116.403848,39.915089';
      }else {
        this.corMapCol = this.formData.longitude + ',' + this.formData.latitude;
      }
      //查看、审核状态不可选择地点
      if(!this.infoDisabled) this.showMapIntLo = true;
    },
    // 地图初始化
    mapHandler({ BMap, map }) {
      this.BMap = BMap;
      this.map = map;
      if(this.formData.longitude ==null || this.formData.longitude === ''
          || this.formData.latitude == null || this.formData.latitude === ''){
        this.addPoint('116.403848', '39.915089');
      }else {
        this.addPoint(this.formData.longitude, this.formData.latitude);
      }
    },
    // 添加点位
    addPoint(lng, lat) {
      let map = this.map;
      let BMap = this.BMap;
      map.clearOverlays();
      let point = new BMap.Point(lng, lat);
      let zoom = map.getZoom();
      setTimeout(() => {
        map.centerAndZoom(point, zoom);
      }, 0);
      var marker = new BMap.Marker(point); // 创建标注
      map.addOverlay(marker); // 将标注添加到地图中
    },
    checkedAddress(pointLngLat) {
      let lngLat = pointLngLat.split(',');
      this.formData.longitude = lngLat[0];
      this.formData.latitude = lngLat[1];
      this.showMapIntLo = false;
    },
    closeMapDialog() {
      this.showMapIntLo = false;
    }
  },
  watch: {
    //选定地点后刷新地图
    showMapIntLo(newV) {
      if(!newV){
        this.mapRefresh = false;
        this.$nextTick(() => {
          this.mapRefresh = true
        })
      }
    }
  }
};
</script>
<style scoped>
  .sketch_content {
    overflow: auto;
    height: 100%;
    border-top: 1px solid #eff1f4;
    border-bottom: 1px solid #eff1f4;
    padding: 0 30px 11px 27px;
  }
  .resumeTable td {
    height: 50px;
    font-weight: bold;
  }
  ::v-deep .switchStyle .el-switch__label {
    position: absolute;
    display: none;
    color: #fff;
  }
  ::v-deep .el-switch__core{
    background-color: rgba(166, 166, 166, 1) ;
  }
  ::v-deep .switchStyle .el-switch__label--left {
    z-index: 9;
    left: 20px;
  }
  ::v-deep .switchStyle .el-switch__label.is-active {
    display: block;
  }
  ::v-deep .switchStyle.el-switch .el-switch__core,
  .el-switch .el-switch__label {
    width: 60px !important;
  }

  ::v-deep .el-collapse-item__header.is-active {
    border-bottom: 1px solid #ebeef5;
    font-size: 18px;
    font-weight: bold;
  }
  ::v-deep .el-collapse-item__header {
    border-bottom: 1px solid #ebeef5;
    font-weight: bold;
  }
  ::v-deep .el-collapse-item__header::before {
    content: "";
    width: 4px;
    height: 18px;
    background-color: #0076E9;
    margin-right: 2px;
  }
</style>