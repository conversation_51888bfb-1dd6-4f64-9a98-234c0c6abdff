<template>
	<div style="border: 1px solid #ccc">
		<Toolbar
			style="border-bottom: 1px solid #ccc"
			:editor="editor"
			:default-config="toolbarConfig"
			:mode="mode"
		/>
		<Editor
			v-model="value"
			style="height: 500px; overflow-y: hidden"
			:default-config="editorConfig"
			:mode="mode"
			@onCreated="onCreated"
			@onChange="onChange"
		/>
	</div>
</template>

<script>
import { Editor, Toolbar } from '@wangeditor/editor-for-vue';
import axios from 'axios';
import { host } from '../../../config/config';

export default {
	// 这里这样命名为了兼容以前的代码
	name: 'Wangeditor',
	components: { Editor, Toolbar },
	props: {
		value: {
			type: String,
			default: ''
		},
		dataId: {
			//业务数据id，上传图片接口会用到
			type: String,
			default: ''
		},
		readOnly: {
			type: <PERSON><PERSON>an,
			default: false
		}
	},
	watch: {
		value(val) {
			if (val) {
				this.$emit('inputChange', val);
			}
		}
	},
	data() {
		return {
			editor: null,
			toolbarConfig: {
				// toolbarKeys: [ /* 显示哪些菜单，如何排序、分组 */ ],
				excludeKeys: ['fullScreen']
			},
			editorConfig: {
				placeholder: '',
				MENU_CONF: {
					uploadImage: {
						customUpload: this.customUpload
					},
					uploadVideo: {
						customUpload: this.customUpload
					}
				}
			},
			mode: 'default' // or 'simple'
		};
	},
	mounted() {},
	beforeDestroy() {
		const editor = this.editor;
		if (editor == null) return;
		editor.destroy(); // 组件销毁时，及时销毁编辑器
	},
	methods: {
		onCreated(editor) {
			this.editor = Object.seal(editor); // 一定要用 Object.seal() ，否则会报错
			if (this.readOnly) this.editor.disable();
			else this.editor.enable();
		},
		onChange(editor) {
			if ('<p><br></p>' === editor.getHtml()) {
				this.$emit('input', ' '); // 不要质疑这行代码，原来有bug(富文本编辑器会导致其他的输入框输入字符后立马失焦)，加上这行代码就正常了，我也不知道为什么
				this.$emit('input', '');
			} else {
				this.$emit('input', editor.getHtml());
			}
		},
		customUpload(file, insertFn) {
			let formData = new FormData();
			formData.append('file', file);
			formData.append('ownId', this.dataId);
			formData.append('code', 'platform_cms_dynamic');
			formData.append('isShowPath', true);
			let config = {
				transformRequest: [
					function (data, headers) {
						// 去除post请求默认的Content-Type
						delete headers.post['Content-Type'];
						return data;
					}
				]
			};
			axios.post(host + '/ybzy/cmsinfo/singleUpload', formData, config).then(res => {
				console.log(res);
				if (res.data.rCode === 0) {
					insertFn(host + 'ybzyfile' + res.data.results.fileOppositePath, '');
				} else {
					this.$message.error('上传失败');
				}
			});
		}
	}
};
</script>
<style src="@wangeditor/editor/dist/css/style.css"></style>
