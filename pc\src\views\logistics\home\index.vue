<!--
 * @Author: 张海滔
 * @Date: 2023-11-15 16:41:12
 * @LastEditors: 张海滔
 * @LastEditTime: 2023-11-20 15:35:46
 * @FilePath: /project-ybzy/pc/src/views/logistics/home/<USER>
 * @Description: 后勤管理首页
-->
<template>
	<div class="page">
		<div class="card-wrap">
			<mini-card v-for="(item, index) in cardData" :key="index" :card-data="item" />
		</div>
		<div class="warning-wrap">
			<titile-card title="原料预警" :img-url="require('@ast/images/sys/hdtj.png')">
				<template #content>
					<div style="height: 640px; width: 100%">
						<es-data-table
							ref="table"
							:row-style="tableRowClassName"
							:full="true"
							:fit="true"
							:thead="thead"
							:page="page"
							:param="dataTableParam"
							:url="tableUrl"
							:toolbar="toolbar"
							:border="true"
							:numbers="true"
							form
						></es-data-table>
					</div>
				</template>
			</titile-card>
		</div>
	</div>
</template>

<script>
import MiniCard from '@cpt/show/mini-card.vue';
import TitileCard from '@cpt/show/titile-card.vue';
import EPie from '@cpt/echarts/pie.vue';
import { groupTagsStatistics, getWarningList } from '@/api/logistics.js';
export default {
	name: 'HqHome',
	components: {
		MiniCard,
		TitileCard,
		EPie
	},
	data() {
		return {
			cardData: [
				{
					img: require('@ast/images/logistics/yllb.png'),
					title: '原料类别',
					unit: '种',
					num: '0'
				},
				{
					img: require('@ast/images/logistics/gys.png'),
					title: '供应商',
					unit: '家',
					num: '0'
				},
				{
					img: require('@ast/images/logistics/yls.png'),
					title: '原料数量',
					unit: '',
					num: '0'
				},
				{
					img: require('@ast/images/logistics/kfs.png'),
					title: '库房数量',
					unit: '个',
					num: '0'
				}
			],
			thead: [
        {
          title: '库房',
          field: 'roomName',
          align: 'center'
        },
				{
					title: '原料名称',
					field: 'name',
					align: 'center'
				},
				{
					title: '原料单位',
					field: 'unit',
					align: 'center'
				},
				{
					title: '预警阈值',
					field: 'warnPoint',
					align: 'center'
				},
				{
					title: '当前库存',
					field: 'curNum',
					align: 'center'
				}
			],
			tableUrl: getWarningList,
			toolbar: [
				{
					type: 'search',
					reset: true,
					contents: [
						{
							type: 'text',
							label: '关键字',
							name: 'keyword',
							placeholder: '请输入库房名称或原料名称'
						}
					]
				}
			],
			page: {
				pageSize: 10,
				totalCount: 0,
				hideOnSinglePage: true
			},
			dataTableParam: {}
		};
	},
	computed: {},
	mounted() {},
	created() {
		this.groupTagsStatistics();
	},
	methods: {
		/**
		 * 表格行高
		 */
		tableRowClassName() {
			return {
				height: '54px !important'
			};
		},
		// 获取card数据
		async groupTagsStatistics() {
			let res = await this.$.ajax({
				url: groupTagsStatistics
			});
			this.cardData = this.cardData.map(i => {
				return {
					...i,
					num: res.results.filter(item => item.name === i.title)[0].count || 0
				};
			});
		}
	}
};
</script>

<style lang="scss" scoped>
@import '@ast/style/public.scss';
.page {
	background: #f0f2f5;
	padding: 10px;
	overflow: auto;
	height: 100%;
	width: 100%;

	.card-wrap {
		@include flexBox(flex-start);
		width: 100%;
		background: #f0f2f5;

		.card {
			flex: 1;
			margin-right: 10px;
			&:last-child {
				margin-right: 0;
			}
		}
	}
	.warning-wrap {
		margin-top: 10px;
		border-radius: 8px;
		overflow: hidden;
		background: #ffffff;
		height: 730px;
	}
}
</style>
