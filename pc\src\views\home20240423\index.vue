<template>
	<div class="serve-hall">
		<!-- 顶部导航栏 -->
		<div class="hall-top">
			<VocationalHeader class="box-center"></VocationalHeader>
		</div>
		<!-- 导航切换 -->
		<div class="hall-nav" :style="{ width: navList.length * 144 + 'px' }">
			<div
				v-for="(item, index) in navList"
				:key="index"
				class="hall-nav_item"
				:class="{ active: item.name === navActive.name }"
				@click="navActive = item"
			>
				{{ item.name }}
			</div>
		</div>
		<div class="content-box">
			<!-- 动态组件 -->
			<component :is="navActive.component" :key="navActive.component"></component>
		</div>
	</div>
</template>

<script>
import VocationalHeader from './header/VocationalHeader.vue';
import ServeDashboard from './components/ServeDashboard.vue';
import ServeSystem from './components/ServeSystem.vue';
import ServePerson from './components/ServePerson/index.vue';
import StudentPerson from './components/ServePerson/student.vue';

import util from 'eoss-ui/src/utils/util';
import store from 'eoss-ui/src/utils/store';
export default {
	name: 'ServeHall',
	components: {
		VocationalHeader,
		ServeDashboard,
		ServeSystem,
		ServePerson,
		StudentPerson
	},

	data() {
		return {
			navList: [],
			navActive: {
				name: '办事大厅',
				component: 'ServeSystem'
			},
			LeaderRole: 'xLeader' // 校领导：xLeader 院领导：yLeader 都不是：noLeader
		};
	},
	computed: {},
	created() {
		// 模拟头部数据（显示头部调试用）
		const userInfo = localStorage.getItem('userInfo');
		if (window.location.href.includes('localhost') && !userInfo) {
			this.userInfo();
		}
		// 权限接口
		this.checkLeaderRole();

		// 主框架需要的东西如流程
		util.getMainConfig(res => {
			// sessionStorage.setItem('mainConfig', JSON.stringify(res));
			this.setConfig(res, 1);
		});
	},
	mounted() {
		this.getData();
	},
	methods: {
		setConfig(results, flag) {
			if (flag) {
				sessionStorage.setItem('mainConfig', JSON.stringify(results));
				util.setStorage({
					type: this.storage,
					key: {
						userId: results.userModel.userId,
						userName: results.userModel.username,
						useCaseCodes: results.resourceCodes || results.userModel.resourceCodes
					}
				});
			}
		},
		toolNavList() {
			const loginUserInfo = JSON.parse(localStorage?.getItem?.('loginUserInfo') || '{}');
			console.log(loginUserInfo, 'loginUserInfo');
			let arr = [];
			if (loginUserInfo?.deptName === '宜宾教育部') {
				arr = [
					{
						name: '管理驾驶舱',
						component: 'ServeDashboard'
					}
				];
			} else if (this.LeaderRole === 'xLeader' || this.LeaderRole === 'yLeader') {
				arr = [
					{
						name: '管理驾驶舱',
						component: 'ServeDashboard'
					},
					{
						name: '办事大厅',
						component: 'ServeSystem'
					},
					{
						name: '个人画像',
						component: 'ServePerson'
					}
				];
			} else if (this.LeaderRole === 'teacher') {
				// 老师
				arr = [
					{
						name: '办事大厅',
						component: 'ServeSystem'
					},
					{
						name: '个人画像',
						component: 'ServePerson'
					}
				];
			} else {
				// 学生
				arr = [
					{
						name: '办事大厅',
						component: 'ServeSystem'
					},
					{
						name: '个人画像',
						component: 'StudentPerson'
					}
				];
			}
			this.navList = arr;
			if (arr.length === 3) {
				this.navActive = arr[1];
			} else {
				this.navActive = arr[0];
			}
		},
		checkLeaderRole() {
			// 校领导：xLeader 院领导：yLeader 学生：student 老师：teacher
			this.$request({
				url: '/ybzy/platauth/front/checkLeaderRole'
			}).then(res => {
				if (res.rCode === 0) {
					// @-待改 调试写固定
					this.LeaderRole = res.results;
					// this.LeaderRole = 'student';
					localStorage.setItem('LeaderRole', this.LeaderRole);
					this.$store.dispatch('getInfo/getLoginUserInfo').then(userInfo => {
						this.toolNavList();
					});
					return;
				}
				console.log('获取权限失败！');
			});
		},
		userInfo() {
			localStorage.setItem(
				'userInfo',
				JSON.stringify({
					id: '1795285433095905282',
					createTime: '2024-05-28 10:46:17',
					updateTime: null,
					createUserId: null,
					createUserName: null,
					updateUserId: null,
					updateUserName: null,
					sortNum: null,
					remark: null,
					subsystemId: '7e826101abc149c9bd67f97f4bffc765',
					tenantId: 'ca0b39c476a94d0a8627f2d7adcefce4',
					nickname: '用户18911969345',
					username: '颜伟-dev',
					loginname: 'CS24052801',
					password: null,
					email: null,
					state: 1,
					phone: '18911969345',
					photoUrl: null,
					sex: null,
					age: null,
					authCode: 'e5068d72b49743939fe4236a04fbed0c',
					collegeName: '',
					majorName: null,
					isDefaultPasswork: false,
					identityInfo: [{ name: '教师', code: 'teacher' }]
				})
			);
		},
		async getData() {
			// const dataList = await xodbApi.get({
			// 	url: 'warehouseConfig_ybzy_dw_dwb_jw_xsjzsj_query'
			// });
			// console.log('------------getData-------------', dataList);
			// dataList.forEach(element => {
			// 	switch (element.mc) {
			// 		case 'A类（纯理论课）':
			// 			this.curriculum.cllk.digital = element.sl;
			// 			break;
			// 		case 'C类（纯实践课）':
			// 			this.curriculum.csjk.digital = element.sl;
			// 			break;
			// 		case 'B类（理论+实践课）':
			// 			this.curriculum.lljsjk.digital = element.sl;
			// 			break;
			// 		case '公共课':
			// 			this.curriculum.ggk.digital = element.sl;
			// 			break;
			// 		// case '公共选修课':
			// 		// 	this.curriculum.ggxxk.digital = element.sl;
			// 		// 	break;
			// 		case '专业必修课':
			// 			this.curriculum.zybxk.digital = element.sl;
			// 			break;
			// 		case '专业选修课':
			// 			this.curriculum.zyxxk.digital = element.sl;
			// 			break;
			// 		case '三年':
			// 			this.curriculum.snzkc.digital = element.sl;
			// 			break;
			// 		case '五年':
			// 			this.curriculum.wnzkc.digital = element.sl;
			// 			break;
			// 		default:
			// 	}
			// });
		}
	}
};
</script>

<style lang="scss" scoped>
$maxWidth: 1200px;
.box-center {
	width: $maxWidth;
	margin: 0 auto;
}
.serve-hall {
	width: 100%;
	height: 100%;
	background: url('~@/assets/images/home/<USER>') no-repeat center center / cover;
	// position: relative;
	overflow-y: scroll;
	overflow-x: hidden;
	.hall-top {
		width: 100%;
		background: #0175e8;
	}
	.hall-nav {
		width: 432px;
		height: 46px;
		background: rgba(255, 255, 255, 0.25);
		border-radius: 10px;
		border: 2px solid #ffffff;
		display: flex;
		margin: 18px auto 16px;
		font-family: MicrosoftYaHei, MicrosoftYaHei;
		font-weight: bold;
		font-size: 16px;
		color: #2c71b7;
		font-style: normal;
		cursor: pointer;
		.hall-nav_item {
			width: 144px;
			box-sizing: border-box;
			display: flex;
			justify-content: center;
			align-items: center;
			border-right: 1px solid #fff;
		}
	}
	.active {
		border-right: none;
		color: #053b6d;
		background: linear-gradient(270deg, #e9fffd 0%, rgba(255, 255, 255, 0) 100%);
		border-image: linear-gradient(272deg, rgba(255, 255, 255, 0), rgba(255, 255, 255, 1)) 1 1;
	}
	.content-box {
		width: 100%;
		height: 100%;
	}
}
</style>
