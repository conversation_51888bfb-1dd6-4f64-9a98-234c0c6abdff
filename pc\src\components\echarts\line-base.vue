<template>
	<div :id="id" style="width: 100%; height: 100%"></div>
</template>
<script>
export default {
	props: {
		id: {
			type: String,
			required: true
		},
		// title: {
		// 	type: String,
		// 	required: true
		// },
		// color: {
		// 	type: Array,
		// 	required: true
		// },
		seriesData: {
			type: Array,
			required: true
		},
		legendData: {
			type: Array,
			required: true,
			defalut: () => {
				return [];
			}
		}
	},
	data() {
		return {
			chart: null
		};
	},
	watch: {
		seriesData() {
			this.draw();
		}
	},
	mounted() {
		this.draw();
	},

	methods: {
		draw() {
			let option = {
				grid: {
					top: '15%',
					bottom: '5%' //也可设置left和right设置距离来控制图表的大小
				},
				tooltip: {
					trigger: 'axis',
					axisPointer: {
						type: 'cross',
						label: {
							backgroundColor: '#6a7985'
						}
					}
				},
				legend: {
					data: ['占比', '人数'],
					top: '2%',
					color: '#999',
					itemWidth: 16
				},
				xAxis: {
					data: this.legendData || [],
					axisLine: {
						show: false, //隐藏X轴轴线
						lineStyle: {
							color: '#999'
						}
					},
					axisTick: {
						show: false //隐藏X轴刻度
					},
					axisLabel: {
						show: true,
						color: '#999' //X轴文字颜色
					}
				},
				yAxis: [
					{
						type: 'value',
						name: '单位：人',
						min: 0,
						max: 1000,
						interval: 200,
						nameTextStyle: {
							padding: [0, 65, 20, 0]
						},
						splitLine: {
							show: false
						},
						axisTick: {
							show: false
						},
						axisLine: {
							show: false,
							lineStyle: {
								color: '#999'
							}
						},
						axisLabel: {
							show: true,
							textStyle: {
								color: '#999'
							}
						}
					},
					{
						type: 'value',
						name: '单位：%',
						min: 0,
						max: 100,
						interval: 20,
						nameTextStyle: {
							padding: [0, 0, 20, 65]
						},
						position: 'right',
						splitLine: {
							show: true
						},
						axisTick: {
							show: false
						},
						axisLine: {
							show: false
						}
					},
					{
						type: 'value',
						gridIndex: 0,
						min: 50,
						max: 100,
						splitNumber: 8,
						splitLine: {
							show: false
						},
						axisLine: {
							show: false
						},
						axisTick: {
							show: false
						},
						axisLabel: {
							show: false
						},
						splitArea: {
							show: true,
							areaStyle: {
								color: ['rgba(250,250,250,0.0)', 'rgba(250,250,250,0.05)']
							}
						}
					}
				],
				series: [
					{
						name: '占比',
						type: 'line',
						yAxisIndex: 1, //使用的 y 轴的 index，在单个图表实例中存在多个 y轴的时候有用
						// smooth: true, //平滑曲线显示
						showAllSymbol: true, //显示所有图形。
						symbol: 'circle', //标记的图形为实心圆
						symbolSize: 10, //标记的大小
						itemStyle: {
							normal: {
								color: 'rgb(4 120 232)', // 拐点的颜色
								borderColor: '#ffffff', // 拐点边框颜色
								borderWidth: 2 // 拐点边框大小
							}
						},
						lineStyle: {
							color: 'rgb(4 120 232)'
						},
						data: this.seriesData[0] || []
					},
					{
						name: '人数',
						type: 'bar',
						barWidth: 20,
						itemStyle: {
							color: new this.$echarts.graphic.LinearGradient(0, 0, 0, 1, [
								{
									offset: 0,
									color: '#0D7DE9'
								},
								{
									offset: 1,
									color: '#E5F1FD'
								}
							])
						},
						data: this.seriesData[1] || []
					}
				]
			};
			// if (this.axisData.length === 0) return;
			this.chart && this.chart.dispose();
			this.chart = this.$echarts.init(document.getElementById(this.id));
			// console.log('>>>this.chart', this.$echarts);
			this.chart.setOption(option);
			window.addEventListener('resize', () => {
				this.chart && this.chart.resize();
			});
		}
	}
};
</script>
