<template>
  <div class="sketch_content">
    <el-form :model="formData" class="resumeTable" :rules="rules" ref="form">
      <el-row>
        <el-col :span="22">
          <el-form-item label="分类名称" label-width="90px" label-position="left" prop="name">
            <el-input v-model="formData.name" :disabled="allDisabled"></el-input>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row>
        <el-col :span="8">
          <el-form-item label="所属类别" label-width="90px" label-position="left" prop="type">
            <el-select ref="type" v-model="formData.type" :disabled="allDisabled" placeholder="收入\支出" clearable >
              <el-option
                  v-for="item in types"
                  :key="item.id"
                  :label="item.name"
                  :value="item.id">
              </el-option>
            </el-select>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row><br></el-row>
      <el-row>
        <el-col :span="3" style="float:right">
          <el-button type="reset" @click="infoPageClose(false)">取消</el-button>
        </el-col>
        <el-col :span="3" style="float:right">
          <el-button type="primary" @click="handleFormSubmit" v-show="!allDisabled">保存</el-button>
        </el-col>
      </el-row>
    </el-form>
  </div>
</template>
<script>

import api from "@/http/society/societyIncomeExpendType/api";

export default {
  name: 'infoPage',
  props: {
    baseData: {
      type: Object
    },
    infoPageMode: {
      type: String
    }
  },
  data() {
    return {
      allDisabled: false,
      formData: {},
      pageMode: 'allOn',
      types: [{id:'1',name:'收入'},{id:'2',name:'支出'}],
      rules: {
        name: { required: true, message: '请输入分类名称', trigger: 'blur' },
        type: { required: true, message: '请选择类别', trigger: 'blur' },
      }
    };
  },
  computed: {
  },
  created() {
    this.formData = { ...this.baseData };
    this.pageMode = this.infoPageMode;
    switch (this.pageMode){
      case 'allOn':
      case '新增':
      case '编辑': this.allDisabled = false; break;
      case '查看': this.allDisabled = true;
    }
  },
  methods: {
    handleFormSubmit(){
      this.$refs.form.validate((valid) => {
        if(valid){
          let saveData = {...this.formData};
          //处理提交数据、
          if(typeof saveData.type == 'object')
            saveData.type = saveData.type.value;

          this.$request({
            url:
                this.pageMode === '新增'
                    ? api.societyIncomeExpendTypeSave
                    : api.societyIncomeExpendTypeUpdate,
            data: saveData,
            method: 'POST'
          }).then(res => {
            if (res.rCode === 0) {
              this.$message.success('操作成功');
              this.infoPageClose(true);
            } else {
              this.$message.error(res.msg);
            }
          });
        }
      })
    },
    infoPageClose(reload){
      this.pageMode = 'allOn';
      this.infoDisabled = false;
      this.$emit('activelyClose',reload);
    },
    closeMapDialog() {
      this.showMapIntLo = false;
    }
  },
  watch: {
  }
};
</script>
<style scoped>
  .sketch_content {
    overflow: auto;
    height: auto;
    border-top: 1px solid #eff1f4;
    border-bottom: 1px solid #eff1f4;
    padding: 0 30px 11px 27px;
  }
  .resumeTable td {
    height: 50px;
    font-weight: bold;
  }
</style>