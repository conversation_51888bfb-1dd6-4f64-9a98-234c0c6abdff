<template>
	<div class="content">
		<div style="width:100%">
			<el-menu :default-active="activeMenus" mode="horizontal" class="es-menu" @select="handleSelect">
				<el-menu-item v-for="(item) in menus" :key="item.key" :index="item.key">
					{{ item.label }}
				</el-menu-item>
			</el-menu>
			<es-data-table :row-style="tableRowClassName" v-if="activeMenus === '0'" ref="table" :key="tableCount"
				:full="true" :fit="true" :thead="thead" :toolbar="toolbar" :border="true" :page="pageOption"
				:url="dataTableUrl" :numbers="true" @btnClick="btnClick" :option-data="optionData"
				@sort-change="sortChange" :param="params" close>
			</es-data-table>
			<es-data-table :row-style="tableRowClassName" v-if="activeMenus === '1'" ref="table" :key="tableCount"
				:full="true" :fit="true" :thead="thead" :toolbar="toolbar" :border="true" :page="pageOption"
				:url="dataTableUrl" :numbers="true" @btnClick="btnClick" :option-data="optionData" form
				@sort-change="sortChange" :param="params" close>
			</es-data-table>
			<es-data-table :row-style="tableRowClassName" v-if="activeMenus === '2'" ref="table" :key="tableCount"
				:full="true" :fit="true" :thead="thead" :toolbar="toolbar" :border="true" :page="pageOption"
				:url="dataTableUrl" :numbers="true" @btnClick="btnClick" :option-data="optionData" form
				@sort-change="sortChange" :param="params" close>
			</es-data-table>
			<es-dialog :title="formTitle" :visible.sync="showForm" width="80%" height="80%"
				:close-on-click-modal="false" :destroy-on-close="true">
				<memberView :info="formData"></memberView>
			</es-dialog>
			<es-dialog :title="formTitle" :visible.sync="showAudit" width="40%" height="40%"
				:close-on-click-modal="false" :destroy-on-close="true">
				<memberAudit :id='ownId' @submit="confirmAudit"></memberAudit>
			</es-dialog>
			<es-dialog title="删除" :visible.sync="showDelete" width="20%" :close-on-click-modal="false" :middle="true"
				height="140px">
				<div>确定要删除该条数据吗</div>
				<div class="btn-box">
					<div class="btn theme" @click="deleteRow">确定</div>
					<div class="btn" @click="showDelete = false">取消</div>
				</div>
			</es-dialog>
		</div>
	</div>
</template>

<script>
import interfaceUrl from '@/http/alumna/member.js';
import SnowflakeId from "snowflake-id";
import memberView from '@/views/alumna/member/components/view.vue';
import memberAudit from '@/views/alumna/member/components/audit.vue';

export default {
	components: { memberView, memberAudit },
	data() {
		return {
			menus: [{ key: '0', label: '待审核' }, { key: '2', label: '审核未通过' }, { key: '1', label: '审核通过' }],
			activeMenus: '0',
			dataTableUrl: interfaceUrl.listJson,
			tableCount: 1,
			showForm: false,
			showAudit: false,
			showDelete: false,
			ownId: null,	//数据行Id
			validityOfDateDisable: false,
			params: {
				orderBy: 'createTime',
				asc: 'false',
				isAudit: 0
			},
			editBtn: {
				type: 'submit',
				skin: 'lay-form-btns',
				contents: [
					{ type: 'primary', text: '确定', event: 'confirm' }
					, { type: 'reset', text: '取消', event: 'cancel' }
				]
			},
			cancelBtn: {
				type: 'submit',
				skin: 'lay-form-btns',
				contents: [{ type: 'reset', text: '取消', event: 'cancel' }]
			},
			toolbar: [
				{
					type: 'search',
					contents: [{ type: 'text', name: 'keyword', placeholder: '关键字查询' }]
				}
			],
			optionData: {
				auditType: [
					{ value: 1, name: '人工验证' }
					, { value: 2, name: '自动审核' }
				]
			},
			thead: [],

			listTheadWait: [
				{
					title: '校友名字',
					align: 'left',
					field: 'memberName',
					showOverflowTooltip: true
				},
				{
					title: '联系方式',
					align: 'left',
					field: 'phoneNum'
				},
				{
					title: '身份',
					align: 'center',
					field: 'identityType'
				},
				{
					title: '学院',
					align: 'left',
					field: 'whereSchool'
				},
				{
					title: '部门',
					align: 'left',
					field: 'whereDept'
				},
				{
					title: '专业',
					align: 'left',
					field: 'whereMajor'
				},
			],

			// listTheadWait: [
			// 	{
			// 		title: '申请人',
			// 		align: 'left',
			// 		field: 'memberName',
			// 		showOverflowTooltip: true
			// 	},
			// 	{
			// 		title: '申请时间',
			// 		align: 'center',
			// 		field: 'createTime'
			// 	}
			// ],
			// listThead: [
			// 	{
			// 		title: '申请人',
			// 		align: 'left',
			// 		field: 'memberName',
			// 		showOverflowTooltip: true
			// 	},
			// 	{
			// 		title: '申请时间',
			// 		align: 'center',
			// 		field: 'createTime'
			// 	},
			// 	{
			// 		title: '审核时间',
			// 		align: 'center',
			// 		field: 'auditTime'
			// 	},
			// 	{
			// 		title: '验证方式',
			// 		align: 'center',
			// 		field: 'auditType',
			// 		type: 'select',
			// 		labelKey: 'name',
			// 		valueKey: 'value',
			// 		readonly: true
			// 	}
			// ],
			auditListBtn: {
				title: '操作',
				type: 'handle',
				width: 180,
				template: '',
				events: [{ code: 'view', text: '查看' }, { code: 'audit', text: '审核' }]
			},
			viewListBtn: {
				title: '操作',
				type: 'handle',
				width: 180,
				template: '',
				events: [{ code: 'view', text: '查看' }]
			},
			pageOption: {
				// layout: 'total, prev, pager, next, jumper',
				pageSize: 10,
				// hideOnSinglePage: true,
				position: 'right',
				current: 1,
				pageNum: 1
			},
			formData: {},
			formTitle: '编辑'
		};
	},
	watch: {
		dataTableUrl() {
			this.tableCount++;
		}
	},
	created() {
		this.thead = this.getListThead(this.listTheadWait, this.auditListBtn);
	},
	methods: {
		//表格行高
		tableRowClassName({ row, rowIndex }) {
			let styleRes = { "height": "54px !important" };
			return styleRes;
		},
		inputChange(key, value) { },
		//页签切换
		handleSelect(res) {
			this.activeMenus = res;
			this.thead = [];
			// if ("0" === res) {
			// 	this.params.isAudit = 0;
			// 	// this.thead = this.getListThead(this.listTheadWait, this.auditListBtn);
			// } else if ("1" === res) {
			// 	this.params.isAudit = 1;
			// 	// this.thead = this.getListThead(this.listThead, this.viewListBtn);
			// } else if ("2" === res) {
			// 	this.params.isAudit = 2;
			// 	// this.thead = this.getListThead(this.listThead, this.viewListBtn);
			// }


			if ("0" === res) {
				this.params.isAudit = 0;
				this.thead = this.getListThead(this.listTheadWait, this.auditListBtn);
			} else if ("1" === res) {
				this.params.isAudit = 1;
				this.thead = this.getListThead(this.listTheadWait, this.viewListBtn);
			} else if ("2" === res) {
				this.params.isAudit = 2;
				this.thead = this.getListThead(this.listTheadWait, this.viewListBtn);
			}


			this.tableCount++;
		},
		getListThead(org, btnJson) {
			let tempThead = Object.assign([], org);
			if (tempThead.length > 7) {
				tempThead.pop();
			}
			tempThead.push(btnJson);
			return tempThead;
		},
		//操作按钮点击事件
		btnClick(res) {
			let code = res.handle.code;
			switch (code) {
				case 'add':
					this.formTitle = '新增';
					const snowflake = new SnowflakeId();
					this.formData = { id: snowflake.generate(), status: true };
					this.showForm = true;
					break;
				case 'edit':
					this.formTitle = '编辑';
					this.ownId = res.row.id;
					this.$request({ url: interfaceUrl.info + '/' + res.row.id, method: 'GET' }).then(res => {
						if (res.rCode === 0) {
							this.formData = res.results;
							this.showForm = true;
						}
					});
					break;
				case 'view':
					this.formTitle = '查看';
					this.ownId = res.row.id;
					this.$request({ url: interfaceUrl.info + '/' + res.row.id, method: 'GET' }).then(res => {
						if (res.rCode === 0) {
							this.formData = res.results;
							this.showForm = true;
						}
					});
					break;
				case 'audit':
					this.formTitle = '审核';
					this.ownId = res.row.id;
					this.$request({ url: interfaceUrl.info + '/' + res.row.id, method: 'GET' }).then(res => {
						if (res.rCode === 0) {
							this.formData = res.results;
							this.showAudit = true;
						}
					});
					break;
				case 'delete':
					this.deleteId = res.row.id;
					this.showDelete = true;
					break;
				default:
					break;
			}
		},
		//表单提交
		handleFormSubmit(data) {
			this.$refs.form.validate(valid => {
				if (!valid) {
					return;
				}
				let formData = data;
				for (let key in formData) {
					if (key.indexOf("_") >= 0) {
						let indexName = key.replace("_", ".");
						formData[indexName] = formData[key];
					}
				}
				let url = "";
				if (this.formTitle === '新增') {
					url = interfaceUrl.save;
				} else {
					url = interfaceUrl.update;
				}

				this.$request({ url: url, data: formData, method: 'POST' }).then(res => {
					if (res.rCode !== 0) {
						this.$message.error(res.msg);
						return;
					}
					this.$message.success('操作成功');
					this.showForm = false;
					this.formData = {};
					this.$refs.table.reload();
				});
			});
		},
		//删除行
		deleteRow() {
			this.$request({ url: interfaceUrl.deleteBatchIds, data: { ids: this.deleteId }, method: 'POST' }).then(res => {
				if (res.rCode === 0) {
					this.$refs.table.reload();
					this.$message.success('删除成功');
				} else {
					this.$message.error(res.msg);
				}
				this.showDelete = false;
			});
		},
		//排序变化事件
		sortChange(column, prop, order) {
			let asc = this.params.asc;
			let orderBy = this.params.orderBy;
			if (column.order === 'ascending') {//升序
				asc = 'true';
				orderBy = column.prop;
			} else if (column.order === 'descending') {//降序
				asc = 'false';
				orderBy = column.prop;
			} else { //不排序
				asc = 'false';
				orderBy = column.prop;
			}
			this.params.asc = asc;
			this.params.orderBy = orderBy;
			this.$refs.table.reload();
		},
		refreshData() {
			this.$refs.table.reload();
		},
		confirmAudit() {
			this.showAudit = false;
			this.refreshData();
		}
	}
};
</script>

<style scoped lang="scss">
@import '../../../assets/style/style.scss';

.content {
	display: flex;
	width: 100%;
	height: 95%;

	::v-deep .es-data-table {
		width: 100%;
		// flex: 1;
		// display: flex;
		flex-direction: column;
		height: calc(100% - 188px);

		.es-data-table-content {
			// flex: 1;
			height: 0;
			// display: flex;
			flex-direction: column;

			.el-table {
				flex: 1;
				// height: 100% !important;
			}

			.es-thead-border {
				.el-table__header {
					th {
						border-right: 1px solid #E1E1E1;
					}
				}
			}
		}
	}

	::v-deep .el-form-item__label {
		background: none;
		border: 0px solid #c2c2c2;
	}
}

.el-dialog__body {
	.btn-box {
		margin-top: 20px;
		display: flex;
		justify-content: flex-end;

		.btn {
			padding: 5px 10px;
			color: #666;
			border: 1px solid #eee;
			cursor: pointer;
			margin-right: 5px;
			border-radius: 4px;
			// &.theme {
			// 	background: $--color-primary;
			// 	color: #fff;
			// 	border-color: $--color-primary;
			// }
		}
	}


}
</style>
