<template>
	<div v-if="showModal" class="card" :class="value ? 'show' : 'close'" :style="{ top: offsetTop }">
		<div class="card-top">
			<div
				v-for="(item, index) of menus"
				:key="index"
				class="card-top-item"
				@mouseenter="currentHover = index"
				@mouseleave="currentHover = ''"
				@click="handleClick(item.params)"
			>
				<img v-if="currentHover === index" class="card-top-item-img" :src="item.img_s" alt="" />
				<img v-else class="card-top-item-img" :src="item.img" alt="" />
				<div>{{ item.title }}</div>
			</div>
		</div>
		<div class="card-bottom">
			<div
				v-for="(item, index) of quickMenusRole"
				:key="index"
				class="card-bottom-item"
				:style="{ background: item.bg }"
				@click="handleClick(item.params)"
			>
				<img class="card-bottom-item-img" src="@/assets/images/layout/edit.png" alt="" />
				<div>{{ item.title }}</div>
			</div>
		</div>
	</div>
</template>

<script>
import { mapGetters } from 'vuex';

export default {
	name: 'Index',
	props: {
		value: {
			type: Boolean,
			default: () => {
				return false;
			}
		},
		offsetTop: {
			type: String,
			default: () => {
				return '50px';
			}
		}
	},
	data() {
		return {
			showModal: false,
			currentHover: '',
			menus: [
				{
					title: '基本信息',
					img: require('@/assets/images/layout/person.png'),
					img_s: require('@/assets/images/layout/person_s.png'),
					params: {
						title: '个人信息',
						componentName: 'personHome',
						path: '/personal?subMenu=2-1',
						type: 'personSelf'
					}
				},
				{
					title: '安全设置',
					img: require('@/assets/images/layout/auth.png'),
					img_s: require('@/assets/images/layout/auth_s.png'),
					params: {
						title: '个人信息',
						componentName: 'personHome',
						path: '/personal?subMenu=3-1',
						type: 'personSelf'
					}
				}
			],
			quickMenus: [
				{
					title: '职教服务大厅',
					bg: '#009944',
					params: { path: '/independentPersonal/vocational' },
					role: [2]
				},
				{
					title: '企业管理中心',
					bg: '#EC6941',
					params: { path: '/independentPersonal/enterprise' },
					role: [3, 4]
				}
			]
		};
	},
	computed: {
		...mapGetters(['roles']),
		quickMenusRole() {
			let roles = this.quickMenus.filter(item => {
				let isRole = false;
				for (let role of this.roles) {
					if (item.role.includes(role)) {
						isRole = true;
					}
				}
				return isRole;
			});
			return roles;
		}
	},
	watch: {
		value(newVal) {
			if (newVal) {
				this.showModal = true;
			} else {
				setTimeout(() => {
					this.showModal = false;
				}, 100);
			}
		}
	},
	methods: {
		/**处理事件*/
		handleClick(params) {
			this.$emit('jumpLink', params);
		}
	}
};
</script>

<style scoped lang="scss">
.card {
	background: #ffffff;
	position: absolute;
	z-index: 666;
	border: 1px solid #dcdfe6;
	border-radius: 4px;
	overflow: hidden;
	width: 152px;
	&-top {
		&-item {
			cursor: pointer;
			padding: 8px 12px 10px;
			border-bottom: 1px solid #dcdfe6;
			font-size: 14px;
			font-family: Microsoft YaHei;
			font-weight: 400;
			color: #8390a3;
			display: flex;
			align-items: center;
			&:hover {
				background: #e3f1ff;
				color: var(--brand-6, #0076e8);
			}
			&-img {
				width: 14px;
				height: 14px;
				margin-right: 8px;
			}
		}
	}
	&-bottom {
		padding: 15px 15px 10px;
		&-item {
			cursor: pointer;
			border-bottom: 1px solid #dcdfe6;
			font-size: 14px;
			font-family: Microsoft YaHei;
			font-weight: 400;
			color: #ffffff;
			padding: 8px;
			margin-bottom: 10px;
			display: flex;
			align-items: center;
			&-img {
				width: 15px;
				height: 15px;
				margin-right: 5px;
			}
		}
	}
}
@keyframes openShow {
	0% {
		height: 0;
	}
	99% {
		height: 74px;
	}
	100% {
		height: auto;
	}
}
@keyframes closeHide {
	0% {
		height: auto;
	}
	1% {
		height: 74px;
	}
	100% {
		height: 0;
	}
}
.show {
	animation: openShow 0.1s;
	animation-fill-mode: forwards;
}
.close {
	animation: closeHide 0.1s;
	animation-fill-mode: forwards;
}
</style>
