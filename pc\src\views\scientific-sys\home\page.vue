<template>
	<div class="page">
		<div class="page-title">科研信息管理系统</div>
		<div
			class="page-content"
			:style="{
				transform: `scale(${scale})`,
				width: '1920px',
				'transform-origin': 'top left'
			}"
		>
			<el-carousel
				arrow="always"
				:autoplay="false"
				trigger="click"
				class="swiper-container"
				height="600px"
			>
				<el-carousel-item v-for="(item, k) of items" :key="k" class="swiper-slide">
					<div v-for="(card, i) of item" :key="i" class="card-box" @click="onJump(card.url)">
						<img v-if="card.img" :src="card.img" alt="" class="img" />
						<span class="label">{{ card.text }}</span>
					</div>
				</el-carousel-item>
			</el-carousel>
		</div>
	</div>
</template>

<script>
export default {
	data() {
		return {
			items: [
				[
					{
						text: '项目管理',
						url: 'verticalProject',
						img: require('@/assets/images/scientific-sys/成果管理.webp')
					},
					{
						text: '平台管理',
						url: 'platformTeam',
						img: require('@/assets/images/scientific-sys/成果库.webp')
					},
					{
						text: '成果管理',
						url: 'academicPaper',
						img: require('@/assets/images/scientific-sys/成果申报.webp')
					},
					{
						text: '统计分析',
						// url: 'scientific-sys-home',
						url: 'subsystem?redirect_uri=%2Fwebroot%2Fdecision%2Fview%2Freport%3Fviewlet%3D%2525E7%2525A7%252591%2525E6%25258A%252580%2525E5%2525A4%252584%2525E6%25258A%2525A5%2525E8%2525A1%2525A8-%2525E6%252594%2525B9.cpt%26ref_t%3Ddesign%26op%3Dwrite%26ref_c%3D05cac037-d714-4959-8f73-704b5277681b',
						img: require('@/assets/images/scientific-sys/项目变更.webp')
					},
					{
						text: '人员管理',
						url: 'teacherPim',
						img: require('@/assets/images/scientific-sys/项目申报.webp')
					}
				]
				// [
				// 	{
				// 		text: '成果管理',
				// 		url: '',
				// 		img: require('@/assets/images/scientific-sys/成果管理.webp')
				// 	},
				// 	{
				// 		text: '成果库',
				// 		url: '',
				// 		img: require('@/assets/images/scientific-sys/成果库.webp')
				// 	},
				// 	{
				// 		text: '成果申报',
				// 		url: 'academicPaper',
				// 		img: require('@/assets/images/scientific-sys/成果申报.webp')
				// 	},
				// 	{
				// 		text: '项目变更',
				// 		url: 'projectChange',
				// 		img: require('@/assets/images/scientific-sys/项目变更.webp')
				// 	},
				// 	{
				// 		text: '项目申报',
				// 		url: 'crosswiseProject',
				// 		img: require('@/assets/images/scientific-sys/项目申报.webp')
				// 	}
				// ],
				// [
				// 	{
				// 		text: '项目库',
				// 		url: '',
				// 		img: require('@/assets/images/scientific-sys/项目库.webp')
				// 	},
				// 	{
				// 		text: '项目立项',
				// 		url: 'approvalList',
				// 		img: require('@/assets/images/scientific-sys/项目立项.webp')
				// 	},
				// 	{
				// 		text: '项目结题',
				// 		url: 'finalAcceptance',
				// 		img: require('@/assets/images/scientific-sys/项目结题.webp')
				// 	}
				// ]
			]
		};
	},
	computed: {
		scale() {
			return this.$utils.toolViewRatio();
		}
	},
	mounted() {},
	methods: {
		onJump(url) {
			if (url) {
				this.$router.push({
					path: url
				});
			} else {
				this.$message('该功能暂未开放！');
			}
		}
	}
};
</script>

<style lang="scss" scoped>
.page {
	height: 100%;
	width: 100%;
	background: url(~@/assets/images/scientific-sys/bg.webp) no-repeat center;
	// background: url(~@/assets/images/scientific-sys/bg-lg.webp) no-repeat center !important;

	background-size: 100% 100%;
	position: relative;
	.page-title {
		position: absolute;
		top: 12px;
		left: 50%;
		transform: translate(-50%, 0);
		font-family: AlibabaPuHuiTi_3_105_Heavy;
		font-size: 40px;
		font-weight: bold;
		background-image: linear-gradient(
			180deg,
			rgba(255, 255, 255, 1) 20%,
			rgba(74, 182, 253, 1) 100%
		);
		// 字体间距
		letter-spacing: 0.15em;

		background-clip: text;
		-webkit-background-clip: text;
		-webkit-text-fill-color: transparent;
	}

	::v-deep .page-content {
		position: absolute;
		top: calc(50% - 276px);
		width: calc(100% - 10px);
		.swiper-container {
			width: 100%;
			height: 100%;

			.swiper-slide {
				display: flex;
				align-items: center;
				width: 86%;
				margin-left: 9%;
				cursor: pointer;
				.card-box {
					position: relative;
					background-size: 100% 100%;
					background-image: url(~@/assets/images/scientific-sys/bg-md.webp);
					min-width: 288px;
					height: 359px;
					margin: 0 15px;
					transition: all 0.3s ease-in-out;
					&:nth-child(3) {
						min-width: 312px;
						height: 389px;
					}
					&:nth-child(1),
					&:nth-child(5) {
						background-image: url(~@/assets/images/scientific-sys/bg-sm.webp);
						min-width: 268px;
						height: 334px;
					}
					display: flex;
					flex-direction: column;
					justify-content: center;
					align-items: center;
					.img {
						width: 335px;
						height: 318px;
						position: absolute;
						bottom: 16%;
					}
					.label {
						font-family: AlimamaShuHeiTi, AlimamaShuHeiTi;
						font-weight: bold;
						font-size: 36px;
						color: #ffffff;
						line-height: 1;
						text-align: center;
						font-style: normal;
						position: absolute;
						bottom: 21%;
					}
					&:hover {
						background-image: url(~@/assets/images/scientific-sys/bg-lg.webp);
						min-width: 312px;
						height: 389px;
						&::after {
							content: '';
							position: absolute;
							top: -108px;
							left: calc(50% - 45px);
							background-image: url(~@/assets/images/scientific-sys/current.webp);
							background-size: 100% 100%;
							width: 91px;
							height: 77px;
						}
						.img {
							animation: slide-top 1.5s cubic-bezier(0.25, 0.2, 0.2, 0.44) infinite alternate;
						}
					}
					@keyframes slide-top {
						0% {
							-webkit-transform: translateY(0);
							transform: translateY(0);
						}
						100% {
							-webkit-transform: translateY(-20px);
							transform: translateY(-20px);
						}
					}
				}
			}
			.el-carousel__arrow--right,
			.el-carousel__arrow--left {
				background: transparent;
			}
			.el-icon-arrow-right,
			.el-icon-arrow-left {
				background: transparent;
				color: transparent;
				background-size: 100% 100%;
				width: 60px;
				height: 72.26px;
				position: relative;
				left: 80px;
				background-image: url(~@/assets/images/scientific-sys/arrow.webp);
			}
			.el-icon-arrow-right {
				right: 100px;
				left: auto;
				transform: rotate(180deg);
			}
			.el-carousel__button {
				width: 14px;
				height: 14px;
				background: #ffffff;
				border-radius: 50%;
				margin: 0 4px;
				opacity: 0.8;
			}
			.is-active {
				.el-carousel__button {
					background: #0da6f1;
				}
			}
		}
	}
}
</style>
