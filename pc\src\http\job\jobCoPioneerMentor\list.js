import httpApi from '@/http/job/jobCoPioneerMentor/api.js';
export default {
	computed: {
		component() {
			return {
				// 纯列表结构
				name: 'es-data-table'
			};
		},
		treeParam() {
			return {
				
			};
		},
		table() {
			return {
				url: httpApi.jobCoPioneerMentorList,
				param: { handlerType: '' },
				full: true,
				fit: true,
				page: true,
				stripe: true,
				numbers: true,
				'row-key': 'id',
				toolbar: [
					{
						type: 'search',
						reset: true,
						contents: [
							//{
							//	name: 'accountName_like',
							//	placeholder: '配置名称',
							//	label: '配置名称',
							//	col: 6
							//},
							{
								type: 'date',
								col: 6,
								name: 'updateTime_begin',
								label: '更新开始时间',
								placeholder: '更新开始时间'
							},
							{
								type: 'date',
								col: 6,
								name: 'updateTime_end',
								label: '更新结束时间',
								placeholder: '更新结束时间'
							}
						]
					}
				],
				thead: [
					{
						title: '企业',
						align: 'left',
						field: 'enterpriseName'
					},
					{
						title: '姓名',
						align: 'left',
						field: 'name'
					},
					{
						title: '公司',
						align: 'left',
						field: 'company'
					},
					{
						title: '职务',
						align: 'left',
						field: 'mentorJob'
					},
					
					{
						title: '状态',
						align: 'left',
						field: 'statusCn'
					},
					{
						title: '操作',
						type: 'handle',
						width: 180,
						events: [
							{
								code: 'view',
								text: '查看'
							},
							{
							    code: 'audit',
							    text: '审核'
							},
							{
								code: 'edit',
								text: '编辑'
							}
							/*,
							{
								text: '删除'
							}*/
						]
					}
				]
			};
		}
	}
};
