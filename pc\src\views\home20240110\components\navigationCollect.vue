<template>
	<div v-loading="loading" class="system-list" element-loading-background="rgba(0, 0, 0, 0.8)">
		<template v-for="(item, index) in collectedData">
			<div
				:key="index"
				class="item"
				@click="item.resourceType ? myAppDataBtn(item) : toOpenWindow(item)"
			>
				<img :src="item.resourceType ? item.logo : handelPicUrl(item.icons)" class="item-img" />
				<span class="name">{{ item.resourceType ? item.name : item.text }}</span>
			</div>
		</template>
		<Empty v-if="collectedData.length < 1" class="empty-none" text="暂无收藏" />

		<es-dialog ref="visible" title="业务直通车" size="max" :visible.sync="visible">
			<iframe
				ref="iframe"
				src=""
				width="100%"
				height="100%"
				frameborder="0"
				allowfullscreen
				allow-same-origin
			>
				<div>浏览器不兼容</div>
			</iframe>
		</es-dialog>
	</div>
</template>

<script>
import Empty from './empty.vue';
import { tenantId, alumniUrl } from '@/config';

import { getTransactionList, checkLogin, getEossAuthentication } from '@/api/home.js';
export default {
	name: 'NavigationCollect',
	components: {
		Empty
	},
	data() {
		return {
			loading: false,
			myAppData: [], //系统直通车展示的数据
			serviceData: [], // 业务直通车
			collectedList: [], // 收藏的
			// collectedData: [],
			interfaceCount: 0,
			visible: false
		};
	},
	computed: {
		collectedData() {
			const arr = [...this.myAppData, ...this.serviceData].filter(item => {
				if (this.collectedList.includes(item.id)) {
					return true;
				} else {
					return false;
				}
			});
			return arr;
		}
	},
	watch: {
		interfaceCount: {
			handler(val) {
				// 请求成功接口数量,三个接口成功才关闭loading
				if (val === 3) {
					this.loading = false;
				}
			}
		}
	},
	async created() {
		this.loading = true;
		// this.getList();
		// this.getListData();
		// this.getCollected();
		await Promise.all[(this.getList(), this.getListData(), this.getCollected())];
	},
	methods: {
		//处理图片路径
		handelPicUrl(url) {
			return `${alumniUrl}/static/andyui2.0/css/picIcon/img/${url}`;
		},
		// 系统直通车列表数据
		getList() {
			let data = {
				pageSize: 1000
			};
			this.$.ajax({
				url: '/ybzy/platuser/front/appList',
				method: 'post',
				data: data
			}).then(res => {
				if (res.rCode === 0) {
					this.interfaceCount++;
					this.myAppData = res.results || [];
				} else {
					this.$message.error(res.msg);
				}
			});
		},
		//业务直通车列表接口
		getListData() {
			let userId = localStorage.getItem('mobileUserId') || '';
			/**原有代码基础加上判断，没有授权的话请求授权*/
			if (!userId) {
				// checkLogin()
				this.$.ajax({
					url: checkLogin
				}).then(res => {
					let code = res.results.code;
					localStorage.setItem('ssoCode', res.results.code);
					let data = {
						serverId: 'ybzyDtcSso',
						authType: '6',
						code
					};
					if (!(code === null)) {
						this.$.ajax({
							url: getEossAuthentication,
							method: 'POST',
							data: data
						})
							.then(res => {
								localStorage.setItem('mobileToken', res.results.mobileToken);
								localStorage.setItem('mobileUserId', res.results.userId);
								this.getListData();
							})
							.catch(err => {
								console.log(err, '认证失败err');
								return '认证失败';
							});
					}
				});
			} else {
				this.$.ajax({
					url: getTransactionList + `?menuCode=cygn&userId=${userId}&type=2`
				})
					.then(res => {
						if (res.rCode === 0) {
							this.interfaceCount++;
							let arr = [];
							res.results.forEach(i => {
								i.children.forEach(item => {
									arr.push(item);
								});
							});
							this.serviceData = arr;
						}
					})
					.catch(error => {
						console.log(error, 'error');
					});
			}
		},
		/**
		 * @description 收藏--查询
		 * */
		getCollected() {
			this.$.ajax({
				url: '/ybzy/specificHomepageShortcut/getList',
				params: {
					pageSize: 1000,
					type: 0 // 0, "收藏"  1, "最近访问" 2, "公告已读"
				}
			}).then(res => {
				if (res.rCode == 0) {
					let list = res?.results?.records || [];
					this.collectedList = list.reduce((acc, cur) => {
						acc.push(cur.sourceId);
						return acc;
					}, []);
				}
			}).finally(()=>{
				this.loading = false;
			})
		},
		// 应用跳转对应链接判断
		myAppDataBtn(item) {
			this.saveRecent(item, 0);
			if (item.url.includes('redirectUri=/project-ybzy/ybzy/index.html')) {
				window.location.href = item.url + '&isTeacher=true';
			} else {
				window.open(item.url);
			}
		},
		// 业务直通车跳转判断
		toOpenWindow(item) {
			if (item.id == '11111111') {
				return;
			}
			// window.open(`${picUrl}${item.newDataUrl}${item.code}`);
			// window.open(`${alumniUrl}${item.url}`);
			this.visible = true;
			this.$nextTick(() => {
				this.$refs.iframe.src = `${alumniUrl}${item.url}`;
			});
			this.saveRecent(item, 1);
		},
		/**
		 * @description 最近访问--创建
		 * */
		saveRecent(item, type) {
			let data = {
				sourceId: item.id, //目标Id
				type: 1, // 0, "收藏"  1, "最近访问" 2, "公告已读"
				sourceType: type //目标类型（0：系统，1：菜单）
			};
			this.$.ajax({
				url: '/ybzy/specificHomepageShortcut/save',
				method: 'POST',
				data: data
			}).then(res => {
				if (res.rCode == 0) {
					// 回调函数执行
					this.$emit("callBack")
				}
			});
		},
	}
};
</script>

<style lang="scss" scoped>
.system-list {
	width: 100%;
	display: flex;
	flex-wrap: wrap;
	padding: 0 25px;
	max-height: calc(90vh - 119px);
	overflow: auto;
	&::-webkit-scrollbar {
		width: 0px;
		height: 0px;
	}
	.item {
		width: 94px;
		height: 128px;
		background: rgba(210, 225, 240, 0.2);
		border-radius: 8px;
		margin-right: 20px;
		display: flex;
		flex-direction: column;
		align-items: center;
		margin-bottom: 20px;
		cursor: pointer;
		position: relative;
		padding-top: 15px;
		&:nth-child(5n) {
			margin-right: 0;
		}
		&:hover {
			box-shadow: 0px 0px 8px 0px rgba(65, 160, 255, 0.4);
			border: 1px solid rgba(73, 165, 255, 0.5);
		}
		.item-img {
			width: 58px;
			height: 58px;
			border-radius: 8px;
			object-fit: contain;
			// 填充
			object-fit: cover;
		}
		.name {
			margin-top: 5px;
			width: 70px;
			height: 40px;
			font-size: 14px;
			font-family: MicrosoftYaHei;
			color: #ffffff;
			line-height: 20px;
			text-align: center;
			overflow: hidden;
			text-overflow: ellipsis;
			display: -webkit-box;
			-webkit-line-clamp: 2;
			-webkit-box-orient: vertical;
		}
	}
}
.empty-none {
	margin-top: 219px;
}
</style>
