<template>
	<!-- 在list.js修改component.name值可改变展示组件 -->
	<component
		:is="component.name"
		ref="dataTable"
		v-bind="table"
		:table="table"
		:tree="treeParam"
		@selection-change="handleSelectionChange"
		@btnClick="btnClick"
		@node-click="treeSelectChange"
		@success="successAfter"
		@search="hadeSearch"
		@reset="hadeReset"
	>
		<es-dialog
			v-if="showForm"
			:title="formTitle"
			:visible.sync="showForm"
			:close-on-click-modal="false"
			:middle="true"
			height="750px"
			class="dialog-form"
		>
			<es-form
				ref="form"
				:model="formData"
				:contents="formItemList"
				:genre="2"
				height="600px"
				collapse
				@change="handleFormItemChange"
				@submit="handleFormSubmit"
				@reset="showForm = false"
			/>
		</es-dialog>
		<es-dialog
			v-if="showView"
			title="详情"
			:visible.sync="showView"
			:close-on-click-modal="false"
			:middle="true"
			height="600px"
			class="dialog-form"
		>
			<es-form
				ref="form"
				:model="viewData"
				:contents="viewItemList"
				:genre="2"
				readonly
				height="500px"
				collapse
				@reset="showView = false"
			/>
		</es-dialog>
	</component>
</template>

<script>
import httpApi from '@/http/lab/labRoomInspect/api.js';
import list from '@/http/lab/labRoomInspect/list.js';
import edit from '@/http/lab/labRoomInspect/edit.js';
import view from '@/http/lab/labRoomInspect/view.js';

export default {
	name: 'LabRoomInspect',
	mixins: [list, edit, view],
	data() {
		return {
			selectRowData: [],
			selectRowIds: [],
			selectTreeData: {},
			formTitle: '',
			showForm: false,
			showView: false,
			formData: {},
			viewData: {},
			extraData: {},
			submitFilterParams: {}
		};
	},
	watch: {
		showForm(val) {
			if (!val) {
				this.formData = {};
			}
		}
	},
	methods: {
		// 初始化扩展数据，字典等
		successAfter(data) {
			this.extraData = data.results?.extraData || {};
		},
		// 数据列表多选回调
		handleSelectionChange(data) {
			let ids = [];
			data.forEach(row => {
				ids.push(row.id);
			});
			this.selectRowData = data;
			this.selectRowIds = ids;
		},
		// tree 点击
		treeSelectChange(res, data) {
			//this.table.param.handlerType = data.id;
			this.selectTreeData = data;
			this.$refs.dataTable.reload();
		},
		// 表单变更时, 回调处理
		handleFormItemChange(filed, data) {
			// 处理表单字段systemCode的变更
			// if (filed == 'systemCode') {
			//	this.formData.systemCode = data.id;
			//	this.formData.systemName = data.name;
			// }
		},
		btnClick(res) {
			let text = res.handle.text;
			switch (text) {
				case '查看':
					this.$request({
						url: httpApi.labRoomInspectInfo,
						params: { id: res.row.id }
					}).then(res => {
						// 处理自定义的数据格式化, extFiled 额外属性名， key、val 数据映射
						// this.viewData = this.formatExtraData(res.results[*], 'extFiled', 'key', 'val');
						this.viewData = res.results;
						// 打开查看弹窗
						this.showView = true;
					});
					break;
				case '新增':
					// 判断是否点击左边树
					// if (this.selectTreeData.id == undefined) {
					// 	this.$message.warning('请选择平台类型');
					// 	return;
					// }
					this.formTitle = '新增';
					this.showForm = true;
					// 初始化表单数据
					this.formData = {
						// useStatus: 1,
						checkStatus: '0',
						id: this.$uuidv4()
					};
					break;
				case '编辑':
					this.formTitle = '编辑';
					this.$request({
						url: httpApi.labRoomInspectInfo,
						params: { id: res.row.id }
					}).then(res => {
						// 处理自定义的数据格式化, extFiled 额外属性名， key、val 数据映射
						// this.formData = this.formatExtraData(res.results[*], 'extFiled', 'key', 'val');
						this.formData = Object.assign({}, res.results, {
							isNormal: res.results.isNormal + ''
						});
						this.showForm = true;
					});
					break;
				case '删除':
					this.deleteId(res.row.id);
					break;
				case '导出':
					this.exportFile();
					break;
				default:
					break;
			}
		},
		//导出函数
		exportFile() {
			let isDev = process.env.NODE_ENV === 'development' ? '/api' : '';
			const paramAll = { ...this.submitFilterParams };
			const paramStr = this.objToUrlParams(paramAll);
			let url = `${isDev}/ybzy/labRoomInspect/exportInspectData${paramStr ? '?' + paramStr : ''}`;
			window.open(url);
		},
		objToUrlParams(obj) {
			return Object.keys(obj)
				.filter(key => obj[key] !== '' && obj[key] !== null)
				.map(key => encodeURIComponent(key) + '=' + encodeURIComponent(obj[key]))
				.join('&');
		},
		hadeSearch(e) {
			this.submitFilterParams = e;
		},
		hadeReset() {
			this.submitFilterParams = {};
		},
		handleFormSubmit() {
			let formData = { ...this.formData };
			// 可额外处理formData中的数据

			this.$request({
				url: this.formTitle == '新增' ? httpApi.labRoomInspectSave : httpApi.labRoomInspectUpdate,
				data: formData,
				method: 'POST'
			}).then(res => {
				if (res.rCode == 0) {
					this.$message.success('操作成功');
					this.showForm = false;
					this.formData = {};
					this.$refs.dataTable.reload();
				} else {
					this.$message.error(res.msg);
				}
			});
		},
		deleteId(id) {
			this.$confirm('确定要删除选中的数据', '删除', {
				confirmButtonText: '确定',
				cancelButtonText: '取消',
				type: 'warning'
			})
				.then(() => {
					this.$request({
						url: httpApi.labRoomInspectDeleteById,
						data: { id: id },
						method: 'POST'
					}).then(res => {
						if (res.rCode == 0) {
							this.$message.success('操作完成');
							this.$refs.dataTable.reload();
						} else {
							this.$message.error(res.msg);
						}
					});
				})
				.catch(() => {});
		},
		// 根据列表额外数据返回的 字典信息，格式化数据
		formatExtraData(data, extFiled, key, val) {
			let text = '未知';
			this.extraData[extFiled].forEach(dict => {
				if (dict[key] === data) {
					text = dict[val];
				}
			});
			return text;
		}
	}
};
</script>
