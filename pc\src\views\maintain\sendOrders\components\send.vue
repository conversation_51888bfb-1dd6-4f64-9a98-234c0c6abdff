<template>
	<div v-loading="loading">
		<es-form
			ref="form"
			:model="formData"
			:contents="formItemList"
			:genre="2"
			label-width="140px"
			height="620px"
			collapse
			@change="repairUserTypeChange"
			@submit="handleFormSubmit"
			@reset="cancel"
		/>
	</div>
</template>
<script>
import api from '@/http/maintain/applicationApi';
import SnowflakeId from 'snowflake-id';

export default {
	props: {
		id: {
			type: String
		},
		workType: {
			type: String
		}
	},
	data() {
		return {
			loading: false,
			ownId: null,
			repairUser: {},
			formData: {},
			//所有维修员
			allWorkers: [],
			//内部维修员
			inWorkers: [],
			//外部维修员
			outWorkers: [],
			workers: []
		};
	},
	computed: {
		formItemList() {
			return [
				{
					name: 'repairCode',
					label: '维修单编号',
					placeholder: '维修单编号',
					readonly: true,
					col: 6
				},
				{
					name: 'repairTypeName',
					label: '报修类别',
					readonly: true,
					col: 6
				},
				{
					label: '报修人',
					name: 'reportUser',
					readonly: true,
					col: 6
				},
				{
					label: '报修电话',
					name: 'repairPhone',
					readonly: true,
					col: 6
				},
				{
					name: 'createTime',
					label: '报修时间',
					readonly: true,
					col: 6
				},
				{
					type: 'datetime',
					name: 'expectDate',
					label: '期待维修时间',
					readonly: true,
					col: 6
				},
				{
					name: 'addressTypeName',
					label: '报修地点',
					readonly: true,
					col: 6
				},
				{
					label: '详细地址',
					name: 'address',
					readonly: true,
					col: 12
				},
				{
					label: '报修描述',
					type: 'textarea',
					name: 'reportContent',
					readonly: true,
					rows: 5
				},
				{
					label: '报修图片',
					name: 'reportImage',
					type: 'attachment',
					readonly: true,
					code: 'hq_report_image',
					'select-type': 'icon-plus',
					preview: true,
					listType: 'picture-card',
					ownId: this.id // 业务id
				},
				{
					type: 'datetime',
					name: 'esCompletionDate',
					label: '预计维修完成时间',
					placeholder: '预计维修完成时间',
					rules: {
						required: true,
						message: '请选择预计维修完成时间',
						trigger: 'blur'
					},
					col: 6
				},
				{
					label: '紧急程度',
					type: 'radio',
					name: 'urgency',
					placeholder: '请选择紧急程度',
					sysCode: 'hq_urgency',
					'label-key': 'shortName',
					'value-key': 'cciValue',
					rules: {
						required: true,
						message: '请选择紧急程度',
						trigger: 'change'
					},
					clearable: true,
					col: 6
				},
				{
					label: '紧急原因',
					name: 'urgencyReason',
					type: 'textarea',
					maxlength: 200,
					rows: 5
				},
				{
					label: '维修员类型',
					type: 'radio',
					name: 'repairUserType',
					placeholder: '请选择维修员类型',
					data: [
						{ label: '内部维修员', value: '0' },
						{ label: '外部维修员', value: '1' }
					],
					'label-key': 'label',
					'value-key': 'value',
					rules: {
						required: true,
						message: '请选择维修员类型',
						trigger: 'change'
					},
					clearable: true,
					col: 6
				},
				{
					label: '维修员',
					type: 'select',
					name: 'repairUser',
					data: this.workers,
					labelKey: 'name',
					valueKey: 'id',
					rules: {
						required: true,
						message: '请选择维修人',
						trigger: 'change'
					},
					clearable: true,
					col: 6
				},
				{
					type: 'submit',
					skin: 'lay-form-btns',
					contents: [
						{ type: 'primary', text: '确定', event: this.handleFormSubmit },
						{ type: 'reset', text: '取消', event: this.cancel }
					]
				}
			];
		}
	},
	watch: {},
	created() {
		this.getInfo();
		this.getWorkers();
	},
	methods: {
		//获取维修工人列表
		getWorkers() {
			this.$request({
				url: api.selectMpList,
				params: { workType: this.workType },
				method: 'GET'
			}).then(res => {
				if (res.rCode == 0) {
					this.allWorkers = res.results;
					this.allWorkers.forEach(obj => {
						if (obj.type == 0) {
							this.inWorkers.push(obj);
						}
						if (obj.type == 1) {
							this.outWorkers.push(obj);
						}
					});
					this.workers = this.inWorkers;
				} else {
					this.$message.error(res.msg);
				}
			});
		},
		getInfo() {
			this.loading = true;
			this.$request({ url: api.getApplicationInfo + '/' + this.id, method: 'GET' })
				.then(res => {
					this.loading = false;
					if (res.rCode === 0) {
						this.formData = res.results;
						//初始化派给内部维修员
						this.formData.repairUserType = '0';
						this.ownId = this.id;
					} else {
						this.$message.error(res.msg);
					}
				})
				.catch(e => {
					this.loading = false;
				});
		},
		repairUserTypeChange(key, value) {
			if (key === 'repairUserType') {
				if (value == '0') {
					this.workers = this.inWorkers;
					this.formData.repairUser = '';
				} else {
					this.workers = this.outWorkers;
					this.formData.repairUser = '';
				}
			}
		},
		handleFormSubmit() {
			//校验
			this.$refs.form.validate(valid => {
				if (valid) {
					let apiUrl = api.savaRepairDispatch;
					const snowflake = new SnowflakeId();
					let saveData = {
						id: snowflake.generate(),
						reportId: this.id,
						dispatchType: '1',
						repairUser: this.formData.repairUser,
						esCompletionDate: this.formData.esCompletionDate,
						urgency: this.formData.urgency,
						urgencyReason: this.formData.urgencyReason
					};

					this.$request({
						url: apiUrl,
						data: saveData,
						method: 'POST'
					}).then(res => {
						if (res.rCode === 0) {
							this.$message.success('操作成功');
							this.$emit('closeSendOrderPage', 'success');
						} else {
							this.$message.error(res.msg);
						}
					});
				}
			});
		},
		cancel() {
			this.$emit('closeSendOrderPage', 'cancel');
		}
	}
};
</script>
<style lang="scss" scoped></style>
