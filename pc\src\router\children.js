/*
	//路由配置
	列：
	{
		path: '/convoke',
		title: 'xxxx',
		name: 'convoke',
		component: resolve => require(['@/views/xxxx/Convoke.vue'], resolve)
	}
*/

export default [
	{
		path: '/modules',
		title: '动态模块',
		name: 'primary',
		component: resolve => require(['@/views/public/Modules.vue'], resolve)
	},
	{
		path: '/process',
		title: '业务流程',
		name: 'process',
		component: resolve => require(['@/views/public/Process.vue'], resolve)
	},
	// 业务中台
	{
		path: '/platApplication',
		title: '应用注册',
		name: 'platApplication',
		component: resolve => require(['@/views/platform/application/index.vue'], resolve)
	},
	// 业务中台
	{
		path: '/dataShareAnalysis',
		title: '数据共享服务分析统计',
		name: 'dataShareAnalysis',
		component: resolve => require(['@/views/platform/dataShareAnalysis/index.vue'], resolve)
	},
	{
		path: '/platbusinesscertstuffconfig',
		title: '资质认证配置',
		name: 'platbusinesscertstuffconfig',
		component: resolve => require(['@/views/platform/certstuffconfig/index.vue'], resolve)
	},
	{
		path: '/platbusinesstype',
		title: '企业类型管理',
		name: 'platbusinesstype',
		component: resolve => require(['@/views/platform/businesstype/index.vue'], resolve)
	},
	{
		path: '/cmsmodel',
		title: '模型管理',
		name: 'cmsmodel',
		component: resolve => require(['@/views/platform/cms/model.vue'], resolve)
	},
	{
		path: '/cmsmodelfield',
		title: '模型字段管理',
		name: 'cmsmodelfield',
		component: resolve => require(['@/views/platform/cms/modelfield.vue'], resolve)
	},
	{
		path: '/cmsnode',
		title: '栏目管理',
		name: 'cmsnode',
		component: resolve => require(['@/views/platform/cms/node.vue'], resolve)
	},
	{
		path: '/cmsinfo',
		title: '内容管理',
		name: 'cmsinfo',
		component: resolve => require(['@/views/platform/cms/info.vue'], resolve)
	},
	{
		path: '/cmsapplication',
		title: '外部应用管理',
		name: 'cmsapplication',
		component: resolve => require(['@/views/platform/cms/cmsapplication.vue'], resolve)
	},
	{
		path: '/platperson',
		title: '人员认证',
		name: 'platperson',
		component: resolve => require(['@/views/platform/person/index.vue'], resolve)
	},
	{
		path: '/platenterprise',
		title: '企业认证',
		name: 'platenterprise',
		component: resolve => require(['@/views/platform/enterprise/index.vue'], resolve)
	},
	{
		path: '/platcertapply',
		title: '资质认证',
		name: 'platcertapply',
		component: resolve => require(['@/views/platform/certapply/index.vue'], resolve)
	},
	{
		path: '/platuser',
		title: '用户管理',
		name: 'platuser',
		component: resolve => require(['@/views/platform/user/index.vue'], resolve)
	},
	{
		path: '/platrole',
		title: '角色管理',
		name: 'platrole',
		component: resolve => require(['@/views/platform/role/index.vue'], resolve)
	},
	{
		path: '/platfrontapi',
		title: 'API管理',
		name: 'platfrontapi',
		component: resolve => require(['@/views/platform/frontapi/index.vue'], resolve)
	},
	{
		path: '/studentData',
		title: '学生信息',
		name: 'studentData',
		component: resolve => require(['@/views/platform/studentData/index.vue'], resolve)
	},

	{
		path: '/CertificateRequest',
		title: '证书申请',
		name: 'studentData',
		component: resolve => require(['@/views/platform/certificate/CertificateRequest.vue'], resolve)
	},
	{
		path: '/CertificateIssue',
		title: '证书核发',
		name: 'studentData',
		component: resolve => require(['@/views/platform/certificate/CertificateIssue.vue'], resolve)
	},
	{
		path: '/CertificateReview',
		title: '证书审核',
		name: 'studentData',
		component: resolve => require(['@/views/platform/certificate/CertificateReview.vue'], resolve)
	},
	{
		path: '/CertificateUpdate',
		title: '证书更新',
		name: 'studentData',
		component: resolve => require(['@/views/platform/certificate/CertificateUpdate.vue'], resolve)
	},
	{
		path: '/CertificateRevocation',
		title: '证书吊销',
		name: 'studentData',
		component: resolve =>
			require(['@/views/platform/certificate/CertificateRevocation.vue'], resolve)
	},

	/* 就业创业 */
	{
		path: '/jobHome',
		title: '就业创业首页',
		name: 'jobHome',
		component: resolve => require(['@/views/job/jobHome/index.vue'], resolve)
	},
	{
		path: '/jobCoPioneerMentor',
		title: '创业导师',
		name: 'jobCoPioneerMentor',
		component: resolve => require(['@/views/job/jobCoPioneerMentor/index.vue'], resolve)
	},
	{
		path: '/jobCoExample',
		title: '成功案例',
		name: 'jobCoExample',
		component: resolve => require(['@/views/job/jobCoExample/index.vue'], resolve)
	},
	{
		path: '/jobCoFinancing',
		title: '资金管理',
		name: 'jobCoFinancing',
		component: resolve => require(['@/views/job/jobCoFinancing/index.vue'], resolve)
	},
	{
		path: '/jobCoPost',
		title: '岗位管理',
		name: 'jobCoPost',
		component: resolve => require(['@/views/job/jobCoPost/index.vue'], resolve)
	},
	{
		path: '/jobCoPostAudit',
		title: '企业招聘审核',
		name: 'jobCoPostAudit',
		component: resolve => require(['@/views/job/jobCoPostAudit/index.vue'], resolve)
	},
	{
		path: '/jobCoPostResume',
		title: '投递信息管理',
		name: 'jobCoPostResume',
		component: resolve => require(['@/views/job/jobCoPostResume/index.vue'], resolve)
	},
	{
		path: '/jobCoInterview',
		title: '面邀信息管理',
		name: 'jobCoInterview',
		component: resolve => require(['@/views/job/jobCoInterview/index.vue'], resolve)
	},
	{
		path: '/jobStudentProject',
		title: '创业项目',
		name: 'jobStudentProject',
		component: resolve => require(['@/views/job/jobStudentProject/index.vue'], resolve)
	},
	{
		path: '/jobCoFinancialServe',
		title: '金融服务',
		name: 'jobCoFinancialServe',
		component: resolve => require(['@/views/job/jobCoFinancialServe/index.vue'], resolve)
	},
	{
		path: '/jobComment',
		title: '评论管理',
		name: 'jobComment',
		component: resolve => require(['@/views/job/jobComment/index.vue'], resolve)
	},
	{
		path: '/jobCoFinance',
		title: '金融机构',
		name: 'jobCoFinance',
		component: resolve => require(['@/views/job/jobCoFinance/index.vue'], resolve)
	},
	{
		path: '/jobStudentResume',
		title: '简历管理',
		name: 'jobStudentResume',
		component: resolve => require(['@/views/job/jobStudentResume/index.vue'], resolve)
	},
	{
		path: '/jobCoPractice',
		title: '实习网点管理',
		name: 'jobCoPractice',
		component: resolve => require(['@/views/job/jobCoPractice/index.vue'], resolve)
	},
	{
		path: '/jobServiceType',
		title: '就业创业服务类型',
		name: 'jobServiceType',
		component: resolve => require(['@/views/job/jobServiceType/index.vue'], resolve)
	},
	{
		path: '/jobService',
		title: '就业创业服务管理',
		name: 'jobService',
		component: resolve => require(['@/views/job/jobService/index.vue'], resolve)
	},
	{
		path: '/EnterpriseDatabase',
		title: '企业库',
		name: 'EnterpriseDatabase',
		component: resolve => require(['@/views/job/EnterpriseDatabase/index.vue'], resolve)
	},

	{
		path: '/jobMutualSelection',
		title: '双选会邀请函',
		name: 'jobMutualSelection',
		component: resolve => require(['@/views/job/jobMutualSelection/index.vue'], resolve)
	},
	{
		path: '/jobReply',
		title: '企业回复审核',
		name: 'jobReply',
		component: resolve => require(['@/views/job/jobReply/index.vue'], resolve)
	},
	{
		path: '/jobDataStat',
		title: '数据统计',
		name: 'jobDataStat',
		component: resolve => require(['@/views/job/jobDataStat/index.vue'], resolve)
	},
	// 社团管理
	{
		path: '/societyHome',
		title: '社团首页',
		name: 'societyHome',
		component: resolve => require(['@/views/society/home/<USER>'], resolve)
	},
	{
		path: '/soStatement',
		title: '社团统计报表',
		name: 'soStatement',
		component: resolve => require(['@/views/society/home/<USER>'], resolve)
	},
	{
		path: '/societyBaseInfo',
		title: '创建社团',
		name: 'societyBaseInfo',
		component: resolve => require(['@/views/society/societyBaseInfo/index.vue'], resolve)
	},
	{
		path: '/societyType',
		title: '社团类型管理',
		name: 'societyType',
		component: resolve => require(['@/views/society/societyType/index.vue'], resolve)
	},
	{
		path: '/societyBaseInfoAudit',
		title: '社团审核',
		name: 'societyBaseInfoAudit',
		component: resolve => require(['@/views/society/societyBaseInfoAudit/index.vue'], resolve)
	},
	{
		path: '/societyStudentAudit',
		title: '入社审核',
		name: 'societyStudentAudit',
		component: resolve => require(['@/views/society/societyStudentAudit/index.vue'], resolve)
	},
	{
		path: '/societyStudentRecord',
		title: '入社/退社记录',
		name: 'societyStudentRecord',
		component: resolve => require(['@/views/society/societyStudentRecord/index.vue'], resolve)
	},
	{
		path: '/societyIncomeExpendType',
		title: '收支类型',
		name: 'societyIncomeExpendType',
		component: resolve => require(['@/views/society/societyIncomeExpendType/index.vue'], resolve)
	},
	{
		path: '/societyIncomeExpendRecord',
		title: '收支记录',
		name: 'societyIncomeExpendRecord',
		component: resolve => require(['@/views/society/societyIncomeExpendRecord/index.vue'], resolve)
	},
	{
		path: '/alumnaManualAudit',
		title: '校友信息管理人工审核',
		name: 'alumnaManualAudit',
		component: resolve => require(['@/views/alumna/member/auditIndex.vue'], resolve)
	},
	{
		path: '/alumnaStats',
		title: '校友信息统计分析',
		name: 'alumnaStats',
		component: resolve => require(['@/views/alumna/stats/index.vue'], resolve)
	},
	{
		path: '/alumnaMember',
		title: '校友信息管理',
		name: 'alumnaMember',
		component: resolve => require(['@/views/alumna/member/index.vue'], resolve)
	},
	{
		path: '/alumnaSmsTemplate',
		title: '生日祝福模版管理',
		name: 'alumnaSmsTemplate',
		component: resolve => require(['@/views/alumna/smsTemplate/index.vue'], resolve)
	},
	{
		path: '/alumnaCommunityLabel',
		title: '社区标签管理',
		name: 'alumnaCommunityLabel',
		component: resolve => require(['@/views/alumna/communityLabel/index.vue'], resolve)
	},
	{
		path: '/alumnaCommunity',
		title: '社区管理',
		name: 'alumnaCommunity',
		component: resolve => require(['@/views/alumna/community/index.vue'], resolve)
	},
	{
		path: '/alumnaCommunityAlbum',
		title: '社区相册管理',
		name: 'alumnaCommunityAlbum',
		component: resolve => require(['@/views/alumna/album/index.vue'], resolve)
	},
	{
		path: '/classManagement',
		title: '社区相册管理',
		name: 'classManagement',
		component: resolve => require(['@/views/alumna/classManagement/index.vue'], resolve)
	},
	// 工会管理
	{
		path: '/unionInfo',
		title: '工会管理',
		name: 'unionInfo',
		component: resolve => require(['@/views/union/unionInfo/index.vue'], resolve)
	},
	{
		path: '/unionManager',
		title: '综合工会管理',
		name: 'unionManager',
		component: resolve => require(['@/views/union/unionManager/index.vue'], resolve)
	},
	{
		path: '/unionMember',
		title: '会员管理',
		name: 'unionMember',
		component: resolve => require(['@/views/union/unionMember/index.vue'], resolve)
	},
	{
		path: '/unionMemberManage',
		title: '综合会员管理',
		name: 'unionMemberManage',
		component: resolve => require(['@/views/union/unionMemberManage/index.vue'], resolve)
	},
	{
		path: '/unionMoveRecord',
		title: '入会记录',
		name: 'unionMoveRecord',
		component: resolve => require(['@/views/union/unionMoveRecord/index.vue'], resolve)
	},
	{
		path: '/unionMoveRecordManage',
		title: '综合入会记录',
		name: 'unionMoveRecordManage',
		component: resolve => require(['@/views/union/unionMoveRecordManage/index.vue'], resolve)
	},
	{
		path: '/unionMoveManage',
		title: '综合退会记录',
		name: 'unionMoveManage',
		component: resolve => require(['@/views/union/unionMoveManage/index.vue'], resolve)
	},
	{
		path: '/unionMoveRecordAudit',
		title: '入会审核',
		name: 'unionMoveRecordAudit',
		component: resolve => require(['@/views/union/unionMoveRecordAudit/index.vue'], resolve)
	},
	{
		path: '/unionAuditManage',
		title: '综合入会审核',
		name: 'unionAuditManage',
		component: resolve => require(['@/views/union/unionAuditManage/index.vue'], resolve)
	},
	{
		path: '/unionMsg',
		title: '意见处理',
		name: 'unionMsg',
		component: resolve => require(['@/views/union/unionMsg/index.vue'], resolve)
	},
	{
		path: '/unionMsgManage',
		title: '综合意见处理',
		name: 'unionMsgManage',
		component: resolve => require(['@/views/union/unionMsgManage/index.vue'], resolve)
	},
	{
		path: '/unionFinanceType',
		title: '收支类型管理',
		name: 'unionFinanceType',
		component: resolve => require(['@/views/union/unionFinanceType/index.vue'], resolve)
	},
	{
		path: '/unionFinanceRecord',
		title: '收支管理',
		name: 'unionFinanceRecord',
		component: resolve => require(['@/views/union/unionFinanceRecord/index.vue'], resolve)
	},
	// 健康管理
	{
		path: '/psyBaseInfo',
		title: '心理咨询师管理',
		name: 'psyBaseInfo',
		component: resolve => require(['@/views/health/psyBaseInfo/index.vue'], resolve)
	},
	{
		path: '/psyAppointOrder',
		title: '预约管理',
		name: 'psyAppointOrder',
		component: resolve => require(['@/views/health/psyAppointOrder/index.vue'], resolve)
	},
	{
		path: '/psyAppointStatistics',
		title: '心理咨询统计',
		name: 'psyAppointStatistics',
		component: resolve => require(['@/views/health/psyAppointStatistics/index.vue'], resolve)
	},
	// 后勤管理
	{
		path: '/hqHome',
		title: '首页',
		name: 'HqHome',
		component: resolve => require(['@/views/logistics/home/<USER>'], resolve)
	},
	{
		path: '/hqStoreroom',
		title: '库房管理',
		name: 'hqStoreroom',
		component: resolve => require(['@/views/logistics/hqStoreroom/index.vue'], resolve)
	},
	{
		path: '/supplier',
		title: '供应商管理',
		name: 'supplier',
		component: resolve => require(['@/views/logistics/supplier/index.vue'], resolve)
	},
	{
		path: '/supplierFiles',
		title: '供应商资质管理',
		name: 'supplierFiles',
		component: resolve => require(['@/views/logistics/supplierFile/index.vue'], resolve)
	},
	{
		path: '/materialCategory',
		title: '原料分类管理',
		name: 'materialCategory',
		component: resolve => require(['@/views/logistics/materialCategory/index.vue'], resolve)
	},
	{
		path: '/material',
		title: '原料管理',
		name: 'material',
		component: resolve => require(['@/views/logistics/material/index.vue'], resolve)
	},
	{
		path: '/stallOpening',
		title: '档口管理',
		name: 'stallOpening',
		component: resolve => require(['@/views/logistics/stallOpening/index.vue'], resolve)
	},
	{
		path: '/operatePerson',
		title: '质检员管理',
		name: '/operatePerson/one',
		component: resolve => require(['@/views/logistics/operatePerson/index.vue'], resolve)
	},
	{
		path: '/operatePerson',
		title: '领料人管理',
		name: '/operatePerson/two',
		component: resolve => require(['@/views/logistics/operatePerson/index.vue'], resolve)
	},
	{
		path: '/operatePerson',
		title: '库房管理员管理',
		name: '/operatePerson/three',
		component: resolve => require(['@/views/logistics/operatePerson/index.vue'], resolve)
	},
	{
		path: '/receiptOrder',
		title: '入库管理',
		name: 'receiptOrder',
		component: resolve => require(['@/views/logistics/receiptOrder/index.vue'], resolve)
	},
	{
		path: '/quarantineLog',
		title: '入库管理',
		name: 'quarantineLog',
		component: resolve => require(['@/views/logistics/quarantineLog/index.vue'], resolve)
	},
	{
		path: '/receiptOrderStatistics',
		title: '入库统计',
		name: 'receiptOrderStatistics',
		component: resolve => require(['@/views/logistics/receiptOrder/indexStatistics.vue'], resolve)
	},
	{
		path: '/shipmentOrder',
		title: '领料管理',
		name: 'shipmentOrder',
		component: resolve => require(['@/views/logistics/shipmentOrder/index.vue'], resolve)
	},
	{
		path: '/shipmentOrderStatistics',
		title: '领料统计',
		name: 'shipmentOrderStatistics',
		component: resolve => require(['@/views/logistics/shipmentOrder/indexStatistics.vue'], resolve)
	},
	{
		path: '/shipmentOrderBack',
		title: '退料管理',
		name: 'shipmentOrderBack',
		component: resolve => require(['@/views/logistics/shipmentOrder/indexBack.vue'], resolve)
	},
	{
		path: '/inventorySheet',
		title: '库存盘点',
		name: 'inventorySheet',
		component: resolve => require(['@/views/logistics/inventorySheet/index.vue'], resolve)
	},
	{
		path: '/statistics',
		title: '库存查询',
		name: 'statistics',
		component: resolve => require(['@/views/logistics/statistics/index.vue'], resolve)
	},
	{
		path: '/hqRepairReport',
		title: '报修管理',
		name: 'hqRepairReport',
		component: resolve => require(['@/views/maintain/application/index.vue'], resolve)
	},
	{
		path: '/hqBuildRoom',
		title: '楼栋管理',
		name: 'hqBuildRoom',
		component: resolve => require(['@/views/maintain/build/index.vue'], resolve)
	},
	{
		path: '/hqRepairDispatch',
		title: '派单管理',
		name: 'hqRepairDispatch',
		component: resolve => require(['@/views/maintain/sendOrders/index.vue'], resolve)
	},
	{
		path: '/hqRepairEvaluate',
		title: '评价管理',
		name: 'hqRepairEvaluate',
		component: resolve => require(['@/views/maintain/reviews/index.vue'], resolve)
	},
	{
		path: '/hqMp',
		title: '维修人员管理',
		name: 'hqMp',
		component: resolve => require(['@/views/maintain/mp/index.vue'], resolve)
	},
	{
		path: '/hqMpDutyRoster',
		title: '维修值班管理',
		name: 'hqMpDutyRoster',
		component: resolve => require(['@/views/maintain/mp/dutyIndex.vue'], resolve)
	},
	{
		path: '/hqBuildManager',
		title: '楼栋管理员信息管理',
		name: 'hqBuildManager',
		component: resolve => require(['@/views/maintain/buildManager/index.vue'], resolve)
	},
	{
		path: '/hqReportStatistics',
		title: '报修统计分析',
		name: 'hqReportStatistics',
		component: resolve => require(['@/views/maintain/statistics/index.vue'], resolve)
	},
	{
		path: '/workStatistics',
		title: '维修人员工作统计',
		name: 'workStatistics',
		component: resolve => require(['@/views/maintain/statistics/indexOne.vue'], resolve)
	},
	{
		path: '/purchaseManage',
		title: '采购管理',
		name: 'purchaseManage',
		component: resolve => require(['@/views/logistics/purchase/index.vue'], resolve)
	},
	{
		path: '/personnelManagement',
		title: '人事管理',
		name: 'personnelManagement',
		component: resolve => require(['@/views/logistics/personnelManagement/index.vue'], resolve)
	},
	//指挥调度
	{
		path: '/refuge',
		title: '避难场所',
		name: 'refuge',
		component: resolve => require(['@/views/screen/refuge/index.vue'], resolve)
	},
	{
		path: '/eventDeal',
		title: '事件上报',
		name: 'eventDeal',
		component: resolve => require(['@/views/screen/eventDeal/index.vue'], resolve)
	},
	{
		path: '/reportDuty',
		title: '值守',
		name: 'reportDuty',
		component: resolve => require(['@/views/screen/reportDuty/index.vue'], resolve)
	},
	{
		path: '/reportEvent',
		title: '事件处理',
		name: 'reportEvent',
		component: resolve => require(['@/views/screen/reportEvent/index.vue'], resolve)
	},
	{
		path: '/videoInfo',
		title: '视频信息',
		name: 'videoInfo',
		component: resolve => require(['@/views/screen/videoInfo/index.vue'], resolve)
	},
	{
		path: '/videoArea',
		title: '视频区域管理',
		name: 'videoArea',
		component: resolve => require(['@/views/screen/videoInfo/areaIndex.vue'], resolve)
	},
	{
		path: '/yjEquip',
		title: '救援装备',
		name: 'yjEquip',
		component: resolve => require(['@/views/screen/yjEquip/index.vue'], resolve)
	},
	{
		path: '/jyMaterials',
		title: '救援物资',
		name: 'jyMaterials',
		component: resolve => require(['@/views/screen/jyMaterials/index.vue'], resolve)
	},
	{
		path: '/jyPower',
		title: '救援力量',
		name: 'jyPower',
		component: resolve => require(['@/views/screen/jyPower/index.vue'], resolve)
	},
	{
		path: '/yjExpert',
		title: '应急专家',
		name: 'yjExpert',
		component: resolve => require(['@/views/screen/yjExpert/index.vue'], resolve)
	},
	{
		path: '/yjMedicalForce',
		title: '医疗力量',
		name: 'yjMedicalForce',
		component: resolve => require(['@/views/screen/yjMedicalForce/index-pro.vue'], resolve)
	},
	{
		path: '/yjTeam',
		title: '应急队伍',
		name: 'yjTeam',
		component: resolve => require(['@/views/screen/yjTeam/index.vue'], resolve)
	},
	{
		path: '/yjVehicle',
		title: '应急车辆',
		name: 'yjVehicle',
		component: resolve => require(['@/views/screen/yjVehicle/index.vue'], resolve)
	},
	{
		path: '/yjYuan',
		title: '预案配置',
		name: 'yjYuan',
		component: resolve => require(['@/views/screen/yjYuan/index.vue'], resolve)
	},
	{
		path: '/zbManage',
		title: '值班管理',
		name: 'zbManage',
		component: resolve => require(['@/views/screen/zbManage/index.vue'], resolve)
	},

	{
		path: '/zbPersonInfo',
		title: '值班人员',
		name: 'zbPersonInfo',
		component: resolve => require(['@/views/screen/zbPersonInfo/index.vue'], resolve)
	},
	{
		path: '/park',
		title: '停车场',
		name: 'park',
		component: resolve => require(['@/views/screen/park/index.vue'], resolve)
	},
	{
		path: '/buildingFacilities',
		title: '楼宇信息',
		name: 'buildingFacilities',
		component: resolve => require(['@/views/screen/buildingFacilities/index.vue'], resolve)
	},
	{
		path: '/tenantLogo',
		title: '租户LOGO配置信息',
		name: 'tenantLogo',
		component: resolve => require(['@/views/sys/tenant-logo/index.vue'], resolve)
	},
	{
		path: '/infrastructureIcon',
		title: '设施图标管理',
		name: 'infrastructureIcon',
		component: resolve => require(['@/views/screen/infrastructureIcon/index.vue'], resolve)
	},
	{
		path: '/infrastructureInfo',
		title: '设施信息',
		name: 'infrastructureInfo',
		component: resolve => require(['@/views/screen/infrastructureInfo/index.vue'], resolve)
	},
	//食堂管理
	{
		path: '/canteenRecord',
		title: '菜品留样管理',
		name: 'canteenRecord',
		component: resolve => require(['@/views/canteen/canteenRecordInfo/index.vue'], resolve)
	},
	{
		path: '/dinnerReservation',
		title: '晚餐预定管理',
		name: 'dinnerReservation',
		component: resolve => require(['@/views/canteen/dinnerReservation/index.vue'], resolve)
	},
	// 综合办公
	{
		path: '/platLostFound',
		title: '挂失招领',
		name: 'platLostFound',
		component: resolve => require(['@/views/platform/lostFound/index.vue'], resolve)
	},
	{
		path: '/platIdleTrade',
		title: '挂失招领',
		name: 'platIdleTrade',
		component: resolve => require(['@/views/platform/idleTrade/index.vue'], resolve)
	},
	{
		path: '/specificVisitorAppt',
		title: '访客预约',
		name: 'specificVisitorAppt',
		component: resolve => require(['@/views/specific/specificVisitorAppt/index.vue'], resolve)
	},
	// @-考勤管理------
	{
		path: '/sys/secondment',
		title: '借调管理',
		name: 'secondment',
		component: resolve => require(['@/views/sys/secondment'], resolve)
	},
	{
		path: '/sys/attendance-management/personnel',
		title: '考勤管理人事',
		name: 'attendanceManagementPersonnel',
		component: resolve => require(['@/views/sys/attendance-management/personnel'], resolve)
	},
	{
		path: '/sys/attendance-management/teacher',
		title: '考勤管理教师',
		name: 'attendanceManagementTeacher',
		component: resolve => require(['@/views/sys/attendance-management/teacher'], resolve)
	},
	{
		path: '/sys/leave',
		title: '考勤管理-请假',
		name: 'Leave',
		component: resolve => require(['@/views/sys/leave/index'], resolve)
	},
	{
		path: '/sys/holiday',
		title: '考勤管理-假日管理',
		name: 'Holiday',
		component: resolve => require(['@/views/sys/holiday/index'], resolve)
	},
	// --------------
	{
		path: '/sys/labor-union-home',
		title: '工会首页',
		name: 'labor-union-home',
		component: resolve => require(['@/views/sys/labor-union-home'], resolve)
	},

	{
		path: '/sys/club-home-student',
		title: '社团管理系统-学生',
		name: 'club-home-student',
		component: resolve => require(['@/views/sys/club-home-student'], resolve)
	},
	{
		path: '/sys/PointsRanking',
		title: '积分排行',
		name: 'PointsRanking',
		component: resolve => require(['@/views/sys/PointsRanking/index'], resolve)
	},

	{
		path: '/sys/club-home-teacher',
		title: '社团管理系统-教职工',
		name: 'club-home-teacher',
		component: resolve => require(['@/views/sys/club-home-teacher'], resolve)
	},
	{
		path: '/sys/retiree-home-teacher',
		title: '退休人员管理系统-教职工',
		name: 'retiree-home-teacher',
		component: resolve => require(['@/views/sys/retiree-home-teacher'], resolve)
	},
	{
		path: '/myAssignment',
		title: '我的任务',
		name: 'MyAssignment',
		component: resolve => require(['@/views/myAssignment/index.vue'], resolve)
	},
	{
		path: '/myAssignmentView',
		title: '我的任务查看',
		name: 'MyAssignmentView',
		component: resolve => require(['@/views/myAssignment/view.vue'], resolve)
	},
	{
		path: '/rzProjectSrAchievement',
		title: '软著',
		name: 'RzProjectSrAchievement',
		component: resolve => require(['@/views/projectSrAchievement/rz/index.vue'], resolve)
	},
	{
		path: '/etipnuuezknypsvProjectSrAchievement',
		title: '学术论文',
		name: 'EtipnuuezknypsvProjectSrAchievement',
		component: resolve =>
			require(['@/views/projectSrAchievement/etipnuuezknypsv/index.vue'], resolve)
	},
	{
		path: '/etipnuuezknypsvProjectSrWyenftrmyuzfeqh',
		title: '学术著作',
		name: 'EtipnuuezknypsvProjectSrWyenftrmyuzfeqh',
		component: resolve =>
			require(['@/views/projectSrAchievement/wyenftrmyuzfeqh/index.vue'], resolve)
	},
	{
		path: '/fjtnxbcilitdrpbProjectSrWyenftrmyuzfeqh',
		title: '平台',
		name: 'fjtnxbcilitdrpbProjectSrWyenftrmyuzfeqh',
		component: resolve =>
			require(['@/views/projectSrAchievement/fjtnxbcilitdrpb/index.vue'], resolve)
	},
	{
		path: '/gxokrzlcuyazmkeProjectSrWyenftrmyuzfeqh',
		title: '学术课题',
		name: 'gxokrzlcuyazmkeProjectSrWyenftrmyuzfeqh',
		component: resolve =>
			require(['@/views/projectSrAchievement/gxokrzlcuyazmke/index.vue'], resolve)
	},
	{
		path: '/veqormhclslamcxProjectSrWyenftrmyuzfeqh',
		title: '获奖成果',
		name: 'veqormhclslamcxProjectSrWyenftrmyuzfeqh',
		component: resolve =>
			require(['@/views/projectSrAchievement/veqormhclslamcx/index.vue'], resolve)
	},
	// Competition Awards
	{
		path: '/competitionAwards',
		title: '获奖成果',
		name: 'competitionAwards',
		component: resolve =>
			require(['@/views/projectSrAchievement/competitionAwards/index.vue'], resolve)
	},
	{
		path: '/mmtradvuivsevspProjectSrWyenftrmyuzfeqh',
		title: '学术专利',
		name: 'mmtradvuivsevspProjectSrWyenftrmyuzfeqh',
		component: resolve =>
			require(['@/views/projectSrAchievement/mmtradvuivsevsp/index.vue'], resolve)
	},
	{
		path: '/xgivtjabknckzveProjectSrWyenftrmyuzfeqh',
		title: '科技成果转化',
		name: 'xgivtjabknckzveProjectSrWyenftrmyuzfeqh',
		component: resolve =>
			require(['@/views/projectSrAchievement/xgivtjabknckzve/index.vue'], resolve)
	},
	{
		path: '/nsukrhuoldxhuwfProjectSrWyenftrmyuzfeqh',
		title: '技术产品',
		name: 'nsukrhuoldxhuwfProjectSrWyenftrmyuzfeqh',
		component: resolve =>
			require(['@/views/projectSrAchievement/nsukrhuoldxhuwf/index.vue'], resolve)
	},
	{
		path: '/kzpovkrvycscvrgProjectSrWyenftrmyuzfeqh',
		title: '技术标准',
		name: 'kzpovkrvycscvrgProjectSrWyenftrmyuzfeqh',
		component: resolve =>
			require(['@/views/projectSrAchievement/kzpovkrvycscvrg/index.vue'], resolve)
	},
	{
		path: '/scientificPayoffsManage',
		title: '科研成果管理',
		name: 'scientificPayoffsManage',
		component: resolve =>
			require(['@/views/projectSrAchievement/scientificPayoffsManage/index.vue'], resolve)
	},
	{
		path: '/scientificPayoffsReply',
		title: '科研成果回复',
		name: 'scientificPayoffsReply',
		component: resolve =>
			require(['@/views/projectSrAchievement/scientificPayoffsManage/indexOne.vue'], resolve)
	},
	// 纪委直通车(用户端)
	{
		path: '/DirectTrain',
		title: '纪委直通车',
		openWindow: true,
		name: 'hqRepairRepDirectTrainortCygn',
		component: resolve => require(['@/views/maintain/DirectTrain/index.vue'], resolve)
	},
	{
		path: '/DirectTrainList',
		title: '纪委直通车列表',
		openWindow: true,
		name: 'hqRepairRepDirectTrainortCygn',
		component: resolve => require(['@/views/maintain/DirectTrain/List.vue'], resolve)
	},
	// 纪委直通车(管理端)
	{
		path: '/disciplineInspectionReach',
		title: '纪委直通车',
		name: 'disciplineInspectionReach',
		component: resolve =>
			require(['@/views/logistics/disciplineInspectionReach/index.vue'], resolve)
	},
	// 其他页面systemOther
	{
		path: '/onlineSchool',
		title: '在线学习',
		name: 'onlineSchool',
		component: resolve => require(['@/views/systemOther/onlineSchool/index.vue'], resolve)
	},
	{
		path: '/superStar',
		title: '超星直播',
		name: 'superStar',
		component: resolve => require(['@/views/systemOther/superStar/index.vue'], resolve)
	},
	{
		path: '/reportForms',
		title: '报表管理',
		name: 'reportForms',
		component: resolve => require(['@/views/systemOther/reportForms/index.vue'], resolve)
	},
	{
		path: '/trafficInquiry',
		title: '交通查询',
		openWindow: true,
		name: 'reportForms',
		component: resolve => require(['@/views/systemOther/trafficInquiry/index.vue'], resolve)
	},
	// 资料管理
	{
		path: '/dataSystem',
		title: '资料管理系统',
		name: 'dataSystem',
		component: resolve => require(['@/views/systemOther/dataSystem/index.vue'], resolve)
	},
	{
		path: '/DataManagement',
		title: '首页',
		name: 'DataManagement',
		component: resolve => require(['@/views/Data _management/index.vue'], resolve)
	},
	{
		path: '/classification',
		title: '资料分类',
		name: 'classification',
		component: resolve => require(['@/views/Data _management/classification.vue'], resolve)
	},
	{
		path: '/myData',
		title: '我的资料',
		name: 'myData',
		component: resolve => require(['@/views/Data _management/myData.vue'], resolve)
	},
	{
		path: '/retrieval',
		title: '资料检索',
		name: 'retrieval',
		component: resolve => require(['@/views/Data _management/retrieval.vue'], resolve)
	},
	{
		path: '/labRoomInspect',
		title: '实验室日检',
		name: 'labRoomInspect',
		component: resolve => require(['@/views/lab/labRoomInspect/index.vue'], resolve)
	},
	{
		path: '/labRoomExamine',
		title: '实验室检查',
		name: 'labRoomExamine',
		component: resolve => require(['@/views/lab/labRoomExamine/index.vue'], resolve)
	},
	{
		path: '/labReformNotify',
		title: '实验室整改通知',
		name: 'labReformNotify',
		component: resolve => require(['@/views/lab/labReformNotify/index.vue'], resolve)
	},
	{
		path: '/ejxyLabReformNotify',
		title: '实验室整改回复',
		name: 'ejxyLabReformNotify',
		component: resolve => require(['@/views/lab/ejxyLabReformNotify/index.vue'], resolve)
	},
	{
		path: '/hrnPersonDictionary',
		title: '岗位字典',
		name: 'hrnPersonDictionary',
		component: resolve => require(['@/views/person/hrnPersonDictionary/index.vue'], resolve)
	},
	{
		path: '/hrnPersonEducation',
		title: '入职教育背景表',
		name: 'hrnPersonEducation',
		component: resolve => require(['@/views/person/hrnPersonEducation/index.vue'], resolve)
	},
	{
		path: '/hrnPersonEntry',
		title: '入职基本信息',
		name: 'hrnPersonEntry',
		component: resolve => require(['@/views/person/hrnPersonEntry/index.vue'], resolve)
	},
	{
		path: '/hrnPersonPost',
		title: '岗位信息',
		name: 'hrnPersonPost',
		component: resolve => require(['@/views/person/hrnPersonPost/index.vue'], resolve)
	},
	{
		path: '/hrnPersonPostRejigger',
		title: '岗位变动',
		name: 'hrnPersonPostRejigger',
		component: resolve => require(['@/views/person/hrnPersonPostRejigger/index.vue'], resolve)
	},
	{
		path: '/hrnPersonPostTransfer',
		title: '岗位转岗',
		name: 'hrnPersonPostTransfer',
		component: resolve => require(['@/views/person/hrnPersonPostTransfer/index.vue'], resolve)
	},
	{
		path: '/hrnPersonPostType',
		title: '岗位类型',
		name: 'hrnPersonPostType',
		component: resolve => require(['@/views/person/hrnPersonPostType/index.vue'], resolve)
	},
	{
		path: '/hrnPersonVocQual',
		title: '职业资格',
		name: 'hrnPersonVocQual',
		component: resolve => require(['@/views/person/hrnPersonVocQual/index.vue'], resolve)
	},
	// 国资服务
	{
		path: '/platPurchaseApply',
		title: '国资处来购验收申请',
		name: 'platPurchaseApply',
		component: resolve => require(['@/views/platPurchase/platPurchaseApply/index.vue'], resolve)
	},
	{
		path: '/platPurchaseVerify',
		title: '国资处来购验收审核',
		name: 'platPurchaseVerify',
		component: resolve => require(['@/views/platPurchase/platPurchaseVerify/index.vue'], resolve)
	},
	{
		path: '/platPurchaseAll',
		title: '国资处来购验收总汇',
		name: 'platPurchaseAll',
		component: resolve => require(['@/views/platPurchase/platPurchaseAll/index.vue'], resolve)
	},
	{
		path: '/userCertificateInfomation',
		title: '证书管理审计信息',
		name: 'platPurchaseAll',
		component: resolve =>
			require(['@/views/systemOther/audit/userCertificateInfomation.vue'], resolve)
	},
	{
		path: '/AuthorizeauditInformation',
		title: '授权管理审计信息',
		name: 'platPurchaseAll',
		component: resolve =>
			require(['@/views/systemOther/audit/AuthorizeauditInformation.vue'], resolve)
	},

	{
		path: '/userRzInfomation',
		title: '认证管理审计信息',
		name: 'platPurchaseAll',
		component: resolve => require(['@/views/systemOther/audit/userRzInfomation.vue'], resolve)
	},
	// 子系统-内部
	{
		path: '/subsystem',
		title: '子系统',
		name: 'subsystem',
		component: resolve => require(['@/views/systemOther/subsystem/index.vue'], resolve)
	},
	// 子系统-新窗口
	{
		path: '/subsystemW',
		title: '子系统',
		name: 'subsystem',
		openWindow: true,
		component: resolve => require(['@/views/systemOther/subsystem/index.vue'], resolve)
	}
];
