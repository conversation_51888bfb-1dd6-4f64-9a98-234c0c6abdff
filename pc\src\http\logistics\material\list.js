import httpApi from '@/http/logistics/material/api.js';
export default {
	computed: {
		component() {
			return {
				// 纯列表结构
				name: 'es-data-table'
			};
		},
		treeParam() {
			return {};
		},
		table() {
			return {
				url: httpApi.listJson,
				param: { handlerType: '' },
				full: true,
				fit: true,
				page: true,
				stripe: true,
				numbers: true,
				checkbox: true,
				'row-key': 'id',
				toolbar: [
					{
						type: 'button',
						contents: [
							{
								text: '新增',
								type: 'primary',
								code: 'toolbar'
							}
							// {
							// 	text: '批量启用',
							// 	type: 'primary',
							// 	code: 'toolbar'
							// },
							// {
							// 	text: '批量停用',
							// 	type: 'danger',
							// 	code: 'toolbar'
							// },
							// {
							// 	text: '批量导入',
							// 	type: '',
							// 	code: 'toolbar'
							// }
						]
					},
					{
						type: 'search',
						reset: true,
						contents: [
							//{
							//	name: 'accountName_like',
							//	placeholder: '配置名称',
							//	label: '配置名称',
							//	col: 6
							//},
							{
								col: 6,
								name: 'keyword',
								label: '关键字',
								placeholder: '原料编号/原料名称'
							},
							{
								type: 'select',
								col: 6,
								name: 'status',
								label: '状态',
								placeholder: '状态',
								sysCode: 'hqIsUse'
							}
						]
					}
				],
				thead: [
					{
						title: '原料名称',
						align: 'left',
						field: 'name',
						sortable: 'custom',
						showOverflowTooltip: true
					},
					{
						title: '原料编号',
						align: 'left',
						field: 'code',
						sortable: 'custom',
						showOverflowTooltip: true
					},
					{
						title: '原料分类',
						align: 'left',
						field: 'name',
						sortable: 'custom',
						showOverflowTooltip: true
					},
					{
						title: '品牌',
						align: 'left',
						field: 'brand',
						sortable: 'custom',
						showOverflowTooltip: true
					},
					{
						title: '规格',
						align: 'left',
						field: 'specification',
						sortable: 'custom',
						showOverflowTooltip: true
					},
					{
						title: '库存单位',
						align: 'left',
						field: 'unit',
						sortable: 'custom',
						showOverflowTooltip: true,
						sysCode: 'hq_material_unit'
					},
					{
						title: '状态',
						align: 'left',
						field: 'status',
						sortable: 'custom',
						showOverflowTooltip: true,
						sysCode: 'hqIsUse'
					},
					{
						title: '操作',
						type: 'handle',
						width: 180,
						template: '',
						events: [
							{
								code: 'row',
								text: '编辑'
							},
							{
								code: 'row',
								text: '查看'
							},
							{
								code: 'row',
								text: '启用',
								rules: rows => {
									return rows.status == 0;
								}
							},
							{
								code: 'row',
								text: '禁用',
								rules: rows => {
									return rows.status == 1;
								}
							},
							{
								code: 'row',
								text: '删除'
							}
						]
					}
				],
				optionData: {
					status: [
						{
							value: 1,
							name: '启用'
						},
						{
							value: 0,
							name: '禁用'
						}
					]
				}
			};
		}
	},
	methods: {
		changeTable(res) {
			switch (res.name) {
				case 'status':
					this.changeStatus(res.data.id);
					break;
			}
		},
		//改变数据启用状态
		changeStatus(id) {
			// this.$request({
			// 	url: httpApi.jobCoFinancialServeChangeStatus,
			// 	data: {id: id},
			// 	method: 'POST'
			// }).then(res=>{
			// 	if(res.success){
			// 		this.$message.success('修改成功');
			// 	}else{
			// 		this.$message.error(res.msg);
			// 	}
			// });
		}
	}
};
