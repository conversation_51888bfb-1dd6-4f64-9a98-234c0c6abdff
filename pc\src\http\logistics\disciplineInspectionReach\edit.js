export default {
	computed: {
		formItemList() {
			return [
				{
					name: 'isAnonymity',
					label: '是否匿名：0实名 1匿名',
					value: '',
					col: 12,
					rules: {
						required: true,
						message: '请输入是否匿名：0实名 1匿名'
					}
				},
				{
					name: 'reporterName',
					label: '举报人姓名',
					value: '',
					col: 12,
					rules: {
						required: true,
						message: '请输入举报人姓名'
					}
				},
				{
					name: 'reporterIdcard',
					label: '举报人身份证号',
					value: '',
					col: 12,
					rules: {
						required: true,
						message: '请输入举报人身份证号'
					}
				},
				{
					name: 'reporterPhone',
					label: '举报人联系方式',
					value: '',
					col: 12,
					rules: {
						required: true,
						message: '请输入举报人联系方式'
					}
				},
				{
					name: 'beReporterName',
					label: '被举报人姓名',
					value: '',
					col: 12,
					rules: {
						required: true,
						message: '请输入被举报人姓名'
					}
				},
				{
					name: 'beReporterDepartment',
					label: '被举报人单位',
					value: '',
					col: 12,
					rules: {
						required: true,
						message: '请输入被举报人单位'
					}
				},
				{
					name: 'beReporterJob',
					label: '被举报人职务',
					value: '',
					col: 12,
					rules: {
						required: true,
						message: '请输入被举报人职务'
					}
				},
				{
					name: 'title',
					label: '标题',
					value: '',
					col: 12,
					rules: {
						required: true,
						message: '请输入标题'
					}
				},
				{
					name: 'content',
					label: '内容',
					value: '',
					col: 12,
					rules: {
						required: true,
						message: '请输入内容'
					}
				},
				{
					name: 'filelist',
					label: '图片',
					value: '',
					col: 12,
					rules: {
						required: true,
						message: '请输入图片'
					}
				},
				{
					name: 'status',
					label: '状态',
					value: '',
					col: 12,
					rules: {
						required: true,
						message: '请输入状态'
					}
				},
				{
					name: 'auditStatus',
					label: '审核状态',
					value: '',
					col: 12,
					rules: {
						required: true,
						message: '请输入审核状态'
					}
				},
				{
					name: 'auditOpinion',
					label: '审核意见',
					value: '',
					col: 12,
					rules: {
						required: true,
						message: '请输入审核意见'
					}
				},
				{
					name: 'auditUserId',
					label: '审核人Id',
					value: '',
					col: 12,
					rules: {
						required: true,
						message: '请输入审核人Id'
					}
				},
				{
					name: 'auditUserName',
					label: '审核人姓名',
					value: '',
					col: 12,
					rules: {
						required: true,
						message: '请输入审核人姓名'
					}
				},
				{
					name: 'auditTime',
					label: '审核时间',
					value: '',
					col: 12,
					rules: {
						required: true,
						message: '请输入审核时间'
					}
				},
			]
		}
	}
};
