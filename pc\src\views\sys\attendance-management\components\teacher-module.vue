<template>
	<div class="main-center-left">
		<!--  表格  -->
		<div class="main-center-table">
			<div class="main-center-table-title title">
				<img class="title-img" src="@ast/images/sys/card.png" alt="" />
				<div>上课统计</div>
			</div>
			<es-select
				v-model="defaultValue"
				placeholder="请选择"
				:data="selectOption"
				class="main-center-table-select"
				size="small"
				@change="handleChange"
			></es-select>
			<div class="main-center-table-con">
				<div id="tableEcharts" class="table-echarts"></div>
			</div>
		</div>
		<!--  图表  -->
		<div class="main-center-chart">
			<div class="main-center-chart-title title">
				<img class="title-img" src="@ast/images/sys/card.png" alt="" />
				<div>周考勤走势</div>
			</div>
			<div id="statisticsEcharts" class="main-center-chart-con"></div>
		</div>
	</div>
</template>

<script>
export default {
	name: 'TableModule',
	data() {
		return {
			selectOption: [
				{
					value: '选项1',
					label: '月'
				},
				{
					value: '选项2',
					label: '周'
				}
			],
			defaultValue: '选项1',
			/**考勤统计的表格配置*/
			statisticsOption: {
				tooltip: {
					trigger: 'axis',
					axisPointer: {
						type: 'line'
					},
					backgroundColor: '#80bbf4',
					textStyle: {
						color: '#ffffff'
					},
					formatter: params => {
						let text = '';
						let total = params.reduce((pre, item) => {
							return pre + item.data;
						}, 0);

						params.forEach(item => {
							let percentage = ((item.data * 100) / total).toFixed(2);
							let img =
								percentage > 30
									? require('@/assets/images/sys/top.png')
									: require('@/assets/images/sys/bottom.png');
							text += `<div>${item.seriesName}人数：${item.data}<img  style="margin: 0 10px;width: 12px;height:12px;" src="${img}"/>${percentage}%</div>`;
						});
						return text;
					}
				},
				grid: {
					left: '2%',
					right: '2%',
					bottom: '2%',
					containLabel: true
				},
				legend: {
					data: ['考勤', '请假', '旷课'],
					right: 20,
					itemWidth: 16
				},
				xAxis: [
					{
						type: 'category',
						axisTick: { show: false },
						axisLine: {
							show: false
						},
						data: ['10-02', '10-03', '10-04', '10-05', '10-06']
					}
				],
				yAxis: [
					{
						type: 'value',
						name: '单位：人',
						min: 0,
						max: 10000,
						interval: 2000,
						nameTextStyle: {
							padding: [0, 65, 10, 0]
						}
					}
				],
				series: [
					{
						name: '考勤',
						type: 'bar',
						barWidth: 20,
						barGap: 0,
						emphasis: {
							focus: 'series'
						},
						itemStyle: {
							color: new this.$echarts.graphic.LinearGradient(0, 0, 0, 1, [
								{
									offset: 0,
									color: 'rgb(0, 118, 232)'
								},
								{
									offset: 1,
									color: 'rgb(233, 243, 253)'
								}
							]),
							borderRadius: [50, 50, 0, 0]
						},
						data: [6200, 4320, 5010, 4340, 7900]
					},
					{
						name: '请假',
						type: 'bar',
						barWidth: 20,
						emphasis: {
							focus: 'series'
						},
						itemStyle: {
							color: new this.$echarts.graphic.LinearGradient(0, 0, 0, 1, [
								{
									offset: 0,
									color: 'rgb(235, 171, 97)'
								},
								{
									offset: 1,
									color: 'rgb(253, 251, 233)'
								}
							]),
							borderRadius: [50, 50, 0, 0]
						},
						data: [4200, 6820, 5910, 4340, 6900]
					},
					{
						name: '旷课',
						type: 'bar',
						barWidth: 20,
						emphasis: {
							focus: 'series'
						},
						itemStyle: {
							color: new this.$echarts.graphic.LinearGradient(0, 0, 0, 1, [
								{
									offset: 0,
									color: 'rgb(236, 95, 104)'
								},
								{
									offset: 1,
									color: 'rgb(253, 233, 233)'
								}
							]),
							borderRadius: [50, 50, 0, 0]
						},
						data: [6500, 5320, 7010, 5540, 5900]
					}
				]
			},
			tableOption: {
				tooltip: {
					trigger: 'axis',
					axisPointer: {
						type: 'cross',
						label: {
							backgroundColor: '#6a7985'
						}
					}
				},
				legend: {
					data: ['上课', '请假', '旷课']
				},
				grid: {
					left: '3%',
					right: '4%',
					bottom: '3%',
					containLabel: true
				},
				xAxis: [
					{
						type: 'category',
						boundaryGap: true,
						axisTick: {
							show: false
						},
						axisLine: {
							show: false
						},
						data: [
							'理论力学',
							'材料力学',
							'结构力学',
							'建筑物理',
							'土木工程材料学',
							'测量学',
							'土力学',
							'核建筑概论',
							'工程地质',
							'道路工程'
						]
					}
				],
				yAxis: [
					{
						type: 'value',
						name: '单位：人',
						min: 0,
						max: 10000,
						interval: 2000,
						nameTextStyle: {
							padding: [0, 65, 10, 0]
						}
					}
				],
				series: [
					{
						name: '旷课',
						type: 'line',
						stack: 'Total',
						smooth: true,
						lineStyle: {
							width: 2,
							color: '#ec5f68'
						},
						symbolSize: 8,
						symbol: 'circle',
						itemStyle: {
							normal: {
								color: '#ec5f68', // 拐点的颜色
								borderColor: '#ffffff', // 拐点边框颜色
								borderWidth: 2 // 拐点边框大小
							}
						},
						areaStyle: {
							opacity: 0.8,
							color: new this.$echarts.graphic.LinearGradient(0, 0, 0, 1, [
								{
									offset: 0,
									color: '#ec878c'
								},
								{
									offset: 1,
									color: '#f8eff1'
								}
							])
						},
						emphasis: {
							focus: 'series'
						},
						data: [2000, 4500, 2200, 4200, 2800, 4800, 3000, 5200, 2800, 5000]
					},
					{
						name: '请假',
						type: 'line',
						stack: 'Total',
						smooth: true,
						lineStyle: {
							width: 2,
							color: '#e7af6d'
						},
						symbolSize: 8,
						symbol: 'circle',
						itemStyle: {
							normal: {
								color: '#ebab61', // 拐点的颜色
								borderColor: '#ffffff', // 拐点边框颜色
								borderWidth: 2 // 拐点边框大小
							}
						},
						areaStyle: {
							opacity: 0.8,
							color: new this.$echarts.graphic.LinearGradient(0, 0, 0, 1, [
								{
									offset: 0,
									color: '#d9b68b'
								},
								{
									offset: 1,
									color: '#dad7d2'
								}
							])
						},
						emphasis: {
							focus: 'series'
						},
						data: [2000, 2200, 2400, 2200, 2000, 2400, 1800, 2200, 2400, 2200]
					},
					{
						name: '上课',
						type: 'line',
						stack: 'Total',
						smooth: true,
						lineStyle: {
							width: 2,
							color: '#0578e9'
						},
						itemStyle: {
							normal: {
								color: '#0f7ee9', // 拐点的颜色
								borderColor: '#ffffff', // 拐点边框颜色
								borderWidth: 2 // 拐点边框大小
							}
						},
						symbolSize: 8,
						symbol: 'circle',
						areaStyle: {
							opacity: 0.8,
							color: new this.$echarts.graphic.LinearGradient(0, 0, 0, 1, [
								{
									offset: 0,
									color: '#469bed'
								},
								{
									offset: 1,
									color: '#c0ddf9'
								}
							])
						},
						emphasis: {
							focus: 'series'
						},
						data: [2000, 2200, 2400, 2200, 2000, 2400, 1800, 2200, 2400, 2200]
					}
				]
			},
			tableChart: null, // 表格图表实例
			statisticsChart: null // 考勤统计的图表实例
		};
	},
	mounted() {
		this.initStatistics();
		this.initTable();
	},
	methods: {
		/**切换table图表的时间范围*/
		handleChange(val) {
			console.log(val);
		},
		/**初始化考勤统计图表*/
		initStatistics() {
			let charDom = document.getElementById('statisticsEcharts');
			this.statisticsChart = this.$echarts.init(charDom);
			this.statisticsChart.setOption(this.statisticsOption);
			window.addEventListener('resize', () => {
				this.statisticsChart && this.statisticsChart.resize();
			});
		},
		/**初始化表格图表*/
		initTable() {
			let charDom = document.getElementById('tableEcharts');
			this.tableChart = this.$echarts.init(charDom);
			this.tableChart.setOption(this.tableOption);
			window.addEventListener('resize', () => {
				this.tableChart && this.tableChart.resize();
			});
		}
	}
};
</script>

<style scoped lang="scss">
@import '@ast/style/public.scss';
.main-center {
	&-left {
		width: 67%;
		border-radius: 8px;
	}
	/**表格*/
	&-table {
		width: 100%;
		padding: 12px 20px;
		background: #ffffff;
		border-radius: 8px;
		position: relative;
		&-title {
			justify-content: flex-start;
			padding-bottom: 12px;
			margin-bottom: 12px;
			border-bottom: 1px solid rgba(241, 244, 247, 1);
		}
		&-select {
			position: absolute;
			width: 60px;
			top: 60px;
			right: 50px;
			//::v-deep .el-input__inner {
			//	border-radius: 20px;
			//}
		}
		&-con {
			height: 340px;
			width: 100%;
			//overflow-x: scroll;
			//&::-webkit-scrollbar-track {
			//	background-color: #ebeef5;
			//}
			//&::-webkit-scrollbar-thumb {
			//	background: #0076e8;
			//}
			.table-echarts {
				height: 100%;
				width: 100%;
				//width: 100vw;
			}
		}
	}
	/**图表*/
	&-chart {
		background: #fff;
		margin-top: 12px;
		padding: 12px 20px;
		border-radius: 8px;
		&-title {
			justify-content: flex-start !important;
			padding-bottom: 12px;
			border-bottom: 1px solid rgba(241, 244, 247, 1);
			margin-bottom: 12px;
		}
		&-con {
			height: 300px;
			width: 100%;
		}
	}
}
.title {
	display: flex;
	align-items: center;
	font-size: 16px;
	font-weight: 550;
	&-img {
		width: 22px;
		height: 22px;
		margin-right: 8px;
	}
}
</style>
