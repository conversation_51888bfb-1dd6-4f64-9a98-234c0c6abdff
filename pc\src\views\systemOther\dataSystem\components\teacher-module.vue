<template>
	<div class="main-center-left">
		<!--  表格  -->
		<div class="main-center-table">
			<div class="main-center-table-title title">
				<img class="title-img" src="@ast/images/systemOther/online-icon5.png" alt="" />
				<div>课程学员统计</div>
			</div>

			<div class="search-box">
				<es-input
					v-model="searchParams.keyword"
					placeholder="请输入关键字搜索"
					class="input"
					size="small"
				/>
				<label class="label">二级学院:</label>
				<es-select
					v-model="searchParams.defaultValue"
					placeholder="请选择"
					:data="selectOption"
					class="select"
					size="small"
				></es-select>

				<es-button class="search-btn" icon="el-icon-search" type="primary" size="small">
					查询
				</es-button>
				<!-- 重置 -->
				<es-button class="search-btn" icon="el-icon-refresh-left" size="small" @click="handleReset">
					重置
				</es-button>
			</div>

			<div class="main-center-table-con">
				<div id="tableEcharts" class="table-echarts"></div>
			</div>
		</div>
		<!--  图表  -->
		<div class="top-box">
			<div class="top-l">
				<div class="top-l-title title">
					<img class="title-img" src="@ast/images/systemOther/online-icon6.png" alt="" />
					<div>直播排行榜</div>
					<es-select
						v-model="selectLive"
						placeholder=""
						:data="selectOption"
						class="select"
						size="mini"
					></es-select>
				</div>
				<div class="content">
					<div v-if="liveList && liveList.length" style="width: 100%; height: 100%">
						<div
							v-for="(item, index) in liveList.slice(0, 5)"
							:key="index"
							class="itemContent"
							@click="toDetail(item)"
						>
							<div class="floor1">
								<div
									v-if="index === 0 || index === 1 || index === 2"
									class="square"
									:class="
										index == 0
											? 'activeNoticeOne'
											: index == 1
											? 'activeNoticeTwo'
											: 'activeNoticeThree'
									"
								>
									{{ 'TOP' + (index + 1) }}
								</div>
								<div v-else class="dot">
									{{ index + 1 }}
								</div>
								<div class="title-text">{{ item.title }}</div>
							</div>
							<div class="floor2">
								<div class="describe">{{ `授课老师：杨老师   浏览量：${item.describe}人` }}</div>
								<!-- <div class="time">{{ item.createTime }}</div> -->
							</div>
							<el-tag v-if="item.liveState" size="mini" class="live">直播中</el-tag>
							<el-tag v-else type="info" size="mini" class="live">直播回放</el-tag>
						</div>
					</div>
					<Empty v-else :tips="'暂无数据'" class="noticeStyle" />
				</div>
			</div>
			<div class="top-r">
				<div class="top-l-title title">
					<img class="title-img" src="@ast/images/systemOther/online-icon7.png" alt="" />
					<div>课程评价</div>
				</div>
				<div class="content">
					<div v-if="courseList && courseList.length" style="width: 100%; height: 100%">
						<div
							v-for="(item, index) in courseList.slice(0, 5)"
							:key="index"
							class="itemContent"
							@click="toDetail(item)"
						>
							<div class="floor1">
								<div class="title-text">{{ item.title }}</div>
								<el-rate
									v-model="item.score"
									disabled
									show-score
									:colors="['#ff6b0c', '#ff6b0c', '#ff6b0c']"
									text-color="#ff6b0c"
									score-template="{value}"
								></el-rate>
							</div>
							<div class="floor2">
								<div class="describe">{{ item.describe }}</div>
								<div class="time">{{ item.time }}</div>
							</div>
						</div>
					</div>
					<Empty v-else :tips="'暂无数据'" class="noticeStyle" />
				</div>
			</div>
		</div>
	</div>
</template>

<script>
export default {
	name: 'TableModule',
	data() {
		return {
			selectOption: [
				{
					value: '选项1',
					label: '月'
				},
				{
					value: '选项2',
					label: '周'
				}
			],
			searchParams: {
				defaultValue: '选项1'
			},
			selectLive: '选项1',
			liveList: [
				{
					title: '商务金融 【微积分】',
					describe: 2000,
					liveState: true
				},
				{
					title: '艺术摄影 【摄影艺术创作】',
					describe: 1500,
					liveState: true
				},
				{
					title: '人工智能 【人工智能语言与伦理】',
					describe: 1200,
					liveState: true
				},
				{
					title: '某某课程 【直播标题】',
					describe: 800,
					liveState: false
				},
				{
					title: '某某课程 【直播标题】',
					describe: 500,
					liveState: false
				}
			],
			courseList: [
				{
					title: '可汗学院公开课：古代中世纪史',
					describe: '课程评价信息文字课程评价信息文字课程评价信息文字...',
					time: '2023.10.01 15:30:30',
					score: 5
				},
				{
					title: '建筑学概论',
					describe: '课程评价信息文字课程评价信息文字课程评价信息文字...',
					time: '2023.10.01 15:30:30',
					score: 4.7
				},
				{
					title: '商务基本礼仪',
					describe: '课程评价信息文字课程评价信息文字课程评价信息文字...',
					time: '2023.10.01 15:30:30',
					score: 4.5
				},
				{
					title: '软件设计',
					describe: '课程评价信息文字课程评价信息文字课程评价信息文字...',
					time: '2023.10.01 15:30:30',
					score: 3.6
				},
				{
					title: '道路工程实践',
					describe: '课程评价信息文字课程评价信息文字课程评价信息文字...',
					time: '2023.10.01 15:30:30',
					score: 2
				}
			],

			tableChart: null, // 表格图表实例
			statisticsChart: null // 考勤统计的图表实例
		};
	},
	mounted() {
		this.initTable();
	},
	methods: {
		/**切换table图表的时间范围*/
		handleChange(val) {
			console.log(val);
		},

		/**初始化表格图表*/
		initTable() {
			const dataX = [
				'理论力学',
				'材料力学',
				'结构力学',
				'建筑物理',
				'土木工程材料学',
				'测量学',
				'土力学',
				'核建筑概论',
				'工程地质',
				'道路工程'
			];

			const dataS = [];
			let tableOption = {
				tooltip: {
					trigger: 'axis',
					axisPointer: {
						type: 'cross',
						label: {
							backgroundColor: '#6a7985'
						}
					}
				},
				legend: {
					data: ['上课', '请假', '旷课'],
					icon: 'rect',
					right: 20,
					itemWidth: 16
				},
				grid: {
					left: '3%',
					right: '4%',
					bottom: '3%',
					containLabel: true
				},
				xAxis: [
					{
						type: 'category',
						boundaryGap: true,
						axisTick: {
							show: false
						},
						axisLine: {
							show: false
						},
						data: dataX
					}
				],
				yAxis: [
					{
						type: 'value',
						name: '单位：人',
						min: 0,
						max: 10000,
						interval: 2000,
						nameTextStyle: {
							padding: [0, 65, 10, 0]
						}
					}
				],
				series: [
					{
						name: '上课',
						type: 'line',
						stack: 'Total',
						smooth: true,
						lineStyle: {
							width: 2,
							color: '#0578e9'
						},
						itemStyle: {
							normal: {
								color: '#0f7ee9', // 拐点的颜色
								borderColor: '#ffffff', // 拐点边框颜色
								borderWidth: 2 // 拐点边框大小
							}
						},
						symbolSize: 8,
						symbol: 'circle',
						areaStyle: {
							opacity: 0.8,
							color: new this.$echarts.graphic.LinearGradient(0, 0, 0, 1, [
								{
									offset: 0,
									color: '#469bed'
								},
								{
									offset: 1,
									color: '#c0ddf9'
								}
							])
						},
						emphasis: {
							focus: 'series'
						},
						data: dataS
					}
				]
			};
			let charDom = document.getElementById('tableEcharts');
			this.tableChart = this.$echarts.init(charDom);
			this.tableChart.setOption(tableOption);
			window.addEventListener('resize', () => {
				this.tableChart && this.tableChart.resize();
			});
		}
	}
};
</script>

<style scoped lang="scss">
@import '@ast/style/public.scss';
.main-center {
	&-left {
		width: 67%;
		border-radius: 8px;
	}
	/**表格*/
	&-table {
		width: 100%;
		padding: 12px 20px;
		background: #ffffff;
		border-radius: 8px;
		position: relative;
		&-title {
			justify-content: flex-start;
			padding-bottom: 12px;
			margin-bottom: 12px;
			border-bottom: 1px solid rgba(241, 244, 247, 1);
		}
		.search-box {
			position: absolute;
			// width: 60px;
			top: 10px;
			right: 20px;
			//::v-deep .el-input__inner {
			//	border-radius: 20px;
			//}
			display: flex;
			align-items: center;
			color: #7a8392;
			.label {
				margin-right: 6px;
			}
			.input {
				width: 130px;
				margin-right: 15px;
			}
			.select {
				width: 100px;
				margin-right: 15px;
			}
			// .search-btn {
			// 	margin-right: 10px;
			// }
		}
		&-con {
			height: 340px;
			width: 100%;
			//overflow-x: scroll;
			//&::-webkit-scrollbar-track {
			//	background-color: #ebeef5;
			//}
			//&::-webkit-scrollbar-thumb {
			//	background: #0076e8;
			//}
			.table-echarts {
				height: 100%;
				width: 100%;
				//width: 100vw;
			}
		}
	}
	/**图表*/
	.top-box {
		display: flex;
		justify-content: space-between;
		.top-l,
		.top-r {
			background: #fff;
			margin-top: 12px;
			padding: 12px 20px;
			border-radius: 8px;
			// flex-shrink: 1;
			width: 49.3%;
			&-title {
				justify-content: flex-start !important;
				padding-bottom: 12px;
				border-bottom: 1px solid rgba(241, 244, 247, 1);
				margin-bottom: 12px;
				position: relative;
				.select {
					position: absolute;
					top: 0;
					right: 0;
					width: 60px;
					height: 24px;
					font-size: 12px;
					letter-spacing: normal;
					color: #3e71f0;
				}
			}
			.content {
				width: 100%;
				.itemContent {
					cursor: pointer;
					width: 100%;
					// height: 46px;
					// border-bottom: 1px solid #ebebeb;
					display: flex;
					flex-direction: column;
					// align-items: center;
					padding: 8px 0;
					position: relative;
					.floor1 {
						display: flex;
						line-height: 1.2;
						margin-bottom: 6px;
						.dot,
						.square {
							width: 18px;
							height: 18px;
							background: #ecf4ff;
							font-weight: normal;
							color: #808998;
							line-height: 18px;
							font-size: 12px;
							text-align: center;
							border-radius: 50%;
							margin: 0 14px;
						}
						.square {
							width: auto;
							padding: 0 6px;
							height: 18px;
							color: #fff;
							font-size: 12px;
							border-radius: 0 6px 0 6px;
							margin: 0 6px 0 0;
						}
						.title-text {
							color: #333;
							font-size: 14px;
							font-weight: bold;
						}
						.activeNoticeOne {
							background: linear-gradient(
								180deg,
								rgba(249, 223, 128, 1) 0%,
								rgba(230, 172, 85, 1) 96%
							);
						}
						.activeNoticeTwo {
							background: linear-gradient(
								180deg,
								rgba(233, 241, 252, 1) 0%,
								rgba(202, 215, 229, 1) 96%
							);
						}
						.activeNoticeThree {
							background: linear-gradient(
								180deg,
								rgba(248, 227, 208, 1) 0%,
								rgba(236, 187, 159, 1) 96%
							);
						}
					}
					.floor2 {
						display: flex;
						justify-content: space-between;
						line-height: 1.2;
						.describe {
							padding: 0 0 0 10px;
							flex: 1;
							font-size: 14px;
							font-weight: 400;
							color: #666666;
							overflow: hidden;
							text-overflow: ellipsis;
							white-space: nowrap;
							padding: 0 18px 0 46px;
						}
						.time {
							// width: 140px;
							font-size: 14px;
							font-weight: 400;
							color: #7a8392;
						}
					}
					.live {
						width: 55px;
						text-align: center;
						position: absolute;
						border-width: 0px;
						padding: 0 8px;
						top: 3px;
						right: 0px;
						font-size: 10px;
					}
				}
			}
		}
		.top-r {
			.content {
				.itemContent {
					::v-deep .floor1 {
						display: flex;
						justify-content: space-between;
						.title-text {
							color: #333;
							font-size: 14px;
							font-weight: bold;
							overflow: hidden;
							text-overflow: ellipsis;
							white-space: nowrap;
							margin-right: 6px;
						}
						.el-rate__icon {
							font-size: 13px !important;
							margin-right: 3px;
							white-space: nowrap;
						}
					}
					.floor2 {
						.describe {
							padding: 0 6px 0 0;
						}
					}
				}
			}
		}
	}
}
.title {
	display: flex;
	align-items: center;
	font-size: 16px;
	font-weight: 550;
	&-img {
		width: 22px;
		height: 22px;
		margin-right: 8px;
	}
}
</style>
