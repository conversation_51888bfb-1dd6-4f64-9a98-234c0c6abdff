<template>
	<div class="content">
		<es-data-table
			ref="table"
			:url="dataTableUrl"
			:param="params"
			:full="true"
			:fit="true"
			:page="pageOption"
			:thead="thead"
			:toolbar="toolbar"
			:border="true"
			:numbers="true"
			close
			@btnClick="btnClick"
			@search="hadeSearch"
			@reset="hadeReset"
		></es-data-table>
	</div>
</template>

<script>
import interfaceUrl from '@/http/platform/student.js';
export default {
	data() {
		return {
			dataTableUrl: interfaceUrl.queryStudentList,
			thead: [
				{
					title: '姓名',
					align: 'left',
					field: 'studentName',
					width: 120
				},
				{
					title: '学号',
					align: 'left',
					field: 'studentNo'
				},
				{
					title: '院系',
					align: 'left',
					field: 'orgName'
				},
				{
					title: '年级',
					align: 'left',
					field: 'yearCode',
					width: 70
				},
				{
					title: '专业',
					align: 'left',
					field: 'majorName'
				},
				{
					title: '身份证号',
					align: 'left',
					field: 'idCardNo'
				},
				{
					title: '手机号码',
					align: 'left',
					field: 'telNo'
				},
				{
					title: '银行卡号',
					align: 'left',
					field: 'bankCardNumber'
				},
				{
					title: '开户行',
					align: 'left',
					field: 'bankInformation'
				}
			],
			pageOption: {
				layout: 'total, prev, pager, next, jumper',
				pageSize: 20,
				// hideOnSinglePage: true,
				position: 'right',
				current: 1,
				pageNum: 1
			},
			params: {},
			collegeList: [],
			submitFilterParams: {}
		};
	},
	computed: {
		toolbar() {
			return [
				{
					type: 'button',
					contents: [
						{
							text: '导出',
							code: 'export',
							type: 'primary'
						}
					]
				},
				{
					type: 'search',
					reset: true,
					contents: [
						{
							label: '姓名',
							placeholder: '请输入姓名',
							name: 'studentName',
							clearable: true,
							col: 3
						},
						{
							name: 'studentNo',
							placeholder: '学号',
							label: '学号',
							col: 3,
							clearable: true
						},
						{
							type: 'select',
							col: 3,
							label: '院系',
							placeholder: '请选择院系',
							name: 'orgCode',
							filterable: true,
							'value-key': 'value',
							'label-key': 'label',
							clearable: true,
							data: this.collegeList
						},
						{
							label: '年级',
							placeholder: '请输入年级',
							name: 'yearCode',
							clearable: true,
							col: 3
						},
						{
							label: '身份证号',
							placeholder: '请输入身份证号',
							name: 'idCardNo',
							clearable: true,
							col: 3
						},
						{
							label: '手机号码',
							placeholder: '请输入手机号码',
							name: 'telNo',
							clearable: true,
							col: 3
						}
					]
				}
			];
		}
	},
	mounted() {
		this.getSelectList();
	},
	methods: {
		getSelectList() {
			this.$request({
				url: interfaceUrl.platmajorGetCollegeSelectList,
				method: 'POST'
			}).then(result => {
				this.collegeList = result?.results || [];
			});
		},
		/**
		 * 操作按钮点击事件
		 * @param {*} res
		 */
		btnClick(res) {
			let code = res.handle.code;
			switch (code) {
				case 'export':
					this.exportFile();
					break;
				default:
					break;
			}
		},
		//导出函数
		exportFile() {
			let isDev = process.env.NODE_ENV === 'development' ? '/api' : '';
			const paramAll = { ...this.submitFilterParams };
			const paramStr = this.objToUrlParams(paramAll);
			let url = `${isDev}${interfaceUrl.exportStudent}${paramStr ? '?' + paramStr : ''}`;
			window.open(url);
		},
		objToUrlParams(obj) {
			return Object.keys(obj)
				.filter(key => obj[key] !== '' && obj[key] !== null)
				.map(key => encodeURIComponent(key) + '=' + encodeURIComponent(obj[key]))
				.join('&');
		},
		hadeSearch(e) {
			this.submitFilterParams = e;
		},
		hadeReset() {
			this.submitFilterParams = {};
		}
	}
};
</script>
<style scoped lang="scss">
@import '../../../assets/style/style.scss';

.content {
	width: 100%;
	height: 100%;
}
</style>
