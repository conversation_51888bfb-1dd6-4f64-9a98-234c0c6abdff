<template>
	<div class="content" style="height: 100%">
		<div style="width: 100%; height: 100%">
			<es-data-table
				v-if="true"
				ref="table"
				:key="tableCount"
				:row-style="tableRowClassName"
				:full="true"
				:fit="true"
				:thead="thead"
				:toolbar="toolbar"
				:page="page"
				:url="dataTableUrl"
				:param="dataTableParam"
				:border="true"
				:numbers="true"
				checkbox
				form
				@btnClick="btnClick"
				@selection-change="handleSelectionChange"
				@edit="changeTable"
			></es-data-table>
			<es-dialog
				v-if="showInfoPage"
				:title="formTitle"
				:drag="true"
				:close-on-click-modal="false"
				:visible.sync="showInfoPage"
				width="80%"
				append-to-body
			>
				<infoPage
					ref="infoPage"
					:base-data="formData"
					:info-page-mode="infoPageMode"
					@activelyClose="closeInfoPage"
				></infoPage>
			</es-dialog>
		</div>
	</div>
</template>

<script>
import api from '@/http/society/societyBaseInfo/api';
import InfoPage from '@/views/society/societyBaseInfo/infoPage.vue';
import SnowflakeId from 'snowflake-id';

export default {
	components: { InfoPage },
	data() {
		return {
			formData: {},
			page: {
				pageSize: 20,
				totalCount: 0
			},
			infoPageMode: 'allOn',
			selectRowData: [],
			selectRowIds: [],
			showInfoPage: false,
			tableCount: 1,
			dialogType: '',
			dataTableUrl: api.societyBaseInfoListJson,
			dataTableParam: { orderBy: 'create_time', asc: false, auditStatus: '2' },
			formTitle: '',
			validityOfDateDisable: false,
			thead: [],
			toolbar: [],
			editToolbar: [
				{
					type: 'button',
					contents: [
						{
							text: '新增',
							code: 'toolbar'
						},
						// {
						// 	text: '查看',
						// 	code: 'toolbar',
						// 	type: 'primary'
						// },
						{
							text: '删除',
							code: 'toolbar',
							type: 'danger'
						},
						{
							text: '导入',
							code: 'toolbar'
						}
					]
				},
				{
					type: 'search',
					reset: true,
					contents: [
						{
							type: 'text',
							name: 'keyword',
							placeholder: '关键字查询',
							clearable: true
						},
						{
							type: 'select',
							name: 'status',
							placeholder: '社团状态',
							clearable: true,
							data: [
								{
									value: 1,
									label: '已成立'
								},
								{
									value: 2,
									label: '已解散'
								}
							]
						}
					]
				}
			],
			listThead: [
				{
					title: '社团名称',
					align: 'left',
					field: 'name',
					sortable: 'custom',
					showOverflowTooltip: true
				},
				{
					title: '申请时间',
					width: '150px',
					align: 'center',
					field: 'createTime',
					sortable: 'custom',
					showOverflowTooltip: true
				},
				{
					title: '社长',
					width: '150px',
					align: 'center',
					field: 'principalName',
					sortable: 'custom',
					showOverflowTooltip: true
				},
				{
					title: '社团状态',
					width: '150px',
					align: 'center',
					field: 'statusVO',
					sortable: 'custom',
					showOverflowTooltip: true,
					render: (h, param) => {
						return h(
							'el-tag',
							{ props: { type: param.row.status === 1 ? '' : 'danger' } },
							param.row.statusVO
						);
					}
				}
			],
			btnJson: {
				title: '操作',
				type: 'handle',
				width: 180,
				template: '',
				events: [
					{
						code: 'row',
						text: '查看'
					},
					{
						code: 'row',
						text: '编辑'
					},
					{
						code: 'row',
						text: '删除'
					}
				]
			}
		};
	},
	created() {
		//初始化查询待审核列表
		this.thead = this.getListThead(this.btnJson);
		this.toolbar = this.editToolbar;
	},
	methods: {
		/**
		 * 表格行高
		 */
		tableRowClassName() {
			return {
				height: '54px !important'
			};
		},
		btnClick(res) {
			let text = res.handle.text;
			let code = res.handle.code;

			if (code === 'row') {
				switch (text) {
					case '查看':
					case '编辑':
						this.openInfoPage(res.row.id, text);
						break;
					case '删除':
						this.deleteRows([res.row.id]);
						break;
				}
			} else {
				switch (text) {
					case '新增':
						this.openInfoPage(null, text);
						break;
					case '查看':
						if (this.selectRowIds.length > 1) {
							this.$message.warning('只能选择一个查看');
						} else if (this.selectRowIds.length < 1) {
							this.$message.warning('请选择一个进行查看');
						} else {
							this.openInfoPage(this.selectRowIds[0], text);
						}
						break;
					case '删除':
						if (this.selectRowIds.length < 1) {
							this.$message.warning('请选择一个删除');
						} else {
							this.deleteRows(this.selectRowIds);
						}
						break;
				}
			}
		},
		//打开infoPage
		openInfoPage(id, pageMode) {
			if (pageMode !== '新增') {
				this.$request({
					url: api.societyBaseInfoInfo + '/' + id,
					method: 'GET'
				}).then(res => {
					//处理请求数据
					this.formData = { ...res.results };
					this.formData.college = {
						value: this.formData.collegeId,
						label: this.formData.collegeName
					};
					this.formData.major = { value: this.formData.major, label: this.formData.majorName };
					// this.formData.teacherId = {value: this.formData.teacherId, label: this.formData.teacherName};
					this.formData.teacherId = this.formData.teacherId?.split(',');
					this.formData.typeId = { value: this.formData.typeId, label: this.formData.typeName };
					this.formData.principal = {
						value: this.formData.principal,
						label: this.formData.principalName
					};

					this.formTitle = pageMode;
					this.infoPageMode = pageMode;
					this.showInfoPage = true;
				});
			} else {
				this.formTitle = pageMode;
				const snowflake = new SnowflakeId();
				this.formData = { id: snowflake.generate() };
				this.infoPageMode = pageMode;
				this.showInfoPage = true;
			}
		},
		//关闭infoPage
		closeInfoPage(reload) {
			this.formData = {};
			this.showInfoPage = false;
			if (reload) this.$refs.table.reload();
		},
		// 数据列表多选回调
		handleSelectionChange(data) {
			let ids = [];
			data.forEach(row => {
				ids.push(row.id);
			});
			this.selectRowData = data;
			this.selectRowIds = ids;
		},
		deleteRows(ids) {
			this.$confirm('确定要删除选中的数据吗？', '删除', {
				confirmButtonText: '确定',
				cancelButtonText: '取消',
				type: 'warning'
			})
				.then(() => {
					this.$request({
						url: api.societyBaseInfoDeleteIds,
						data: { ids: ids.join(',') },
						method: 'POST'
					}).then(res => {
						if (res.rCode === 0) {
							this.$message.success('操作完成');
							this.$refs.table.reload();
						} else {
							this.$message.error(res.msg);
						}
					});
				})
				.catch(() => {});
		},
		getListThead(btnJson) {
			let tempThead = [...this.listThead];
			tempThead.push(btnJson);
			return tempThead;
		},
		changeTable(val) {
			switch (val.name) {
				case 'status':
					break;
			}
		}
	}
};
</script>
