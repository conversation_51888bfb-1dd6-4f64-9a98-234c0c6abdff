<template>
	<!-- 在list.js修改component.name值可改变展示组件 -->
	<component
		:is="component.name"
		ref="dataTable"
		v-bind="table"
		:table="table"
		@selection-change="handleSelectionChange"
		@btnClick="btnClick"
		@success="successAfter"
    @edit="changeTable"
    form
	>
		<es-dialog
			v-if="showForm"
			:title="formTitle"
			:visible.sync="showForm"
			:close-on-click-modal="false"
			:middle="true"
			height="750px"
			class="dialog-form"
		>
			<es-form
				ref="form"
				:model="formData"
				:contents="formItemList"
				:genre="2"
				height="600px"
				collapse

				@change="handleFormItemChange"
				@submit="handleFormSubmit"
        @click="handleFormAudit"
				@reset="showForm = false"
			/>
		</es-dialog>
		<es-dialog
			v-if="showView"
			title="详情"
			:visible.sync="showView"
			:close-on-click-modal="false"
			:middle="true"
			height="600px"
			class="dialog-form"
		>
			<es-form
				ref="form"
				:model="viewData"
				:contents="viewItemList"
				:genre="2"
				height="500px"
				collapse
				@reset="showView = false"
			/>
		</es-dialog>
	</component>
</template>

<script>
import httpApi from '@/http/job/jobCoFinancialServe/api.js';
import list from '@/http/job/jobCoFinancialServe/list.js';
import edit from '@/http/job/jobCoFinancialServe/edit.js';
import view from '@/http/job/jobCoFinancialServe/view.js';
export default {
	name: 'JobCoFinancialServe',
	mixins: [list, edit, view],
	data() {
		return {
			selectRowData: [],
			selectRowIds: [],
			formTitle: '',
			showForm: false,
			showView: false,
			formData: {},
			viewData: {},
			extraData: {}
		};
	},
	watch: {
		showForm(val) {
			if (!val) {
				this.formData = {};
			}else {
        this.selectData();
      }
		}
	},
	methods: {
		// 初始化扩展数据，字典等
		successAfter(data) {
			this.extraData = data.results.extraData;
		},
		// 数据列表多选回调
		handleSelectionChange(data) {
			let ids = [];
			data.forEach(row => {
				ids.push(row.id);
			});
			this.selectRowData = data;
			this.selectRowIds = ids;
		},
		// 表单变更时, 回调处理
		handleFormItemChange(filed, data) {
		    // 处理表单字段systemCode的变更
			// if (filed == 'systemCode') {
			//	this.formData.systemCode = data.id;
			//	this.formData.systemName = data.name;
			// }
		},
    btnClick(res) {
      let text = res.handle.text;
      let code = res.handle.code;

      if(code === 'row'){
        switch (text) {
          case '查看': this.viewPageLoad(res.row.id); break;
          case '编辑': this.editPageLoad(res.row.id); break;
          case '删除': this.deleteId([res.row.id]); break;
          case '审核': this.auditPageLoad(res.row.id); break;
          default: break;
        }
      }else {
        switch (text) {
          case '新增':
            this.addPageLoad();
            break;
          case '查看':
            if(this.selectRowIds.length>1){
              this.$message.warning('只能选择一个查看');
            }else if(this.selectRowIds.length<1){
              this.$message.warning('请选择一个进行查看');
            }else {
              this.viewPageLoad(this.selectRowIds[0]);
            }
            break;
          case '删除': this.deleteId(this.selectRowIds); break;
          default: break;
        }
      }
    },
    addPageLoad(){
      this.formTitle = '新增';
      this.formData = {
        // useStatus: 1,
      };
      this.editPageMode = 'add';
      this.showForm = true;
    },
    viewPageLoad(id){
      this.$request({
        url: httpApi.jobCoFinancialServeInfo,
        params: { id: id }
      }).then(res => {
        // 处理自定义的数据格式化, extFiled 额外属性名， key、val 数据映射
        // this.viewData = this.formatExtraData(res.results[*], 'extFiled', 'key', 'val');
        this.viewData = res.results;
        this.editPageMode = 'view'
        // 打开查看弹窗
        this.showView = true;
      });
    },
    editPageLoad(id){
      this.formTitle = '编辑';
      this.$request({
        url: httpApi.jobCoFinancialServeInfo,
        params: { id: id }
      }).then(res => {
        // 处理自定义的数据格式化, extFiled 额外属性名， key、val 数据映射
        // this.formData = this.formatExtraData(res.results[*], 'extFiled', 'key', 'val');
        this.formData = res.results
        this.editPageMode = 'edit';
        this.showForm = true;
      });
    },
    auditPageLoad(id){
      this.formTitle = '审核';
      this.$request({
        url: httpApi.jobCoFinancialServeInfo,
        params: { id: id }
      }).then(res => {
        // 处理自定义的数据格式化, extFiled 额外属性名， key、val 数据映射
        // this.formData = this.formatExtraData(res.results[*], 'extFiled', 'key', 'val');
        this.formData = res.results
        this.editPageMode = 'audit';
        this.showForm = true;
      });
    },
		handleFormSubmit() {
			let formData = { ...this.formData };
			// 可额外处理formData中的数据
      if(typeof formData.orgIdVO != 'object')
        formData.orgId = formData.orgIdVO;
      else formData.orgId = formData.orgIdVO.value;
      Reflect.deleteProperty(formData,'orgIdVO');

			this.$request({
				url:
					this.formTitle === '新增'
						? httpApi.jobCoFinancialServeSave
						: httpApi.jobCoFinancialServeUpdate,
				data: formData,
				method: 'POST'
			}).then(res => {
				if (res.rCode === 0) {
					this.$message.success('操作成功');
					this.showForm = false;
					this.formData = {};
					this.$refs.dataTable.reload();
				} else {
					this.$message.error(res.msg);
				}
			});
		},
    handleFormAudit(res) {
      //校验
      this.$refs['form'].validate((valid) => {  //开启校验
        if (valid) {   // 如果校验通过，请求接口
          let id = this.formData.id;
          let btnType = res.text;
          let auditStatus = -1;
          let auditOpinion = this.formData.auditOpinion;

          this.$confirm('是否确认'+btnType+'？', '审核', {
            confirmButtonText: '确定',
            cancelButtonText: '取消',
            type: 'warning'
          })
              .then(() => {
                switch (btnType){
                  case '审核通过': auditStatus = 1;break;
                  case '驳回': auditStatus = 2;break;
                }
                if(auditStatus !== -1){
                  this.$request({
                    url: httpApi.jobCoFinancialServeAudit,
                    data:{id: id,auditStatus: auditStatus, auditOpinion: auditOpinion},
                    method: 'POST'
                  }).then(res =>{
                    if(res.success){
                      this.$message.success('审核成功');
                      this.showForm = false;
                      this.formData = {};
                      this.$refs.dataTable.reload();
                    }else {
                      this.$message.error(res.msg);
                    }
                  })
                }
              }).catch(() => {});
        } else { return false; }//校验不通过
      });
    },
    deleteId(ids) {
      this.$confirm('确定要删除选中的数据吗？', '删除', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      })
          .then(() => {
            this.$request({
              url: httpApi.jobCoFinancialServeDeleteByIds,
              data: { ids: ids.join(',') },
              method: 'POST'
            }).then(res => {
              if (res.rCode === 0) {
                this.$message.success('操作完成');
                this.$refs.dataTable.reload();
              } else {
                this.$message.error(res.msg);
              }
            });
          })
          .catch(() => {});
    },
		// 根据列表额外数据返回的 字典信息，格式化数据
		formatExtraData(data, extFiled, key, val) {
			let text = '未知';
			this.extraData[extFiled].forEach(dict => {
				if (dict[key] === data) {
					text = dict[val];
				}
			});
			return text;
		}
	}
};
</script>
