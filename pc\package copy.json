{
  "name": "project-ybgz",
  "description": "",
  "version": "0.2.24",
  "private": true,
  "scripts": {
    "build": "npm run build:release",
    "serve": "npm run dev:development",
    "dev:development": "cross-env vue-cli-service serve --mode development",
    "build:release": "cross-env vue-cli-service build  --mode release",
    "build:production": "cross-env vue-cli-service build  --mode production",
    "build:analyze": "vue-cli-service build --mode analyze",
    "lint": "vue-cli-service lint"
  },
  "dependencies": {
    "@teckel/vue-pdf": "^4.3.5",
    "@wangeditor/editor": "^5.1.23",
    "@wangeditor/editor-for-vue": "^1.0.2",
    "axios": "^0.27.2",
    "babel-polyfill": "^6.26.0",
    "classlist-polyfill": "^1.2.0",
    "core-js": "^3.8.3",
    "decimal.js": "^10.4.3",
    "echarts": "^5.4.1",
    // 9/22 删除
    // "element-eoss": "^2.14.3",
    // "eoss-element": "^0.2.93",
    // "eoss-ui": "^0.5.62",
    // "eoss-element": "^0.2.90",
    // "eoss-ui": "^0.5.59",
    // "eoss-element": "^0.2.96",
    // "eoss-ui": "^0.5.66",
    // 9/19列表有问题
    // "eoss-element": "^0.3.5",
    // "eoss-ui": "^0.5.81-beta",
    // 9/21安全版本
    // "eoss-element": "^0.3.6",
    // "eoss-ui": "^0.5.81-beta2",
    // 2025/1/15 更新（流程发起不了）
    // "eoss-element": "^0.3.30",
    // "eoss-ui": "^0.6.12",
    // 2025/2/19 更新(内部打开页面一直登录)
    // "eoss-element": "^0.3.30",
		// "eoss-ui": "^0.6.21",
    // 2025/3/17 更新()
    "eoss-element": "^0.3.30",
		"eoss-ui": "^0.6.30",
		"gojs": "^2.2.12",
    "js-base64": "^3.7.5",
    "js-md5": "^0.7.3",
    "lodash": "^4.17.21",
    "qs": "^6.11.0",
    // 9/22 删除
    // "scwl-component": "git+http://git.wisesoft.net.cn/western_logistics_fe/eoss_business_component.git#development",
    "snowflake-id": "^1.1.0",
    "sockjs-client": "^1.6.1",
    "stompjs": "^2.3.3",
    "uuid": "^9.0.0",
    "v-calendar": "^2.4.2",
    "vue": "^2.6.14",
    "vue-baidu-map": "^0.21.22",
    "vue-quill-editor": "^3.0.6",
    "vue-router": "^3.5.1",
    "vuex": "^3.6.2",
    "wujie-vue2": "^1.0.0-rc.25"
  },
  "devDependencies": {
    "@babel/core": "^7.12.16",
    "@babel/eslint-parser": "^7.12.16",
    "@vue/cli-plugin-babel": "~5.0.0",
    "@vue/cli-plugin-eslint": "~5.0.0",
    "@vue/cli-plugin-router": "~5.0.0",
    "@vue/cli-plugin-vuex": "~5.0.0",
    "@vue/cli-service": "~5.0.0",
    "babel-eslint": "^10.1.0",
    "cross-env": "^7.0.3",
    "eslint": "^7.32.0",
    "eslint-config-prettier": "^8.5.0",
    "eslint-plugin-prettier": "^4.0.0",
    "eslint-plugin-vue": "^8.0.3",
    "git-revision-webpack-plugin": "^3.0.6",
    "sass": "^1.32.7",
    "sass-loader": "^12.0.0",
    "vue-template-compiler": "^2.6.14",
    "webpack-bundle-analyzer": "^4.8.0"
  }
}