import httpApi from '@/http/logistics/disciplineInspectionReach/api.js';
export default {
	computed: {
		component() {
			return {
				// 树表结构
				//name: 'es-tree-group'
				// 纯列表结构
				name: 'es-data-table'
			};
		},
		treeParam() {
			return {
				// 不需要左边树 可以
				//url: httpApi.platDisciplineInspectionReachTree,
				//title: '分类树'
			};
		},
		table() {
			return {
				url: httpApi.platDisciplineInspectionReachList,
				param: { handlerType: '' },
				full: true,
				fit: true,
				page: true,
				stripe: true,
				numbers: true,
				'row-key': 'id',
				toolbar: [
					// {
					// 	type: 'button',
					// 	contents: [
					// 		{
					// 			text: '新增',
					// 			type: 'primary'
					// 		}
					// 	]
					// },
					{
						type: 'search',
						reset: true,
						contents: [
							//{
							//	name: 'accountName_like',
							//	placeholder: '配置名称',
							//	label: '配置名称',
							//	col: 6
							//},
							{
								col: 6,
								name: 'title',
								label: '标题',
								placeholder: '请输入标题'
							}
						]
					}
				],
				thead: [
					{
						title: '标题',
						align: 'left',
						showOverflowTooltip: true,
						field: 'title'
					},
					{
						title: '是否匿名',
						align: 'center',

						width: 90,
						field: 'anonymity'
					},
					{
						title: '举报人姓名',
						align: 'center',
						minWidth: 100,
						field: 'reporterName'
					},
					// {
					// 	title: '举报人联系方式',
					// 	align: 'left',
					// 	showOverflowTooltip: true,
					// 	field: 'reporterPhone'
					// },
					{
						title: '被举报人姓名',
						minWidth: 100,
						align: 'center',
						field: 'beReporterName'
					},
					{
						title: '被举报人单位',
						align: 'center',
						showOverflowTooltip: true,
						field: 'beReporterDepartment'
					},
					{
						title: '被举报人职务',
						align: 'center',
						showOverflowTooltip: true,
						field: 'beReporterJob'
					},
					{
						title: '操作',
						type: 'handle',
						width: 180,
						events: [
							{
								text: '查看'
							}
							// ,
							// {
							// 	text: '编辑'
							// }
							// ,
							// {
							// 	text: '删除'
							// }
						]
					}
				]
			};
		}
	}
};
