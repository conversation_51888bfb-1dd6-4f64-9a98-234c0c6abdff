<!--
 @desc:变更历史
 -->
<template>
	<div class="main">
		<div v-if="list.length > 0" class="card-list">
			<el-descriptions
				v-for="(item, k) of list"
				:key="k"
				class="card-list-item"
				:title="`第${k + 1}次变更`"
			>
				<template slot="extra">
					<el-button type="text" icon="el-icon-s-promotion" @click="onInfo(item)">
						查看变更详情
					</el-button>
				</template>
				<el-descriptions-item label="申请人">{{ item.createUserName }}</el-descriptions-item>
				<el-descriptions-item label="变更时间">{{ item.createTime }}</el-descriptions-item>
				<el-descriptions-item label="状态">
					<el-tag size="small" :type="projecstate('9')">{{ item.auditStateTxt }}</el-tag>
				</el-descriptions-item>
				<el-descriptions-item label="是否终止">{{ item.terminateStateTxt }}</el-descriptions-item>

				<el-descriptions-item span="3" label="变更事由">
					{{ item.changeReason }}
				</el-descriptions-item>
				<el-descriptions-item label="变更内容">
					{{ item.changeContent }}
				</el-descriptions-item>
			</el-descriptions>
		</div>
		<el-empty v-else description="无变更记录"></el-empty>
		<!-- 历史变更详情 -->
		<es-dialog
			title="查看变更详情"
			:visible.sync="visibleBasicInfo"
			:show-scale="true"
			size="lg"
			width="90%"
			height="90%"
		>
			<BasicInfo
				v-if="visibleBasicInfo"
				:id="formId"
				:contents-key="contentsKey"
				:is-flow-pattern="false"
				:form-data-n="formDataN"
				title="查看变更详情"
				@visible="
					e => {
						visibleBasicInfo = e;
						$refs.refTable.reload();
					}
				"
			/>
		</es-dialog>
	</div>
</template>

<script>
import BasicInfo from '@/views/scientific-sys/components/project-info/basic-info.vue';

export default {
	components: {
		BasicInfo
	},
	inject: ['id', 'openType'],
	props: {
		list: {
			type: Array,
			default: () => []
		}
	},
	data() {
		return {
			formDataN: {},
			formId: '',
			param: {
				type: 0 //0待审核 1已审核
			},
			changeList: [{}],
			visibleBasicInfo: false
		};
	},
	computed: {
		contentsKey() {
			// LENTHWAYS("lengthWays", "纵向项目",0),
			// CROSSWISE("crosswise", "横向项目",1),
			// COLLEGE("college", "院级项目",2),
			const projectClassify = Number(this.projectClassify || 1);
			let contentsKey = '';
			switch (projectClassify) {
				case 0:
					contentsKey = 'verticalProject'; // 纵向项目
					break;
				case 1:
					contentsKey = 'crosswiseProject'; // 横向项目
					break;
				case 2:
					contentsKey = 'instituteProject'; // 院级项目
					break;
			}
			return contentsKey;
		}
	},
	created() {
		// this.getInfo();
	},
	methods: {
		onInfo(e) {
			this.formDataN = JSON.parse(e.changeProjectInfo);
			this.formId = e.id;
			this.visibleBasicInfo = true;
		},
		// 计算状态颜色
		projecstate(state) {
			let stateCurrent = '';
			// 项目状态（字典编码：project_status）0：待立项； 1：已立项； 2：立项不通过； 3：变更审核中； 4：结题审核中； 5：已结题； 6：已归档； 7：终止；
			switch (state) {
				case '0':
					stateCurrent = 'warning';
					break;
				case '3':
				case '4':
					stateCurrent = 'primary';
					break;
				case '1':
				case '5':
				case '6':
					stateCurrent = 'success';
					break;
				// case '2':
				// 	stateCurrent = 'info';
				// 	break;
				case '2':
				case '7':
					stateCurrent = 'danger';
					break;
				default:
					break;
			}
			return stateCurrent;
		}
		// async getInfo() {
		// 	const loading = this.$.loading(this.$loading, '加载中...');
		// 	try {
		// 		let { rCode, msg, results } = await this.$.ajax({
		// 			url: getChangeInfoByid,
		// 			method: 'get',
		// 			params: { projectId: this.id }
		// 		});
		// 		if (rCode == 0) {
		// 			Array.isArray(results) && results.reverse;
		// 			this.changeList = results || [];
		// 		} else {
		// 			this.$message.error(msg);
		// 		}
		// 	} catch (error) {
		// 		console.log('>>>error', error);
		// 	} finally {
		// 		loading.close();
		// 	}
		// }
	}
};
</script>

<style lang="scss" scoped>
@import '@ast/style/public.scss';
.main {
	height: 100%;
	overflow: auto;
	padding: 8px 8px;
	.change-info {
		width: 100%;
		// height: calc(100% - 120px);
		overflow: auto;
		border: 1px solid #d8d8d8;
		border-radius: 10px;
		ul {
			margin-bottom: 16px;
			li {
				@include flexBox(flex-start);
				width: 100%;
				border: 1px solid #d0d0d0;
				border-bottom: none;
				&:nth-last-child(1) {
					border-bottom: 1px solid #d0d0d0;
				}
				.title {
					width: 200px;
					height: 100%;
					padding: 10px 16px;
					color: #747474;
					font-weight: 550;
					text-align: right;
					background: #f5f5f5;
					border-right: 1px solid #d0d0d0;
				}
				.value {
					height: 100%;
					flex: 1;
					padding: 10px 16px;
					color: #747474;
				}
				.t-head {
					width: 100%;
					text-align: center;
				}
			}
			.contetn {
				height: 100px;
			}
		}
		.no-data {
			text-align: center;
			padding: 20px;
		}
	}
	.card-list {
		display: flex;
		flex-wrap: wrap;
		align-content: start;
		height: 100%;
		// padding: 20px 0 0 4%;
		padding: 15px;
		overflow: auto;
		.card-list-item {
			flex-basis: 48.2%;
			margin: 10px;
			margin-bottom: 10px;
			padding: 20px;
			position: relative;
			background: #fff;
			border-radius: 5px;
			overflow: hidden;
			box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
			top: 0px;
			animation: scale-in-center 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94) both;
			transition: all 0.3s ease;
			&:hover {
				top: -5px;
				box-shadow: 0 10px 20px rgba(0, 0, 0, 0.15);
				.content {
					.content-text {
						&:first-child {
							color: #409eff;
						}
					}
				}
			}
		}
		@keyframes scale-in-center {
			0% {
				-webkit-transform: scale(0.7);
				transform: scale(0.7);
				opacity: 1;
			}
			100% {
				-webkit-transform: scale(1);
				transform: scale(1);
				opacity: 1;
			}
		}
	}
}
</style>
