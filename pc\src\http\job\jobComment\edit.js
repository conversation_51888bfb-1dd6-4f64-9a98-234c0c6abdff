export default {
	computed: {
		formItemList() {
			return [
				{
					name: 'type',
					label: '类别',
					value: '',
					col: 12,
					rules: {
						required: true,
						message: '请输入类别'
					}
				},
				{
					name: 'objId',
					label: '文章编号',
					value: '',
					col: 12,
					rules: {
						required: true,
						message: '请输入文章编号'
					}
				},
				{
					name: 'content',
					label: '内容',
					value: '',
					col: 12,
					rules: {
						required: true,
						message: '请输入内容'
					}
				},
				{
					name: 'status',
					label: '状态(0.停用、1.启用)',
					value: '',
					col: 12,
					rules: {
						required: true,
						message: '请输入状态(0.停用、1.启用)'
					}
				},
				{
					name: 'auditStatus',
					label: '审核状态(0.待审核、1.审核通过、2.驳回)',
					value: '',
					col: 12,
					rules: {
						required: true,
						message: '请输入审核状态(0.待审核、1.审核通过、2.驳回)'
					}
				},
				{
					name: 'auditOpinion',
					label: '审核意见',
					value: '',
					col: 12,
					rules: {
						required: true,
						message: '请输入审核意见'
					}
				},
				{
					name: 'createUser',
					label: '创建人编号',
					value: '',
					col: 12,
					rules: {
						required: true,
						message: '请输入创建人编号'
					}
				},
			]
		}
	}
};
