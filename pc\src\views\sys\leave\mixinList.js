export const mixinList = {
	computed: {
		// 动态列表配置、接口、数据
		wd() {
			return {
				// 基本配置
				basics: {
					dataTableUrl: '/ybzy/attendanceLeave/list', // 列表接口
					info: '/ybzy/attendanceLeave/info', // 详情接口
					save: '/ybzy/attendanceLeave/save', // 新增
					edit: '/ybzy/attendanceLeave/update', // 修改
					delete: '/ybzy/attendanceLeave/deleteById', // 删除 removeById逻辑删除 deleteById真删
					revoke: '/ybzy/attendanceLeave/recallFlow', // 撤销
					flowTypeCode: 'egress_apply', // 流程code  因公外出 type=2
					flowTypeCode2: 'leave_manager', // 流程code 因私请假 type=1
					defaultProcessKey: 'leave_process' // 默认关联流程 key
				},
				// 筛选配置
				toolbar: [
					{
						type: 'button',
						contents: [
							{
								text: '因私请假',
								code: 'add',
								icon: 'es-icon-xinzeng',
								type: 'primary'
							},
							{
								text: '因公请假',
								code: 'add',
								icon: 'es-icon-xinzeng',
								type: 'primary'
							}
						]
					},
					{
						type: 'search',
						contents: [
							{
								label: '申请人',
								name: 'keword',
								placeholder: '请输入申请人名字',
								col: 3
							}
						]
					}
					// {
					// 	type: 'filter',
					// 	contents: [
					// 		{
					// 			type: 'text',
					// 			label: '部门名称',
					// 			name: 'deptName',
					// 			placeholder: '请输入部门名称',
					// 			col: 3
					// 		}
					// 	]
					// }
				],
				// 列表配置
				thead: [
					{
						title: '姓名',
						align: 'center',
						field: 'name',
						minWidth: '80px',
						showOverflowTooltip: true
					},
					{
						title: '部门',
						align: 'center',
						field: 'deptName',
						showOverflowTooltip: true
					},
					{
						title: '请假类型',
						align: 'center',
						field: 'leaveTypeTxt',
						// showOverflowTooltip: true,
						render: (h, params) => {
							let leaveTypeTxt = params.row.leaveTypeTxt;
							return h('span', {}, leaveTypeTxt ? leaveTypeTxt : '因公外出');
						},
						minWidth: '80px'
					},
					// {
					// 	title: '外出地点',
					// 	align: 'center',
					// 	field: 'address',
					// 	showOverflowTooltip: true,
					// 	minWidth: '80px'
					// },
					{
						title: '创建时间',
						align: 'center',
						field: 'createTime',
						width: '160px',
						showOverflowTooltip: true
					},
					{
						title: '离开时间',
						align: 'center',
						field: 'startDateTime',
						width: '160px',
						showOverflowTooltip: true
					},
					{
						title: '返回时间',
						align: 'center',
						field: 'endDateTime',
						width: '160px',
						showOverflowTooltip: true
					},
					{
						title: '请假天数',
						align: 'center',
						field: 'days',
						showOverflowTooltip: true,
						minWidth: '80px'
					},
					{
						title: '状态',
						align: 'center',
						field: 'statusTxt',
						fixed: 'right',
						width: '90px',
						render: (h, params) => {
							let statusTxt = params.row.statusTxt;
							let status = params.row.status;
							return h(
								'el-tag',
								{
									props: {
										size: 'mini',
										// 0-草稿 1-已提交 11-审核中  2-审核通过  9-驳回
										type:
											status === '0'
												? 'warning'
												: status === '1'
												? 'primary'
												: status === '2'
												? 'success'
												: 'danger'
									}
								},
								statusTxt
							);
						}
					},
					{
						title: '操作',
						type: 'handle',
						width: 180,
						fixed: 'right',
						template: '',
						events: [
							// 0-草稿  1-审核中  2-审核通过  9-驳回
							{
								code: 'look',
								icon: 'es-icon-chakan',
								text: '查看'
							},
							{
								code: 'edit',
								text: '编辑',
								icon: 'es-icon-bianji',
								rules: rows => rows.status === '0'
							},
							{
								code: 'back',
								text: '撤回',
								icon: 'es-icon-go-back',
								rules: rows => rows.status === '1'
							},
							{
								code: 'delect',
								text: '删除',
								icon: 'es-icon-shanchu',
								rules: rows => rows.status === '0'
							}
						]
					}
				]
			};
		}
	}
};
