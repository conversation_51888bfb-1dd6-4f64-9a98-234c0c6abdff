{"ast": null, "code": "function _toConsumableArray(r) { return _arrayWithoutHoles(r) || _iterableToArray(r) || _unsupportedIterableToArray(r) || _nonIterableSpread(); }\nfunction _nonIterableSpread() { throw new TypeError(\"Invalid attempt to spread non-iterable instance.\\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.\"); }\nfunction _unsupportedIterableToArray(r, a) { if (r) { if (\"string\" == typeof r) return _arrayLikeToArray(r, a); var t = {}.toString.call(r).slice(8, -1); return \"Object\" === t && r.constructor && (t = r.constructor.name), \"Map\" === t || \"Set\" === t ? Array.from(r) : \"Arguments\" === t || /^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(t) ? _arrayLikeToArray(r, a) : void 0; } }\nfunction _iterableToArray(r) { if (\"undefined\" != typeof Symbol && null != r[Symbol.iterator] || null != r[\"@@iterator\"]) return Array.from(r); }\nfunction _arrayWithoutHoles(r) { if (Array.isArray(r)) return _arrayLikeToArray(r); }\nfunction _arrayLikeToArray(r, a) { (null == a || a > r.length) && (a = r.length); for (var e = 0, n = Array(a); e < a; e++) n[e] = r[e]; return n; }\nfunction _regeneratorRuntime() { \"use strict\"; /*! regenerator-runtime -- Copyright (c) 2014-present, Facebook, Inc. -- license (MIT): https://github.com/facebook/regenerator/blob/main/LICENSE */ _regeneratorRuntime = function _regeneratorRuntime() { return e; }; var t, e = {}, r = Object.prototype, n = r.hasOwnProperty, o = Object.defineProperty || function (t, e, r) { t[e] = r.value; }, i = \"function\" == typeof Symbol ? Symbol : {}, a = i.iterator || \"@@iterator\", c = i.asyncIterator || \"@@asyncIterator\", u = i.toStringTag || \"@@toStringTag\"; function define(t, e, r) { return Object.defineProperty(t, e, { value: r, enumerable: !0, configurable: !0, writable: !0 }), t[e]; } try { define({}, \"\"); } catch (t) { define = function define(t, e, r) { return t[e] = r; }; } function wrap(t, e, r, n) { var i = e && e.prototype instanceof Generator ? e : Generator, a = Object.create(i.prototype), c = new Context(n || []); return o(a, \"_invoke\", { value: makeInvokeMethod(t, r, c) }), a; } function tryCatch(t, e, r) { try { return { type: \"normal\", arg: t.call(e, r) }; } catch (t) { return { type: \"throw\", arg: t }; } } e.wrap = wrap; var h = \"suspendedStart\", l = \"suspendedYield\", f = \"executing\", s = \"completed\", y = {}; function Generator() {} function GeneratorFunction() {} function GeneratorFunctionPrototype() {} var p = {}; define(p, a, function () { return this; }); var d = Object.getPrototypeOf, v = d && d(d(values([]))); v && v !== r && n.call(v, a) && (p = v); var g = GeneratorFunctionPrototype.prototype = Generator.prototype = Object.create(p); function defineIteratorMethods(t) { [\"next\", \"throw\", \"return\"].forEach(function (e) { define(t, e, function (t) { return this._invoke(e, t); }); }); } function AsyncIterator(t, e) { function invoke(r, o, i, a) { var c = tryCatch(t[r], t, o); if (\"throw\" !== c.type) { var u = c.arg, h = u.value; return h && \"object\" == _typeof(h) && n.call(h, \"__await\") ? e.resolve(h.__await).then(function (t) { invoke(\"next\", t, i, a); }, function (t) { invoke(\"throw\", t, i, a); }) : e.resolve(h).then(function (t) { u.value = t, i(u); }, function (t) { return invoke(\"throw\", t, i, a); }); } a(c.arg); } var r; o(this, \"_invoke\", { value: function value(t, n) { function callInvokeWithMethodAndArg() { return new e(function (e, r) { invoke(t, n, e, r); }); } return r = r ? r.then(callInvokeWithMethodAndArg, callInvokeWithMethodAndArg) : callInvokeWithMethodAndArg(); } }); } function makeInvokeMethod(e, r, n) { var o = h; return function (i, a) { if (o === f) throw Error(\"Generator is already running\"); if (o === s) { if (\"throw\" === i) throw a; return { value: t, done: !0 }; } for (n.method = i, n.arg = a;;) { var c = n.delegate; if (c) { var u = maybeInvokeDelegate(c, n); if (u) { if (u === y) continue; return u; } } if (\"next\" === n.method) n.sent = n._sent = n.arg;else if (\"throw\" === n.method) { if (o === h) throw o = s, n.arg; n.dispatchException(n.arg); } else \"return\" === n.method && n.abrupt(\"return\", n.arg); o = f; var p = tryCatch(e, r, n); if (\"normal\" === p.type) { if (o = n.done ? s : l, p.arg === y) continue; return { value: p.arg, done: n.done }; } \"throw\" === p.type && (o = s, n.method = \"throw\", n.arg = p.arg); } }; } function maybeInvokeDelegate(e, r) { var n = r.method, o = e.iterator[n]; if (o === t) return r.delegate = null, \"throw\" === n && e.iterator.return && (r.method = \"return\", r.arg = t, maybeInvokeDelegate(e, r), \"throw\" === r.method) || \"return\" !== n && (r.method = \"throw\", r.arg = new TypeError(\"The iterator does not provide a '\" + n + \"' method\")), y; var i = tryCatch(o, e.iterator, r.arg); if (\"throw\" === i.type) return r.method = \"throw\", r.arg = i.arg, r.delegate = null, y; var a = i.arg; return a ? a.done ? (r[e.resultName] = a.value, r.next = e.nextLoc, \"return\" !== r.method && (r.method = \"next\", r.arg = t), r.delegate = null, y) : a : (r.method = \"throw\", r.arg = new TypeError(\"iterator result is not an object\"), r.delegate = null, y); } function pushTryEntry(t) { var e = { tryLoc: t[0] }; 1 in t && (e.catchLoc = t[1]), 2 in t && (e.finallyLoc = t[2], e.afterLoc = t[3]), this.tryEntries.push(e); } function resetTryEntry(t) { var e = t.completion || {}; e.type = \"normal\", delete e.arg, t.completion = e; } function Context(t) { this.tryEntries = [{ tryLoc: \"root\" }], t.forEach(pushTryEntry, this), this.reset(!0); } function values(e) { if (e || \"\" === e) { var r = e[a]; if (r) return r.call(e); if (\"function\" == typeof e.next) return e; if (!isNaN(e.length)) { var o = -1, i = function next() { for (; ++o < e.length;) if (n.call(e, o)) return next.value = e[o], next.done = !1, next; return next.value = t, next.done = !0, next; }; return i.next = i; } } throw new TypeError(_typeof(e) + \" is not iterable\"); } return GeneratorFunction.prototype = GeneratorFunctionPrototype, o(g, \"constructor\", { value: GeneratorFunctionPrototype, configurable: !0 }), o(GeneratorFunctionPrototype, \"constructor\", { value: GeneratorFunction, configurable: !0 }), GeneratorFunction.displayName = define(GeneratorFunctionPrototype, u, \"GeneratorFunction\"), e.isGeneratorFunction = function (t) { var e = \"function\" == typeof t && t.constructor; return !!e && (e === GeneratorFunction || \"GeneratorFunction\" === (e.displayName || e.name)); }, e.mark = function (t) { return Object.setPrototypeOf ? Object.setPrototypeOf(t, GeneratorFunctionPrototype) : (t.__proto__ = GeneratorFunctionPrototype, define(t, u, \"GeneratorFunction\")), t.prototype = Object.create(g), t; }, e.awrap = function (t) { return { __await: t }; }, defineIteratorMethods(AsyncIterator.prototype), define(AsyncIterator.prototype, c, function () { return this; }), e.AsyncIterator = AsyncIterator, e.async = function (t, r, n, o, i) { void 0 === i && (i = Promise); var a = new AsyncIterator(wrap(t, r, n, o), i); return e.isGeneratorFunction(r) ? a : a.next().then(function (t) { return t.done ? t.value : a.next(); }); }, defineIteratorMethods(g), define(g, u, \"Generator\"), define(g, a, function () { return this; }), define(g, \"toString\", function () { return \"[object Generator]\"; }), e.keys = function (t) { var e = Object(t), r = []; for (var n in e) r.push(n); return r.reverse(), function next() { for (; r.length;) { var t = r.pop(); if (t in e) return next.value = t, next.done = !1, next; } return next.done = !0, next; }; }, e.values = values, Context.prototype = { constructor: Context, reset: function reset(e) { if (this.prev = 0, this.next = 0, this.sent = this._sent = t, this.done = !1, this.delegate = null, this.method = \"next\", this.arg = t, this.tryEntries.forEach(resetTryEntry), !e) for (var r in this) \"t\" === r.charAt(0) && n.call(this, r) && !isNaN(+r.slice(1)) && (this[r] = t); }, stop: function stop() { this.done = !0; var t = this.tryEntries[0].completion; if (\"throw\" === t.type) throw t.arg; return this.rval; }, dispatchException: function dispatchException(e) { if (this.done) throw e; var r = this; function handle(n, o) { return a.type = \"throw\", a.arg = e, r.next = n, o && (r.method = \"next\", r.arg = t), !!o; } for (var o = this.tryEntries.length - 1; o >= 0; --o) { var i = this.tryEntries[o], a = i.completion; if (\"root\" === i.tryLoc) return handle(\"end\"); if (i.tryLoc <= this.prev) { var c = n.call(i, \"catchLoc\"), u = n.call(i, \"finallyLoc\"); if (c && u) { if (this.prev < i.catchLoc) return handle(i.catchLoc, !0); if (this.prev < i.finallyLoc) return handle(i.finallyLoc); } else if (c) { if (this.prev < i.catchLoc) return handle(i.catchLoc, !0); } else { if (!u) throw Error(\"try statement without catch or finally\"); if (this.prev < i.finallyLoc) return handle(i.finallyLoc); } } } }, abrupt: function abrupt(t, e) { for (var r = this.tryEntries.length - 1; r >= 0; --r) { var o = this.tryEntries[r]; if (o.tryLoc <= this.prev && n.call(o, \"finallyLoc\") && this.prev < o.finallyLoc) { var i = o; break; } } i && (\"break\" === t || \"continue\" === t) && i.tryLoc <= e && e <= i.finallyLoc && (i = null); var a = i ? i.completion : {}; return a.type = t, a.arg = e, i ? (this.method = \"next\", this.next = i.finallyLoc, y) : this.complete(a); }, complete: function complete(t, e) { if (\"throw\" === t.type) throw t.arg; return \"break\" === t.type || \"continue\" === t.type ? this.next = t.arg : \"return\" === t.type ? (this.rval = this.arg = t.arg, this.method = \"return\", this.next = \"end\") : \"normal\" === t.type && e && (this.next = e), y; }, finish: function finish(t) { for (var e = this.tryEntries.length - 1; e >= 0; --e) { var r = this.tryEntries[e]; if (r.finallyLoc === t) return this.complete(r.completion, r.afterLoc), resetTryEntry(r), y; } }, catch: function _catch(t) { for (var e = this.tryEntries.length - 1; e >= 0; --e) { var r = this.tryEntries[e]; if (r.tryLoc === t) { var n = r.completion; if (\"throw\" === n.type) { var o = n.arg; resetTryEntry(r); } return o; } } throw Error(\"illegal catch attempt\"); }, delegateYield: function delegateYield(e, r, n) { return this.delegate = { iterator: values(e), resultName: r, nextLoc: n }, \"next\" === this.method && (this.arg = t), y; } }, e; }\nfunction ownKeys(e, r) { var t = Object.keys(e); if (Object.getOwnPropertySymbols) { var o = Object.getOwnPropertySymbols(e); r && (o = o.filter(function (r) { return Object.getOwnPropertyDescriptor(e, r).enumerable; })), t.push.apply(t, o); } return t; }\nfunction _objectSpread(e) { for (var r = 1; r < arguments.length; r++) { var t = null != arguments[r] ? arguments[r] : {}; r % 2 ? ownKeys(Object(t), !0).forEach(function (r) { _defineProperty(e, r, t[r]); }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(e, Object.getOwnPropertyDescriptors(t)) : ownKeys(Object(t)).forEach(function (r) { Object.defineProperty(e, r, Object.getOwnPropertyDescriptor(t, r)); }); } return e; }\nfunction _defineProperty(e, r, t) { return (r = _toPropertyKey(r)) in e ? Object.defineProperty(e, r, { value: t, enumerable: !0, configurable: !0, writable: !0 }) : e[r] = t, e; }\nfunction _toPropertyKey(t) { var i = _toPrimitive(t, \"string\"); return \"symbol\" == _typeof(i) ? i : i + \"\"; }\nfunction _toPrimitive(t, r) { if (\"object\" != _typeof(t) || !t) return t; var e = t[Symbol.toPrimitive]; if (void 0 !== e) { var i = e.call(t, r || \"default\"); if (\"object\" != _typeof(i)) return i; throw new TypeError(\"@@toPrimitive must return a primitive value.\"); } return (\"string\" === r ? String : Number)(t); }\nfunction asyncGeneratorStep(n, t, e, r, o, a, c) { try { var i = n[a](c), u = i.value; } catch (n) { return void e(n); } i.done ? t(u) : Promise.resolve(u).then(r, o); }\nfunction _asyncToGenerator(n) { return function () { var t = this, e = arguments; return new Promise(function (r, o) { var a = n.apply(t, e); function _next(n) { asyncGeneratorStep(a, r, o, _next, _throw, \"next\", n); } function _throw(n) { asyncGeneratorStep(a, r, o, _next, _throw, \"throw\", n); } _next(void 0); }); }; }\nfunction _typeof(o) { \"@babel/helpers - typeof\"; return _typeof = \"function\" == typeof Symbol && \"symbol\" == typeof Symbol.iterator ? function (o) { return typeof o; } : function (o) { return o && \"function\" == typeof Symbol && o.constructor === Symbol && o !== Symbol.prototype ? \"symbol\" : typeof o; }, _typeof(o); }\nimport ProcesPage from '@/components/process-page.vue';\nimport titleCard from '@cpt/scientific-sys/title-card.vue';\nimport html2canvas from 'html2canvas';\nimport jsPDF from 'jspdf';\nexport default {\n  components: {\n    ProcesPage: ProcesPage,\n    titleCard: titleCard\n  },\n  props: {\n    id: {\n      type: String,\n      default: ''\n    },\n    title: {\n      type: String,\n      default: '查看' // 查看 编辑 新增\n    },\n    basics: {\n      type: Object,\n      default: function _default() {\n        return {\n          info: '/ybzy/paAcademicPaper/info',\n          save: '/ybzy/paAcademicPaper/save',\n          edit: 'ybzy/paAcademicPaper/update',\n          flowTypeCode: 'projectAchievement_etipnuuezknypsv',\n          // 流程code\n          defaultProcessKey: 'projectAchievement_etipnuuezknypsv' // 默认关联流程 key\n        };\n      }\n    }\n  },\n  data: function data() {\n    return {\n      account: JSON.parse(localStorage.getItem('loginUserInfo')).code,\n      visiblePrint: false,\n      formReadonly: false,\n      basicInfo: {},\n      rowData: [],\n      orderLoading: false,\n      // 订单列表加载状态\n\n      formData: {\n        students: [],\n        teachers: []\n      },\n      row: {},\n      // 订单类型\n      ORDER_TYPES: {\n        TRAIN: 'FcOutApi_TrainOrder',\n        TICKET: 'FcOutApi_TicketOrder',\n        HOTEL: 'FcOutApi_HotelOrder',\n        USECAR: 'FcOutApi_UsecarOrder'\n      },\n      activeOrderType: 'FcOutApi_TrainOrder',\n      // 当前选中的订单类型\n      // 列表相关\n      tableData: [],\n      startingLoading: false,\n      startingOptions: [],\n      destinationLoading: false,\n      destinationOptions: []\n    };\n  },\n  computed: {\n    contents: function contents() {\n      var _this = this;\n      var formReadonly = this.formReadonly;\n      var formData = this.formData;\n      var arr = [{\n        name: 'number',\n        placeholder: '系统自动生成',\n        label: '申请单号',\n        disabled: true,\n        col: 6,\n        rules: {\n          required: false // 不需要前端验证，但确保字段存在\n        }\n      }, {\n        name: 'name',\n        placeholder: '请输入',\n        label: '申请人',\n        rules: {\n          required: !formReadonly,\n          message: '请输入',\n          trigger: 'blur'\n        },\n        col: 6\n      }, {\n        name: 'account',\n        placeholder: '请输入',\n        label: '教工号',\n        rules: {\n          required: !formReadonly,\n          message: '请输入',\n          trigger: 'blur'\n        },\n        col: 6\n      }, {\n        name: 'leaveType',\n        placeholder: '请选择',\n        col: 6,\n        label: '请假类型',\n        hide: formData.type !== '1',\n        type: 'select',\n        rules: {\n          required: !formReadonly,\n          message: '请选择',\n          trigger: 'blur'\n        },\n        sysCode: 'ybzy_leave_select'\n      }, {\n        name: 'deptName',\n        placeholder: '请输入',\n        label: '部门',\n        rules: {\n          required: !formReadonly,\n          message: '请输入',\n          trigger: 'blur'\n        },\n        col: 6\n      }, {\n        name: 'post',\n        placeholder: '请输入',\n        label: '职称/职务',\n        hide: formData.type !== '2',\n        rules: {\n          required: !formReadonly,\n          message: '请输入',\n          trigger: 'blur'\n        },\n        col: 6\n      }, {\n        name: 'phone',\n        placeholder: '请输入',\n        label: '联系电话',\n        rules: {\n          required: !formReadonly,\n          message: '请输入',\n          trigger: 'blur'\n        },\n        col: 6\n      }, {\n        name: 'members',\n        label: '同行人员',\n        type: 'selector',\n        types: ['person'],\n        placeholder: '点击选择',\n        col: 12,\n        //readonly: true,\n        //multiple: false,\n        hide: formData.type !== '2',\n        value: [{\n          showid: 'sf5454asfbfc85fb4385465456b5641',\n          showname: '李'\n        }]\n        // rules: {\n        // \trequired: true,\n        // \tmessage: '请选择',\n        // \ttrigger: 'blur'\n        // }\n      }, {\n        name: 'leaveType',\n        placeholder: '请选择',\n        col: 12,\n        label: '出差类型',\n        type: 'select',\n        hide: formData.type !== '2',\n        rules: {\n          required: !formReadonly,\n          message: '请选择',\n          trigger: 'blur'\n        },\n        sysCode: 'leave_business_trip_type'\n      }, {\n        name: 'vehicle',\n        placeholder: '请选择',\n        col: 12,\n        label: '交通工具',\n        type: 'radio',\n        hide: formData.type !== '2',\n        rules: {\n          required: !formReadonly,\n          message: '请选择',\n          trigger: 'blur'\n        },\n        sysCode: 'leave_application_vehicle_code'\n      }, {\n        name: 'startingType',\n        placeholder: '请选择',\n        col: 6,\n        label: '出发地类型',\n        type: 'select',\n        hide: formData.type !== '2',\n        data: [{\n          label: '国内',\n          value: 0\n        }, {\n          label: '国际',\n          value: 1\n        }],\n        'label-key': 'label',\n        'value-key': 'value',\n        events: {\n          change: function change(value) {\n            _this.formData.startingCodeTxt = '';\n            _this.formData.startingCode = '';\n            _this.queryCityList('', 'starting');\n          }\n        }\n      }, {\n        name: 'startingCodeTxt',\n        placeholder: '请选择出发地',\n        col: 6,\n        label: '出发地',\n        type: 'select',\n        hide: formData.type !== '2',\n        filterable: true,\n        remote: true,\n        'remote-method': function remoteMethod(val) {\n          _this.queryCityList(val, 'starting');\n        },\n        loading: this.startingLoading,\n        clearable: true,\n        data: this.startingOptions,\n        'label-key': 'label',\n        'value-key': 'value',\n        events: {\n          change: function change(type, value) {\n            // 选择变化时的处理\n            // 找到选中的城市，获取名称\n            var selectedCity = _this.startingOptions.find(function (item) {\n              return item.value === value;\n            });\n            if (selectedCity) {\n              _this.formData.startingCodeTxt = selectedCity.label;\n              _this.formData.startingCode = selectedCity.value;\n            } else {\n              _this.formData.startingCodeTxt = '';\n              _this.formData.startingCode = '';\n            }\n          }\n        },\n        rules: {\n          required: !formReadonly,\n          message: '请选择',\n          trigger: 'blur'\n        }\n      }, {\n        name: 'destinationType',\n        placeholder: '请选择',\n        col: 6,\n        label: '目的地类型',\n        type: 'select',\n        hide: formData.type !== '2',\n        data: [{\n          label: '国内',\n          value: 0\n        }, {\n          label: '国际',\n          value: 1\n        }],\n        'label-key': 'label',\n        'value-key': 'value',\n        events: {\n          change: function change(value) {\n            _this.formData.destinationCodeTxt = '';\n            _this.formData.destinationCode = '';\n            _this.queryCityList('', 'destination');\n          }\n        }\n      }, {\n        name: 'destinationCodeTxt',\n        placeholder: '请选择目的地',\n        col: 6,\n        label: '目的地',\n        type: 'select',\n        key: 'destinationCodeTxt',\n        hide: formData.type !== '2',\n        filterable: true,\n        remote: true,\n        multiple: true,\n        'remote-method': function remoteMethod(val) {\n          if (!val) return;\n          _this.queryCityList(val, 'destination');\n        },\n        loading: this.destinationLoading,\n        clearable: true,\n        data: this.destinationOptions,\n        'label-key': 'label',\n        'value-key': 'value',\n        events: {\n          change: function change(type, value) {\n            _this.handleDestinationChange(value);\n          },\n          // 确保下拉框打开时有数据\n          'visible-change': function visibleChange(visible) {\n            if (visible && _this.destinationOptions.length === 0) {\n              _this.queryCityList('', 'destination');\n            }\n          }\n        },\n        rules: {\n          required: !formReadonly,\n          message: '请选择',\n          trigger: 'blur'\n        }\n      }, {\n        name: 'address',\n        placeholder: '请输入',\n        label: '详细地址',\n        hide: formData.type !== '2',\n        rules: {\n          required: !formReadonly,\n          message: '请输入',\n          trigger: 'blur'\n        },\n        col: 6\n      }, {\n        name: 'days',\n        placeholder: '',\n        label: formData.type === '1' ? '请假天数' : '外出天数',\n        disabled: true,\n        min: 0,\n        col: 6\n      }, {\n        name: 'reason',\n        placeholder: '请输入',\n        label: formData.type === '1' ? '请假原因' : '外出事由',\n        type: 'textarea',\n        'show-word-limit': true,\n        maxlength: 255,\n        // hide: formData.type !== '1',\n        rules: {\n          required: !formReadonly,\n          message: '请输入',\n          trigger: 'blur'\n        },\n        col: 12\n      }, {\n        name: 'startDateTime',\n        placeholder: '请选择',\n        col: 4.5,\n        type: 'date',\n        label: formData.type === '1' ? '开始时间' : '离宜时间',\n        format: 'yyyy-MM-dd',\n        events: {\n          change: function change(e, q) {\n            _this.calculateLeaveDays();\n          }\n        },\n        rules: {\n          required: !formReadonly,\n          message: '请选择',\n          trigger: 'blur'\n        }\n      }, {\n        name: 'isStartAm',\n        col: 1.5,\n        type: 'radio',\n        'value-key': 'value',\n        'label-key': 'label',\n        events: {\n          change: function change(e, q) {\n            _this.calculateLeaveDays();\n          }\n        },\n        data: [{\n          label: '上午',\n          value: '1'\n        }, {\n          label: '下午',\n          value: '2'\n        }]\n      }, {\n        name: 'endDateTime',\n        placeholder: '请选择',\n        col: 4.5,\n        type: 'date',\n        label: formData.type === '1' ? '结束时间' : '返宜时间',\n        format: 'yyyy-MM-dd',\n        events: {\n          change: function change(e, q) {\n            _this.calculateLeaveDays();\n          }\n        },\n        rules: {\n          required: !formReadonly,\n          message: '请选择',\n          trigger: 'blur'\n        }\n      }, {\n        name: 'isEndAm',\n        col: 1.5,\n        type: 'radio',\n        'value-key': 'value',\n        'label-key': 'label',\n        events: {\n          change: function change(e, q) {\n            _this.calculateLeaveDays();\n          }\n        },\n        data: [{\n          label: '上午',\n          value: '1'\n        }, {\n          label: '下午',\n          value: '2'\n        }]\n      }, {\n        name: 'remark',\n        placeholder: '请输入',\n        label: '交接事宜',\n        type: 'textarea',\n        'show-word-limit': true,\n        maxlength: 255,\n        hide: formData.type !== '1',\n        col: 12\n      }, {\n        name: 'emergencyContact',\n        placeholder: '请输入',\n        label: '紧急联系人',\n        hide: formData.type !== '1',\n        col: 6\n      }, {\n        name: 'fj',\n        label: '附件',\n        type: 'attachment',\n        col: 12,\n        code: 'job_dual_select_adjunct',\n        preview: true,\n        ownId: this.formData.id // 业务id\n      }];\n      return arr;\n    },\n    contentsPrint: function contentsPrint() {\n      var _this2 = this;\n      var formReadonly = this.formReadonly;\n      var formData = this.formData;\n      console.log(_typeof(formData.startingCode), _typeof(formData.startingType));\n      var arr = [{\n        name: 'number',\n        placeholder: '系统自动生成',\n        label: '申请单号',\n        disabled: true,\n        col: 6,\n        rules: {\n          required: false // 不需要前端验证，但确保字段存在\n        }\n      }, {\n        name: 'name',\n        placeholder: '请输入',\n        label: '申请人',\n        rules: {\n          required: !formReadonly,\n          message: '请输入',\n          trigger: 'blur'\n        },\n        col: 6\n      }, {\n        name: 'account',\n        placeholder: '请输入',\n        label: '教工号',\n        rules: {\n          required: !formReadonly,\n          message: '请输入',\n          trigger: 'blur'\n        },\n        col: 6\n      }, {\n        name: 'leaveType',\n        placeholder: '请选择',\n        col: 6,\n        label: '请假类型',\n        hide: formData.type !== '1',\n        type: 'select',\n        rules: {\n          required: !formReadonly,\n          message: '请选择',\n          trigger: 'blur'\n        },\n        sysCode: 'ybzy_leave_select'\n      }, {\n        name: 'deptName',\n        placeholder: '请输入',\n        label: '部门',\n        rules: {\n          required: !formReadonly,\n          message: '请输入',\n          trigger: 'blur'\n        },\n        col: 6\n      }, {\n        name: 'post',\n        placeholder: '请输入',\n        label: '职称/职务',\n        hide: formData.type !== '2',\n        rules: {\n          required: !formReadonly,\n          message: '请输入',\n          trigger: 'blur'\n        },\n        col: 6\n      }, {\n        name: 'phone',\n        placeholder: '请输入',\n        label: '联系电话',\n        rules: {\n          required: !formReadonly,\n          message: '请输入',\n          trigger: 'blur'\n        },\n        col: 6\n      }, {\n        name: 'members',\n        label: '同行人员',\n        type: 'selector',\n        types: ['person'],\n        placeholder: '点击选择',\n        col: 12,\n        //readonly: true,\n        //multiple: false,\n        hide: formData.type !== '2',\n        value: [{\n          showid: 'sf5454asfbfc85fb4385465456b5641',\n          showname: '李'\n        }]\n        // rules: {\n        // \trequired: true,\n        // \tmessage: '请选择',\n        // \ttrigger: 'blur'\n        // }\n      }, {\n        name: 'vehicle',\n        placeholder: '请选择',\n        col: 12,\n        label: '交通工具',\n        type: 'radio',\n        hide: formData.type !== '2',\n        rules: {\n          required: !formReadonly,\n          message: '请选择',\n          trigger: 'blur'\n        },\n        sysCode: 'leave_application_vehicle_code'\n      }, {\n        name: 'startingType',\n        placeholder: '请选择',\n        col: 6,\n        label: '出发地类型',\n        type: 'select',\n        hide: formData.type !== '2',\n        data: [{\n          label: '国内',\n          value: 0\n        }, {\n          label: '国际',\n          value: 1\n        }],\n        'label-key': 'label',\n        'value-key': 'value',\n        events: {\n          change: function change(value) {\n            _this2.formData.startingCodeTxt = '';\n            _this2.formData.startingCode = '';\n            _this2.queryCityList('', 'starting');\n          }\n        }\n      }, {\n        name: 'startingCodeTxt',\n        placeholder: '请选择出发地',\n        col: 6,\n        label: '出发地',\n        type: 'select',\n        hide: formData.type !== '2',\n        filterable: true,\n        remote: true,\n        'remote-method': function remoteMethod(val) {\n          _this2.queryCityList(val, 'starting');\n        },\n        loading: this.startingLoading,\n        clearable: true,\n        data: this.startingOptions,\n        'label-key': 'label',\n        'value-key': 'value',\n        events: {\n          change: function change(type, value) {\n            // 选择变化时的处理\n            // 找到选中的城市，获取名称\n            var selectedCity = _this2.startingOptions.find(function (item) {\n              return item.value === value;\n            });\n            if (selectedCity) {\n              _this2.formData.startingCodeTxt = selectedCity.label;\n              _this2.formData.startingCode = selectedCity.value;\n            } else {\n              _this2.formData.startingCodeTxt = '';\n              _this2.formData.startingCode = '';\n            }\n          }\n        },\n        rules: {\n          required: !formReadonly,\n          message: '请选择',\n          trigger: 'blur'\n        }\n      }, {\n        name: 'destinationType',\n        placeholder: '请选择',\n        col: 6,\n        label: '目的地类型',\n        type: 'select',\n        hide: formData.type !== '2',\n        data: [{\n          label: '国内',\n          value: 0\n        }, {\n          label: '国际',\n          value: 1\n        }],\n        'label-key': 'label',\n        'value-key': 'value',\n        events: {\n          change: function change(value) {\n            _this2.formData.destinationCodeTxt = '';\n            _this2.formData.destinationCode = '';\n            _this2.queryCityList('', 'destination');\n          }\n        }\n      }, {\n        name: 'destinationCode',\n        placeholder: '请选择目的地',\n        col: 6,\n        label: '目的地',\n        type: 'select',\n        key: 'destinationCode',\n        hide: formData.type !== '2',\n        filterable: true,\n        remote: true,\n        multiple: true,\n        'remote-method': function remoteMethod(val) {\n          if (!val) return;\n          _this2.queryCityList(val, 'destination');\n        },\n        loading: this.destinationLoading,\n        clearable: true,\n        data: this.destinationOptions,\n        'label-key': 'label',\n        'value-key': 'value',\n        events: {\n          change: function change(type, value) {\n            // 选择变化时的处理\n            // 找到选中的城市，获取名称\n            if (value && value.length) {\n              // 将选中的城市名称和代码转换为逗号分隔的字符串\n              var selectedCities = value.map(function (code) {\n                var city = _this2.destinationOptions.find(function (item) {\n                  return item.value === code;\n                });\n                return city ? city.label : code;\n              });\n              _this2.formData.destinationCodeTxt = selectedCities.join(',');\n              _this2.formData.destinationCode = value.join(',');\n            } else {\n              _this2.formData.destinationCodeTxt = '';\n              _this2.formData.destinationCode = '';\n            }\n          }\n        },\n        rules: {\n          required: !formReadonly,\n          message: '请选择',\n          trigger: 'blur'\n        }\n      }, {\n        name: 'address',\n        placeholder: '请输入',\n        label: '详细地址',\n        hide: formData.type !== '2',\n        rules: {\n          required: !formReadonly,\n          message: '请输入',\n          trigger: 'blur'\n        },\n        col: 6\n      }, {\n        name: 'days',\n        placeholder: '',\n        label: formData.type === '1' ? '请假天数' : '外出天数',\n        min: 0,\n        col: 6\n      }, {\n        name: 'emergencyContact',\n        placeholder: '请输入',\n        label: '紧急联系人',\n        hide: formData.type !== '1',\n        col: 6\n      }, {\n        name: 'startDateTimeTxt',\n        placeholder: '请选择',\n        col: 6,\n        type: 'date',\n        label: formData.type === '1' ? '开始时间' : '离宜时间',\n        // format: 'yyyy-MM-dd',\n        events: {\n          change: function change(e, q) {\n            _this2.calculateLeaveDays();\n          }\n        },\n        rules: {\n          required: !formReadonly,\n          message: '请选择',\n          trigger: 'blur'\n        }\n      }, {\n        name: 'endDateTimeTxt',\n        placeholder: '请选择',\n        col: 6,\n        type: 'date',\n        label: formData.type === '1' ? '结束时间' : '返宜时间',\n        // format: 'yyyy-MM-dd',\n        events: {\n          change: function change(e, q) {\n            _this2.calculateLeaveDays();\n          }\n        },\n        rules: {\n          required: !formReadonly,\n          message: '请选择',\n          trigger: 'blur'\n        }\n      }, {\n        name: 'reason',\n        placeholder: '请输入',\n        label: formData.type === '1' ? '请假原因' : '外出事由',\n        type: 'textarea',\n        'show-word-limit': true,\n        maxlength: 255,\n        // hide: formData.type !== '1',\n        rules: {\n          required: !formReadonly,\n          message: '请输入',\n          trigger: 'blur'\n        },\n        col: 12\n      }, {\n        name: 'remark',\n        placeholder: '请输入',\n        label: '交接事宜',\n        type: 'textarea',\n        'show-word-limit': true,\n        maxlength: 255,\n        hide: formData.type !== '1',\n        col: 12\n      }];\n      return arr;\n    },\n    // 根据当前选中的订单类型返回对应的表头配置\n    thead: function thead() {\n      // 根据订单类型添加特定字段\n      var fields = [];\n      switch (this.activeOrderType) {\n        case this.ORDER_TYPES.TRAIN:\n          // 火车票\n          fields = [{\n            title: '车票号',\n            field: 'orderNo',\n            align: 'center',\n            fixed: false\n          }, {\n            title: '乘车人',\n            field: 'name',\n            align: 'center',\n            fixed: false\n          }, {\n            title: '车次',\n            field: 'trainCode',\n            align: 'center',\n            fixed: false\n          }, {\n            title: '座位',\n            align: 'center',\n            field: 'zw',\n            fixed: false\n          }, {\n            title: '出发站',\n            field: 'startCity',\n            align: 'center',\n            fixed: false\n          }, {\n            title: '到达站',\n            field: 'endCity',\n            align: 'center',\n            fixed: false\n          }, {\n            title: '出发时间',\n            field: 'startTime',\n            align: 'center',\n            fixed: false\n          }, {\n            title: '到达时间',\n            field: 'endTime',\n            align: 'center',\n            fixed: false\n          }, {\n            title: '价格',\n            field: 'amount',\n            align: 'center',\n            fixed: false\n          }, {\n            title: '状态',\n            field: 'status',\n            align: 'center',\n            fixed: false\n          }];\n          break;\n        case this.ORDER_TYPES.TICKET:\n          // 机票\n          fields = [{\n            title: '票号',\n            field: 'ticketCode',\n            align: 'center',\n            fixed: false\n          }, {\n            title: '乘机人',\n            align: 'center',\n            field: 'name',\n            fixed: false\n          }, {\n            title: '航班号',\n            align: 'center',\n            field: 'hbh',\n            fixed: false\n          }, {\n            title: '舱位信息',\n            align: 'center',\n            field: 'cw',\n            fixed: false\n          }, {\n            title: '出发机场',\n            field: 'startCity',\n            align: 'center',\n            fixed: false\n          }, {\n            title: '到达机场',\n            field: 'endCity',\n            align: 'center',\n            fixed: false\n          }, {\n            title: '起飞时间',\n            field: 'qfsj',\n            align: 'center',\n            fixed: false\n          }, {\n            title: '到达时间',\n            field: 'ddsj',\n            align: 'center',\n            fixed: false\n          }, {\n            title: '价格',\n            field: 'amount',\n            align: 'center',\n            fixed: false\n          }, {\n            title: '状态',\n            field: 'status',\n            align: 'center',\n            fixed: false\n          }];\n          break;\n        case this.ORDER_TYPES.HOTEL:\n          // 酒店\n          fields = [{\n            title: '订单编号',\n            field: 'ddbh',\n            align: 'center',\n            fixed: false\n          }, {\n            title: '预定人',\n            field: 'name',\n            align: 'center',\n            fixed: false\n          }, {\n            title: '酒店名称',\n            field: 'hotelName',\n            align: 'center',\n            fixed: false\n          }, {\n            title: '房型',\n            field: 'jdfx',\n            align: 'center',\n            fixed: false\n          }, {\n            title: '入住人',\n            align: 'center',\n            field: 'passengers',\n            minWidth: 100,\n            fixed: false,\n            render: function render(h, params) {\n              // 处理换行显示\n              if (!params.row.passengers) return h('span', '-');\n              var content = params.row.passengers.split('\\n').map(function (text) {\n                return h('div', text);\n              });\n              return h('div', content);\n            }\n          }, {\n            title: '房间数',\n            field: 'fjsl',\n            align: 'center',\n            fixed: false\n          }, {\n            title: '入住时间',\n            field: 'startEndTime',\n            align: 'center',\n            fixed: false\n          }, {\n            title: '价格',\n            field: 'amount',\n            align: 'center',\n            fixed: false\n          }, {\n            title: '状态',\n            field: 'status',\n            align: 'center',\n            fixed: false\n          }];\n          break;\n        case this.ORDER_TYPES.USECAR:\n          // 用车\n          fields = [{\n            title: '预定人',\n            align: 'center',\n            field: 'name',\n            fixed: false\n          }, {\n            title: '乘客信息',\n            align: 'center',\n            field: 'passengers',\n            fixed: false,\n            render: function render(h, params) {\n              // 处理换行显示\n              if (!params.row.passengers) return h('span', '-');\n              var content = params.row.passengers.split('\\n').map(function (text) {\n                return h('div', text);\n              });\n              return h('div', content);\n            }\n          }, {\n            title: '车型',\n            align: 'center',\n            field: 'carInfo',\n            fixed: false\n          }, {\n            title: '出发地',\n            field: 'startCity',\n            align: 'center',\n            fixed: false\n          }, {\n            title: '目的地',\n            field: 'endCity',\n            align: 'center',\n            fixed: false\n          }, {\n            title: '用车时间',\n            field: 'startTime',\n            align: 'center',\n            fixed: false\n          }, {\n            title: '价格',\n            field: 'amount',\n            align: 'center',\n            fixed: false\n          }, {\n            title: '状态',\n            field: 'status',\n            align: 'center',\n            fixed: false\n          }];\n          break;\n        default:\n          fields = [{\n            title: '预定人',\n            align: 'center',\n            field: 'name',\n            fixed: false\n          }, {\n            title: '状态',\n            field: 'status',\n            align: 'center',\n            fixed: false\n          }];\n      }\n      return fields;\n    }\n  },\n  watch: {\n    id: {\n      handler: function handler(newVal, oldVal) {\n        if (newVal) {\n          this.getPorjectInfo();\n        }\n      },\n      immediate: true\n    },\n    // 监听目的地选项变化，确保正确回显\n    destinationOptions: {\n      handler: function handler(newVal) {\n        var _this3 = this;\n        if (newVal.length > 0 && this.formData.destinationCode) {\n          // 强制刷新表单\n          this.$nextTick(function () {\n            _this3.$forceUpdate();\n          });\n        }\n      },\n      deep: true\n    }\n  },\n  mounted: function mounted() {\n    var _this4 = this;\n    // 初始化加载出发地和目的地列表\n    if (this.title.includes('新增') && this.title.includes('因公')) {\n      // 只有在因公外出情况下才需要初始化城市列表\n      this.$nextTick(function () {\n        _this4.queryCityList('', 'starting');\n        _this4.queryCityList('', 'destination');\n      });\n    }\n  },\n  methods: {\n    exportToPdf: function exportToPdf() {\n      var printMe = document.getElementById('printMe'); // 获取需要打印的内容\n      html2canvas(printMe, {\n        letterRendering: true,\n        scale: 2,\n        useCORS: true,\n        allowTaint: true\n      }).then(function (canvas) {\n        var imgData = canvas.toDataURL('image/png');\n        var pdf = new jsPDF('p', 'mm', 'a4'); // 设置 PDF 页面大小为 A4\n\n        // 计算图片在 PDF 上的位置和大小\n        var imgWidth = pdf.internal.pageSize.width;\n        var imgHeight = canvas.height * imgWidth / canvas.width;\n\n        // 添加图像到 PDF 上\n        pdf.addImage(imgData, 'PNG', 0, 0, imgWidth, imgHeight);\n\n        // 保存 PDF 文件\n        pdf.save('sample.pdf');\n      });\n    },\n    calculateLeaveDays: function calculateLeaveDays() {\n      var startDateTime = this.formData.startDateTime;\n      var endDateTime = this.formData.endDateTime;\n      var isStartAm = (this.formData.isStartAm || 0) * 1; // 1代表上午，2代表下午\n      var isEndAm = (this.formData.isEndAm || 0) * 1; // 同上\n\n      if (!startDateTime || !endDateTime) {\n        console.error('Start or end date is missing.');\n        return;\n      }\n      var startDate = new Date(startDateTime);\n      var endDate = new Date(endDateTime);\n      var sameDay = startDate.toDateString() === endDate.toDateString();\n\n      // 检查结束时间是否早于开始时间\n      if (endDate < startDate) {\n        this.$message.error('结束时间不能小于开始时间');\n        this.formData.days = 0;\n        this.formData.endDateTime = '';\n        return;\n      }\n\n      // 计算日期差\n      var diff = endDate.getTime() - startDate.getTime();\n      var oneDay = 1000 * 60 * 60 * 24;\n      var days = Math.floor(diff / oneDay) + 1;\n\n      // 考虑半天的情况\n      // 如果同一天\n      if (sameDay) {\n        if (isStartAm === 2 && isEndAm === 1) {\n          days = 0.5; // 如果开始是下午，结束是上午，不计天数\n          this.formData.isEndAm = '2';\n        } else if (isStartAm === 1 && isEndAm === 1) {\n          days = 0.5; // 如果都是上午，计半天\n        } else if (isStartAm === 2 && isEndAm === 2) {\n          days = 0.5; // 如果都是下午，也计半天\n        } else {\n          days = 1; // 其他情况（如上午到下午），计全天\n        }\n      } else {\n        // 如果不是同一天且有剩余时间\n        if (isStartAm === 1 && isEndAm === 1) {\n          days -= 0.5;\n        }\n        if (isStartAm === 2 && isEndAm === 1) {\n          days -= 1;\n        }\n        if (isStartAm === 2 && isEndAm === 2) {\n          days -= 0.5;\n        }\n      }\n\n      // 将结果赋值给表单数据\n      this.$set(this.formData, 'days', days);\n      console.log(days, 'days');\n    },\n    // 是否只读和编辑\n    toolFormReadonly: function toolFormReadonly() {\n      var _this5 = this;\n      this.$nextTick(function () {\n        var isEdit = window.location.href.includes('isEdit'); // 判断是否流程页面的编辑\n        _this5.formReadonly = _this5.title === '查看' && !isEdit ? true : false;\n      });\n    },\n    //修改保存\n    save: function save(callBank) {\n      var _this6 = this;\n      return _asyncToGenerator(/*#__PURE__*/_regeneratorRuntime().mark(function _callee() {\n        return _regeneratorRuntime().wrap(function _callee$(_context) {\n          while (1) switch (_context.prev = _context.next) {\n            case 0:\n              _this6.$refs.formRef.validate(function (valid) {\n                if (valid) {\n                  var _this6$formData$membe, _this6$formData$membe2;\n                  var loading = _this6.load('提交中...');\n                  var formData = _objectSpread(_objectSpread({}, _this6.formData), {}, {\n                    number: _this6.formData.number || '',\n                    // 确保number字段被包含在提交的数据中\n                    members: ((_this6$formData$membe = _this6.formData.members) === null || _this6$formData$membe === void 0 || (_this6$formData$membe2 = _this6$formData$membe.map) === null || _this6$formData$membe2 === void 0 ? void 0 : _this6$formData$membe2.call(_this6$formData$membe, function (item) {\n                      var attr = JSON.parse(item.attr || '{}');\n                      return {\n                        applicationId: _this6.id,\n                        // 请假申请id\n                        userId: item.showid,\n                        // 用户id\n                        userName: item.showname,\n                        // 用户姓名\n                        userCode: attr.outcode,\n                        // 用户编码\n                        userPhone: attr.phone,\n                        // 联系方式\n                        sex: attr.sex // 性别\n                      };\n                    })) || []\n                  });\n\n                  // 确保destinationCode和destinationCodeTxt是逗号分隔的字符串\n                  if (Array.isArray(formData.destinationCode)) {\n                    formData.destinationCode = formData.destinationCode.join(',');\n                  }\n                  if (Array.isArray(formData.destinationCodeTxt)) {\n                    formData.destinationCodeTxt = formData.destinationCodeTxt.join(',');\n                  }\n                  _this6.$.ajax({\n                    url: _this6.title.includes('新增') ? _this6.basics.save : _this6.basics.edit,\n                    method: 'post',\n                    data: formData,\n                    format: false\n                  }).then(function (res) {\n                    loading.close();\n                    if (res.rCode == 0) {\n                      if (callBank) {\n                        callBank();\n                      } else {\n                        _this6.$message.success(res.msg);\n                        _this6.handleSuccess();\n                      }\n                    } else {\n                      _this6.$message.warning(res.msg);\n                    }\n                  });\n                }\n              });\n            case 1:\n            case \"end\":\n              return _context.stop();\n          }\n        }, _callee);\n      }))();\n    },\n    // 提交成功\n    handleSuccess: function handleSuccess(e) {\n      //pendingId则为流程的审核页面，否则是弹窗的流程\n      var isFlow = window.location.href.includes('pendingId');\n      if (isFlow) {\n        window.close();\n      } else {\n        this.$emit('update:visible', false);\n        this.$emit('visible', false);\n      }\n    },\n    // 页面加载\n    load: function load(text) {\n      return this.$.loading(this.$loading, text);\n    },\n    getPorjectInfo: function getPorjectInfo() {\n      var _this7 = this;\n      return _asyncToGenerator(/*#__PURE__*/_regeneratorRuntime().mark(function _callee2() {\n        var loginUserInfo, loading, _yield$_this7$$$ajax, rCode, msg, results, obj;\n        return _regeneratorRuntime().wrap(function _callee2$(_context2) {\n          while (1) switch (_context2.prev = _context2.next) {\n            case 0:\n              _this7.toolFormReadonly();\n              if (!_this7.title.includes('新增')) {\n                _context2.next = 6;\n                break;\n              }\n              loginUserInfo = JSON.parse(localStorage.getItem('loginUserInfo') || '{}');\n              _this7.formData = {\n                type: _this7.title.includes('因公') ? '2' : '1',\n                isStartAm: '1',\n                isEndAm: '2',\n                name: loginUserInfo.name,\n                // 申请人\n                account: _this7.account,\n                // 教工号\n                number: '',\n                // 申请单号，系统自动生成，先设置为空字符串确保字段存在\n                deptId: loginUserInfo.deptId,\n                deptName: loginUserInfo.deptName,\n                phone: loginUserInfo.phone,\n                id: _this7.id,\n                startingType: 0,\n                // 默认出发地类型为国内\n                destinationType: 0 // 默认目的地类型为国内\n              };\n\n              // 如果是因公外出，初始化城市列表\n              if (_this7.title.includes('因公')) {\n                _this7.$nextTick(function () {\n                  _this7.queryCityList('', 'starting');\n                  _this7.queryCityList('', 'destination');\n                });\n              }\n              return _context2.abrupt(\"return\");\n            case 6:\n              loading = _this7.load('加载中...');\n              _context2.prev = 7;\n              _context2.next = 10;\n              return _this7.$.ajax({\n                url: _this7.basics.info,\n                method: 'get',\n                params: {\n                  id: _this7.id\n                }\n              });\n            case 10:\n              _yield$_this7$$$ajax = _context2.sent;\n              rCode = _yield$_this7$$$ajax.rCode;\n              msg = _yield$_this7$$$ajax.msg;\n              results = _yield$_this7$$$ajax.results;\n              if (rCode == 0) {\n                obj = results;\n                _this7.setTableData(obj);\n\n                // 如果是因公外出且状态为已审批通过，加载订单数据\n                if (obj.type === '2' && obj.status === '2') {\n                  _this7.getOrdersByType();\n                }\n              } else {\n                _this7.$message.error(msg);\n              }\n            case 15:\n              _context2.prev = 15;\n              loading.close();\n              return _context2.finish(15);\n            case 18:\n            case \"end\":\n              return _context2.stop();\n          }\n        }, _callee2, null, [[7,, 15, 18]]);\n      }))();\n    },\n    setTableData: function setTableData(obj) {\n      var _obj$members,\n        _this8 = this;\n      var formData = _objectSpread(_objectSpread({}, obj), {}, {\n        startDateTimeTxt: obj.startDateTime + (obj.isStartAm === '1' ? '-上午' : '-下午'),\n        endDateTimeTxt: obj.endDateTime + (obj.isEndAm === '1' ? '-上午' : '-下午'),\n        members: (obj === null || obj === void 0 || (_obj$members = obj.members) === null || _obj$members === void 0 ? void 0 : _obj$members.map(function (item) {\n          return {\n            showid: item.userId,\n            showname: item.userName,\n            attr: JSON.stringify({\n              outcode: item.userCode,\n              phone: item.userPhone,\n              sex: item.sex\n            })\n          };\n        })) || [],\n        startingType: obj.startingType !== undefined ? obj.startingType : 0,\n        destinationType: obj.destinationType !== undefined ? obj.destinationType : 0\n      });\n\n      // 处理目的地代码，确保在编辑模式下是数组\n      if (obj.destinationCode && typeof obj.destinationCode === 'string') {\n        formData.destinationCode = obj.destinationCode.split(',');\n      }\n      this.formData = formData;\n\n      // 编辑状态下，加载城市列表\n      if (this.formData.type === '2') {\n        this.$nextTick(function () {\n          _this8.queryCityList('', 'starting');\n          // 如果有目的地代码，先将其添加到选项中以确保回显正确\n          if (obj.destinationCode) {\n            // 将目的地代码和文本转为数组\n            var destinationCodes = typeof obj.destinationCode === 'string' ? obj.destinationCode.split(',') : obj.destinationCode;\n            var destinationNames = obj.destinationCodeTxt.split(',');\n\n            // 预先添加已选城市到选项列表\n            _this8.destinationOptions = destinationCodes.map(function (code, index) {\n              return {\n                label: destinationNames[index] || code,\n                value: code\n              };\n            });\n\n            // 然后再加载完整列表\n            _this8.queryCityList('', 'destination');\n          } else {\n            _this8.queryCityList('', 'destination');\n          }\n        });\n      }\n    },\n    queryCityList: function queryCityList(keyword, type) {\n      var _this9 = this;\n      return _asyncToGenerator(/*#__PURE__*/_regeneratorRuntime().mark(function _callee3() {\n        var _yield$_this9$$$ajax, rCode, msg, results, options, yibinExists, existingOptions, mergedOptions;\n        return _regeneratorRuntime().wrap(function _callee3$(_context3) {\n          while (1) switch (_context3.prev = _context3.next) {\n            case 0:\n              if (type === 'starting') {\n                _this9.startingLoading = true;\n              } else {\n                _this9.destinationLoading = true;\n              }\n              _context3.prev = 1;\n              _context3.next = 4;\n              return _this9.$.ajax({\n                url: '/ybzy/attendanceLeave/queryCityPage',\n                method: 'get',\n                params: {\n                  type: type === 'starting' ? _this9.formData.startingType : _this9.formData.destinationType,\n                  keyword: keyword,\n                  pageNum: 1,\n                  pageSize: 100\n                }\n              });\n            case 4:\n              _yield$_this9$$$ajax = _context3.sent;\n              rCode = _yield$_this9$$$ajax.rCode;\n              msg = _yield$_this9$$$ajax.msg;\n              results = _yield$_this9$$$ajax.results;\n              if (rCode == 0) {\n                options = results.records.map(function (item) {\n                  return {\n                    label: item.name,\n                    value: item.code\n                  };\n                });\n                if (type === 'starting') {\n                  // 确保宜宾市选项存在\n                  yibinExists = options.some(function (item) {\n                    return item.value === '10369';\n                  });\n                  if (!yibinExists && _this9.formData.startingType === 0) {\n                    // 只在国内城市列表中添加宜宾\n                    options.unshift({\n                      label: '宜宾',\n                      value: '10369'\n                    });\n                  }\n                  _this9.startingOptions = options;\n\n                  // 如果是新增模式，设置默认值为宜宾\n                  if (_this9.title.includes('新增') && !_this9.formData.startingCode && _this9.formData.startingType === 0) {\n                    _this9.$set(_this9.formData, 'startingCode', '10369');\n                    _this9.$set(_this9.formData, 'startingCodeTxt', '宜宾');\n                    _this9.$set(_this9.formData, 'destinationCode', '');\n                    _this9.$set(_this9.formData, 'destinationCodeTxt', '');\n                  }\n                } else if (type === 'destination') {\n                  // console.log(8888);\n                  // 合并新选项与已有选项，确保不会丢失已选城市\n                  existingOptions = _toConsumableArray(_this9.destinationOptions);\n                  mergedOptions = _toConsumableArray(options); // 检查已有选项是否在新选项中，如果不在则添加\n                  existingOptions.forEach(function (existing) {\n                    var exists = mergedOptions.some(function (option) {\n                      return option.value === existing.value;\n                    });\n                    if (!exists) {\n                      mergedOptions.push(existing);\n                    }\n                  });\n                  _this9.destinationOptions = mergedOptions;\n                  // this.$forceUpdate();\n                }\n              } else {\n                _this9.$message.error(msg);\n              }\n            case 9:\n              _context3.prev = 9;\n              if (type === 'starting') {\n                _this9.startingLoading = false;\n              } else {\n                _this9.destinationLoading = false;\n              }\n              return _context3.finish(9);\n            case 12:\n            case \"end\":\n              return _context3.stop();\n          }\n        }, _callee3, null, [[1,, 9, 12]]);\n      }))();\n    },\n    // 跳转到订票系统\n    goToBooking: function goToBooking() {\n      // 调用SSO认证接口\n      var baseUrl = window.location.origin;\n      window.open(\"\".concat(baseUrl, \"/ybzy/attendanceLeave/authSso\"));\n    },\n    // 切换订单类型\n    handleOrderTypeChange: function handleOrderTypeChange() {\n      this.getOrdersByType();\n    },\n    // 获取订单列表\n    getOrdersByType: function getOrdersByType() {\n      var _this10 = this;\n      this.orderLoading = true;\n      this.tableData = []; // 清空之前的数据\n\n      this.$.ajax({\n        url: '/ybzy/attendanceLeave/getOrdersByType',\n        method: 'get',\n        params: {\n          id: this.id,\n          type: this.activeOrderType\n        }\n      }).then(function (res) {\n        if (res.rCode === 0) {\n          _this10.tableData = res.results || [];\n        } else {\n          _this10.$message.error(res.msg || '获取订单数据失败');\n        }\n      }).finally(function () {\n        _this10.orderLoading = false;\n      });\n    }\n  }\n};", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}