/*自定义换肤主题配置*/
$--color-primary: #0076E9;
// $--color-primary-light-1: var(--theme-primary-light-1);
// $--color-primary-light-2: var(--theme-primary-light-2);
// $--color-primary-light-3: var(--theme-primary-light-3);
// $--color-primary-light-4: var(--theme-primary-light-4);
// $--color-primary-light-5: var(--theme-primary-light-5);
// $--color-primary-light-6: var(--theme-primary-light-6);
// $--color-primary-light-7: var(--theme-primary-light-7);
// $--color-primary-light-8: var(--theme-primary-light-8);
// $--color-primary-light-9: var(--theme-primary-light-9);
// $--color-primary-light-10: var(--theme-primary-light-10);

/*
    固定颜色主题配置采用如下配置方式

    $--color-primary: #e94c41;
*/


$--el-font-path: '~eoss-element/lib/theme-chalk/fonts';
@import "~eoss-element/packages/theme-chalk/src/index";
$--es-font-path: '~eoss-ui/lib/theme-chalk/fonts';
@import "~eoss-ui/packages/theme-chalk/src/index";


@import './base.scss';
@import './main.scss';
@import './login.scss';
@import './style.scss';
@import './add-new.scss';
@import './es-process.scss';
@import './financing-data.scss';
@import './loan-statistics.scss';
@import './tree-select.scss';
@import './write-report.scss';

@import './amend-standing.scss';
@import './amend-warning.scss';
@import './treeSelect.scss';
@import './write-check.scss';
@import './amendStanding.scss';
@import './check-list.scss';
@import './contentBox.scss';
@import './filling.scss';