<template>
	<div style="width: 100%; height: 100%">
		<es-data-table
			ref="table"
			style="width: 100%"
			:row-style="tableRowClassName"
			:border="true"
			:thead="thead"
			:page="true"
			stripe
			:url="basic.tableUrl"
			:param="param"
			:toolbar="toolbar"
			method="get"
			close
			@btnClick="btnClick"
		></es-data-table>
		<!-- 新增变更弹框 -->
		<es-dialog
			v-if="visibleChange"
			ref="visibleChange"
			class="dialog-box"
			title="选择项目"
			size="md"
			height="650px"
			:append-to-body="false"
			:modal-append-to-body="false"
			:visible.sync="visibleChange"
			@close="handlecloseChange"
		>
			<SelectData @cloeseChoose="cloeseChoose" />
		</es-dialog>
		<es-dialog
			:title="title + '变更'"
			:visible.sync="isChange"
			:show-scale="false"
			size="full"
			height="100%"
		>
			<!-- <Detail
				v-if="isChange"
				:id="bgIdxx"
				:title="title"
				open-type="edit"
				init-active-name="BasicInfo"
				@reload="reload"
			/> -->
			<BasicInfo
				v-if="isChange"
				:id="bgIdxx"
				style="width: 100%"
				:contents-key="contentsKey"
				:title="title + '变更'"
				:is-flow-pattern="true"
				:basics="{
					info: title.includes('查看')
						? '/ybzy/projectBaseInfo/getChangeInfo'
						: '/ybzy/projectBaseInfo/getBaseVOById',
					edit: '/ybzy/projectBaseInfo/projectChange',
					flowTypeCode: 'change',
					defaultProcessKey: 'changeManage'
				}"
				:btn-list="[{ text: '提交', event: 'sub', type: 'primary' }]"
				@visible="
					e => {
						isChange = e;
						$refs.table.reload();
					}
				"
			/>
		</es-dialog>
	</div>
</template>

<script>
import SelectData from '@/views/scientific-sys/components/selectData.vue';
// import Detail from '@/views/scientific-sys/project-management/inventory/detail.vue';
import BasicInfo from '../components/project-info/basic-info.vue';

import { updateChangeFlow, saveChangeFlow } from '@/api/scientific-sys.js';
import { createNamespacedHelpers } from 'vuex';
const { mapState } = createNamespacedHelpers('getRoleMap');

export default {
	components: {
		BasicInfo,
		SelectData
		// Detail
	},
	data() {
		return {
			basic: {
				tableUrl: '/ybzy/projectBaseInfo/projectChangePage'
			},
			projectClassify: 1,
			title: '',
			param: {
				orderBy: 'createTime',
				asc: 'false', // true 升序，false 降序
				state: 1
			},
			bgIdxx: '',
			isChange: false,
			action: saveChangeFlow,
			show: false,
			nodeName: '',
			values: '',
			getFormId: '',
			readonly: false,
			visibleChange: false,
			tableData: [],
			toolbar: [
				{
					type: 'button',
					contents: [
						{
							text: '变更申请',
							type: 'primary'
						}
					]
				},
				{
					type: 'search',
					reset: true,
					contents: [
						{
							type: 'text',
							label: '关键字查询',
							name: 'keyword',
							placeholder: '请输入关键字',
							col: 3
						}
					]
				},
				{
					type: 'filter',
					contents: [
						{
							type: 'text',
							label: '项目编号',
							name: 'projectNumber',
							placeholder: '请输入立项编号',
							col: 3
						},
						{
							type: 'text',
							label: '项目名称',
							name: 'projectName',
							placeholder: '请输入',
							col: 3
						},
						{
							type: 'select',
							label: '项目分类',
							placeholder: '请选择',
							name: 'projectType',
							col: 3,
							sysCode: 'send_type'
						},
						{
							type: 'select',
							label: '变更状态',
							placeholder: '请选择',
							name: 'auditState',
							col: 3,
							sysCode: 'project_flow_state'
						},
						{
							type: 'text',
							label: '申请人 ',
							name: 'createUserName',
							placeholder: '输入申报人查询',
							col: 3
						},
						{
							type: 'monthrange',
							label: '申请时间',
							name: 'declareTimeStartAndEnd',
							default: true,
							placeholder: '选择日期',
							clearable: true,
							col: 3
						}
					]
				}
			],
			thead: [
				{
					title: '项目编号',
					field: 'projectNumber',
					align: 'center',
					showOverflowTooltip: true
				},
				{
					title: '项目名称',
					field: 'projectName',
					align: 'center',
					showOverflowTooltip: true
				},
				{
					title: '项目分类',
					field: 'projectClassifyTxt',
					align: 'center',
					// width: 120,
					showOverflowTooltip: true
				},
				{
					title: '变更申请时间',
					field: 'createTime',
					align: 'center',
					showOverflowTooltip: true
				},
				{
					title: '变更申请人',
					field: 'createUserName',
					align: 'center',
					// width: 120,
					showOverflowTooltip: true
				},
				{
					title: '变更事由',
					field: 'changeReason',
					align: 'center',
					showOverflowTooltip: true
				},
				{
					title: '变更状态',
					fixed: 'right',
					field: 'auditStateTxt',
					align: 'center',
					// width: 100,
					render: (h, params) => {
						let auditStateTxt = params.row.auditStateTxt;
						let auditState = params.row.auditState;
						return h(
							'el-tag',
							{
								props: {
									size: 'small',
									type: this.projecstate(auditState)
								}
							},
							auditStateTxt
						);
					}
				},

				{
					title: '操作',
					type: 'handle',
					// fixed: 'right',
					// width: '60',
					align: 'center',
					events: [
						{
							text: '查看'
						}
						// {
						// 	text: '编辑',
						// 	rules: rows => {
						// 		return rows.change_state === '草稿';
						// 	}
						// },

						// {
						// 	text: '变更',
						// 	rules: rows => {
						// 		return rows.change_state === '待变更';
						// 	}
						// },
						// {
						// 	text: '撤销',
						// 	rules: rows => {
						// 		return rows.auditState === '2';
						// 	}
						// }
					]
				}
			]
		};
	},

	computed: {
		...mapState(['projectScienceManager']),
		contentsKey() {
			// LENTHWAYS("lengthWays", "纵向项目",0),
			// CROSSWISE("crosswise", "横向项目",1),
			// COLLEGE("college", "院级项目",2),
			const projectClassify = Number(this.projectClassify);
			let contentsKey = '';
			switch (projectClassify) {
				case 0:
					contentsKey = 'verticalProject'; // 纵向项目
					break;
				case 1:
					contentsKey = 'crosswiseProject'; // 横向项目
					break;
				case 2:
					contentsKey = 'instituteProject'; // 院级项目
					break;
			}
			return contentsKey;
		}
	},
	watch: {
		projectScienceManager: {
			immediate: true,
			handler(newVal) {
				// projectScienceManager：父级传递的身份标识 如果为科技处老师值为true 可进行修改删除操作，反之不可
				if (typeof newVal == 'boolean' && newVal === true) {
					this.thead[this.thead.length - 1].events.push({
						text: '变更',
						rules: rows => {
							return rows.change_state === '待变更';
						}
					});
				}
			}
		}
	},
	mounted() {
		//拖拽组件缩放事件总线
		// this.getListData();
	},
	methods: {
		/**
		 * 表格行高
		 */
		tableRowClassName({ row, rowIndex }) {
			let styleRes = {
				height: '54px !important'
			};
			return styleRes;
		},
		// 获取项目审核状态
		projecstate(state) {
			let stateCurrent = '';
			// 项目状态（字典编码：project_status）0：草稿、1：待审核：2：审核中、3：审核通过、9：驳回
			switch (state) {
				case '0':
					stateCurrent = 'warning';
					break;
				case '2':
					stateCurrent = 'primary';
					break;
				case '3':
					stateCurrent = 'success';
					break;
				case '1':
					stateCurrent = 'info';
					break;
				case '9':
					stateCurrent = 'danger';
					break;
				default:
					break;
			}
			return stateCurrent;
		},
		reload(reload) {
			this.isChange = reload;
			this.$refs.table.reload();
		},
		getListData() {
			this.$.ajax({
				url: '/ybzy/projectChange/listJson'
			})
				.then(res => {
					if (res.results == null) {
						this.tableData = [];
					} else {
						this.tableData = res.results.records;
					}
				})
				.catch(err => {});
		},
		//高级搜索
		submit({ data }) {
			this.$.ajax({
				url: '/ybzy/projectChange/listJson',
				params: data
			})
				.then(res => {
					if (res.results == null) {
						this.tableData = [];
					} else {
						this.tableData = res.results.records;
					}
				})
				.catch(err => {});
		},
		handlecloseChange() {
			this.visibleChange = false;
		},
		btnClick({ handle, row }) {
			switch (handle.text) {
				case '变更申请':
					this.visibleChange = true;
					break;
				case '撤销':
					this.$confirm('确定撤销', {
						confirmButtonText: '确定',
						cancelButtonText: '取消',
						type: 'warning'
					})
						.then(() => {
							// this.$.ajax({
							// 	url: '/ybzy/projectChange/deleteById',
							// 	method: 'POST',
							// 	data: {
							// 		id: row.id
							// 	}
							// })
							// 	.then(res => {
							// 		this.$message.success('删除成功');
							// 		this.$.ajax({
							// 			url: '/ybzy/projectChange/listJson'
							// 		})
							// 			.then(res => {
							// 				if (res.results == null) {
							// 					this.tableData = [];
							// 				} else {
							// 					this.tableData = res.results.records;
							// 				}
							// 			})
							// 			.catch(err => {});
							// 	})
							// 	.catch(err => {});
						})
						.catch(() => {});
					break;
				case '查看':
					this.title = '查看';
					this.isChange = true;
					this.bgIdxx = row.id;

					// this.visibleChange = true;
					// this.title = '详情';
					// this.readonly = true;
					// this.show = false;
					// this.getFormId = row.id;
					// if (row.change_state === '草稿') {
					// 	this.draft = true;
					// } else {
					// 	this.draft = false;
					// }
					break;
				case '编辑':
					this.visibleChange = true;
					this.readonly = false;
					this.show = true;
					this.title = '编辑变更';
					this.getFormId = row.id;
					this.action = updateChangeFlow;
					this.draft = true;
					break;
				// case '变更':
				// 	this.title = '变更';
				// 	this.isChange = true;
				// 	this.bgIdxx = row.project_id;
				// 	break;

				default:
					break;
			}
		},
		cloeseChoose(row) {
			this.projectClassify = row.projectClassify;
			this.title = '编辑';
			this.isChange = true;
			this.visibleChange = false;
			this.bgIdxx = row.id;
		},
		reInterface() {
			this.$refs.table.reload();
		},
		handleisChange() {
			this.isChange = false;
			this.$.ajax({
				url: '/ybzy/projectChange/listJson'
			})
				.then(res => {
					if (res.results == null) {
						this.tableData = [];
					} else {
						this.tableData = res.results.records;
					}
				})
				.catch(err => {});
		}
	}
};
</script>
<style lang="scss" scoped>
// ::v-deep {
// 	.el-dialog {
// 		height: 100%;
// 	}
// }
::v-deep.dialog-box {
	.el-dialog__header {
		padding: 12px 20px 33px !important;
	}
}
</style>
