<template>
	<div>
		<es-dialog
			title="编辑"
			:visible.sync="visible"
			width="600px"
			height="350px"
			ref="visible"
			@close="handleclose"
		>
			<es-form
				v-if="visible"
				ref="form"
				:model="formData"
				:formatSubmit="false"
				:contents="formItemList"
				enter-submit
				@submit="handleFormSubmit"
				label-position="top"
			></es-form>
		</es-dialog>
	</div>
</template>

<script>
import api from '@/http/dataManage/data-manager';


export default {
	name: 'editFile',

	data() {
		return {
			visible: false,
			formData: {
				classId: null,
				fileName: '',
				fileMat: '',
				fileSize: '',
				remark: '',
				fileSysId: '',
				id: ''
			}
		};
	},
	computed: {
		formItemList() {
			return [
				{
					name: 'journalName',
					row: true,
					placeholder: '分类名称',
					label: '分类名称'
				},
				{
					type: 'switch',
					label: '状态',
					name: 'status',
					value: true,
					placeholder: '',
					data: [
						{
							value: 1,
							name: '启用'
						},
						{
							value: 0,
							name: '禁用'
						}
					]
				}
			];
		}
	},
	methods: {
		open(RowData) {
			this.formData = {...RowData};
			this.visible = true;
		},
		handleFormSubmit() {
			let { journalName, parentId, id, status } = this.formData;
			let param = { journalName, parentId, id, status };
			this.$request({
				url: api.UpdateClassFy,
				data: param,
				format: false,
				method: 'POST'
			}).then(res => {
                this.visible = false
				if (res.rCode === 0) {
					this.$message.success(res.msg);
				}
			});
		},
		handleclose() {}
	}
};
</script>

<style scoped></style>
