export const mixinList = {
	props: {
		// 路由props参数，用于动态化参数
		contentsKey: {
			type: String,
			required: true
		}
	},
	computed: {
		// 动态列表配置、接口、数据
		wd() {
			let obj = {
				basics: {}, // 基本配置
				toolbar: [], // 筛选配置
				thead: [] // 列表配置
			};
			switch (this.contentsKey) {
				// 申报、变更、结题分页列表接口：/projectFlowInfo/listJson
				// 参数：
				// projectClassify->项目分类（字典编码：  project_classify->0：纵向；1：横向；2：院级）
				// @--项目申报-横向项目
				case 'crosswiseProject':
					obj = {
						// 基本配置
						basics: {
							dataTableUrl: '/ybzy/projectBaseInfo/projectApplyPage?projectClassify=1', // 列表接口
							delete: '/ybzy/projectBaseInfo/applyDel', // 删除 removeById逻辑删除 deleteById真删
							revoke: '/ybzy/projectWorkFlow/recallApplyFlow' // 撤销
						},
						// 筛选配置
						toolbar: [
							{
								type: 'button',
								contents: [
									{
										text: '新增',
										code: 'add',
										icon: 'es-icon-xinzeng',
										type: 'primary'
									}
									// {
									// 	text: '导出',
									// 	code: 'export',
									// 	icon: 'es-icon-daochu',
									// 	type: 'primary'
									// }
								]
							},
							{
								type: 'search',
								reset: true,
								contents: [
									{
										type: 'text',
										label: '关键字查询',
										name: 'keyword',
										placeholder: '请输入',
										col: 3
									},
									{
										name: 'cooperationType',
										placeholder: '请选择',
										label: '合作类型',
										type: 'select',
										data: this.codesObj.project_cooperation_type,
										'value-key': 'cciValue',
										'label-key': 'shortName',
										filterable: true
									},
									{
										name: 'contractType',
										placeholder: '请选择',
										label: '合同类型',
										type: 'select',
										data: this.codesObj.project_contract_type,
										'value-key': 'cciValue',
										'label-key': 'shortName',
										filterable: true
									}
								]
							}
							// {
							// 	type: 'filter',
							// 	contents: [
							// 		{
							// 			type: 'text',
							// 			label: '论文名称',
							// 			name: 'thesisName',
							// 			placeholder: '请输入论文名称',
							// 			col: 3
							// 		}
							// 	]
							// }
						],
						// 列表配置
						thead: [
							{
								title: '项目名称',
								minWidth: 120,
								align: 'center',
								field: 'projectName',
								showOverflowTooltip: true
							},
							{
								title: '项目类型',
								align: 'center',
								field: 'projectTypeTxt',
								showOverflowTooltip: true
							},
							// {
							// 	title: '合同签订日期',
							// 	align: 'center',
							// 	field: 'signContractDate',
							// 	showOverflowTooltip: true
							// },
							{
								title: '合作类型',
								align: 'center',
								field: 'cooperationType',
								data: this.codesObj.project_cooperation_type,
								'value-key': 'cciValue',
								'label-key': 'shortName',
								showOverflowTooltip: true
							},
							{
								title: '合同类型',
								align: 'center',
								field: 'contractType',
								data: this.codesObj.project_contract_type,
								'value-key': 'cciValue',
								'label-key': 'shortName',
								showOverflowTooltip: true
							},
							{
								title: '申报时间',
								align: 'center',
								field: 'declareTime',
								showOverflowTooltip: true
							},
							{
								title: '创建人',
								align: 'center',
								field: 'createUserName',
								showOverflowTooltip: true
							},
							{
								title: '学院/部门',
								align: 'center',
								field: 'declareOrgName',
								showOverflowTooltip: true
							},
							{
								title: '创建时间',
								align: 'center',
								width: '180px',
								field: 'createTime',
								showOverflowTooltip: true
							},
							{
								title: '状态',
								fixed: 'right',
								align: 'center',
								field: 'auditStateTxt',
								width: '90px',
								render: (h, params) => {
									let auditStateTxt = params.row.auditStateTxt;
									let auditState = params.row.auditState;
									return h(
										'el-tag',
										{
											props: {
												size: 'small',
												type: this.projecstate(auditState)
											}
										},
										auditStateTxt
									);
								}
							},
							{
								title: '操作',
								type: 'handle',
								width: 210,
								fixed: 'right',
								template: '',
								events: [
									{
										code: 'look',
										icon: 'es-icon-chakan',
										text: '查看'
									},
									{
										code: 'edit',
										text: '编辑',
										icon: 'es-icon-bianji',
										rules: rows => rows.auditState === '0'
									},
									{
										code: 'back',
										text: '撤回',
										icon: 'es-icon-go-back',
										// rules: rows => rows.auditState === '2'
										rules: rows => rows.isApplyRevocable === '1'
									},
									{
										code: 'delect',
										text: '删除',
										icon: 'es-icon-shanchu',
										rules: rows => rows.auditState === '0'
									}
								]
							}
						]
					};
					break;
				// @--项目申报-纵向项目
				case 'verticalProject':
					obj = {
						// 基本配置
						basics: {
							dataTableUrl: '/ybzy/projectBaseInfo/projectApplyPage?projectClassify=0', // 列表接口
							delete: '/ybzy/projectBaseInfo/applyDel', // 删除 removeById逻辑删除 deleteById真删
							revoke: '/ybzy/projectWorkFlow/recallApplyFlow' // 撤销
						},
						// 筛选配置
						toolbar: [
							{
								type: 'button',
								contents: [
									{
										text: '新增',
										code: 'add',
										icon: 'es-icon-xinzeng',
										type: 'primary'
									}
									// {
									// 	text: '导出',
									// 	code: 'export',
									// 	icon: 'es-icon-daochu',
									// 	type: 'primary'
									// }
								]
							},
							{
								type: 'search',
								reset: true,
								contents: [
									{
										type: 'text',
										label: '关键字查询',
										name: 'keyword',
										placeholder: '请输入',
										col: 3
									},

									{
										name: 'projectType',
										label: '项目类型',
										type: 'select',
										data: this.codesObj.project_type,
										'value-key': 'cciValue',
										'label-key': 'shortName',

										col: 3
									},
									{
										name: 'projectLevel',
										label: '项目级别',
										// 纵向-project_level_lengthways、院级-project_level_college）
										type: 'select',
										data: this.codesObj.project_level_lengthways,
										'value-key': 'cciValue',
										'label-key': 'shortName',

										col: 3
									}
								]
							}
							// {
							// 	type: 'filter',
							// 	contents: [
							// 		{
							// 			type: 'text',
							// 			label: '论文名称',
							// 			name: 'thesisName',
							// 			placeholder: '请输入论文名称',
							// 			col: 3
							// 		}
							// 	]
							// }
						],
						// 列表配置
						thead: [
							{
								title: '项目名称',
								minWidth: 120,
								align: 'center',
								field: 'projectName',
								showOverflowTooltip: true
							},
							{
								title: '项目类型',
								align: 'center',
								field: 'projectTypeTxt',
								showOverflowTooltip: true
							},

							{
								title: '项目级别',
								align: 'center',
								field: 'projectLevel',
								// 纵向-project_level_lengthways、院级-project_level_college）
								data: this.codesObj.project_level_lengthways,
								'value-key': 'cciValue',
								'label-key': 'shortName',
								showOverflowTooltip: true
							},
							{
								title: '创建人',
								align: 'center',
								field: 'createUserName',
								showOverflowTooltip: true
							},
							{
								title: '学院/部门',
								align: 'center',
								field: 'declareOrgName',
								showOverflowTooltip: true
							},
							{
								title: '创建时间',
								align: 'center',
								field: 'createTime',
								width: '180px',
								showOverflowTooltip: true
							},
							{
								title: '状态',
								fixed: 'right',
								align: 'center',
								field: 'auditStateTxt',
								width: '90px',
								render: (h, params) => {
									let stateTxt = params.row.auditStateTxt;
									let auditState = params.row.auditState;
									return h(
										'el-tag',
										{
											props: {
												size: 'small',
												type: this.projecstate(auditState)
											}
										},
										stateTxt
									);
								}
							},
							{
								title: '操作',
								type: 'handle',
								width: 210,
								fixed: 'right',
								template: '',
								events: [
									// 0-草稿  1-审核中  2-审核通过  9-驳回
									{
										code: 'look',
										icon: 'es-icon-chakan',
										text: '查看'
									},
									{
										code: 'edit',
										text: '编辑',
										icon: 'es-icon-bianji',
										rules: rows => rows.auditState === '0'
									},
									{
										code: 'back',
										text: '撤回',
										icon: 'es-icon-go-back',
										// rules: rows => rows.auditState === '2'
										rules: rows => rows.isApplyRevocable === '1' 
									},
									{
										code: 'delect',
										text: '删除',
										icon: 'es-icon-shanchu',
										rules: rows => rows.auditState === '0'
									}
								]
							}
						]
					};
					break;
				// @--项目申报-院级项目
				case 'instituteProject':
					obj = {
						// 基本配置
						basics: {
							dataTableUrl: '/ybzy/projectBaseInfo/projectApplyPage?projectClassify=2', // 列表接口
							delete: '/ybzy/projectBaseInfo/applyDel', // 删除 removeById逻辑删除 deleteById真删
							revoke: '/ybzy/projectWorkFlow/recallApplyFlow' // 撤销
						},
						// 筛选配置
						toolbar: [
							{
								type: 'button',
								contents: [
									{
										text: '新增',
										code: 'add',
										icon: 'es-icon-xinzeng',
										type: 'primary'
									}
									// {
									// 	text: '导出',
									// 	code: 'export',
									// 	icon: 'es-icon-daochu',
									// 	type: 'primary'
									// }
								]
							},
							{
								type: 'search',
								reset: true,
								contents: [
									{
										type: 'text',
										label: '关键字查询',
										name: 'keyword',
										placeholder: '请输入关键字查询',
										col: 3
									},
									{
										name: 'projectType',
										label: '项目类型',
										type: 'select',
										data: this.codesObj.project_type,
										'value-key': 'cciValue',
										'label-key': 'shortName'
									},
									{
										name: 'projectLevel',
										label: '项目级别',
										// 纵向-project_level_lengthways、院级-project_level_college）
										type: 'select',
										data: this.codesObj.project_level_college,
										'value-key': 'cciValue',
										'label-key': 'shortName'
									}
								]
							}
							// {
							// 	type: 'filter',
							// 	contents: [
							// 		{
							// 			type: 'text',
							// 			label: '论文名称',
							// 			name: 'thesisName',
							// 			placeholder: '请输入论文名称',
							// 			col: 3
							// 		}
							// 	]
							// }
						],
						// 列表配置
						thead: [
							{
								title: '项目名称',
								minWidth: 120,
								align: 'center',
								field: 'projectName',
								showOverflowTooltip: true
							},
							{
								title: '项目类型',
								align: 'center',
								field: 'projectTypeTxt',
								showOverflowTooltip: true
							},
							{
								title: '项目级别',
								align: 'center',
								field: 'projectLevel',
								data: this.codesObj.project_level_college,
								'value-key': 'cciValue',
								'label-key': 'shortName',
								showOverflowTooltip: true
							},
							{
								title: '申报时间',
								align: 'center',
								field: 'declareTime',
								showOverflowTooltip: true
							},

							{
								title: '创建人',
								align: 'center',
								field: 'createUserName',
								showOverflowTooltip: true
							},
							{
								title: '学院/部门',
								align: 'center',
								field: 'declareOrgName',
								showOverflowTooltip: true
							},
							{
								title: '创建时间',
								align: 'center',
								field: 'createTime',
								width: '180px',
								showOverflowTooltip: true
							},
							{
								title: '状态',
								fixed: 'right',
								align: 'center',
								field: 'auditStateTxt',
								width: '90px',
								render: (h, params) => {
									let auditStateTxt = params.row.auditStateTxt;
									let auditState = params.row.auditState;
									return h(
										'el-tag',
										{
											props: {
												size: 'small',
												type: this.projecstate(auditState)
											}
										},
										auditStateTxt
									);
								}
							},
							{
								title: '操作',
								type: 'handle',
								width: 210,
								fixed: 'right',
								template: '',
								events: [
									// 0-草稿  1-审核中  2-审核通过  9-驳回
									{
										code: 'look',
										icon: 'es-icon-chakan',
										text: '查看'
									},
									{
										code: 'edit',
										text: '编辑',
										icon: 'es-icon-bianji',
										rules: rows => rows.auditState === '0'
									},
									{
										code: 'back',
										text: '撤回',
										icon: 'es-icon-go-back',
										// rules: rows => rows.auditState === '2'
										rules: rows => rows.isApplyRevocable === '1'
									},
									{
										code: 'delect',
										text: '删除',
										icon: 'es-icon-shanchu',
										rules: rows => rows.auditState === '0'
									}
								]
							}
						]
					};
					break;
				default:
					break;
			}
			return obj;
		}
	},
	methods: {
		// 获取项目审核状态
		projecstate(state) {
			let stateCurrent = '';

			// 项目申请状态（0：草稿、1：待审核：2：审核中、3：审核通过、9：驳回）
			switch (state) {
				case '0':
					stateCurrent = 'warning';
					break;
				case '1':
					stateCurrent = 'info';
					break;
				case '3':
					stateCurrent = 'success';
					break;
				case '2':
					stateCurrent = 'primary';
					break;
				case '9':
					stateCurrent = 'danger';
					break;
				default:
					break;
			}

			return stateCurrent;
		}
	}
};
