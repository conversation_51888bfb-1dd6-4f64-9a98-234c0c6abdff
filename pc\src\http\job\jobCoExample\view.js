export default {
	computed: {
		viewItemList() {
			return [
				{
					name: 'enterpriseId',
					label: '企业',
					value: '',
					col: 12,
					readonly: true,
					rules: {
						required: true,
						message: '请输入企业'
					}
				},
				{
					name: 'exampleName',
					label: '案例名称',
					value: '',
					col: 12,
					readonly: true,
					rules: {
						required: true,
						message: '请输入案例名称'
					}
				},
				{
					name: 'exampleIntroduction',
					label: '案例内容',
					value: '',
					type: 'textarea',
					col: 12,
					disabled: true,
					rules: {
						required: true,
						message: '请输入案例内容'
					}
				},
				{
					label: '案例配图',
					name: 'exampleImage',
					type: 'attachment',
					code: 'platform_user_head_sculpture',
					ownId: this.ownId, // 业务id
					disabled: true,
					portrait: true,
					param: {
						isShowPath: true
					},
					col: 12
				},
				{
					name: 'createUser',
					label: '发布人',
					value: '',
					col: 6,
					readonly: true,
					disabled: true,
				},
				{
					name: 'status',
					label: '启用状态',
					value: '',
					type: 'switch',
					col: 12,
					disabled: true,
					data: [
						{
							value: 1,
							text: '启用'
						},
						{
							value: 0,
							text: '禁用',
						}
					]
				},
			]
		}
	}
};
