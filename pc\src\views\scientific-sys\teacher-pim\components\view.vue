<template>
	<div class="teacher-pim">
		<es-form
			ref="formRef"
			v-loading="loading"
			label-width="150px"
			:model="formData"
			:contents="contents"
			table
			:span="3"
			:readonly="formReadonly"
			@submit="handleFormSubmit"
			@click="handleFormClick"
			:key="formKey"
		></es-form>
	</div>
</template>

<script>
export default {
	props: {
		title: {
			type: String,
			default: '查看'
		},
		formId: {
			type: String,
			default: ''
		},
		basics: {
			type: Object,
			default: () => {
				return {
					info: '', // 详情接口
					save: '', // 新增
					edit: '', // 修改
					selectList: '' // 多级学科
				};
			}
		}
	},
	data() {
		return {
			loading: false,
			formData: {
				// member: []
			},
			codesObj: {
				selectList1: [],
				selectList2: [],
				selectList3: []
			}, //批量数据字典
			personList: [],
			formKey: 0
		};
	},
	computed: {
		formReadonly() {
			return this.title.includes('查看');
		},
		formAdd() {
			return this.title.includes('新增');
		},
		contents() {
			return [
				{
					label: '人员类型',
					name: 'type',
					type: 'select',
					// data: this.codesObj.project_member_type,
					'value-key': 'cciValue',
					'label-key': 'shortName',
					col: 3,
					data: [{ cciValue: '0', shortName: '校内人员' }],
					placeholder: '请输入',
					rules: {
						required: !this.formReadonly,
						message: '请选择',
						trigger: 'blur'
					},
					filterable: true
				},
				{
					label: '姓名',
					name: 'name',
					type: 'text',
					hide: this.formAdd,
					col: 3,
					disabled: true,
					rules: {
						required: !this.formReadonly,
						message: '请输入',
						trigger: 'blur'
					}
				},
				{
					label: '姓名',
					// name: this.formReadonly ? 'name' : 'member',
					// type: this.formReadonly ? 'text' : 'select',
					// types: ['employee', 'otheremployee'],
					name: 'nameAndNum',
					type: 'select',
					col: 3,
					multiple: false,
					data: this.personList,
					'value-key': 'nameAndNum',
					'label-key': 'nameAndNum',
					filterable: true,
					hide: !this.formAdd,
					rules: {
						required: true,
						message: '请选择',
						trigger: 'blur'
					},
					events: {
						change: (e, val) => {
							this.personList.forEach(item => {
								if (item.nameAndNum === val) {
									// console.log(item, '选中人时');
									this.$set(this.formData, 'name', item.userName);
									this.$set(this.formData, 'number', item.loginName);
									this.$set(this.formData, 'orgName', item.orgName);
									this.$set(this.formData, 'orgCode', item.orgCode);
									this.$set(this.formData, 'phone', item.phone);
									this.$set(this.formData, 'sex', item.sex);
									this.$set(this.formData, 'idcard', item.idCard);
								}
							});
							// const value = this.formData[e.name];
							// const obj = {
							// 	...value[0],
							// 	...JSON.parse(value[0]?.attr || '{}')
							// };
							// console.log(obj, e, value, '选中人时');
							// this.$set(this.formData, 'name', obj.username);
							// if (!obj.showid) return;
							// this.$set(this.formData, 'number', obj.outcode);
							// this.$set(this.formData, 'orgName', obj.orgName);
							// this.$set(this.formData, 'deptId', obj.orgId);
							// this.$set(this.formData, 'professional', obj.postName);
							// this.$set(this.formData, 'phone', obj.phone);
							// this.$set(this.formData, 'sex', obj.sex);
							// this.$set(this.formData, 'idcard', obj.idcard);
						},
						confirm: e => {}
					}
				},
				{
					label: '英文名字',
					name: 'englishName',
					col: 3,
					placeholder: '请输入',
					type: 'text',

					filterable: true
				},
				{
					label: '曾用名',
					name: 'formerName',
					col: 3,
					placeholder: '请输入',
					type: 'text',
					filterable: true
				},
				{
					label: '教工号',
					name: 'number',
					col: 3,
					placeholder: '请输入',
					type: 'text',
					disabled: true,
					rules: {
						required: !this.formReadonly,
						message: '请选择',
						trigger: 'blur'
					},
					filterable: true
				},
				{
					label: '性别',
					name: 'sex',
					col: 3,
					placeholder: '请选择',
					type: 'select',
					'value-key': 'value',
					'label-key': 'label',
					rules: {
						required: !this.formReadonly,
						message: '请选择',
						trigger: 'blur'
					},
					data: [
						{
							label: '男',
							value: '0'
						},
						{
							label: '女',
							value: '1'
						}
					],
					filterable: true
				},
				{
					label: '出生年月',
					name: 'birthdate',
					col: 3,
					placeholder: '请输入',
					type: 'date',
					rules: {
						required: !this.formReadonly,
						message: '请输入',
						trigger: 'blur'
					},

					filterable: true
				},
				{
					label: '岗位类型',
					name: 'postType',
					placeholder: '请选择',
					type: 'select',
					data: this.codesObj.member_post_type,
					'value-key': 'cciValue',
					'label-key': 'shortName',
					rules: {
						required: !this.formReadonly,
						message: '请输入',
						trigger: 'blur'
					},
					filterable: true
				},
				{
					label: '部门/学院',
					name: 'orgName',
					col: 3,
					placeholder: '请输入',
					disabled: true,
					type: 'text',
					rules: {
						required: !this.formReadonly,
						message: '请选择',
						trigger: 'blur'
					}
				},
				{
					label: '职称',
					name: 'rank',
					col: 3,
					placeholder: '请输入',
					type: 'select',
					data: this.codesObj.member_rank,
					'value-key': 'cciValue',
					'label-key': 'shortName',
					filterable: true,
					rules: {
						required: !this.formReadonly,
						message: '请选择',
						trigger: 'blur'
					}
				},
				{
					label: '科技活动人员类型',
					name: 'activityMemberType',
					placeholder: '请输入',
					type: 'select',
					data: this.codesObj.project_st_activity_member_type,
					'value-key': 'cciValue',
					'label-key': 'shortName',
					filterable: true,
					rules: {
						required: !this.formReadonly,
						message: '请选择',
						trigger: 'blur'
					}
				},
				{
					label: '入职年份',
					placeholder: '请选择',
					name: 'entryDate',
					rules: {
						required: !this.formReadonly,
						message: '请选择',
						trigger: 'blur'
					},
					type: 'date'
				},
				{
					label: '最后学历',
					name: 'highestEducation',
					col: 3,
					placeholder: '请输入',
					type: 'select',
					data: this.codesObj.member_highest_education,
					'value-key': 'cciValue',
					'label-key': 'shortName',
					filterable: true,
					rules: {
						required: !this.formReadonly,
						message: '请输入',
						trigger: 'blur'
					}
				},
				{
					label: '最后学位',
					name: 'highestDegree',
					col: 3,
					placeholder: '请输入',
					type: 'select',
					data: this.codesObj.member_highest_degree,
					'value-key': 'cciValue',
					'label-key': 'shortName',
					rules: {
						required: !this.formReadonly,
						message: '请输入',
						trigger: 'blur'
					},
					filterable: true
				},
				{
					label: '技术职务',
					name: 'technicalPosition',
					col: 3,
					placeholder: '请输入',
					type: 'select',
					data: this.codesObj.project_technical_position,
					'value-key': 'cciValue',
					'label-key': 'shortName',
					filterable: true,
					rules: {
						required: !this.formReadonly,
						message: '请选择',
						trigger: 'blur'
					}
				},
				{
					label: '职务类别',
					name: 'positionType',
					col: 3,
					placeholder: '请输入',
					type: 'select',
					data: this.codesObj.member_position_type,
					'value-key': 'cciValue',
					'label-key': 'shortName',
					rules: {
						required: !this.formReadonly,
						message: '请输入',
						trigger: 'blur'
					}
				},
				{
					label: '证件类型',
					name: 'idcardType',
					col: 3,
					placeholder: '请输入',
					type: 'select',
					data: this.codesObj.member_idcard_type,
					'value-key': 'cciValue',
					'label-key': 'shortName',
					rules: {
						required: !this.formReadonly,
						message: '请输入',
						trigger: 'blur'
					},
					filterable: true
				},
				{
					label: '证件号码',
					name: 'idcard',
					col: 3,
					placeholder: '请输入',
					type: 'text',
					rules: {
						required: !this.formReadonly,
						message: '请输入',
						trigger: 'blur'
					}
				},
				{
					label: '一级学科',
					name: 'firstLevelDiscipline',
					col: 3,
					placeholder: '请选择',
					type: 'select',
					data: this.codesObj.selectList1,
					'value-key': 'value',
					'label-key': 'label',
					rules: {
						required: !this.formReadonly,
						message: '请输入',
						trigger: 'blur'
					},
					events: {
						change: (e, v) => {
							this.formData.subDiscipline = '';
							this.formData.tertiaryDiscipline = '';
							this.codesObj.selectList2 = [];
							this.codesObj.selectList3 = [];
							this.getDiscipline(v, '2');
						}
					},
					filterable: true
				},
				{
					label: '二级学科',
					name: 'subDiscipline',
					col: 3,
					placeholder: '请先选择一级学科',
					type: 'select',
					data: this.codesObj.selectList2,
					'value-key': 'value',
					'label-key': 'label',
					// rules: {
					// 	required: !this.formReadonly,
					// 	message: '请输入',
					// 	trigger: 'blur'
					// },
					events: {
						change: (e, v) => {
							this.formData.tertiaryDiscipline = '';
							this.codesObj.selectList3 = [];
							this.getDiscipline(v, '3');
						}
					},
					filterable: true
				},
				{
					label: '三级学科',
					name: 'tertiaryDiscipline',
					col: 3,
					placeholder: '请先选择三级学科',
					type: 'select',
					data: this.codesObj.selectList3,
					'value-key': 'value',
					'label-key': 'label',
					// rules: {
					// 	required: !this.formReadonly,
					// 	message: '请输入',
					// 	trigger: 'blur'
					// },
					filterable: true,
					events: {
						change: (e, v) => {
							this.formKey++;
						}
					}
				},
				// {
				// 	label: '相关学科',
				// 	name: 'subject',
				// 	col: 3,
				// 	placeholder: '请输入',
				// 	type: 'text',
				// 	rules: {
				// 		required: !this.formReadonly,
				// 		message: '请输入',
				// 		trigger: 'blur'
				// 	},
				// 	filterable: true
				// },
				{
					label: '所属学科',
					// name: 'relatedDiscipline',
					name: 'firstLevelDiscipline',
					col: 3,
					placeholder: '请输入',
					type: 'select',
					data: this.codesObj.selectList1,
					'value-key': 'value',
					'label-key': 'label',
					rules: {
						required: !this.formReadonly,
						message: '请输入',
						trigger: 'blur'
					}
				},
				{
					label: '政治面貌',
					name: 'politicalStatus',
					col: 3,
					placeholder: '请输入',
					type: 'select',
					data: this.codesObj.member_political_status,
					'value-key': 'cciValue',
					'label-key': 'shortName',
					rules: {
						required: !this.formReadonly,
						message: '请输入',
						trigger: 'blur'
					}
				},
				{
					label: '研究方向',
					name: 'researchDirection',
					col: 3,
					placeholder: '请输入',
					type: 'text',
					rules: {
						required: !this.formReadonly,
						message: '请输入',
						trigger: 'blur'
					}
				},
				{
					label: 'E-mail',
					name: 'email',
					col: 3,
					placeholder: '请输入',
					type: 'text',
					filterable: true
				},
				{
					label: '行政职务',
					name: 'administrativePost',
					col: 3,
					placeholder: '请输入',
					type: 'text',
					filterable: true
				},
				{
					label: '是否在编',
					name: 'isPermanentStaff',
					type: 'select',
					'value-key': 'value',
					'label-key': 'label',
					rules: {
						required: !this.formReadonly,
						message: '请选择',
						trigger: 'blur'
					},
					data: [
						{
							label: '否',
							value: '0'
						},
						{
							label: '是',
							value: '1'
						}
					],
					filterable: true
				},
				{
					label: '定职时间',
					name: 'confirmPostDate',
					placeholder: '请选择',
					col: 3,
					type: 'date'
				},
				{
					label: '国籍',
					name: 'nationality',
					col: 3,
					placeholder: '请输入',
					type: 'select',
					data: this.codesObj.member_nationality,
					'value-key': 'cciValue',
					'label-key': 'shortName',
					rules: {
						required: !this.formReadonly,
						message: '请输入',
						trigger: 'blur'
					},
					filterable: true
				},
				{
					label: '民族',
					name: 'nation',
					col: 3,
					placeholder: '请输入',
					type: 'select',
					data: this.codesObj.member_nation,
					'value-key': 'cciValue',
					'label-key': 'shortName',
					rules: {
						required: !this.formReadonly,
						message: '请输入',
						trigger: 'blur'
					},
					filterable: true
				},

				{
					label: '联系电话',
					name: 'phone',
					col: 3,
					placeholder: '请输入',
					type: 'text',
					rules: {
						required: !this.formReadonly,
						message: '请选择',
						trigger: 'blur'
					}
				},
				{
					label: '办公电话',
					name: 'officePhone',
					col: 3,
					placeholder: '请输入',
					type: 'text'
				},
				// {
				// 	label: '承担类型',
				// 	name: 'assumeType',
				// 	// col: 12,
				// 	placeholder: '请输入',
				// 	type: 'select',
				// 	rules: {
				// 		required: !this.formReadonly,
				// 		message: '请选择',
				// 		trigger: 'blur'
				// 	},
				// 	data: this.codesObj.member_assume_type,
				// 	'label-key': 'shortName',
				// 	'value-key': 'cciValue',
				// 	// sysCode: 'project_assume_type',
				// 	filterable: true
				// },
				{
					label: '个人网址',
					name: 'personalWebsite',
					col: 3,
					placeholder: '请输入',
					type: 'text'
				}
			];
		}
	},
	watch: {
		formId: {
			handler(newVal, oldVal) {
				this.getInfo(newVal);
			},
			immediate: true
		}
	},
	created() {
		this.getPersonList();
	},
	methods: {
		getPersonList() {
			this.$request({
				url: 'ybzy/projectMemberInfoTem/personList'
			}).then(res => {
				this.personList = res.results;
			});
		},
		// 批量请求数据字典
		getSysCode() {
			//人员类型 project_member_type
			//证件类型 member_idcard_type
			//承担类型 member_assume_type
			//职称 member_rank
			//国籍 member_nationality
			//民族 member_nation
			//政治面貌 member_political_status
			//职务类别 member_position_type
			//岗位类型 member_post_type
			//最后学历 member_highest_education
			//最后学位 member_highest_degree
			// const codes = 'project_type,project_cooperation_type';
			const codes =
				'member_assume_type,member_rank,project_member_type,member_idcard_type,member_nationality,member_nation,member_political_status,member_position_type,member_post_type,member_highest_education,member_highest_degree,project_st_activity_member_type,project_technical_position';
			this.$utils.findSysCodeList(codes).then(res => {
				this.codesObj = { ...this.codesObj, ...res };
			});
		},
		// 请求学科
		getDiscipline(parent = '', level = '') {
			//type 类型  parent 父级编码  level 层级
			this.$request({
				url: this.basics.selectList,
				params: { type: 'discipline', parent, level }
				// method: 'POST'
			}).then(res => {
				if (res.rCode == 0) {
					const arr = res.results;
					switch (level) {
						case '1':
							this.codesObj.selectList1 = arr;
							break;
						case '2':
							this.codesObj.selectList2 = arr;
							break;
						case '3':
							this.codesObj.selectList3 = arr;
							break;
						default:
							break;
					}
				} else {
					this.$message.error(res.msg);
				}
			});
		},

		handleFormSubmit() {
			const loading = this.$.loading(this.$loading, '提交中...');
			let formData = {
				...this.formData,
				subject: this.formData.firstLevelDiscipline,
				assumeType: '0'
			};
			delete formData.member;
			this.$.ajax({
				url: this.title.includes('新增') ? this.basics.save : this.basics.edit,
				method: 'post',
				data: formData
				// format: false
			}).then(res => {
				loading.close();
				if (res.rCode == 0) {
					this.$message.success(res.msg);
					this.$emit('close', false);
				} else {
					this.$message.error(res.msg);
				}
			});
		},
		handleFormClick(submitType) {
			console.log('submitType', submitType);
			// console.log(this.$refs.form.$children);
			// if (submitType.text == '保存') {
			// 	this.$refs.form.$children[0].validate(val => {
			// 		console.log(val);
			// 	});
			// }
		},
		getInfo(id) {
			this.getSysCode();
			this.getDiscipline('', '1'); // 获取一级学科
			if (id) {
				this.loading = true;
				this.$request({
					url: this.basics.info,
					params: { id }
					// method: 'POST'
				}).then(res => {
					if (res.rCode == 0) {
						this.loading = false;
						const obj = res.results;
						this.getDiscipline(obj.firstLevelDiscipline, '2'); // 获取二级学科
						this.getDiscipline(obj.subDiscipline, '3'); // 获取三级学科

						this.formData = {
							...obj,
							member: [
								{
									showname: obj.name
								}
							]
						};
					} else {
						this.$message.error(res.msg);
					}
				});
			} else {
				const testData = {
					// id: '50d32553-5529-4d43-a3b0-802c054c8dff',
					// member: [
					// 	{
					// 		showid: '24abe5b77c0c41d385b8937881caf49d',
					// 		showname: '张灵(教师)',
					// 		showshortname: '',
					// 		stype: 'employee',
					// 		stypename: null,
					// 		pathfilid: 'en6e0ed8bb0a8240378f46be827040918a',
					// 		pathname: '教务处-教务处',
					// 		attr: '{"userId":"24abe5b77c0c41d385b8937881caf49d","oldUserId":null,"outcode":"*********","username":"张灵","orgId":"en6e0ed8bb0a8240378f46be827040918a","orgpId":null,"orgCode":null,"orgName":"教务处","creditCode":null,"orgShortName":"教务处","hideUnits":0,"orgBusinessName":null,"depId":"dep9dc3a464d0e949568df270cb9e4ad13c","olddepId":null,"depCode":null,"depName":"教学管理与质量处","depShortName":"教务处","loginName":"*********","phone":"***********","email":"","idcard":"512501197511061962","officetel":"","cakey":"zl1962","postId":"cfec2fe11bd548a7b71590dce13ce994","postName":"教师","subcenterId":"","subcenterName":null,"position":"","loginid":"8993dfced9254c928d8f2a7b983cefd5","personid":"em4f499647e5ed4009bb287983ffd5a851","userpersonid":null,"sex":"1","pwd":null,"oldpwd":null,"sort":0,"plsort":0,"entrustUserId":null,"isaddress":1,"isofficetel":0,"ischooser":0,"state":1,"officeId":null,"officeCode":null,"officeName":null,"officefullName":null,"addn":null,"adobjecid":null,"telshort":null,"type":"0","roleRange":null,"userRolelist":null,"usrResource":null,"birthface":null,"workingyears":null,"orgStructureType":null,"subsystemids":null,"orgpath":null,"tenantid":null,"tenantfullName":null,"tenantshortName":null,"txtbornDate":null,"historystate":0,"otherPostLevel":0,"updateorgRootPath":null,"extendList":{}}',
					// 		strdisabled: 'false'
					// 	}
					// ],
					// number: '*********',
					// name: '张灵',
					// orgName: '教务处',
					// deptId: 'en6e0ed8bb0a8240378f46be827040918a',
					// professional: '教师',
					// phone: '***********',
					// sex: '1',
					// idcard: '512501197511061962',
					// type: '0',
					// englishName: 'zhangning',
					// postType: '1',
					// politicalStatus: '1',
					// relatedDiscipline: '无',
					// highestDegree: '1',
					// highestEducation: '1',
					// formerName: '无',
					// rank: '无',
					// birthdate: '2024-08-22',
					// activityMemberType: '无',
					// entryDate: '2024-08-30',
					// technicalPosition: '无',
					// idcardType: '1',
					// positionType: '1',
					// isPermanentStaff: '0',
					// subDiscipline: '',
					// firstLevelDiscipline: '',
					// subject: '无',
					// tertiaryDiscipline: '',
					// email: '无',
					// researchDirection: '无',
					// administrativePost: '无',
					// nation: '1',
					// nationality: '1',
					// confirmPostDate: '2024-08-30',
					// officePhone: '无',
					// personalWebsite: '无'
				};
				this.formData = {
					...testData,
					id: this.$uuidv4()
				};
			}
		}
	}
};
</script>

<style lang="scss" scoped>
.teacher-pim {
	width: 100%;
	height: 100%;
	// padding: 20px 60px;
	// padding: 20px 30px;
}
</style>
