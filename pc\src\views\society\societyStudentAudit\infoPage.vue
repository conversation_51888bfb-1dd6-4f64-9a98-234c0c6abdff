<template>
  <div class="sketch_content">
    <el-form :model="formData" class="resumeTable" :rules="rules" ref="form">
      <el-row>
        <el-col :span="11">
          <el-form-item label="社团" label-width="90px" label-position="left" prop="society">
            <el-select v-model="formData.society" value-key="value" :disabled="infoDisabled" :remote-method="getSocieties"
                       placeholder="输入名称查询" filterable clearable remote>
              <el-option
                  v-for="item in sociSelectList"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value">
              </el-option>
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="11">
          <el-form-item label="申请人" label-width="90px" label-position="left" prop="applyUser">
            <el-select v-model="formData.applyUser" value-key="value" :disabled="infoDisabled" :remote-method="getStudent"
                       placeholder="输入姓名查询" filterable clearable remote>
              <el-option
                  v-for="item in studentSelectList"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value">
              </el-option>
            </el-select>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row>
        <el-col :span="11">
          <el-form-item label="申请" label-width="90px" label-position="left" prop="applyType">
            <el-select v-model="formData.applyType" :disabled="infoDisabled">
              <el-option value="1" label="入社"></el-option>
              <el-option value="2" label="退社"></el-option>
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="11">
          <el-form-item label="申请时间" label-width="90px" label-position="left" v-show="infoVisible" prop="operateTime">
            <el-date-picker v-model="formData.operateTime" type="datetime" :value-format="dateformat" :disabled="infoDisabled"></el-date-picker>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row>
        <el-col :span="11">
          <el-form-item label="所属学院" label-width="90px" label-position="left" v-show="infoVisible" prop="userCollege">
            <el-input v-model="formData.applyUserCollege" :disabled="infoDisabled"></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="11">
          <el-form-item label="所属班级" label-width="90px" label-position="left" v-show="infoVisible" prop="userClass">
            <el-input v-model="formData.applyUserClass" :disabled="infoDisabled"></el-input>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row>
        <el-col :span="22" v-show="auditVisible" :disabled="auditDisabled" >
          <el-form-item label="审核意见" label-width="90px" label-position="left" prop="auditOpinion">
            <el-input v-model="formData.auditOpinion" :disabled="auditDisabled" type="textarea" ></el-input>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row><br></el-row>
      <el-row>
        <el-col :span="3" style="float:right">
          <el-button type="reset" @click="infoPageClose(false)">取消</el-button>
        </el-col>
        <el-col :span="3" style="float:right">
          <el-button type="primary" @click="handleFormSubmit" v-show="!auditBtnVisible">保存</el-button>
        </el-col>
        <el-col :span="3" style="float:right" v-show="auditBtnVisible">
          <el-button type="danger" @click="handleFormAudit('驳回')" v-show="auditBtnVisible">驳回</el-button>
        </el-col>
        <el-col :span="4" style="float:right" v-show="auditBtnVisible">
          <el-button type="primary" @click="handleFormAudit('审核通过')" v-show="auditBtnVisible">审核通过</el-button>
        </el-col>
      </el-row>
    </el-form>
  </div>
</template>
<script>

import api from "@/http/society/societyStudentAudit/api";

export default {
  name: 'infoPage',
  props: {
    baseData: {
      type: Object
    },
    infoPageMode: {
      type: String
    }
  },
  data() {
    return {
      infoDisabled: false,
      infoVisible: true,
      auditDisabled: true,
      auditVisible: true,
      auditBtnVisible: false,
      formData: {},
      pageMode: 'allOn',
      tpSelectList: [],
      studentSelectList: [],
      sociSelectList: [],
      uploadFileList: [],
      dateformat: 'yyyy-MM-dd HH:mm:ss',
    };
  },
  computed: {
    rules() {
      return{
        society: [{required: true, message: '请选择社团', trigger: 'blur'}],
        applyUser: [{required: true, message: '请选择申请人', trigger: 'blur'}],
        applyType: [{required: true, message: '请选择申请类型', trigger: 'blur'}],
        auditOpinion: [{ required: this.auditBtnVisible, message: '请输入审核意见', trigger: 'blur' }],
      }
    },
  },
  created() {
    this.formData = { ...this.baseData };
    this.pageMode = this.infoPageMode;
    switch (this.pageMode){
      case '审核':
        this.infoDisabled = true;
        this.auditDisabled = false;
        this.auditVisible = true;
        this.auditBtnVisible = true;
        break;
      case 'allOn':
      case '新增申请':
        this.infoDisabled = false;
        this.infoVisible = false;
        this.auditDisabled = true;
        this.auditVisible = false;
        this.auditBtnVisible = false;
        break;
      case '查看':
        this.infoDisabled = true;
        this.auditDisabled = true;
        this.auditVisible = true;
        this.auditBtnVisible = false;
        break;
    }
  },
  methods: {
    handleFormSubmit() {
      console.log(this.formData);
      this.$refs.form.validate((valid) => {
        if(valid){
          let saveData = {...this.formData};
          //处理数据
          saveData.status = saveData.status === true ? 1:0;
          if(typeof saveData.society == 'object') saveData.societyId = saveData.society.value;
          else saveData.societyId = saveData.society;
          if(typeof saveData.applyUser == 'object') saveData.applyUserId = saveData.applyUser.value;
          else saveData.applyUserId = saveData.applyUser;

          this.$request({
            url:
                this.pageMode === '新增申请'
                    ? api.societyStudentAuditSave
                    : api.societyStudentAuditUpdate,
            data: saveData,
            method: 'POST'
          }).then(res => {
            if (res.rCode === 0) {
              this.$message.success('操作成功');
              this.infoPageClose(true);
            } else {
              this.$message.error(res.msg);
            }
          });
        }
      })
    },
    handleFormAudit(res){
      //校验
      this.$refs['form'].validate((valid) => {  //开启校验
        if (valid) {   // 如果校验通过，请求接口
          let id = this.formData.id;
          let btnType = res;
          let auditStatus = -1;
          let auditOpinion = this.formData.auditOpinion;

          this.$confirm('是否确认'+btnType+'？', '审核', {
            confirmButtonText: '确定',
            cancelButtonText: '取消',
            type: 'warning'
          })
              .then(() => {
                switch (btnType){
                  case '审核通过': auditStatus = 2;break;
                  case '驳回': auditStatus = 3;break;
                }
                if(auditStatus !== -1){
                  this.$request({
                    url: api.societyStudentAuditAudit,
                    data:{id: id,auditStatus: auditStatus, auditOpinion: auditOpinion},
                    method: 'POST'
                  }).then(response =>{
                    if(response.success){
                      this.$message.success(response.msg);
                      this.infoPageClose(true);
                    }else {
                      this.$message.error(response.msg);
                    }
                  })
                }
              }).catch(() => {});
        } else { return false; }//校验不通过
      });},
    infoPageClose(reload){
      this.pageMode = 'allOn';
      this.infoDisabled = false;
      this.$emit('activelyClose',reload);
    },
    closeMapDialog() {
      this.showMapIntLo = false;
    },
    getStudent(res) {
      this.$request({
        url: api.studentList,
        params: { name: res, },
        method: 'GET'
      }).then(result => {
        if(result.success){
          this.studentSelectList = result.results;
        }else {
          this.$message.error('操作失败');
        }
      });
    },
    getSocieties(res) {
      this.$request({
        url: api.societyList,
        params: { label: res, pageNum: 1, pageSize: 10 },
        method: 'GET'
      }).then(res => {
        if(res.results != null){
          if(res.success){
            this.sociSelectList = [...res.results.records];
          }else {
            this.$message.error(res.msg);
          }
        }
      })
    },
  },
  watch: {
  }
};
</script>
<style scoped>
  .sketch_content {
    overflow: auto;
    height: auto;
    border-top: 1px solid #eff1f4;
    border-bottom: 1px solid #eff1f4;
    padding: 0 30px 11px 27px;
  }
  .resumeTable td {
    height: 50px;
    font-weight: bold;
  }
</style>