<template>
	<div class="content">
		<es-data-table
			ref="table"
			:row-style="tableRowClassName"
			:full="true"
			:fit="true"
			:thead="thead"
			:toolbar="toolbar"
			:border="true"
			:page="pageOption"
			:url="dataTableUrl"
			:option-data="optionData"
			:numbers="true"
			:param="params"
			close
			form
			@btnClick="btnClick"
			@sort-change="sortChange"
			@submit="hadeSubmit"
		></es-data-table>
		<es-dialog
			:title="formTitle"
			:visible.sync="showForm"
			width="60%"
			:drag="false"
			:close-on-click-modal="false"
			:destroy-on-close="true"
		>
			<el-form
				ref="form"
				v-if="showForm"
				:inline="true"
				label-position="right"
				label-width="100px"
				:model="formData"
				:rules="formRules"
			>
				<el-form-item label="库房" prop="storeroomId">
					<el-select
						v-model="formData.storeroomId"
						placeholder="请选择库房"
						:disabled="formTitle !== '新增'"
						@change="changeStoreroom"
					>
						<el-option
							v-for="item in storeroomOptions"
							:key="item.id"
							:label="item.name"
							:value="item.id"
						></el-option>
					</el-select>
				</el-form-item>
				<el-form-item label="盘点时间" prop="inventoryTime">
					<el-date-picker
						v-model="formData.inventoryTime"
						type="datetime"
						value-format="yyyy-MM-dd HH:mm:ss"
						placeholder="选择入库时间"
						:disabled="formTitle !== '新增'"
					></el-date-picker>
				</el-form-item>
				<el-form-item label="盘点人" prop="userName">
					<el-input v-model="formData.userName" :disabled="formTitle !== '新增'"></el-input>
				</el-form-item>
			</el-form>
			<el-button
				size="mini"
				style="background-color: #0076e9; border-color: #0076e9; color: #ffffff"
				v-if="formTitle !== '查看'"
				@click="doShowMaterial"
			>
				添加原料
			</el-button>
			<div style="display: flex; flex-direction: column; align-items: center; height: 410px">
				<h3 style="margin-bottom: 10px">原料单</h3>
				<el-table :data="formData.details" border size="mini" style="width: 100%; height: 100%">
					<el-table-column label="序号" type="index" align="center"></el-table-column>
					<el-table-column
						prop="materialCode"
						label="原料编号"
						align="center"
						:show-overflow-tooltip="true"
					></el-table-column>
					<el-table-column
						prop="materialName"
						label="原料名称"
						align="center"
						:show-overflow-tooltip="true"
					></el-table-column>
					<el-table-column
						prop="brand"
						label="供应商"
						align="center"
						:show-overflow-tooltip="true"
					></el-table-column>
					<el-table-column
						prop="specification"
						label="规格"
						align="center"
						:show-overflow-tooltip="true"
					></el-table-column>
					<el-table-column prop="unit" label="库存单位" align="center"></el-table-column>
					<el-table-column prop="residueNum" label="库存量" align="center"></el-table-column>
					<!-- <el-table-column prop="num" label="盘点库存量">
						<template slot-scope="scope">
							<el-input-number
								v-model="scope.row.num"
								placeholder="请输入数量"
								:controls="false"
								:min="1"
								size="small"
								style="width: 100px"
								:disabled="formTitle === '查看'"
							></el-input-number>
						</template>
					</el-table-column> -->
				</el-table>
			</div>
			<div
				style="
					display: flex;
					flex-direction: row;
					justify-content: center;
					padding-top: 5px;
					padding-right: 20px;
				"
			>
				<el-button
					size="mini"
					style="background-color: #f5f5f5; border-color: #d9d9d9; color: #000000"
					@click="submit('0')"
					v-if="formTitle !== '查看'"
				>
					暂 存
				</el-button>
				<el-button
					size="mini"
					style="background-color: #0076e9; border-color: #0076e9; color: #ffffff"
					@click="submit('1')"
					v-if="formTitle !== '查看'"
				>
					确 定
				</el-button>
				<el-button
					size="mini"
					style="background-color: #f5f5f5; border-color: #d9d9d9; color: #000000"
					@click="showForm = false"
				>
					取 消
				</el-button>
			</div>
		</es-dialog>

		<es-dialog
			title="原料列表"
			:visible.sync="showMaterial"
			width="60%"
			:drag="false"
			:close-on-click-modal="false"
			:middle="true"
		>
			<es-data-table
				ref="materialTable"
				:row-style="tableRowClassName"
				checkbox
				:full="true"
				:fit="true"
				:thead="materialThead"
				:toolbar="materialToolbar"
				:border="true"
				:page="pageOption"
				:url="materialDataUrl"
				:option-data="materialOptionData"
				:numbers="true"
				:param="params"
				close
				form
				v-if="showMaterial"
			></es-data-table>
		</es-dialog>
	</div>
</template>

<script>
import interfaceUrl from '@/http/logistics/inventorySheet.js';
import supplierApi from '@/http/logistics/supplier.js';
import storeroomApi from '@/http/logistics/hqStoeroom.js';
import materialApi from '@/http/logistics/material/api.js';
import materialCategoryApi from '@/http/logistics/materialcategory.js';
import SnowflakeId from 'snowflake-id';
import { host } from '../../../../config/config';

const snowflake = new SnowflakeId();

export default {
	data() {
		return {
			ownId: '',
			dataTableUrl: interfaceUrl.listJson,
			showForm: false,
			formTitle: '编辑',
			editBtn: {
				type: 'submit',
				skin: 'lay-form-btns',
				contents: [
					{
						type: 'primary',
						text: '确定',
						event: 'confirm'
					},
					{
						type: 'reset',
						text: '取消',
						event: 'cancel'
					}
				]
			},
			cancelBtn: {
				type: 'submit',
				skin: 'lay-form-btns',
				contents: [
					{
						type: 'reset',
						text: '取消',
						event: 'cancel'
					}
				]
			},
			thead: [
				{
					title: '库房名称',
					field: 'storeroomName',
					align: 'center',
					showOverflowTooltip: true
				},
				{
					title: '盘点单号',
					field: 'id',
					align: 'center',
					showOverflowTooltip: true
				},
				{
					title: '盘点人',
					field: 'userName',
					align: 'center',
					showOverflowTooltip: true
				},
				{
					title: '盘点时间',
					field: 'inventoryTime',
					align: 'center',
					showOverflowTooltip: true
				},
				{
					title: '盘点状态',
					align: 'center',
					field: 'status',
					type: 'switch',
					readonly: true
				},
				{
					title: '操作',
					type: 'handle',
					width: 180,
					template: '',
					events: [
						{
							code: 'view',
							text: '查看'
						},
						{
							code: 'edit',
							text: '编辑',
							rules: rows => {
								return rows.status === '0';
							}
						},
						{
							code: 'download',
							text: '导出'
						}
					]
				}
			],
			optionData: {
				status: [
					{ name: '待提交', value: '0' },
					{ name: '已盘点', value: '1' }
				]
			},
			pageOption: true,
			params: {
				asc: 'false',
				orderBy: 'createTime'
			},
			formData: {
				storeroomId: null,
				inventoryTime: null,
				userName: null,
				details: []
			},
			formRules: {
				inventoryTime: [{ required: true, message: '请选择盘点时间', trigger: 'change' }],
				storeroomId: [{ required: true, message: '请选择库房', trigger: 'change' }],
				userName: [{ required: true, message: '请填写盘点人', trigger: 'blur' }]
			},
			supplierOptions: [], //供应商选择列表
			storeroomOptions: [], //库房选择列表

			curMaterialIds: [], //当前选择的原料(用于重新选择时去重)
			showSubmit: false,
			showMaterial: false,
			materialDataUrl: materialApi.listJson,
			materialToolbar: [
				{
					type: 'button',
					contents: [
						{
							text: '确定',
							code: 'selected',
							type: 'primary',
							event: res => {
								this.selMaterial(res.ele.selected);
								this.showMaterial = false;
							}
						},
						{
							text: '取消',
							code: 'cancel',
							event: res => {
								this.showMaterial = false;
							}
						}
					]
				},
				{
					type: 'search',
					reset: true,
					contents: [
						{
							type: 'text',
							name: 'name',
							placeholder: '原料名称'
						},
						{
							type: 'select',
							name: 'categoryId',
							placeholder: '原料分类',
							valueType: 'string',
							default: true,
							parentCheck: true,
							tree: true,
							url: '/ybzy/hqbasematerialcategory/getTreeListOne',
							'value-key': 'id',
							'label-key': 'name'
						}
					]
				}
			],
			materialThead: [
				{
					title: '原料编号',
					field: 'code',
					align: 'center',
					showOverflowTooltip: true
				},
				{
					title: '原料名称',
					field: 'name',
					align: 'center',
					showOverflowTooltip: true
				},
				{
					title: '原料分类',
					field: 'categoryName',
					align: 'center',
					showOverflowTooltip: true
				},
				{
					title: '原料单位',
					field: 'unit',
					align: 'center',
					showOverflowTooltip: true
				},
				{
					title: '规格',
					field: 'specification',
					align: 'center',
					showOverflowTooltip: true
				},
				{
					title: '供应商',
					field: 'brand',
					align: 'center',
					showOverflowTooltip: true
				}
			],
			materialOptionData: {
				categoryId: []
			}
		};
	},
	computed: {
		toolbar() {
			return [
				{
					type: 'button',
					contents: [
						{
							text: '新增盘点单',
							code: 'add',
							type: 'primary'
						}
					]
				},
				{
					type: 'filter',
					reset: true,
					contents: [
						{
							type: 'select',
							label: '库房',
							name: 'roomId',
							col: 4,
							clearable: true,
							data: this.storeroomOptions.map(i => {
								return {
									name: i.name,
									value: i.id
								};
							})
						},
						{ type: 'text', label: '盘点人', name: 'userName', col: 4, clearable: true },
						{
							type: 'date',
							label: '开始时间',
							name: 'start',
							col: 4,
							clearable: true,
							value: '',
							rules: {
								required: false,
								message: '请选择开始时间',
								trigger: 'change'
							}
						},
						{
							type: 'date',
							label: '结束时间',
							name: 'end',
							col: 4,
							clearable: true,
							value: '',
							rules: {
								required: false,
								message: '请选择开始结束时间',
								trigger: 'change'
							}
						}
					]
				}
			];
		}
	},
	watch: {},
	created() {
		this.supplierList();
		this.storeroomList();
		this.categoryList();
	},
	mounted() {},
	methods: {
		/**
		 * 表格行高
		 */
		tableRowClassName({ row, rowIndex }) {
			let styleRes = {
				height: '54px !important'
			};
			return styleRes;
		},
		inputChange(key, value) {},
		hadeSubmit(data) {},
		/**
		 * 操作按钮点击事件
		 * @param {*} res
		 */
		btnClick(res) {
			let code = res.handle.code;
			switch (code) {
				case 'add':
					// 新增
					this.formTitle = '新增';
					this.ownId = snowflake.generate();
					this.curMaterialIds = [];
					this.formData = {
						id: this.ownId,
						storeroomId: null,
						inventoryTime: null,
						userName: null,
						details: []
					};
					this.showForm = true;
					break;
				case 'edit':
					// 编辑
					this.formTitle = '编辑';
					this.ownId = res.row.id;
					this.$request({
						url: interfaceUrl.info + '/' + res.row.id,
						method: 'GET'
					}).then(res => {
						if (res.rCode === 0) {
							this.curMaterialIds = [];
							this.formData = res.results;
							let details = this.formData.details;
							for (let item of details) {
								this.curMaterialIds.push(item.materialId);
							}
							this.showForm = true;
						}
					});
					break;
				case 'view':
					this.formTitle = '查看';
					this.ownId = res.row.id;
					this.$request({
						url: interfaceUrl.info + '/' + res.row.id,
						method: 'GET'
					}).then(res => {
						if (res.rCode == 0) {
							this.formData = res.results;
							this.showForm = true;
						}
					});
					break;
				case 'download':
					window.open(host + interfaceUrl.download + '?id=' + res.row.id, '_self');
					break;
				default:
					break;
			}
		},
		handleClose() {},
		handleFormItemChange() {},
		handleFormSubmit(data) {
			let formData = data;
			let url = '';
			if (this.formTitle === '新增') {
				url = interfaceUrl.save;
			} else {
				url = interfaceUrl.update;
			}
			this.$request({
				url: url,
				data: formData,
				method: 'POST',
				format: false
			}).then(res => {
				if (res.rCode === 0) {
					this.$message.success('操作成功');
					this.showForm = false;
					this.formData = {};
					this.$refs.table.reload();
				} else {
					this.$message.error(res.msg);
				}
			});
		},
		/**
		 * 排序变化事件
		 * @param {*} column
		 * @param {*} prop
		 * @param {*} order
		 */
		sortChange(column, prop, order) {
			if (column.order == 'ascending') {
				//升序
				this.params = {
					asc: 'true',
					orderBy: column.prop
				};
			} else if (column.order == 'descending') {
				//降序
				this.params = {
					asc: 'false',
					orderBy: column.prop
				};
			} else {
				//不排序
				this.params = {
					asc: 'false',
					orderBy: 'createTime'
				};
			}
			this.$refs.table.reload();
		},
		supplierList() {
			this.$request({
				url: supplierApi.listAll,
				method: 'GET'
			}).then(res => {
				if (res.rCode === 0) {
					this.supplierOptions = res.results;
				}
			});
		},
		storeroomList() {
			this.$request({
				url: storeroomApi.listAll,
				method: 'GET'
			}).then(res => {
				if (res.rCode === 0) {
					this.storeroomOptions = res.results;
				}
			});
		},
		categoryList() {
			this.$request({
				url: materialCategoryApi.categoryListSel,
				method: 'POST'
			}).then(res => {
				if (res.rCode === 0) {
					this.materialOptionData.categoryId = res.results.records;
				}
			});
		},
		changeStoreroom(val) {
			this.formData.details = [];
			this.curMaterialIds = [];
		},
		//点击添加原料按钮
		doShowMaterial() {
			//判断是否已选定了库房
			if (!this.formData.storeroomId) {
				this.$message.warning('请先确定库房');
				return;
			}
			this.params.storeroomId = this.formData.storeroomId;
			this.showMaterial = true;
		},
		//原料选择
		selMaterial(rows) {
			if (this.formTitle === '新增') {
				this.curMaterialIds = [];
				this.formData.details = rows.map(i => {
					let materialId = i.id;
					this.curMaterialIds.push(materialId);
					let ownId = snowflake.generate();
					return {
						...i,
						id: ownId,
						materialCode: i.code,
						materialName: i.name,
						residueNum: i.inventoryNum,
						inventorySheetId: this.ownId,
						materialId: materialId,
						num: i.inventoryNum
					};
				});
			} else {
				let arr = rows.map(i => {
					let ownId = snowflake.generate();
					return {
						...i,
						id: ownId,
						materialCode: i.code,
						materialName: i.name,
						residueNum: i.inventoryNum,
						inventorySheetId: this.ownId,
						materialId: i.id,
						num: i.inventoryNum
					};
				});
				for (let item of arr) {
					let materialId = item.materialId;
					if (this.curMaterialIds.includes(materialId)) {
						continue;
					}
					this.curMaterialIds.push(materialId);
					this.formData.details.push(item);
				}
			}
		},
		submit(status) {
			this.$refs.form.validate(valid => {
				if (valid) {
					this.formData = {
						...this.formData,
						status,
						details: this.formData.details.map(i => {
							return {
								id: i.id,
								inventorySheetId: i.inventorySheetId,
								materialId: i.materialId,
								residueNum: i.residueNum,
								num: i.num
							};
						})
					};
					this.handleFormSubmit(this.formData);
				}
			});
		}
	}
};
</script>
<style scoped lang="scss">
@import '../../../assets/style/style.scss';

.content {
	width: 100%;
	height: 100%;
}

.el-dialog__body {
	.btn-box {
		margin-top: 20px;
		display: flex;
		justify-content: flex-end;

		.btn {
			padding: 5px 10px;
			color: #666;
			border: 1px solid #eee;
			cursor: pointer;
			margin-right: 5px;
			border-radius: 4px;
		}
	}
}
</style>
