/**
 * 企业端api相关接口
 */
const interfaceUrl = {
    listJson: '/ybzy/hqpurchaseorder/listJson', // 列表
    info: '/ybzy/hqpurchaseorder', // 获取
    save: '/ybzy/hqpurchaseorder/save', // 保存
    update: '/ybzy/hqpurchaseorder/update', // 修改
    deleteBatchIds: '/ybzy/hqpurchaseorder/deleteBatchIds', // 删除
    submit: '/ybzy/hqpurchaseorder/submit', // 提交入库
    materialListAll: '/ybzy/hqbasematerial/getAllListByStatus', // 所有启用原料
    supplierListAll: '/ybzy/hqsupplier/getAllListByStatus', // 所有启用原料
    getListByOrderId: '/ybzy/hqpurchaseorder/getListByOrderId', // 明细列表
    deleteById: '/ybzy/hqpurchaseorder/deleteById', // 删除列表数据

};
export default interfaceUrl;