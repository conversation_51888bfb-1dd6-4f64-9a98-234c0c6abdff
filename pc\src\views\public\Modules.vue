<template>
	<div>
		<component :is="module" :boot-name="$route.query.boot"></component>
	</div>
</template>
<script>
/*
	动态加载模块，根据路由地址中的参数动态加载不同依赖下的模块，主要用于eoss公共模块的页面
	depend： 依赖名称
	module： 模块名称
	列：
		如业务流程组件eoss-process：
		depend=process
		module=rules
	路由地址就是：/#/modules?depend=process&module=rules,页面就会加载业务流程的规则配置模块页面
*/
import EsError from './Error.vue';
export default {
	components: { EsError },
	computed: {
		module: function () {
			const module =
				(this.$route.query.depend ? this.$route.query.depend : '') +
				(this.$route.query.module ? this.$route.query.module : '');
			return 'es-' + (module ? module.toLowerCase() : 'error');
		}
	},
	mounted() {}
};
</script>
