<template>
	<!-- 在list.js修改component.name值可改变展示组件 -->
	<component :is="component.name" ref="dataTable" v-bind="table" :table="table"
		@selection-change="handleSelectionChange" @btnClick="btnClick" @search="search" @reset="search({})"
		@success="successAfter">
		<es-dialog v-if="showForm" :title="formTitle" :visible.sync="showForm" :close-on-click-modal="false"
			:middle="true" width="600px" height="450px" class="dialog-form">
			<es-form ref="form" :model="formData" :contents="formItemList" :genre="2" height="300px" collapse
				@change="handleFormItemChange" @submit="handleFormSubmit" @reset="showForm = false" />
		</es-dialog>
		<es-dialog v-if="showView" title="岗位详情" :visible.sync="showView" :close-on-click-modal="false" :middle="true"
			height="700px" class="dialog-form">
			<!-- 设置对话框内容高度 -->
			<div v-loading="loading" class="sketch_content">
				<table class="resumeTable">
					<tr>
						<th>企业名称：</th>
						<!-- <td>{{ viewData?.enterprise?.corpName }}</td> -->
						<td>{{ viewData.corpName }}</td>
						<th>岗位名称：</th>
						<td>{{ viewData.name }}</td>
					</tr>
					<tr>
						<th>岗位类型：</th>
						<td>{{ viewData.postTypeDes }}</td>
						<th>工作性质：</th>
						<td>{{ viewData.jobNature }}</td>
					</tr>
					<tr>
						<th>学历要求：</th>
						<td>{{ viewData.education }}</td>
						<th>工作经验：</th>
						<td>{{ viewData.workExperience }}</td>
					</tr>
					<tr>
						<th>薪资：</th>
						<td colspan="3">{{ viewData.salaryStructure }}</td>
					</tr>
					<tr>
						<th valign="top">岗位介绍：</th>
						<td valign="top" colspan="3">
							<div v-html="viewData.introduce"></div>
						</td>
					</tr>
					<tr>
						<th>所属地区：</th>
						<td colspan="3">{{ viewData.areaName }}</td>
					</tr>
					<!-- <tr>
						<th>详细地址：</th>
						<td colspan="3">{{ viewData.address }}</td>
					</tr> -->
					<tr>
						<th>工作时间：</th>
						<td colspan="3">{{ viewData.workTime }}</td>
					</tr>
					<tr v-if="formData.lon && formData.lat">
						<th></th>
						<td colspan="3">
							<baidu-map :center="center" :zoom="zoom" class="baiduMap" :scroll-wheel-zoom="true"
								@ready="handler">
								<bm-view style="width: 80%; height: 280px; flex: 1"></bm-view>
							</baidu-map>
						</td>
					</tr>

					<tr>
						<!-- <th>岗位标签：</th> -->
						<th>保障待遇：</th>
						<td colspan="3">
							<el-tag v-for="(tag, index) in viewData.tags" :key="index" size="small">
								{{ tag }}
							</el-tag>
						</td>
					</tr>
					<tr>
						<th>公司福利：</th>
						<td colspan="3">{{ viewData.benefit }}</td>
					</tr>
					<tr>
						<th valign="top">岗位面向：</th>
						<td valign="top" colspan="3">
							<!-- 内部表格 -->
							<table v-if="viewData.targetColSchMaj && viewData.targetColSchMaj.length > 0" border="1"
								class="collegeClass">
								<tr>
									<!-- <th>序号</th> -->
									<th>学院</th>
									<th>专业</th>
									<th style="width: 30px">审核状态</th>
									<th>学院审核意见</th>
									<th>招就处审核意见</th>
								</tr>
								<tr v-for="(item, index) in viewData.targetColSchMaj" :key="index">
									<!-- <td style="text-align: center; width: 80px">{{ index + 1 }}</td> -->
									<td style="text-align: center">{{ item.targetCollegeName }}</td>
									<td style="text-align: center">{{ item.targetMajorName }}</td>
									<td style="text-align: center">
										<!-- // 审核状态auditStatus：0:待审核（二级学院审核）, 11:待招就处审核（二级学院审核通过）, 1:已审核（招就处审核通过）, 2:审核未通过, 9:草稿 -->
										<el-tag :type="item.auditStatus === 1
											? 'success'
											: item.auditStatus === 0
												? 'info'
												: item.auditStatus === 11
													? 'warning'
													: 'danger'
											" size="mini">
											{{
												{
													0: '待审核',
													11: '待招就处审核',
													1: '已审核',
													2: '审核未通过'
												}[item.auditStatus] || '审核未通过'
											}}
										</el-tag>
									</td>
									<td style="text-align: center">{{ item.collegeAuditComment || '暂无意见' }}</td>
									<td style="text-align: center">{{ item.auditOpinion || '暂无意见' }}</td>
								</tr>
							</table>
						</td>
					</tr>
					<tr>
						<th>发布人：</th>
						<td>{{ viewData.createUserName }}</td>
						<th>发布时间：</th>
						<td>{{ viewData.createTime }}</td>
					</tr>
					<tr>
						<th>是否启用：</th>
						<td>{{ viewData.statusStr }}</td>
						<th>招聘形式：</th>
						<td>{{ viewData.lineType === 1 ? '线下' : '线上' }}</td>
					</tr>
					<tr v-if="viewData.lineType === 1">
						<th>招聘地点：</th>
						<td>{{ viewData.offlinePlace || '暂无' }}</td>
						<th>进校时间：</th>
						<td>{{ viewData.enterSchoolTime || '暂无' }}</td>
					</tr>
					<!-- <tr v-if="viewData.auditStatusStr || viewData.auditOpinion">
						<th>审核状态：</th>
						<td>{{ viewData.auditStatusStr }}</td>
						<th>审核意见：</th>
						<td>{{ viewData.auditOpinion }}</td>
					</tr> -->
				</table>
			</div>
		</es-dialog>
		<el-dialog v-if="showPostResumPage" title="岗位投递情况" :visible.sync="showPostResumPage" append-to-body width="70%"
			height="700px" :close-on-click-modal="false" :destroy-on-close="true">
			<postResumePage ref="postResumePage" :post-id="postId"></postResumePage>
		</el-dialog>
	</component>
</template>

<script>
import httpApi from '@/http/job/jobCoPost/api.js';
import list from '@/http/job/jobCoPost/list.js';
import audit from '@/http/job/jobCoPost/audit.js';
import view from '@/http/job/jobCoPost/view.js';
import postResumePage from '@/views/job/jobCoPostResume/index.vue';

export default {
	name: 'JobCoPost',
	components: { postResumePage },
	mixins: [list, audit, view],
	data() {
		return {
			loading: false,
			selectRowData: [],
			selectRowIds: [],
			formTitle: '',
			showForm: false,
			showView: false,
			formData: {},
			viewData: {},
			extraData: {},
			isSystemReo: false, //true招就处，false二级学院
			//投递情况表
			showPostResumPage: false,
			postId: null,
			// 地图相关
			BMap: '',
			map: '',
			center: {
				lng: 116.*********,
				lat: 39.**********
			},
			params: {},
			selectParams: {},
			zoom: 17
		};
	},
	watch: {
		showForm(val) {
			if (!val) {
				this.formData = {};
			}
		}
	},
	created() {
		this.getUserAuthority();
	},
	methods: {
		// 获取角色信息
		getUserAuthority() {
			// 获取登录用户信息
			const userInfo = JSON.parse(localStorage.getItem('loginUserInfo'));
			this.$request({
				url: '/ybzy/platuser/front/checkRole',
				data: {
					account: userInfo.code,
					roleCode: 'system_reo' //招就处true
				},
				method: 'post'
			}).then(res => {
				this.isSystemReo = res.results.system_reo;
				// this.isSystemReo = false;
			});
		},
		search(res) {
			const paramsN = { ...res };
			this.selectParams = paramsN;
		},
		// 地图初始化
		handler({ BMap, map }) {
			this.BMap = BMap;
			this.map = map;
			this.addPoint(this.viewData.lon, this.viewData.lat);
		},
		// 添加点位
		addPoint(lng, lat) {
			let newLng = lng ? lng : this.center.lng;
			let newLat = lat ? lat : this.center.lat;
			let map = this.map;
			let BMap = this.BMap;
			map.clearOverlays();
			var point = new BMap.Point(newLng, newLat);
			let zoom = map.getZoom();
			setTimeout(() => {
				map.centerAndZoom(point, zoom);
			}, 0);
			var marker = new BMap.Marker(point); // 创建标注
			map.addOverlay(marker); // 将标注添加到地图中
		},
		// 初始化扩展数据，字典等
		successAfter(data) {
			this.extraData = data.results.extraData;
		},
		// 数据列表多选回调
		handleSelectionChange(data) {
			let ids = [];
			data.forEach(row => {
				ids.push(row.id);
			});
			this.selectRowData = data;
			this.selectRowIds = ids;
		},
		// 表单变更时, 回调处理
		handleFormItemChange(filed, data) {
			// 处理表单字段systemCode的变更
			// if (filed == 'systemCode') {
			//	this.formData.systemCode = data.id;
			//	this.formData.systemName = data.name;
			// }
		},
		btnClick(res) {
			let code = res.handle.code;
			switch (code) {
				case 'view':
					// 打开查看弹窗
					this.showView = true;
					this.loading = true;
					this.$request({
						url: httpApi.jobCoPostInfo,
						params: { id: res.row.id }
					}).then(res => {
						// 处理自定义的数据格式化, extFiled 额外属性名， key、val 数据映射
						// this.viewData = this.formatExtraData(res.results[*], 'extFiled', 'key', 'val');
						this.viewData = res.results;
					});
					this.loading = false;
					break;
				case 'add':
					this.formTitle = '新增';
					this.showForm = true;
					// 初始化表单数据
					this.formData = {
						// useStatus: 1,
					};
					break;
				case 'postResumePageBtn':
					this.showPostResumPage = true;
					this.postId = res.row.id;
					break;
				case 'audit':
					this.formTitle = this.isSystemReo ? '招就处审核' : '二级学院审核';
					this.formData = { ...res.row };


					this.showForm = true;
					break;
				case 'delete':
					this.deleteId(res.row.id);
					break;
				default:
					break;
			}
		},
		handleFormSubmit() {
			let formData = {
				id: this.formData.majorId,
				auditStatus: this.formData.auditStatus,
				auditOpinion: this.formData.auditOpinion,
				orgId: this.formData.orgId
			};
			// 可额外处理formData中的数据

			this.$request({
				url: httpApi.jobCoPostAudit,
				data: formData,
				method: 'POST'
			}).then(res => {
				if (res.rCode == 0) {
					this.$message.success('操作成功');
					this.showForm = false;
					this.formData = {};
					this.$refs.dataTable.reload();
				} else {
					this.$message.error(res.msg);
				}
			});
		},
		deleteId(id) {
			this.$confirm('确定要删除选中的数据', '删除', {
				confirmButtonText: '确定',
				cancelButtonText: '取消',
				type: 'warning'
			})
				.then(() => {
					this.$request({
						url: httpApi.jobCoPostDeleteById,
						data: { id: id },
						method: 'POST'
					}).then(res => {
						if (res.rCode == 0) {
							this.$message.success('操作完成');
							this.$refs.dataTable.reload();
						} else {
							this.$message.error(res.msg);
						}
					});
				})
				.catch(() => { });
		},
		// 根据列表额外数据返回的 字典信息，格式化数据
		formatExtraData(data, extFiled, key, val) {
			let text = '未知';
			this.extraData[extFiled].forEach(dict => {
				if (dict[key] === data) {
					text = dict[val];
				}
			});
			return text;
		}
	}
};
</script>
<style lang="scss" scoped>
.sketch_content {
	padding: 20px;
	box-sizing: border-box;
	min-height: 580px;

	&::-webkit-scrollbar {
		width: 4px;
		height: 4px;

		&-thumb {
			background: #c0c4cc;
			border-radius: 2px;
		}

		&-track {
			background: #f5f7fa;
			border-radius: 2px;
		}
	}
}

.resumeTable {
	width: 100%;
	border-collapse: separate;
	border-spacing: 0;
	background: #fff;
	border-radius: 8px;
	overflow: hidden;
	box-shadow: 0 2px 12px rgba(0, 0, 0, 0.06);
	transition: all 0.3s ease;

	&:hover {
		box-shadow: 0 4px 16px rgba(0, 0, 0, 0.08);
	}

	th,
	td {
		padding: 10px 12px;
		border: 1px solid #ebeef5;
		font-size: 13px;
		line-height: 1.5;
		transition: all 0.3s ease;
	}

	tr:hover td {
		background-color: #f9fafc;
	}

	th {
		background: linear-gradient(to right, #f5f7fa, #f8f9fa);
		color: #303133;
		font-weight: 600;
		text-align: right;
		width: 110px;
		white-space: nowrap;
		letter-spacing: 0.3px;
	}

	td {
		color: #606266;

		div {
			line-height: 1.6;
		}
	}

	.el-tag {
		margin: 2px;
		padding: 0 8px;
		height: 22px;
		line-height: 20px;
		font-size: 12px;
		transition: all 0.3s ease;
		border-radius: 3px;

		&:hover {
			transform: translateY(-1px);
			box-shadow: 0 2px 5px rgba(0, 0, 0, 0.08);
		}
	}
}

.collegeClass {
	width: 100%;
	border-collapse: separate;
	border-spacing: 0;
	margin: 8px 0;
	border-radius: 6px;
	overflow: hidden;
	box-shadow: 0 1px 8px rgba(0, 0, 0, 0.04);
	border: none;

	th {
		background: linear-gradient(to right, #f5f7fa, #f8f9fa);
		color: #303133;
		text-align: center !important;
		padding: 8px 10px;
		font-weight: 600;
		font-size: 13px;
		border: 1px solid #ebeef5;
	}

	td {
		padding: 8px 10px;
		font-size: 13px;
		border: 1px solid #ebeef5;
		transition: all 0.3s ease;
	}

	tr:hover td {
		background-color: #f9fafc;
	}

	.el-tag {
		margin: 2px;
		padding: 0 6px;
		height: 22px;
		line-height: 20px;
		font-size: 12px;
		transition: all 0.3s ease;
	}
}

.baiduMap {
	width: 100%;
	height: 250px;
	margin: 10px 0;
	border-radius: 6px;
	overflow: hidden;
	box-shadow: 0 1px 8px rgba(0, 0, 0, 0.06);
	transition: all 0.3s ease;

	&:hover {
		box-shadow: 0 2px 12px rgba(0, 0, 0, 0.08);
	}
}

.dialog-form {
	border-radius: 8px;
	overflow: hidden;

	:deep(.el-dialog__body) {
		padding: 15px 20px;
	}
}
</style>
