/*  接口地址定义
    方式1：
        //获取xxx信息
        export const getInfoStatistical = '/hr/shycInfo/getInfoStatistical.dhtml';
    调用方式1：
        import {getInfoStatistical} from '@/http/system.js';
        console.log(getInfoStatistical)

    方式2：
        //系统管理模块
        const system = {
            //用户、用户组树
            getDeptTree: '/bootdemo/simplesysDept/getDeptTree.djson',
        }
        export default system;
    调用方式2:
        import system from '@/http/system.js';
        console.log(system.getDeptTree)
        
*/

//接口地址
const api = {
	// 消息发送
	jobCoPioneerMentorList: '/ybzy/jobCoPioneerMentor/listJson',
	jobCoPioneerMentorInfo: '/ybzy/jobCoPioneerMentor/info',
	info: '/ybzy/jobCoPioneerMentor',
	jobCoPioneerMentorSave: '/ybzy/jobCoPioneerMentor/save',
	jobCoPioneerMentorUpdate: '/ybzy/jobCoPioneerMentor/update',
	jobCoPioneerMentorDeleteById: '/ybzy/jobCoPioneerMentor/deleteById',
	jobCoPioneerMentorTree: '/ybzy/jobCoPioneerMentor/Tree',
	//数据字典-获取代码表数据
	dicFindSysCode:'/sys/v1/mecpSys/findSysCode.json?sysAppCode=',
};
export default api;
