<!--
 @desc:社团管理系统-学生
 @author: 
 @date: 2023/11/13
 -->
<template>
	<div class="main">
		<div class="card-box">
			<mini-card v-for="(item, index) in card" :key="index" :card-data="item" />
		</div>
		<titile-card title="社团活动" :img-url="require('@/assets/images/sys/ghgl.png')">
			<template #content>
				<div class="table-box">
					<es-data-table
						ref="table"
						:thead="thead"
						:page="true"
						:url="tableUrl"
						:toolbar="toolbar"
						:response="func"
						method="get"
						style="width: 100%"
						height="400px"
						@change="pageSizeChange"
						@current="pageCurrentChange"
					></es-data-table>
				</div>
			</template>
		</titile-card>
		<div class="e-box">
			<titile-card title="人数统计" :img-url="require('@/assets/images/sys/rstj.png')">
				<template #content>
					<div class="is-pie">
            <e-line id="rstj" :series-data="rstjData" />
					</div>
				</template>
			</titile-card>
			<titile-card title="活动统计" :img-url="require('@/assets/images/sys/sthdtj.png')">
				<template #content>
					<div class="is-pie">
            <e-pie id="hdtj" :series-data="hdtjData" />
					</div>
				</template>
			</titile-card>
		</div>
	</div>
</template>

<script>
import MiniCard from '@/components/show/mini-card.vue';
import TitileCard from '@/components/show/titile-card.vue';
import EPieBase from '@/components/echarts/pie-base.vue';
import ELineBase from '@/components/echarts/line-base.vue';
import ELine from "@/components/echarts/line.vue";
import EPie from "@/components/echarts/pie.vue";

export default {
	components: {EPie, ELine, MiniCard, TitileCard, EPieBase, ELineBase },

	data() {
		return {
			card: [
				{
					img: require('@/assets/images/sys/gh_icon1.png'),
					title: '社团总数',
					unit: '个',
					num: '0'
				},
				{
					img: require('@/assets/images/sys/gh_icon2.png'),
					title: '社团总人数',
					unit: '人',
					num: '0'
				}
			], //统计部分卡片展示内容
			tableUrl: '/museumcloud/activity/activityItem/front/list', //表格请求接口
			toolbar: [
				{
					type: 'search',
					reset: true,
					contents: [
						{
							type: 'text',
							label: '关键字',
							name: 'keyword',
							placeholder: '请输入关键字'
						},
						{
							type: 'select',
							label: '活动状态',
							name: 'active',
							placeholder: '请选择活动状态'
						}
					]
				}
			], //表格搜索内容
			thead: [
				{
					title: '活动名称',
					field: 'createTime',
					fixed: true
				},
				{
					title: '发布人',
					field: 'formName',
					fixed: false
				},
				{
					title: '活动时间',
					field: 'companyname',
					fixed: false
				},
				{
					title: '人数上限',
					field: 'fillUsername',
					fixed: false
				},
				{
					title: '联系电话',
					field: 'telephonenum',
					fixed: false
				},
				{
					title: '活动地址',
					field: 'startdate',
					width: '200px',
					fixed: false
				},
				{
					title: '活动状态',
					field: 'companyStatusText',
					fixed: false
				}
			], //表格表头展示内容
			rstjData: [], //人数统计图表数据
			hdtjData: [
					{
						name: '已预约活动',
						value: '10'
					},
					{
						name: '待预约活动',
						value: '14'
					},
					{
						name: '预约中活动',
						value: '20'
					}
				]//活动统计图表数据
		};
	},
	mounted() {
		//console.log(this.$refs.table, 333);
    this.getCardData();
    this.getActivityPieData();
    this.getMemberLineData();
	},
	methods: {
		pageCurrentChange(current) {
		},
		pageSizeChange(size) {
		},
		func(res) {
			let { data } = res;
			let param = {};
			for (let i in data) {
				if (i == 'startdate') {
					param['start'] = data[i][0];
					param['end'] = data[i][1];
				} else {
					param[i] = data[i];
				}
			}
			console.log(data);
			return param;
		},
    // 顶部综合数据
    getCardData() {
      this.$.ajax({
        method: 'GET',
        url: '/ybzy/society/home/<USER>'
      }).then(data => {
        if(data.success) {
          this.card[0].num = String(data.results.societyCount);
          this.card[1].num = String(data.results.memberCount);
          //console.info('打印原始数据:', data.results);
          //console.info('打印收入支出柱状图数据', ArrList);
        }else {
          this.$message.error(data.msg);
        }
      });
    },
    // 活动统计饼图
    getActivityPieData() {
      this.$.ajax({
        method: 'GET',
        url: '/museumcloud/activity/activityItem/front/activityStatusCount'
      }).then(data => {
        if(data.success) {
          let activityList = [];
          data.data.list.forEach(el => {
            activityList.push({ name: el.name, value: el.value });
          });
          this.hdtjData = activityList;
        }else {
          this.$message.error(data.msg);
        }
      });
    },
    // 人数统计柱状图
    getMemberLineData() {
      this.$.ajax({
        method: 'GET',
        url: '/ybzy/society/home/<USER>'
      }).then(data => {
        if(data.success) {
          if(data.results != null) {
            let yAxis0 = data?.results?.memberNums;
            let sum = yAxis0.reduce(function (prev, next, index, array) {
              return prev + next;
            });
            let yAxis1 = [...yAxis0];
            yAxis1.forEach((item, index) => {
              yAxis1[index] = (item / sum * 100).toFixed(2); // 取两位小数
            });
            this.rstjData.push(data.results.xAxis);
            this.rstjData.push(yAxis1);
            this.rstjData.push(yAxis0);
          }
        }else {
          this.$message.error(data.msg);
        }
      });
    },
	}
};
</script>
<style lang="scss" scoped>
@import '@ast/style/public.scss';

.main {
	background: #f0f2f5;
	padding: 12px;
	overflow: auto;
	height: 100%;
	width: 100%;
	& > * {
		background: #fff;
	}
	header {
		@include flexBox(space-between);
		padding: 6px 20px;
		border-radius: 8px;
		font-size: 20px;
		font-weight: 550;
	}
	.card-box {
		@include flexBox(space-between);
		width: 100%;
		margin: 0 0 12px;
		background: #f0f2f5;

		.card {
			width: 49.6%;
		}
	}
	.tabs {
		// width: 300px;
		@include flexBox();
		p {
			margin-left: 10px;
			font-weight: 550;
			cursor: pointer;
		}
		.is-active {
			color: #0377e8;
		}
	}
	.e-box {
		@include flexBox(space-between);
		background: #f0f2f5;
		margin-top: 12px;
		width: 100%;
		& > * {
			background: #fff;
		}
		.title-card {
			width: 49.5%;
		}
	}
	.is-pie {
		width: 100%;
		height: 400px;
	}
}
</style>
