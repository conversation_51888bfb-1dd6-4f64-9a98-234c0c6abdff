<template>
	<es-form
		:key="formData.id"
		ref="form"
		v-loading="loading"
		:model="formData"
		:contents="formItemList"
		label-width="100px"
		height="650px"
		:genre="2"
		collapse
		:submit="formTitle !== '查看'"
		@change="inputChange"
		@click="handleFormSubmit"
	/>
</template>

<script>
import modelFieldUrl from '@/http/platform/modelfield.js';
export default {
	name: 'ResumeView',
	props: {
		info: {
			type: Object,
			default: () => {
				return {};
			}
		},
		formTitle: {
			type: String,
			default: ''
		},
		cmsnodeCodeList: {
			// 所属目录列表
			type: Array,
			default: () => {
				return [];
			}
		},
		cmsmodelCodeList: {
			// 文章模型列表
			type: Array,
			default: () => {
				return [];
			}
		}
	},
	data() {
		return {
			loading: false,
			formData: {},
			attachmentsList: []
		};
	},
	computed: {
		formItemList() {
			const readonly = this.formTitle === '查看';
			return [
				{
					label: '文章标题:',
					name: 'title',
					readonly: readonly,
					col: 4
				},
				{
					label: '文章编码:',
					name: 'code',
					readonly: readonly,
					col: 4
				},
				{
					label: '关键字:',
					name: 'keywords',
					readonly: readonly,
					col: 4
				},
				{
					label: '描述:',
					name: 'description',
					readonly: readonly,
					col: 4
				},
				{
					label: '所属目录:',
					name: 'nodeId',
					readonly: readonly,
					type: 'select',
					placeholder: '',
					event: 'multipled',
					labelKey: 'name',
					valueKey: 'id',
					data: this.cmsnodeCodeList,
					col: 4
				},
				// {
				// 	label: '内容显示类型:',
				// 	name: 'infoType',
				// 	type: 'radio',
				// 	// readonly: readonly,
				// 	// disabled: true,
				// 	data: [
				// 		{
				// 			label: '普通',
				// 			value: 2
				// 		},
				// 		{
				// 			label: '跳转外链',
				// 			value: 1
				// 		}
				// 	],
				// 	col: 6
				// },
				{
					label: '文章模型:',
					name: 'infoModelId',
					readonly: readonly,
					type: 'select',
					placeholder: '',
					event: 'multipled',
					labelKey: 'name',
					valueKey: 'id',
					data: this.cmsmodelCodeList,
					col: 4
				},
				{
					label: '发布时间:',
					name: 'createTime',
					readonly: readonly,
					col: 4
				},
				// {
				// 	label: '排序:',
				// 	name: 'sortIndex',
				// 	readonly: readonly,
				// 	col: 4
				// },
				// {
				// 	label: '状态:',
				// 	name: 'status',
				// 	type: 'radio',
				// 	// disabled: true,
				// 	data: [
				// 		{
				// 			label: '启用',
				// 			value: 1
				// 		},
				// 		{
				// 			label: '禁用',
				// 			value: 0
				// 		}
				// 	],
				// 	col: 6
				// },
				{
					name: 'img',
					label: '封面图片:',
					type: 'attachment',
					value: '',
					preview: true,
					readonly: readonly,
					listType: 'picture-card',
					selectType: 'icon-plus',
					code: 'scientific_gg_img',
					ownId: this.formData.id
				},
				{
					name: 'fj',
					label: '附件',
					type: 'attachment',
					col: 12,
					code: 'scientific_gg_fj',
					preview: true,
					readonly: readonly,
					ownId: this.formData.id // 业务id
				},
				{
					type: 'attachment',
					label: '内容:',
					code: 'USER_IMG',
					ownId: '666666',
					showFileList: false,
					disabled: true,
					render: h => {
						return h('wangeditor', {
							props: {
								readOnly: readonly,
								value: this.formData.richText,
								dataId: this.formData.id
							},
							on: {
								inputChange: val => {
									this.$set(this.formData, 'richText', val);
								}
							}
						});
					},
					rules: {
						required: true,
						message: '请填写邀请说明',
						validator: (rule, value, callback) => {
							const explainDes = this.formData.explainDes;
							if (explainDes) {
								callback();
							}
							callback(new Error('请填写邀请说明'));
						},
						trigger: 'change'
					},
					col: 12
				},

				{
					type: 'submit',
					skin: 'lay-form-btns',
					contents: [
						{
							type: 'reset',
							text: '关闭'
						}
					]
				}
			];
		}
	},
	created() {
		this.doHandleFormData(this.info);
	},
	methods: {
		inputChange(key, value) {},
		// 保存
		async handleFormSubmit(e) {
			const valid = await this.$refs.form.validate();
			if (!valid) return;
		},
		doHandleFormData(newData) {
			this.$request({
				url: modelFieldUrl.getModelForm,
				params: {
					modelId: newData.infoModelId,
					objType: 'info',
					objId: newData.id
				},
				method: 'GET'
			}).then(res => {
				if (res.rCode === 0) {
					newData.richText = res.results?.modelForm?.richText || '';
					this.formData = newData;
				}
			});
		}
	}
};
</script>

<style scoped lang="scss">
.el-upload--handle {
	width: 100%;
}
::v-deep [data-slate-node='element'] {
	text-align: left !important;
}
</style>
