<template>
	<div class="scientific-research">
		<div class="achievement">
			<div class="achievement-img">
				<img class="img" :src="require('@/assets/images/home20240423/kycg-img.png')" alt="" />
				<img class="text-img" :src="require('@/assets/images/home20240423/kycg.png')" alt="" />
			</div>
			<div class="card">
				<div v-for="(item, index) in achievementList" :key="index" class="card-item">
					<p>
						<span class="item-num">{{ dataRes[item.key] || '0' }}</span>
						<span class="item-unit">{{ item.unit }}</span>
					</p>
					<p class="item-name">{{ item.name }}</p>
				</div>
			</div>
		</div>
		<div class="project">
			<div class="project-img">
				<img class="img" :src="require('@/assets/images/home20240423/kyxm.png')" alt="" />
			</div>
			<div class="card">
				<div v-for="(item, index) in projectList" :key="index" class="card-item">
					<p>
						<span class="item-num">{{ dataRes[item.key] || '0' }}</span>
						<span class="item-unit">{{ item.unit }}</span>
					</p>
					<p class="item-name">{{ item.name }}</p>
				</div>
			</div>
		</div>
		<div class="lecture">
			<div class="lecture-img">
				<img class="img" :src="require('@/assets/images/home20240423/xsjz.png')" alt="" />
			</div>
			<div class="card">
				<div v-for="(item, index) in lectureList" :key="index" class="card-item">
					<p>
						<span class="item-num">{{ item.number }}</span>
						<span class="item-unit">{{ item.unit }}</span>
					</p>
					<p class="item-name">{{ item.name }}</p>
				</div>
			</div>
		</div>
	</div>
</template>

<script>
import { xodbApi2 } from '@/api/xodb';
export default {
	name: '',
	components: {},
	props: {},
	data() {
		return {
			dataRes: {},
			achievementList: [
				{
					name: '论文发布数',
					key: 'lwfbsl',
					unit: '篇'
				},
				{
					name: '专利发布数',
					key: 'zlfbsl',
					unit: '项'
				},
				{
					name: '专著发布数',
					key: 'zzfbsl',
					unit: '篇'
				}
			],
			projectList: [
				{
					name: '科研项目总数',
					key: 'kyxmzs',
					unit: '项'
				},
				{
					name: '参与教师学生数',
					key: 'cyjsxsrs',
					unit: '人'
				}
			],
			lectureList: [
				{
					name: '学术讲座',
					number: '0',
					unit: '次'
				},
				{
					name: '参与教师学生数',
					number: '0',
					unit: '人'
				}
			]
		};
	},
	computed: {
		loginUserInfo() {
			return JSON.parse(localStorage.getItem('loginUserInfo')) || {};
		}
	},
	mounted() {
		this.getDataN('ads_jg_kycg_query', 'dataRes'); // 科研成果
		this.getDataN('ads_ky_kyxmqk_query', 'dataRes'); // 科研项目

		// this.getLectureList(); // 学术讲座
	},
	methods: {
		async getDataN(url, listName) {
			const LeaderRole = localStorage.getItem('LeaderRole');
			try {
				const {
					data: { list }
				} = await xodbApi2.post({
					url,
					params: {
						// xyjgbh: '1'
						xyjgbh: LeaderRole === 'xLeader' ? '1' : this.loginUserInfo.orgCode
					}
				});
				const data = list[0] || {};
				switch (listName) {
					case 'dataRes':
						this[listName] = { ...this[listName], ...data };
						break;
				}
			} catch (error) {
				console.error(`处理数据失败${listName}:`, error);
			}
		}
		// 学术讲座
		// async getLectureList() {
		// 	const {
		// 		dataResult: { dataList }
		// 	} = await xodbApi.get({
		// 		url: 'warehouseConfig_ybzy_dw_dwb_jw_xsjzsj_query'
		// 	});
		// 	const data = dataList[0];
		// 	this.lectureList[0].number = data.xsjzcs || 0;
		// 	this.lectureList[1].number = data.cyjsxsrs || 0;
		// }
	}
};
</script>

<style lang="scss" scoped>
.scientific-research {
	margin-top: 4px;
	display: flex;
	flex-wrap: wrap;
	justify-content: space-around;
}
.achievement,
.project,
.lecture {
	width: 100%;
	height: 94px;
	margin-top: 10px;
	display: flex;
	flex-shrink: 1;
	background: rgba(255, 255, 255, 0.25);
	border-radius: 3px;
	border: 1px solid #ffffff;
}
.project,
.lecture {
	width: calc(50% - 4px);
}
.achievement-img {
	width: 263px;
	height: 100%;
	display: flex;
	justify-content: center;
	align-items: center;
	.img {
		width: 64px;
		height: 53px;
	}
	.text-img {
		width: 83px;
		height: 16px;
		margin-left: 23px;
	}
}
.project-img,
.lecture-img {
	width: 106px;
	height: 100%;
	display: flex;
	justify-content: center;
	align-items: center;
	.img {
		width: 49px;
		height: 40px;
	}
}
.project-img,
.lecture-img,
.achievement-img {
	flex-shrink: 0;
	background: linear-gradient(90deg, #d7f1fd 0%, rgba(235, 255, 253, 0.22) 100%);
	border-radius: 2px 0px 0px 3px;
	border: 1px solid;
	border-image: linear-gradient(92deg, rgba(255, 255, 255, 0), rgba(255, 255, 255, 1)) 1 1;
}
.card {
	width: 100%;
	display: flex;
	justify-content: space-between;

	.card-item {
		flex-grow: 1;
		display: flex;
		flex-direction: column;
		justify-content: center;
		align-items: center;
	}
	.card-item + .card-item {
		position: relative;
		&::after {
			content: '';
			height: 40px;
			position: absolute;
			left: 0;
			border-left: 1px dashed rgba(117, 150, 187, 1);
		}
	}
	.item-num {
		font-family: DINPro, DINPro;
		font-weight: bold;
		font-size: 24px;
		color: #0a325b;
		line-height: 31px;
	}
	.item-unit {
		font-family: MicrosoftYaHei;
		font-size: 14px;
		margin-left: 6px;
		color: #294d79;
		line-height: 19px;
	}
	.item-name {
		font-family: MicrosoftYaHei;
		font-size: 14px;
		color: #454545;
		line-height: 19px;
		margin-top: 6px;
	}
}
</style>
