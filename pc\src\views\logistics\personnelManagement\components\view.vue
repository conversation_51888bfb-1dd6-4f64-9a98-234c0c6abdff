<template>
	<div class="teacher-pim">
		<es-form
			ref="formRef"
			:key="formKey"
			v-loading="loading"
			label-width="150px"
			:model="formData"
			:contents="contents"
			table
			:span="3"
			:readonly="formReadonly"
			@submit="handleFormSubmit"
			@click="handleFormClick"
		></es-form>
	</div>
</template>

<script>
export default {
	props: {
		title: {
			type: String,
			default: '查看'
		},
		formId: {
			type: String,
			default: ''
		},
		basics: {
			type: Object,
			default: () => {
				return {
					info: '', // 详情接口
					save: '', // 新增
					edit: '' // 修改
				};
			}
		}
	},
	data() {
		return {
			loading: false,
			formData: {},
			codesObj: {}, //批量数据字典
			formKey: 0
		};
	},
	computed: {
		formReadonly() {
			return this.title.includes('查看');
		},
		formAdd() {
			return this.title.includes('新增');
		},
		contents() {
			return [
				{
					label: '姓名',
					name: 'name',
					type: 'text',
					col: 3,
					rules: {
						required: !this.formReadonly,
						message: '请输入',
						trigger: 'blur'
					}
				},
				{
					label: '性别',
					name: 'sex',
					col: 3,
					placeholder: '请选择',
					type: 'select',
					'value-key': 'value',
					'label-key': 'label',
					rules: {
						required: !this.formReadonly,
						message: '请选择',
						trigger: 'blur'
					},
					data: [
						{
							label: '男',
							value: '1'
						},
						{
							label: '女',
							value: '2'
						}
					],
					filterable: true
				},
				{
					label: '身份证',
					name: 'idCard',
					col: 3,
					placeholder: '请输入',
					type: 'text',
					rules: {
						required: !this.formReadonly,
						message: '请输入',
						trigger: 'blur'
					}
				},
				{
					label: '出生年月',
					name: 'birthday',
					col: 3,
					placeholder: '请输入',
					type: 'date',
					events: {
						change: (e, val) => {
							// 计算年龄
							const age = this.getAge(val);
							this.$set(this.formData, 'age', age);
						}
					},
					rules: {
						required: !this.formReadonly,
						message: '请输入',
						trigger: 'blur'
					}
				},
				{
					label: '年龄',
					name: 'age',
					col: 3,
					placeholder: '请输入',
					trigger: 'blur'
				},
				{
					label: '民族',
					name: 'nation',
					col: 3,
					placeholder: '请输入',
					type: 'select',
					data: this.codesObj.member_nation,
					'value-key': 'cciValue',
					'label-key': 'shortName',
					rules: {
						required: !this.formReadonly,
						message: '请输入',
						trigger: 'blur'
					},
					filterable: true
				},
				{
					label: '农商行卡号',
					name: 'bankNumber',
					col: 3,
					placeholder: '请输入',
					rules: {
						required: !this.formReadonly,
						message: '请输入',
						trigger: 'blur'
					}
				},
				{
					label: '家庭住址',
					name: 'homeAddress',
					col: 3,
					placeholder: '请输入',
					rules: {
						required: !this.formReadonly,
						message: '请输入',
						trigger: 'blur'
					}
				},
				{
					label: '学历',
					name: 'education',
					col: 3,
					placeholder: '请输入',
					type: 'select',
					data: this.codesObj.hq_person_education,
					'value-key': 'cciValue',
					'label-key': 'shortName',
					filterable: true,
					rules: {
						required: !this.formReadonly,
						message: '请输入',
						trigger: 'blur'
					}
				},
				{
					label: '社保',
					name: 'insurance',
					col: 3,
					placeholder: '请输入',
					rules: {
						required: !this.formReadonly,
						message: '请输入',
						trigger: 'blur'
					}
				},
				{
					label: '岗位',
					name: 'post',
					col: 3,
					placeholder: '请输入',
					rules: {
						required: !this.formReadonly,
						message: '请输入',
						trigger: 'blur'
					}
				},
				{
					label: '联系电话',
					name: 'phone',
					col: 3,
					placeholder: '请输入',
					type: 'text',
					rules: {
						required: !this.formReadonly,
						message: '请选择',
						trigger: 'blur'
					}
				},
				{
					label: '进入后勤时间',
					name: 'joinDate',
					placeholder: '请选择',
					col: 3,
					type: 'date',
					rules: {
						required: !this.formReadonly,
						message: '请选择',
						trigger: 'blur'
					}
				},
				{
					label: '合同签订时间',
					name: 'contractSigningTime',
					placeholder: '请选择',
					col: 3,
					type: 'date',
					rules: {
						required: !this.formReadonly,
						message: '请选择',
						trigger: 'blur'
					}
				},
				{
					label: '状态',
					name: 'state',
					col: 3,
					placeholder: '请输入',
					type: 'select',
					data: this.codesObj.hq_person_state,
					'value-key': 'cciValue',
					'label-key': 'shortName',
					filterable: true,
					rules: {
						required: !this.formReadonly,
						message: '请输入',
						trigger: 'blur'
					}
				}
			];
		}
	},
	watch: {
		formId: {
			handler(newVal, oldVal) {
				this.getInfo(newVal);
			},
			immediate: true
		}
	},
	created() {},
	methods: {
		// 根据日期计算年龄
		getAge(birthDate) {
			// 如果传入的是字符串，尝试将其转换为 Date 对象
			if (typeof birthDate === 'string') {
				birthDate = new Date(birthDate);
			}

			// 获取今天的日期
			const today = new Date();

			// 计算年份差
			let age = today.getFullYear() - birthDate.getFullYear();

			// 检查是否已经过了今年的生日
			const monthDifference = today.getMonth() - birthDate.getMonth();
			if (monthDifference < 0 || (monthDifference === 0 && today.getDate() < birthDate.getDate())) {
				age--;
			}

			return age;
		},
		// 批量请求数据字典
		getSysCode() {
			const codes = 'member_nation,hq_person_education,hq_person_state';
			this.$utils.findSysCodeList(codes).then(res => {
				this.codesObj = { ...this.codesObj, ...res };
			});
		},

		handleFormSubmit() {
			const loading = this.$.loading(this.$loading, '提交中...');
			let formData = {
				...this.formData,
				subject: this.formData.firstLevelDiscipline,
				assumeType: '0'
			};
			this.$.ajax({
				url: this.title.includes('新增') ? this.basics.save : this.basics.edit,
				method: 'post',
				data: formData,
				format: false
			}).then(res => {
				loading.close();
				if (res.rCode == 0) {
					this.$message.success(res.msg);
					this.$emit('close', false);
				} else {
					this.$message.error(res.msg);
				}
			});
		},
		handleFormClick(submitType) {
			console.log('submitType', submitType);
		},
		getInfo(id) {
			this.getSysCode();
			if (id) {
				this.loading = true;
				this.$request({
					url: this.basics.info,
					params: { id }
					// method: 'POST'
				}).then(res => {
					if (res.rCode == 0) {
						this.loading = false;
						const obj = res.results;

						this.formData = {
							...obj,
							member: [
								{
									showname: obj.name
								}
							]
						};
					} else {
						this.$message.error(res.msg);
					}
				});
			}
		}
	}
};
</script>

<style lang="scss" scoped>
.teacher-pim {
	width: 100%;
	height: 100%;
	// padding: 20px 60px;
	// padding: 20px 30px;
}
</style>
