<template>
	<div class="backlog">
		<div style="height: 50px">
			<es-tabs v-model="activeName" @tab-click="handleClick">
				<el-tab-pane label="待办" name="first"></el-tab-pane>
				<el-tab-pane label="申请" name="second"></el-tab-pane>
				<el-tab-pane label="待阅" name="third"></el-tab-pane>
				<el-tab-pane label="已办" name="fourth"></el-tab-pane>
			</es-tabs>
		</div>
		<div class="sixContentItem" v-for="(item, index) in list.slice(0, 5)" :key="index">
			<div class="dot"></div>
			<div class="text">
				<span class="textContent">{{ item.bname }}</span>
				<div class="sendTimeConetent">
					<span class="sendName">{{ activeName == 'fourth' ? item.username : item.userName }}</span>
					<span class="sendTime">
						{{ activeName == 'fourth' ? dateChange(item.createtime) : item.createtime }}
					</span>
				</div>
			</div>
		</div>
	</div>
</template>

<script>
export default {
	data() {
		return {
			activeName: 'first',
			loading: false,
			list: []
		};
	},
	methods: {
		handleClick(tab) {
			switch (tab.label) {
				case '待办':
					this.interfaceData('/oa/wfPending/wfPendingMerge/list_json.dhtml');
					break;
				case '申请':
					this.interfaceData('/oa/task/wfApplication/me_list_json.dhtml');
					break;
				case '待阅':
					this.interfaceData('/oa/wfPending/wfPendingMerge/list_json.dhtml');
					break;
				case '已办':
					this.interfaceData('/oa/wfpened/wfPended/list_json.dhtml');
					break;
				default:
					break;
			}
		},
		//时间戳转日期格式
		dateChange(time) {
			// 时间戳
			let timestamp = time;
			// 此处时间戳以毫秒为单位
			let date = new Date(timestamp);
			let Year = date.getFullYear();
			let Moth = date.getMonth() + 1 < 10 ? '0' + (date.getMonth() + 1) : date.getMonth() + 1;
			let Day = date.getDate() < 10 ? '0' + date.getDate() : date.getDate();
			let Hour = date.getHours() < 10 ? '0' + date.getHours() : date.getHours();
			let Minute = date.getMinutes() < 10 ? '0' + date.getMinutes() : date.getMinutes();
			let Sechond = date.getSeconds() < 10 ? '0' + date.getSeconds() : date.getSeconds();
			let GMT = Year + '-' + Moth + '-' + Day + '   ' + Hour + ':' + Minute + ':' + Sechond;
			return GMT;
		},
		//我的待办接口
		interfaceData(url) {
			this.loading = true;
			this.$request({
				url: url,
				data: {
					query_pendingattr: 0,
					rows: 6,
					page: 1,
					sord: 'desc'
				},
				method: 'POST'
			})
				.then(res => {
					this.loading = false;
					this.list = res.data;
					let name = 'first';
					window.parent.postMessage(name, '*');
				})
				.catch(error => {
					this.loading = false;
				});
		}
	},
	mounted() {
		this.interfaceData('/oa/wfPending/wfPendingMerge/list_json.dhtml');
	}
};
</script>

<style lang="scss" scoped>
* {
	margin: 0;
	padding: 0;
}
.backlog {
	width: 100%;
	height: 100%;
	overflow: hidden;
	background: #fff;
	.sixContentItem {
		padding: 0 0 0 20px;
		width: 100%;
		height: 61px;
		line-height: 63px;
		display: flex;
		align-items: center;
		.dot {
			width: 13px;
			height: 13px;
			background: #ec993e;
			border-radius: 50%;
		}
		.text {
			flex: 1;
			height: 100%;
			padding: 0 4px 0 17px;
			display: flex;
			align-items: center;
			overflow: hidden;
			position: relative;
			.textContent {
				display: inline-block;
				height: 24px;
				line-height: 24px;
				font-size: 14px;
				font-family: Microsoft YaHei;
				font-weight: bold;
				color: #333333;
				overflow: hidden;
				white-space: nowrap;
				text-overflow: ellipsis;
			}
			.sendTimeConetent {
				padding: 0 4px 0 17px;
				width: 100%;
				height: 22px;
				bottom: 0;
				left: 0;
				position: absolute;
				display: flex;
				.sendName {
					display: inline-block;
					width: 140px;
					height: 28px;
					line-height: 28px;
					font-size: 14px;
					font-family: Microsoft YaHei;
					font-weight: 400;
					color: #7a8392;
				}
				.sendTime {
					display: inline-block;
					width: 200px;
					height: 28px;
					line-height: 28px;
					font-size: 14px;
					font-family: Microsoft YaHei;
					font-weight: 400;
					color: #7a8392;
				}
			}
		}
	}
}
.el-tabs__content {
}
</style>
