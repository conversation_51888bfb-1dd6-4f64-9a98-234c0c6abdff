<template>
	<el-row>
		<el-col :span="4">
			<es-tree
				:data="treeData"
				:props="{
					children: 'children',
					label: 'areaName'
				}"
				:search="{
					name: 'areaName',
					placeholder: '请输入关键字筛选区域'
				}"
				show-search
				style="height: 100%; width: 100%; margin-top: 1px"
				@node-click="handleNodeClick"
			></es-tree>
		</el-col>
		<el-col :span="20">
			<es-data-table
				ref="dataTable"
				class="table-box"
				v-bind="table"
				:table="table"
				:border="true"
				:full="true"
				:fit="true"
				:param="params"
				form
				@selection-change="handleSelectionChange"
				@btnClick="btnClick"
				@success="successAfter"
			>
				<!-- 表格内容部分 -->
				<es-dialog
					v-if="showForm"
					:title="formTitle"
					:visible.sync="showForm"
					:close-on-click-modal="false"
					:middle="true"
					:drag="false"
					width="450px"
					height="320px"
				>
					<es-form
						ref="form"
						:model="formData"
						:contents="formItemList"
						:genre="2"
						collapse
						:readonly="formTitle === '查看'"
						@change="handleFormItemChange"
						@submit="handleFormSubmit"
						@click="handleFormAudit"
						@reset="showForm = false"
					/>
				</es-dialog>
				<!-- 通道内容部分 -->
				<es-dialog
					v-if="showChannel"
					:title="formTitleChannel"
					:visible.sync="showChannel"
					:close-on-click-modal="false"
					:middle="true"
					:drag="false"
					height="660px"
				>
					<ViewChannel :device-id="ownId" :device-code="deviceCode" />
				</es-dialog>
			</es-data-table>
		</el-col>
	</el-row>
</template>

<script>
import interfaceUrl from '@/http/screen/videoInfo/api';
import ViewChannel from './viewChannel.vue';
import { v4 as uuidv4 } from 'uuid';

export default {
	name: 'VideoInfo',
	components: { ViewChannel }, //视频信息
	data() {
		return {
			deviceCode: '',
			ownId: '',
			showForm: false,
			showDelete: false,
			formData: {},
			extraData: {},
			formTitle: '编辑',
			params: {
				asc: 'false',
				orderBy: 'createTime',
				area: ''
			},
			// 树的数据
			treeData: [],
			formTitleChannel: '通道',
			showChannel: false //通道弹窗
		};
	},
	computed: {
		// 表单配置
		formItemList() {
			return [
				{
					label: '设备名称',
					name: 'deviceName',
					placeholder: '请输入名称',
					event: 'multipled',
					rules: {
						required: true,
						message: '请输入名称',
						trigger: 'blur'
					},
					verify: 'required',
					col: 12
				},
				{
					label: '设备编码',
					name: 'deviceCode',
					placeholder: '请输入设备编码',
					event: 'multipled',
					rules: {
						required: true,
						message: '请输入设备编码',
						trigger: 'blur'
					},
					verify: 'required',
					col: 12
				},
				// {
				// 	label: '在线状态',
				// 	name: 'isOnline',
				// 	placeholder: '请输入在线状态',
				// 	event: 'multipled',
				// 	rules: {
				// 		required: true,
				// 		message: '请输入在线状态',
				// 		trigger: 'blur'
				// 	},
				// 	verify: 'required',
				// 	  col: 12,
				// 	type: 'select'
				// },
				// {
				// 	label: '通道编码',
				// 	name: 'channelCode',
				// 	placeholder: '请输入通道编码',
				// 	event: 'multipled',
				// 	rules: {
				// 		required: true,
				// 		message: '请输入通道编码',
				// 		trigger: 'blur'
				// 	},
				// 	verify: 'required',
				// 	  col: 12
				// },
				// {
				// 	label: '通道名称',
				// 	name: 'channelName',
				// 	placeholder: '请输入通道名称',
				// 	event: 'multipled',
				// 	rules: {
				// 		required: true,
				// 		message: '请输入通道名称',
				// 		trigger: 'blur'
				// 	},
				// 	verify: 'required',
				// 	  col: 12
				// },
				{
					label: '区域',
					name: 'area',
					placeholder: '请输入使用区域',
					event: 'multipled',
					rules: {
						required: true,
						message: '请输入使用区域',
						trigger: 'blur'
					},
					type: 'select',
					filterable: true,
					data: (() => {
						const list = JSON.parse(JSON.stringify(this.treeData || []));
						list.shift();
						return list;
					})(),
					'value-key': 'areaCode',
					'label-key': 'areaName',
					verify: 'required',
					col: 12
				}
				// {
				// 	name: 'remark',
				// 	label: '备注',
				// 	placeholder: '请输入备注',
				// 	type: 'textarea',
				// 	col: 12
				// }
			];
		},
		// 列表配置
		table() {
			return {
				url: interfaceUrl.listJson,
				param: { handlerType: '' },
				full: true,
				fit: true,
				page: true,
				stripe: true,
				numbers: true,
				// checkbox: true,
				'row-key': 'id',
				toolbar: [
					{
						type: 'button',
						contents: [
							{
								text: '新增',
								type: 'primary',
								code: 'toolbar'
							}
							// {
							// 	text: '批量启用',
							// 	type: 'primary',
							// 	code: 'toolbar'
							// },
							// {
							// 	text: '批量停用',
							// 	type: 'danger',
							// 	code: 'toolbar'
							// },
							// {
							// 	text: '批量导入',
							// 	type: '',
							// 	code: 'toolbar'
							// }
						]
					},
					{
						type: 'search',
						reset: true,
						contents: [
							{
								col: 6,
								name: 'keyword',
								label: '关键字',
								placeholder: '设备名称'
							}
						]
					}
				],
				thead: [
					{
						title: '设备名称',
						align: 'center',
						field: 'deviceName'
					},
					{
						title: '设备编码',
						align: 'center',
						field: 'deviceCode'
					},
					{
						title: '区域',
						align: 'center',
						field: 'areaTxt'
					},
					{
						title: '操作',
						type: 'handle',
						width: 150,
						template: '',
						events: [
							{
								code: 'view',
								text: '查看'
							},
							{
								code: 'edit',
								text: '编辑'
							},
							{
								code: 'channel',
								text: '管理通道'
							},
							{
								code: 'delete',
								text: '删除'
							}
						]
					}
				]
			};
		}
	},
	watch: {},
	created() {
		this.getTreeData();
	},
	mounted() {},
	methods: {
		// 请求树数据
		getTreeData() {
			this.$request({
				url: interfaceUrl.areaListJson,
				params: { status: 1, pageNum: 1, pageSize: -1, asc: true, orderBy: 'sortNum' },
				method: 'GET'
			}).then(res => {
				this.treeData = res.results.records;
				// 最前面添加个全部
				this.treeData.unshift({
					areaCode: '',
					areaName: '全部'
				});
			});
		},
		handleNodeClick(e) {
			this.params.area = e.areaCode;
			this.$refs.dataTable.reload();
		},

		// 初始化扩展数据，字典等
		successAfter(data) {
			this.extraData = data.results.extraData;
		},
		// 数据列表多选回调
		handleSelectionChange(data) {
			let ids = [];
			data.forEach(row => {
				ids.push(row.id);
			});
			this.selectRowData = data;
			this.selectRowIds = ids;
		},
		handleFormAudit(val) {
			console.log(val);
		},
		/**
		 * 表格行高
		 */
		tableRowClassName({ row, rowIndex }) {
			let styleRes = {
				height: '54px !important'
			};
			return styleRes;
		},
		inputChange(key, value) {},
		hadeSubmit(data) {},
		/**
		 * 操作按钮点击事件
		 * @param {*} res
		 */
		btnClick(res) {
			let text = res.handle.text;
			switch (text) {
				case '编辑':
					this.viewPageLoad(res.row.id, '编辑');
					break;
				case '查看':
					this.viewPageLoad(res.row.id, '查看');
					break;
				case '删除':
					this.deleteRow(res.row);
					break;
				case '管理通道':
					this.ownId = res.row.id;
					this.deviceCode = res.row.deviceCode;
					this.formTitleChannel = '管理通道-' + res.row.deviceName;
					this.showChannel = true;
					break;
				case '新增':
					this.viewPageLoad('', '新增');
					break;
				default:
					break;
			}
		},
		viewPageLoad(id, formTitle) {
			this.formTitle = formTitle;
			this.ownId = id || uuidv4();
			if (formTitle === '新增') {
				this.formData = {
					id: this.ownId
				};
				this.showForm = true;
				return;
			}
			this.$request({
				url: interfaceUrl.info + '/' + id,
				method: 'GET'
			}).then(res => {
				this.formData = res.results;
				this.showForm = true;
			});
		},

		handleClose() {},
		handleFormItemChange() {},
		handleFormSubmit(data) {
			let formData = data;
			delete formData.extMap;
			let url = '';
			if (this.formTitle == '新增') {
				url = interfaceUrl.save;
				let Base64 = require('js-base64').Base64;
				formData['password'] = Base64.encode(formData.pwd);
			} else {
				url = interfaceUrl.update;
			}
			formData.state = formData.state ? 1 : 0;
			if (undefined != formData.photoUrlTemp && undefined != formData.photoUrlTemp.response) {
				formData.photoUrl = formData.photoUrlTemp.response.adjunctId;
			}
			this.$request({
				url: url,
				data: formData,
				method: 'POST'
			}).then(res => {
				if (res.rCode == 0) {
					this.$message.success('操作成功');
					this.showForm = false;
					this.formData = {};
					this.$refs.dataTable.reload();
				} else {
					this.formData.state = 1 == this.formData.state ? true : false;
					this.$message.error(res.msg);
				}
			});
		},
		deleteRow(row) {
			this.$confirm(`确定要删除"${row.deviceName}"吗？`, '删除', {
				confirmButtonText: '确定',
				cancelButtonText: '取消',
				type: 'warning'
			})
				.then(() => {
					this.$request({
						url: interfaceUrl.deleteById,
						data: { id: row.id },
						method: 'POST'
					}).then(res => {
						if (res.rCode == 0) {
							this.$refs.dataTable.reload();
							this.$message.success('删除成功');
						} else {
							this.$message.error(res.msg);
						}
						this.showDelete = false;
					});
				})
				.catch(() => {});
		}
	}
};
</script>
<style scoped lang="scss">
.content {
	width: 100%;
	height: 100%;
	padding: 10px;
}
//.el-row,
//.el-col,
//.table-box {
//	height: 100%;
//	width: 100%;
//}
.el-col {
	height: calc(100vh - 66px);
}
</style>
