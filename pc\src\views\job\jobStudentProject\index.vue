<template>
  <div class="content" style="height: 100%">
    <div style="width:100%;height: 100%">
      <es-data-table :row-style="tableRowClassName" v-if="true" ref="table" :key="tableCount" :full="true" :fit="true" :thead="thead" :toolbar="toolbar"
                     @btnClick="btnClick" @selection-change="handleSelectionChange" @edit="changeTable"
                     :page="page" :url="dataTableUrl" :param="dataTableParam" :border="true" :numbers="true" :option-data="optionData" checkbox form>
      </es-data-table>
      <el-dialog :title="formTitle" v-if="showInfoPage" :visible.sync="showInfoPage" width="80%" append-to-body>
        <div style="height: 90vh">
          <infoPage ref="infoPage" v-on:activelyClose="closeInfoPage" :base-data="this.formData" :info-page-mode="this.infoPageMode"></infoPage>
        </div>
      </el-dialog>
    </div>
  </div>
</template>

<script>
import api from '@/http/job/jobStudentProject/api';
import InfoPage from "@/views/job/jobStudentProject/infoPage.vue";
import {fileAccess} from "../../../../config/config";
import SnowflakeId from "snowflake-id";

export default {
  components: {InfoPage},
  data() {
    return {
      formData: {},
      page: {
        pageSize: 20,
        totalCount: 0,
      },
      infoPageMode: 'allOn',
      selectRowData: [],
      selectRowIds: [],
      showInfoPage: false,
      tableCount: 1,
      dialogType: "",
      dataTableUrl: api.jobStudentProjectList,
      dataTableParam: {  },
      formTitle: '',
      validityOfDateDisable: false,
      thead: [],
      toolbar: [],
      editToolbar:[
        {
          type: 'button',
          contents: [
            {
              text: '查看',
              code: 'toolbar',
              type: 'primary'
            },
            {
              text: '删除',
              code: 'toolbar',
              type: 'danger'
            },
            // {
            //   text: '新增',
            //   code: 'toolbar',
            // },
          ]
        },
        {
          type: 'search',
          reset: true,
          contents: [
            {
              type: 'text',
              name: 'keyword',
              placeholder: '关键字查询',
              clearable: true,
            },
            {
              name: 'auditStatus',
              type: 'select',
              label: '项目状态',
              placeholder: '项目状态',
              col: 3,
              clearable: true,
              data: [
                { pid: '0', value: '0', label: '待审核' },
                { pid: '1', value: '1', label: '已通过' },
                { pid: '2', value: '2', label: '已驳回' },
                { pid: '3', value: '3', label: '已结束' }
              ]
            },
            {
              type: 'date',
              col: 6,
              name: 'startDate',
              label: '申请时间(开始)',
              placeholder: '申请时间(开始)'
            },
            {
              type: 'date',
              col: 6,
              name: 'endDate',
              label: '申请时间(结束)',
              placeholder: '申请时间(结束)'
            }
          ]
        },
      ],
      listThead: [
        {
          title: '项目名称',
          width: "300px",
          align: 'left',
          field: 'name',
          sortable: 'custom',
          showOverflowTooltip: true,
        },
        {
          title: '项目地点',
          align: 'left',
          field: 'address',
          sortable: 'custom',
          showOverflowTooltip: true,
        },
        {
          title: '发布人',
          width: "130px",
          align: 'center',
          field: 'createUser',
          sortable: 'custom',
          showOverflowTooltip: true,
        },
        {
          title: '发布时间',
          width: "150px",
          align: 'center',
          field: 'createTime',
          sortable: 'custom',
          showOverflowTooltip: true,
        },
        {
          title: '状态',
          width: "100px",
          field: 'status',
          align: 'center',
          type: 'switch',
        },
        {
          title: '发布状态',
          width: "110px",
          align: 'center',
          field: 'auditStatus',
          sortable: 'custom',
          showOverflowTooltip: true,
          render: (h, params) => {
            return h(
                'el-tag',
                {props:{ type: params.row.auditStatus === 2? 'danger':'' } },
                params.row.auditStatusVO
            )
          }
        },
      ],
      btnJson: {
        title: '操作',
        type: 'handle',
        width: 180,
        template: '',
        events: [
          {
            code: 'row',
            text: '查看'
          },
          {
            code: 'row',
            text: '审核',
            rules: rows => {
              return rows.auditStatusVO === '待审核';
            }
          },
          {
            code: 'row',
            text: '编辑'
          },
          {
            code: 'row',
            text: '删除'
          },
        ]
      },
      optionData: {
        status: [
          {
            value: 1,
            name: '启用'
          },
          {
            value: 0,
            name: '禁用'
          }
        ]
      },
    };
  },
  created() {
    //初始化查询待审核列表
    this.thead = this.getListThead(this.btnJson);
    this.toolbar = this.editToolbar;
  },
  methods: {
    /**
     * 表格行高
     */
    tableRowClassName() {
      return {
        "height": "54px !important"
      };
    },
    btnClick(res){
      let text = res.handle.text;
      let code = res.handle.code;

      if(code === 'row'){
        switch (text){
          case '查看':
          case '审核':
          case '编辑': this.openInfoPage(res.row.id, text); break;
          case '删除': this.deleteRows([res.row.id]); break;
        }
      }else {
        switch (text){
          case '新增': this.openInfoPage(null, text); break;
          case '查看':
            if(this.selectRowIds.length>1){
              this.$message.warning('只能选择一个查看');
            }else if(this.selectRowIds.length<1){
              this.$message.warning('请选择一个进行查看');
            }else {this.openInfoPage(this.selectRowIds[0], text);}
            break;
          case '删除':
            if(this.selectRowIds.length<1){
              this.$message.warning('请选择一个删除');
            }else {this.deleteRows(this.selectRowIds);}
            break;
        }
      }
    },
    //打开infoPage
    openInfoPage(id, pageMode){
      if(pageMode !== '新增'){
        this.$request({
          url: api.jobStudentProjectInfo,
          params: { id: id},
          method: 'GET'
        }).then(res => {
          // 处理返回数据
          this.formData = {...res.results};
          if(this.formData.cover != null && this.formData.cover !== '')
            this.formData.coverUrl = fileAccess + this.formData.cover;

          this.formTitle = pageMode;
          this.formData.status = this.formData.status === 1;
          this.infoPageMode = pageMode;
          this.showInfoPage = true;
        });
      }else {
        const snowflake = new SnowflakeId();
        this.formTitle = pageMode;
        this.formData = { id: snowflake.generate()};
        this.formData.status = false;
        this.formData.auditStatus = 0;
        this.infoPageMode = pageMode;
        this.showInfoPage = true;
      }
    },
    //关闭infoPage
    closeInfoPage(reload){
      this.formData = {};
      this.showInfoPage = false;
      if(reload)
        this.$refs.table.reload();
    },
    // 数据列表多选回调
    handleSelectionChange(data) {
      let ids = [];
      data.forEach(row => {
        ids.push(row.id);
      });
      this.selectRowData = data;
      this.selectRowIds = ids;
    },
    deleteRows(ids){
      this.$confirm('确定要删除选中的数据吗？', '删除', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      })
          .then(() => {
            this.$request({
              url: api.jobStudentProjectDeleteByIds,
              data: { ids: ids.join(',') },
              method: 'POST'
            }).then(res => {
              if (res.rCode === 0) {
                this.$message.success('操作完成');
                this.$refs.table.reload();
              } else {
                this.$message.error(res.msg);
              }
            });
          })
          .catch(() => {});
    },
    getListThead(btnJson){
      let tempThead = [...this.listThead];
      tempThead.push(btnJson);
      return tempThead;
    },
    changeTable(val) {
      switch (val.name){
        case 'status': this.changeStatus(val.data.id); break;
      }
    },
    //改变数据启用状态
    changeStatus(id) {
      this.$request({
        url: api.jobStudentProjectChangeStatus,
        data: {id: id},
        method: 'POST'
      }).then(res=>{
        if(res.success){
          this.$message.success('修改成功');
        }else{
          this.$message.error(res.msg);
        }
      });
    }
  },
};
</script>