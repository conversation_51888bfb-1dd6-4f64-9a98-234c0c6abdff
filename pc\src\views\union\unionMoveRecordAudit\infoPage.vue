<template>
  <div class="sketch_content">
    <el-form :model="formData" class="resumeTable" ref="form">
      <el-row>
        <el-col :span="11">
          <el-form-item label="申请人" label-width="90px" label-position="left" prop="memberName">
            <el-input v-model="formData.memberName" :disabled="infoDisabled"></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="11">
          <el-form-item label="所属学院" label-width="90px" label-position="left" prop="collegeName">
            <el-input v-model="formData.collegeName" :disabled="infoDisabled"></el-input>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row>
        <el-col :span="11">
          <el-form-item label="工号" label-width="90px" label-position="left" prop="jobNumber">
            <el-input v-model="formData.jobNumber" :disabled="infoDisabled" ></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="11">
          <el-form-item label="联系电话" label-width="90px" label-position="left" prop="telephone">
            <el-input v-model="formData.telephone" :disabled="infoDisabled" ></el-input>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row>
        <el-col :span="11">
          <el-form-item label="职务" label-width="90px" label-position="left" prop="duties">
            <el-input v-model="formData.duties" :disabled="infoDisabled"></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="11">
          <el-form-item label="在编/非在编" label-width="100px" label-position="left" prop="isSystem">
            <es-select v-model="formData.isSystem" sys-code="union_is_system" value-key="isSystem" :disabled="infoDisabled"></es-select>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row>
        <el-col :span="11">
          <el-form-item label="申请时间" label-width="90px" label-position="left" prop="createTime">
            <el-input v-model="formData.createTime" :disabled="infoDisabled"></el-input>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row>
        <el-col :span="22">
          <el-form-item label="详细信息" label-width="90px" label-position="left" prop="duties">
            <el-input v-model="formData.remark" type="textarea" :disabled="infoDisabled"></el-input>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row>
        <el-col :span="22">
          <el-form-item label="附件" label-width="90px" label-position="left" prop="duties">
            <es-upload v-bind="attrs" select-type="icon-plus" list-type="picture-card" :disabled="infoDisabled"></es-upload>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row>
        <el-col :span="3" style="float:right">
          <el-button type="reset" @click="infoPageClose(false)">取消</el-button>
        </el-col>
        <el-col :span="3" style="float:right">
          <el-button type="primary" @click="handleFormSubmit('审核通过')" v-show="!auditDisabled">审核通过</el-button>
        </el-col>
        <el-col :span="3" style="float:right">
          <el-button type="danger" @click="handleFormSubmit('驳回')" v-show="!auditDisabled">驳回</el-button>
        </el-col>
      </el-row>
    </el-form>
  </div>
</template>
<script>

import api from "@/http/union/unionMoveRecordAudit/api";

export default {
  name: 'infoPage',
  props: {
    baseData: {
      type: Object
    },
    infoPageMode: {
      type: String
    }
  },
  data() {
    return {
      infoDisabled: true,
      auditDisabled: true,
      formData: {},
      pageMode: 'allOn',
    };
  },
  computed: {
    attrs() {
      return {
        code: 'union_move_record_adjunct',
        ownId: this.formData.id,
        preview: true,
        download: true,
        operate: false,
        "auto-upload": true,
      }
    }
  },
  created() {
    this.formData = { ...this.baseData };
    this.pageMode = this.infoPageMode;
    switch (this.pageMode){
      case'新增':
        this.infoDisabled = true;
        this.auditDisabled = true;
        break;
      case'查看':
        this.infoDisabled = true;
        this.auditDisabled = true;
        break;
      case'审核':
        this.infoDisabled = true;
        this.auditDisabled = false;
        break;
    }
  },
  methods: {
    handleFormSubmit(res) {
      let auditData = {};
      //处理数据
      auditData.id = this.formData.id;
      auditData.auditStatus = res === '审核通过' ? 1:2;

      this.$request({
        url: api.audit,
        data: auditData,
        method: 'POST'
      }).then(res => {
        if (res.rCode === 0) {
          this.$message.success('操作成功');
          this.infoPageClose(true);
        } else {
          this.$message.error(res.msg);
        }
      });
    },
    infoPageClose(reload){
      this.pageMode = 'allOn';
      this.infoDisabled = false;
      this.$emit('activelyClose',reload);
    },
  },
  watch: {
  }
};
</script>
<style scoped>
  .sketch_content {
    overflow: auto;
    height: 100%;
    border-top: 1px solid #eff1f4;
    border-bottom: 1px solid #eff1f4;
    padding: 0 30px 11px 27px;
  }
  .resumeTable td {
    height: 50px;
    font-weight: bold;
  }

  ::v-deep .el-collapse-item__header.is-active {
    border-bottom: 1px solid #ebeef5;
    font-size: 18px;
    font-weight: bold;
  }
  ::v-deep .el-collapse-item__header::before {
    content: "";
    width: 4px;
    height: 18px;
    background-color: #0076E9;
    margin-right: 2px;
  }
</style>