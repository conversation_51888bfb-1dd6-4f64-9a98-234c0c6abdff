<template>
	<div class="content">
		<es-data-table
			ref="table"
			:row-style="tableRowClassName"
			:full="true"
			:fit="true"
			:thead="thead"
			:toolbar="toolbar"
			:border="true"
			:page="pageOption"
			:url="dataTableUrl"
			:numbers="true"
			:param="params"
			close
			form
			@btnClick="btnClick"
			@sort-change="sortChange"
			@submit="hadeSubmit"
			@edit="changeTable"
		></es-data-table>
		<es-dialog
			:title="formTitle"
			:visible.sync="showForm"
			width="800px"
			height="500px"
			:close-on-click-modal="false"
			:destroy-on-close="true"
		>
			<es-form
				ref="form"
				:model="formData"
				:contents="formItemList"
				height="400px"
				:genre="2"
				collapse
				@change="inputChange"
				@submit="handleFormSubmit"
				@reset="showForm = false"
			/>
		</es-dialog>
	</div>
</template>

<script>
import apiUrl from '@/http/job/jobCoInterview/api';
export default {
	data() {
		return {
			ownId: '',
			dataTableUrl: apiUrl.listJson,
			showForm: false,
			showRoleForm: false,
			enpList: [],
			formData: {},
			formTitle: '编辑',
			editBtn: {
				type: 'submit',
				skin: 'lay-form-btns',
				contents: [
					{
						type: 'primary',
						text: '确定',
						event: 'confirm'
					},
					{
						type: 'reset',
						text: '取消',
						event: 'cancel'
					}
				]
			},
			cancelBtn: {
				type: 'submit',
				skin: 'lay-form-btns',
				contents: [
					{
						type: 'reset',
						text: '取消',
						event: 'cancel'
					}
				]
			},
			toolbar: [
				{
					type: 'search',
					contents: [
						// {
						// 	type: 'select',
						// 	placeholder: '是否面邀',
						// 	name: 'isInvite',
						// 	event: 'multipled',
						// 	data: [
						// 		{ value: 1, name: '是' },
						// 		{ value: 0, name: '否' }
						// 	],
						// 	clearable: true,
						// 	col: 4
						// },
						{
							type: 'text',
							name: 'keyword',
							placeholder: '关键字查询'
						}
					]
				}
			],
			thead: [
				{
					title: '企业名称',
					align: 'left',
					field: 'enterpriseNameAndDes',
					showOverflowTooltip: true
				},
				{
					title: '岗位名称',
					align: 'left',
					field: 'postName',
					showOverflowTooltip: true
				},
				{
					title: '面试人姓名',
					align: 'left',
					field: 'userName',
					showOverflowTooltip: true
				},
				{
					title: '面试时间',
					align: 'left',
					field: 'interviewDate',
					render: (h, param) => {
						return h(
							'p',
							{ calss: { p1: true } },
							this.formatLink(param.row.interviewDate, param.row.interviewTime)
						);
					},
					showOverflowTooltip: true
				},
				{
					title: '面试地址',
					align: 'left',
					field: 'interviewAddress'
				},
				{
					title: '面试状态',
					align: 'center',
					width: 120,
					field: 'interviewStatusDes'
				},
				{
					title: '面邀时间',
					align: 'center',
					width: 160,
					field: 'createTime'
					// valueFormat: "yyyy-MM-dd"
				},
				{
					title: '操作',
					type: 'handle',
					width: 120,
					template: '',
					events: [
						{
							code: 'view',
							text: '查看'
						}
					]
				}
			],
			pageOption: {
				layout: 'total, prev, pager, next, jumper',
				pageSize: 10,
				// hideOnSinglePage: true,
				position: 'right',
				current: 1,
				pageNum: 1
			},
			params: {
				postId: null,
				asc: 'false',
				orderBy: 'createTime'
			}
		};
	},
	computed: {
		formItemList() {
			return [
				{
					label: '公司名',
					name: 'enterpriseNameAndDes',
					placeholder: '',
					event: 'multipled',
					rules: {
						required: false,
						message: '',
						trigger: 'blur'
					},
					verify: 'required',
					col: 12
				},
				{
					label: '岗位名称',
					name: 'postName',
					placeholder: '',
					event: 'multipled',
					rules: {
						required: false,
						message: '',
						trigger: 'blur'
					},
					verify: 'required',
					col: 6
				},
				{
					label: '岗位薪资',
					name: 'postSalary',
					placeholder: '',
					event: 'multipled',
					rules: {
						required: false,
						message: '',
						trigger: 'blur'
					},
					verify: 'required',
					col: 6
				},
				{
					label: '面试人姓名',
					name: 'userName',
					placeholder: '',
					event: 'multipled',
					rules: {
						required: false,
						message: '',
						trigger: 'blur'
					},
					verify: 'required',
					col: 6
				},
				{
					label: '面试时间',
					name: 'interviewDate',
					placeholder: '',
					event: 'multipled',
					rules: {
						required: false,
						message: '',
						trigger: 'blur'
					},
					verify: 'required',
					col: 6
				},
				{
					label: '面试时间段',
					name: 'interviewTime',
					placeholder: '',
					event: 'multipled',
					rules: {
						required: false,
						message: '',
						trigger: 'blur'
					},
					verify: 'required',
					col: 6
				},
				{
					label: '面试地址',
					name: 'interviewAddress',
					placeholder: '',
					event: 'multipled',
					rules: {
						required: false,
						message: '',
						trigger: 'blur'
					},
					verify: 'required',
					col: 6
				},
				{
					label: '面试状态',
					name: 'interviewStatusDes',
					placeholder: '',
					event: 'multipled',
					rules: {
						required: false,
						message: '',
						trigger: 'blur'
					},
					verify: 'required',
					col: 6
				},
				{
					label: '面试邀请时间',
					name: 'createTime',
					placeholder: '',
					event: 'multipled',
					rules: {
						required: false,
						message: '',
						trigger: 'blur'
					},
					verify: 'required',
					col: 6
				}
			];
		}
	},
	watch: {},
	created() {},
	mounted() {},
	methods: {
		/**
		 * 表格行高
		 */
		tableRowClassName({ row, rowIndex }) {
			let styleRes = {
				height: '54px !important'
			};
			return styleRes;
		},
		//合并表格列
		formatLink(date, time) {
			var des = '';
			if (date && time) {
				des = date + ' ' + time;
			} else {
				if (date) {
					des = date;
				}
			}
			return des;
		},
		inputChange(key, value) {},
		hadeSubmit(data) {},
		/**
		 * 操作按钮点击事件
		 * @param {*} res
		 */
		btnClick(res) {
			let code = res.handle.code;
			switch (code) {
				// case 'edit':
				// 	// 编辑
				// 	this.formTitle = '编辑';
				// 	this.ownId = res.row.id;
				// 	this.editModule(this.formItemList, []);
				// 	this.$request({
				// 		url: apiUrl.info + '/' + res.row.id,
				// 		method: 'GET'
				// 	}).then(res => {
				// 		if (res.rCode == 0) {
				// 			this.formData = res.results;
				// 			this.showForm = true;
				// 		}
				// 	});
				// 	break;
				case 'view':
					this.formTitle = '查看';
					this.ownId = res.row.id;
					this.readModule(this.formItemList, []);
					this.$request({
						url: apiUrl.info + '/' + res.row.id,
						method: 'GET'
					}).then(res => {
						if (res.rCode == 0) {
							this.formData = res.results;
							this.showForm = true;
						}
					});
					break;
				default:
					break;
			}
		},
		handleClose() {},
		handleFormItemChange() {},
		handleFormSubmit(data) {
			let formData = data;
			delete formData.extMap;
			let url = '';
			if (this.formTitle == '新增') {
				url = apiUrl.save;
			} else {
				url = apiUrl.update;
			}
			this.$request({
				url: url,
				data: formData,
				method: 'POST'
			}).then(res => {
				if (res.rCode == 0) {
					this.$message.success('操作成功');
					this.showForm = false;
					this.formData = {};
					this.$refs.table.reload();
				} else {
					this.$message.error(res.msg);
				}
			});
		},
		/**
		 * 排序变化事件
		 * @param {*} column
		 * @param {*} prop
		 * @param {*} order
		 */
		sortChange(column, prop, order) {
			if (column.order == 'ascending') {
				//升序
				this.params = {
					asc: 'true',
					orderBy: column.prop
				};
			} else if (column.order == 'descending') {
				//降序
				this.params = {
					asc: 'false',
					orderBy: column.prop
				};
			} else {
				//不排序
				this.params = {
					asc: 'false',
					orderBy: 'createTime'
				};
			}
			this.$refs.table.reload();
		},
		/**
		 * 编辑模式
		 */
		editModule(list, hideField) {
			for (var i in list) {
				var item = list[i];
				item.readonly = false;
				if (undefined != hideField && hideField.length > 0 && hideField.indexOf(item.name) > -1) {
					item.hide = true;
				}
			}
			list.push(this.editBtn);
		},
		/**
		 * 只读模式
		 */
		readModule(list, hideField) {
			for (var i in list) {
				var item = list[i];
				item.readonly = true;
				if (undefined != hideField && hideField.length > 0 && hideField.indexOf(item.name) > -1) {
					item.hide = true;
				}
			}
			list.push(this.cancelBtn);
		},
		changeTable(val) {
			if ('isPractice' === val.name) {
				this.$request({
					url: apiUrl.updatePractice,
					data: {
						id: val.data.id
					},
					method: 'POST'
				}).then(res => {
					if (res.rCode == 0) {
						this.$message.success('设置成功！');
					} else {
						this.$message.error(res.msg);
					}
				});
			}
		}
	}
};
</script>
<style scoped lang="scss">
@import '../../../assets/style/style.scss';

.content {
	display: flex;
	width: 100%;
	height: 100%;

	::v-deep .es-data-table {
		flex: 1;
		display: flex;
		flex-direction: column;

		.es-data-table-content {
			flex: 1;
			height: 0;
			display: flex;
			flex-direction: column;

			.el-table {
				flex: 1;
				// height: 100% !important;
			}

			.es-thead-border {
				.el-table__header {
					th {
						border-right: 1px solid #e1e1e1;
					}
				}
			}
		}
	}

	::v-deep .el-form-item__label {
		background: none;
		border: 0px solid #c2c2c2;
	}
}

.el-dialog__body {
	.btn-box {
		margin-top: 20px;
		display: flex;
		justify-content: flex-end;

		.btn {
			padding: 5px 10px;
			color: #666;
			border: 1px solid #eee;
			cursor: pointer;
			margin-right: 5px;
			border-radius: 4px;
		}
	}
}
</style>
