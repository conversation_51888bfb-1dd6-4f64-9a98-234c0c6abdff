<template>
	<div class="serve-hall">
		<!-- 顶部导航栏 -->
		<div class="hall-top">
			<VocationalHeader class="box-center"></VocationalHeader>
		</div>
		<!-- 顶部展示图片 -->
		<img src="@/assets/images/serveHall/top-bg.png" class="navbar-top" />
		<!-- 通知公告 -->
		<div class="notice">
			<!-- 公告部分 -->
			<div class="notice-box box-center">
				<div class="notice-left">
					<span class="text">通知公告</span>
					<span class="btn" @click="jumpPage('/notice')">more ></span>
				</div>
				<img src="@/assets/images/serveHall/notice-bg.png" class="bg-img" />
				<div class="notice-right">
					<p v-for="(item, index) in noticeList" :key="index" class="item" @click="onDetail(item)">
						<span class="time">{{ item.createTime | dateShow }}</span>
						<span class="title ellipsis-1">{{ item.title }}</span>
						<span class="status" v-if="item.isRead == 0">未读</span>
					</p>
				</div>
			</div>
			<!-- 搜索栏部分 -->
			<div class="search">
				<search @onSearch="onSearch" />
			</div>
		</div>
		<!-- 系统直通车 -->
		<div class="system">
			<div class="system-box box-center">
				<p class="title">系统直通车</p>
				<p class="sub-title">SYSTEM THROUGH TRAIN</p>
				<div class="system-list">
					<template v-for="(item, index) in myAppData">
						<div :key="index" class="item" @click="myAppDataBtn(item)">
							<!-- item.name == '' ? false : true -->
							<img :src="item.logo" class="item-img" />
							<span class="name ellipsis-1">{{ item.name }}</span>
							<span class="desc ellipsis-1">{{ item.remark }}</span>
							<div
								v-if="item.type != 'more'"
								:class="['collect-box', collectedList.includes(item.id) && 'collected']"
								@click.stop="
									collectedList.includes(item.id) ? deleteBatchIds(item, 0) : saveCollect(item, 0)
								"
							>
								<template v-if="collectedList.includes(item.id)">
									<i class="el-icon-star-on icon"></i>
									<span>已收藏</span>
								</template>
								<template v-else>
									<i class="el-icon-star-off icon"></i>
									<span>收藏</span>
								</template>
							</div>
							<img src="@/assets/images/serveHall/active-bg.png" class="active-bg" />
						</div>
					</template>
					<Empty v-if="!myAppData.length" text="暂无数据" />
				</div>
			</div>
		</div>
		<!-- 业务直通车 -->
		<div class="service">
			<div class="service-box box-center">
				<p class="title">业务直通车</p>
				<p class="sub-title">SERVICE THROUGH TRAI</p>
				<div class="service-bar">
					<el-tabs v-model="activeName" @tab-click="handleClick">
						<el-tab-pane
							v-for="(item, index) in serviceList.slice(0, 15)"
							:key="index"
							:label="item.text"
							:name="item.text"
						></el-tab-pane>
					</el-tabs>
					<span class="btn" @click="jumpPage('/serveCar')">more ></span>
				</div>
				<div class="service-list">
					<template v-for="(item, index) in serviceData">
						<div :key="index" v-if="index <= 14" class="service-item" @click="toOpenWindow(item)">
							<img :src="handelPicUrl(item.icons)" class="item-img" />
							<div class="item-right">
								<span class="name ellipsis-1">{{ item.text }}</span>
								<span class="desc ellipsis-1">{{ item.appCode }}</span>
							</div>
							<div
								:class="['collect-box', collectedList.includes(item.id) && 'collected']"
								@click.stop="
									collectedList.includes(item.id) ? deleteBatchIds(item, 1) : saveCollect(item, 1)
								"
							>
								<template v-if="collectedList.includes(item.id)">
									<i class="el-icon-star-on icon"></i>
									<span>已收藏</span>
								</template>
								<template v-else>
									<i class="el-icon-star-off icon"></i>
									<span>收藏</span>
								</template>
							</div>
							<img src="@/assets/images/serveHall/active-bg.png" class="active-bg" />
						</div>
					</template>
					<Empty v-if="!serviceData.length" text="暂无数据" />
				</div>
			</div>
		</div>
		<!-- 任务中心 和 最近使用 -->
		<div class="task">
			<div class="task-box box-center">
				<!-- 任务中心 -->
				<div class="task-card">
					<div class="task-title">
						<div class="title">
							<span>任务中心</span>
							<span class="more-btn" @click="taskMore">more ></span>
						</div>
					</div>
					<div class="task-list">
						<div class="left">
							<div
								v-for="(item, index) in taskType"
								:key="index"
								:class="['left-item', item.code == taskActive && 'left-item-active']"
								@click="taskHandleClick(item)"
							>
								<img :src="item.url" class="item-img" />
								<img :src="item.activeUrl" class="item-img-active" />
								<span>{{ item.name }}</span>
							</div>
						</div>
						<div
							v-loading="taskLoading"
							class="right"
							element-loading-background="rgba(0, 0, 0, 0)"
						>
							<Empty
								v-if="!taskList.length"
								:text="taskActive == 'todo' ? '真好，您的事情都已经办完了' : '暂无发起的任务哦'"
							/>
							<template v-else>
								<div
									class="right-item"
									v-for="(item, index) in taskList"
									:key="index"
									@click="taskJump(item)"
								>
									<span class="item-time">
										{{ taskActive == 'send' ? item.endtime : item.createtime }}
									</span>
									<span class="item-title ellipsis-1">{{ item.bname }}</span>
									<span class="item-desc">
										<span class="desc-text ellipsis-1">
											{{ taskActive == 'send' ? item.pitemname : item.pendtitle }}
										</span>
										<span v-if="taskActive == 'send'" class="item-status">{{ item.state }}</span>
									</span>
								</div>
							</template>
						</div>
					</div>
				</div>
				<!-- 最新使用 -->
				<div class="task-card last-used">
					<div class="title">最近使用</div>
					<div class="used-box">
						<template v-for="(item, index) in recentList">
							<div
								v-if="item"
								:key="index"
								class="used-item"
								@click="item.resourceType ? myAppDataBtn(item) : toOpenWindow(item)"
							>
								<img
									:src="item.resourceType ? item.logo : handelPicUrl(item.icons)"
									class="item-img"
								/>
								<span class="name ellipsis-2">{{ item.resourceType ? item.name : item.text }}</span>
								<img src="@/assets/images/serveHall/active-bg.png" class="active-bg" />
							</div>
						</template>
						<Empty v-if="!recentList.length" text="您暂时还没有已使用的服务" />
					</div>
				</div>
			</div>
		</div>
		<es-dialog title="业务直通车" size="max" :visible.sync="visible" ref="visible">
			<iframe
				ref="iframe"
				src=""
				width="100%"
				height="100%"
				frameborder="0"
				allowfullscreen
				allow-same-origin
			>
				<div>浏览器不兼容</div>
			</iframe>
		</es-dialog>
		<Navigation ref="navigation" @callBack="getRecent" />
	</div>
</template>

<script>
import UsullySet from './components/UsullySet.vue';
import Usefulapp from './components/usefulapp.vue';
import Backlog from './components/backlog.vue';
import Empty from './components/empty.vue';
import VocationalHeader from './header/VocationalHeader.vue';
import headAvator from './header/headAvator.vue';
import search from './components/search.vue';
import Navigation from './components/navigation.vue';

import { tenantId, alumniUrl } from '@/config';
import { getTransactionList, checkLogin, getEossAuthentication } from '@/api/home.js';
export default {
	name: 'ServeHall',
	components: {
		UsullySet,
		Usefulapp,
		Backlog,
		VocationalHeader,
		headAvator,
		Empty,
		search,
		Navigation
	},
	created() {
		let value = localStorage.getItem('userInfo');
		let parsedValue = JSON.parse(value);
		this._userinfo = parsedValue;
	},
	data() {
		return {
			_userinfo: {},
			// 2021/1/10
			keyWord: '', //关键字
			visible: false, //iframe展示判断
			noticeList: [], //通知公告数据
			myAppDataTotol: [], //系统直通车所有数据
			myAppData: [], //系统直通车展示的数据
			myAppMore: [
				{
					logo: require('@/assets/images/serveHall/more-serve-icon.png'),
					name: '更多服务',
					remark: 'More',
					url: '/serveCar?type=system',
					type: 'more'
				}
			], //系统直通车更多的展示数据
			serviceList: [], //业务所有数据
			serviceData: [], //业务当前显示的列表数据
			activeName: '全部', //业务直通车选中分类
			serviceIndex: '0', //当前选中业务分类的下标，用于筛选时的数据展示问题
			taskType: [
				{
					name: '我的待办',
					code: 'todo',
					url: require('@/assets/images/serveHall/task-dealt.png'),
					activeUrl: require('@/assets/images/serveHall/task-dealt-active.png')
				},
				{
					name: '我发起的',
					code: 'send',
					url: require('@/assets/images/serveHall/task-send.png'),
					activeUrl: require('@/assets/images/serveHall/task-send-active.png')
				}
			], //任务中心分类
			taskActive: 'todo', //任务中心当前选中
			taskLoading: false, //任务模块加载动画
			taskList: [], //任务列表
			collectedList: [], //收藏列表id合集
			recentList: [] //访问列表id合集
		};
	},
	filters: {
		// 日期格式切换
		dateShow(str) {
			let strTime = '';
			if (str) {
				let dateArray = str.split(' ')[0].split('-');
				strTime = dateArray[1] + '.' + dateArray[2];
			}
			return strTime;
		}
	},
	async mounted() {
		this.getInformation(); //获取公告列表
		this.interfaceData('/oa/wfPending/wfPendingMerge/list_json.dhtml', true); //我的待办接口
		// await this.getList(); //系统直通车列表数据
		// await this.getListData(); //业务直通车列表接口
		await Promise.all[(this.getList(), this.getListData(), this.getRecent())];
		this.getCollected(); //查询收藏
		// this.getRecent(); //查询最近最近访问
	},
	methods: {
		/**
		 * @description 获取公告列表
		 * */
		getInformation() {
			let data = {
				nodeCode: 'educationServiceNotice',
				tenantId: tenantId,
				pageNum: 1,
				pageSize: 2
			};
			this.$.ajax({
				url: '/ybzy/specificHomepageShortcut/paging',
				params: data
			}).then(res => {
				this.noticeList = res?.results?.records || [];
			});
		},
		// 系统直通车列表数据
		getList() {
			let data = {
				pageSize: 11
			};
			this.$.ajax({
				url: '/ybzy/platuser/front/appList',
				method: 'post',
				data: data
			}).then(res => {
				if (res.rCode === 0) {
					this.myAppDataTotol = res.results || [];
					this.myAppData = this.myAppDataTotol.slice(0, 11).concat(this.myAppMore);
					console.log(res.results,'系统直通车列表数据++++++++++++++++++++');
				} else {
					this.$message.error(res.msg);
				}
			});
		},
		//我的待办接口
		interfaceData(url, need) {
			this.taskLoading = true;
			let data = {
				rows: 3,
				page: 1,
				sord: 'desc'
			};
			if (need) {
				data.query_pendingattr = 0;
			}
			this.$.ajax({
				url: url,
				data: data,
				method: 'POST'
			})
				.then(res => {
					this.taskList = res.data || [];
					let name = 'first';
					window.parent.postMessage(name, '*');
				})
				.finally(() => {
					this.taskLoading = false;
				});
		},
		//业务直通车列表接口
		getListData() {
			let userId = localStorage.getItem('mobileUserId') || '';
			/**原有代码基础加上判断，没有授权的话请求授权*/
			if (!userId) {
				// checkLogin()
				this.$.ajax({
					url: checkLogin
				}).then(res => {
					let code = res.results.code;
					localStorage.setItem('ssoCode', res.results.code);
					let data = {
						serverId: 'ybzyDtcSso',
						authType: '6',
						code
					};
					if (!(code === null)) {
						this.$.ajax({
							url: getEossAuthentication,
							method: 'POST',
							data: data
						})
							.then(res => {
								localStorage.setItem('mobileToken', res.results.mobileToken);
								localStorage.setItem('mobileUserId', res.results.userId);
								this.getListData();
							})
							.catch(err => {
								console.log(err, '认证失败err');
								return '认证失败';
							});
					}
				});
			} else {
				this.$.ajax({
					url: getTransactionList + `?menuCode=cygn&userId=${userId}&type=2`
				})
					.then(res => {
						let arr = [];
						let usullayListGet = [];
						res.results.forEach(i => {
							i.children.forEach(item => {
								arr.push(item);
							});
						});
						this.serviceList = [
							{
								children: arr,
								text: '全部'
							}
						];
						this.serviceList = this.serviceList.concat(res.results || []);
						this.serviceData = this.serviceList[0].children;
						console.log('业务直通车列表接口');
					})
					.catch(error => {
						console.log(error, 'error');
					});
			}
		},
		/**
		 * @description 最近访问--查询
		 * */
		getRecent() {
			this.$.ajax({
				url: '/ybzy/specificHomepageShortcut/getList',
				params: {
					orderBy: 'updateTime',
					asc: false,
					type: 1, // 0, "收藏"  1, "最近访问" 2, "公告已读"
					pageSize: 10
				}
			}).then(res => {
				if (res.rCode == 0) {
					let list = res?.results?.records || [];
					list = list.reduce((acc, cur) => {
						acc.push(cur.sourceId);
						return acc;
					}, []);
					// 开始匹配访问列表
					let totalList = this.myAppDataTotol.concat(this.serviceList[0]?.children || []);
					this.recentList = totalList.reduce((acc, cur) => {
						if (list.includes(cur.id)) {
							let index = list.indexOf(cur.id);
							acc[index] = cur;
						}
						return acc;
					}, []);
				}
			});
		},
		/**
		 * @description 最近访问--创建
		 * */
		saveRecent(item, type) {
			let data = {
				sourceId: item.id, //目标Id
				type: 1, // 0, "收藏"  1, "最近访问" 2, "公告已读"
				sourceType: type //目标类型（0：系统，1：菜单）
			};
			this.$.ajax({
				url: '/ybzy/specificHomepageShortcut/save',
				method: 'POST',
				data: data
			}).then(res => {
				if (res.rCode == 0) {
					this.getRecent();
				}
			});
		},
		/**
		 * @description 收藏--查询
		 * */
		getCollected() {
			this.$.ajax({
				url: '/ybzy/specificHomepageShortcut/getList',
				params: {
					pageSize: 1000,
					type: 0 // 0, "收藏"  1, "最近访问" 2, "公告已读"
				}
			}).then(res => {
				if (res.rCode == 0) {
					let list = res?.results?.records || [];
					this.collectedList = list.reduce((acc, cur) => {
						acc.push(cur.sourceId);
						return acc;
					}, []);
				}
			});
		},
		/**
		 * @description 收藏--创建
		 * */
		saveCollect(item, type) {
			let data = {
				sourceId: item.id, //目标Id
				type: 0, // 0, "收藏"  1, "最近访问" 2, "公告已读"
				sourceType: type //目标类型（0：系统，1：菜单）
			};
			this.$.ajax({
				url: '/ybzy/specificHomepageShortcut/save',
				method: 'POST',
				data: data
			})
				.then(res => {
					if (res.rCode == 0) {
						this.$message.success(res.msg);
						this.getCollected();
					} else {
						this.$message.info(res.msg);
					}
				})
				.catch(res => {
					this.$message.warning(res.msg);
				});
		},
		/**
		 * @description 取消收藏--查询
		 * */
		deleteBatchIds(item) {
			this.$.ajax({
				url: '/ybzy/specificHomepageShortcut/deleteCollect',
				method: 'POST',
				data: {
					ids: item.id //目标Id
				}
			})
				.then(res => {
					if (res.rCode == 0) {
						this.$message.success(res.msg);
						this.getCollected();
					} else {
						this.$message.info(res.msg);
					}
				})
				.catch(res => {
					this.$message.warning(res.msg);
				});
		},
		// 跳转公告详情页面
		onDetail(item) {
			this.$router.push({
				path: '/noticeDetail',
				query: {
					id: item.id
				}
			});
		},
		// 业务直通车类型切换
		handleClick(event) {
			this.activeName = event.name || '';
			this.serviceIndex = event.index || 0;
			this.serviceData = this.serviceList[this.serviceIndex].children;
			// 看是否需要正在进行筛选
			this.keyWord && this.onSearch(this.keyWord);
		},
		// 任务中心切换事件
		taskHandleClick(tab) {
			this.taskActive = tab.code;
			this.taskList = [];
			switch (tab.code) {
				case 'todo':
					this.interfaceData('/oa/wfPending/wfPendingMerge/list_json.dhtml', true);
					break;
				case 'send':
					this.interfaceData('/oa/task/wfApplication/me_list_json.dhtml', false);
					break;
				default:
					break;
			}
		},
		// 业务直通车跳转判断
		toOpenWindow(item) {
			if (item.id == '11111111') {
				return;
			}
			// window.open(`${picUrl}${item.newDataUrl}${item.code}`);
			// window.open(`${alumniUrl}${item.url}`);
			this.visible = true;
			this.$nextTick(() => {
				this.$refs.iframe.src = `${alumniUrl}${item.url}`;
			});
			this.saveRecent(item, 1);
		},
		//处理图片路径
		handelPicUrl(url) {
			return `${alumniUrl}/static/andyui2.0/css/picIcon/img/${url}`;
		},
		/**
		 * @description 点击跳转对应页面
		 * */
		jumpPage(url) {
			this.$router.push(url);
		},
		// 应用跳转对应链接判断
		myAppDataBtn(item) {
			if (item.type != 'more') {
				this.saveRecent(item, 0);
			} else {
				return this.jumpPage(item.url);
			}
			// /ybzy/platauth/front/loginEoss?redirectUri=/project-ybzy/ybzy/index.html#/main?cookie=1
			if (item.url.includes('redirectUri=/project-ybzy/ybzy/index.html')) {
				window.location.href = item.url + '&isTeacher=true';
			} else {
				window.open(item.url);
			}
		},
		// 内容搜索事件
		onSearch(value) {
			this.serviceData = this.serviceList[this.serviceIndex]?.children || [];
			// this.keyWord;
			// myAppDataTotol // 系统直通车
			// serviceData //业务直通车
			this.keyWord = value;
			if (!value) {
				// 数据恢复
				this.serviceData = this.serviceList[this.serviceIndex]?.children || [];
				this.myAppData = this.myAppDataTotol.slice(0, 11).concat(this.myAppMore);
			} else {
				let serviceArr = [],
					appList = [];
				// 系统直通车数据筛选
				appList = this.myAppDataTotol.reduce((acc, cur) => {
					if (cur.name.includes(value)) {
						acc.push(cur);
					}
					return acc;
				}, []);
				this.myAppData = appList.slice(0, 11);
				// 业务直通车数据筛选
				serviceArr = this.serviceData.reduce((acc, cur) => {
					if (cur.text.includes(value)) {
						acc.push(cur);
					}
					return acc;
				}, []);
				this.serviceData = serviceArr;
			}
		},
		//业务模块更多跳转
		taskMore() {
			let obj = {
				name: this.taskActive == 'todo' ? '我的代办' : '我发起的'
			};
			this.$refs.navigation.onNavClick(obj);
		},
		// 任务待办跳转链接
		taskJump(e) {
			// window.open(alumniUrl + '/oa/wfPending/wfPending/list.dhtml?serverId=ybzyDtcSso&authType=6');
			let url = e.pendingurl
				.replace(/\[recordid\]/g, e.apprecordid)
				.replace(/\[pendingId\]/g, e.id);
			window.open(alumniUrl + url);
		}
	}
};
</script>

<style lang="scss" scoped>
$maxWidth: 1200px;
.box-center {
	width: $maxWidth;
	margin: 0 auto;
}
.serve-hall {
	width: 100%;
	height: 100%;
	background: #ecf0f4;
	min-width: 1550px;
	position: relative;
	overflow: auto;
	.hall-top {
		width: 100%;
		background: #0175e8;
	}
	.navbar-top {
		width: 100%;
		height: 200px;
		object-fit: cover;
	}
	.notice {
		width: 100%;
		height: 160px;
		background: #eef4f9;
		position: relative;
		.notice-box {
			height: 90px;
			background: #ffffff;
			box-shadow: 0px 0px 12px 0px rgba(63, 77, 89, 0.3);
			border-radius: 8px;
			position: absolute;
			top: -45px;
			left: 50%;
			transform: translate(-50%, 0px);
			display: flex;
			align-items: center;
			overflow: hidden;
			.notice-left {
				width: 120px;
				height: 100%;
				padding: 16px 0;
				background: #0175e8;
				display: flex;
				flex-direction: column;
				align-items: center;
				justify-content: center;
				position: relative;
				&::after {
					content: '';
					width: 20px;
					height: 20px;
					background: #0175e8;
					position: absolute;
					right: -4px;
					transform: rotate(40deg) skewX(-16deg);
				}
				.text {
					height: 26px;
					font-size: 20px;
					font-family: MicrosoftYaHei;
					color: #ffffff;
					line-height: 26px;
				}
				.btn {
					width: 64px;
					height: 22px;
					background: #ffffff;
					border-radius: 10px;
					border: 1px solid #ffffff;
					margin-top: 10px;
					color: #0175e8;
					font-size: 14px;
					font-family: PingFangSC, PingFang SC;
					font-weight: 400;
					text-align: center;
					line-height: 20px;
					cursor: pointer;
					&:hover {
						background: #0175e8;
						color: #ffffff;
					}
				}
			}
			.bg-img {
				width: 500px;
				height: 100%;
				object-fit: cover;
			}
			.notice-right {
				width: 580px;
				height: 100%;
				padding: 16px 25px;
			}
			.item {
				display: flex;
				align-items: center;
				cursor: pointer;
				margin-bottom: 10px;
				.time {
					width: 46px;
					height: 24px;
					font-size: 18px;
					font-family: ArialMT;
					color: #636975;
					line-height: 24px;
					flex: 0;
				}
				.title {
					max-width: calc(100% - 46px - 30px - 40px);
					height: 24px;
					font-size: 15px;
					margin-left: 20px;
					font-family: MicrosoftYaHei;
					color: #242527;
					line-height: 24px;
					&:hover {
						color: #51a8ff;
					}
				}
				.status {
					width: 40px;
					height: 20px;
					margin-left: 10px;
					background: #ffffff;
					border-radius: 10px;
					border: 1px solid #cedae5;
					text-align: center;
				}
			}
		}
		.search {
			padding-top: 80px;
			.search-box {
				margin: 0 auto;
			}
		}
	}
	.collect-box {
		position: absolute;
		right: 5px;
		top: 5px;
		padding: 0 8px;
		height: 22px;
		background: rgba(0, 0, 0, 0.2);
		border-radius: 10px;
		border: 1px solid rgba(255, 255, 255, 0.4);
		color: #ffffff;
		font-size: 12px;
		font-family: MicrosoftYaHei;
		display: flex;
		align-items: center;
		display: none;
		.icon {
			font-size: 12px;
			margin-right: 4px;
		}
	}
	.collected {
		color: #ffdc86;
	}
	.active-bg {
		width: 124px;
		height: 74px;
		position: absolute;
		right: 10px;
		bottom: 0;
		display: none;
	}
	.system {
		width: 100%;
		height: 467px;
		background: rgba(51, 62, 73, 0.08);
		background: url('~@/assets/images/serveHall/home-bg1.png') center;
		background-size: cover;
		.system-box {
			height: 100%;
			padding: 30px 0;
			.title {
				height: 26px;
				font-size: 22px;
				font-family: MicrosoftYaHei;
				color: #ffffff;
				line-height: 26px;
				text-align: center;
			}
			.sub-title {
				height: 22px;
				text-align: center;
				font-size: 14px;
				font-family: ArialMT;
				color: #b1b8cb;
				line-height: 22px;
				margin-top: 10px;
			}
		}
		.system-list {
			width: 100%;
			margin-top: 30px;
			display: flex;
			flex-wrap: wrap;
			.item {
				width: 183px;
				height: 150px;
				padding: 20px 0;
				background: rgba(0, 0, 0, 0.25);
				border-radius: 8px;
				margin-right: 20px;
				display: flex;
				flex-direction: column;
				align-items: center;
				margin-bottom: 20px;
				cursor: pointer;
				position: relative;

				.item-img {
					width: 58px;
					height: 58px;
					border-radius: 12px;
					object-fit: contain;
				}
				.name {
					width: 80%;
					height: 20px;
					font-size: 16px;
					font-family: MicrosoftYaHei;
					color: #ffffff;
					line-height: 20px;
					margin-top: 12px;
					text-align: center;
				}
				.desc {
					width: 80%;
					height: 18px;
					margin-top: 2px;
					font-size: 12px;
					font-family: ArialMT;
					color: #a7b1bb;
					line-height: 18px;
					text-align: center;
				}
				&:nth-child(6n) {
					margin-right: 0;
				}
				&:hover {
					background: linear-gradient(180deg, #3ec8ff 0%, #0075e8 100%);
					box-shadow: 0px 2px 10px 0px rgba(0, 38, 75, 0.5);
					.desc {
						color: #ffffff;
					}
					.collect-box {
						display: inline-flex;
					}
					.active-bg {
						display: block;
					}
				}
			}
		}
	}
	.service {
		width: 100%;
		height: 493px;
		background: rgba(90, 136, 182, 0.04);
		background: url('~@/assets/images/serveHall/home-bg2.png') center;
		background-size: cover;
		.service-box {
			height: 100%;
			padding: 30px 0;
			.title {
				height: 26px;
				font-size: 22px;
				font-family: MicrosoftYaHei;
				color: #000000;
				line-height: 26px;
				text-align: center;
			}
			.sub-title {
				height: 22px;
				text-align: center;
				font-size: 14px;
				font-family: ArialMT;
				color: #a1a5af;
				line-height: 22px;
				margin-top: 10px;
			}
		}
		.service-bar {
			margin-top: 10px;
			display: flex;
			justify-content: space-between;
			.btn {
				width: 62px;
				height: 24px;
				font-size: 14px;
				color: #0175e8;
				line-height: 24px;
				flex-shrink: 0;
				cursor: pointer;
				text-align: right;
				&:hover {
					font-weight: bold;
				}
			}
		}
		.service-list {
			display: flex;
			flex-wrap: wrap;
			margin-top: 20px;
			.service-item {
				width: 224px;
				height: 90px;
				padding: 16px 15px;
				background: rgba(255, 255, 255, 0.5);
				border-radius: 8px;
				margin-right: 20px;
				margin-bottom: 20px;
				display: flex;
				align-items: center;
				cursor: pointer;
				position: relative;
				&:nth-child(5n) {
					margin-right: 0;
				}

				.item-img {
					width: 58px;
					height: 58px;
					border-radius: 12px;
					margin-right: 10px;
				}
				.item-right {
					width: 100%;
					display: flex;
					flex-direction: column;
					align-items: flex-start;
					.name {
						width: 80%;
						font-size: 14px;
						font-family: MicrosoftYaHei;
						color: #242527;
						line-height: 20px;
					}
					.desc {
						width: 80%;
						font-size: 12px;
						font-family: ArialMT;
						color: #808890;
						line-height: 18px;
						margin-top: 4px;
					}
				}
				&:hover {
					background: linear-gradient(180deg, #4fcdff 0%, #0076ec 100%);
					box-shadow: 0px 2px 10px 0px rgba(0, 38, 75, 0.5);
					.collect-box {
						display: inline-flex;
					}
					.active-bg {
						display: block;
					}
					.name,
					.desc {
						color: #ffffff;
					}
				}
			}
		}
	}
	.task {
		width: 100%;
		height: 436px;
		background: rgba(16, 58, 100, 0.1);
		background: url('~@/assets/images/serveHall/home-bg3.png') center;
		background-size: cover;
		.task-box {
			height: 100%;
			padding: 40px 0;
			display: flex;
			justify-content: space-between;
			.title {
				height: 24px;
				font-size: 18px;
				font-family: MicrosoftYaHei;
				color: #ffffff;
				line-height: 24px;
				padding-left: 30px;
				position: relative;
				display: flex;
				justify-content: space-between;
				&::after {
					content: '';
					position: absolute;
					left: 0;
					top: 4px;
					width: 16px;
					height: 16px;
					background: #0175e8;
				}
			}
		}
		.task-card {
			width: 590px;
			height: 356px;
			background: rgba(0, 0, 0, 0.25);
			border-radius: 8px;
		}
		.task-title {
			width: 100%;
			height: 64px;
			padding: 20px 15px 20px;
			.more-btn {
				font-size: 14px;
				color: #ffffff;
				cursor: pointer;
				&:hover {
					color: #0175e8;
				}
			}
		}
		.task-list {
			display: flex;
			overflow: hidden;
			.left {
				width: 120px;
				height: 292px;
				padding: 20px 0;
				background: rgba(6, 27, 48, 0.2);
				border-radius: 0px 8px 0px 8px;
				&-item {
					display: flex;
					flex-direction: column;
					justify-content: center;
					align-items: center;
					width: 100%;
					height: 115px;
					margin-bottom: 22px;
					cursor: pointer;
					font-size: 14px;
					font-family: MicrosoftYaHei;
					color: #a3b0be;
					line-height: 22px;
					.item-img {
						width: 24px;
						height: 23px;
					}
					.item-img-active {
						width: 24px;
						height: 23px;
						display: none;
					}
					&:hover {
						border-right: 3px solid #0175e8;
						background: linear-gradient(
							90deg,
							rgba(1, 117, 232, 0.15) 0%,
							rgba(1, 117, 232, 0.4) 100%
						);
						font-weight: bold;
						color: #31c5ff;
						.item-img {
							display: none;
						}
						.item-img-active {
							display: block;
						}
					}
				}
				&-item-active {
					border-right: 3px solid #0175e8;
					background: linear-gradient(
						90deg,
						rgba(1, 117, 232, 0.15) 0%,
						rgba(1, 117, 232, 0.4) 100%
					);
					font-weight: bold;
					color: #31c5ff;
					.item-img {
						display: none;
					}
					.item-img-active {
						display: block;
					}
				}
			}
			.right {
				width: 440px;
				height: 292px;
				margin-left: 15px;
				.right-item {
					width: 100%;
					height: 92px;
					margin-bottom: 10px;
					border-bottom: 1px solid rgba(255, 255, 255, 0.2);
					display: flex;
					flex-direction: column;
					cursor: pointer;
					.item-time {
						height: 20px;
						font-size: 14px;
						font-family: ArialMT;
						color: #ffffff;
						line-height: 20px;
					}
					.item-title {
						height: 24px;
						margin-top: 8px;
						font-size: 16px;
						font-family: MicrosoftYaHei, MicrosoftYaHei;
						font-weight: bold;
						color: #ffffff;
						line-height: 24px;
					}
					.item-desc {
						height: 20px;
						margin-top: 8px;
						font-size: 14px;
						font-family: MicrosoftYaHei;
						color: #a0a4ab;
						line-height: 20px;
						display: flex;
						justify-content: space-between;
						// display: inline-block;
					}
					.desc-text {
					}
					.item-status {
						font-size: 14px;
						font-family: MicrosoftYaHei;
						color: #31c5ff;
						line-height: 20px;
						flex-shrink: 0;
					}
					&:hover {
						.item-title {
							color: #51a8ff;
						}
					}
				}
			}
		}
		.last-used {
			padding: 20px;
			.used-box {
				width: 100%;
				height: calc(100% - 20px);
				display: flex;
				flex-wrap: wrap;
				.used-item {
					margin-top: 20px;
					width: 94px;
					height: 128px;
					background: rgba(0, 0, 0, 0.25);
					border-radius: 8px;
					margin-right: 20px;
					cursor: pointer;
					display: flex;
					flex-direction: column;
					align-items: center;
					padding: 15px 0 10px;
					position: relative;
					overflow: hidden;
					&:nth-child(5n) {
						margin-right: 0;
					}
					&:hover {
						background: linear-gradient(180deg, #4fcdff 0%, #0076ec 100%);
						box-shadow: 0px 2px 10px 0px rgba(0, 38, 75, 0.5);
						.active-bg {
							display: block;
							left: 0;
						}
					}
					.item-img {
						width: 58px;
						height: 58px;
						border-radius: 12px;
					}
					.name {
						max-width: 90%;
						margin-top: 5px;
						font-size: 14px;
						color: #ffffff;
						line-height: 20px;
						text-align: center;
					}
				}
			}
		}
	}
}
</style>
<style scoped>
::v-deep .el-tabs__header {
	margin: 0;
}
::v-deep .el-tabs__item {
	height: 36px;
	line-height: normal;
}
::v-deep .el-tabs__nav-wrap::after {
	background-color: transparent;
}
::v-deep .el-tabs__item {
	font-size: 16px;
	color: #3e3e3e;
	line-height: 24px;
}
::v-deep .el-tabs__item:hover {
	color: #0175e8;
}
::v-deep .el-tabs__item.is-active {
	font-weight: bold;
	color: #0175e8;
}
::v-deep .el-tabs__active-bar {
	height: 4px;
	background: #0175e8;
	border-radius: 2px;
}
</style>
