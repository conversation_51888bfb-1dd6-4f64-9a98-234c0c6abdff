<template>
	<div class="content" style="height: 100%">
		<div style="width: 100%; height: 100%">
			<es-data-table
				v-if="true"
				ref="table"
				:key="tableCount"
				:row-style="tableRowClassName"
				:full="true"
				:fit="true"
				:thead="thead"
				:toolbar="toolbar"
				:page="page"
				:url="dataTableUrl"
				:param="dataTableParam"
				:border="true"
				:numbers="true"
				form
				@btnClick="btnClick"
				@selection-change="handleSelectionChange"
				@edit="changeTable"
			></es-data-table>
			<el-dialog
				v-if="showInfoPage"
				:title="formTitle"
				:visible.sync="showInfoPage"
				width="80%"
				height="90vh"
				append-to-body
			>
				<div style="height: 80vh">
					<add
						v-if="formTitle === '新增'"
						ref="add"
						:base-data="formData"
						:info-page-mode="infoPageMode"
						@activelyClose="closeInfoPage"
					></add>
					<infoPage
						v-else
						ref="infoPage"
						:base-data="formData"
						:info-page-mode="infoPageMode"
						@activelyClose="closeInfoPage"
					></infoPage>
				</div>
			</el-dialog>
		</div>
	</div>
</template>

<script>
import api from '@/http/specific/specificVisitorAppt/api';
import InfoPage from '@/views/specific/specificVisitorAppt/infoPage.vue';
import Add from '@/views/specific/specificVisitorAppt/add.vue';
import SnowflakeId from 'snowflake-id';

export default {
	components: { Add, InfoPage },
	data() {
		return {
			formData: {},
			page: {
				pageSize: 20,
				totalCount: 0
			},
			infoPageMode: 'allOn',
			selectRowData: [],
			selectRowIds: [],
			showInfoPage: false,
			tableCount: 1,
			dialogType: '',
			dataTableUrl: api.listJson,
			dataTableParam: { orderBy: 'create_time', asc: false },
			formTitle: '',
			validityOfDateDisable: false,
			thead: [],
			toolbar: [],
			editToolbar: [
				{
					type: 'button',
					contents: [
						// {
						// 	text: '新增',
						// 	type: 'primary',
						// 	code: 'add'
						// },
						// {
						// 	text: '查看',
						// 	code: 'toolbar',
						// 	type: 'primary'
						// },
						// {
						// 	text: '删除',
						// 	code: 'toolbar',
						// 	type: 'danger'
						// }
					]
				},
				{
					type: 'search',
					reset: true,
					contents: [
						{
							type: 'text',
							name: 'keyword',
							placeholder: '关键字查询',
							clearable: true
						},
						{
							type: 'select',
							name: 'auditStatus',
							placeholder: '审核状态',
							clearable: true,
							data: [
								{
									value: 0,
									label: '待审核'
								},
								{
									value: 1,
									label: '已通过'
								},
								{
									value: 2,
									label: '已驳回'
								},
								{
									value: 3,
									label: '已过期'
								}
							]
						}
					]
				}
			],
			listThead: [
				{
					title: '访客姓名',
					width: '150px',
					align: 'left',
					field: 'name',
					sortable: 'custom',
					showOverflowTooltip: true
				},
				{
					title: '手机号码',
					width: '200px',
					align: 'center',
					field: 'telephone',
					sortable: 'custom',
					showOverflowTooltip: true
				},
				{
					title: '预计来访时间',
					width: '250px',
					align: 'center',
					field: 'visitStart',
					sortable: 'custom',
					showOverflowTooltip: true
				},
				{
					title: '被访问人姓名',
					width: '150px',
					align: 'center',
					field: 'visitedName',
					sortable: 'custom',
					showOverflowTooltip: true
				},
				{
					title: '被访所属学院/部门',
					align: 'center',
					field: 'visitedCollege',
					sortable: 'custom',
					showOverflowTooltip: true
				},
				{
					title: '审核状态',
					width: '100px',
					align: 'center',
					field: 'auditStatus',
					sortable: 'custom',
					showOverflowTooltip: true,
					render: (h, param) => {
						let tem = '';
						switch (param.row.auditStatus) {
							case 0:
								tem = '';
								break;
							case 1:
								tem = 'success';
								break;
							case 2:
								tem = 'danger';
								break;
							case 3:
								tem = 'info';
								break;
						}
						return h('el-tag', { props: { type: tem } }, param.row.auditStatusName);
					}
				}
			],
			btnJson: {
				title: '操作',
				type: 'handle',
				width: 180,
				template: '',
				events: [
					{
						code: 'row',
						text: '查看'
					},
					{
						code: 'row',
						text: '审核',
						rules: rows => {
							const userId = sessionStorage.getItem('userId');
							return rows.auditStatus === 0 && rows.visitedSysUserId === userId;
						}
					},
					{
						code: 'row',
						text: '删除',
						rules: rows => {
							const userId = sessionStorage.getItem('userId');
							return rows.auditStatus === 0 && rows.visitedSysUserId === userId;
						}
					}
				]
			}
		};
	},
	created() {
		//初始化查询待审核列表
		this.thead = this.getListThead(this.btnJson);
		this.toolbar = this.editToolbar;
	},
	methods: {
		/**
		 * 表格行高
		 */
		tableRowClassName() {
			return {
				height: '54px !important'
			};
		},
		btnClick(res) {
			let text = res.handle.text;
			let code = res.handle.code;

			if (code === 'row') {
				switch (text) {
					case '查看':
					case '审核':
						this.openInfoPage(res.row.id, text);
						break;
					case '删除':
						this.deleteRows([res.row.id]);
						break;
				}
			} else {
				switch (text) {
					case '新增':
						this.openInfoPage(null, text);
						break;
					case '查看':
						if (this.selectRowIds.length > 1) {
							this.$message.warning('只能选择一个查看');
						} else if (this.selectRowIds.length < 1) {
							this.$message.warning('请选择一个进行查看');
						} else {
							this.openInfoPage(this.selectRowIds[0], text);
						}
						break;
					case '删除':
						if (this.selectRowIds.length < 1) {
							this.$message.warning('请选择一个删除');
						} else {
							this.deleteRows(this.selectRowIds);
						}
						break;
				}
			}
		},
		//打开infoPage
		openInfoPage(id, pageMode) {
			this.formTitle = pageMode;
			this.infoPageMode = pageMode;
			if (pageMode !== '新增') {
				this.$request({
					url: api.infoInfo,
					params: { id: id },
					method: 'GET'
				}).then(res => {
					//处理请求数据
					this.formData = { ...res.results };
					this.formData.drive = this.formData.drive ? '是' : '否';

					this.showInfoPage = true;
				});
			} else {
				const snowflake = new SnowflakeId();
				this.formData = { id: snowflake.generate() };
				this.showInfoPage = true;
			}
		},
		//关闭infoPage
		closeInfoPage(reload) {
			this.formData = {};
			this.showInfoPage = false;
			if (reload) this.$refs.table.reload();
		},
		// 数据列表多选回调
		handleSelectionChange(data) {
			let ids = [];
			data.forEach(row => {
				ids.push(row.id);
			});
			this.selectRowData = data;
			this.selectRowIds = ids;
		},
		deleteRows(ids) {
			this.$confirm('确定要删除选中的数据吗？', '删除', {
				confirmButtonText: '确定',
				cancelButtonText: '取消',
				type: 'warning'
			})
				.then(() => {
					this.$request({
						url: api.deleteBatchIds,
						data: { ids: ids.join(',') },
						method: 'POST'
					}).then(res => {
						if (res.rCode === 0) {
							this.$message.success('操作完成');
							this.$refs.table.reload();
						} else {
							this.$message.error(res.msg);
						}
					});
				})
				.catch(() => {});
		},
		getListThead(btnJson) {
			let tempThead = [...this.listThead];
			tempThead.push(btnJson);
			return tempThead;
		},
		changeTable(val) {
			switch (val.name) {
				case 'status':
					break;
			}
		}
	}
};
</script>
