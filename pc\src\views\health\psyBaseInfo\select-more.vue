<template>
	<div class="select-more" :style="{ width: width }">
		<el-input v-model="inputValue" placeholder="请选择内容" readonly class="input-with-select">
			<el-button slot="append" type="primary" :disabled="readonly" @click="showForm = true">
				选择
			</el-button>
		</el-input>
		<es-dialog
			v-if="showForm"
			:title="'选择关联用户'"
			:visible.sync="showForm"
			:drag="false"
			width="750px"
			height="650px"
			:close-on-click-modal="false"
			:destroy-on-close="true"
		>
			<div v-loading="loading" class="content">
				<es-data-table
					ref="table"
					:row-style="tableRowClassName"
					:full="true"
					:fit="true"
					:thead="thead"
					:toolbar="toolbar"
					:border="true"
					:page="pageOption"
					:url="dataTableUrl"
					:numbers="true"
					:param="params"
					close
					form
					highlight-current-row
					@success="success"
					@btnClick="btnClick"
					@sort-change="sortChange"
					@current-change="handleSelection"
				></es-data-table>
				<div class="content-r">
					<div class="top">选中值</div>
					<div class="tag-box">
						<el-tag
							v-if="selectRowData?.name"
							size="medium"
							type="success"
							class="el-tag"
							closable
							@close="closeIcon(selectRowData)"
						>
							<span class="text">
								{{ selectRowData.name }}
							</span>
						</el-tag>
					</div>
				</div>
			</div>
		</es-dialog>
	</div>
</template>

<script>
import interfaceUrl from '@/http/health/psyBaseInfo.js';
export default {
	name: 'SelectMore',
	props: {
		width: {
			type: String,
			default: '100%'
		},
		initData: {
			// 选择的值
			type: Object,
			default: () => {
				return {};
			}
		},
		readonly: {
			type: Boolean,
			default: false
		}
	},
	data() {
		return {
			loading: false,
			inputValue: '',

			collegeSelectData: [],
			dataTableUrl: interfaceUrl.getPersonListByTpye,
			showForm: false,
			formTitle: '编辑',
			toolbar: [
				{
					type: 'search',
					contents: [
						{
							type: 'text',
							name: 'keyword',
							placeholder: '关键字查询'
						}
					]
				},
				{
					type: 'button',
					contents: [
						{
							text: '确认选中',
							code: 'toolbar',
							type: 'primary'
						}
					]
				}
			],

			pageOption: {
				layout: 'total, prev, pager, next, jumper',
				pageSize: 10,
				// hideOnSinglePage: true,
				position: 'right',
				current: 1,
				pageNum: 1
			},
			params: {
				// asc: 'false',
				// orderBy: 'createTime',
				// collegeId: ''
			},
			selectRowData: {}
		};
	},
	computed: {
		thead() {
			return [
				{
					title: '姓名',
					align: 'left',
					field: 'name',
					showOverflowTooltip: true
				},
				{
					title: '联系方式',
					align: 'left',
					field: 'phone',
					showOverflowTooltip: true
				},
				{
					title: '工号',
					align: 'left',
					field: 'number',
					showOverflowTooltip: true
				}
			];
		}
	},
	watch: {
		initData: {
			handler(val) {
				console.log('watch initData', val);
				const valClone = JSON.parse(JSON.stringify(val));
				if (val.name) {
					this.toolSelect(valClone);
					this.inputValue = (valClone.name || '') + (valClone.number || '');
				}
			},
			deep: true,
			immediate: true
		}
	},

	methods: {
		/**
		 * 表格行高
		 */
		tableRowClassName({ row, rowIndex }) {
			let styleRes = {};
			if (row.isSelect === 'NO') {
				styleRes.background = '#f5f5f5 !important';
			}
			return styleRes;
		},

		// 数据列表选择回调
		handleSelection(row) {
			this.toolSelect(row);
		},
		// 请求成功回调函数
		success() {},
		closeIcon() {
			this.toolSelect({});
			this.$refs.table.setCurrentRow();
		},
		// 初始化数据
		toolSelect(row) {
			if (row.isSelect === 'NO') {
				this.$refs.table.setCurrentRow();
				this.$message.warning('该用户已被关联');
				return;
			}
			row && (this.selectRowData = row);
		},

		/**
		 * 操作按钮点击事件
		 * @param {*} res
		 */
		btnClick(res) {
			let code = res.handle.code;
			switch (code) {
				case 'toolbar':
					this.inputValue = (this.selectRowData.name || '') + (this.selectRowData.number || '');
					this.$emit('onSelect', this.selectRowData);
					this.showForm = false;
					break;
				default:
					break;
			}
		},
		/**
		 * 排序变化事件
		 * @param {*} column
		 * @param {*} prop
		 * @param {*} order
		 */
		sortChange(column, prop, order) {
			if (column.order == 'ascending') {
				//升序
				this.params = {
					asc: 'true',
					orderBy: column.prop
				};
			} else if (column.order == 'descending') {
				//降序
				this.params = {
					asc: 'false',
					orderBy: column.prop
				};
			} else {
				//不排序
				this.params = {
					asc: 'false',
					orderBy: 'createTime'
				};
			}
			this.$refs.table.reload();
		}
	}
};
</script>
<style scoped lang="scss">
@import '~@/assets/style/style.scss';
.select-more {
	width: 100%;
	.input-with-select {
		width: 100%;

		.prefix {
			height: 100%;
			line-height: 3;
		}
	}
}
.content {
	display: flex;
	width: 100%;
	height: 100%;

	::v-deep .es-data-table {
		width: 87%;
	}
	.content-r {
		width: 13%;
		margin-left: 6px;
		.top {
			margin-bottom: 20px;
		}
		.tag-box {
			height: 93%;
			overflow-y: scroll;

			.el-tag {
				margin-right: 5px;
				margin-bottom: 5px;
				.text {
					display: inline-block;
					max-width: 185px;
					overflow: hidden;
					text-overflow: ellipsis;
					white-space: nowrap;
					line-height: 1;
					// cursor: pointer;
				}
			}
		}
	}
}
</style>
