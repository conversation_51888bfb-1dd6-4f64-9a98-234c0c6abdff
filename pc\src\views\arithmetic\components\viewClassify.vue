<template>
	<es-form
		:key="formData._id"
		ref="form"
		v-loading="loading"
		:model="formData"
		:contents="formItemList"
		label-width="100px"
		:genre="2"
		collapse
		:submit="formTitle !== '查看'"
		@change="inputChange"
		@submit="handleFormSubmit"
	/>
</template>

<script>
import interfaceUrl from '@/api/arithmetic.js';
export default {
	name: 'ResumeView',

	props: {
		info: {
			type: Object,
			default: () => {
				return {};
			}
		},
		formTitle: {
			type: String,
			default: ''
		}
	},
	data() {
		return {
			loading: false,
			formData: { _id: '' },
			attachmentsList: []
		};
	},
	computed: {
		formItemList() {
			const readonly = this.formTitle === '查看';
			return [
				{
					label: '算法类型名称',
					name: 'title',
					readonly: readonly,
					rules: {
						required: true,
						message: '请输入标题',
						trigger: 'change'
					},
					col: 6
				},
				{
					label: '算法介绍',
					name: 'introduce',
					readonly: readonly,
					rules: {
						required: true,
						message: '请输入标题',
						trigger: 'change'
					},
					col: 6
				}
			];
		}
	},
	watch: {
		info: {
			handler(val) {
				this.formData = val;
			},
			deep: true,
			immediate: true
		}
	},
	created() {
		// this.queryInfo();
	},
	methods: {
		// 请求详情
		queryInfo() {
			this.$request2({
				url: interfaceUrl.classifyList,
				method: 'POST'
			}).then(res => {
				if (res.code === 200) {
					this.tableData = res.data;
				}
			});
		},
		inputChange(key, value) {},
		// 保存
		async handleFormSubmit(e) {
			const valid = await this.$refs.form.validate();
			if (!valid) return;
			this.loading = true;
			const formData = JSON.parse(JSON.stringify(this.formData));
			const params = {
				...formData
			};
			if (this.formTitle === '新增') {
				params.create_time = this.$.formatDate(new Date().getTime(), 'yyyy-MM-dd HH:mm:ss');
			} else {
				params.update_time = this.$.formatDate(new Date().getTime(), 'yyyy-MM-dd HH:mm:ss');
			}
			this.$request2({
				url: this.formTitle === '新增' ? interfaceUrl.classifyAdd : interfaceUrl.classifyEdit,
				data: params,
				method: 'POST',
				type: 'JSON'
			}).then(res => {
				this.loading = false;
				this.$emit('close');
			});
		},
		doHandleFormData(newData) {
			this.formData = newData;
		}
	}
};
</script>

<style scoped lang="scss">
.el-upload--handle {
	width: 100%;
}
</style>
