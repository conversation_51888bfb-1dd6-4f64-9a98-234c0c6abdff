<template>
    <div class="content">
        <div style="width:100%;height:100%">
            <el-menu :default-active="activeMenus" mode="horizontal" class="es-menu" @select="handleSelect">
                <el-menu-item v-for="(item, index) in menus" :key="index" :index="String(index)">
                    {{ item.label }}
                </el-menu-item>
            </el-menu>
            <es-data-table :row-style="tableRowClassName" v-if="activeMenus === '0'" ref="table" :key="tableCount" :full="true" :fit="true" :thead="thead" :toolbar="toolbar"
                :border="true" :page="pageOption" :url="dataTableUrl" :numbers="true" @btnClick="btnClick"
                @sort-change="sortChange" :param="params" @submit="hadeSubmit" close>
            </es-data-table>
            <es-data-table :row-style="tableRowClassName"  v-if="activeMenus === '1'" ref="table" :key="tableCount" :full="true" :fit="true" :thead="thead" :toolbar="toolbar"
                :border="true" :page="pageOption" :url="dataTableUrl" :numbers="true" @btnClick="btnClick"
                @sort-change="sortChange" :param="params" @submit="hadeSubmit" close>
            </es-data-table>
            <es-data-table :row-style="tableRowClassName"  v-if="activeMenus === '2'" ref="table" :key="tableCount" :full="true" :fit="true" :thead="thead" :toolbar="toolbar"
                :border="true" :page="pageOption" :url="dataTableUrl" :numbers="true" @btnClick="btnClick"
                @sort-change="sortChange" :param="params" @submit="hadeSubmit" close>
            </es-data-table>
            <es-dialog :title="formTitle" :visible.sync="showForm" width="80%" height="80%" :close-on-click-modal="false"
                :destroy-on-close="true">
                <es-form ref="form" :model="formData" :contents="formItemList" height="100%" :genre="2" collapse
                    @change="inputChange"
                    @submit="handleFormSubmit" 
                    @reset="showForm = false" />
            </es-dialog>
            <!-- 审核 -->
            <es-dialog  v-if="showAudit" :visible.sync="showAudit" :title="formTitle" height="80%" width="80%">
                <aduit v-if="showAudit" :select-info="selectInfo" style="height: calc(100% - 40px);" :type="dialogType" @refresh="refreshData" @cancel="visible = $event"
				:form-info="formData" :region-data="regionData"></aduit>
            </es-dialog>
            <!-- 查看企业详细信息 -->
            <es-dialog  v-if="showAllView" :visible.sync="showAllView" :title="formTitle" height="80%" width="80%">
                <allview v-if="showAllView" :select-info="selectInfo" style="height: calc(100% - 40px);"  @refresh="refreshData" @cancel="showAllView = $event"
				:form-info="formData" :region-data="regionData"></allview>
            </es-dialog>
            <es-dialog title="删除" :visible.sync="showDelete" width="20%" :close-on-click-modal="false" :middle="true"
                height="140px">
                <div>确定要删除该条数据吗</div>
                <div class="btn-box">
                    <div class="btn theme" @click="deleteRow">确定</div>
                    <div class="btn" @click="showDelete = false">取消</div>
                </div>
            </es-dialog>
        </div>
    </div>
</template>

<script>
import interfaceUrl from '@/http/platform/person.js';
import SnowflakeId from "snowflake-id";
import aduit from './aduit.vue';
import allview from './allview.vue';
export default {
  components: { aduit,allview },
    data() {
        return {
            //menus: [{ label: '待审核' },{label: '已退回'},{ label: '已认证' }],
			activeMenus: '2',
            queryStatus: '',
            dataTableUrl: interfaceUrl.listJson,
            tableCount: 1,
            showForm: false,
            showDelete: false,
            showAudit: false,
            showAllView: false,
            dialogType: "",
            selectInfo: null,//选中的数据值
            formData: {},
            formTitle: '编辑',
            regionData:[],
            typeDicData:[],
            validityOfDateDisable: false,
            editBtn: {
                type: 'submit',
                skin: 'lay-form-btns',
                contents: [
                    {
                        type: 'primary',
                        text: '确定',
                        event: 'confirm'
                    },
                    {
                        type: 'reset',
                        text: '取消',
                        event: 'cancel'
                    },
                ]
            },
            
            cancelBtn: {
                type: 'submit',
                skin: 'lay-form-btns',
                contents: [
                    {
                        type: 'reset',
                        text: '取消',
                        event: 'cancel'
                    },
                ]
            },
            editToolbar:[
                // {
                //     type: 'button',
                //     contents: [
                //         {
                //             text: '新增',
                //             code: 'add',
                //             type: 'primary'
                //         }
                //     ]
                // },
                {
                    type: 'search',
                    contents: [
                        {
                            type: 'text',
                            name: 'keyword',
                            placeholder: '关键字查询'
                        },
                    ]
                },
                {
                    type: 'filter',
                    contents: [
                        {
                            type: 'text',
                            label: '姓名',
                            placeholder: '请输入姓名',
                            name: 'xm',
                            clearable: true,
                            col: 4
                        },
                        {
                            type: 'select',
                            label: '认证来源',
                            placeholder: '请选择认证来源',
                            name: 'certifyFrom',
                            event: 'multipled',
                            sysCode: 'plat_person_cert_apply_certify_from',
                            'label-key': 'shortName',
                            'value-key': 'cciValue',
                            clearable: true,
                            col: 4
                        },
                        {
                            type: 'text',
                            label: '学号/工号',
                            placeholder: '请输入学号/工号',
                            name: 'xhZgh',
                            clearable: true,
                            col: 4
                        },
                        {
                            type: 'select',
                            label: '人员类型',
                            placeholder: '请选择人员类型',
                            name: 'zglx',
                            event: 'multipled',
                            sysCode: 'plat_person_type',
                            'label-key': 'shortName',
                            'value-key': 'cciValue',
                            clearable: true,
                            col: 4
                        },
                        {
                            type: 'date',
                            label: '申请时间',
                            placeholder: '请输入申请时间',
                            name: 'createTime',
                            clearable: true,
                            col: 4
                        }
                    ]
                },
            ],
            viewToolbar:[
                {
                    type: 'search',
                    contents: [
                        {
                            type: 'text',
                            name: 'keyword',
                            placeholder: '关键字查询'
                        },
                    ]
                },
                {
                    type: 'filter',
                    contents: [
                        {
                            type: 'text',
                            label: '姓名',
                            placeholder: '请输入姓名',
                            name: 'xm',
                            clearable: true,
                            col: 4
                        },
                        {
                            type: 'select',
                            label: '认证来源',
                            placeholder: '请选择认证来源',
                            name: 'certifyFrom',
                            event: 'multipled',
                            sysCode: 'plat_person_cert_apply_certify_from',
                            'label-key': 'shortName',
                            'value-key': 'cciValue',
                            clearable: true,
                            col: 4
                        },
                        {
                            type: 'text',
                            label: '学号/工号',
                            placeholder: '请输入学号/工号',
                            name: 'xhZgh',
                            clearable: true,
                            col: 4
                        },
                        {
                            type: 'select',
                            label: '人员类型',
                            placeholder: '请选择人员类型',
                            name: 'zglx',
                            event: 'multipled',
                            sysCode: 'plat_person_type',
                            'label-key': 'shortName',
                            'value-key': 'cciValue',
                            clearable: true,
                            col: 4
                        },
                        {
                            type: 'date',
                            label: '申请时间',
                            placeholder: '请输入申请时间',
                            name: 'createTime',
                            clearable: true,
                            col: 4
                        }
                    ]
                },
            ],
            toolbar: [],
            listThead: [
                {
                    title: '姓名',
                    align: 'left',
                    field: 'xm',
                    sortable: 'custom',
					showOverflowTooltip: true,
                },
                {
                    title: '人员类型',
                    width: "160px",
                    align: 'left',
                    field: 'zglx',
                    sortable: 'custom',
					showOverflowTooltip: true,
                },
                {
                    title: '学号/工号',
                    width: "160px",
                    align: 'center',
                    field: 'xhZgh',
                    sortable: 'custom',
					showOverflowTooltip: true,
                },
                {
                    title: '性别',
                    width: "160px",
                    align: 'center',
                    field: 'xbdm',
                    sortable: 'custom',
					showOverflowTooltip: true,
                },
                {
                    title: '民族',
                    width: "160px",
                    align: 'center',
                    field: 'mzdm',
                    sortable: 'custom',
					showOverflowTooltip: true,
                },
                {
                    title: '手机号',
                    width: "160px",
                    align: 'center',
                    field: 'sjh',
                    sortable: 'custom',
					showOverflowTooltip: true,
                },
                {
                    title: '认证来源',
                    width: "160px",
                    align: 'center',
                    field: 'certifyFrom',
                    sortable: 'custom',
					showOverflowTooltip: true,
                },
                {
                    title: '申请时间',
                    align: 'center',
                    field: 'updateTime',
                    sortable: 'custom',
					showOverflowTooltip: true,
                }
            ],
            auditListBtn: {
                title: '操作',
                type: 'handle',
                width: 180,
                template: '',
                events: [
                    {
                        code: 'audit',
                        text: '审核'
                    },
                    {
                        code: 'edit',
                        text: '编辑'
                    },
                    {
                        code: 'view',
                        text: '查看'
                    },
                    {
                        code: 'delete',
                        text: '删除'
                    },
                ]
            },
            viewListBtn: {
                title: '操作',
                type: 'handle',
                width: 180,
                template: '',
                events: [
                    {
                        code: 'view',
                        text: '查看'
                    },
                    {
                        code: 'edit',
                        text: '编辑'
                    },
                ]
            },
            allViewListBtn: {
                title: '操作',
                type: 'handle',
                width: 180,
                template: '',
                events: [
                    {
                        code: 'allview',
                        text: '查看'
                    },
                    // {
                    //     code: 'edit',
                    //     text: '编辑'
                    // },
                ]
            },
            thead: [],
            pageOption: {
                layout: 'total, prev, pager, next, jumper',
                pageSize: 10,
                position: 'right',
                // hideOnSinglePage: true,
                current: 1,
                pageNum: 1
            },
            params: {
                asc: "false",
                orderBy: "updateTime"
            },
            formItemList:[]
        };
    },
    computed: {
        tearcherFormItemList(){
            return  {
                title: '教职工信息',
                contents: [
                    {
                        label: '职工号',
                        name: 'platPersonTeacher_zgh',
                        type: 'input',
                        placeholder: '请选择职工号',
                        rules: {
                            required: true,
                            message: '请选择职工号',
                            trigger: 'blur'
                        },
                        verify: 'required',
                        col: 5,
                    },
                    {
                        label: '所在单位',
                        name: 'platPersonTeacher_szdw',
                        placeholder: '请输入所在单位',
                        rules: {
                            required: true,
                            message: '请输入所在单位',
                            trigger: 'blur'
                        },
                        verify: 'required',
                        col: 5
                    },
                    {
                        label: '当前状态',
                        name: 'platPersonTeacher_dqzt',
                        type: 'select',
                        placeholder: '请输入当前状态',
                        rules: {
                            required: true,
                            message: '请输入当前状态',
                            trigger: 'blur'
                        },
                        verify: 'required',
                        sysCode: 'plat_person_teacher_JZGDQZTDM',
                        'label-key': 'shortName',
                        'value-key': 'cciValue',
                        col: 5
                    },
                    {
                        label: '籍贯代码',
                        name: 'platPersonTeacher_jgdm',
                        placeholder: '请输入籍贯代码',
                        col: 5
                    },
                    {
                        label: '学历代码',
                        name: 'platPersonTeacher_xwdm',
                        type: 'select',
                        placeholder: '请输入学历代码',
                        col: 5,
                        verify: 'required',
                        col: 5,
                        sysCode: 'plat_person_xldm',
                        'label-key': 'shortName',
                        'value-key': 'cciValue',
                    },
                    {
                        label: '最高学位',
                        name: 'platPersonTeacher_zgxw',
                        type: 'select',
                        placeholder: '请选择最高学位',
                        rules: {
                            required: true,
                            message: '请选择最高学位',
                            trigger: 'blur'
                        },
                        verify: 'required',
                        col: 5,
                        sysCode: 'plat_person_xwdm',
                        'label-key': 'shortName',
                        'value-key': 'cciValue',
                    },
                    {
                        label: '来校日期',
                        name: 'platPersonTeacher_lxrq',
                        type: 'date',
                        placeholder: '请选择来校日期',
                        rules: {
                            required: true,
                            message: '请选择最高学位',
                            trigger: 'blur'
                        },
                        verify: 'required',
                        col: 5,
                        
                    },
                    {
                        label: '教师类别',
                        name: 'platPersonTeacher_jslb',
                        type: 'select',
                        placeholder: '请选择教师类别',
                        rules: {
                            required: true,
                            message: '请选择教师类别',
                            trigger: 'blur'
                        },
                        verify: 'required',
                        col: 5,
                        sysCode: 'plat_person_teacher_JSLBDM',
                        'label-key': 'shortName',
                        'value-key': 'cciValue',
                    },
                    {
                        label: '最高学历',
                        name: 'platPersonTeacher_zgxl',
                        type: 'select',
                        placeholder: '请输入最高学历',
                        rules: {
                            required: true,
                            message: '请选择最高学历',
                            trigger: 'blur'
                        },
                        verify: 'required',
                        col: 5,
                        sysCode: 'plat_person_xldm',
                        'label-key': 'shortName',
                        'value-key': 'cciValue',
                    },
                    {
                        label: '参加工作日期',
                        name: 'platPersonTeacher_cjgzrq',
                        type: 'date',
                        placeholder: '请选择参加工作日期',
                        rules: {
                            required: true,
                            message: '请选择参加工作日期',
                            trigger: 'blur'
                        },
                        verify: 'required',
                        col: 5,
                    },
                    {
                        label: '专业技术职务',
                        name: 'platPersonTeacher_zyjszw',
                        type: 'input',
                        placeholder: '请输入专业技术职务',
                        col: 5,
                    },
                ]
            };
        },
        studentFormItemList(){
            return  {
                title: '学生信息',
                contents: [
                    {
                        label: '学号',
                        name: 'platPersonStudent_xh',
                        type: 'input',
                        placeholder: '请选择学号',
                        rules: {
                            required: true,
                            message: '请选择学号',
                            trigger: 'blur'
                        },
                        verify: 'required',
                        col: 5,
             
                    },
                    {
                        label: '血型代码',
                        name: 'platPersonStudent_xxdm',
                        placeholder: '请输入血型代码',
                        rules: {
                            required: true,
                            message: '请输入血型代码',
                            trigger: 'blur'
                        },
                        verify: 'required',
                        col: 5
                    },
                    {
                        label: '主页地址',
                        name: 'platPersonStudent_zydz',
                        placeholder: '请输入主页地址',
                        rules: {
                            required: false,
                            message: '请输入主页地址',
                            trigger: 'blur'
                        },
                        verify: 'required',
                        col: 5
                    },
                    {
                        label: '特长',
                        name: 'platPersonStudent_tc',
                        placeholder: '请输入特长',
                        col: 5
                    },
                    {
                        label: '是否在校',
                        name: 'platPersonStudent_sfzx',
                        placeholder: '请输入是否在校',
                        col: 5
                    },
                    {
                        label: '专业代码',
                        name: 'platPersonStudent_zydm',
                        type: 'text',
                        placeholder: '请选择专业代码',
                        rules: {
                            required: true,
                            message: '请选择专业代码',
                            trigger: 'blur'
                        },
                        verify: 'required',
                        col: 5
                    },
                    {
                        label: '学制',
                        name: 'platPersonStudent_xz',
                        type: 'select',
                        placeholder: '请输入学制',
                        col: 5,
                        sysCode: 'plat_person_xzdm',
                        'label-key': 'shortName',
                        'value-key': 'cciValue',
                        
                    },
                    {
                        label: '学籍状态代码',
                        name: 'platPersonStudent_xjztdm',
                        type: 'select',
                        placeholder: '请选择学籍状态代码',
                        rules: {
                            required: true,
                            message: '请选择学籍状态代码',
                            trigger: 'blur'
                        },
                        verify: 'required',
                        col: 5,
                        sysCode: 'plat_person_student_XJZTDM',
                        'label-key': 'shortName',
                        'value-key': 'cciValue',
                    },
                    {
                        label: '院系代码',
                        name: 'platPersonStudent_yxdm',
                        placeholder: '请输入院系代码',
                        rules: {
                            required: true,
                            message: '请选择院系代码',
                            trigger: 'blur'
                        },
                        verify: 'required',
                        col: 5
                    },
                    {
                        label: '入学日期',
                        name: 'platPersonStudent_rxrq',
                        type: 'date',
                        placeholder: '请选择入学日期',
                        rules: {
                            required: true,
                            message: '请选择入学日期',
                            trigger: 'blur'
                        },
                        verify: 'required',
                        col: 5,
                    },
                    {
                        label: 'qq号码',
                        name: 'platPersonStudent_qqhm',
                        type: 'input',
                        placeholder: '请输入qq号码',
                        col: 5,
                    },
                    {
                        label: '家庭电话',
                        name: 'platPersonStudent_jtdh',
                        type: 'input',
                        placeholder: '请输入家庭电话',
                        rules: {
                            required: true,
                            message: '请输入家庭电话',
                            trigger: 'blur'
                        },
                        verify: 'required',
                        col: 5,
                    },
                    {
                        label: '年级代码',
                        name: 'platPersonStudent_njdm',
                        type: 'input',
                        placeholder: '请选择年级代码',
                        rules: {
                            required: true,
                            message: '请选择年级代码',
                            trigger: 'blur'
                        },
                        verify: 'required',
                        col: 5,
                    },
                    {
                        label: '班号',
                        name: 'platPersonStudent_bh',
                        type: 'input',
                        placeholder: '请选择班号',
                        rules: {
                            required: true,
                            message: '请选择班号',
                            trigger: 'blur'
                        },
                        verify: 'required',
                        col: 5,
                    },
                    {
                        label: '学历层次代码',
                        name: 'platPersonStudent_xlccdm',
                        type: 'select',
                        placeholder: '请选择学历层次代码',
                        rules: {
                            required: true,
                            message: '请选择学历层次代码',
                            trigger: 'blur'
                        },
                        verify: 'required',
                        col: 5,
                        sysCode: 'plat_person_xldm',
                        'label-key': 'shortName',
                        'value-key': 'cciValue',
                    },
                    {
                        label: '培养层次代码',
                        name: 'platPersonStudent_pycdm',
                        type: 'select',
                        placeholder: '请选择培养层次代码',
                        rules: {
                            required: true,
                            message: '请选择培养层次代码',
                            trigger: 'blur'
                        },	
                        verify: 'required',
                        col: 5,
                        sysCode: 'plat_person_pyccdm',
                        'label-key': 'shortName',
                        'value-key': 'cciValue',
                    },
                    {
                        label: '报道注册标记',
                        name: 'platPersonStudent_bdzcbj',
                        type: 'select',
                        placeholder: '请选择报道注册标记',
                        rules: {
                            required: true,
                            message: '请选择报道注册标记',
                            trigger: 'blur'
                        },
                        verify: 'required',
                        col: 5,
                        sysCode: 'yes_or_no',
                        'label-key': 'shortName',
                        'value-key': 'cciValue',
                    },
                ]
            };
        },
        formBaseItemList() {
            return [
            {
                title: '人员信息',
                contents: [
                    {
                        label: '人员类型',
                        name: 'zglx',
                        type: 'checkbox',
                        placeholder: '请选择人员类型',
                        rules: {
                            required: true,
                            message: '请选择人员类型',
                            trigger: 'blur'
                        },
                        verify: 'required',
                        col: 5,
                        sysCode: 'plat_person_type',
                        'label-key': 'shortName',
                        'value-key': 'cciValue',
                    },
                    {
                        label: '姓名',
                        name: 'xm',
                        placeholder: '请输入姓名',
                        rules: {
                            required: true,
                            message: '请输入姓名',
                            trigger: 'blur'
                        },
                        verify: 'required',
                        col: 5
                    },
                    // {
                    //     label: '学号/职工号',
                    //     name: 'xhZgh',
                    //     placeholder: '请输入学号/职工号',
                    //     rules: {
                    //         required: true,
                    //         message: '请输入学号/职工号',
                    //         trigger: 'blur'
                    //     },
                    //     verify: 'required',
                    //     col: 5
                    // },
                    {
                        label: '姓名拼音',
                        name: 'xmpy',
                        placeholder: '请输入姓名拼音',
                        rules: {
                            required: true,
                            message: '请输入姓名拼音',
                            trigger: 'blur'
                        },
                        verify: 'required',
                        col: 5
                    },
                    {
                        label: '曾用名',
                        name: 'cym',
                        placeholder: '请输入曾用名',
                        col: 5
                    },
                    {
                        label: '英文名',
                        name: 'ywm',
                        placeholder: '请输入英文名',
                        col: 5
                    },
                    {
                        label: '性别',
                        name: 'xbdm',
                        type: 'select',
                        placeholder: '请选择性别',
                        rules: {
                            required: true,
                            message: '请选择性别',
                            trigger: 'blur'
                        },
                        verify: 'required',
                        col: 5,
                        sysCode: 'plat_person_xb',
                        'label-key': 'shortName',
                        'value-key': 'cciValue',
                    },
                    {
                        label: '出生日期',
                        name: 'csrq',
                        type: 'date',
                        placeholder: '请选择出生日期',
                        col: 5,
                        
                    },
                    {
                        label: '证件类型码',
                        name: 'zjlxm',
                        type: 'select',
                        placeholder: '请选择证件类型码',
                        rules: {
                            required: true,
                            message: '请选择证件类型码',
                            trigger: 'blur'
                        },
                        verify: 'required',
                        col: 5,
                        sysCode: 'plat_person_sfzlx',
                        'label-key': 'shortName',
                        'value-key': 'cciValue',
                    },
                    {
                        label: '证件号',
                        name: 'zjh',
                        placeholder: '请输入证件号',
                        rules: {
                            required: true,
                            message: '请选择证件类型码',
                            trigger: 'blur'
                        },
                        verify: 'required',
                        col: 5
                    },
                    {
                        label: '民族',
                        name: 'mzdm',
                        type: 'select',
                        placeholder: '请选择民族',
                        rules: {
                            required: true,
                            message: '请选择民族',
                            trigger: 'blur'
                        },
                        verify: 'required',
                        col: 5,
                        sysCode: 'plat_person_mz',
                        'label-key': 'shortName',
                        'value-key': 'cciValue',
                    },
                    {
                        label: '国家(地区)代码',
                        name: 'gjdqdm',
                        type: 'select',
                        placeholder: '请选择国家(地区)代码',
                        rules: {
                            required: true,
                            message: '请选择国家(地区)代码',
                            trigger: 'blur'
                        },
                        verify: 'required',
                        col: 5,
                        sysCode: 'plat_person_gjdq',
                        'label-key': 'shortName',
                        'value-key': 'cciValue',
                    },
                    {
                        label: '港澳台侨代码',
                        name: 'gatqdm',
                        type: 'select',
                        placeholder: '请选择港澳台侨代码',
                        col: 5,
                        sysCode: 'plat_person_qqlb',
                        'label-key': 'shortName',
                        'value-key': 'cciValue',
                    },
                    {
                        label: '政治面貌代码',
                        name: 'zzmmdm',
                        type: 'select',
                        placeholder: '请选择政治面貌代码',
                        rules: {
                            required: true,
                            message: '请选择政治面貌代码',
                            trigger: 'blur'
                        },
                        verify: 'required',
                        col: 5,
                        sysCode: 'plat_person_zzmm',
                        'label-key': 'shortName',
                        'value-key': 'cciValue',
                    },
                    {
                        label: '婚姻状况代码',
                        name: 'hyzkdm',
                        type: 'select',
                        placeholder: '请选择婚姻状况代码',
                        rules: {
                            required: true,
                            message: '请选择婚姻状况代码',
                            trigger: 'blur'
                        },
                        verify: 'required',
                        col: 5,
                        sysCode: 'plat_person_hyzk',
                        'label-key': 'shortName',
                        'value-key': 'cciValue',
                    },
                    {
                        label: '健康状况代码',
                        name: 'jkzkdm',
                        type: 'select',
                        placeholder: '请选择健康状况代码',
                        rules: {
                            required: true,
                            message: '请选择健康状况代码',
                            trigger: 'blur'
                        },
                        verify: 'required',
                        col: 5,
                        sysCode: 'plat_person_jkzk',
                        'label-key': 'shortName',
                        'value-key': 'cciValue',
                    },
                    
                ]
            },
            {
                title: '联系信息',
                contents: [
                    {
                        label: '手机号',
                        name: 'sjh',
                        placeholder: '请输入手机号',
                        rules: {
                            required: true,
                            message: '请输入手机号',
                            trigger: 'blur'
                        },
                        verify: 'required',
                        col: 5
                    },
                    {
                        label: '电子邮箱',
                        name: 'dzyx',
                        placeholder: '请输入电子邮箱',
                        rules: {
                            required: false,
                            message: '请输入电子邮箱',
                            trigger: 'blur'
                        },
                        verify: 'email',
                        col: 5
                    },
                    {
                        label: '家庭地址',
                        name: 'jtdz',
                        placeholder: '请输入家庭地址',
                        rules: {
                            required: true,
                            message: '请输入家庭地址',
                            trigger: 'blur'
                        },
                        verify: 'required',
                        col: 5
                    },
                ]
            },
            
        ]
        },
        // formItemList() {
        //     return this.formBaseItemList;
        // },
    },
    watch: {
        dataTableUrl() {
            this.tableCount++;
        },
        formItemList:{
            handler(newValue, oldValue){
            },
            deep:true,
        }
    },
    created() {
        this.formItemList= this.formBaseItemList;
        //初始化查询待审核列表
        this.queryStatus = "audited";
        this.params["status"] = "audited";
        this.toolbar = this.editToolbar;
        let tempThead = this.getListThead(this.auditListBtn);
        this.thead = tempThead;
    },
    mounted() {

    },
    methods: {
        /**
        * 表格行高
        */
        tableRowClassName({ row, rowIndex }) {
            let styleRes = {
                "height": "54px !important"
            }
            return styleRes

        },
        inputChange(key, value) {
            if (key == 'zglx') {
                this.initFormItemList(value);
                console.log(this.formItemList,"inputChange");
                
            }
        },
        hadeSubmit(data) {

        },
        /**
         * 操作按钮点击事件
         * @param {*} res 
         */
        btnClick(res) {
            let code = res.handle.code;
            switch (code) {
                case 'add':
                    // 新增
                    this.formTitle = '新增';
                    this.editModule(this.formItemList);
                    const snowflake = new SnowflakeId();
                    this.formData = { id: snowflake.generate(), status: true };
                    this.showForm = true;
                    this.showAudit = false;
                    this.initFormItemList("");
                    break;
                case 'audit':
                    this.formTitle = "审核";
                    this.selectInfo = res.row.id;
                    this.$request({
                        url: interfaceUrl.info + '/' + res.row.id,
                        method: 'GET'
                    }).then(res => {
                        if (res.rCode == 0) {
                            this.formData = res.results;
                            this.dialogType = "audit";
                            this.showForm = false;
                            this.showAudit = true;
                            
                        }
                    });
                    
                    break;
                case 'edit':
                    // 编辑
                    this.formTitle = '编辑';
                    this.editModule(this.formItemList);
                    this.$request({
                        url: interfaceUrl.info + '/' + res.row.id,
                        method: 'GET'
                    }).then(res => {
                        if (res.rCode == 0) {
                            this.formData = res.results;
                            this.handleFormData(res);
                            this.initFormItemList(this.formData.zglx);
                            this.showForm = true;
                            this.showAudit = false;
                            this.showAllView = false;
                        }
                    });
                    break;
                case 'view':
                    this.formTitle = '查看';
                    this.$request({
                        url: interfaceUrl.info + '/' + res.row.id,
                        method: 'GET'
                    }).then(res => {
                        if (res.rCode == 0) {
                            this.formData = res.results;
                            this.dialogType = "view";
                            this.selectInfo = res.results.id;
                            this.showForm = false;
                            this.showAudit = true;
                            this.showAllView = false;
                        }
                    });
                    break;
                case 'allview':
                    this.formTitle = '查看';
                    this.$request({
                        url: interfaceUrl.info + '/' + res.row.id,
                        method: 'GET'
                    }).then(res => {
                        if (res.rCode == 0) {
                            this.formData = res.results;
                            this.dialogType = "view";
                            this.selectInfo = res.results.id;
                            this.showForm = false;
                            this.showAudit = false;
                            this.showAllView = true;
                        }
                    });
                    break;
                case 'delete':
                    this.deleteId = res.row.id;
                    this.showDelete = true;
                    break;
                default:
                    break;
            }
        },
        handleClose() {},
        handleFormSubmit(data) {
            let formData = data;
            delete formData.extMap;

            let zglx = data.zglx;
            if(undefined != zglx && zglx.length>0){
                formData.zglx = zglx.join(",");
            }

            for (let key in formData) {
                if(key.indexOf("_")>=0){
                    let indexName = key.replace("_",".");
                    formData[indexName] = formData[key];
                }
            };

            let url = "";
            if (this.formTitle == '新增') {
                url = interfaceUrl.save;
            } else {
                url = interfaceUrl.update;
            }
           
            formData.status = formData.status ? '1' : '0';
            this.$request({
                url: url,
                data: formData,
                method: 'POST'
            }).then(res => {
                if (res.rCode == 0) {
                    this.$message.success('操作成功');
                    this.showForm = false;
                    this.formData = {};
                    this.$refs.table.reload();
                } else {
                    this.$message.error(res.msg);
                    formData.zglx = zglx;
                }
            });
        },
        deleteRow() {
            this.$request({
                url: interfaceUrl.deleteBatchIds,
                data: { ids: this.deleteId },
                method: 'POST'
            }).then(res => {
                if (res.rCode == 0) {
                    this.$refs.table.reload();
                    this.$message.success('删除成功');
                } else {
                    this.$message.error(res.msg);
                }
                this.showDelete = false;
            });
        },
        /**
         * 排序变化事件
         * @param {*} column 
         * @param {*} prop 
         * @param {*} order 
         */
        sortChange(column, prop, order) {
            
            if (column.order == 'ascending') {//升序
                this.params = {
                    asc: "true",
                    orderBy: column.prop
                }

            } else if (column.order == 'descending') {//降序 
                this.params = {
                    asc: "false",
                    orderBy: column.prop
                }
            } else { //不排序
                this.params = {
                    asc: "false",
                    orderBy: "createTime"
                }
            }
            this.params["status"] = this.queryStatus;
            this.$refs.table.reload()

        },
        /**
         * 编辑模式
         */
        editModule(list) {
            for (var i in list) {
                var item = list[i];
                item.readonly = false;
            }
            list.push(this.editBtn);
        },
        /**
         * 页签切换
         * @param {*} res 
         */
         handleSelect(res) {
            sessionStorage.setItem('menusIndex',res);
            this.activeMenus = res;
            if ("0" == res) {
                this.queryStatus = "noaudit";
                this.params["status"] = this.queryStatus;
                let tempThead = this.getListThead(this.auditListBtn);
                this.toolbar = this.editToolbar;
                this.thead = tempThead;
            } else if ("1" == res) {
                this.queryStatus = "return";
                this.params["status"] = this.queryStatus;
                let tempThead = this.getListThead(this.viewListBtn);
                this.toolbar = this.viewToolbar;
                this.thead = tempThead;
            } else if("2" == res){
                this.queryStatus = "audited";
                this.params["status"] = this.queryStatus;
                let tempThead = this.getListThead(this.allViewListBtn);
                this.toolbar = this.viewToolbar;
                this.thead = tempThead;
            }
            //alert(this.queryStatus);
            this.tableCount++;
        },
        /**
         * 处理formData数据为可展示数据
         */
        handleFormData(res){
            let that = this;
            let personType = this.formData.zglx;
            if(typeof(personType)!='undefined'){
                let personTypeArray = personType.split(",");
                this.formData.zglx = personTypeArray;
                personTypeArray.forEach(function(type){
                    if(type=="student"){
                        that.formItemList.splice(2,0,that.studentFormItemList);
                    }else if(type=="teacher"){
                        let index = that.formItemList.length==2?2:3;
                        that.formItemList.splice(index,0,that.tearcherFormItemList);
                    }
                })
            }
            
            this.buildSubIndexName("platPersonStudent");
            this.buildSubIndexName("platPersonTeacher");
        },
        /**
         * 只读模式
         */
         readModule(list) {
            for (var i in list) {
                var indexList = list[i].contents;
                for(var k in indexList){
                    var item = indexList[k];
                    if (item.name === 'templateDownload' || item.name === 'dataExport') {
                        continue;
                    }
                
                    item.readonly = true;
                    if (item.type && item.type == 'submit') {
                        item.contents = [
                            {
                                type: 'reset',
                                text: '取消',
                                event: 'cancel'
                            },
                        ];
                    }
                }
            }
            this.formItemList = list;
        },
        getListThead(btnJson){
            let tempThead = this.listThead;
            if(tempThead.length>7){
                tempThead.pop();
            }
            tempThead.push(btnJson);
            return tempThead;
        },
        
        refreshData() {
            this.showAudit = false;
            this.showAllView = false;
            this.$refs.table.reload();
		},
        initFormItemList(value){
            let length = this.formItemList.length;
            if(length>2){
                for(let i=length;i-2>0;i--){
                    this.formItemList.pop();
                }
            }

            if(value=="student,teacher"){
                this.$set(this.formItemList,2,this.studentFormItemList);
                this.$set(this.formItemList,3,this.tearcherFormItemList);
            }else if(value=="student"){
                this.$set(this.formItemList,2,this.studentFormItemList);
            }else if(value=="teacher"){
                this.$set(this.formItemList,2,this.tearcherFormItemList);
            }
        },
        buildSubIndexName(subVOname){
            let vo = this.formData[subVOname];
            if(vo!=undefined&&vo!=null){
                for (let key in vo) {
                    this.$set(this.formData,subVOname+"_"+key,vo[key]||"")
                    // this.formData[subVOname+"_"+key] = vo[key]||"";
                };
            }
            delete this.formData[subVOname];
        }
    }
};
</script>
<style scoped lang="scss">
@import '../../../assets/style/style.scss';

.content {
    width: 100%;
    height: 100%;

}

.el-dialog__body {
    .btn-box {
        margin-top: 20px;
        display: flex;
        justify-content: flex-end;

        .btn {
            padding: 5px 10px;
            color: #666;
            border: 1px solid #eee;
            cursor: pointer;
            margin-right: 5px;
            border-radius: 4px;
            // &.theme {
            // 	background: $--color-primary;
            // 	color: #fff;
            // 	border-color: $--color-primary;
            // }
        }
    }


}
</style>
