import util from 'eoss-ui/src/utils/util';
import { Message } from 'eoss-element';
export default {
	namespaced: true,
	state: {
		userInfo: {} // 用户基本信息
	},
	mutations: {},
	actions: {
		async getLoginUserInfo({ state, commit }) {
			return new Promise((resolve, reject) => {
				if (JSON.stringify(state.userInfo) !== '{}') {
					state.userInfo = JSON.parse(localStorage.getItem('loginUserInfo'));
					return resolve(state.userInfo);
				}
				if (localStorage.getItem('loginUserInfo')) {
					state.userInfo = JSON.parse(localStorage.getItem('loginUserInfo'));
					return resolve(state.userInfo);
				}
				util
					.ajax({
						url: '/ybzy/platuser/front/getLoginUserInfo'
					})
					.then(res => {
						if (res.rCode == 0) {
							state.userInfo = res.results;
							// 登录后获取更多的用户信息并存储
							localStorage.setItem('loginUserInfo', JSON.stringify(res.results));
							return resolve(res.results);
						} else {
							Message({
								message: '未获取用户信息',
								type: 'warning'
							});
						}
						resolve({});
					});
			});
		}
	}
};
