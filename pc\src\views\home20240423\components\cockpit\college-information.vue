<template>
	<div class="college-information">
		<div v-for="(item, k) in list" :key="k" class="college-information-item">
			<div class="name">{{ item.name }}</div>
			<div class="val">
				{{ item.count }}
				<span class="unit">{{ item.unit }}</span>
			</div>
			<div class="img">
				<img :src="item.img" alt="" />
			</div>
		</div>
	</div>
</template>

<script>
import { xodbApi2 } from '@/api/xodb';
export default {
	name: 'CollegeInformation',
	components: {},
	props: {},
	data() {
		return {
			dataRes: {}
		};
	},
	computed: {
		loginUserInfo() {
			return JSON.parse(localStorage.getItem('loginUserInfo')) || {};
		},
		list() {
			// 校领导：xLeader 院领导：yLeader 都不是：noLeader
			const LeaderRole = localStorage.getItem('LeaderRole');
			return [
				{
					name: '现有教师数',
					count: this.dataRes.xyjss || '0',
					unit: '人',
					img: require('@/assets/images/home20240423/information-icon1.png')
				},
				{
					name: LeaderRole === 'yLeader' ? '专业数' : '现有二级学院数',
					count: LeaderRole === 'yLeader' ? this.dataRes.zysl || '0' : this.dataRes.xyejxys || '0',
					unit: '个',
					img: require('@/assets/images/home20240423/information-icon2.png')
				},
				{
					name: '本学年招生数',
					count: this.dataRes.bxyzss || '0',
					unit: '人',
					img: require('@/assets/images/home20240423/information-icon3.png')
				},
				{
					name: '现有学生数',
					count: this.dataRes.xyxss || '0',
					unit: '人',
					img: require('@/assets/images/home20240423/information-icon4.png')
				}
			];
		}
	},

	created() {
		this.toolLeaderRole();
	},
	methods: {
		// 判断权限展示不同功能
		toolLeaderRole() {
			this.getDataN('ads_xx_xyxyjss_query', 'dataRes');
		},
		async getDataN(url, listName) {
			const LeaderRole = localStorage.getItem('LeaderRole');
			try {
				const {
					data: { list }
				} = await xodbApi2.post({
					url,
					params: {
						xyjgbh: LeaderRole === 'xLeader' ? '1' : this.loginUserInfo.orgCode
					}
				});
				const data = list[0] || {};
				this[listName] = data;
			} catch (error) {
				console.error(`处理数据失败${listName}:`, error);
			}
		}
	}
};
</script>

<style lang="scss" scoped>
.college-information {
	font-family: MicrosoftYaHei, MicrosoftYaHei;

	display: flex;
	justify-content: space-between;
	width: 100%;
	.college-information-item {
		width: 222px;
		height: 100%;
		background: linear-gradient(90deg, rgba(233, 255, 252, 0.8) 0%, rgba(255, 255, 255, 0) 100%);
		border-radius: 4px;
		border: 1px solid;
		border-image: linear-gradient(80deg, rgba(255, 255, 255, 0), rgba(255, 255, 255, 1)) 1 1;
		padding: 29px 0 20px 20px;
		margin-right: 5px;
		&:last-child {
			margin-right: 0;
		}
		position: relative;
		.name {
			height: 24px;
			font-weight: bold;
			font-size: 18px;
			color: #053b6d;
			line-height: 24px;
			margin-bottom: 30px;
		}

		.val {
			height: 41px;
			font-family: DINPro, DINPro;
			font-weight: bold;
			font-size: 32px;
			color: #0a325b;
			line-height: 41px;
			.unit {
				height: 21px;
				font-family: MicrosoftYaHei;
				font-size: 16px;
				color: #294d79;
				line-height: 21px;
				font-weight: normal;
			}
		}

		.img {
			width: 40px;
			height: 44px;
			position: absolute;
			top: 32px;
			right: 25px;
			img {
				width: 100%;
				height: 100%;
			}
		}
	}
}
</style>
