<template>
	<div class="card-box">
		<es-toolbar
			v-if="toolbar.length > 0"
			:contents="toolbar"
			show-label
			reset
			@search="totalAndReload"
			@reset="totalAndReload"
			@submit="totalAndReload"
		></es-toolbar>
		<es-tabs v-model="activeTabName" @tab-click="handleClick">
			<el-tab-pane
				v-for="(tab, tabK) of tabs"
				:key="tabK"
				:label="` ${tab.label}(${tab.count})`"
				:name="tab.label"
			></el-tab-pane>
		</es-tabs>

		<!-- v-if="list.length > 0 || loading" -->
		<div v-infinite-scroll="load" :infinite-scroll-disabled="noMore" class="card-list">
			<div class="card-list-item" @click="$emit('btnClick', { handle: { text: '新增' } })">
				<div class="add-btn">
					<i class="iconfont icon-add"></i>
					<span>新增</span>
				</div>
			</div>
			<div v-for="(item, k) of list" :key="k" class="card-list-item">
				<span class="state" :class="stateTool(item.status)">{{ item.statusTxt }}</span>
				<img class="img" src="@/assets/images/scientific-sys/portrait.png" alt="" />
				<div
					class="content"
					@click.stop="
						theadN.theadLook && $emit('btnClick', { handle: theadN.theadLook, row: item })
					"
				>
					<p
						v-for="(theadItem, theadK) of theadN.theadContent"
						:key="theadK"
						class="content-text ellipsis-1"
					>
						<span>{{ theadItem.title }}:{{ item[theadItem.field] }}</span>
					</p>
				</div>
				<div class="btn-list">
					<template v-for="(theadHandle, theadHandleK) of theadN.theadHandle">
						<span
							v-if="!theadHandle.rules || theadHandle.rules(item)"
							:key="theadHandleK"
							class="btn-item"
							@click.stop="$emit('btnClick', { handle: theadHandle, row: item })"
						>
							{{ theadHandle.text }}
						</span>
					</template>
				</div>
			</div>
			<div class="more-box">
				<span class="more-box-text">{{ loading ? '加载中...' : noMore ? '我是底线' : '' }}</span>
			</div>
		</div>
		<!-- <Empty v-else /> -->
	</div>
</template>

<script>
export default {
	name: 'CardList',
	props: {
		url: {
			type: String,
			default: ''
		},
		totalUrl: {
			type: String,
			default: ''
		},
		thead: {
			type: Array,
			default: () => {
				return [];
			}
		},
		toolbar: {
			type: Array,
			default: () => {
				return [];
			}
		},
		param: {
			type: Object,
			default: () => {
				return {};
			}
		}
	},
	data() {
		return {
			activeTabName: '',
			// 0-草稿  1-审核中  2-审核通过  9-驳回
			tabs: [
				{
					label: '全部',
					count: 0,
					value: ''
				},
				{
					label: '草稿',
					count: 0,
					value: '0'
				},
				{
					label: '审核中',
					count: 0,
					value: '1'
				},
				{
					label: '审核通过',
					count: 0,
					value: '2'
				},
				{
					label: '驳回',
					count: 0,
					value: '9'
				}
			],
			list: [],
			loading: false,
			noMore: false,
			params: {},
			pageNum: 1,
			pageSize: 10,
			total: 0,
			getTotal: false
		};
	},
	computed: {
		theadN() {
			let theadContent = [];
			let theadHandle = [];
			let theadLook = null;
			this.thead.forEach(item => {
				if (item.type == 'handle') {
					theadHandle = item.events.filter(item => {
						if (item.text === '查看' || item.code === 'look') {
							theadLook = item;
							// return false;
						}
						return true;
					});
				} else {
					theadContent.push(item);
				}
			});
			return {
				theadContent,
				theadHandle,
				theadLook
			};
		}
	},
	created() {
		this.activeTabName = this.tabs[0].label;
	},
	methods: {
		totalAndReload(e) {
			this.getTotal = false;
			this.reload(e);
		},
		reload(e) {
			const params = e?.data ?? e ?? {};
			this.params = params;
			this.pageNum = 1;
			this.list = [];
			this.loading = false;
			this.noMore = false;
			this.load();
		},
		handleClick(e) {
			this.activeTabName = e.name;
			this.reload({});
		},
		load() {
			if (this.loading || this.noMore) return;
			if (!this.getTotal) {
				this.loadTotal();
			}
			this.loading = true;

			// 查找activeTabName对应tabs对象
			const tab = this.tabs.find(item => item.label === this.activeTabName) || {};
			// 标签参数
			const tabParams = { status: '' };
			if (tab.label) {
				tabParams.status = tab.value;
			}

			this.$request({
				url: this.url,
				params: {
					...this.params,
					...tabParams,
					...this.param,
					pageNum: this.pageNum,
					pageSize: this.pageSize
				}
			})
				.then(res => {
					if (res.rCode == 0) {
						this.loading = false;
						const list = res.results?.records || [];
						if (this.pageNum == 1) {
							this.list = list;
						} else {
							this.list = [...this.list, ...list];
						}
						this.pageNum++;
						if (list.length < this.pageSize) {
							this.noMore = true;
						}
					} else {
						this.$message.error(res.msg || '网络异常！');
						this.loading = false;
						this.noMore = false;
					}
				})
				.catch(err => {
					console.log(err);
				});
		},
		loadTotal() {
			this.getTotal = true;
			this.$request({
				url: this.totalUrl,
				params: {
					...this.params,
					...this.param
				}
			})
				.then(res => {
					const _data = res.results || {};
					this.$set(this.tabs[0], 'count', _data.allCount || 0);
					this.$set(this.tabs[1], 'count', _data.draftCount || 0);
					this.$set(this.tabs[2], 'count', _data.verifyInCount || 0);
					this.$set(this.tabs[3], 'count', _data.passCount || 0);
					this.$set(this.tabs[4], 'count', _data.returnCount || 0);
				})
				.catch(err => {
					console.log(err);
				});
		},
		// 计算状态颜色
		stateTool(state) {
			// 0-草稿  1-审核中  2-审核通过  9-驳回
			let stateCurrent = '';
			switch (state) {
				case '0':
					stateCurrent = 'info';
					break;
				case '1':
					stateCurrent = 'warning';
					break;
				case '2':
					stateCurrent = 'primary';
					break;
				case '9':
					stateCurrent = 'danger';
					break;
				default:
					break;
			}
			return stateCurrent;
		}
	}
};
</script>

<style lang="scss" scoped>
.card-box {
	height: 100%;
	width: 100vw;
	background: #efefef;

	.card-list {
		display: flex;
		flex-wrap: wrap;
		align-content: start;
		height: calc(100% - 90px);
		padding: 20px 0 0 20px;
		overflow: auto;
		.card-list-item {
			// width: 401px;
			flex-basis: 30%;
			height: 219px;
			margin: 10px;
			padding: 20px 20px 0 20px;
			position: relative;
			background: #fff;
			border-radius: 5px;
			overflow: hidden;
			position: relative;
			top: 0;
			box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
			animation: scale-in-center 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94) both;
			transition: all 0.3s ease;

			.state {
				position: absolute;
				top: 0;
				right: 0;
				color: #fff;
				width: 72px;
				height: 82px;
				background: #909399;
				padding-left: 33px;
				padding-top: 3px;
				clip-path: polygon(100% 0, 0 0, 100% 100%);
			}
			.add-btn {
				width: 100%;
				height: 80%;
				display: flex;
				justify-content: center;
				align-items: center;
				font-weight: 700;
				margin-bottom: 20px;
				margin-top: 5px;
				color: #333;
			}
			.content {
				padding-left: 50px;
				font-size: 14px;
				cursor: default;

				.content-text {
					color: #7f7f7f;
					margin-bottom: 10px;
					&:first-child {
						font-weight: 700;
						margin-bottom: 20px;
						margin-top: 5px;
						color: #333;
					}
				}
			}

			.btn-list {
				padding: 15px 0;
				display: flex;
				align-items: center;
				justify-content: center;
				border-top: #d7d7d7 1px solid;
				margin-top: 20px;
				color: #7f7f7f;

				.btn-item {
					width: 100%;
					text-align: center;
					border-right: #d7d7d7 1px solid;
					padding: 2px 0;
					cursor: pointer;
					&:hover {
						color: #409eff;
					}
					&:last-child {
						border-right: none;
					}
				}
			}
			&:hover {
				position: relative;
				top: -5px;
				box-shadow: 0 10px 20px rgba(0, 0, 0, 0.15);
				.content {
					.content-text {
						&:first-child {
							color: #409eff;
						}
					}
				}
			}

			// 审批中
			.primary {
				background: #409eff;
			}
			// 待立项
			.success {
				background: #67c23a;
			}
			.warning {
				background: #e6a23c;
			}
			// 驳回
			.danger {
				background: #f56c6c;
			}
			// 草稿
			.info {
				background: #909399;
			}

			.img {
				position: absolute;
				top: 10px;
				left: 10px;
				width: 46px;
				height: 46px;
			}
		}
		.more-box {
			width: 100%;
			color: #7f7f7f;
			text-align: center;
			padding: 20px 0;
			&-text {
				position: relative;
				// 前后加一条线
				&::after,
				&::before {
					content: '';
					position: absolute;
					top: 10px;
					bottom: 0;
					right: 120%;
					width: 230px;
					height: 1px;
					background: linear-gradient(to right, #d7d7d700, #d7d7d7);
				}
				&::before {
					background: linear-gradient(to right, #d7d7d7, #d7d7d700);

					right: auto;
					left: 120%;
				}
			}
		}
		@keyframes scale-in-center {
			0% {
				-webkit-transform: scale(0);
				transform: scale(0.3);
				opacity: 1;
			}
			100% {
				-webkit-transform: scale(1);
				transform: scale(1);
				opacity: 1;
			}
		}
	}
	::v-deep {
		.el-tabs__header {
			margin: 0;
		}
		.el-tabs__nav-wrap {
			padding-left: 60px !important;
			background: #fff;
		}
		.el-tabs__nav-wrap:not(.is-hide)::after {
			content: '';
			position: absolute;
			left: 0;
			bottom: 0;
			width: 100%;
			height: 0px;
			background-color: rgba(0, 0, 0, 0.12);
			z-index: 1;
		}
	}
}
</style>
