export default {
	computed: {
		formItemList() {
			return [
				{
					name: 'years',
					label: '年份',
					value: '',
					col: 12,
					rules: {
						required: true,
						message: '请输入年份'
					}
				},
				{
					name: 'number',
					label: '序号',
					value: '',
					col: 12,
					rules: {
						required: true,
						message: '请输入序号'
					}
				},
				{
					name: 'staffNumber',
					label: '教工号',
					value: '',
					col: 12,
					rules: {
						required: true,
						message: '请输入教工号'
					}
				},
				{
					name: 'name',
					label: '姓名',
					value: '',
					col: 12,
					rules: {
						required: true,
						message: '请输入姓名'
					}
				},
				{
					name: 'idNumber',
					label: '身份证号',
					value: '',
					col: 12,
					rules: {
						required: true,
						message: '请输入身份证号'
					}
				},
				{
					name: 'birthday',
					label: '出生年月',
					value: '',
					col: 12,
					rules: {
						required: true,
						message: '请输入出生年月'
					}
				},
				{
					name: 'age',
					label: '年龄',
					value: '',
					col: 12,
					rules: {
						required: true,
						message: '请输入年龄'
					}
				},
				{
					name: 'sex',
					label: '性别',
					value: '',
					col: 12,
					rules: {
						required: true,
						message: '请输入性别'
					}
				},
				{
					name: 'contactNumber',
					label: '联系电话',
					value: '',
					col: 12,
					rules: {
						required: true,
						message: '请输入联系电话'
					}
				},
				{
					name: 'nativePlace',
					label: '籍贯',
					value: '',
					col: 12,
					rules: {
						required: true,
						message: '请输入籍贯'
					}
				},
				{
					name: 'beforeName',
					label: '曾用名',
					value: '',
					col: 12,
					rules: {
						required: true,
						message: '请输入曾用名'
					}
				},
				{
					name: 'politicalStatus',
					label: '政治面貌',
					value: '',
					col: 12,
					rules: {
						required: true,
						message: '请输入政治面貌'
					}
				},
				{
					name: 'nation',
					label: '民族',
					value: '',
					col: 12,
					rules: {
						required: true,
						message: '请输入民族'
					}
				},
				{
					name: 'isCollegeDiploma',
					label: '是否具有高校教师资格证',
					value: '',
					col: 12,
					rules: {
						required: true,
						message: '请输入是否具有高校教师资格证'
					}
				},
				{
					name: 'diplomaFile',
					label: '证书附件',
					value: '',
					col: 12,
					rules: {
						required: true,
						message: '请输入证书附件'
					}
				},
				{
					name: 'joinLeagueTime',
					label: '入团时间',
					value: '',
					col: 12,
					rules: {
						required: true,
						message: '请输入入团时间'
					}
				},
				{
					name: 'joinPartyTime',
					label: '入党时间',
					value: '',
					col: 12,
					rules: {
						required: true,
						message: '请输入入党时间'
					}
				},
				{
					name: 'authorizedCategory',
					label: '编制类别',
					value: '',
					col: 12,
					rules: {
						required: true,
						message: '请输入编制类别'
					}
				},
				{
					name: 'isTryout',
					label: '是否有试用期',
					value: '',
					col: 12,
					rules: {
						required: true,
						message: '请输入是否有试用期'
					}
				},
				{
					name: 'tryoutTime',
					label: '试用期限',
					value: '',
					col: 12,
					rules: {
						required: true,
						message: '请输入试用期限'
					}
				},
				{
					name: 'tryoutTerm',
					label: '试用到期',
					value: '',
					col: 12,
					rules: {
						required: true,
						message: '请输入试用到期'
					}
				},
				{
					name: 'isDoubleQualified',
					label: '是否双师型教师',
					value: '',
					col: 12,
					rules: {
						required: true,
						message: '请输入是否双师型教师'
					}
				},
				{
					name: 'contractYears',
					label: '合同年限',
					value: '',
					col: 12,
					rules: {
						required: true,
						message: '请输入合同年限'
					}
				},
				{
					name: 'isLongContract',
					label: '是否长期合同',
					value: '',
					col: 12,
					rules: {
						required: true,
						message: '请输入是否长期合同'
					}
				},
				{
					name: 'contractBegin',
					label: '合同期限开始',
					value: '',
					col: 12,
					rules: {
						required: true,
						message: '请输入合同期限开始'
					}
				},
				{
					name: 'contractEnd',
					label: '合同期限结束',
					value: '',
					col: 12,
					rules: {
						required: true,
						message: '请输入合同期限结束'
					}
				},
				{
					name: 'contractFile',
					label: '合同附件',
					value: '',
					col: 12,
					rules: {
						required: true,
						message: '请输入合同附件'
					}
				},
				{
					name: 'formalTime',
					label: '实际转正日期',
					value: '',
					col: 12,
					rules: {
						required: true,
						message: '请输入实际转正日期'
					}
				},
				{
					name: 'formalFile',
					label: '转正附件',
					value: '',
					col: 12,
					rules: {
						required: true,
						message: '请输入转正附件'
					}
				},
				{
					name: 'isDoubleShoulder',
					label: '是否双肩挑',
					value: '',
					col: 12,
					rules: {
						required: true,
						message: '请输入是否双肩挑'
					}
				},
				{
					name: 'personPostStatus',
					label: '职位状态',
					value: '',
					col: 12,
					rules: {
						required: true,
						message: '请输入职位状态'
					}
				},
				{
					name: 'status',
					label: '状态',
					value: '',
					col: 12,
					rules: {
						required: true,
						message: '请输入状态'
					}
				},
			]
		}
	}
};
