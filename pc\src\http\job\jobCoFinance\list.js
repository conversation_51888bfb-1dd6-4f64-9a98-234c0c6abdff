import httpApi from '@/http/job/jobCoFinance/api.js';
export default {
	computed: {
		component() {
			return {
				// 纯列表结构
				name: 'es-data-table'
			};
		},
		treeParam() {
			return {
				
			};
		},
		table() {
			return {
				url: httpApi.jobCoFinanceList,
				param: { handlerType: '' },
				full: true,
				fit: true,
				page: true,
				stripe: true,
				numbers: true,
				checkbox: true,
				'row-key': 'id',
				toolbar: [
					{
						type: 'button',
						contents: [
							{
								text: '新增',
								code: 'toolbar'
							},
							{
								text: '删除',
								type: 'danger',
								code: 'toolbar'
							},
							{
								text: '查看',
								type: 'primary',
								code: 'toolbar'
							},
						]
					},
					{
						type: 'search',
						reset: true,
						contents: [
							//{
							//	name: 'accountName_like',
							//	placeholder: '配置名称',
							//	label: '配置名称',
							//	col: 6
							//},
							{
								col: 6,
								name: 'name',
								label: '机构名称',
								placeholder: '机构名称'
							},
							{
								type: 'date',
								col: 6,
								name: 'startDate',
								label: '更新开始时间',
								placeholder: '更新开始时间'
							},
							{
								type: 'date',
								col: 6,
								name: 'endDate',
								label: '更新结束时间',
								placeholder: '更新结束时间'
							}
						]
					}
				],
				thead: [
					{
						title: '机构名称',
						align: 'left',
						field: 'name'
					},
					{
						title: '启用状态',
						align: 'center',
						field: 'status',
						width: 100,
						type: 'switch',
					},
					{
						title: '操作',
						type: 'handle',
						width: 180,
						events: [
							{
								text: '查看',
								code: 'row'
							},
							{
								text: '编辑',
								code: 'row'
							},
							{
								text: '删除',
								code: 'row'
							}
						]
					}
				],
				optionData: {
					status: [
						{
							value: 1,
							name: '启用'
						},
						{
							value: 0,
							name: '禁用'
						}
					]
				},
			};
		}
	},
	methods: {
		changeTable(res){
			switch (res.name){
				case 'status': this.changeStatus(res.data.id); break;
			}
		},
		//改变数据启用状态
		changeStatus(id) {
			this.$request({
				url: httpApi.jobCoFinanceChangeStatus,
				data: {id: id},
				method: 'POST'
			}).then(res=>{
				if(res.success){
					this.$message.success('修改成功');
				}else{
					this.$message.error(res.msg);
				}
			});
		}
	}
};
