<template>
	<es-data-table
		ref="materialTable"
		:row-style="tableRowClassName"
		checkbox
		:full="true"
		:fit="true"
		:thead="materialThead"
		:toolbar="materialToolbar"
		:border="true"
		:page="pageOption"
		:url="materialDataUrl"
		:numbers="true"
		:param="{ categoryId: this.categoryId }"
		close
		form
	></es-data-table>
</template>

<script>
import materialApi from '@/http/logistics/material/api.js';

export default {
	name: 'MaterialByCategory',
	props: {
		categoryId: {
			type: String,
			default: ''
		}
	},
	data() {
		return {
			materialToolbar: [
				{
					type: 'button',
					contents: [
						{
							text: '确定',
							code: 'selected',
							type: 'primary',
							event: res => {
								this.$emit('selected', res.ele.selected);
								this.$emit('update:visible', false);
							}
						},
						{
							text: '取消',
							code: 'cancel',
							event: res => {
								this.$emit('update:visible', false);
							}
						}
					]
				},
				{
					type: 'search',
					reset: true,
					contents: [
						{
							type: 'text',
							name: 'name',
							placeholder: '原料名称'
						},
						{
							type: 'select',
							name: 'categoryId',
							placeholder: '原料分类',
							valueType: 'string',
							default: true,
							parentCheck: true,
							tree: true,
							url: '/ybzy/hqbasematerialcategory/getTreeListOne?parentId=' + this.categoryId,
							'value-key': 'id',
							'label-key': 'name'
						}
					]
				}
			],
			materialThead: [
				{
					title: '原料编号',
					field: 'code',
					align: 'center',
					showOverflowTooltip: true
				},
				{
					title: '原料名称',
					field: 'name',
					align: 'center',
					showOverflowTooltip: true
				},
				{
					title: '原料分类',
					field: 'categoryName',
					align: 'center',
					minWidth: 50,
					showOverflowTooltip: true
				},
				{
					title: '原料单位',
					field: 'unit',
					width: 80,
					align: 'center',
					showOverflowTooltip: true
				},
				{
					title: '规格',
					field: 'specification',
					minWidth: 70,
					align: 'center',
					showOverflowTooltip: true
				},
				{
					title: '供应商',
					field: 'brand',
					minWidth: 100,
					align: 'center',
					showOverflowTooltip: true
				}
			],
			pageOption: {
				layout: 'total, prev, pager, next, jumper',
				pageSize: 1000,
				// hideOnSinglePage: true,
				position: 'right',
				current: 1,
				pageNum: 1
			},
			materialDataUrl: materialApi.listJson
		};
	},
	mounted() {},
	methods: {
		/**
		 * 表格行高
		 */
		tableRowClassName({ row, rowIndex }) {
			let styleRes = {
				height: '54px !important'
			};
			return styleRes;
		}
	}
};
</script>
