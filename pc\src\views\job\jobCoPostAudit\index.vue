<template>
	<!-- 在list.js修改component.name值可改变展示组件 -->
	<component
		:is="component.name"
		ref="dataTable"
		v-bind="table"
		:table="table"
		@selection-change="handleSelectionChange"
		@btnClick="btnClick"
		@success="successAfter"
	>
		<es-dialog
			v-if="showForm"
			:title="formTitle"
			:visible.sync="showForm"
			:close-on-click-modal="false"
			:middle="true"
			width="600px"
			height="450px"
			class="dialog-form"
		>
			<es-form
				ref="form"
				:model="formData"
				:contents="formItemList"
				:genre="2"
				height="300px"
				collapse
				@change="handleFormItemChange"
				@submit="handleFormSubmit"
				@reset="showForm = false"
			/>
		</es-dialog>
		<es-dialog
			v-if="showView"
			title="岗位详情"
			:visible.sync="showView"
			:close-on-click-modal="false"
			:middle="true"
			height="700px"
			class="dialog-form"
		>
			<!-- 设置对话框内容高度 -->
			<div class="sketch_content">
				<table class="resumeTable">
					<tr>
						<th>企业名称：</th>
						<td>{{ viewData.enterprise.corpName }}</td>
						<th>岗位名称：</th>
						<td>{{ viewData.name }}</td>
					</tr>
					<tr>
						<th>岗位类型：</th>
						<td>{{ viewData.postTypeDes }}</td>
						<th>工作性质：</th>
						<td>{{ viewData.jobNature }}</td>
					</tr>
					<tr>
						<th>学历要求：</th>
						<td>{{ viewData.education }}</td>
						<th>工作经验：</th>
						<td>{{ viewData.workExperience }}</td>
					</tr>
					<tr>
						<th>薪资：</th>
						<td colspan="3">{{ viewData.salaryStructure }}</td>
					</tr>
					<tr>
						<th valign="top">岗位介绍：</th>
						<td valign="top" colspan="3"><div v-html="viewData.introduce"></div></td>
					</tr>
					<tr>
						<th>所属地区：</th>
						<td colspan="3">{{ viewData.areaName }}</td>
					</tr>
					<tr>
						<th>详细地址：</th>
						<td colspan="3">{{ viewData.address }}</td>
					</tr>
					<tr v-if="formData.lon && formData.lat">
						<th></th>
						<td colspan="3">
							<baidu-map
								:center="center"
								:zoom="zoom"
								class="baiduMap"
								:scroll-wheel-zoom="true"
								@ready="handler"
							>
								<bm-view style="width: 80%; height: 280px; flex: 1"></bm-view>
							</baidu-map>
						</td>
					</tr>

					<tr>
						<th>岗位标签：</th>
						<td colspan="3">
							<el-tag v-for="(tag, index) in viewData.tags" :key="index">{{ tag }}</el-tag>
						</td>
					</tr>
					<tr>
						<th>发布人：</th>
						<td>{{ viewData.createUserName }}</td>
						<th>发布时间：</th>
						<td>{{ viewData.createTime }}</td>
					</tr>
					<tr>
						<th>状态：</th>
						<td>{{ viewData.statusStr }}</td>
						<th>审核状态：</th>
						<td>{{ viewData.auditStatusStr }}</td>
					</tr>
					<tr>
						<th>审核意见：</th>
						<td colspan="3">{{ viewData.auditOpinion }}</td>
					</tr>
				</table>
			</div>
		</es-dialog>
		<el-dialog
			v-if="showPostResumPage"
			title="岗位投递情况"
			:visible.sync="showPostResumPage"
			append-to-body
			width="70%"
			height="700px"
			:close-on-click-modal="false"
			:destroy-on-close="true"
		>
			<postResumePage ref="postResumePage" :post-id="postId"></postResumePage>
		</el-dialog>
	</component>
</template>

<script>
import httpApi from '@/http/job/jobCoPostAudit/api.js';
import list from '@/http/job/jobCoPostAudit/list.js';
import audit from '@/http/job/jobCoPostAudit/audit.js';
import view from '@/http/job/jobCoPostAudit/view.js';
import postResumePage from '@/views/job/jobCoPostResume/index.vue';

export default {
	name: 'JobCoPost',
	components: { postResumePage },
	mixins: [list, audit, view],
	data() {
		return {
			selectRowData: [],
			selectRowIds: [],
			formTitle: '',
			showForm: false,
			showView: false,
			formData: {},
			viewData: {},
			extraData: {},
			//投递情况表
			showPostResumPage: false,
			postId: null,
			// 地图相关
			BMap: '',
			map: '',
			center: {
				lng: 116.395645038,
				lat: 39.9299857781
			},
			zoom: 17
		};
	},
	watch: {
		showForm(val) {
			if (!val) {
				this.formData = {};
			}
		}
	},
	methods: {
		// 地图初始化
		handler({ BMap, map }) {
			this.BMap = BMap;
			this.map = map;
			this.addPoint(this.viewData.lon, this.viewData.lat);
		},
		// 添加点位
		addPoint(lng, lat) {
			let newLng = lng ? lng : this.center.lng;
			let newLat = lat ? lat : this.center.lat;
			let map = this.map;
			let BMap = this.BMap;
			map.clearOverlays();
			var point = new BMap.Point(newLng, newLat);
			let zoom = map.getZoom();
			setTimeout(() => {
				map.centerAndZoom(point, zoom);
			}, 0);
			var marker = new BMap.Marker(point); // 创建标注
			map.addOverlay(marker); // 将标注添加到地图中
		},
		// 初始化扩展数据，字典等
		successAfter(data) {
			this.extraData = data.results.extraData;
		},
		// 数据列表多选回调
		handleSelectionChange(data) {
			let ids = [];
			data.forEach(row => {
				ids.push(row.id);
			});
			this.selectRowData = data;
			this.selectRowIds = ids;
		},
		// 表单变更时, 回调处理
		handleFormItemChange(filed, data) {
			// 处理表单字段systemCode的变更
			// if (filed == 'systemCode') {
			//	this.formData.systemCode = data.id;
			//	this.formData.systemName = data.name;
			// }
		},
		btnClick(res) {
			let code = res.handle.code;
			switch (code) {
				case 'view':
					this.$request({
						url: httpApi.jobCoPostInfo,
						params: { id: res.row.id }
					}).then(res => {
						// 处理自定义的数据格式化, extFiled 额外属性名， key、val 数据映射
						// this.viewData = this.formatExtraData(res.results[*], 'extFiled', 'key', 'val');
						this.viewData = res.results;
						// 打开查看弹窗
						this.showView = true;
					});
					break;
				case 'add':
					this.formTitle = '新增';
					this.showForm = true;
					// 初始化表单数据
					this.formData = {
						// useStatus: 1,
					};
					break;
				case 'postResumePageBtn':
					this.showPostResumPage = true;
					this.postId = res.row.id;
					break;
				case 'audit':
					this.formTitle = '审核';
          this.formData = { ...res.row };
          this.showForm = true;
					break;
				case 'delete':
					this.deleteId(res.row.id);
					break;
				default:
					break;
			}
		},
		handleFormSubmit() {
			let formData = { id: this.formData.id, auditStatus: this.formData.auditStatus, auditOpinion: this.formData.auditOpinion, orgId: this.formData.orgId };
			// 可额外处理formData中的数据

			this.$request({
				url: httpApi.jobCoPostAudit,
				data: formData,
				method: 'POST',
			}).then(res => {
				if (res.rCode == 0) {
					this.$message.success('操作成功');
					this.showForm = false;
					this.formData = {};
					this.$refs.dataTable.reload();
				} else {
					this.$message.error(res.msg);
				}
			});
		},
		deleteId(id) {
			this.$confirm('确定要删除选中的数据', '删除', {
				confirmButtonText: '确定',
				cancelButtonText: '取消',
				type: 'warning'
			})
				.then(() => {
					this.$request({
						url: httpApi.jobCoPostDeleteById,
						data: { id: id },
						method: 'POST'
					}).then(res => {
						if (res.rCode == 0) {
							this.$message.success('操作完成');
							this.$refs.dataTable.reload();
						} else {
							this.$message.error(res.msg);
						}
					});
				})
				.catch(() => {});
		},
		// 根据列表额外数据返回的 字典信息，格式化数据
		formatExtraData(data, extFiled, key, val) {
			let text = '未知';
			this.extraData[extFiled].forEach(dict => {
				if (dict[key] === data) {
					text = dict[val];
				}
			});
			return text;
		}
	}
};
</script>
<style scoped>
.sketch_content {
	overflow: auto;
	height: 630px;
	border-top: 1px solid #eff1f4;
	border-bottom: 1px solid #eff1f4;
	padding: 0px 30px 11px 27px;
}

.sketch_content::-webkit-scrollbar {
	width: 3px;
}

.sketch_content::-webkit-scrollbar-thumb {
	background: #8798af;
	border-radius: 2px;
}

.sketch_content::-webkit-scrollbar-track {
	background: transparent;
}

.resumeTable {
	border-bottom: none;
	border-top: none;
	width: 800px;
	margin: 10px auto;
}

.resumeTable th {
	width: 10%;
}

.resumeTable td {
	height: 50px;
	width: 40%;
	/* font-weight: bold; */
}

.baiduMap {
	width: 100%;
}

.el-tag {
	margin-right: 10px;
}
</style>
