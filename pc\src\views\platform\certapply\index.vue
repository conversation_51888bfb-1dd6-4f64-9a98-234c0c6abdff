<template>
    <div class="content">
        <div style="width:100%">
            <el-menu :default-active="activeMenus" mode="horizontal" class="es-menu" @select="handleSelect">
                <el-menu-item v-for="(item, index) in menus" :key="index" :index="String(index)">
                    {{ item.label }}
                </el-menu-item>
            </el-menu>
            <es-data-table :row-style="tableRowClassName" v-if="activeMenus === '0'" ref="table" :key="tableCount" :full="true" :fit="true" :thead="thead" :toolbar="toolbar"
                :border="true" :page="pageOption" :url="dataTableUrl" :numbers="true" @btnClick="btnClick"
                @sort-change="sortChange" :param="params" @submit="hadeSubmit" close>
            </es-data-table>
            <es-data-table :row-style="tableRowClassName"  v-if="activeMenus === '1'" ref="table" :key="tableCount" :full="true" :fit="true" :thead="thead" :toolbar="toolbar"
                :border="true" :page="pageOption" :url="dataTableUrl" :numbers="true" @btnClick="btnClick"
                @sort-change="sortChange" :param="params" @submit="hadeSubmit" close>
            </es-data-table>
        </div>
        <es-dialog v-if="visible" :title="dialogTitle" :visible.sync="visible" height="80%" width="80%">
            <addDialog v-if="visible && addFlag" @refresh="refreshData" :select-info="selectInfo" style="height: calc(100% - 40px);" :form-info="selectRow" :title="dialogTitle" @cancel="dialogCancel"></addDialog>
            <auditDialog v-if="visible && auditFlag" @refresh="refreshData" type="audit" style="height: calc(100% - 40px);" :select-info="selectInfo" :form-info="selectRow" @cancel="dialogCancel"></auditDialog>
            <auditDialog v-if="visible && viewFlag" @refresh="refreshData" type="view" style="height: calc(100% - 40px);" :select-info="selectInfo" :form-info="selectRow" @cancel="dialogCancel"></auditDialog>
        </es-dialog>
        <es-dialog title="删除" :visible.sync="showDelete" width="20%" :close-on-click-modal="false" :middle="true"
            height="140px">
            <div>确定要删除该条数据吗</div>
            <div class="btn-box">
                <div class="btn theme" @click="deleteRow">确定</div>
                <div class="btn" @click="showDelete = false">取消</div>
            </div>
        </es-dialog>
    </div>
</template>

<script>
import interfaceUrl from '@/http/platform/certapply.js';
import addDialog from './component/add';
import auditDialog from './component/audit';
import viewDialog from './component/view';
export default {
    components: { addDialog,auditDialog,viewDialog },
    data() {
        return {
            visible: false,
            addFlag: false,
            auditFlag: false,
            viewFlag: false,
            showDelete: false,
            deleteId: '',
            dialogTitle: "",
            menus: [{ label: '待审核' }, { label: '已退回' }, { label: '已认证' }],
			activeMenus: '0',
            queryStatus:[],
            dataTableUrl: interfaceUrl.listJson,
            treeData: [],
            tableCount: 1,
            selectInfo: null,//选中的数据值
            selectRow: null,
            typeDicData:this.typeDicData,
            search: {
                name: 'keyword',
                placeholder: '请输入关键字筛选'
            },
            toolbar: [],
            /**
             * 待审核工具栏
             */
            waitAuditToolbar: [ 
                {
                    type: 'button',
                    contents: [
                        {
                            text: '新增',
                            code: 'add',
                            type: 'primary'
                        }
                    ]
                },
                {
                    type: 'search',
                    contents: [
                        {
                            type: 'text',
                            name: 'keyword',
                            placeholder: '关键字查询'
                        },
                    ]
                },
                {
                    type: 'filter',
                    contents: [
                        {
                            type: 'text',
                            label: '姓名',
                            placeholder: '请输入姓名',
                            name: 'xm',
                            clearable: true,
                            col: 4
                        },
                        {
                            type: 'select',
                            label: '业务类型',
                            placeholder: '请选择业务类型',
                            name: 'businessTypeId',
                            data: this.typeDicData,
                            'label-key': 'name',
                            'value-key': 'value',
                            col: 4
                        },
                        {
                            type: 'select',
                            label: '认证来源',
                            placeholder: '请选择认证来源',
                            name: 'certifyFrom',
                            event: 'multipled',
                            sysCode: 'plat_person_cert_apply_certify_from',
                            'label-key': 'shortName',
                            'value-key': 'cciValue',
                            clearable: true,
                            col: 4
                        },
                        {
                            type: 'text',
                            label: '学号/职工号',
                            placeholder: '请输入学号/职工号',
                            name: 'xhZgh',
                            clearable: true,
                            col: 4
                        },
                        {
                            type: 'select',
                            label: '人员类型',
                            placeholder: '请选择人员类型',
                            name: 'zglx',
                            event: 'multipled',
                            sysCode: 'plat_person_type',
                            'label-key': 'shortName',
                            'value-key': 'cciValue',
                            clearable: true,
                            col: 4
                        },
                        {
                            type: 'date',
                            label: '申请时间',
                            placeholder: '请输入申请时间',
                            name: 'applyTime',
                            clearable: true,
                            col: 4
                        }
                    ]
                },
            ],
            /**
             * 已审核工具栏
             */
            auditedToolbar: [
                {
                    type: 'search',
                    contents: [
                        {
                            type: 'text',
                            name: 'keyword',
                            placeholder: '关键字查询'
                        },
                    ]
                },
                {
                    type: 'filter',
                    contents: [
                        {
                            type: 'text',
                            label: '姓名',
                            placeholder: '请输入姓名',
                            name: 'xm',
                            clearable: true,
                            col: 4
                        },
                        {
                            type: 'select',
                            label: '业务类型',
                            placeholder: '请选择业务类型',
                            name: 'chargeMode',
                            event: 'multipled',
                            data: this.typeDicData,
							'label-key': 'name',
							'value-key': 'value',
                            clearable: true,
                            col: 4
                        },
                        {
                            type: 'select',
                            label: '认证来源',
                            placeholder: '请选择认证来源',
                            name: 'certifyFrom',
                            event: 'multipled',
                            sysCode: 'plat_person_cert_apply_certify_from',
                            'label-key': 'shortName',
                            'value-key': 'cciValue',
                            clearable: true,
                            col: 4
                        },
                        {
                            type: 'text',
                            label: '学号/职工号',
                            placeholder: '请输入学号/职工号',
                            name: 'xhZgh',
                            clearable: true,
                            col: 4
                        },
                        {
                            type: 'select',
                            label: '人员类型',
                            placeholder: '请选择人员类型',
                            name: 'zglx',
                            event: 'multipled',
                            sysCode: 'plat_person_type',
                            'label-key': 'shortName',
                            'value-key': 'cciValue',
                            clearable: true,
                            col: 4
                        },
                        {
                            type: 'date',
                            label: '申请时间',
                            placeholder: '请输入申请时间',
                            name: 'applyTime',
                            clearable: true,
                            col: 4
                        }
                    ]
                },
            ],
            thead: [],
            listThead: [
                {
                    title: '姓名',
                    align: 'left',
                    field: 'xm',
                    sortable: 'custom',
					showOverflowTooltip: true,
                },
                {
                    title: '学号/职工号',
                    width: "160px",
                    align: 'left',
                    field: 'xhZgh',
                    sortable: 'custom',
					showOverflowTooltip: true,
                },
                {
                    title: '姓名拼音',
                    width: "160px",
                    align: 'center',
                    field: 'xmpy',
                    sortable: 'custom',
					showOverflowTooltip: true,
                },
                {
                    title: '人员类型',
                    width: "160px",
                    align: 'center',
                    field: 'zglx',
                    sortable: 'custom',
					showOverflowTooltip: true,
                },
                {
                    title: '业务类型',
                    width: "160px",
                    align: 'center',
                    field: 'businessTypeName',
                    sortable: 'custom',
					showOverflowTooltip: true,
                },
                {
                    title: '认证来源',
                    width: "160px",
                    align: 'center',
                    field: 'certifyFrom',
                    sortable: 'custom',
					showOverflowTooltip: true,
                },
                {
                    title: '申请时间',
                    align: 'center',
                    field: 'applyTime',
                    sortable: 'custom',
					showOverflowTooltip: true,
                }
            ],
            /**
             * 待审核操作栏
             */
            waitAuditBtn: [
                {
                    title: '操作',
                    type: 'handle',
                    width: 180,
                    template: '',
                    events: [
                        {
                            code: 'audit',
                            text: '审核'
                        },
                        {
                            code: 'edit',
                            text: '编辑'
                        },
                        {
                            code: 'view',
                            text: '查看'
                        },
                        {
                            code: 'delete',
                            text: '删除'
                        },
                    ]
                }
            ],
            /**
             * 已审核操作栏
             */
            auditedBtn: [
                {
                    title: '操作',
                    type: 'handle',
                    width: 180,
                    template: '',
                    events: [
                        {
                            code: 'view',
                            text: '查看'
                        },
                    ]
                }
            ],
            pageOption: {
                layout: 'total, prev, pager, next, jumper',
                pageSize: 10,
                // hideOnSinglePage: true,
                position: 'right',
                current: 1,
                pageNum: 1
            },
            params: {
                asc: "false",
                orderBy: "t1.applyTime"
            },
        };
    },
    computed: {
        
    },
    watch: {
        dataTableUrl() {
            this.tableCount++;
        }
    },
    created() {
        this.$request({
            url: interfaceUrl.typeDic,
            method: 'get',
            async:true,//取消异步请求
        }).then(res => {
            if (res.rCode == 0) {
                this.typeDicData = res.results;
                this.handleSelect(this.activeMenus);
            }
        });
        
    },
    mounted() {

    },
    methods: {
        /**
         * 页签切换
         * @param {*} res 
         */
        handleSelect(res) {
            if ("0" == res) {
                this.getDictionary();
                this.toolbar = this.waitAuditToolbar;
                this.toolbar[2].contents[1].data = this.typeDicData;
                this.thead = this.listThead.concat(this.waitAuditBtn);
                this.queryStatus = [0];
                this.params["statusArray"] = this.queryStatus;

            } else if ("1" == res) {
                this.toolbar = this.auditedToolbar;
                this.thead = this.listThead.concat(this.auditedBtn);
                this.toolbar[1].contents[1].data = this.typeDicData;
                this.queryStatus = [2];
                this.params["statusArray"] = this.queryStatus;
            } else if ("2" == res) {
                this.toolbar = this.auditedToolbar;
                this.thead = this.listThead.concat(this.auditedBtn);
                this.toolbar[1].contents[1].data = this.typeDicData;
                this.queryStatus = [1];
                this.params["statusArray"] = this.queryStatus;
            }
            
            this.tableCount++;
        },
        hadeSubmit(data) {
            console.log("hadeSubmit", data);
        },
        deleteRow() {
            this.$request({
                url: interfaceUrl.deleteBatchIds,
                data: { ids: this.deleteId },
                method: 'POST'
            }).then(res => {
                if (res.rCode == 0) {
                    this.$refs.table.reload();
                    this.$message.success('删除成功');
                } else {
                    this.$message.error(res.msg);
                }
                this.showDelete = false;
            });
        },
        /**
         * 表格行高
         */
        tableRowClassName({row, rowIndex}) {
            let styleRes = {
                "height": "54px !important"
            }
            return styleRes
            
        },
        /**
         * 操作按钮点击事件
         * @param {*} res 
         */
        btnClick(res) {
            let code = res.handle.code;
            switch (code) {
                case 'add':
                    // 添加
                    this.visible = true;
                    this.addFlag = true;
                    this.auditFlag = false;
                    this.viewFlag = false;
                    this.dialogTitle = "新增";
                    this.selectInfo = "";
                    this.selectRow = null;
                    break;
                case 'edit':
                    // 编辑
                    this.visible = true;
                    this.addFlag = true;
                    this.auditFlag = false;
                    this.viewFlag = false;
                    this.dialogTitle = "编辑";
                    this.selectInfo = res.row.id;
                    this.selectRow = res.row;
                    break;
                case 'audit':
                    // 审核
                    this.visible = true;
                    this.addFlag = false;
                    this.auditFlag = true;
                    this.viewFlag = false;
                    this.dialogTitle = "审核";
                    // res.row.id
                    this.selectInfo = res.row.id;
                    this.selectRow = res.row;
                    break;
                case 'view':
                    // 查看
                    this.visible = true;
                    this.addFlag = false;
                    this.auditFlag = false;
                    this.viewFlag = true;
                    this.dialogTitle = "查看";
                    this.selectInfo = res.row.id;
                    this.selectRow = res.row;
                    break;
                case 'delete':
                    this.deleteId = res.row.id;
                    this.showDelete = true;
                    break;
                default:
                    break;
            }
        },
        /**
         * 排序变化事件
         * @param {*} column 
         * @param {*} prop 
         * @param {*} order 
         */
        sortChange(column, prop, order) {
            console.log(column);
            if (column.order == 'ascending') {//升序
                this.params = {
                    asc: "true",
                    orderBy: column.prop
                }

            } else if (column.order == 'descending') {//降序 
                this.params = {
                    asc: "false",
                    orderBy: column.prop
                }
            } else { //不排序
                this.params = {
                    asc: "false",
                    orderBy: "t1.applyTime"
                }
            }
            this.params["statusArray"] = this.queryStatus;
            this.$refs.table.reload()
        },
        dialogCancel(){
            this.visible = false;
            this.addFlag = false;
            this.auditFlag = false;
            this.viewFlag = false;
        },
        refreshData() {
            this.dialogCancel();
            this.$refs.table.reload()
		},
        //获取列表thead
        getListThead(btnJson){
            let tempThead = this.listThead;
            if(tempThead.length>7){
                tempThead.pop();
            }
            tempThead.push(btnJson);
            return tempThead;
        },
        /**
         * 获取下拉字典
         */
         getDictionary(){
             
         },
    }
};
</script>
<style scoped lang="scss">
@import '../../../assets/style/style.scss';

.content {
    display: flex;
    width: 100%;
    height: 100%;

    ::v-deep .es-data-table {
        flex: 1;
        display: flex;
        flex-direction: column;
        height: calc(100% - 58px);

        .es-data-table-content {
            flex: 1;
            height: 0;
            display: flex;
            flex-direction: column;

            .el-table {
                flex: 1;
                // height: 100% !important;
            }

            .es-thead-border {
                .el-table__header {
                    th {
                        border-right: 1px solid #E1E1E1;
                    }
                }
            }
        }
    }

    ::v-deep .el-form-item__label {
        background: none;
        border: 0px solid #c2c2c2;
    }
}

.el-dialog__body {
    .btn-box {
        margin-top: 20px;
        display: flex;
        justify-content: flex-end;

        .btn {
            padding: 5px 10px;
            color: #666;
            border: 1px solid #eee;
            cursor: pointer;
            margin-right: 5px;
            border-radius: 4px;
            // &.theme {
            // 	background: $--color-primary;
            // 	color: #fff;
            // 	border-color: $--color-primary;
            // }
        }
    }


}
</style>
