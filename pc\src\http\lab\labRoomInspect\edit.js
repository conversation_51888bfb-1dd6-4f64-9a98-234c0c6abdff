export default {
	computed: {
		formItemList() {
			return [
				{
					type: 'datetime',
					name: 'inspectDate',
					label: '日期',
					value: '',
					col: 12,
					rules: {
						required: true,
						message: '请输入日期'
					}
				},
				{
					name: 'roomName',
					label: '实验室名称',
					value: '',
					col: 12,
					rules: {
						required: true,
						message: '请输入实验室名称'
					}
				},
				{
					name: 'roomPersonLiable',
					label: '实验室负责人（实验员）',
					value: '',
					col: 12,
					rules: {
						required: true,
						message: '请输入实验室负责人（实验员）'
					}
				},

				{
					type: 'radio',
					name: 'isNormal',
					label: '是否正常',
					placeholder: '请选择是否正常',
					value: '',
					rules: {
						required: true,
						message: '请输入是否正常',
						trigger: 'change'
					},
					//verify: 'required',
					col: 5,
					sysCode: 'yes_or_no',
					'label-key': 'shortName',
					'value-key': 'cciValue'
				},
				{
					name: 'existProblem',
					label: '存在具体问题',
					value: '',
					col: 12,
					rules: {
						required: this.formData.isNormal == 1 ? false : true,
						message: '请输入存在具体问题'
					}
				},
				{
					label: '附件',
					type: 'attachment',
					code: 'lab_room_inspect_file',
					ownId: this.formData.id, // 业务id
					dragSort: true,
					limit: 8,
					col: 12
				}
			];
		}
	}
};
