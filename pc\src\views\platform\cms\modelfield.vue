<template>
	<basic-container>
		<avue-crud
			ref="crud"
			v-model="form"
			:option="option"
			:table-loading="loading"
			:data="data"
			:page="page"
			:before-open="beforeOpen"
			:cell-style="cellStyle"
			@row-update="rowUpdate"
			@row-save="rowSave"
			@row-del="rowDel"
			@search-change="searchChange"
			@search-reset="searchReset"
			@selection-change="selectionChange"
			@current-change="currentChange"
			@size-change="sizeChange"
			@refresh-change="refreshChange"
			@on-load="onLoad"
		>
			<template slot="menuLeft">
				<el-button size="medium" class="el-icon-back" @click="$router.back()">返回</el-button>
			</template>
		</avue-crud>
	</basic-container>
</template>

<script>
const defQuery = {
	modelId: ''
};
import modelFieldUrl from '@/http/platform/modelfield.js';
import nodeUrl from '@/http/platform/node.js';
import componentList from './component.js';
const dictUrl = '/blade-system/dict/dictList';

export default {
	name: 'ModelField',
	data() {
		//字段编码英文字母组成
		var validatecode = (rule, value, callback) => {
			let isletter2 = /^[a-zA-Z]+$/.test(value);
			if (!isletter2) {
				callback(new Error('字段编码必须是由字母组成，建议使用小驼峰命名规则'));
			} else {
				callback();
			}
		};
		//排序数字只能是非负整数
		var validateSortIndex = (rule, value, callback) => {
			let isletter2 = /^[1-9]\d*|0$/.test(value);
			if (!isletter2) {
				callback(new Error('排序数字只能为非负整数'));
			} else {
				callback();
			}
		};
		//跨越列数范围为1~12
		// var validateColspan = (rule, value, callback) => {
		//   let isletter2 = /^[1-9]\d*|0$/.test(value);
		//   if (!isletter2 || value > 24) {
		//     callback(new Error('跨越列数范围为1~24之间的整数'));
		//   } else {
		//     callback();
		//   }
		// };
		// 字典编号由数字、英文字母或者下划线组成
		// var validateDictCode = (rule, value, callback) => {
		//   if(!rule.required){
		//     callback();
		//   }
		//   let isletter2 = /^\w{1,20}$/.test(value);
		//   if (!isletter2) {
		//     callback(new Error('字典编号由数字、英文字母或者下划线组成'));
		//   } else {
		//     callback();
		//   }
		// };
		return {
			modelId: null,
			modelType: null,
			form: {},
			query: Object.assign({}, defQuery),
			loading: true,
			page: {
				pageSize: 10,
				currentPage: 1,
				total: 0
			},
			selectionList: [],
			data: [],
			option: {
				size: 'medium',
				height: 'auto',
				calcHeight: 'auto',
				menuWidth: 300,
				labelWidth: 120,
				align: 'center',
				searchShow: true,
				searchMenuSpan: 6,
				tip: false,
				border: true,
				viewBtn: true,
				columnBtn: false,
				selection: false,
				dialogClickModal: false,
				dialogMenuPosition: 'center',
				column: [
					{
						label: '字段名称',
						prop: 'name',
						span: 24,
						rules: [
							{
								required: true,
								message: '请输入字段名称',
								trigger: 'blur'
							}
						]
					},
					{
						label: '字段编码',
						prop: 'code',
						span: 24,
						rules: [
							{
								required: true,
								trigger: 'blur',
								validator: validatecode
							}
						]
					},
					{
						label: '类型',
						prop: 'inputType',
						span: 24,
						type: 'select',
						filterable: true,
						width: 200,
						dicData: componentList,
						rules: [
							{
								required: true,
								message: '请选择类型',
								trigger: 'blur'
							}
						]
					},
					{
						label: '内置类型',
						prop: 'sysType',
						span: 24,
						width: 100,
						type: 'select',
						dicData: [
							{
								value: 1,
								label: '内置字段'
							},
							{
								value: 2,
								label: '自定义字段'
							}
						],
						rules: [
							{
								required: true,
								message: '请输入内置类型',
								trigger: 'blur'
							}
						],
						hide: true
					},
					{
						label: '输入提示',
						prop: 'tips',
						span: 24,
						rules: [
							{
								required: false,
								message: '请输入输入提示',
								trigger: 'blur'
							}
						],
						hide: true
					},
					// {
					// 	label: '验证表达式',
					// 	prop: 'validates',
					// 	span: 24,
					// 	rules: [
					// 		{
					// 			required: false,
					// 			message: '请输入验证表达式',
					// 			trigger: 'blur'
					// 		}
					// 	],
					// 	hide: true
					// },
					{
						label: '文件类型限制',
						prop: 'accept',
						type: 'radio',
						span: 24,
						value: '*',
						dicData: [
							{
								value: '*',
								label: '所有'
							},
							{
								value: 'image/*',
								label: '图片'
							},
							{
								value: 'audio/*',
								label: '音频'
							},
							{
								value: 'video/*',
								label: '视频'
							}
						],
						rules: [
							{
								required: true,
								message: '请选择文件类型限制',
								trigger: 'blur'
							}
						],
						hide: true,
						display: false
					},
					{
						label: '默认数值',
						prop: 'defaultValue',
						span: 24,
						rules: [
							{
								required: false,
								message: '请输入默认值',
								trigger: 'blur'
							}
						],
						hide: true
					},
					{
						label: '',
						prop: 'dictCode',
						span: 24,
						type: 'select',
						dicUrl: '',
						props: {},
						rules: [
							{
								required: true,
								message: '',
								trigger: 'blur'
							}
						],
						dicFormatter: res => {
							return res.results;
						},
						hide: true,
						display: false
					},
					{
						label: '是否必填',
						prop: 'requireSet',
						type: 'radio',
						dicData: [
							{ label: '否', value: false },
							{ label: '是', value: true }
						],
						rules: [
							{
								required: true,
								message: '请选择是否必填',
								trigger: 'blur'
							}
						]
					},
					{
						label: '是否多值',
						prop: 'multiple',
						type: 'radio',
						dicData: [
							{ label: '否', value: false },
							{ label: '是', value: true }
						],
						rules: [
							{
								required: true,
								message: '请输入是否多值',
								trigger: 'blur'
							}
						],
						value: false,
						display: false
					},
					{
						label: '排列样式',
						prop: 'colspan',
						type: 'radio',
						dicData: [
							{
								label: '一行一列',
								value: 24
							},
							{
								label: '一行两列',
								value: 12
							}
						],
						rules: [
							{
								required: false,
								message: '请选择排列样式',
								trigger: 'blur'
							}
						],
						hide: true
					},
					{
						label: '排序',
						prop: 'sortIndex',
						type: 'number',
						minRows: 1,
						rules: [
							{
								required: true,
								validator: validateSortIndex,
								trigger: 'blur'
							}
						]
					},
					{
						label: '状态',
						prop: 'status',
						type: 'radio',
						dicData: [
							{
								label: '启用',
								value: 1
							},
							{
								label: '禁用',
								value: 0
							}
						],
						rules: [
							{
								required: true,
								message: '请选择状态',
								trigger: 'blur'
							}
						]
					}
				]
			}
		};
	},
	computed: {
		permissionList() {
			return {
				addBtn: true,
				viewBtn: true,
				delBtn: true,
				editBtn: true
			};
		},
		ids() {
			let ids = [];
			this.selectionList.forEach(ele => {
				ids.push(ele.id);
			});
			return ids.join(',');
		}
	},
	watch: {
		'form.inputType'() {
			let index_dictCode = this.getColIndex('dictCode');
			let index_multiple = this.getColIndex('multiple');
			let index_accept = this.getColIndex('accept');
			this.option.column[index_dictCode].display = false;
			this.option.column[index_multiple].display = false;
			this.option.column[index_accept].display = false;
			this.form.dictCode = null;
			this.form.multiple = false;
			if (
				this.form.inputType === 'select' ||
				this.form.inputType === 'checkbox' ||
				this.form.inputType === 'switch' ||
				this.form.inputType === 'radio'
			) {
				this.option.column[index_dictCode].label = '字典';
				this.option.column[index_dictCode].type = 'select';
				this.option.column[index_dictCode].props.label = 'dictValue';
				this.option.column[index_dictCode].props.value = 'code';
				this.option.column[index_dictCode].dicUrl = dictUrl;
				this.option.column[index_dictCode].rules[0].message = '请选择字典';
				this.option.column[index_dictCode].display = true;
			} else if (this.form.inputType === 'select-node' || this.form.inputType === 'select-info') {
				this.option.column[index_dictCode].label = '栏目';
				this.option.column[index_dictCode].type = 'tree';
				this.option.column[index_dictCode].props.label = 'name';
				this.option.column[index_dictCode].props.value = 'id';
				this.option.column[index_dictCode].dicUrl =
					(process.env.NODE_ENV === 'development' ? 'api' : '') + nodeUrl.getTreeNodeList;
				this.option.column[index_dictCode].rules[0].message = '请选择栏目';
				this.option.column[index_dictCode].display = true;
			}
			//当为输入框、下拉选择、栏目选择组件，才能设置 填写多值
			if (
				this.form.inputType === 'input' ||
				this.form.inputType === 'select' ||
				this.form.inputType === 'select-node' ||
				this.form.inputType === 'select-info'
			) {
				// if (this.modelType === 'info') {
				// 	this.option.column[index_multiple].display = true;
				// }
				this.option.column[index_multiple].display = true;
			}
			//当为文件上传时，限制上传文件类型
			if (this.form.inputType === 'upload-fileDrag' || this.form.inputType === 'upload-fileBtn') {
				this.option.column[index_accept].display = true;
			}
		}
	},
	created() {
		this.modelId = this.$route.query.modelId;
		this.modelType = this.$route.query.modelType;
		this.query.modelId = this.modelId;
	},
	methods: {
		cellStyle({ row, column }) {
			if (column.property === 'status') {
				if (row.status === 0) {
					return { color: 'red', 'text-align': 'center' };
				}
			}
		},
		getColIndex(prop) {
			for (let index in this.option.column) {
				if (this.option.column[index].prop === prop) {
					return index;
				}
			}
		},
		rowSave(row, done) {
			row.modelId = this.modelId;
			this.$request({
				url: modelFieldUrl.add,
				data: row,
				method: 'POST'
			}).then(res => {
				if (res.rCode === 0) {
					done();
					this.onLoad();
					this.$message({
						type: 'success',
						message: '操作成功!'
					});
				} else {
					this.$message.error(res.msg);
				}
			});
		},
		rowUpdate(row, index, done) {
			this.$request({
				url: modelFieldUrl.update,
				data: row,
				method: 'POST'
			}).then(res => {
				if (res.rCode === 0) {
					done();
					this.onLoad();
					this.$message({
						type: 'success',
						message: '操作成功!'
					});
				} else {
					this.$message.error(res.msg);
				}
			});
		},
		rowDel(row) {
			this.$confirm('确定将选择数据删除?', {
				confirmButtonText: '确定',
				cancelButtonText: '取消',
				type: 'warning'
			}).then(() => {
				this.$request({
					url: modelFieldUrl.remove,
					data: {
						ids: row.id
					},
					method: 'POST'
				}).then(res => {
					if (res.rCode === 0) {
						this.onLoad();
						this.$message({
							type: 'success',
							message: '操作成功!'
						});
					} else {
						this.$message.error(res.msg);
					}
				});
			});
		},
		handleDelete() {
			if (this.selectionList.length === 0) {
				this.$message.warning('请选择至少一条数据');
				return;
			}
			this.$confirm('确定将选择数据删除?', {
				confirmButtonText: '确定',
				cancelButtonText: '取消',
				type: 'warning'
			})
				.then(() => {
					return remove(this.ids);
				})
				.then(() => {
					this.onLoad();
					this.$message({
						type: 'success',
						message: '操作成功!'
					});
				});
		},
		beforeOpen(done, type) {
			if (['edit', 'view'].includes(type)) {
				this.$request({
					url: modelFieldUrl.detail,
					params: {
						id: this.form.id
					},
					method: 'GET'
				}).then(res => {
					if (res.rCode === 0) {
						this.form = res.results;
					} else {
						this.$message.error(res.msg);
					}
				});
			} else if (['add'].includes(type)) {
				this.$nextTick(() => {
					this.$set(this.form, 'sortIndex', this.page.total + 1);
				});
			}
			done();
		},
		searchReset() {
			this.query = Object.assign({}, defQuery);
			this.query.modelId = this.modelId;
			this.onLoad();
		},
		searchChange(params, done) {
			this.query = Object.assign(params, defQuery);
			this.query.modelId = this.modelId;
			this.page.currentPage = 1;
			this.onLoad();
			done();
		},
		selectionChange(list) {
			this.selectionList = list;
		},
		selectionClear() {
			this.selectionList = [];
		},
		currentChange(currentPage) {
			this.page.currentPage = currentPage;
		},
		sizeChange(pageSize) {
			this.page.pageSize = pageSize;
		},
		onLoad() {
			this.loading = true;
			let params = {
				pageNum: this.page.currentPage,
				pageSize: this.page.pageSize
			};
			params = Object.assign(params, this.query);
			this.$request({
				url: modelFieldUrl.page,
				params: params,
				method: 'GET'
			}).then(res => {
				if (res.rCode === 0) {
					const data = res.results;
					this.page.total = data.total;
					this.data = data.records;
					this.loading = false;
					this.selectionClear();
				}
			});
		},
		refreshChange() {
			this.onLoad();
		}
	}
};
</script>

<style></style>
