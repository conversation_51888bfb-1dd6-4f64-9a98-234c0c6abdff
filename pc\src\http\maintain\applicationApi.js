/**
 * 报修管理相关接口
 */
const interfaceUrl = {
	getOrgSelectList: '/ybzy/platmajor/getOrgSelectList', // 机构下拉框
	savaApplication: '/ybzy/hqrepairreport/save', // 新增申请
	updateApplication: '/ybzy/hqrepairreport/update', // 修改申请

	getApplicationInfo: '/ybzy/hqrepairreport', // 查看报修申请

	getHqRepairDispatchList: '/ybzy/hqrepairdispatch/listJson', //派单列表
	getReview: '/ybzy/hqrepairevaluate/getByReportId', //查看报修单评论
	saveReview: '/ybzy/hqrepairevaluate/save', //保存评论

	reportList: '/ybzy/hqrepairreport/reportList', // 查询用户报修申请列表
	listJson: '/ybzy/hqrepairreport/listJson', // 报修申请列表

	getRepairWorkerList: '/ybzy/hqrepairdispatch/queryRepairWorker', // 获取维修工人下拉列表

	savaRepairDispatch: '/ybzy/hqrepairdispatch/save', // 派单
	updateEsCompletionDate: '/ybzy/hqrepairdispatch/updateEsCompletionDate', // 修改派单的预计维修完成时间
	cancelDispatch: '/ybzy/hqrepairdispatch/cancelDispatch', // 撤销派单

	getlistOfReview: '/ybzy/hqrepairevaluate/listJson', //获取评论列表
	delReviewById: '/ybzy/hqrepairevaluate/deleteById', //删除评论

	//维修人员管理
	getListOfMp: '/ybzy/hqMp/listJson', //获取维修员列表
	selectMpList: '/ybzy/hqMp/selectList', //获取维修员下拉选择列表
	getMpInfoById: '/ybzy/hqMp/info', //根据id查询维修员详情
	saveMp: '/ybzy/hqMp/save', //保存维修员
	saveCustomIn: '/ybzy/hqMp/addInMaintenance', //保存自定义内部维修员
	saveInMp: '/ybzy/hqMp/saveIn', //批量保存内部维修员
	updataMp: '/ybzy/hqMp/update', //修改维修员
	deleteMpById: '/ybzy/hqMp/deleteById', //删除维修员
	statisticsPersonWork: '/ybzy/hqMp/statisticsPersonWork', //分页查询维修人员工作统计情况
	//内部维修员接口
	delMpById: '/sys/sys/sysUserRole/delete.dhtml', //解绑内部维修员
	getDataList: '/ybzy/hqMp/personList', //获取基建处所有人员
	roleToUsers: '/sys/sys/sysUserRole/bindRoleToUsers.dhtml', //绑定维修人员

	//统计报修申请数据
	getStatisticsData: '/ybzy/hqrepairreport/statistics',
	//报修统计分析(查询条件报修类别、报修地点、报修时间【年份查询】)
	statistics: '/ybzy/hqrepairreport/statistics',
	//获取报修地点下拉列表
	getRepairAddressList: '/ybzy/tzhddBuildingFacilities/getParents',

	// ------------- 楼栋房间管理-------------------
	getBuildingSelectList: '/ybzy/hqBuildRoom/getSelectList', //获取楼栋下拉列表
	getBuildingParentList: '/ybzy/hqBuildRoom/getParentList', //获取楼栋父级下拉列表
	getBuildingTreeList: '/ybzy/hqBuildRoom/getTreeList', //学校楼栋信息树状数据接口
	buildListJson: '/ybzy/hqBuildRoom/listJson', //获取楼层分页列表
	getBuildInfo: '/ybzy/hqBuildRoom', //获取详情
	buildSave: '/ybzy/hqBuildRoom/save', //保存
	buildUpdate: '/ybzy/hqBuildRoom/update', //修改
	buildUpdateStates: '/ybzy/hqBuildRoom/updateStates', //修改
	buildDelById:'/ybzy/hqBuildRoom/deleteById', //删除

	// ------------- 楼栋管理员接口-------------------
	buildManagerJson: '/ybzy/hqBuildManager/listJson', //获取楼栋管理员分页列表
	buildManagerInfo: '/ybzy/hqBuildManager', //获取楼栋管理员详情
	buildManagerSave: '/ybzy/hqBuildManager/save', //保存楼栋管理员
	buildManagerUpdate: '/ybzy/hqBuildManager/update', //编辑楼栋管理员
	buildManagerDel: '/ybzy/hqBuildManager/deleteById', //删除楼栋管理员

	// ------------- 维修员值班表接口-------------------
	dutyListJson: '/ybzy/hqMaintenanceDutyRoster/listJson', //分页列表
	dutyInfo: '/ybzy/hqMaintenanceDutyRoster/info', //详情
	dutySave: '/ybzy/hqMaintenanceDutyRoster/save', //保存
	dutyUpdate: '/ybzy/hqMaintenanceDutyRoster/update', //编辑
	dutyDel: '/ybzy/hqMaintenanceDutyRoster/deleteById' //删除
};
export default interfaceUrl;
