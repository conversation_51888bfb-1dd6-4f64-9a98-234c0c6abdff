<template>
	<div class="main">
		<!--  内容区域  -->
		<div class="main-center">
			<div class="box1">
				<div class="title">
					<img class="title-img" src="@ast/images/systemOther/data-icon5.png" alt="" />
					<span>一级文件类型分析</span>
				</div>
				<div id="myEchart1" class="chart-con"></div>
			</div>
			<div class="box2">
				<div class="title">
					<img class="title-img" src="@ast/images/systemOther/data-icon5.png" alt="" />
					<span>文件类型占比</span>
				</div>
				<div id="myEchart2" class="chart-con"></div>
			</div>

			<div class="box3">
				<div class="title">
					<img class="title-img" src="@ast/images/systemOther/data-icon5.png" alt="" />
					<span>资源统计</span>
				</div>
				<div class="statisticsBox">
					<div class="items">
						<img :src="require('@/assets/images/systemOther/data-icon2.png')" alt="" />
						<div class="content">
							<span>总资源数</span>
							<p>
								{{ statisticsDatas.fileCount }}
								<span>条</span>
							</p>
						</div>
					</div>

					<div class="items">
						<img :src="require('@/assets/images/systemOther/data-icon1.png')" alt="" />
						<div class="content">
							<span>总下载量</span>
							<p>
								{{ statisticsDatas.downCount }}

								<span>条</span>
							</p>
						</div>
					</div>

					<div class="items">
						<img :src="require('@/assets/images/systemOther/data-icon4.png')" alt="" />
						<div class="content">
							<span>总收藏量</span>
							<p>
								{{ statisticsDatas.collectCount }}
								<span>条</span>
							</p>
						</div>
					</div>
				</div>
			</div>
			<div class="box4">
				<div class="title">
					<img class="title-img" src="@ast/images/systemOther/data-icon5.png" alt="" />
					<span>12个月上传汇总</span>
				</div>
				<div id="myEchart3" class="chart-con"></div>
			</div>
			<!--  -->

			<div class="box6">
				<div class="title">
					<img class="title-img" src="@ast/images/systemOther/data-icon5.png" alt="" />
					<span>二级学院上传汇总</span>
					<!-- <i class="el-icon-refresh icon"></i> -->
				</div>
				<div id="myEchart4" class="chart-con"></div>
			</div>
		</div>
	</div>
</template>
<script>
import api from '@/http/dataManage/data-manager';

export default {
	name: 'AttendanceManagementPersonnel',
	data() {
		return {
			statisticsDatas: {
				fileCount: 0,
				collectCount: 0,
				downCount: 0
			},
			myChart: []
		};
	},
	mounted() {
		this.getData();
		window.addEventListener('resize', this.onResize);
	},
	beforeDestroy() {
		window.removeEventListener('resize', this.onResize);
		this.myChart.forEach(item => {
			if (item) {
				item.dispose();
			}
		});
	},
	methods: {
		getData() {
			let { orgName, orgId } = JSON.parse(localStorage.getItem('loginUserInfo'));
			let param = {
				orgId,
				college_name: orgName
			};
			this.$request({
				url: api.getIndexCount,
				data: param,
				method: 'GET'
			}).then(res => {
				if (res.rCode === 0) {
					let { fileCount, collectCount, downCount, firstMap, fileTypeMap, collegeMap, monthMap } =
						res.results;

					this.statisticsDatas = {
						fileCount,
						collectCount,
						downCount
					};

					this.$nextTick(() => {
						this.toolChart1('myEchart1', 0, firstMap);
						this.toolChart2('myEchart2', 1, fileTypeMap);
						this.toolChart3('myEchart3', 3, monthMap);
						this.toolChart4('myEchart4', 5, collegeMap);
					});
				} else {
					this.$message.error(res.msg);
				}
			});
		},

		onResize() {
			this.myChart.forEach(item => {
				if (item) {
					item.resize();
				}
			});
		},
		toolChart1(dom, i, data) {
			const pieData = Object.entries(data).map(([name, value]) => ({
				name: name,
				value: parseFloat(value) // 如果需要数值类型
			}));

			let chartDom = document.getElementById(dom);
			if (this.myChart[i]) {
				this.myChart[i].clear();
			} else {
				this.myChart[i] = this.$echarts.init(chartDom);
			}
			const option = {
				tooltip: {
					trigger: 'item',
					formatter: '{b}: {c}个)'
				},
				legend: {
					orient: 'vertical', // 设置图例为竖直方向
					icon: 'circle',
					y: 'bottom', // 设置图例在y轴上的位置为底部
					right: 0 // 设置图例在x轴上的位置为右边
				},
				series: [
					{
						name: '文件详情',
						type: 'pie',
						radius: [0, 60],
						center: ['30%', '50%'],
						roseType: 'area',
						data: pieData
					}
				]
			};

			option && this.myChart[i].setOption(option);
		},
		toolChart2(dom, i, data) {
			const xData = Object.keys(data); // 获取所有的键
			const yData = Object.values(data).map(value => parseInt(value));

			let chartDom = document.getElementById(dom);
			if (this.myChart[i]) {
				this.myChart[i].clear();
			} else {
				this.myChart[i] = this.$echarts.init(chartDom);
			}
			// const xData = ['图片', '文档', '视频', '音频', '软件', '其他'];
			// const yData = [298, 263, 500, 300, 360, 180];

			const styleData = { title: '就业率年度分析', color: ['#0076e8', '#ebf3fb'] };

			const option = {
				tooltip: {
					trigger: 'axis'
					// axisPointer: {
					// 	type: 'shadow'
					// }
				},

				grid: {
					left: '9.17%',
					top: '10%',
					bottom: '8%',
					right: '4.95%'
				},
				xAxis: {
					data: xData,
					axisTick: {
						show: false
					},
					axisLine: {
						lineStyle: {
							color: 'rgba(255, 129, 109, 0.1)',
							width: 1 //这里是为了突出显示加上的
						}
					},
					axisLabel: {
						//
						textStyle: {
							color: '#999'
							// fontSize: 14
						}
					}
				},
				yAxis: [
					{
						splitNumber: 2,
						axisTick: {
							show: false
						},
						axisLine: {
							lineStyle: {
								color: 'rgba(255, 129, 109, 0.1)',
								width: 1 //这里是为了突出显示加上的
							}
						},
						axisLabel: {
							textStyle: {
								color: '#8997A5',
								fontSize: 14
							},
							formatter: function (value) {
								return value;
							}
						},
						splitArea: {
							areaStyle: {
								color: 'rgba(255,255,255,.5)'
							}
						},
						splitLine: {
							//刻度线
							show: true,
							lineStyle: {
								color: '#B2C3DA',
								type: [3, 3],
								dashOffset: 2
							}
						}
					}
				],
				series: [
					{
						name: '文件数',
						// 柱状图
						type: 'bar',
						// 柱子宽度
						barWidth: 20,
						// barCategoryGap: '0%', //柱间距离
						symbol: 'path://M0,10 L10,10 C5.5,10 5.5,5 5,0 C4.5,5 4.5,10 0,10 z',
						itemStyle: {
							normal: {
								color: {
									type: 'linear',
									x: 0,
									y: 0,
									x2: 0,
									y2: 1,
									colorStops: [
										{
											offset: 0,
											color: styleData.color[0] //  0%  处的颜色
										},
										{
											offset: 1,
											color: styleData.color[0] //  100%  处的颜色
										}
									],
									global: false //  缺省为  false
								}
							},
							emphasis: {
								opacity: 1
							}
						},
						data: yData,
						z: 10
					}
				]
			};

			option && this.myChart[i].setOption(option);
		},
		toolChart3(dom, i, data) {
			const xData = Object.keys(data); // 获取所有的键
			const yData = Object.values(data).map(value => parseInt(value));

			let chartDom = document.getElementById(dom);
			if (this.myChart[i]) {
				this.myChart[i].clear();
			} else {
				this.myChart[i] = this.$echarts.init(chartDom);
			}

			let option = {
				tooltip: {
					trigger: 'axis'
				},
				grid: {
					left: '5%',
					top: '10%',
					bottom: '8%',
					right: '5%'
				},
				xAxis: [
					{
						type: 'category',
						data: xData,
						axisLine: {
							lineStyle: {
								color: '#999'
							}
						}
					}
				],
				yAxis: [
					{
						type: 'value',
						splitNumber: 4,
						splitLine: {
							lineStyle: {
								type: 'dashed',
								color: '#DDD'
							}
						},
						axisLine: {
							show: false,
							lineStyle: {
								color: '#333'
							}
						},
						nameTextStyle: {
							color: '#999'
						},
						splitArea: {
							show: false
						}
					}
				],
				series: [
					{
						name: '文件数',
						type: 'line',
						data: yData,
						lineStyle: {
							normal: {
								width: 8,
								color: {
									type: 'linear',

									colorStops: [
										{
											offset: 0,
											color: '#0076e8' // 0% 处的颜色
										},
										{
											offset: 1,
											color: '#0076e8' // 100% 处的颜色
										}
									],
									globalCoord: false // 缺省为 false
								}
								// shadowColor: '#0076e8',
								// shadowBlur: 10,
								// shadowOffsetY: 20
							}
						},
						itemStyle: {
							normal: {
								color: '#fff',
								borderWidth: 10,
								borderColor: '#A9F387'
							}
						},
						smooth: true
					}
				]
			};

			option && this.myChart[i].setOption(option);
		},
		toolChart4(dom, i, data) {
			const xData = Object.keys(data); // 获取所有的键
			const yData = Object.values(data).map(value => parseInt(value));

			let chartDom = document.getElementById(dom);
			if (this.myChart[i]) {
				this.myChart[i].clear();
			} else {
				this.myChart[i] = this.$echarts.init(chartDom);
			}

			const styleData = { title: '就业率年度分析', color: ['#0076e8', '#ebf3fb'] };

			const option = {
				tooltip: {
					trigger: 'axis'
					// axisPointer: {
					// 	type: 'shadow'
					// }
				},

				grid: {
					left: '5%',
					top: '10%',
					bottom: '8%',
					right: '4.95%'
				},
				xAxis: {
					data: xData,
					axisTick: {
						show: false
					},
					axisLine: {
						lineStyle: {
							color: 'rgba(255, 129, 109, 0.1)',
							width: 1 //这里是为了突出显示加上的
						}
					},
					axisLabel: {
						//
						textStyle: {
							color: '#999'
							// fontSize: 14
						}
					}
				},
				yAxis: [
					{
						splitNumber: 2,
						axisTick: {
							show: false
						},
						axisLine: {
							lineStyle: {
								color: 'rgba(255, 129, 109, 0.1)',
								width: 1 //这里是为了突出显示加上的
							}
						},
						axisLabel: {
							textStyle: {
								color: '#8997A5',
								fontSize: 14
							},
							formatter: function (value) {
								return value;
							}
						},
						splitArea: {
							areaStyle: {
								color: 'rgba(255,255,255,.5)'
							}
						},
						splitLine: {
							//刻度线
							show: true,
							lineStyle: {
								color: '#B2C3DA',
								type: [3, 3],
								dashOffset: 2
							}
						}
					}
				],
				series: [
					{
						name: '文件数',
						// 柱状图
						type: 'bar',
						// 柱子宽度
						barWidth: 20,
						// barCategoryGap: '0%', //柱间距离
						symbol: 'path://M0,10 L10,10 C5.5,10 5.5,5 5,0 C4.5,5 4.5,10 0,10 z',
						itemStyle: {
							normal: {
								color: {
									type: 'linear',
									x: 0,
									y: 0,
									x2: 0,
									y2: 1,
									colorStops: [
										{
											offset: 0,
											color: styleData.color[0] //  0%  处的颜色
										},
										{
											offset: 1,
											color: styleData.color[0] //  100%  处的颜色
										}
									],
									global: false //  缺省为  false
								}
							},
							emphasis: {
								opacity: 1
							}
						},
						data: yData,
						z: 10
					}
				]
			};

			option && this.myChart[i].setOption(option);
		},

		/**年月切换下拉选择*/
		handleChange(val) {
			console.log(val);
		}
	}
};
</script>

<style lang="scss" scoped>
@import '@ast/style/public.scss';
.main {
	height: 100%;
	width: 100%;
	background: #f0f2f5;
	padding: 12px;
	overflow-y: scroll;
	.banner {
		width: 100%;
		border-radius: 6px;
		height: 110px;
		background: url(~@ast/images/systemOther/data-banner.png);
		margin-bottom: 12px;
	}
	/**头部*/
	&-header {
		::v-deep .select {
			border: none;
			margin-right: 8px;
			.el-input__inner {
				width: 160px;
				border: none;
			}
		}
	}
	/**中间部分*/
	&-center {
		display: flex;
		flex-wrap: wrap;
		justify-content: space-between;
		.box1,
		.box2,
		.box3,
		.box4,
		.box5,
		.box6 {
			flex-basis: 49%;
			background: #fff;
			padding: 6px 0;
			border-radius: 6px;
			font-size: 16px;
			margin-bottom: 12px;
			height: 280px;
			.chart-con {
				width: 100%;
				height: calc(100% - 36px);
			}
			.list {
				// width: 100%;
				height: 100%;
				padding: 20px 15px;
				.list-item {
					// width: 100%;
					display: flex;
					flex-direction: column;
					font-size: 12px;
					margin-bottom: 16px;
					color: #949faf;
					.floor1 {
						margin-bottom: 5px;
						.order {
							font-size: 13px;
							font-weight: bold;
							margin-right: 5px;
							font-style: italic;
							display: inline-block;
							line-height: 1;
						}
						.floor1-text,
						.floor1-text2 {
							overflow: hidden;
							text-overflow: ellipsis;
							white-space: nowrap;
							display: inline-block;
							line-height: 1;
						}
						.floor1-text {
							max-width: 300px;
						}
						.floor1-text2 {
							float: right;
						}
					}
					&:nth-child(1) {
						.order {
							color: #0b7ce9;
						}
					}
					&:nth-child(2) {
						.order {
							color: #40a9ff;
						}
					}
					&:nth-child(3) {
						.order {
							color: #2dba4f;
						}
					}
				}
			}

			.statisticsBox {
				width: 95%;
				height: 80%;
				display: flex;
				.items {
					@include flexBox(flex-start);
					flex: 1;
					padding: 20px;
					border-radius: 8px;
					background: #fff;
					img {
						width: 50px;
						height: 50px;
					}
					.content {
						margin-left: 16px;
						p {
							font-size: 22px;
							font-weight: 550;
						}
						span {
							font-size: 16px;
							font-weight: 500;
							color: #999;
						}
					}
				}
			}

			.NoticList {
				margin-top: 20px;
				height: 80%;
				display: flex;
				flex-direction: column;
				align-items: center;
				overflow: scroll;
				.items {
					width: 80%;
					text-align: center;
					padding: 6px 0;
					margin: 5px 0;
					// background: #0b7ce9;
				}
			}
			.NoticList::-webkit-scrollbar {
				display: none !important;
			}
		}
	
		.box6 {
			flex-basis: 100%;
		}
	}
	/**穿透修改卡片组件*/
	.card-box {
		@include flexBox(space-between);
		width: 100%;
		margin: 12px 0;
		background: #f0f2f5;
	}
}
.title {
	position: relative;
	display: flex;
	align-items: center;
	font-size: 16px;
	font-weight: 550;
	padding: 0 20px 6px;
	border-bottom: 1px solid rgba(241, 244, 247, 1);
	&-img {
		width: 22px;
		height: 22px;
		margin-right: 8px;
	}
	.top-badge {
		position: relative;
		display: inline-block;
		background-color: #fceee1;
		color: #ee9a55;
		font-weight: bold;
		padding: 0 5px;
		border-radius: 4px;
		font-size: 12px;
		font-style: italic;
		position: relative;
		margin-left: 8px;
		margin-top: 2px;
		line-height: 1.3;

		&::before {
			// 右边小三角形
			content: '';
			position: absolute;
			width: 0;
			height: 0;
			border-top: 5px solid transparent;
			border-bottom: 5px solid transparent;
			border-right: 7px solid #fceee1;
			top: 3px;
			left: -4.8px;

			z-index: 1;
		}
	}

	.icon {
		position: absolute;
		top: 3px;
		right: 10px;
		color: #707070;
	}
}
</style>
