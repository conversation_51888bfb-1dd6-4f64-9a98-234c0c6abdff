<template>
	<div class="content">
		<es-data-table ref="table" :key="tableCount" :row-style="tableRowClassName" :full="true" :fit="true"
			:thead="thead" :toolbar="toolbar" :border="true" :page="pageOption" :url="dataTableUrl" 
			:param="params" close @btnClick="btnClick" @sort-change="sortChange"></es-data-table>
		<es-dialog :title="formTitle" :visible.sync="showForm" width="80%" height="80%" :close-on-click-modal="false"
			:destroy-on-close="true">
			<memberView :info="formData"></memberView>
		</es-dialog>
		<es-dialog title="删除" :visible.sync="showDelete" width="20%" :close-on-click-modal="false" :middle="true"
			height="140px">
			<div>确定要删除该条数据吗</div>
			<div class="btn-box">
				<div class="btn theme" @click="deleteRow">确定</div>
				<div class="btn" @click="showDelete = false">取消</div>
			</div>
		</es-dialog>
	</div>
	
</template>

<script>
import interfaceUrl from '@/http/alumna/member.js';
import SnowflakeId from 'snowflake-id';
import memberView from '@/views/alumna/member/components/view.vue';
import { host } from '../../../../config/config';


export default {
	components: { memberView },
	data() {
		return {
			dataTableUrl: interfaceUrl.listJson,
			tableCount: 1,
			showForm: false,
			showDelete: false,
			ownId: null, //数据行Id
			validityOfDateDisable: false,
			params: {
				orderBy: 'createTime',
				asc: 'false',
				isAudit: 1
			},
			toolbar: [
				{
					type: 'button',
					group: true,
					length: 5,
					contents: [
						{
							text: '导入',
							upload: true,
							action: interfaceUrl.importExcel,
						},
						{
							text: '导出',
							exportXls: true,
							event: () => {
								window.open(host + interfaceUrl.exportTabels);
							}
						},
						{
							text: '下载导入模板',
							exportXls: true,
							event: () => {
								window.open(host + interfaceUrl.exportTemplate);
							}
						},
					]
				},
				{
					type: 'search',
					contents: [{ type: 'text', name: 'keyword', placeholder: '关键字查询' }]
				}
			],
			thead: [
				{
					title: '校友名字',
					align: 'left',
					field: 'memberName',
					showOverflowTooltip: true
				},
				{
					title: '联系方式',
					align: 'left',
					field: 'phoneNum'
				},
				{
					title: '身份',
					align: 'center',
					field: 'identityType'
				},
				{
					title: '学院',
					align: 'left',
					field: 'whereSchool'
				},
				{
					title: '部门',
					align: 'left',
					field: 'whereDept'
				},
				{
					title: '专业',
					align: 'left',
					field: 'whereMajor'
				},
				{
					title: '操作',
					type: 'handle',
					width: 180,
					template: '',
					events: [{ code: 'view', text: '查看' }]
				}
			],
			pageOption: {
				pageSize: 10,
				position: 'center',
				current: 1,
				pageNum: 1
			},
			formData: {},
			formTitle: '编辑'
		};
	},
	watch: {
		dataTableUrl() {
			this.tableCount++;
		}
	},
	methods: {

		//表格行高
		tableRowClassName({ row, rowIndex }) {
			let styleRes = { height: '54px !important' };
			return styleRes;
		},
		inputChange(key, value) { },
		//操作按钮点击事件
		btnClick(res) {
			let code = res.handle.code;
			switch (code) {
				case 'add':
					this.formTitle = '新增';
					const snowflake = new SnowflakeId();
					this.formData = { id: snowflake.generate(), status: true };
					this.showForm = true;
					break;
				case 'edit':
					this.formTitle = '编辑';
					this.ownId = res.row.id;
					this.$request({ url: interfaceUrl.info + '/' + res.row.id, method: 'GET' }).then(res => {
						if (res.rCode === 0) {
							this.formData = res.results;
							this.showForm = true;
						}
					});
					break;
				case 'view':
					this.formTitle = '查看';
					this.ownId = res.row.id;
					this.$request({ url: interfaceUrl.info + '/' + res.row.id, method: 'GET' }).then(res => {
						if (res.rCode === 0) {
							this.formData = res.results;
							this.showForm = true;
						}
					});
					break;
				case 'delete':
					this.deleteId = res.row.id;
					this.showDelete = true;
					break;
				default:
					break;
			}
		},
		//删除行
		deleteRow() {
			this.$request({
				url: interfaceUrl.deleteBatchIds,
				data: { ids: this.deleteId },
				method: 'POST'
			}).then(res => {
				if (res.rCode === 0) {
					this.$refs.table.reload();
					this.$message.success('删除成功');
				} else {
					this.$message.error(res.msg);
				}
				this.showDelete = false;
			});
		},
		//排序变化事件
		sortChange(column, prop, order) {
			let asc = this.params.asc;
			let orderBy = this.params.orderBy;
			if (column.order === 'ascending') {
				//升序
				asc = 'true';
				orderBy = column.prop;
			} else if (column.order === 'descending') {
				//降序
				asc = 'false';
				orderBy = column.prop;
			} else {
				//不排序
				asc = 'false';
				orderBy = column.prop;
			}
			this.params.asc = asc;
			this.params.orderBy = orderBy;
			this.$refs.table.reload();
		},
		refreshData() {
			this.$refs.table.reload();
		}
	}
};
</script>

<style scoped lang="scss">
@import '../../../assets/style/style.scss';

.content {
	display: flex;
	width: 100%;
	height: 95%;
	::v-deep .es-data-table {
		width: 100%;
		// flex: 1;
		// display: flex;
		flex-direction: column;
		height: calc(100% - 128px);

		.es-data-table-content {
			// flex: 1;
			height: 0;
			// display: flex;
			flex-direction: column;

			.el-table {
				flex: 1;
				// height: 100% !important;
			}

			.es-thead-border {
				.el-table__header {
					th {
						border-right: 1px solid #e1e1e1;
					}
				}
			}
		}
	}

	::v-deep .el-form-item__label {
		background: none;
		border: 0px solid #c2c2c2;
	}
}

.el-dialog__body {
	.btn-box {
		margin-top: 20px;
		display: flex;
		justify-content: flex-end;

		.btn {
			padding: 5px 10px;
			color: #666;
			border: 1px solid #eee;
			cursor: pointer;
			margin-right: 5px;
			border-radius: 4px;
			// &.theme {
			// 	background: $--color-primary;
			// 	color: #fff;
			// 	border-color: $--color-primary;
			// }
		}
	}
}
</style>
