/**
 * 社团相关接口
 */
const interfaceUrl = {
	societyBaseInfoAuditListJson: '/ybzy/society/listJson', // 列表
	societyBaseInfoAuditInfo: '/ybzy/society', // 获取
	societyBaseInfoAuditAudit: '/ybzy/society/audit', // 审核
	societyBaseInfoAuditDeleteIds: '/ybzy/society/deleteBatchIds', // 删除
	societyTypeSelectList: '/ybzy/societyType/selectList', // 社团类型下拉框
	teacherSelectList: '/ybzy/platperson/front/getTeacherSelectList', // 教师下拉框
	collegeSelectList: '/ybzy/platmajor/getCollegeSelectList', //学院下拉框
	majorSelectList: '/ybzy/platmajor/getMajorSelectList', // 专业下拉框
};
export default interfaceUrl;
