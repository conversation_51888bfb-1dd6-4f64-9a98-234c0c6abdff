<template>
	<div class="content">
		<es-data-table
			ref="table"
			:key="changeFlag"
			:row-style="tableRowClassName"
			:full="true"
			:fit="true"
			:thead="thead"
			:toolbar="toolbar"
			:border="true"
			:page="pageOption"
			:url="dataTableUrl"
			:numbers="true"
			:param="params"
			@btnClick="btnClick"
			@search="hadeSubmit"
			@reset="resetTable"
			close
			form
		></es-data-table>
	</div>
</template>

<script>
import interfaceUrl from '@/http/logistics/receiptOrder.js';

import supplierApi from '@/http/logistics/supplier.js';
import storeroomApi from '@/http/logistics/hqStoeroom.js';

export default {
	data() {
		return {
			dataTableUrl: interfaceUrl.statisticsPageList,

			supplierOptions: [], //供应商选择列表
			storeroomOptions: [], //库房选择列表

			thead: [
				{
					title: '原料名称',
					field: 'name',
					align: 'center',
					minWidth: 70,
					showOverflowTooltip: true
				},
				{
					title: '原料编号',
					field: 'code',
					align: 'center',
					showOverflowTooltip: true
				},
				// {
				// 	title: '原料分类',
				// 	field: 'categoryName',
				// 	align: 'center',
				// 	showOverflowTooltip: true
				// },
				{
					title: '供应商',
					field: 'brand',
					align: 'center',
					minWidth: 100,
					showOverflowTooltip: true
				},
				{
					title: '单位',
					field: 'unit',
					width: 80,
					align: 'center'
				},
				{
					title: '单价',
					field: 'price',
					align: 'center'
				},
				{
					title: '库房',
					field: 'storeroomName',
					align: 'center',
					showOverflowTooltip: true
				},
				{
					title: '入库量',
					field: 'num',
					align: 'center'
				},
				{
					title: '总价',
					field: 'totalPrice',
					align: 'center'
				},
				{
					title: '入库时间',
					field: 'receiptTime',
					align: 'center',
					showOverflowTooltip: true
				}
			],
			pageOption: {
				pageSize: 20,
				totalCount: 0
			},
			params: {},
			submitFilterParams: {}
		};
	},
	computed: {
		toolbar() {
			return [
				{
					type: 'button',
					contents: [
						{
							text: '导出',
							code: 'export',
							type: 'primary'
						}
					]
				},
				{
					type: 'search',
					reset: true,
					contents: [
						{
							type: 'select',
							name: 'supplierId',
							placeholder: '供应商',
							data: this.supplierOptions,
							'label-key': 'name',
							'value-key': 'id',
							clearable: true,
							col: 4
						},
						{
							type: 'select',
							name: 'storeroomId',
							placeholder: '库房',
							data: this.storeroomOptions,
							'label-key': 'name',
							'value-key': 'id',
							clearable: true,
							col: 4
						},
						{
							type: 'daterange',
							name: 'receiptTimeDes',
							default: true,
							placeholder: '日期',
							clearable: true
						},
						{
							type: 'text',
							name: 'keyword',
							placeholder: '原料名称/编号',
							col: 6
						}
					]
				}
			];
		}
	},
	watch: {},
	created() {
		this.supplierList();
		this.storeroomList();
	},
	mounted() {},
	methods: {
		/**
		 * 表格行高
		 */
		tableRowClassName({ row, rowIndex }) {
			let styleRes = {
				height: '54px !important'
			};
			return styleRes;
		},
		supplierList() {
			this.$request({
				url: supplierApi.listAll,
				method: 'GET'
			}).then(res => {
				if (res.rCode === 0) {
					this.supplierOptions = res.results;
				}
			});
		},
		storeroomList() {
			this.$request({
				url: storeroomApi.listAll,
				method: 'GET'
			}).then(res => {
				if (res.rCode === 0) {
					this.storeroomOptions = res.results;
				}
			});
		},
		hadeSubmit(e) {
			this.submitFilterParams = e;
		},
		/**
		 * 操作按钮点击事件
		 * @param {*} res
		 */
		btnClick(res) {
			let code = res.handle.code;
			switch (code) {
				//导出
				case 'export':
					this.exportFile();
					break;
				default:
					break;
			}
		},
		//导出函数
		exportFile() {
			let isDev = process.env.NODE_ENV === 'development' ? '/api' : '';
			const paramAll = { ...this.params, ...this.submitFilterParams };
			let url = `${isDev}/ybzy/hqreceiptorder/exportStatisticsData`;
			let urlParams = this.objToUrlParams(paramAll);
			if (urlParams) {
				url += '?' + urlParams;
			}
			window.open(url, '_self');
		},
		objToUrlParams(obj) {
			return Object.keys(obj)
				.filter(key => obj[key] !== '' && obj[key] !== null)
				.map(key => encodeURIComponent(key) + '=' + encodeURIComponent(obj[key]))
				.join('&');
		},
		resetTable() {
			this.submitFilterParams = {};
			this.$refs.table.resetTable();
		}
	}
};
</script>
<style scoped lang="scss">
@import '../../../assets/style/style.scss';

.content {
	display: flex;
	width: 100%;
	height: 100%;

	::v-deep .es-data-table {
		width: 100%;
		.es-data-table-content {
			.el-table {
				flex: 1;
			}

			.es-thead-border {
				.el-table__header {
					th {
						border-right: 1px solid #e1e1e1;
					}
				}
			}
		}
	}

	::v-deep .el-form-item__label {
		background: none;
		border: 0px solid #c2c2c2;
	}
}
</style>
