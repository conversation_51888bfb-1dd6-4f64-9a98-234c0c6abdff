<template>
	<div class="time-select">
		<el-time-select
			v-model="startTime"
			placeholder="起始时间"
			:readonly="readonly"
			:picker-options="{
				start: defaultTime.start,
				step: '00:20',
				end: defaultTime.end
			}"
			@change="endTime = ''"
		></el-time-select>
		~
		<el-time-select
			v-model="endTime"
			placeholder="结束时间"
			:readonly="readonly"
			:disabled="!startTime"
			:picker-options="{
				start: startTime,
				step: '00:20',
				end: defaultTime.end,
				minTime: startTime
			}"
		></el-time-select>
	</div>
</template>

<script>
export default {
	props: {
		width: {
			type: String,
			default: '100%'
		},
		defaultTime: {
			// 初始化值
			type: Object,
			default: () => {
				return {
					start: '08:40',
					end: '21:00'
				};
			}
		},
		initData: {
			// 选择的值
			type: Object,
			default: () => {
				return {};
			}
		},
		readonly: {
			type: Boolean,
			default: false
		}
	},
	data() {
		return {
			startTime: '',
			endTime: ''
		};
	},
	watch: {
		initData: {
			handler(val) {
				this.startTime = val.startTime;
				this.endTime = val.endTime;
			},
			immediate: true,
			deep: true
		},
		endTime: {
			handler(val) {
				this.$emit('change', {
					startTime: this.startTime,
					endTime: this.endTime
				});
				if (this.startTime && this.endTime) {
					this.$emit('changeStr', this.startTime + '~' + this.endTime);
					this.startTime = '';
					this.endTime = '';
				}
			}
		}
	}
};
</script>
<style lang="scss" scoped>
.time-select {
	display: flex;
	justify-content: space-between;
	width: 100%;
	height: 100%;
}
</style>
