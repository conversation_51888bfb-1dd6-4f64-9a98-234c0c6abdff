<template>
	<div class="visitor">
		<div v-for="(item, index) in list" :key="index" class="item">
			<p class="num">
				<span>{{ item.value }}</span>
				<span class="unit">人</span>
			</p>
			<p class="name">{{ item.name }}</p>
		</div>
	</div>
</template>

<script>
export default {
	name: '',
	components: {},
	props: {},
	data() {
		return {
			detail: {}
		};
	},
	computed: {
		list() {
			return [
				{
					name: '累计访客',
					value: this.detail.totalNum || 0
				},
				{
					name: '今日访客',
					value: this.detail.todayNum || 0
				},
				{
					name: '本月访客',
					value: this.detail.monthNum || 0
				}
			];
		}
	},
	created() {
		this.getInfo('/ybzy/specificVisitorAppt/getStatistics'); //教职工基本数据_查询
	},
	methods: {
		getInfo(url) {
			this.$request({ url: url, method: 'GET' }).then(res => {
				if (res.rCode === 0) {
					this.detail = res.results || {};
					return;
				}
			});
		}
	}
};
</script>

<style lang="scss" scoped>
.visitor {
	display: flex;
	flex-wrap: wrap;
	justify-content: space-between;
	// padding: 0 19px;
	margin-top: 12px;
	.item {
		text-align: center;
		width: 24%;
		.num {
			font-family: DINPro, DINPro;
			font-weight: bold;
			font-size: 26px;
			color: #0a325b;
			line-height: 33px;
		}
		.unit {
			font-family: MicrosoftYaHei;
			font-size: 14px;
			color: #294d79;
			line-height: 19px;
			margin-left: 7px;
		}
		.name {
			font-family: MicrosoftYaHei;
			font-size: 14px;
			color: #454545;
			line-height: 19px;
			margin-top: 6px;
		}
	}
	.item + .item {
		position: relative;
		&::after {
			content: '';
			position: absolute;
			height: 40px;
			border-left: 1px dashed #a9bed5;
			left: -30%;
			top: 11px;
		}
	}
}
</style>
