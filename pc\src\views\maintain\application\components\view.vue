<template>
	<div class="template_div" v-loading="loading">
		<el-menu :default-active="activeMenus" mode="horizontal" class="es-menu" @select="handleSelect">
			<el-menu-item v-for="(item, index) in menus" :key="index" :index="String(index)">
				{{ item.label }}
			</el-menu-item>
		</el-menu>
		<div class="content">
			<div v-if="showBaseInfoView" class="content_div">
				<es-form
					ref="form"
					:model="formData"
					:contents="formItemList"
					:genre="2"
					height="600px"
					label-width="140px"
					collapse
					:submit="false"
					:reset="false"
				/>
			</div>
			<div v-if="showProgressBarView" class="content_bar_div">
				<el-timeline>
					<el-timeline-item
						v-for="(activity, index) in activities"
						:key="index"
						:type="index == 0 ? 'primary' : ''"
						:size="index == 0 ? 'large' : 'normal'"
						:timestamp="activity.createTime"
						placement="top"
					>
						<div>
							<p style="font-size: 16px; font-weight: bold; margin: 12px 0px">
								【{{ activity.dispatchTypeName }}】
							</p>
							<p v-if="activity.dispatchType == 0">
								【{{ activity.repairUserName }}】于{{ activity.createTime }}提交报修单
							</p>
							<p v-else-if="activity.dispatchType == 1">
								受理人【{{ activity.createUserName }}】指派给维修人员【{{
									activity.repairUserName
								}}】承修
							</p>
							<p v-else-if="activity.dispatchType == 6">
								系统自动指派给维修人员【{{ activity.repairUserName }}】承修
							</p>
							<p v-else-if="activity.dispatchType == 2">
								受理人【{{ activity.createUserName }}】转派给维修人员【{{
									activity.repairUserName
								}}】
							</p>
							<p v-else-if="activity.dispatchType == 3">
								【{{ activity.repairUserName }}】于{{ activity.createTime }}撤销派单
							</p>
							<p v-else-if="activity.dispatchType == 4 && formData.repairUserType == 0">
								维修人员【{{ activity.repairUserName }}】于{{ activity.createTime }}确认维修已完成
							</p>
							<p v-else-if="activity.dispatchType == 4 && formData.repairUserType == 1">
								维修管理员【{{ activity.repairUserName }}】于{{ activity.createTime }}确认维修已完成
							</p>
							<p v-if="activity.dispatchType == 5">
								【{{ activity.createUserName }}】于{{ activity.createTime }}修改预计维修完成时间：{{
									activity.remark
								}}
							</p>
						</div>
					</el-timeline-item>
				</el-timeline>
			</div>
			<!-- 处置结果（维修结果） -->
			<div v-if="showMaintenanceResView" class="content_div">
				<es-form
					ref="form"
					:model="formData"
					:contents="maintenanceRes"
					height="600px"
					label-width="140px"
					collapse
					:submit="false"
					:reset="false"
				/>
			</div>
			<div v-if="showReviewView" class="content_review_div">
				<div v-if="reviewInfo">
					<div>
						<h2 style="margin-bottom: 20px">对此次的维修服务满意度：</h2>
						<p style="margin-bottom: 20px">{{ reviewInfo.attitudeName }}</p>
					</div>
					<div>
						<h2 style="margin-bottom: 20px">对此次的维修服务打分：</h2>
						<el-rate
							:value="Number(reviewInfo.serviceScore)"
							disabled
							show-score
							style="margin-bottom: 20px"
						></el-rate>
					</div>
					<div>
						<h2 style="margin-bottom: 20px">对此次的维修服务建议：</h2>
						<p style="margin-bottom: 20px">{{ reviewInfo.remark }}</p>
					</div>
				</div>
				<div v-else><el-empty description="该报修单暂无评价"></el-empty></div>
			</div>
		</div>
	</div>
</template>
<script>
import api from '@/http/maintain/applicationApi';

export default {
	name: 'ViewPage',
	props: {
		id: {
			type: String,
			default: ''
		}
	},
	data() {
		return {
			loading: false,
			menus: [{ label: '报修详情', type: 'baseInfo' }],
			activeMenus: '0',
			showBaseInfoView: true,
			showProgressBarView: false,
			showMaintenanceResView: false,
			showReviewView: false,

			ownId: null,
			formData: {},
			formItemList: [
				{
					title: '报修信息',
					contents: [
						{
							name: 'repairCode',
							label: '维修单编号',
							placeholder: '维修单编号',
							readonly: true,
							col: 6
						},
						{
							name: 'repairTypeName',
							label: '报修类别',
							readonly: true,
							col: 6
						},
						{
							label: '报修人',
							name: 'reportUser',
							placeholder: '请输入报修人',
							readonly: true,
							col: 6
						},
						{
							label: '报修人所属机构',
							name: 'reportUserOrgName',
							readonly: true,
							col: 6
						},
						{
							label: '报修电话',
							name: 'repairPhone',
							placeholder: '请输入报修电话',
							readonly: true,
							col: 6
						},
						{
							type: 'datetime',
							name: 'createTime',
							label: '报修时间',
							placeholder: '报修时间',
							readonly: true,
							col: 6
						},
						{
							type: 'datetime',
							name: 'expectDate',
							label: '期待维修时间',
							placeholder: '期待维修时间',
							readonly: true,
							col: 6
						},
						{
							name: 'addressTypeName',
							label: '报修地点',
							readonly: true,
							col: 6
						},
						{
							label: '详细地址',
							name: 'address',
							placeholder: '请输入详细地址',
							readonly: true,
							col: 12
						},
						{
							label: '报修描述',
							type: 'textarea',
							name: 'reportContent',
							readonly: true,
							rows: 5
						},
						{
							label: '报修图片',
							type: 'attachment',
							name: 'img1',
							readonly: true,
							code: 'hq_report_image',
							'select-type': 'icon-plus',
							preview: true,
							listType: 'picture-card',
							ownId: this.id // 业务id
						}
					]
				}
			],
			maintenanceRes: [
				{
					label: '实际维修完成时间',
					type: 'datetime',
					name: 'finishTime',
					readonly: true,
					col: 6
				},
				{
					label: '维修费用',
					name: 'cost',
					readonly: true,
					col: 6
				},
				{
					label: '协助维修人',
					type: 'textarea',
					name: 'assistRepairUserNames',
					readonly: true
				},
				{
					label: '维修详情',
					type: 'textarea',
					name: 'repairContent',
					readonly: true
				},
				{
					label: '维修结果图片',
					type: 'attachment',
					name: 'img2',
					code: 'hq_report_deal_image',
					'select-type': 'icon-plus',
					preview: true,
					readonly: true,
					listType: 'picture-card',
					ownId: this.id // 业务id
				}
			],
			sendOrderInfo: {
				title: '派单信息',
				contents: [
					{
						label: '维修员类型',
						name: 'repairUserTypeName',
						readonly: true,
						col: 6
					},
					{
						label: '维修员',
						name: 'repairUserName',
						readonly: true,
						col: 6
					},
					{
						name: 'esCompletionDate',
						label: '预计维修完成时间',
						readonly: true,
						col: 6
					},
					{
						label: '紧急程度',
						name: 'urgencyName',
						readonly: true,
						col: 6
					},
					{
						label: '紧急原因',
						name: 'urgencyReason',
						type: 'textarea',
						readonly: true,
						col: 12
					}
				]
			},
			activities: [],
			reviewInfo: {}
		};
	},
	computed: {},
	watch: {},
	created() {
		this.getInfo();
		this.hqRepairDispatchList();
		this.getReviewInfo();
	},
	methods: {
		//获取报修详情
		getInfo() {
			this.loading = true;
			this.$request({ url: api.getApplicationInfo + '/' + this.id, method: 'GET' }).then(res => {
				this.loading = false;
				if (res.rCode === 0) {
					this.formData = res.results;
					let status = this.formData.reportStatus;
					if (status != '1') {
						this.menus.push({ label: '报修进度', type: 'progressBar' });
					}
					//已维修、已评价展示处置结果
					if (status == '3' || status == '4') {
						this.menus.push({ label: '处置结果', type: 'resultBar' });
					}
					if (status == '4') {
						this.menus.push({ label: '报修评价', type: 'review' });
					}
					if (status == '2' || status == '3' || status == '4') {
						this.formItemList.push(this.sendOrderInfo);
					}
				} else {
					this.$message.error(res.msg);
				}
			});
		},
		//获取报修进度
		hqRepairDispatchList() {
			this.$request({
				url: api.getHqRepairDispatchList,
				method: 'get',
				params: {
					reportId: this.id,
					pageSize: -1,
					pageNum: -1
				}
			}).then(res => {
				if (res.rCode == 0) {
					this.activities = res.results.records;
				} else {
					this.$message.error(res.msg);
				}
			});
		},
		//获取评价信息
		getReviewInfo() {
			this.$request({
				url: api.getReview,
				method: 'get',
				params: {
					reportId: this.id
				}
			}).then(res => {
				if (res.rCode == 0) {
					this.reviewInfo = res.results;
				} else {
					this.$message.error(res.msg);
				}
			});
		},
		handleSelect(res) {
			let tabIndex = parseInt(res);
			let tabInfo = this.menus[tabIndex];
			if ('baseInfo' == tabInfo.type) {
				this.showBaseInfoView = true;
				this.showProgressBarView = false;
				this.showReviewView = false;
				this.showMaintenanceResView = false;
			} else if ('progressBar' == tabInfo.type) {
				this.showBaseInfoView = false;
				this.showProgressBarView = true;
				this.showReviewView = false;
				this.showMaintenanceResView = false;
			} else if ('resultBar' == tabInfo.type) {
				this.showBaseInfoView = false;
				this.showProgressBarView = false;
				this.showReviewView = false;
				this.showMaintenanceResView = true;
			} else {
				this.showBaseInfoView = false;
				this.showProgressBarView = false;
				this.showReviewView = true;
				this.showMaintenanceResView = false;
			}
		}
	}
};
</script>
<style lang="scss" scoped>
@import '../../../../assets/style/style.scss';

.template_div {
	height: 620px;
}
.content {
	width: 100%;
	height: calc(100% - 50px);
	.content_div {
		height: calc(100% - 10px);
	}
	.content_bar_div {
		margin: 20px 20px;
		overflow-y: auto;
		max-height: 580px;
	}
	.content_review_div {
		margin: 20px 20px;
	}
}

.el-dialog__body {
	overflow: auto !important;
	.es-form .es-collapse {
		height: 100%;
	}
	height: 100%;

	.btn-box {
		margin-top: 20px;
		display: flex;
		justify-content: flex-end;

		.btn {
			padding: 5px 10px;
			color: #666;
			border: 1px solid #eee;
			cursor: pointer;
			margin-right: 5px;
			border-radius: 4px;
		}
	}
}
</style>
