.tree-select {
	padding: 0px 10px;
	height: 100%;
	.el-input-group__append {
		background: $--color-primary !important;
		color: #fff;
	}
	p {
		margin: 10px 0px;
		text-align: center;
	}
	.search-info {
		padding: 0px 10px;
	}
	.filter-tree {
		margin: 10px 0px;
	}
	.el-tree {
		height: 100%;
		padding: 10px 0px;
		overflow: auto;
		font-size: 13px;
	}

	.el-tree::-webkit-scrollbar {
		width: 4px;
		height: 4px;
	}

	.el-tree::-webkit-scrollbar-thumb {
		border-radius: 5px;
		-webkit-box-shadow: inset 0 0 5px rgba(0, 0, 0, 0.2);
		background: rgba(0, 0, 0, 0.2);
	}

	.el-tree::-webkit-scrollbar-track {
		-webkit-box-shadow: inset 0 0 5px rgba(0, 0, 0, 0.2);

		border-radius: 0;

		background: rgba(0, 0, 0, 0.1);
	}
	.el-tree-node > .el-tree-node__children {
		overflow: unset !important;
	}
}
