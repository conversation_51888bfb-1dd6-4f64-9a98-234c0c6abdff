<template>
	<div>
		<es-dialog
			title="编辑"
			:visible.sync="visible"
			width="600px"
			height="350px"
			ref="visible"
			@close="handleclose"
		>
			<es-form
				v-if="visible"
				ref="form"
				:model="formData"
				:formatSubmit="false"
				:contents="formItemList"
				enter-submit
				@submit="handleFormSubmit"
				label-position="top"
			></es-form>
		</es-dialog>
	</div>
</template>

<script>
import api from '@/http/dataManage/data-manager';

export default {
	name: 'editFile',

	data() {
		return {
			visible: false,
			formData: {
				id: '',
				classId: null,
				fileName: '',
				fileMat: '',
				fileSize: '',
				remark: '',
				fileSysId: '',
			}
		};
	},
	computed: {
		formItemList() {
			return [
				{
					name: 'fileName',
					placeholder: '文件名',
					label: '文件名'
				},
				{
					name: 'remark',
					placeholder: '备注',
					label: '备注'
				},
				{
					label: '上传附件',
					type: 'attachment',
					name: 'fj',
					code: 'doc_manage_upload',
					ownId: this.formData.fileSysId,
					rules: {
						required: true,
						message: '请上传附件',
						trigger: 'blur'
					}
				}
			];
		}
	},
	methods: {
		open(RowData) {
			this.formData = {...RowData};
			console.log(RowData);
			this.visible = true;
		},
		handleFormSubmit() {
			console.log(this.formData);
			
			let { id,classId, fileName ,fileMat, fileSize, remark ,fileSysId } = this.formData;
			let param = { id,classId, fileName ,fileMat, fileSize, remark ,fileSysId };
			this.$request({
				url: api.UpdateFile,
				data: param,
				format: false,
				method: 'POST'
			}).then(res => {
                this.visible = false
				if (res.rCode === 0) {
					this.$message.success(res.msg);
				}
			});



		},
		handleclose() {}
	}
};
</script>

<style scoped></style>
