export default {
	computed: {
		viewItemList() {
			return [
				{
					name: 'college<PERSON><PERSON>',
					label: '二级学院',
					value: '',
					col: 12
				},
				{
					name: 'title',
					label: '标题',
					value: '',
					col: 12
				},
				{
					name: 'depict',
					label: '描述',
					value: '',
					col: 12
				},
				{
					label: '整改报告文件',
					type: 'attachment',
					code: 'lab_room_reform_file',
					name: 'reformFile',
					ownId: this.viewData.id, // 业务id
					dragSort: true,
					limit: 8,
					col: 12
				},
				{
					hide: this.viewData.reformStatus !== '1',
					name: 'reply',
					label: '报告回复',
					value: '',
					col: 12
				},
				{
					hide: this.viewData.reformStatus !== '1',
					label: '报告回复文件',
					type: 'attachment',
					code: 'lab_room_report_reply_file',
					name: 'reportFile',
					ownId: this.viewData.id, // 业务id
					dragSort: true,
					limit: 8,
					col: 12
				}
			];
		}
	}
};
