<template>
	<div class="container">
		<div class="treeContainer">
			<el-input
				class="search"
				placeholder="输入部门进行搜索"
				v-model="filterText"
				size="small"
			></el-input>
			<el-tree
				class="filter-tree"
				:data="data"
				:props="defaultProps"
				:filter-node-method="filterNode"
				@node-click="handleNodeClick"
				node-key="id"
				ref="tree"
			></el-tree>
		</div>

		<div class="tableContainer">
			<div class="headerContainer">
				<el-form :model="form">
					<el-row>
						<el-col :span="18" class="handleBut">
							<el-upload :show-file-list="false" :http-request="handleUpload" action="">
								<el-button type="primary" size="small" v-if="isHandleAuthority">上传</el-button>
							</el-upload>
						</el-col>
						<el-col :span="6">
							<el-form-item>
								<el-input v-model="form.fileName" size="small" placeholder="请输入文件名">
									<el-button slot="append" @click="getTableData">搜索</el-button>
								</el-input>
							</el-form-item>
						</el-col>
					</el-row>
				</el-form>
			</div>
			<es-data-table :data="tableData" v-loading="isLoading">
				<template slot="prepend">
					<el-table-column
						prop="fileName"
						label="文件名称"
						align="center"
						show-overflow-tooltip
					></el-table-column>
					<el-table-column
						prop="department"
						label="部门"
						align="center"
						show-overflow-tooltip
					></el-table-column>
					<el-table-column
						prop="updateTime"
						label="上传时间"
						align="center"
						show-overflow-tooltip
					></el-table-column>
					<el-table-column label="操作" align="center" width="200">
						<template slot-scope="{ row }">
							<el-button type="text" @click="handleCheck(row.filePath, row.fileName)">
								查看
							</el-button>
							<el-button type="text" @click="handleDownload(row.filePath, row.fileName)">
								下载
							</el-button>
							<el-button type="text" @click="handleDelete(row.id)" v-if="isHandleAuthority">
								删除
							</el-button>
						</template>
					</el-table-column>
				</template>
			</es-data-table>
			<es-pagination
				:totalCount="totalCount"
				style="float: right"
				:pageSize="10"
				@current="currentChange"
				@change="sizeChange"
			></es-pagination>
		</div>
	</div>
</template>

<script>
import axios from 'axios';
export default {
	data() {
		return {
			filterText: '',
			form: {
				fileName: '',
				current: 1,
				size: 10,
				departmentCode: ''
			},
			isLoading: false,
			data: [], // 机构树数据
			defaultProps: {
				children: 'child',
				label: 'name'
			},
			tableData: [],
			// 上传信息
			uploadInfo: {
				department: '', // 部门名称
				departmentCode: '', //部门号
				fileName: '', //文件名
				filePath: '', //文件地址
				title: ''
			},
			totalCount: 1, //分页总数
			userInfo: {
				account: '',
				roleCode: 'system_manager' //写死
			},
			isHandleAuthority: false
		};
	},
	watch: {
		filterText(val) {
			this.$refs.tree.filter(val);
		}
	},
	computed: {
		proxyApi() {
			return process.env.NODE_ENV === 'development' ? '/apia' : '/college-base';
		}
	},
	mounted() {
		this.getUserAuthority();
		this.getInitData();
	},
	methods: {
		async getInitData() {
			try {
				this.data = await this.getTreeData();
			} catch (error) {
				console.log(error);
			}
			// 设置默认选中
			// this.uploadInfo.departmentCode = this.data[0].id;
			// this.uploadInfo.department = this.data[0].name;
			// this.$nextTick(() => {
			// 	this.$refs.tree.setCurrentKey(this.data[0].id);
			// });
			this.uploadInfo.departmentCode = null;
			this.getTableData();
		},
		// 获取角色信息
		getUserAuthority() {
			// 获取登录用户信息
			this.userInfo.account = JSON.parse(localStorage.getItem('loginUserInfo')).code;
			this.$request({
				url: '/ybzy/platuser/front/checkRole',
				data: this.userInfo,
				method: 'post'
			}).then(res => {
				this.isHandleAuthority = res.results[this.userInfo.roleCode];
			});
		},
		getTableData() {
			this.isLoading = true;
			const data = {
				current: this.form.current,
				size: this.form.size,
				fileName: this.form.fileName,
				departmentCode: this.uploadInfo.departmentCode
			};
			this.$request({
				url: `${this.proxyApi}/academy/rule/list`,
				method: 'post',
				data: data,
				format: false
			}).then(res => {
				this.tableData = res.data.records;
				this.totalCount = res.data.total;
				this.isLoading = false;
			});
		},
		// 获取机构列表
		getTreeData() {
			return new Promise((resolve, reject) => {
				this.$.ajax({
					// http://yszj.ybzy.cn/ybzy-hr-api/person/hrnCorrelation/getJobCascade
					url: 'http://yszj.ybzy.cn/ybzy-hr-api/person/hrnDepartment/getDepartmentCascade',
					method: 'get'
				}).then(res => {
					resolve(res.data);
				});
			});
		},

		filterNode(value, data) {
			if (!value) return true;
			return data.name.indexOf(value) !== -1;
		},
		// 部门选择事件
		handleNodeClick(data) {
			this.uploadInfo.departmentCode = data.id;
			this.uploadInfo.department = data.name;
			this.getTableData();
		},
		// 上传文件
		handleUpload(data) {
			if (!this.uploadInfo.departmentCode) {
				this.$message.warning('请先选择部门');
				return;
			}
			let formData = new FormData();
			formData.append('file', data.file);
			this.uploadInfo.fileName = data.file.name.split('.').shift();
			this.uploadInfo.title = this.uploadInfo.fileName;
			// /api/apia
			// axios.post('/file/minio/file/bucket/pull/1', formData)
			this.$request({
				url: `${this.proxyApi}/file/minio/file/bucket/pull/1`,
				data: formData,
				method: 'post',
				format: false
			}).then(res => {
				if (res.code == 200) {
					this.uploadInfo.filePath = res.data;
					this.$request({
						url: `${this.proxyApi}/academy/rule/create`,
						data: this.uploadInfo,
						method: 'post',
						format: false
					}).then(res => {
						if (res.code == 200) {
							this.$message.success('上传成功');
							this.getTableData();
						} else {
							this.$message.warning(res.msg);
						}
					});
				} else {
					this.$message.warning(res.data.msg);
				}
			});
		},
		// 查看
		handleCheck(url, fileName) {
			window.open(url, '_blank');
		},
		// 下载
		handleDownload(url, fileName) {
			axios({
				url: url,
				method: 'GET',
				responseType: 'blob' // 设置响应的数据类型为二进制流
			})
				.then(response => {
					const blobUrl = window.URL.createObjectURL(
						new Blob([response.data], { type: response.headers['content-type'] })
					); // 指定 MIME 类型
					const contentDisposition = response.headers['content-disposition'];
					let downloadFileName = fileName; // 默认文件名

					// 解析 Content-Disposition 头部以获取文件名
					if (contentDisposition && contentDisposition.indexOf('attachment') !== -1) {
						const matches = /filename[^;=\n]*=((['"]).*?\2|([^;\n]*))/i.exec(contentDisposition);
						if (matches != null && matches[1]) {
							downloadFileName = matches[1].replace(/['"]/g, ''); // 去掉引号
						}
					}

					const link = document.createElement('a');
					link.href = blobUrl;
					link.download = downloadFileName; // 设置下载的文件名
					document.body.appendChild(link);
					link.click();
					document.body.removeChild(link);
					window.URL.revokeObjectURL(blobUrl); // 释放URL对象
					this.$message.success('下载成功');
				})
				.catch(error => {
					this.$message.error('下载失败');
				});
		},
		// 删除
		handleDelete(id) {
			this.$confirm('此操作将永久删除该文件, 是否继续?', '提示', {
				confirmButtonText: '确定',
				cancelButtonText: '取消',
				type: 'warning'
			})
				.then(() => {
					this.$request({
						url: `${this.proxyApi}/academy/rule/delete`,
						params: { ids: id },
						method: 'get'
					}).then(res => {
						this.form.current = 1;
						this.getTableData();
						this.tableData = this.$message({
							type: 'success',
							message: '删除成功!'
						});
					});
				})
				.catch(() => {});
		},
		sizeChange(val) {
			this.form.size = val;
			this.getTableData();
		},
		currentChange(val) {
			this.form.current = val;
			this.getTableData();
		}
	}
};
</script>

<style lang="scss" scoped>
.container {
	display: flex;
	height: 100%;
	width: 100%;
	.treeContainer {
		height: 100%;
		border-right: 1px solid #f0f0f0;
		padding-right: 5px;
		width: 25%;
		overflow: auto;
		.filter-tree {
			height: calc(100% - 40px);
			width: 100%;
			overflow: auto;
		}
	}
	.search {
		margin-bottom: 5px;
	}
	.tableContainer {
		height: 100%;
		width: 80%;
		padding-left: 5px;
		.headerContainer {
			.handleBut {
				height: 38px;
				// display: flex;
				// justify-content: flex-end;
			}
		}
	}
}
</style>
