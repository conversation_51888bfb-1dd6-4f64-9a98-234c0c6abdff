<template>
	<div class="content">
		<div style="width: 100%">
			<el-menu
				:default-active="activeMenus"
				mode="horizontal"
				class="es-menu"
				@select="handleSelect"
			>
				<el-menu-item v-for="item in menus" :key="item.key" :index="item.key">
					{{ item.label }}
				</el-menu-item>
			</el-menu>
			<es-data-table
				v-if="activeMenus === '0'"
				ref="table"
				:key="tableCount"
				:row-style="tableRowClassName"
				:full="true"
				:fit="true"
				:thead="thead"
				:toolbar="toolbar"
				:border="true"
				:page="pageOption"
				:url="dataTableUrl"
				:numbers="true"
				:param="params"
				close
				@btnClick="btnClick"
				@sort-change="sortChange"
			></es-data-table>
			<es-data-table
				v-if="activeMenus === '1'"
				ref="table"
				:key="tableCount"
				:row-style="tableRowClassName"
				:full="true"
				:fit="true"
				:thead="thead"
				:toolbar="toolbar"
				:border="true"
				:page="pageOption"
				:url="dataTableUrl"
				:numbers="true"
				:param="params"
				close
				@btnClick="btnClick"
				@sort-change="sortChange"
			></es-data-table>
			<es-data-table
				v-if="activeMenus === '2'"
				ref="table"
				:key="tableCount"
				:row-style="tableRowClassName"
				:full="true"
				:fit="true"
				:thead="thead"
				:toolbar="toolbar"
				:border="true"
				:page="pageOption"
				:url="dataTableUrl"
				:numbers="true"
				:param="params"
				close
				@btnClick="btnClick"
				@sort-change="sortChange"
			></es-data-table>
			<es-dialog
				:title="formTitle"
				:visible.sync="showForm"
				width="80%"
				height="80%"
				:close-on-click-modal="false"
				:destroy-on-close="true"
			>
				<es-form
					ref="form"
					:model="formData"
					:contents="formItemList"
					height="100%"
					:genre="2"
					collapse
					@change="inputChange"
					@submit="handleFormSubmit"
					@reset="showForm = false"
				/>
			</es-dialog>
			<es-dialog
				title="删除"
				:visible.sync="showDelete"
				width="20%"
				:close-on-click-modal="false"
				:middle="true"
				height="140px"
			>
				<div>确定要删除该条数据吗</div>
				<div class="btn-box">
					<div class="btn theme" @click="deleteRow">确定</div>
					<div class="btn" @click="showDelete = false">取消</div>
				</div>
			</es-dialog>
			<!-- 社区成员 -->
			<el-dialog
				:visible.sync="memberVisible"
				title="社区成员"
				width="600px"
				append-to-body
				custom-class="member-dialog"
				destroy-on-close
			>
				<el-table v-loading="memberLoading" :data="memberList">
					<el-table-column label="成员姓名" prop="memberName" />
					<el-table-column label="电话号码" prop="phoneNum" />
				</el-table>
				<span slot="footer" class="dialog-footer">
					<el-button @click="memberVisible = false">关闭</el-button>
				</span>
			</el-dialog>
		</div>
	</div>
</template>

<script>
import interfaceUrl from '@/http/alumna/community.js';
import alumnacommunitymemberInterfaceUrl from '@/http/alumna/alumnacommunitymember.js';
import SnowflakeId from 'snowflake-id';
import { fileAccess } from '@/../config/config';
export default {
	data() {
		return {
			menus: [
				{ key: '0', label: '待审核' },
				{ key: '2', label: '审核未通过' },
				{ key: '1', label: '审核通过' }
			],
			activeMenus: '0',
			dataTableUrl: interfaceUrl.listJson,
			tableCount: 1,
			showForm: false,
			showDelete: false,
			ownId: null, //数据行Id
			validityOfDateDisable: false,
			params: {
				orderBy: 'createTime',
				asc: 'false',
				isAudit: 0
			},
			editBtn: {
				type: 'submit',
				skin: 'lay-form-btns',
				contents: [
					{ type: 'primary', text: '确定', event: 'confirm' },
					{ type: 'reset', text: '取消', event: 'cancel' }
				]
			},
			cancelBtn: {
				type: 'submit',
				skin: 'lay-form-btns',
				contents: [{ type: 'reset', text: '取消', event: 'cancel' }]
			},
			toolbar: [
				{
					type: 'search',
					contents: [{ type: 'text', name: 'keyword', placeholder: '关键字查询' }]
				}
			],
			thead: [],
			listThead: [
				{
					title: '社区名称',
					align: 'left',
					field: 'communityName',
					showOverflowTooltip: true
				},
				{
					title: '容纳人数',
					align: 'left',
					field: 'capacityNum'
				},
				{
					title: '现有人数',
					align: 'left',
					field: 'memberNum'
				},
				{
					title: '申请人',
					align: 'left',
					field: 'createUserName'
				},
				{
					title: '申请时间',
					align: 'left',
					field: 'createTime',
					showOverflowTooltip: true
				}
			],
			auditListBtn: {
				title: '操作',
				type: 'handle',
				width: 180,
				template: '',
				events: [
					{ code: 'audit', text: '审核' },
					{ code: 'view', text: '查看' }
				]
			},
			viewListBtn: {
				title: '操作',
				type: 'handle',
				width: 180,
				template: '',
				events: [{ code: 'view', text: '查看' }]
			},
			passListBtn: {
				title: '操作',
				type: 'handle',
				width: 180,
				template: '',
				events: [
					{ code: 'view', text: '查看' },
					{
						code: 'disabled',
						text: '关闭',
						rules: rows => rows.enableStatus !== '1'
					},
					{
						code: 'enabled',
						text: '开启',
						rules: rows => rows.enableStatus === '1'
					}
				]
			},
			pageOption: {
				layout: 'total, prev, pager, next, jumper',
				pageSize: 10,
				// hideOnSinglePage: true,
				position: 'right',
				current: 1,
				pageNum: 1
			},
			formData: {},
			formTitle: '编辑',
			formItemList: [
				{
					label: '社区主题名称',
					name: 'communityName',
					placeholder: '请输入社区主题名称',
					rules: {
						required: true,
						message: '请输入社区主题名称',
						trigger: 'blur'
					},
					verify: 'required',
					col: 8
				},
				{
					label: '社区封面图',
					name: 'communityLogoTemp',
					type: 'attachment',
					code: 'alumna_community_cover',
					ownId: this.ownId,
					size: 100,
					portrait: true,
					readonly: false,
					param: {
						isShowPath: true
					},
					col: 12
				},
				{
					label: '社区容纳人数',
					name: 'capacityNum',
					placeholder: '请输入社区容纳人数',
					type: 'number',
					controls: true,
					rules: {
						required: true,
						message: '请输入社区容纳人数',
						trigger: 'blur'
					},
					verify: 'required',
					col: 4,
					inline: [
						{
							type: 'button',
							text: '查看',
							mold: 'primary',
							event: this.viewMember
						}
					]
				},
				{
					label: '社区介绍',
					name: 'introduce',
					placeholder: '请填写社区介绍',
					type: 'textarea',
					col: 12
				},
				{
					label: '管理员联系方式',
					name: 'adminUserPhone',
					placeholder: '',
					col: 12
				},
				{
					label: '审核状态',
					name: 'isAudit',
					type: 'radio',
					data: [
						{ value: 1, name: '审核通过' },
						{ value: 2, name: '审核不通过' }
					],
					verify: 'required',
					rules: {
						required: true,
						message: '请确定是否审核通过',
						trigger: 'blur'
					},
					col: 4
				},
				{
					label: '审核意见',
					name: 'auditContent',
					placeholder: '请填写审核意见',
					type: 'textarea',
					col: 12
				}
			],
			// 社区成员
			memberVisible: false,
			memberLoading: true,
			memberList: []
		};
	},
	watch: {
		dataTableUrl() {
			this.tableCount++;
		},
		formItemList: {
			handler(newValue, oldValue) {},
			deep: true
		}
	},
	created() {
		this.thead = this.getListThead(this.auditListBtn);
	},
	methods: {
		//表格行高
		tableRowClassName({ row, rowIndex }) {
			let styleRes = { height: '54px !important' };
			return styleRes;
		},
		inputChange(key, value) {},
		//操作按钮点击事件
		btnClick(res) {
			let code = res.handle.code;
			switch (code) {
				case 'add':
					this.formTitle = '新增';
					this.editModule(this.formItemList);
					const snowflake = new SnowflakeId();
					this.formData = { id: snowflake.generate(), status: true };
					this.showForm = true;
					break;
				case 'edit':
					this.formTitle = '编辑';
					this.ownId = res.row.id;
					this.editModule(this.formItemList);
					this.$request({ url: interfaceUrl.info + '/' + res.row.id, method: 'GET' }).then(res => {
						if (res.rCode === 0) {
							this.formData = res.results;
							this.showForm = true;
						}
					});
					break;
				case 'view':
					this.formTitle = '查看';
					this.ownId = res.row.id;
					this.readModule(this.formItemList);
					this.$request({ url: interfaceUrl.info + '/' + res.row.id, method: 'GET' }).then(res => {
						if (res.rCode === 0) {
							this.formData = res.results;
							this.formData.communityLogoTemp = fileAccess + res.results.communityLogo;
							this.showForm = true;
						}
					});
					break;
				case 'delete':
					this.deleteId = res.row.id;
					this.showDelete = true;
					break;
				case 'audit':
					this.formTitle = '审核';
					this.auditModule(this.formItemList, ['isAudit', 'auditContent']);
					this.ownId = res.row.id;
					this.$request({ url: interfaceUrl.info + '/' + res.row.id, method: 'GET' }).then(res => {
						if (res.rCode === 0) {
							this.formData = res.results;
							this.formData.communityLogoTemp = fileAccess + res.results.communityLogo;
							this.formData.isAudit = null;
							this.showForm = true;
						}
					});
					break;
				case 'enabled': // 开启
					this.$request({ url: interfaceUrl.open + '/' + res.row.id, method: 'GET' }).then(res => {
						if (res.rCode === 0) {
							this.$refs.table.reload();
						}
					});
					break;
				case 'disabled': // 关闭
					this.$request({ url: interfaceUrl.close + '/' + res.row.id, method: 'GET' }).then(res => {
						if (res.rCode === 0) {
							this.$refs.table.reload();
						}
					});
					break;
				default:
					break;
			}
		},
		//表单提交
		handleFormSubmit(data) {
			this.$refs.form.validate(valid => {
				if (!valid) {
					return;
				}
				let formData = data;
				for (let key in formData) {
					if (key.indexOf('_') >= 0) {
						let indexName = key.replace('_', '.');
						formData[indexName] = formData[key];
					}
				}
				let url = '';
				if (this.formTitle === '新增') {
					url = interfaceUrl.save;
				} else if (this.formTitle === '审核') {
					url = interfaceUrl.info + '/' + this.ownId + '/audit';
					formData = {
						isAudit: formData.isAudit,
						auditContent: formData.auditContent
					};
				} else {
					url = interfaceUrl.update;
				}

				this.$request({ url: url, data: formData, method: 'POST' }).then(res => {
					if (res.rCode !== 0) {
						this.$message.error(res.msg);
						return;
					}
					this.$message.success('操作成功');
					this.showForm = false;
					this.formData = {};
					this.$refs.table.reload();
				});
			});
		},
		//删除行
		deleteRow() {
			this.$request({
				url: interfaceUrl.deleteBatchIds,
				data: { ids: this.deleteId },
				method: 'POST'
			}).then(res => {
				if (res.rCode === 0) {
					this.$refs.table.reload();
					this.$message.success('删除成功');
				} else {
					this.$message.error(res.msg);
				}
				this.showDelete = false;
			});
		},
		//排序变化事件
		sortChange(column, prop, order) {
			let asc = this.params.asc;
			let orderBy = this.params.orderBy;
			if (column.order === 'ascending') {
				//升序
				asc = 'true';
				orderBy = column.prop;
			} else if (column.order === 'descending') {
				//降序
				asc = 'false';
				orderBy = column.prop;
			} else {
				//不排序
				asc = 'false';
				orderBy = column.prop;
			}
			this.params.asc = asc;
			this.params.orderBy = orderBy;
			this.$refs.table.reload();
		},
		//编辑模式
		editModule(list) {
			for (var i in list) {
				var item = list[i];
				item.readonly = false;
			}
			list.push(this.editBtn);
		},
		//只读模式
		readModule(list) {
			for (var i in list) {
				var item = list[i];
				item.readonly = true;
			}
			list.push(this.cancelBtn);
		},
		//审核模式
		auditModule(list, editCols) {
			for (var i in list) {
				var item = list[i];
				item.readonly = !(
					editCols !== undefined &&
					editCols.length > 0 &&
					editCols.indexOf(item.name) > -1
				);
			}
			list.push(this.editBtn);
		},
		//页签切换
		handleSelect(res) {
			this.activeMenus = res;
			this.thead = [];
			if ('0' === res) {
				this.params.isAudit = 0;
				this.thead = this.getListThead(this.auditListBtn);
			} else if ('1' === res) {
				this.params.isAudit = 1;
				this.thead = this.getListThead(this.passListBtn);
			} else if ('2' === res) {
				this.params.isAudit = 2;
				this.thead = this.getListThead(this.viewListBtn);
			}
			this.tableCount++;
		},
		getListThead(btnJson) {
			let tempThead = Object.assign([], this.listThead);
			if (tempThead.length > 7) {
				tempThead.pop();
			}
			tempThead.push(btnJson);
			return tempThead;
		},
		refreshData() {
			this.$refs.table.reload();
		},
		viewMember() {
			this.memberList = []
			this.memberVisible = true
			this.memberLoading = true
			this.$request({ url: `${alumnacommunitymemberInterfaceUrl.listJson}?communityId=${this.ownId}&pageNum=1&pageSize=1000`, method: 'GET' }).then(res => {
				if (res.rCode === 0) {
					this.memberList = res.results.records
					this.memberLoading = false
				} else {
					this.memberLoading = false
				}
			}).catch(() => this.memberLoading = false);
		}
	}
};
</script>

<style scoped lang="scss">
@import '../../../assets/style/style.scss';
.content {
	display: flex;
	width: 100%;
	height: 100%;

	::v-deep .es-data-table {
		flex: 1;
		display: flex;
		flex-direction: column;
		height: calc(100% - 58px);

		.es-data-table-content {
			flex: 1;
			height: 0;
			display: flex;
			flex-direction: column;

			.el-table {
				flex: 1;
				// height: 100% !important;
			}

			.es-thead-border {
				.el-table__header {
					th {
						border-right: 1px solid #e1e1e1;
					}
				}
			}
		}
	}

	::v-deep .el-form-item__label {
		background: none;
		border: 0px solid #c2c2c2;
	}
}

.el-dialog__body {
	.btn-box {
		margin-top: 20px;
		display: flex;
		justify-content: flex-end;

		.btn {
			padding: 5px 10px;
			color: #666;
			border: 1px solid #eee;
			cursor: pointer;
			margin-right: 5px;
			border-radius: 4px;
			// &.theme {
			// 	background: $--color-primary;
			// 	color: #fff;
			// 	border-color: $--color-primary;
			// }
		}
	}
}
::v-deep .member-dialog .el-dialog__body {
	max-height: 70vh;
}
</style>
