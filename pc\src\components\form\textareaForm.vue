<!--
 @desc:文本域表单
 @author: WH
 @date: 2023/5/8
 -->
<template>
	<es-dialog :title="dialogTitle" :visible.sync="visible" width="600px" height="300px">
		<div class="textarea-box">
			<es-input
				type="textarea"
				:rows="6"
				:count="300"
				placeholder="请输入办理意见"
				v-model="textarea"
			></es-input>
			<div class="btn-box">
				<el-button @click="confirm" type="primary" size="medium">确定</el-button>
				<el-button @click="esc" size="medium">取消</el-button>
			</div>
		</div>
	</es-dialog>
</template>

<script>
export default {
	props: {
		dialogTitle: {
			type: String,
			require: true
		},
		visible: {
			type: Boolean,
			require: true
		}
	},
	data() {
		return {
			textarea: ''
		};
	},
	methods: {
		confirm() {
			this.$emit('confirm', this.textarea);
		},
		esc() {
			this.$emit('update:visible', false);
		}
	}
};
</script>

<style lang="scss" scoped>
.textarea-box {
	width: 100%;
	height: 100%;
	overflow: auto;
	position: relative;
}
.btn-box {
	position: absolute;
	width: 100%;
	text-align: right;
	bottom: 0;
}
</style>
