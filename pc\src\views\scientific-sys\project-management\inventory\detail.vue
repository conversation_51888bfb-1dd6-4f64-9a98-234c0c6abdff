<template>
	<es-tabs v-model="activeName" @tab-click="handleClick">
		<el-tab-pane label="基本信息" name="BasicInfo" class="basic-box">
			<!-- <basic-info v-if="activeName == 'BasicInfo'" :openType="openType" /> -->
			<BasicInfo
				:id="id"
				style="width: 100%"
				:contents-key="contentsKey"
				:title="title"
				:is-flow-pattern="isFlowPattern"
				:basics="{
					info: '/ybzy/projectBaseInfo/getAllViewInfo', // 详情接口
					edit: '/ybzy/projectBaseInfo/projectApproval', // 修改
					flowTypeCode: 'project_crosswise', // 流程code
					defaultProcessKey: 'gylc' // 默认关联流程 key
				}"
				@formData="e => (changes = e.changes)"
				@visible="
					e => {
						visibleBasicInfo = e;
						$refs.refTable.reload();
					}
				"
			/>
			<!-- 右侧节点信息 -->
			<node-info v-if="openType == 'add'" />
		</el-tab-pane>
		<template v-if="openType !== 'add'">
			<!-- <el-tab-pane label="资金信息" name="CapitalInfo">
				<capital-info v-if="activeName == 'CapitalInfo'" />
			</el-tab-pane> -->
			<el-tab-pane label="变更信息" name="ChangeInfo">
				<change-info :list="changes" />
			</el-tab-pane>
		</template>
	</es-tabs>
</template>
<script>
// import BasicInfo from './basic-info/index.vue';
import BasicInfo from '../../components/project-info/basic-info.vue';
// import CapitalInfo from './capital-info/index.vue';
import ChangeInfo from './change-info/index.vue';
import NodeInfo from './node-info/index.vue';

// import { getRoleMap } from '@/api/scientific-sys.js';
export default {
	name: 'ScientificDeail',
	components: {
		BasicInfo,
		// CapitalInfo,
		ChangeInfo,
		NodeInfo
	},
	provide() {
		return {
			id: this.id,
			openType: this.openType,
			initActiveName: this.initActiveName,
			projectClassifyStr: this.projectClassify
		};
	},
	props: {
		id: {
			type: String,
			required: true
		},
		isFlowPattern: {
			type: Boolean,
			default: true
		},
		openType: {
			type: String,
			required: true
		},
		initActiveName: {
			type: String,
			// required: true
			default: 'BasicInfo'
		},
		projectClassify: {
			type: String,
			default: ''
		},
		title: {
			type: String,
			default: '查看'
		}
	},
	data() {
		return {
			changes: [],
			activeName: '',
			projectScienceManager: ''
		};
	},
	computed: {
		contentsKey() {
			// LENTHWAYS("lengthWays", "纵向项目",0),
			// CROSSWISE("crosswise", "横向项目",1),
			// COLLEGE("college", "院级项目",2),
			const projectClassify = Number(this.projectClassify);
			let contentsKey = '';
			switch (projectClassify) {
				case 0:
					contentsKey = 'verticalProject'; // 纵向项目
					break;
				case 1:
					contentsKey = 'crosswiseProject'; // 横向项目
					break;
				case 2:
					contentsKey = 'instituteProject'; // 院级项目
					break;
			}
			return contentsKey;
		}
	},
	created() {
		this.activeName = this.initActiveName;
		this.$bus.$on('closeDialog', reload => {
			this.reload(reload);
		});
	},
	destroyed() {
		this.$bus.$off('closeDialog');
	},
	methods: {
		reload(reload) {
			this.$emit('reload', reload);
		},
		handleClick(tab, event) {
			console.log(tab, event);
		}
	}
};
</script>
<style lang="scss" scoped>
.basic-box {
	display: flex;
}
</style>
