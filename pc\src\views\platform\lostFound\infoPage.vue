<template>
  <div class="sketch_content">
    <el-form :model="formData" class="resumeTable" :rules="rules" ref="form">
      <el-row>
        <el-col :span="11">
          <el-form-item label="标题" label-width="90px" label-position="left" prop="title">
            <el-input v-model="formData.title" :disabled="infoDisabled"></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="11">
          <el-form-item label="分类" label-width="90px" label-position="left" prop="type">
            <el-select v-model="formData.type" :disabled="infoDisabled" clearable>
              <el-option-group>
                <el-option :value="0" label="遗失"></el-option>
                <el-option :value="1" label="拾获"></el-option>
              </el-option-group>
            </el-select>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row>
        <el-col :span="11">
          <el-form-item label="时间" label-width="90px" label-position="left" prop="lfTime">
            <el-date-picker v-model="formData.lfTime" :disabled="infoDisabled" value-format="yyyy-MM-dd hh:mm:ss" type="datetime"></el-date-picker>
          </el-form-item>
        </el-col>
        <el-col :span="11">
          <el-form-item label="地点" label-width="90px" label-position="left" prop="location">
            <el-input v-model="formData.location" :disabled="infoDisabled"></el-input>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row>
        <el-col :span="11">
          <el-form-item label="发布人" label-width="90px" label-position="left" prop="publisher">
            <el-input v-model="formData.publisher" :disabled="infoDisabled"></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="11">
          <el-form-item label="电话" label-width="90px" label-position="left" prop="telephone">
            <el-input v-model="formData.telephone" :disabled="infoDisabled"></el-input>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row>
        <el-col :span="22">
          <el-form-item label="照片" label-width="90px" label-position="left">
            <es-upload v-bind="attrs" :disabled="infoDisabled"
                       select-type="icon-plus" list-type="picture-card" ref="upload">
            </es-upload>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row>
          <el-col :span="22">
            <el-form-item label="内容" label-width="90px" label-position="left" prop="details">
              <el-input v-model="formData.details" :disabled="infoDisabled" type="textarea" rows="8"></el-input>
            </el-form-item>
          </el-col>
      </el-row>
      <el-row>
        <el-col :span="3" style="float:right">
          <el-button type="reset" @click="infoPageClose(false)">取消</el-button>
        </el-col>
        <el-col :span="3" style="float:right">
          <el-button type="primary" @click="handleFormSubmit" v-show="!infoDisabled">保存</el-button>
        </el-col>
      </el-row>
    </el-form>
  </div>
</template>
<script>

import api from "@/http/platform/lostFound";

export default {
  name: 'infoPage',
  props: {
    baseData: {
      type: Object
    },
    infoPageMode: {
      type: String
    }
  },
  data() {
    return {
      infoDisabled: true,
      formData: {},
      pageMode: 'allOn',
      rules: {
        title: [{required: true, message: '请输入标题', trigger: 'blur'}],
      }
    };
  },
  computed: {
    attrs() {
      return {
        code: 'plat_lost_found_photo',
        ownId: this.formData.id,
        preview: true,
        download: true,
        operate: true,
        "auto-upload": false,
      }
    }
  },
  created() {
    this.formData = { ...this.baseData };
    this.pageMode = this.infoPageMode;
    switch (this.pageMode){
      case'新增':
        this.infoDisabled = false;break;
      case'查看':
        this.infoDisabled = true;break;
      case'编辑':
        this.infoDisabled = false;break;
    }
  },
  methods: {
    handleFormSubmit() {
      //校验
      this.$refs.form.validate((valid) => {
        if(valid){
          let apiUrl = this.pageMode === '编辑'? api.update:api.save;
          let saveData = {...this.formData};
          //处理数据
          if(this.formData.typeId != null && typeof saveData.typeId == 'object')
            saveData.typeId = saveData.typeId.value;
          if(this.formData.teacherId != null && typeof saveData.teacherId == 'object')
            saveData.teacherId = saveData.teacherId.value;
          if(typeof saveData.principal == 'object')
            saveData.principal = saveData.principal.value;
          else saveData.principalId = saveData.principal;

          this.$delete(saveData,'typeName');
          this.$delete(saveData,'teacherName');
          this.$delete(saveData,'college');

          this.$request({
            url: apiUrl,
            data: saveData,
            method: 'POST'
          }).then(res => {
            if (res.rCode === 0) {
              let id = res.results;
              if(id != null && id !== '') {
                this.formData.id = id;
                this.$set(this.$refs.upload.datas,'ownId',id);
              }

              this.$refs.upload.handleUpload();
              this.$message.success('操作成功');
              this.infoPageClose(true);
            } else {
              this.$message.error(res.msg);
            }
          });
        }
      })
    },
    infoPageClose(reload){
      this.pageMode = 'allOn';
      this.infoDisabled = false;
      this.$emit('activelyClose',reload);
    },
  },
  watch: {
  }
};
</script>
<style lang="scss" scoped>
  .sketch_content {
    overflow: auto;
    height: 100%;
    border-top: 1px solid #eff1f4;
    border-bottom: 1px solid #eff1f4;
    padding: 0 30px 11px 27px;
    ::v-deep .es-upload-button{
      display: none !important;
    }
  }
  .resumeTable td {
    height: 50px;
    font-weight: bold;
  }

  ::v-deep .el-collapse-item__header.is-active {
    border-bottom: 1px solid #ebeef5;
    font-size: 18px;
    font-weight: bold;
  }
  ::v-deep .el-collapse-item__header::before {
    content: "";
    width: 4px;
    height: 18px;
    background-color: #0076E9;
    margin-right: 2px;
  }
</style>