<template>
	<div class="content">
		<es-tree-group
			:tree="tree"
			@node-click="handleChange"
			:syncKeys="{ id: 'id', name: 'name' }"
		></es-tree-group>
		<es-data-table
			ref="table"
			:row-style="tableRowClassName"
			:full="true"
			:fit="true"
			:thead="thead"
			:toolbar="toolbar"
			:border="true"
			:page="pageOption"
			:url="dataTableUrl"
			:numbers="true"
			@btnClick="btnClick"
			@sort-change="sortChange"
			:param="params"
			@submit="hadeSubmit"
			@edit="changeTable"
			close
			form
		></es-data-table>
		<!--  原料分类  -->
		<es-dialog
			:title="formTitle"
			:visible.sync="showFormC"
			width="40%"
			height="400px"
			:drag="false"
			:close-on-click-modal="false"
			:destroy-on-close="true"
		>
			<es-form
				ref="form"
				:model="formDataC"
				:contents="formItemListC"
				height="260px"
				:genre="2"
				collapse
				@change="inputChange"
				@submit="handleFormSubmitC"
				@reset="showFormC = false"
			/>
		</es-dialog>
		<!--  原料  -->
		<es-dialog
			:title="formTitle"
			:visible.sync="showForm"
			width="50%"
			height="700px"
			:drag="false"
			:close-on-click-modal="false"
			:destroy-on-close="true"
		>
			<es-form
				ref="form"
				:model="formData"
				:contents="formItemList"
				height="580px"
				:genre="2"
				collapse
				@change="inputChange"
				@submit="handleFormSubmit"
				@reset="showForm = false"
			/>
		</es-dialog>
		<es-dialog
			title="删除"
			:visible.sync="showDelete"
			width="20%"
			:close-on-click-modal="false"
			:middle="true"
			height="140px"
			:drag="false"
		>
			<div>确定要删除该条数据吗</div>
			<div class="btn-box">
				<div class="btn theme" @click="deleteRow">确定</div>
				<div class="btn" @click="showDelete = false">取消</div>
			</div>
		</es-dialog>
		<es-dialog
			title="删除"
			:visible.sync="showDeleteC"
			width="20%"
			:close-on-click-modal="false"
			:middle="true"
			height="140px"
			:drag="false"
		>
			<div>确定要删除该条数据吗</div>
			<div class="btn-box">
				<div class="btn theme" @click="deleteRowC">确定</div>
				<div class="btn" @click="showDeleteC = false">取消</div>
			</div>
		</es-dialog>
	</div>
</template>

<script>
import httpApiC from '@/http/logistics/materialcategory.js';
import httpApi from '@/http/logistics/material.js';
import supplierApi from '@/http/logistics/supplier.js';
import SnowflakeId from 'snowflake-id';
export default {
	data() {
		return {
			showFormC: false,
			tree: {
				defaultExpandAll: false,
				showSearch: false,
				data: [],
				defaultProps: {
					children: 'children',
					label: 'name'
				}
			},
			nodeSelectData: [],
			supplierOptions: [], //供应商选择列表
			selectedTreeNodeValue: '',
			clickNode: '',
			editType: 'edit',
			ownId: '',
			dataTableUrl: httpApi.listJson,
			showForm: false,
			showDelete: false,
			showDeleteC: false,
			formDataC: {},
			formData: {},
			formTitle: '编辑',
			editBtn: {
				type: 'submit',
				skin: 'lay-form-btns',
				contents: [
					{
						type: 'primary',
						text: '确认',
						event: 'submit'
					},
					{
						type: 'reset',
						text: '取消',
						event: 'cancel'
					}
				]
			},
			cancelBtn: {
				type: 'submit',
				skin: 'lay-form-btns',
				contents: [
					{
						type: 'reset',
						text: '取消',
						event: 'cancel'
					}
				]
			},
			toolbar: [
				{
					type: 'button',
					contents: [
						{
							text: '新增分类',
							code: 'addC',
							type: 'primary',
							disabled: true
						},
						{
							text: '删除分类',
							code: 'deleteC',
							type: 'primary',
							disabled: true
						},
						{
							text: '新增原料',
							code: 'add',
							type: 'primary',
							disabled: true
						}
					]
				},
				{
					type: 'search',
					contents: [
						{
							type: 'text',
							name: 'keyword',
							placeholder: '关键字查询'
						}
					]
				}
			],
			thead: [
				{
					title: '原料名称',
					align: 'left',
					field: 'name',
					sortable: 'custom',
					showOverflowTooltip: true
				},
				{
					title: '原料编号',
					align: 'left',
					field: 'code',
					sortable: 'custom',
					showOverflowTooltip: true
				},
				{
					title: '原料分类',
					align: 'left',
					field: 'categoryName',
					sortable: 'custom',
					showOverflowTooltip: true
				},
				{
					title: '规格',
					align: 'left',
					field: 'specification',
					sortable: 'custom',
					showOverflowTooltip: true
				},
				{
					title: '库存单位',
					align: 'left',
					field: 'unit',
					sortable: 'custom',
					showOverflowTooltip: true,
					sysCode: 'hq_material_unit'
				},
				{
					title: '供应商',
					align: 'left',
					field: 'brand',
					sortable: 'custom',
					showOverflowTooltip: true
				},
				{
					title: '状态',
					align: 'left',
					field: 'status',
					sortable: 'custom',
					showOverflowTooltip: true,
					sysCode: 'hqIsUse'
				},
				{
					title: '操作',
					type: 'handle',
					width: 180,
					template: '',
					events: [
						{
							code: 'edit',
							text: '编辑'
						},
						{
							code: 'view',
							text: '查看'
						},
						{
							code: 'status',
							text: '启用',
							rules: rows => {
								return rows.status == 0;
							}
						},
						{
							code: 'status',
							text: '禁用',
							rules: rows => {
								return rows.status == 1;
							}
						},
						{
							code: 'delete',
							text: '删除'
						}
					]
				}
			],
			pageOption: {
				layout: 'total, prev, pager, next, jumper',
				pageSize: 10,
				hideOnSinglePage: false,
				position: 'center',
				current: 1,
				pageNum: 1
			},
			params: {
				asc: 'false',
				orderBy: 'createTime'
			},
			allCategoryList: []
		};
	},
	computed: {
		formItemListC() {
			return [
				{
					label: '上级分类',
					type: 'select',
					name: 'parentId',
					placeholder: '请选择原料分类',
					event: 'multipled',
					// rules: {
					//   required: true,
					//   message: '请选择原料分类',
					//   trigger: 'blur'
					// },
					data: this.nodeSelectData,
					valueKey: 'id',
					labelKey: 'name',
					verify: 'required',
					disabled: true,
					col: 11
				},
				{
					label: '分类名称',
					name: 'name',
					placeholder: '请输入分类名称',
					event: 'multipled',
					rules: {
						required: true,
						message: '请输入分类名称',
						trigger: 'blur'
					},
					maxlength: 100,
					verify: 'required',
					col: 11
				},
				{
					type: 'radio',
					label: '启用状态',
					name: 'status',
					event: 'multipled',
					rules: {
						required: false,
						trigger: 'blur'
					},
					verify: 'required',
					col: 11,
					data: [
						{
							value: 1,
							name: '启用'
						},
						{
							value: 0,
							name: '禁用'
						}
					]
				},
				{
					label: '分类说明',
					name: 'remark',
					placeholder: '请输入分类说明',
					event: 'multipled',
					rules: {
						required: false,
						message: '请输入分类说明',
						trigger: 'blur'
					},
					maxlength: 250,
					col: 11
				}
			];
		},
		formItemList() {
			const readonly = this.formTitle === '查看';
			return [
				{
					label: '原料名称',
					name: 'name',
					placeholder: '请输入原料名称',
					event: 'multipled',
					rules: {
						required: true,
						message: '请输入原料名称',
						trigger: 'blur'
					},
					verify: 'required',
					maxlength: 100,
					readonly: readonly,
					col: 6
				},
				{
					label: '原料分类',
					type: 'select',
					name: 'categoryId',
					placeholder: '请选择原料分类',
					event: 'multipled',
					rules: {
						required: true,
						message: '请选择原料分类',
						trigger: 'blur'
					},
					data: this.nodeSelectData,
					'value-key': 'id',
					'label-key': 'name',
					verify: 'required',
					disabled: true,
					col: 6
				},
				{
					label: '库存单位',
					name: 'unit',
					placeholder: '请输入库存单位',
					event: 'multipled',
					rules: {
						required: true,
						message: '请输入库存单位',
						trigger: 'blur'
					},
					verify: 'required',
					readonly: readonly,
					col: 6,
					type: 'select',
					sysCode: 'hq_material_unit'
				},
				// {
				// 	label: '供应商',
				// 	name: 'brand',
				// 	placeholder: '请输入供应商',
				// 	event: 'multipled',
				// 	rules: {
				// 		required: true,
				// 		message: '请输入供应商',
				// 		trigger: 'blur'
				// 	},
				// 	verify: 'required',
				// 	readonly: readonly,
				// 	col: 6
				// },
				{
					type: 'select',
					name: 'brandId',
					label: '供应商',
					data: this.supplierOptions,
					'label-key': 'name',
					'value-key': 'id',
					rules: {
						required: true,
						message: '请选择供应商',
						trigger: 'change'
					},
					verify: 'required',
					readonly: readonly,
					col: 6
				},
				{
					label: '规格',
					name: 'specification',
					placeholder: '请输入规格',
					event: 'multipled',
					rules: {
						required: true,
						message: '请输入规格',
						trigger: 'blur'
					},
					maxlength: 20,
					verify: 'required',
					readonly: readonly,
					col: 6
				},
				{
					label: '能量',
					name: 'energy',
					placeholder: '请输入能量',
					event: 'multipled',
					rules: {
						required: false,
						message: '请输入能量',
						trigger: 'blur'
					},
					maxlength: 10,
					verify: 'required',
					readonly: readonly,
					col: 6
				},
				{
					label: '蛋白质',
					name: 'protein',
					placeholder: '请输入蛋白质',
					event: 'multipled',
					rules: {
						required: false,
						message: '请输入蛋白质',
						trigger: 'blur'
					},
					maxlength: 10,
					verify: 'required',
					readonly: readonly,
					col: 6
				},
				{
					label: '脂肪',
					name: 'fat',
					placeholder: '请输入脂肪',
					event: 'multipled',
					rules: {
						required: false,
						message: '请输入脂肪',
						trigger: 'blur'
					},
					maxlength: 10,
					verify: 'required',
					readonly: readonly,
					col: 6
				},
				{
					label: '碳水化合物',
					name: 'carbohydrate',
					placeholder: '请输入碳水化合物',
					event: 'multipled',
					rules: {
						required: false,
						message: '请输入碳水化合物',
						trigger: 'blur'
					},
					maxlength: 10,
					verify: 'required',
					readonly: readonly,
					col: 6
				},
				{
					label: '钠',
					name: 'sodium',
					placeholder: '请输入钠',
					event: 'multipled',
					rules: {
						required: false,
						message: '请输入钠',
						trigger: 'blur'
					},
					maxlength: 10,
					verify: 'required',
					readonly: readonly,
					col: 6
				},
				{
					label: '价格',
					name: 'price',
					placeholder: '请输入价格',
					event: 'multipled',
					rules: {
						required: true,
						message: '请输入价格',
						trigger: 'blur'
					},
					maxlength: 10,
					verify: 'required',
					readonly: readonly,
					col: 6
				},
				// {
				// 	label: '原料图',
				// 	type: 'attachment',
				// 	code: 'material_base_img',
				// 	name: 'materialImg',
				// 	ownId: this.formData.id,
				// 	size: 100,
				// 	portrait: true,
				// 	readonly: readonly,
				// 	param: {
				// 		isShowPath: true
				// 	},
				// 	col: 6
				// }
				{
					label: '原料图(*最多可上传3张图片)',
					name: 'materialImg',
					type: 'attachment',
					code: 'material_base_img',
					'select-type': 'icon-plus',
					preview: true,
					listType: 'picture-card',
					ownId: this.formData.id, // 业务id
					readonly: readonly,
					limit: 3,
					col: 12
				}
			];
		}
		// formItemList() {
		// 	return this.editType === 'edit'
		// 		? [...this.arr.slice(0, -2), this.editBtn]
		// 		: [...this.arr, this.cancelBtn];
		// },
	},
	watch: {
		showForm(val) {
			if (!val) {
				this.formData = {};
			} else {
				this.selectAllList();
			}
		}
	},
	created() {
		this.initTree();
		this.supplierList();
		this.selectAllList();
	},
	mounted() {},
	methods: {
		initTree() {
			this.$request({
				url: httpApi.categoryTree,
				params: { status: 1 },
				method: 'GET'
			}).then(res => {
				if (res.rCode == 0) {
					this.tree.data = res.results;
				}
			});
		},
		/**
		 * 树点击事件
		 */
		handleChange(tree, data) {
			this.nodeSelectData = [];
			let node = { id: data.id, name: data.name, selected: true };
			this.nodeSelectData.push(node);
			this.selectedTreeNodeValue = data.id;
			this.clickNode = data.id;
			this.$set(this.params, 'categoryId', data.id);
			//自己必须是最后一级分类，且不是第一级分类
			//自己是最后一级分类 & 父级分类不为空；才能新增原料
			if (data.level == 1) {
				this.toolbar[0].contents[0].disabled = false;
				this.toolbar[0].contents[1].disabled = true;
				this.toolbar[0].contents[2].disabled = true;
			} else if (data.level == 2) {
				this.toolbar[0].contents[0].disabled = true;
				this.toolbar[0].contents[1].disabled = false;
				this.toolbar[0].contents[2].disabled = false;
			} else {
				this.toolbar[0].contents[0].disabled = true;
				this.toolbar[0].contents[1].disabled = true;
				this.toolbar[0].contents[2].disabled = true;
			}
		},
		handleNodeClick(data) {},
		/**
		 * 表格行高
		 */
		tableRowClassName({ row, rowIndex }) {
			let styleRes = {
				height: '54px !important'
			};
			return styleRes;
		},
		inputChange(key, value) {},
		hadeSubmit(data) {},
		changeTable(val) {
			switch (val.name) {
				case 'status':
					this.changeStatus(val.data.id, val.data.status);
					break;
			}
		},
		//改变数据启用状态
		changeStatus(id, status) {
			this.$request({
				url: httpApi.categoryUpdateStatus,
				params: { ids: id, status: status },
				method: 'GET'
			}).then(res => {
				if (res.success) {
					this.$message.success('操作成功！');
				} else {
					this.$message.error(res.msg);
				}
			});
		},
		/**
		 * 操作按钮点击事件
		 * @param {*} res
		 */
		btnClick(res) {
			let code = res.handle.code;
			let text = res.handle.text;
			switch (code) {
				case 'addC':
					this.formTitle = '新增分类';
					this.editModule(this.formItemListC);
					const snowflakeC = new SnowflakeId();
					let idC = snowflakeC.generate();
					this.ownId = idC;
					this.formDataC = {
						id: idC,
						level: 2, //默认新增第一级
						name: null,
						createTime: null,
						status: 1,
						address: null,
						remark: null
					};
					this.showFormC = true;
					break;
				case 'deleteC':
					this.showDeleteC = true;
					break;
				case 'add':
					// 新增
					this.formTitle = '新增';
					this.editModule(this.formItemList);
					const snowflake = new SnowflakeId();
					let id = snowflake.generate();
					this.ownId = id;
					this.formData = {
						id: id,
						name: null,
						createTime: null,
						status: null,
						address: null,
						remark: null
					};
					this.showForm = true;
					break;
				case 'edit':
					// 编辑
					this.formTitle = '编辑';
					this.ownId = res.row.id;
					this.editModule(this.formItemList);
					this.$request({
						url: httpApi.info + '/' + res.row.id,
						method: 'GET'
					}).then(res => {
						if (res.rCode == 0) {
							//编辑时,要对树对象进行处理
							let parentId = res.results.categoryId;
							this.nodeSelectData = [];
							if (parentId) {
								let tempName = this.getParentName(parentId);
								let node = { id: parentId, name: tempName, selected: true };
								this.nodeSelectData.push(node);
							}
							this.formData = res.results;
							this.showForm = true;
						}
					});
					break;
				case 'status':
					//状态修改
					let url = httpApi.statusStop;
					if (text == '启用') {
						url = httpApi.statusOpen;
					}
					this.$request({
						url: url,
						params: { ids: res.row.id }
					}).then(res => {
						if (res.rCode == 0) {
							this.$message.success(res.msg);
							this.$refs.table.reload();
						} else {
							this.$message.error(res.msg);
						}
					});
					break;
				case 'view':
					this.formTitle = '查看';
					this.ownId = res.row.id;
					this.readModule(this.formItemList);
					this.$request({
						url: httpApi.info + '/' + res.row.id,
						method: 'GET'
					}).then(res => {
						if (res.rCode == 0) {
							let parentId = res.results.categoryId;
							this.nodeSelectData = [];
							if (parentId) {
								let tempName = this.getParentName(parentId);
								let node = { id: parentId, name: tempName, selected: true };
								this.nodeSelectData.push(node);
							}
							this.formData = res.results;
							this.showForm = true;
						}
					});
					break;
				case 'delete':
					this.deleteId = res.row.id;
					this.showDelete = true;
					break;
				default:
					break;
			}
		},
		getParentName(parentId) {
			let reName = '';
			for (let i = 0; i < this.allCategoryList.length; i++) {
				if (this.allCategoryList[i].value == parentId) {
					reName = this.allCategoryList[i].label;
				}
			}

			return reName;
		},
		handleClose() {},
		handleFormItemChange() {},
		handleFormSubmitC(data) {
			let formData = data;
			delete formData.extMap;
			let url = '';
			if (this.formTitle == '新增分类') {
				url = httpApiC.save;
			} else {
				url = httpApiC.update;
			}
			this.$request({
				url: url,
				data: formData,
				method: 'POST'
			}).then(res => {
				if (res.rCode == 0) {
					this.$message.success('操作成功'); //
					this.showFormC = false;
					this.formDataC = {};
					this.$refs.table.reload();
					this.initTree();
					// this.selectAllList();
				} else {
					this.formData.state = 1 == this.formData.state ? true : false;
					this.$message.error(res.msg);
				}
			});
		},
		handleFormSubmit(data) {
			let formData = data;
			delete formData.extMap;
			let url = '';
			if (this.formTitle == '新增') {
				url = httpApi.save;
			} else {
				url = httpApi.update;
			}
			this.$request({
				url: url,
				data: formData,
				method: 'POST'
			}).then(res => {
				if (res.rCode == 0) {
					this.$message.success('操作成功'); //
					this.showForm = false;
					this.formData = {};
					this.$refs.table.reload();
					this.initTree();
					this.selectAllList();
				} else {
					this.formData.state = 1 == this.formData.state ? true : false;
					this.$message.error(res.msg);
				}
			});
		},
		/**
		 * 删除原料
		 */
		deleteRow() {
			this.$request({
				url: httpApi.deleteBatchIds,
				data: { ids: this.deleteId },
				method: 'POST'
			}).then(res => {
				if (res.rCode == 0) {
					this.$refs.table.reload();
					this.$message.success('删除成功');
					this.initTree();
					this.selectAllList();
				} else {
					this.$message.error(res.msg);
				}
				this.showDelete = false;
			});
		},
		/**
		 * 删除分类
		 */
		deleteRowC() {
			this.$request({
				url: httpApi.categoryDel,
				data: { id: this.clickNode },
				method: 'POST'
			}).then(res => {
				if (res.rCode == 0) {
					this.$refs.table.reload();
					this.$message.success('删除成功');
					this.initTree();
					this.selectAllList();
				} else {
					this.$message.error(res.msg);
				}
				this.showDeleteC = false;
			});
		},
		/**
		 * 获取所有分类keyvalue集合
		 * @param res
		 */
		selectAllList() {
			this.$request({
				url: httpApi.getListAllKeyValue,
				method: 'GET'
			}).then(res => {
				this.allCategoryList = res.results;
			});
		},
		// 供应商(供应商)列表
		supplierList() {
			this.$request({
				url: supplierApi.listAll,
				method: 'GET'
			}).then(res => {
				if (res.rCode === 0) {
					this.supplierOptions = res.results;
				}
			});
		},
		/**
		 * 排序变化事件
		 * @param {*} column
		 * @param {*} prop
		 * @param {*} order
		 */
		sortChange(column, prop, order) {
			if (column.order == 'ascending') {
				//升序
				this.params = {
					asc: 'true',
					orderBy: column.prop
				};
			} else if (column.order == 'descending') {
				//降序
				this.params = {
					asc: 'false',
					orderBy: column.prop
				};
			} else {
				//不排序
				this.params = {
					asc: 'false',
					orderBy: 'createTime'
				};
			}
			this.$refs.table.reload();
		},
		/**
		 * 编辑模式
		 */
		editModule(list) {
			list.forEach((item, index) => {
				item.readonly = false;
			});
			this.editType = 'edit';
		},
		/**
		 * 只读模式
		 */
		readModule(list, hideField) {
			for (var i in list) {
				var item = list[i];
				item.readonly = true;
			}
			this.editType = 'read';
		}
	}
};
</script>
<style scoped lang="scss">
@import '../../../assets/style/style.scss';
.el-row,
.el-col,
.table-box {
	height: 100%;
	width: 100%;
}
.content {
	display: flex;
	width: 100%;
	height: 100%;

	::v-deep .es-data-table {
		flex: 1;
		display: flex;
		flex-direction: column;

		.es-data-table-content {
			flex: 1;
			height: 0;
			display: flex;
			flex-direction: column;

			.el-table {
				flex: 1;
				// height: 100% !important;
			}

			.es-thead-border {
				.el-table__header {
					th {
						border-right: 1px solid #e1e1e1;
					}
				}
			}
		}
	}

	::v-deep .el-form-item__label {
		background: none;
		border: 0px solid #c2c2c2;
	}
}

.el-dialog__body {
	.btn-box {
		margin-top: 20px;
		display: flex;
		justify-content: flex-end;

		.btn {
			padding: 5px 10px;
			color: #666;
			border: 1px solid #eee;
			cursor: pointer;
			margin-right: 5px;
			border-radius: 4px;
			// &.theme {
			// 	background: $--color-primary;
			// 	color: #fff;
			// 	border-color: $--color-primary;
			// }
		}
	}
}
</style>
