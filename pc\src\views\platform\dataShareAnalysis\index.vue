<template>
	<div>
		<div class="content">
			<el-form :model="formData" label-width="100px">
				<el-row>
					<el-col :span="5">
						<el-form-item label="关键词搜索" prop="key">
							<el-input v-model="formData.key" placeholder="使用方名称/标识" />
						</el-form-item>
					</el-col>
					<el-col :span="8">
						<el-form-item label="请求时间" prop="timerange">
							<el-date-picker
								v-model="formData.timerange"
								type="datetimerange"
								range-separator="至"
								start-placeholder="请选择日期"
								end-placeholder="请选择日期"
								format="yyyy-MM-dd HH:mm"
								value-format="yyyy-MM-dd HH:mm"
							></el-date-picker>
						</el-form-item>
					</el-col>
					<el-col :span="5">
						<el-form-item label="调用状态" prop="status">
							<el-select v-model="formData.status" clearable placeholder="请选择调用状态">
								<el-option label="成功" :value="1" />
								<el-option label="失败" :value="2" />
							</el-select>
						</el-form-item>
					</el-col>
					<el-col :span="6" style="text-align: right">
						<el-button type="primary" @click="onSearch">查询</el-button>
						<el-button @click="onReset">重置</el-button>
						<el-button @click="onExport">导出</el-button>
					</el-col>
				</el-row>
			</el-form>

			<el-table :data="list" border>
				<el-table-column prop="name" label="使用方名称" width="150"></el-table-column>
				<el-table-column prop="key" label="使用方标识" width="100"></el-table-column>
				<el-table-column prop="method" label="调用服务"></el-table-column>
				<el-table-column label="服务方式" width="100">
					<template>查询</template>
				</el-table-column>
				<el-table-column prop="number" label="调用量" width="120" sortable></el-table-column>
				<el-table-column prop="time" label="调用时间" width="150"></el-table-column>
				<el-table-column prop="duration" label="调用耗时" width="90"></el-table-column>
				<el-table-column label="调用状态" width="100">
					<template>
						<el-tag type="success">成功</el-tag>
					</template>
				</el-table-column>
				<el-table-column label="操作" width="60">
					<template slot-scope="{ row }">
						<el-link type="primary" :underline="false" @click="onDetail(row)">查看</el-link>
					</template>
				</el-table-column>
			</el-table>
		</div>
		<el-dialog title="查看详情" :visible.sync="dialogVisible" width="900px" append-to-body>
			<div class="detail">
				<div class="detail-row">
					<p>
						<span>使用方名称：</span>
						<span>{{ row.name }}</span>
					</p>
					<p>
						<span>使用方标识：</span>
						<span>{{ row.key }}</span>
					</p>
				</div>
				<div class="detail-row">
					<p>
						<span>调用服务：</span>
						<span>{{ row.method }}</span>
					</p>
					<p>
						<span>服务方式：</span>
						<span>查询</span>
					</p>
				</div>
				<div class="detail-row">
					<p>
						<span>调用时间：</span>
						<span>{{ row.time }}</span>
					</p>
					<p>
						<span>调用耗时：</span>
						<span>{{ row.duration }}</span>
					</p>
				</div>
				<div class="detail-row">
					<p>
						<span>请求IP：</span>
						<span>***********</span>
					</p>
					<p>
						<span>请求方式：</span>
						<span>POST</span>
					</p>
				</div>
				<div class="detail-row">
					<p>
						<span>请求参数：</span>
						<span>{}</span>
					</p>
					<p>
						<span>调用状态：</span>
						<span>成功</span>
					</p>
				</div>
			</div>
		</el-dialog>
	</div>
</template>

<script>
export default {
	data() {
		return {
			formData: {},
			originList: [
				{
					name: '宜宾职院数据大屏',
					key: 'ybzysjdp',
					method: 'ADS 智慧校园大屏 能源 水量涨幅排行查询服务',
					time: '2024-09-28 08:43',
					duration: '105ms',
					number: 28
				},
				{
					name: '宜宾职院数据大屏',
					key: 'ybzysjdp',
					method: 'ADS 智慧校园大屏 教学 首页二级学院职称查询服务',
					time: '2024-09-28 08:43',
					duration: '88ms',
					number: 5
				},
				{
					name: '宜宾职院数据大屏',
					key: 'ybzysjdp',
					method: 'ADS 智慧校园大屏 能源 电量涨幅排行查询服务',
					time: '2024-09-28 08:43',
					duration: '88ms',
					number: 13
				},
				{
					name: '宜宾职院数据大屏',
					key: 'ybzysjdp',
					method: 'ADS 智慧校园大屏 首页 数据概览查询服务',
					time: '2024-09-28 08:43',
					duration: '84ms',
					number: 20
				},
				{
					name: '宜宾职院数据大屏',
					key: 'ybzysjdp',
					method: 'ADS 智慧校园大屏 后勤 保修动态查询服务',
					time: '2024-09-28 08:41',
					duration: '83ms',
					number: 3
				},
				{
					name: '宜宾职院数据大屏',
					key: 'ybzysjdp',
					method: 'ADS 智慧校园大屏 后勤 宿舍统计查询服务',
					time: '2024-09-28 08:41',
					duration: '77ms',
					number: 9
				},
				{
					name: '宜宾职院数据大屏',
					key: 'ybzysjdp',
					method: 'ADS 智慧校园大屏 后勤 保修统计查询服务',
					time: '2024-09-28 08:41',
					duration: '74ms',
					number: 17
				},
				{
					name: '宜宾职院数据大屏',
					key: 'ybzysjdp',
					method: 'ADS 智慧校园大屏 能源 总计查询服务',
					time: '2024-09-28 08:41',
					duration: '85ms',
					number: 2
				},
				{
					name: '宜宾职院数据大屏',
					key: 'ybzysjdp',
					method: 'ADS 智慧校园大屏 后勤 总计查询服务',
					time: '2024-09-28 08:41',
					duration: '81ms',
					number: 6
				},
				{
					name: '宜宾职院数据大屏',
					key: 'ybzysjdp',
					method: 'ADS 智慧校园大屏 能源 本年度用水查询服务',
					time: '2024-09-28 08:41',
					duration: '79ms',
					number: 2
				},
				{
					name: '宜宾职院数据大屏',
					key: 'ybzysjdp',
					method: 'ADS 智慧校园大屏 能源 本年度用电查询服务',
					time: '2024-09-28 08:41',
					duration: '70ms',
					number: 26
				},
				{
					name: '宜宾职院数据大屏',
					key: 'ybzysjdp',
					method: 'ADS 智慧校园大屏 教学 最受欢迎教师查询服务',
					time: '2024-09-28 08:41',
					duration: '71ms',
					number: 31
				},
				{
					name: '宜宾职院数据大屏',
					key: 'ybzysjdp',
					method: 'ADS 智慧校园大屏 教学 课程满意度查询服务',
					time: '2024-09-28 08:36',
					duration: '85ms',
					number: 18
				},
				{
					name: '宜宾职院数据大屏',
					key: 'ybzysjdp',
					method: 'ADS 智慧校园大屏 教学 各学院学生人数查询服务',
					time: '2024-09-28 08:36',
					duration: '85ms',
					number: 32
				},
				{
					name: '宜宾职院数据大屏',
					key: 'ybzysjdp',
					method: 'ADS 智慧校园大屏 教学 考试优良率查询服务',
					time: '2024-09-28 08:36',
					duration: '108ms',
					number: 25
				},
				{
					name: '宜宾职院数据大屏',
					key: 'ybzysjdp',
					method: 'ADS 智慧校园大屏 教学 平均成绩查询服务',
					time: '2024-09-28 08:36',
					duration: '88ms',
					number: 6
				},
				{
					name: '宜宾职院数据大屏',
					key: 'ybzysjdp',
					method: 'ADS 智慧校园大屏 后勤 -卡通消费趋势查询服务',
					time: '2024-09-28 08:36',
					duration: '80ms',
					number: 31
				},
				{
					name: '宜宾职院数据大屏',
					key: 'ybzysjdp',
					method: 'ADS 智慧校园大屏 教学 总计查询服务',
					time: '2024-09-28 08:36',
					duration: '80ms',
					number: 30
				},
				{
					name: '宜宾职院数据大屏',
					key: 'ybzysjdp',
					method: 'ADS 智慧校园大屏 后勤 -卡通充值趋势查询服务',
					time: '2024-09-28 08:35',
					duration: '97ms',
					number: 14
				},
				{
					name: '宜宾职院数据大屏',
					key: 'ybzysjdp',
					method: 'ADS 智慧校园大屏 教学 学时类型占比查询服务',
					time: '2024-09-28 08:35',
					duration: '73ms',
					number: 9
				},
				{
					name: '宜宾职院数据大屏',
					key: 'ybzysjdp',
					method: 'ADS 智慧校园大屏 后勤 人均消费走势查询服务',
					time: '2024-09-28 08:35',
					duration: '69ms',
					number: 13
				},
				{
					name: '宜宾职院数据大屏',
					key: 'ybzysjdp',
					method: 'ADS 智慧校园大屏 能源 能耗概况查询服务',
					time: '2024-09-28 08:35',
					duration: '143ms',
					number: 3
				},
				{
					name: '宜宾职院数据大屏',
					key: 'ybzysjdp',
					method: 'ADS 智慧校园大屏 教学 师资规模查询服务',
					time: '2024-09-28 08:34',
					duration: '95ms',
					number: 8
				},
				{
					name: '宜宾职院数据大屏',
					key: 'ybzysjdp',
					method: 'ADS 智慧校园大屏 人事 各学院人员数量音询服务',
					time: '2024-09-28 08:34',
					duration: '92ms',
					number: 29
				},
				{
					name: '宜宾职院数据大屏',
					key: 'ybzysjdp',
					method: 'ADS 智慧校园大屏 人事 编制类型查询服务',
					time: '2024-09-28 08:34',
					duration: '92ms',
					number: 31
				},
				{
					name: '宜宾职院数据大屏',
					key: 'ybzysjdp',
					method: 'ADS 智慧校园大屏 人事 数据概览查询服务',
					time: '2024-09-28 08:34',
					duration: '90ms',
					number: 15
				},
				{
					name: '宜宾职院数据大屏',
					key: 'ybzysjdp',
					method: 'ADS 智慧校园大屏 人事 各学院人员数量查询服务',
					time: '2024-09-28 08:35',
					duration: '66ms',
					number: 13
				},
				{
					name: '宜宾职院数据大屏',
					key: 'ybzysjdp',
					method: 'ADS 智慧校园大屏 人事 编制类型查询服务',
					time: '2024-09-28 08:35',
					duration: '69ms',
					number: 1
				},
				{
					name: '宜宾职院数据大屏',
					key: 'ybzysjdp',
					method: 'ADS 智慧校园大屏 人事 数据概览查询服务',
					time: '2024-09-28 08:35',
					duration: '143ms',
					number: 4
				},
				{
					name: '宜宾职院数据大屏',
					key: 'ybzysjdp',
					method: 'ADS 智慧校园大屏 人事 政治面貌分布查询服务',
					time: '2024-09-28 08:34',
					duration: '95ms',
					number: 11
				},
				{
					name: '宜宾职院数据大屏',
					key: 'ybzysjdp',
					method: 'ADS 智慧校园大屏 人事 人员学历分布查询服务',
					time: '2024-09-28 08:34',
					duration: '92ms',
					number: 24
				},
				{
					name: '宜宾职院数据大屏',
					key: 'ybzysjdp',
					method: 'ADS 智慧校园大屏 人事 教师来校时间分布查询服务',
					time: '2024-09-28 08:34',
					duration: '92ms',
					number: 2
				},
				{
					name: '宜宾职院数据大屏',
					key: 'ybzysjdp',
					method: 'ADS 智慧校园大屏 人事 教师年龄占比查询服务',
					time: '2024-09-28 08:34',
					duration: '90ms',
					number: 23
				},
				{
					name: '宜宾职院数据大屏',
					key: 'ybzysjdp',
					method: 'ADS 智慧校园大屏 人事 职称等级查询服务',
					time: '2024-09-28 08:34',
					duration: '125ms',
					number: 24
				},
				{
					name: '宜宾职院数据大屏',
					key: 'ybzysjdp',
					method: 'ADS-教师基本数据NEW查询服务',
					time: '2024-09-28 08:34',
					duration: '120ms',
					number: 4
				},
				{
					name: '宜宾职院数据大屏',
					key: 'ybzysjdp',
					method: 'ads xs 学生基本数据查询服务',
					time: '2024-09-28 08:34',
					duration: '92ms',
					number: 12
				}
			],
			list: [],
			dialogVisible: false,
			row: {}
		};
	},
	mounted() {
		this.list = JSON.parse(JSON.stringify(this.originList));
	},
	methods: {
		onSearch() {
			const { key, timerange, status } = this.formData;
			if (status === 2) {
				this.list = [];
				return;
			}
			let _list = JSON.parse(JSON.stringify(this.originList));
			if (key) {
				_list = _list.filter(item => item.name.includes(key) || item.key.includes(key));
			}
			if (timerange && timerange.length) {
				_list = _list.filter(item => item.time >= timerange[0] && item.time <= timerange[1]);
			}
			this.list = _list;
		},
		onReset() {
			this.formData = {};
			this.list = JSON.parse(JSON.stringify(this.originList));
		},
		onExport() {
			window.open(process.env.BASE_URL + '/数据共享服务分析统计.xlsx');
		},
		onDetail(row) {
			this.row = row;
			this.dialogVisible = true;
		}
	}
};
</script>
<style scoped lang="scss">
.content {
	height: calc(100vh - 60px);
	padding: 10px;
	overflow: auto;
}

.detail {
	padding: 0 30px;

	&-row {
		display: grid;
		grid-template-columns: 50% 50%;
		margin: 20px 0;

		p span:first-of-type {
			color: #999;
		}
	}
}
</style>
