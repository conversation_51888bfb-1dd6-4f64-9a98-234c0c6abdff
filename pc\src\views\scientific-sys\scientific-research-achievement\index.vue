<!-- eslint-disable no-duplicate-case -->
<template>
	<div class="content">
		<CardList
			v-if="isCard"
			ref="refTable"
			:url="wd.basics.dataTableUrl"
			:totalUrl="wd.basics.totalStatus"
			:toolbar="wd.toolbar"
			:thead="wd.thead"
			:param="params"
			@btnClick="btnClick"
		/>
		<es-data-table
			v-else
			ref="refTable"
			stripe
			:row-style="tableRowClassName"
			:thead="wd.thead"
			:toolbar="wd.toolbar"
			:border="true"
			:page="pageOption"
			:url="wd.basics.dataTableUrl"
			:numbers="true"
			:param="params"
			:option-data="optionData"
			style="width: 100%"
			close
			form
			@btnClick="btnClick"
			@sort-change="sortChange"
			@submit="hadeSubmit"
			@edit="changeTable"
		></es-data-table>

		<es-dialog
			:title="formTitle"
			:visible.sync="visibleBasicInfo"
			:show-scale="false"
			size="full"
			height="100%"
		>
			<BasicInfo
				v-if="visibleBasicInfo"
				:id="formId"
				:contents-key="contentsKey"
				:title="formTitle"
				@visible="
					e => {
						visibleBasicInfo = e;
						$refs.refTable.reload();
					}
				"
			/>
		</es-dialog>
	</div>
</template>

<script>
import BasicInfo from '../components/achievement-info/basic-info';
import CardList from '../components/card-list.vue';
import { mixinList } from './mixinList';
export default {
	components: { BasicInfo, CardList },
	mixins: [mixinList],
	props: {
		// 是否展示卡片列表
		isCard: {
			type: Boolean,
			default: true
		}
	},
	data() {
		return {
			visibleBasicInfo: false,
			deleteId: '',
			codesObj: {},
			showForm: false,
			enpList: [],
			roleList: [],
			formReadonly: true,
			roleFormData: {
				userId: '',
				username: '',
				phone: '',
				selectedRoleList: []
			},
			formId: '',
			formTitle: '查看',
			optionData: {
				state: [
					{
						value: 1,
						name: '启用'
					},
					{
						value: 0,
						name: '禁用'
					}
				]
			},
			editBtn: {
				type: 'submit',
				skin: 'lay-form-btns',
				contents: [
					{
						type: 'primary',
						text: '确定',
						event: 'confirm'
					},
					{
						type: 'reset',
						text: '取消',
						event: 'cancel'
					}
				]
			},
			cancelBtn: {
				type: 'submit',
				skin: 'lay-form-btns',
				contents: [
					{
						type: 'reset',
						text: '取消',
						event: 'cancel'
					}
				]
			},
			pageOption: {
				layout: 'total, prev, pager, next, jumper',
				pageSize: 20,
				// hideOnSinglePage: true,
				position: 'right',
				current: 1,
				pageNum: 1
			},
			params: {
				orderBy: 't2.create_time',
				asc: 'false' // true 升序，false 降序
			},
			submitFilterParams: {}
		};
	},
	created() {
		this.getSysCode();
	},
	methods: {
		// 批量请求数据字典
		getSysCode() {
			let codes =
				'pa_member_type,pa_assume_type_student,academic_type_code,pa_common_yes_no,member_rank';
			codes =
				codes +
				',pa_assume_type_teacher_paper,achievement_form,achievement_source,pa_publish_scope,pa_research_category,pa_thesis_level,pa_subject_type,pa_retrieval_system';
			codes = codes + ',patent_ipr_type,pa_assume_type_teacher_patent,patent_type,patent_ca_rank';
			codes =
				codes +
				',pa_assume_type_teacher_writing,writing_category,writing_type,writing_author_sort,publishing_place';
			codes = codes + ',pa_assume_type_teacher_platform_team,team_level';
			codes = codes + ',pa_assume_type_teacher_technical_standard,standard_type';
			codes = codes + ',pa_assume_type_teacher_technical_products';
			codes =
				codes +
				',pa_assume_type_teacher_awards,reward_category,award_unit_sort,award_level,school_rank,award_prizewinner_rank,award_personal_rank';
			codes = codes + ',pa_assume_type_teacher_software_copyright';
			codes =
				codes +
				',pa_assume_type_teacher_science_achievement,science_achievement_type,pa_change_way,pa_transferee_type';
			this.$utils.findSysCodeList(codes).then(res => {
				this.codesObj = { ...this.codesObj, ...res };
			});
		},
		/**
		 * 表格行高
		 */
		tableRowClassName({ row, rowIndex }) {
			let styleRes = {
				height: '54px !important'
			};
			return styleRes;
		},
		// 高级搜索确认
		hadeSubmit(e) {
			this.submitFilterParams = e.data;
		},
		/**
		 * 操作按钮点击事件
		 * @param {*} res
		 */
		async btnClick(res) {
			let text = res.handle.text;
			this.formId = res.row?.id || '';
			switch (text) {
				case '查看':
					this.formTitle = '查看';
					this.visibleBasicInfo = true;
					
					break;
				case '编辑':
					this.formTitle = '编辑';
					this.visibleBasicInfo = true;
					break;
				case '新增':
					this.formId = this.$uuidv4();
					this.formTitle = '新增';
					this.visibleBasicInfo = true;

					break;
				case '撤回':
					this.$confirm(`您确定要撤回数据吗？`, '提示', {
						confirmButtonText: '确定',
						cancelButtonText: '取消',
						type: 'warning'
					})
						.then(() => {
							const loading = this.$loading();
							this.$request({
								url: this.wd.basics.revoke,
								data: {
									id: this.formId
								},
								method: 'POST'
							}).then(res => {
								loading.close();
								if (res.rCode == 0) {
									this.$message.success('撤回成功');
									this.$refs.refTable.reload();
								}
							});
						})
						.catch(err => {});
					break;
				case '删除':
					{
						this.$confirm(`您确定要删除数据吗？`, '提示', {
							confirmButtonText: '确定',
							cancelButtonText: '取消',
							type: 'warning'
						})
							.then(() => {
								this.$request({
									url: this.wd.basics.info,
									method: 'get',
									params: { id: this.formId }
								}).then(res2 => {
									const loading = this.$loading();
									this.$request({
										url: this.wd.basics.delete,
										data: {
											id: this.formId
										},
										method: 'POST'
									}).then(res => {
										loading.close();
										if (res.rCode == 0) {
											// 删除详情里面的所有附件
											this.$utils.deleteFile('transationform_editfile', this.formId);
											if (res2.rCode == 0 && res2.results?.students) {
												res2.results.students.map(item => {
													this.$utils.deleteFile('transationform_editfile', item.id);
												});
											}
											this.$message.success(res.msg);
											this.$refs.refTable.reload();
										} else {
											this.$message.error(res.msg);
										}
									});
								});
							})
							.catch(err => {});
					}
					break;
				case '导出':
					this.exportFile();
					break;
				default:
					break;
			}
		},
		//导出函数
		exportFile() {
			let isDev = process.env.NODE_ENV === 'development' ? '/api' : '';
			const paramAll = { ...this.params, ...this.submitFilterParams };
			let url = `${isDev}/ybzy/projectSrAchievement/export?${this.objToUrlParams(paramAll)}`;
			window.open(url);
		},
		objToUrlParams(obj) {
			return Object.keys(obj)
				.map(key => encodeURIComponent(key) + '=' + encodeURIComponent(obj[key]))
				.join('&');
		},
		handleClose() {},
		handleFormItemChange() {},

		/**
		 * 排序变化事件
		 * @param {*} column
		 * @param {*} prop
		 * @param {*} order
		 */
		sortChange(column, prop, order) {
			if (column.order == 'ascending') {
				//升序
				this.params = {
					asc: 'true',
					orderBy: column.prop
				};
			} else if (column.order == 'descending') {
				//降序
				this.params = {
					asc: 'false',
					orderBy: column.prop
				};
			} else {
				//不排序
				this.params = {
					asc: 'false',
					orderBy: 'createTime'
				};
			}
			this.$refs.refTable.reload();
		},
		/**
		 * 编辑模式
		 */
		editModule(list, hideField) {
			for (var i in list) {
				var item = list[i];
				item.readonly = false;
				if (undefined != hideField && hideField.length > 0 && hideField.indexOf(item.name) > -1) {
					item.hide = true;
				}
			}
			list.push(this.editBtn);
		},

		changeTable(val) {
			if ('state' === val.name) {
				this.$request({
					url: this.wd.basics.updateState,
					data: {
						id: val.data.id,
						state: val.value
					},
					method: 'POST'
				}).then(res => {
					if (res.rCode == 0) {
						this.$message.success('操作成功');
					} else {
						this.$message.error(res.msg);
					}
				});
			}
		},
		reset() {
			this.visible = false;
		}
	}
};
</script>
<style scoped lang="scss">
.content {
	width: 100%;
	height: 100%;
	display: flex;
	flex: 1;
}
</style>
