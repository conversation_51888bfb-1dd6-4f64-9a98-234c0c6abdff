<template>
  <div class="content" style="height: 100%">
    <es-tree-group
        :tree="tree"
        :width="90"
        @node-click="treeClick"
        :syncKeys="{id:'id', name:'name'}"
    ></es-tree-group>
    <es-data-table :row-style="tableRowClassName" v-if="true" ref="table" :key="tableCount" :full="true" :fit="true" :thead="thead" :toolbar="toolbar"
                   @btnClick="btnClick" @selection-change="handleSelectionChange" @edit="changeTable"
                   :page="page" :url="dataTableUrl" :param="dataTableParam" :border="true" :numbers="true" checkbox form>
    </es-data-table>
    <el-dialog :title="formTitle" v-if="showInfoPage" :visible.sync="showInfoPage" width="70%" height="auto" append-to-body>
      <div style="height: 80vh">
        <infoPage ref="infoPage" v-on:activelyClose="closeInfoPage" :base-data="this.formData" :info-page-mode="this.infoPageMode"></infoPage>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import api from '@/http/union/unionMoveRecordAudit/api';
import InfoPage from "@/views/union/unionMoveRecordAudit/infoPage.vue";

export default {
  components: {InfoPage},
  data() {
    return {
      tree: {
        defaultExpandAll:true,
        showSearch: false,
        data: [],
        defaultProps: {
          children: 'children',
          label: 'name'
        }
      },
      formData: {},
      page: {
        pageSize: 20,
        totalCount: 0,
      },
      infoPageMode: 'allOn',
      selectRowData: [],
      selectRowIds: [],
      showInfoPage: false,
      tableCount: 1,
      dialogType: "",
      dataTableUrl: api.cUserListJson,
      dataTableParam: { orderBy: 'create_time', asc: false},
      formTitle: '',
      validityOfDateDisable: false,
      thead: [],
      toolbar: [],
      editToolbar:[
        {
          type: 'button',
          contents: [
            // {
            //   text: '新增',
            //   code: 'toolbar',
            // },
            {
              text: '查看',
              code: 'toolbar',
              type: 'primary'
            },
            {
              text: '删除',
              code: 'toolbar',
              type: 'danger'
            },
          ]
        },
        {
          type: 'search',
          reset: true,
          contents: [
            {
              type: 'select',
              name: 'auditStatus',
              placeholder: '审核状态',
              clearable: true,
              sysCode: 'union_audit_status'
            },
            {
              type: 'text',
              name: 'keyword',
              placeholder: '输入签名查询',
              clearable: true,
            },
          ]
        },
      ],
      listThead: [
        {
          title: '申请人',
          align: 'left',
          field: 'memberName',
          sortable: 'custom',
          showOverflowTooltip: true,
        },
        {
          title: '所属学院',
          width: "200px",
          align: 'center',
          field: 'collegeName',
          sortable: 'custom',
          showOverflowTooltip: true,
        },
        {
          title: '工号',
          width: "130px",
          align: 'center',
          field: 'jobNumber',
          sortable: 'custom',
          showOverflowTooltip: true,
        },
        {
          title: '在编/非在编',
          width: "80px",
          align: 'center',
          field: 'isSystemName',
          sortable: 'custom',
          showOverflowTooltip: true,
        },
        {
          title: '审核状态',
          width: "120px",
          align: 'center',
          field: 'auditStatusName',
          sortable: 'custom',
          showOverflowTooltip: true,
        },
      ],
      btnJson: {
        title: '操作',
        type: 'handle',
        width: 180,
        template: '',
        events: [
          {
            code: 'row',
            text: '查看'
          },
          {
            code: 'row',
            text: '审核',
            rules: rows => {
              return rows.auditStatus === 0;
            }
          },
          {
            code: 'row',
            text: '删除'
          },
        ]
      },
    };
  },
  created() {
    // 初始化左侧机构树
    this.initTree();
    // 初始化查询待审核列表
    this.thead = this.getListThead(this.btnJson);
    this.toolbar = this.editToolbar;
  },
  methods: {
    /**
     * 表格行高
     */
    tableRowClassName() {
      return {
        "height": "54px !important"
      };
    },
    btnClick(res){
      let text = res.handle.text;
      let code = res.handle.code;

      if(code === 'row'){
        switch (text){
          case '查看':
          case '审核': this.openInfoPage(res.row.id, text); break;
          case '删除': this.deleteRows([res.row.id]); break;
        }
      }else {
        switch (text){
          case '新增': this.openInfoPage(null, text); break;
          case '查看':
            if(this.selectRowIds.length>1){
              this.$message.warning('只能选择一个查看');
            }else if(this.selectRowIds.length<1){
              this.$message.warning('请选择一个进行查看');
            }else {this.openInfoPage(this.selectRowIds[0], text);}
            break;
          case '删除':
            if(this.selectRowIds.length<1){
              this.$message.warning('请选择一个删除');
            }else {this.deleteRows(this.selectRowIds);}
            break;
        }
      }
    },
    //打开infoPage
    openInfoPage(id, pageMode){
      if(pageMode !== '新增'){
        this.$request({
          url: api.info+'/'+id,
          method: 'GET'
        }).then(res => {
          //处理请求数据
          this.formData = {...res.results};
          this.formData.isSystem = {value: this.formData.isSystem, label:this.formData.isSystemName}

          this.formTitle = pageMode;
          this.infoPageMode = pageMode;
          this.showInfoPage = true;
        });
      }else {
        this.formTitle = pageMode;
        this.formData = {};
        this.infoPageMode = pageMode;
        this.showInfoPage = true;
      }
    },
    //关闭infoPage
    closeInfoPage(reload){
      this.formData = {};
      this.showInfoPage = false;
      if(reload)
        this.$refs.table.reload();
    },
    // 数据列表多选回调
    handleSelectionChange(data) {
      let ids = [];
      data.forEach(row => {
        ids.push(row.id);
      });
      this.selectRowData = data;
      this.selectRowIds = ids;
    },
    deleteRows(ids){
      this.$confirm('确定要删除选中的数据吗？', '删除', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        this.$request({
          url: api.deleteBatchIds,
          data: { ids: ids.join(',') },
          method: 'POST'
        }).then(res => {
          if (res.rCode === 0) {
            this.$message.success('操作完成');
            this.$refs.table.reload();
          } else {
            this.$message.error(res.msg);
          }
        });
      })
      .catch(() => {});
    },
    getListThead(btnJson){
      let tempThead = [...this.listThead];
      tempThead.push(btnJson);
      return tempThead;
    },
    changeTable(val) {
      switch (val.name){
        case 'status': break;
      }
    },
    // 树点击事件
    treeClick(tree, data) {
      this.clickNode = data.id;
      this.$set(this.dataTableParam, 'unionId', data.id);
    },

    // 初始化左侧树数据
    initTree() {
      this.$request({
        url: api.cUserTreeList,
        method: 'POST'
      }).then(res => {
        if (res.rCode === 0) {
          this.tree.data = res.results.baseTree;
          this.unionSelectData = res.results.parentTree;
        }
      });
    },
  },
};
</script>
<style scoped lang="scss">
.content {
  display: flex;
  ::v-deep .es-data-table{
    width: 100vh;
    flex: 1;
    display: flex;
    flex-direction: column;

    .es-data-table-content {
      flex: 1;
      height: 0;
      display: flex;
      flex-direction: column;
    }
  }
}
</style>