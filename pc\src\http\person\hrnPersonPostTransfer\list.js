import httpApi from './api.js';
export default {
	computed: {
		component() {
			return {
				// 树表结构
				//name: 'es-tree-group'
				// 纯列表结构
				name: 'es-data-table'
			};
		},
		treeParam() {
			return {
				// 不需要左边树 可以
				//url: httpApi.hrnPersonPostTransferTree,
				//title: '分类树'
			};
		},
		table() {
			return {
				url: httpApi.hrnPersonPostTransferList,
				param: { handlerType: '' },
				full: true,
				fit: true,
				page: true,
				stripe: true,
				numbers: true,
				'row-key': 'id',
				toolbar: [
					{
						type: 'button',
						contents: [
							{
								text: '新增',
								type: 'primary'
							}
						]
					},
					{
						type: 'search',
						reset: true,
						contents: [
							//{
							//	name: 'accountName_like',
							//	placeholder: '配置名称',
							//	label: '配置名称',
							//	col: 6
							//},
							{
								type: 'date',
								col: 6,
								name: 'updateTime_begin',
								label: '更新开始时间',
								placeholder: '更新开始时间'
							},
							{
								type: 'date',
								col: 6,
								name: 'updateTime_end',
								label: '更新结束时间',
								placeholder: '更新结束时间'
							}
						]
					}
				],
				thead: [
					{
						title: '基本信息ID',
						align: 'left',
						field: 'hrnPersonId'
					},
					{
						title: '转岗日期',
						align: 'left',
						field: 'transferTime'
					},
					{
						title: '转岗类型',
						align: 'left',
						field: 'transferType'
					},
					{
						title: '转岗名称',
						align: 'left',
						field: 'transferName'
					},
					{
						title: '变动事由',
						align: 'left',
						field: 'transferRemark'
					},
					{
						title: '附件',
						align: 'left',
						field: 'transferFile'
					},
					{
						title: '审批',
						align: 'left',
						field: 'auditProcess'
					},
					{
						title: '状态',
						align: 'left',
						field: 'status'
					},
					{
						title: '操作',
						type: 'handle',
						width: 180,
						events: [
							{
								text: '查看'
							},
							{
								text: '编辑'
							},
							{
								text: '删除'
							}
						]
					}
				]
			};
		}
	}
};
